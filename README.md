# 🚀 AI SEO 智能营销平台

一个基于人工智能的搜索引擎优化和内容营销平台，集成了多种AI模型、知识库管理、内容生成、SEO分析等功能。

## 📋 项目概述

### 🎯 核心功能
- **🤖 AI内容生成**: 支持多种AI模型（GPT、Claude、豆包、通义千问等）
- **📚 知识库管理**: 基于VikingDB的向量数据库，支持文档上传、分块、语义检索
- **💬 智能对话**: 多轮对话管理，上下文记忆，模板化提示词
- **📊 SEO分析**: 关键词分析、趋势监控、排名跟踪
- **🎨 内容优化**: 基于模板的内容生成和优化
- **👥 多租户管理**: 企业级用户管理、权限控制、配额管理
- **📈 数据监控**: 使用统计、成本分析、性能监控

### 🏗️ 技术架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端 (React)   │    │  后端 (FastAPI)  │    │  数据库 (PG)    │
│                 │    │                 │    │                 │
│ • Material-UI   │◄──►│ • SQLAlchemy    │◄──►│ • PostgreSQL    │
│ • Axios         │    │ • Alembic       │    │ • Redis         │
│ • React Router  │    │ • Pydantic      │    │ • VikingDB      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🛠️ 技术栈

### 后端技术
- **框架**: FastAPI 0.104.1
- **数据库**: PostgreSQL + SQLAlchemy 2.0.23
- **缓存**: Redis 5.0.1
- **迁移**: Alembic 1.13.0
- **认证**: JWT + Python-JOSE
- **异步**: Uvicorn + AsyncPG
- **向量数据库**: 字节跳动 VikingDB
- **AI集成**: 多模型支持（OpenAI、Anthropic、字节跳动等）

### 前端技术
- **框架**: React 19.1.1
- **UI库**: Material-UI 7.2.0
- **状态管理**: React Hooks
- **路由**: React Router DOM 6.30.1
- **HTTP客户端**: Axios 1.11.0
- **图表**: Chart.js 4.5.0
- **Markdown**: React-MD-Editor 4.0.8

## 🚀 快速开始

### 环境要求
- Python 3.9+
- Node.js 16+
- PostgreSQL 13+
- Redis 6+

### 后端启动

1. **克隆项目**
```bash
git clone <repository-url>
cd geo
```

2. **安装依赖**
```bash
cd backend
pip install -r requirements.txt
```

3. **环境配置**
```bash
cp .env.example .env
# 编辑 .env 文件，配置数据库连接、Redis、AI API密钥等
```

4. **数据库迁移**
```bash
alembic upgrade head
```

5. **启动服务**
```bash
python run.py
```

后端服务将在 `http://localhost:8000` 启动

### 前端启动

1. **安装依赖**
```bash
cd frontend
npm install
```

2. **启动开发服务器**
```bash
npm start
```

前端应用将在 `http://localhost:3000` 启动

## 📁 项目结构

```
geo/
├── backend/                 # 后端代码
│   ├── app/
│   │   ├── api/v1/         # API路由
│   │   ├── core/           # 核心配置
│   │   ├── models/         # 数据模型
│   │   ├── services/       # 业务逻辑
│   │   └── utils/          # 工具函数
│   ├── alembic/            # 数据库迁移
│   ├── docs/               # 文档
│   ├── logs/               # 日志文件
│   └── requirements.txt    # Python依赖
├── frontend/               # 前端代码
│   ├── src/
│   │   ├── components/     # React组件
│   │   ├── pages/          # 页面组件
│   │   ├── services/       # API服务
│   │   └── utils/          # 工具函数
│   ├── public/             # 静态资源
│   └── package.json        # Node.js依赖
└── README.md               # 项目说明
```

## 🔧 核心模块

### 1. AI服务模块
- **AIRequest**: AI请求记录和状态管理
- **AIModelConfig**: AI模型配置和参数管理
- **AITemplate**: 模板化提示词管理
- **AIQuota**: 用户配额和使用限制
- **AIUsageStatistics**: 使用统计和成本分析

### 2. 知识库模块
- **UserKnowledgeBase**: 用户专属知识库
- **KnowledgeDocument**: 文档管理和元数据
- **DocumentChunk**: 文档分块和向量化
- **AIConversation**: 对话会话管理
- **AIConversationMessage**: 消息存储和检索

### 3. 用户管理模块
- **User**: 用户账户和认证
- **Company**: 企业多租户管理
- **Permission & Role**: 权限和角色控制
- **Subscription**: 订阅和计费管理

## 🔌 API接口

### 认证接口
- `POST /api/v1/auth/login` - 用户登录
- `POST /api/v1/auth/register` - 用户注册
- `POST /api/v1/auth/refresh` - 刷新Token

### AI服务接口
- `POST /api/v1/ai/content/generate` - 内容生成
- `POST /api/v1/ai/content/optimize` - 内容优化
- `GET /api/v1/ai/templates` - 获取模板列表
- `POST /api/v1/ai/templates` - 创建模板

### 知识库接口
- `GET /api/v1/knowledge-bases` - 获取知识库列表
- `POST /api/v1/knowledge-bases` - 创建知识库
- `POST /api/v1/knowledge-bases/{kb_id}/documents` - 上传文档
- `GET /api/v1/knowledge-bases/{kb_id}/documents/{doc_id}/download` - 下载文档

### 对话管理接口
- `GET /api/v1/conversations` - 获取对话列表
- `POST /api/v1/conversations` - 创建对话
- `GET /api/v1/conversations/{conversation_id}/messages` - 获取消息历史

## 🎨 支持的AI模型

| 模型类型 | 支持的模型 | 功能特性 |
|---------|-----------|----------|
| OpenAI | GPT-4, GPT-3.5 | 文本生成、对话、函数调用 |
| Anthropic | Claude-3 | 长文本理解、安全对话 |
| 字节跳动 | 豆包 | 中文优化、多模态 |
| 阿里云 | 通义千问 | 中文理解、知识问答 |
| 百川智能 | Baichuan | 中文生成、推理 |
| 月之暗面 | Kimi | 长上下文、文档理解 |

## 📊 数据库设计

### 核心表结构
- **users**: 用户基础信息
- **companies**: 企业信息
- **ai_requests**: AI请求记录
- **ai_model_configs**: AI模型配置
- **user_knowledge_bases**: 用户知识库
- **knowledge_documents**: 知识库文档
- **ai_conversations**: AI对话会话
- **ai_conversation_messages**: 对话消息

## 🔒 安全特性

- **JWT认证**: 基于Token的无状态认证
- **权限控制**: 细粒度的RBAC权限管理
- **数据隔离**: 多租户数据安全隔离
- **API限流**: 防止恶意请求和滥用
- **输入验证**: 严格的数据验证和清理
- **日志审计**: 完整的操作日志记录

## 📈 监控和分析

- **使用统计**: 实时的API调用统计
- **成本分析**: AI服务成本跟踪和分析
- **性能监控**: 响应时间和错误率监控
- **用户行为**: 用户操作和偏好分析
- **系统健康**: 服务状态和资源使用监控

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系我们

- 项目地址: [GitLab Repository](http://180.184.183.207:38001/geo/geo)
- 问题反馈: [Issues](http://180.184.183.207:38001/geo/geo/-/issues)
- 文档中心: [Documentation](./backend/docs/)

---

⭐ 如果这个项目对您有帮助，请给我们一个星标！
