"""
Geo分析数据模型
"""
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from enum import Enum

from ...core.geo.config import AIModelType
from ...services.ai.prompt_templates.base_prompts import PromptType

@dataclass
class AnalysisRequest:
    """AI分析请求数据模型"""
    keyword: str
    ai_model: AIModelType
    prompt_type: PromptType
    additional_params: Optional[Dict[str, Any]] = None

@dataclass
class AnalysisResult:
    """AI分析结果数据模型"""
    keyword: str
    ai_model: AIModelType
    success: bool
    content: str
    sources: List[Dict[str, Any]]
    response_time: float
    usage: Optional[Dict[str, Any]] = None
    error: Optional[str] = None

@dataclass
class StructuredData:
    """结构化数据模型"""
    content: Dict[str, Any]
    data_type: str
    keyword: str
    success: bool
    error: Optional[str] = None

@dataclass
class CompleteAnalysisResult:
    """完整分析结果数据模型"""
    keyword: str
    ai_model: AIModelType
    raw_analysis: Optional[AnalysisResult]
    structured_data: Optional[StructuredData]
    success: bool
    error: Optional[str] = None

class AnalysisStatus(Enum):
    """分析状态枚举"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"

@dataclass
class AnalysisTask:
    """分析任务数据模型"""
    task_id: str
    request: AnalysisRequest
    status: AnalysisStatus
    result: Optional[CompleteAnalysisResult] = None
    created_at: Optional[str] = None
    completed_at: Optional[str] = None
    error_message: Optional[str] = None
