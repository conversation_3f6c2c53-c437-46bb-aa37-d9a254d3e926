"""
模板配置数据模型
"""
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from enum import Enum

class TemplateType(Enum):
    """模板类型枚举"""
    DASHBOARD = "dashboard"
    ANALYSIS = "analysis"
    RANKING = "ranking"
    PLATFORM = "platform"
    ACCOUNT = "account"

@dataclass
class TemplateInfo:
    """模板信息"""
    name: str
    type: TemplateType
    title: str
    description: str
    file_path: str
    css_files: List[str]
    js_files: List[str]
    dependencies: List[str] = None

@dataclass
class TemplateContext:
    """模板上下文"""
    title: str
    description: str
    data: Dict[str, Any]
    css_files: List[str] = None
    js_files: List[str] = None
    meta_tags: Dict[str, str] = None

@dataclass
class RenderResult:
    """渲染结果"""
    content: str
    template_name: str
    success: bool
    error: Optional[str] = None
    render_time: Optional[float] = None

class TemplateRegistry:
    """模板注册表"""
    
    def __init__(self):
        self._templates: Dict[str, TemplateInfo] = {}
        self._register_default_templates()
    
    def _register_default_templates(self):
        """注册默认模板"""
        # 仪表板模板
        self.register_template(TemplateInfo(
            name="dashboard",
            type=TemplateType.DASHBOARD,
            title="Geo监控中心 - 仪表板",
            description="系统主仪表板，显示整体统计信息",
            file_path="templates/geo/dashboard.html",  # backend目录下的相对路径
            css_files=[],  # 内联CSS，不需要外部文件
            js_files=[]    # 内联JS，不需要外部文件
        ))

        # 分析模板
        self.register_template(TemplateInfo(
            name="analysis",
            type=TemplateType.ANALYSIS,
            title="Geo监控中心 - AI分析",
            description="AI关键词分析页面",
            file_path="templates/geo/analysis.html",
            css_files=[],
            js_files=[]
        ))

        # 排名分析模板
        self.register_template(TemplateInfo(
            name="ranking",
            type=TemplateType.RANKING,
            title="Geo监控中心 - 关键词排名",
            description="关键词排名分析页面",
            file_path="templates/geo/ranking.html",
            css_files=[],
            js_files=[]
        ))

        # 平台推荐模板
        self.register_template(TemplateInfo(
            name="platform",
            type=TemplateType.PLATFORM,
            title="Geo监控中心 - 平台推荐",
            description="平台推荐分析页面",
            file_path="templates/geo/platform.html",
            css_files=[],
            js_files=[]
        ))

        # 账号推荐模板
        self.register_template(TemplateInfo(
            name="account",
            type=TemplateType.ACCOUNT,
            title="Geo监控中心 - 账号推荐",
            description="账号推荐分析页面",
            file_path="templates/geo/account.html",
            css_files=[],
            js_files=[]
        ))



    def register_template(self, template_info: TemplateInfo):
        """注册模板"""
        self._templates[template_info.name] = template_info
    
    def get_template(self, name: str) -> Optional[TemplateInfo]:
        """获取模板信息"""
        return self._templates.get(name)
    
    def get_all_templates(self) -> List[TemplateInfo]:
        """获取所有模板"""
        return list(self._templates.values())
    
    def get_templates_by_type(self, template_type: TemplateType) -> List[TemplateInfo]:
        """根据类型获取模板"""
        return [t for t in self._templates.values() if t.type == template_type]

# 全局模板注册表实例
template_registry = TemplateRegistry()
