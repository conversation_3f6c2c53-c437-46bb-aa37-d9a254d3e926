"""
权限系统相关模型
"""
from sqlalchemy import Column, String, Boolean, Text, DateTime, ForeignKey, UniqueConstraint
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid

from app.database import Base


class Permission(Base):
    """权限表"""
    __tablename__ = "permissions"
    __table_args__ = {
        'comment': '权限表'
    }

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    permission_name = Column(String(100), nullable=False, comment="权限名称")
    permission_code = Column(String(100), nullable=False, unique=True, comment="权限代码")
    module = Column(String(50), nullable=False, comment="所属模块")
    action = Column(String(50), nullable=False, comment="操作类型")
    resource = Column(String(50), nullable=True, comment="资源类型")
    description = Column(Text, nullable=True, comment="权限描述")
    is_system_permission = Column(Boolean, default=False, comment="是否为系统内置权限")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")

    # 关联关系
    role_permissions = relationship("RolePermission", back_populates="permission", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<Permission(code={self.permission_code}, name={self.permission_name})>"


class Role(Base):
    """角色表"""
    __tablename__ = "roles"
    __table_args__ = {
        'comment': '角色表'
    }

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    role_name = Column(String(50), nullable=False, comment="角色名称")
    role_code = Column(String(50), nullable=False, unique=True, comment="角色代码")
    role_type = Column(String(20), nullable=False, comment="角色类型")
    description = Column(Text, nullable=True, comment="角色描述")
    is_active = Column(Boolean, default=True, comment="是否激活")
    is_system_role = Column(Boolean, default=False, comment="是否为系统内置角色")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")

    # 关联关系
    role_permissions = relationship("RolePermission", back_populates="role", cascade="all, delete-orphan")
    user_roles = relationship("UserRole", back_populates="role", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<Role(code={self.role_code}, name={self.role_name})>"


class RolePermission(Base):
    """角色权限关联表"""
    __tablename__ = "role_permissions"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    role_id = Column(UUID(as_uuid=True), ForeignKey("roles.id", ondelete="CASCADE"), nullable=False)
    permission_id = Column(UUID(as_uuid=True), ForeignKey("permissions.id", ondelete="CASCADE"), nullable=False)
    granted_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True, comment="授权人")
    granted_at = Column(DateTime(timezone=True), server_default=func.now(), comment="授权时间")

    # 唯一约束和表注释
    __table_args__ = (
        UniqueConstraint('role_id', 'permission_id', name='uq_role_permission'),
        {'comment': '角色权限关联表'}
    )

    # 关联关系
    role = relationship("Role", back_populates="role_permissions")
    permission = relationship("Permission", back_populates="role_permissions")
    granted_by_user = relationship("User", foreign_keys=[granted_by])

    def __repr__(self):
        return f"<RolePermission(role_id={self.role_id}, permission_id={self.permission_id})>"


class UserRole(Base):
    """用户角色关联表"""
    __tablename__ = "user_roles"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    role_id = Column(UUID(as_uuid=True), ForeignKey("roles.id", ondelete="CASCADE"), nullable=False)
    role_status = Column(String(20), default="active", comment="角色状态: active, inactive, pending_approval")
    assigned_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True, comment="分配人")
    assigned_at = Column(DateTime(timezone=True), server_default=func.now(), comment="分配时间")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")

    # 唯一约束和表注释
    __table_args__ = (
        UniqueConstraint('user_id', 'role_id', name='uq_user_role'),
        {'comment': '用户角色关联表'}
    )

    # 关联关系
    user = relationship("User", foreign_keys=[user_id], back_populates="user_roles")
    role = relationship("Role", back_populates="user_roles")
    assigned_by_user = relationship("User", foreign_keys=[assigned_by])

    def __repr__(self):
        return f"<UserRole(user_id={self.user_id}, role_id={self.role_id}, status={self.role_status})>"
