from sqlalchemy import Column, String, Boolean, DateTime, Text, Enum, Numeric, Integer, JSON
from sqlalchemy.sql import func
from sqlalchemy.dialects.postgresql import UUID
from app.database import Base
import uuid
from datetime import datetime
import enum

class ProjectStatus(str, enum.Enum):
    ACTIVE = "active"           # 活跃监控
    PAUSED = "paused"          # 暂停监控
    COMPLETED = "completed"     # 已完成
    CANCELLED = "cancelled"     # 已取消

class MonitoringFrequency(str, enum.Enum):
    DAILY = "daily"            # 每日监控
    WEEKLY = "weekly"          # 每周监控
    MONTHLY = "monthly"        # 每月监控

class AISearchEngine(str, enum.Enum):
    DOUBAO = "doubao"          # 豆包AI
    CHATGPT = "chatgpt"        # ChatGPT
    CLAUDE = "claude"          # Claude
    GEMINI = "gemini"          # Gemini
    TONGYI = "tongyi"          # 通义千问
    WENXIN = "wenxin"          # 文心一言

class MonitoringProject(Base):
    __tablename__ = "monitoring_projects"
    __table_args__ = {
        'comment': '监控项目表'
    }
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    company_id = Column(UUID(as_uuid=True), index=True)
    
    # 项目基本信息
    project_name = Column(String(200), nullable=False)
    project_description = Column(Text)
    target_website = Column(String(500), nullable=False)  # 目标网站
    target_brand = Column(String(200), nullable=False)    # 目标品牌
    
    # 监控配置
    keywords = Column(JSON, nullable=False)               # 监控关键词列表
    search_engines = Column(JSON, nullable=False)         # 监控的AI搜索引擎
    monitoring_frequency = Column(Enum(MonitoringFrequency), default=MonitoringFrequency.DAILY)
    
    # 竞争对手
    competitors = Column(JSON)  # 竞争对手列表
    
    # 项目状态
    status = Column(Enum(ProjectStatus), default=ProjectStatus.ACTIVE)
    
    # 统计信息
    total_keywords = Column(Integer, default=0)
    total_rankings = Column(Integer, default=0)
    avg_ranking = Column(Numeric(5, 2))
    best_ranking = Column(Integer)
    
    # 时间信息
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    last_monitored_at = Column(DateTime(timezone=True))
    next_monitor_at = Column(DateTime(timezone=True))

class RankingRecord(Base):
    __tablename__ = "ranking_records"
    __table_args__ = {
        'comment': '排名记录表'
    }
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    project_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    
    # 搜索信息
    keyword = Column(String(200), nullable=False, index=True)
    search_engine = Column(Enum(AISearchEngine), nullable=False)
    search_query = Column(Text, nullable=False)  # 完整搜索查询
    
    # 排名信息
    ranking_position = Column(Integer)  # 排名位置，null表示未找到
    total_results = Column(Integer)     # 总结果数
    
    # 结果详情
    result_title = Column(String(500))
    result_url = Column(String(1000))
    result_snippet = Column(Text)
    result_score = Column(Numeric(5, 4))  # 相关性评分
    
    # 竞争对手排名
    competitor_rankings = Column(JSON)  # 竞争对手在同一查询中的排名
    
    # 监控时间
    monitored_at = Column(DateTime(timezone=True), server_default=func.now(), index=True)
    
    # 元数据
    search_metadata = Column(JSON)  # 搜索相关的元数据

class KeywordTrend(Base):
    __tablename__ = "keyword_trends"
    __table_args__ = {
        'comment': '关键词趋势表'
    }
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    project_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    keyword = Column(String(200), nullable=False, index=True)
    
    # 趋势数据
    date = Column(DateTime(timezone=True), nullable=False, index=True)
    avg_ranking = Column(Numeric(5, 2))
    best_ranking = Column(Integer)
    worst_ranking = Column(Integer)
    total_mentions = Column(Integer, default=0)
    
    # 搜索引擎分布
    engine_rankings = Column(JSON)  # 各搜索引擎的排名分布
    
    # 变化趋势
    ranking_change = Column(Integer)  # 与前一天相比的排名变化
    trend_direction = Column(String(10))  # up/down/stable
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())

class MonitoringAlert(Base):
    __tablename__ = "monitoring_alerts"
    __table_args__ = {
        'comment': '监控告警表'
    }
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    project_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    user_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    
    # 告警信息
    alert_type = Column(String(50), nullable=False)  # ranking_drop/ranking_rise/new_competitor
    alert_title = Column(String(200), nullable=False)
    alert_message = Column(Text, nullable=False)
    severity = Column(String(20), default="medium")  # low/medium/high/critical
    
    # 相关数据
    keyword = Column(String(200))
    search_engine = Column(String(50))
    old_ranking = Column(Integer)
    new_ranking = Column(Integer)
    
    # 状态
    is_read = Column(Boolean, default=False)
    is_resolved = Column(Boolean, default=False)
    
    # 时间
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    read_at = Column(DateTime(timezone=True))
    resolved_at = Column(DateTime(timezone=True))

class MonitoringReport(Base):
    __tablename__ = "monitoring_reports"
    __table_args__ = {
        'comment': '监控报告表'
    }
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    project_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    
    # 报告信息
    report_type = Column(String(50), nullable=False)  # daily/weekly/monthly
    report_period_start = Column(DateTime(timezone=True), nullable=False)
    report_period_end = Column(DateTime(timezone=True), nullable=False)
    
    # 报告数据
    summary_data = Column(JSON, nullable=False)  # 汇总数据
    ranking_changes = Column(JSON)               # 排名变化
    keyword_performance = Column(JSON)           # 关键词表现
    competitor_analysis = Column(JSON)           # 竞争对手分析
    recommendations = Column(JSON)               # 优化建议
    
    # 生成信息
    generated_at = Column(DateTime(timezone=True), server_default=func.now())
    generated_by = Column(String(50), default="system")  # system/manual
