from sqlalchemy import Column, String, DateTime, Text, Enum, Integer, JSON, ForeignKey, Index
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from app.database import Base
import uuid
from datetime import datetime
import enum


class EmbeddingStatus(str, enum.Enum):
    PENDING = "pending"       # 待处理
    PROCESSING = "processing" # 处理中
    COMPLETED = "completed"   # 已完成
    FAILED = "failed"        # 失败


class MessageType(str, enum.Enum):
    USER = "user"           # 用户消息
    ASSISTANT = "assistant" # AI助手消息
    SYSTEM = "system"       # 系统消息





class UserKnowledgeBase(Base):
    """用户知识库表"""
    __tablename__ = "user_knowledge_bases"
    __table_args__ = {
        'comment': '用户知识库表'
    }

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    name = Column(String(200), nullable=False)
    description = Column(Text)
    viking_collection_name = Column(String(100), nullable=False)  # VikingDB集合名
    document_count = Column(Integer, default=0)
    vector_count = Column(Integer, default=0)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 关系定义
    documents = relationship("KnowledgeDocument", back_populates="knowledge_base", cascade="all, delete-orphan")


class KnowledgeDocument(Base):
    """知识库文档表"""
    __tablename__ = "knowledge_documents"
    __table_args__ = (
        Index('idx_kb_status', 'knowledge_base_id', 'embedding_status'),
        {'comment': '知识库文档表'}
    )

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    knowledge_base_id = Column(UUID(as_uuid=True), ForeignKey('user_knowledge_bases.id'), nullable=False)
    title = Column(String(500), nullable=False)
    content = Column(Text, nullable=False)
    file_path = Column(String(1000))  # 原始文件路径
    file_type = Column(String(50))    # pdf, docx, txt, md等
    file_size = Column(Integer)       # 文件大小（字节）
    chunk_count = Column(Integer, default=0)  # 分块数量
    embedding_status = Column(Enum(EmbeddingStatus), default=EmbeddingStatus.PENDING)
    error_message = Column(Text)      # 错误信息
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 关系定义
    knowledge_base = relationship("UserKnowledgeBase", back_populates="documents")
    chunks = relationship("DocumentChunk", back_populates="document", cascade="all, delete-orphan")


class DocumentChunk(Base):
    """文档分块表"""
    __tablename__ = "document_chunks"
    __table_args__ = (
        Index('idx_document_chunk', 'document_id', 'chunk_index'),
        {'comment': '文档分块表'}
    )

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    document_id = Column(UUID(as_uuid=True), ForeignKey('knowledge_documents.id'), nullable=False)
    chunk_index = Column(Integer, nullable=False)  # 分块序号
    content = Column(Text, nullable=False)         # 分块内容
    token_count = Column(Integer)                  # token数量
    viking_vector_id = Column(String(100))         # VikingDB中的向量ID
    embedding_model = Column(String(100))          # 使用的embedding模型
    embedding_status = Column(Enum(EmbeddingStatus), default=EmbeddingStatus.PENDING)
    created_at = Column(DateTime, default=datetime.utcnow)

    # 关系定义
    document = relationship("KnowledgeDocument", back_populates="chunks")


class AIConversation(Base):
    """AI对话会话表"""
    __tablename__ = "ai_conversations"
    __table_args__ = {
        'comment': 'AI对话会话表'
    }

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    title = Column(String(200), nullable=False)
    template_id = Column(UUID(as_uuid=True), ForeignKey('ai_templates.id'))
    template_parameters = Column(JSON)  # 模板参数
    knowledge_bases = Column(JSON)      # 关联的知识库ID列表
    context_summary = Column(Text)      # 上下文摘要
    message_count = Column(Integer, default=0)
    created_at = Column(DateTime, default=datetime.utcnow, index=True)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 关系定义
    messages = relationship("AIConversationMessage", back_populates="conversation", cascade="all, delete-orphan")
    requests = relationship("AIRequest", back_populates="conversation")
    template = relationship("AITemplate", back_populates="conversations")


class AIConversationMessage(Base):
    """AI对话消息表"""
    __tablename__ = "ai_conversation_messages"
    __table_args__ = (
        Index('idx_conversation_created', 'conversation_id', 'created_at'),
        {'comment': 'AI对话消息表'}
    )

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    conversation_id = Column(UUID(as_uuid=True), ForeignKey('ai_conversations.id'), nullable=False)
    message_type = Column(Enum(MessageType), nullable=False)
    content = Column(Text, nullable=False)
    request_id = Column(UUID(as_uuid=True))  # 关联的AI请求ID
    created_at = Column(DateTime, default=datetime.utcnow)

    # 关系定义
    conversation = relationship("AIConversation", back_populates="messages")



