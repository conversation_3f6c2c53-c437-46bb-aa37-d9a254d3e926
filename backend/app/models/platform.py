"""
Platform Integration Models
平台集成相关数据模型
"""

from sqlalchemy import Column, String, Boolean, DateTime, Text, JSON, Integer, ForeignKey, UniqueConstraint, Numeric
from sqlalchemy.sql import func
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from app.database import Base
import uuid
from datetime import datetime


class PlatformConfig(Base):
    """平台配置表"""
    __tablename__ = "platform_configs"
    __table_args__ = {
        'comment': '平台配置表'
    }
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    platform_code = Column(String(50), unique=True, nullable=False, index=True)  # 平台代码
    platform_name = Column(String(100), nullable=False)  # 平台名称
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # Virtual fields for compatibility - not in database
    platform_type = "content"
    api_base_url = ""
    auth_type = "api_key"
    config_schema = {}
    features = []
    rate_limits = {}
    display_order = 0
    
    # 关联关系 - 简化，避免无法解析的外键关系
    pass  # 暂时禁用关联关系，避免SQLAlchemy配置错误


class PlatformCredential(Base):
    """平台凭证表"""
    __tablename__ = "platform_credentials"
    __table_args__ = {
        'comment': '平台凭证表'
    }
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    platform_code = Column(String(50), ForeignKey('platform_configs.platform_code'), nullable=False, index=True)
    username = Column(String(255), nullable=True)  # 用户名
    password = Column(Text, nullable=True)  # 密码
    api_key = Column(Text, nullable=True)  # API密钥
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # Virtual fields for compatibility
    user_id = None
    credential_type = "api_key"
    credentials = None
    expires_at = None
    is_valid = True
    last_verified_at = None
    
    # 关联关系 - 简化
    pass  # 暂时禁用关联关系


class PlatformAuthToken(Base):
    """平台认证令牌表"""
    __tablename__ = "platform_auth_tokens"
    __table_args__ = {
        'comment': '平台认证令牌表'
    }
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    platform_code = Column(String(50), ForeignKey('platform_configs.platform_code'), nullable=False, index=True)
    access_token = Column(Text, nullable=False)  # 访问令牌
    expires_at = Column(DateTime(timezone=True), nullable=False)  # 令牌过期时间
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Virtual fields for compatibility
    credential_id = None
    refresh_token = None
    token_type = "Bearer"
    scope = None
    is_active = True
    refreshed_at = None
    
    # 关联关系 - 简化
    pass  # 暂时禁用关联关系


class PlatformOrderRoute(Base):
    """平台订单路由表"""
    __tablename__ = "platform_order_routes"
    __table_args__ = {
        'comment': '平台订单路由表'
    }
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    order_id = Column(UUID(as_uuid=True), ForeignKey('orders.id'), nullable=False)
    platform_code = Column(String(50), ForeignKey('platform_configs.platform_code'), nullable=False, index=True)
    platform_order_id = Column(String(200), nullable=True)  # 平台方订单ID
    route_status = Column(String(50), nullable=True, default='pending')  # 路由状态
    selected_price = Column(Numeric(10, 2), nullable=True)  # 选择的价格
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # Virtual fields for compatibility
    request_id = None
    route_type = "order"
    request_data = {}
    response_data = {}
    retry_count = 0
    last_retry_at = None
    error_message = None
    completed_at = None
    
    # 关联关系 - 简化
    pass  # 暂时禁用关联关系


# 为了兼容性，创建别名
PlatformToken = PlatformAuthToken
PlatformRoute = PlatformOrderRoute