from sqlalchemy import Column, String, Boolean, DateTime, Text, Integer, Numeric, JSON, ForeignKey, UniqueConstraint
from sqlalchemy.sql import func
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from app.database import Base
import uuid
from datetime import datetime

class ContentProvider(Base):
    __tablename__ = "content_providers"
    __table_args__ = {
        'comment': '内容提供商表'
    }
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), nullable=False, unique=True, index=True)  # 关联用户ID，确保唯一
    
    # 个人/机构基础信息
    provider_name = Column(String(200), nullable=False, index=True)  # 提供商名称
    provider_type = Column(String(20), default="individual")  # individual, company, studio, mcn
    real_name = Column(String(100))  # 真实姓名（个人提供商必填）
    company_name = Column(String(200))  # 公司名称（机构提供商必填）
    id_card_number = Column(String(50))  # 身份证号码
    business_license = Column(String(100))  # 营业执照号
    
    # 联系信息
    contact_phone = Column(String(50))
    contact_email = Column(String(255), nullable=False)
    contact_address = Column(Text)
    
    # 业务信息
    business_description = Column(Text)
    service_categories = Column(JSON)  # 服务类别
    platform_accounts = Column(JSON)  # 平台账号信息
    portfolio_urls = Column(JSON)     # 作品集链接
    qualification_files = Column(JSON)  # 资质文件
    
    # 认证状态管理
    verification_status = Column(String(20), default="pending")  # pending, verified, rejected
    verification_note = Column(Text)  # 认证备注
    verification_time = Column(DateTime(timezone=True))  # 认证时间
    verifier_id = Column(UUID(as_uuid=True))  # 审核人员ID
    
    # 业务状态
    is_active = Column(Boolean, default=True)
    service_rating = Column(Numeric(3, 2), default=0.00)  # 服务评分
    completed_orders = Column(Integer, default=0)  # 完成订单数
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())


class ChannelEarnings(Base):
    """渠道商收益记录"""
    __tablename__ = "channel_earnings"
    __table_args__ = {
        'comment': '渠道商收益表'
    }

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    provider_id = Column(UUID(as_uuid=True), nullable=False, index=True)  # 渠道商ID
    order_id = Column(UUID(as_uuid=True), nullable=False, index=True)  # 订单ID

    # 收益信息
    gross_amount = Column(Numeric(10, 2), nullable=False)  # 总金额
    platform_commission = Column(Numeric(10, 2), nullable=False)  # 平台佣金
    net_amount = Column(Numeric(10, 2), nullable=False)  # 净收益

    # 订单信息
    company_name = Column(String(200))  # 企业名称
    content_title = Column(String(500))  # 内容标题
    content_type = Column(String(50))  # 内容类型

    # 结算状态
    settlement_status = Column(String(20), default="pending")  # pending, settled, cancelled
    settlement_date = Column(DateTime(timezone=True))  # 结算日期

    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())


class ChannelWithdraw(Base):
    """渠道商提现记录"""
    __tablename__ = "channel_withdraws"
    __table_args__ = {
        'comment': '渠道商提现表'
    }

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    provider_id = Column(UUID(as_uuid=True), nullable=False, index=True)  # 渠道商ID

    # 提现信息
    amount = Column(Numeric(10, 2), nullable=False)  # 提现金额
    bank_account = Column(String(100), nullable=False)  # 银行账号
    bank_name = Column(String(100), nullable=False)  # 银行名称
    account_holder = Column(String(100), nullable=False)  # 账户持有人
    withdraw_reason = Column(Text)  # 提现说明

    # 处理状态
    status = Column(String(20), default="pending")  # pending, processing, completed, failed
    processor_id = Column(UUID(as_uuid=True))  # 处理人员ID
    process_note = Column(Text)  # 处理备注

    # 时间戳
    applied_at = Column(DateTime(timezone=True), server_default=func.now())  # 申请时间
    processed_at = Column(DateTime(timezone=True))  # 处理时间
    completed_at = Column(DateTime(timezone=True))  # 完成时间
    estimated_arrival = Column(DateTime(timezone=True))  # 预计到账时间

    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())


class ChannelCategory(Base):
    """渠道分类表 - 定义渠道商的服务分类体系"""
    __tablename__ = "channel_categories"
    __table_args__ = {
        'comment': '渠道分类表'
    }

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)

    # 层级结构
    parent_id = Column(UUID(as_uuid=True), ForeignKey('channel_categories.id'), nullable=True, index=True)  # 父分类ID
    category_level = Column(Integer, nullable=False, default=1, index=True)  # 分类层级：1=主分类，2=子分类
    sort_order = Column(Integer, nullable=False, default=0, index=True)  # 排序顺序

    # 分类基本信息
    category_name = Column(String(100), nullable=False, index=True)  # 分类名称
    category_code = Column(String(50), unique=True, nullable=False, index=True)  # 分类代码，用于程序识别
    category_description = Column(Text)  # 分类描述

    # 状态管理
    is_active = Column(Boolean, default=True)  # 是否启用

    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关系定义
    parent = relationship("ChannelCategory", remote_side=[id], backref="children")  # 父子关系


class ChannelService(Base):
    """渠道服务表 - 存储具体的服务内容"""
    __tablename__ = "channel_services"
    __table_args__ = {
        'comment': '渠道服务表'
    }

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    category_id = Column(UUID(as_uuid=True), ForeignKey('channel_categories.id'), nullable=False, index=True)  # 分类ID

    # 服务来源和创建者信息
    service_source = Column(String(20), nullable=False, default='platform', index=True)  # 服务来源：platform/provider
    provider_id = Column(UUID(as_uuid=True), ForeignKey('content_providers.id'), nullable=True, index=True)  # 创建该服务的渠道商ID

    # 审核信息
    approval_status = Column(String(20), default='approved')  # 审核状态：pending, approved, rejected
    approval_note = Column(Text)  # 审核备注
    approval_time = Column(DateTime(timezone=True))  # 审核时间
    approver_id = Column(UUID(as_uuid=True))  # 审核人员ID

    # 服务基本信息
    service_name = Column(String(200), nullable=False, index=True)  # 服务名称
    service_code = Column(String(50), nullable=False, index=True)  # 服务代码
    service_description = Column(Text, nullable=False)  # 服务详细描述
    service_features = Column(JSON)  # 服务特色/亮点，JSON数组

    # 价格信息
    base_price = Column(Numeric(10, 2), nullable=False)  # 基础价格
    discount_price = Column(Numeric(10, 2))  # 优惠价格
    price_unit = Column(String(20), default='次')  # 价格单位：次、篇、个等

    # 服务规格
    service_specs = Column(JSON)  # 服务规格，如字数范围、图片数量等
    delivery_time = Column(Integer, default=24)  # 交付时间（小时）
    revision_count = Column(Integer, default=2)  # 修改次数

    # 渠道和平台信息
    channel_type = Column(String(50))  # 频道类型
    portal_type = Column(String(50))  # 综合门户类型
    platform_specs = Column(JSON)  # 平台规格信息
    coverage_area = Column(JSON)  # 覆盖区域（原tags字段）

    # 服务状态
    is_active = Column(Boolean, default=True)  # 是否启用

    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关系定义
    category = relationship("ChannelCategory", backref="services")
    creator = relationship("ContentProvider", backref="created_services")


class ChannelCategoryMapping(Base):
    """渠道商服务绑定表 - 记录渠道商绑定的服务"""
    __tablename__ = "channel_category_mappings"
    __table_args__ = (
        UniqueConstraint('service_id', name='uq_channel_category_mappings_service_id'),
        {'comment': '渠道分类映射表'}
    )

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    provider_id = Column(UUID(as_uuid=True), ForeignKey('content_providers.id'), nullable=False, index=True)  # 渠道商ID
    service_id = Column(UUID(as_uuid=True), ForeignKey('channel_services.id'), nullable=False, unique=True, index=True)  # 服务ID - 每个服务只能被绑定一次
    is_active = Column(Boolean, default=True)  # 是否启用该服务

    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关系定义
    provider = relationship("ContentProvider", backref="service_mappings")
    service = relationship("ChannelService", backref="provider_mappings")


class ChannelServiceEvaluation(Base):
    """渠道商服务评价表 - 记录渠道商对绑定服务的评价信息"""
    __tablename__ = "channel_service_evaluations"
    __table_args__ = {
        'comment': '渠道商服务评价表'
    }

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    mapping_id = Column(UUID(as_uuid=True), ForeignKey('channel_category_mappings.id'), nullable=False, unique=True, index=True)  # 服务绑定ID，一对一关系

    # 评价信息
    service_rating = Column(Numeric(3, 2), default=0.00)  # 服务评分 (0.00-5.00)
    quality_rating = Column(Numeric(3, 2), default=0.00)  # 质量评分 (0.00-5.00)
    delivery_rating = Column(Numeric(3, 2), default=0.00)  # 交付评分 (0.00-5.00)
    communication_rating = Column(Numeric(3, 2), default=0.00)  # 沟通评分 (0.00-5.00)

    # 统计信息
    total_orders = Column(Integer, default=0)  # 总订单数
    completed_orders = Column(Integer, default=0)  # 完成订单数
    cancelled_orders = Column(Integer, default=0)  # 取消订单数

    # 排序和优先级
    is_primary = Column(Boolean, default=False)  # 是否为主要服务
    is_recommended = Column(Boolean, default=False)  # 是否推荐该服务

    # 状态管理
    is_active = Column(Boolean, default=True)  # 是否启用

    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关系定义
    mapping = relationship("ChannelCategoryMapping", backref="evaluation")
