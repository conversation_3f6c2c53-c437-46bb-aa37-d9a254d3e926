"""
公告系统数据模型
"""
from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, ForeignKey, Enum
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from app.database import Base
from datetime import datetime
import enum
import uuid


class AnnouncementType(enum.Enum):
    """公告类型枚举"""
    SYSTEM = "system"  # 系统公告
    MAINTENANCE = "maintenance"  # 维护公告
    FEATURE = "feature"  # 功能更新
    PROMOTION = "promotion"  # 推广活动
    NOTICE = "notice"  # 通知公告


class AnnouncementStatus(enum.Enum):
    """公告状态枚举"""
    DRAFT = "draft"  # 草稿
    PUBLISHED = "published"  # 已发布
    ARCHIVED = "archived"  # 已归档


class AnnouncementPriority(enum.Enum):
    """公告优先级枚举"""
    LOW = "low"  # 低优先级
    NORMAL = "normal"  # 普通优先级
    HIGH = "high"  # 高优先级
    URGENT = "urgent"  # 紧急


class AnnouncementTarget(enum.Enum):
    """公告目标用户枚举"""
    ALL = "all"  # 所有用户
    ENTERPRISE = "enterprise"  # 企业用户
    CHANNEL = "channel"  # 渠道商
    AGENT = "agent"  # 代理商
    ADMIN = "admin"  # 管理员


class Announcement(Base):
    """公告表"""
    __tablename__ = "announcements"
    __table_args__ = {'comment': '公告表'}
    
    # 主键
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, comment='公告ID')
    
    # 基本信息
    title = Column(String(200), nullable=False, comment='公告标题')
    content = Column(Text, nullable=False, comment='公告内容')
    summary = Column(String(500), comment='公告摘要')
    
    # 分类和类型
    type = Column(Enum('system', 'maintenance', 'feature', 'promotion', 'notice', name='announcement_type'), nullable=False, default='notice', comment='公告类型')
    priority = Column(Enum('low', 'normal', 'high', 'urgent', name='announcement_priority'), nullable=False, default='normal', comment='优先级')
    target_audience = Column(Enum('all', 'enterprise', 'channel', 'agent', 'admin', name='announcement_target'), nullable=False, default='all', comment='目标用户')

    # 状态和显示
    status = Column(Enum('draft', 'published', 'archived', name='announcement_status'), nullable=False, default='draft', comment='状态')
    is_pinned = Column(Boolean, default=False, comment='是否置顶')
    is_popup = Column(Boolean, default=False, comment='是否弹窗显示')
    
    # 时间控制
    publish_time = Column(DateTime, comment='发布时间')
    expire_time = Column(DateTime, comment='过期时间')
    
    # 统计信息
    view_count = Column(Integer, default=0, comment='查看次数')
    
    # 创建者信息
    created_by = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=False, comment='创建者ID')
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.now, comment='创建时间')
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='更新时间')
    
    # 关系
    creator = relationship("User", foreign_keys=[created_by])

    def __repr__(self):
        return f"<Announcement(id={self.id}, title='{self.title}', status='{self.status.value}')>"

    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'title': self.title,
            'content': self.content,
            'summary': self.summary,
            'type': self.type.value,
            'priority': self.priority.value,
            'target_audience': self.target_audience.value,
            'status': self.status.value,
            'is_pinned': self.is_pinned,
            'is_popup': self.is_popup,
            'publish_time': self.publish_time.isoformat() if self.publish_time else None,
            'expire_time': self.expire_time.isoformat() if self.expire_time else None,
            'view_count': self.view_count,
            'created_by': self.created_by,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
