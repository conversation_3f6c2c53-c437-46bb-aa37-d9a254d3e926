# 导入所有模型，确保它们被注册到SQLAlchemy
# 用户相关模型
from .user import (
    User, VerificationCode, UserStatus, UserRoleEnum,
    UserStatistics, UserActivity, UserRoleHistory
)

# 企业相关模型
from .company import Company

# 渠道商相关模型
from .channel import ContentProvider, ChannelEarnings, ChannelWithdraw, ChannelCategory, ChannelService, ChannelCategoryMapping, ChannelServiceEvaluation

# 代理商相关模型
from .agent import Agent, Commission, CommissionSettlement

# 内容相关模型
from .content import ContentRequest, ContentDelivery, ContentRevision

# 订单相关模型
from .order import (
    Order, Payment, PaymentRefund, Subscription, OrderItem,
    SubscriptionPlan, SubscriptionOrder, QuotaUsage, SubscriptionChange, QuotaAlert,
    OrderType, OrderStatus, PaymentStatus, PaymentMethod, SubscriptionChangeType
)
from .status import OrderStatusLog

# 监控相关模型
from .monitoring import (
    MonitoringProject, RankingRecord, KeywordTrend,
    MonitoringAlert, MonitoringReport
)

# 文件上传相关模型
from .upload import UploadedFile, UploadSession, FileAccessLog
from .ruanwenjie import RuanwenjieMedia, RuanwenjieOrder, RuanwenjieOrderStatus, RuanwenjieConfig

# AI服务相关模型
from .ai_service import (
    AIRequest, AIQuota, AITemplate, AIUsageStatistics, AIModelConfig,
    AIServiceType, AIRequestStatus, AIModelType
)

# 知识库相关模型
from .knowledge import (
    UserKnowledgeBase, KnowledgeDocument, DocumentChunk,
    EmbeddingStatus, AIConversation, AIConversationMessage, MessageType
)

# 系统相关模型
from .system import (
    SystemLog, SystemConfig, SystemStatistics,
    SystemAlert, SystemHealthCheck
)

# 权限相关模型
from .permission import Permission, Role, RolePermission, UserRole

# 推荐相关模型
from .referral import ReferralLink, UserReferral

# 帮助文档相关模型
from .help_document import HelpCategory, HelpDocument, HelpDocumentMedia

# 公告相关模型
from .announcement import (
    Announcement,
    AnnouncementType, AnnouncementStatus, AnnouncementPriority, AnnouncementTarget
)

# 平台相关模型
from .platform import (
    PlatformConfig, PlatformCredential, PlatformAuthToken, PlatformOrderRoute,
    PlatformToken, PlatformRoute  # 别名
)

__all__ = [
    # 用户相关
    "User", "VerificationCode", "UserStatus", "UserRoleEnum",
    "UserStatistics", "UserActivity", "UserRoleHistory",

    # 企业相关
    "Company",

    # 渠道商相关
    "ContentProvider", "ChannelEarnings", "ChannelWithdraw", "ChannelCategory", "ChannelService", "ChannelCategoryMapping", "ChannelServiceEvaluation",

    # 代理商相关
    "Agent", "Commission", "CommissionSettlement",

    # 内容相关
    "ContentRequest", "ContentDelivery", "ContentRevision",

    # 订单相关
    "Order", "Payment", "PaymentRefund", "Subscription", "OrderItem", "OrderStatusLog",
    "SubscriptionPlan", "SubscriptionOrder", "QuotaUsage", "SubscriptionChange", "QuotaAlert",
    "OrderType", "OrderStatus", "PaymentStatus", "PaymentMethod", "SubscriptionChangeType",

    # 监控相关
    "MonitoringProject", "RankingRecord", "KeywordTrend",
    "MonitoringAlert", "MonitoringReport",

    # 文件上传相关
    "UploadedFile", "UploadSession", "FileAccessLog",

    # AI服务相关
    "AIRequest", "AIQuota", "AITemplate", "AIUsageStatistics", "AIModelConfig",
    "AIConversation", "AIConversationMessage", "MessageType",
    "AIServiceType", "AIRequestStatus", "AIModelType",

    # 知识库相关
    "UserKnowledgeBase", "KnowledgeDocument", "DocumentChunk",
    "EmbeddingStatus",

    # 系统相关
    "SystemLog", "SystemConfig", "SystemStatistics",
    "SystemAlert", "SystemHealthCheck",

    # 权限相关
    "Permission", "Role", "RolePermission", "UserRole",

    # 推荐相关
    "ReferralLink", "UserReferral",

    # 软文街相关
    "RuanwenjieMedia", "RuanwenjieOrder", "RuanwenjieOrderStatus", "RuanwenjieConfig",
    
    # 帮助文档相关
    "HelpCategory", "HelpDocument", "HelpDocumentMedia",

    # 公告相关
    "Announcement",
    "AnnouncementType", "AnnouncementStatus", "AnnouncementPriority", "AnnouncementTarget",

    # 平台相关
    "PlatformConfig", "PlatformCredential", "PlatformAuthToken", "PlatformOrderRoute",
    "PlatformToken", "PlatformRoute",  # 别名
]
