from sqlalchemy import Column, String, Boolean, DateTime, Text, Enum, Numeric, Integer, JSON
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.database import Base
import uuid
from datetime import datetime, timezone
import enum

class OrderType(str, enum.Enum):
    SUBSCRIPTION = "subscription"      # 套餐订单
    SUBSCRIPTION_UPGRADE = "subscription_upgrade" # 订阅升级订单
    CONTENT_SERVICE = "content_service" # 内容服务订单

class OrderStatus(str, enum.Enum):
    PENDING = "pending"           # 待支付
    PAID = "paid"                # 已支付
    PROCESSING = "processing"     # 处理中
    COMPLETED = "completed"       # 已完成
    CANCELLED = "cancelled"       # 已取消
    REFUNDED = "refunded"        # 已退款

class PaymentStatus(str, enum.Enum):
    PENDING = "pending"          # 待支付
    SUCCESS = "success"          # 支付成功
    FAILED = "failed"           # 支付失败
    REFUNDED = "refunded"       # 已退款

class PaymentMethod(str, enum.Enum):
    ALIPAY = "alipay"           # 支付宝
    WECHAT = "wechat"           # 微信支付
    BANK_CARD = "bank_card"     # 银行卡
    BALANCE = "balance"         # 余额支付

class Order(Base):
    __tablename__ = "orders"
    __table_args__ = {
        'comment': '订单表'
    }
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    order_no = Column(String(50), unique=True, index=True, nullable=False)
    
    # 订单基本信息
    user_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    company_id = Column(UUID(as_uuid=True), index=True)  # 企业订单
    order_type = Column(Enum(OrderType), nullable=False)
    order_status = Column(Enum(OrderStatus), default=OrderStatus.PENDING)
    
    # 商品信息
    product_id = Column(UUID(as_uuid=True))  # 套餐ID或服务ID
    product_name = Column(String(200), nullable=False)
    product_description = Column(Text)
    product_specs = Column(JSON)  # 产品规格信息
    
    # 价格信息
    original_amount = Column(Numeric(12, 2), nullable=False)  # 原价
    discount_amount = Column(Numeric(12, 2), default=0.00)    # 优惠金额
    final_amount = Column(Numeric(12, 2), nullable=False)     # 实付金额
    currency = Column(String(10), default="CNY")
    
    # 优惠信息
    coupon_id = Column(UUID(as_uuid=True))
    coupon_code = Column(String(50))
    discount_details = Column(JSON)
    
    # 代理商信息
    agent_id = Column(UUID(as_uuid=True))  # 推荐代理商
    commission_rate = Column(Numeric(5, 4))  # 佣金比例
    commission_amount = Column(Numeric(12, 2))  # 佣金金额
    
    # 订单备注
    customer_note = Column(Text)  # 客户备注
    admin_note = Column(Text)     # 管理员备注
    
    # 时间信息
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    paid_at = Column(DateTime(timezone=True))
    completed_at = Column(DateTime(timezone=True))
    cancelled_at = Column(DateTime(timezone=True))
    
    # 有效期信息（套餐订单）
    service_start_date = Column(DateTime(timezone=True))
    service_end_date = Column(DateTime(timezone=True))
    
    # 关系
    status_logs = relationship("OrderStatusLog", back_populates="order")

class Payment(Base):
    __tablename__ = "payments"
    __table_args__ = {
        'comment': '支付记录表'
    }
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    order_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    
    # 支付信息 - 匹配精简版数据库
    payment_method = Column(String(20), nullable=False)  # alipay/wechat/offline/balance/bank_transfer
    payment_status = Column(String(20), nullable=False, default='pending')  # pending/processing/success/failed/refunded/partial_refunded
    amount = Column(Numeric(12, 2), nullable=False)  # 原payment_amount改为amount
    
    # 第三方支付信息
    transaction_no = Column(String(100))  # 第三方交易号
    
    # 时间信息
    paid_at = Column(DateTime(timezone=True))
    refunded_at = Column(DateTime(timezone=True))
    created_at = Column(DateTime(timezone=True), server_default=func.now())

class PaymentRefund(Base):
    __tablename__ = "payment_refunds"
    __table_args__ = {
        'comment': '支付退款表'
    }
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    payment_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    refund_no = Column(String(50), unique=True, nullable=False, index=True)
    
    # 退款信息
    refund_amount = Column(Numeric(12, 2), nullable=False)
    refund_reason = Column(String(500))
    refund_status = Column(String(20), default='pending')  # pending/approved/processing/success/failed/cancelled
    refund_method = Column(String(20))  # 退款方式
    
    # 操作人信息
    operator_id = Column(UUID(as_uuid=True))  # 操作人
    approved_at = Column(DateTime(timezone=True))  # 批准时间
    processed_at = Column(DateTime(timezone=True))  # 处理时间
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())

class Subscription(Base):
    __tablename__ = "subscriptions"
    __table_args__ = {
        'comment': '订阅套餐表'
    }
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    plan_id = Column(UUID(as_uuid=True), nullable=False)
    
    # Status and dates
    status = Column(String(20), default='active')  # active/expired/cancelled/suspended
    start_date = Column(DateTime(timezone=True), nullable=False)
    end_date = Column(DateTime(timezone=True), nullable=False)
    trial_end_date = Column(DateTime(timezone=True))
    billing_cycle = Column(String(20))
    next_billing_date = Column(DateTime(timezone=True))
    
    # Usage tracking (from migration)
    used_content_requests = Column(Integer, default=0)
    used_monitoring_projects = Column(Integer, default=0)
    used_api_calls = Column(Integer, default=0)
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # Additional fields - these are computed properties, not DB columns
    # company_id, plan_name, plan_type will be loaded from related tables
    
    # These are mappings from the new field names
    @property
    def content_quota(self):
        # Get from plan relationship
        return 0
    
    @property
    def monitoring_quota(self):
        # Get from plan relationship  
        return 0
    
    @property
    def ai_quota(self):
        # Get from plan relationship
        return 0
    
    @property
    def content_used(self):
        return self.used_content_requests or 0
    
    @property
    def monitoring_used(self):
        return self.used_monitoring_projects or 0
    
    @property
    def ai_used(self):
        return self.used_api_calls or 0
    
    @property
    def is_active(self):
        return self.status == 'active'
    
    @property
    def auto_renewal(self):
        # This would need to be stored elsewhere or in a settings table
        return False
    
    @property
    def renewal_price(self):
        # This would need to be calculated from plan
        return None

class OrderItem(Base):
    __tablename__ = "order_items"
    __table_args__ = {
        'comment': '订单明细表'
    }
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    order_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    
    # 商品信息
    item_type = Column(String(50), nullable=False)  # subscription/content/monitoring/ai
    item_name = Column(String(200), nullable=False)
    item_description = Column(Text)
    item_specs = Column(JSON)
    
    # 数量和价格
    quantity = Column(Integer, default=1)
    unit_price = Column(Numeric(12, 2), nullable=False)
    total_price = Column(Numeric(12, 2), nullable=False)
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc))

class SubscriptionPlan(Base):
    __tablename__ = "subscription_plans"
    __table_args__ = {
        'comment': '订阅套餐计划表'
    }
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    plan_code = Column(String(50), nullable=False, unique=True)
    plan_name = Column(String(100), nullable=False)
    plan_type = Column(String(20), nullable=False)  # free/basic/pro/enterprise/custom
    target_user_type = Column(String(50), nullable=False)  # enterprise/provider/both
    
    # 价格设置
    monthly_price = Column(Numeric(12, 2), default=0)
    quarterly_price = Column(Numeric(12, 2))
    semi_annual_price = Column(Numeric(12, 2))
    yearly_price = Column(Numeric(12, 2))
    
    # 配额限制（企业用户）
    max_content_requests = Column(Integer, default=0)
    max_monitoring_projects = Column(Integer, default=0)
    max_api_calls = Column(Integer, default=0)
    max_team_members = Column(Integer, default=1)
    
    # 配额限制（内容提供商）
    max_service_orders = Column(Integer, default=0)
    max_channels = Column(Integer, default=1)
    commission_rate = Column(Numeric(5, 4), default=0)
    
    # 功能权限
    features = Column(JSON)
    
    # 状态
    is_active = Column(Boolean, default=True)
    display_order = Column(Integer, default=0)
    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

class SubscriptionOrder(Base):
    __tablename__ = "subscription_orders"
    __table_args__ = {
        'comment': '订阅订单表'
    }
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    order_id = Column(UUID(as_uuid=True), nullable=False, unique=True, index=True)
    plan_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    billing_cycle = Column(String(20), nullable=False)  # monthly/quarterly/semi_annual/yearly
    start_date = Column(DateTime(timezone=True))
    end_date = Column(DateTime(timezone=True))

class QuotaUsage(Base):
    __tablename__ = "quota_usage"
    __table_args__ = {
        'comment': '配额使用记录表'
    }
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    subscription_id = Column(UUID(as_uuid=True), nullable=False)
    usage_month = Column(String(7), nullable=False, index=True)  # YYYY-MM
    user_type = Column(String(20), nullable=False)
    
    # 企业用户配额
    max_content_requests = Column(Integer, default=0)
    used_content_requests = Column(Integer, default=0)
    max_monitoring_projects = Column(Integer, default=0)
    used_monitoring_projects = Column(Integer, default=0)
    max_api_calls = Column(Integer, default=0)
    used_api_calls = Column(Integer, default=0)
    max_team_members = Column(Integer, default=1)
    used_team_members = Column(Integer, default=1)
    
    # 内容提供商配额
    max_service_orders = Column(Integer, default=0)
    used_service_orders = Column(Integer, default=0)
    max_channels = Column(Integer, default=1)
    used_channels = Column(Integer, default=1)
    
    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

class SubscriptionChangeType(str, enum.Enum):
    UPGRADE = "upgrade"
    DOWNGRADE = "downgrade"
    RENEWAL = "renewal"
    CANCELLATION = "cancellation"
    PAUSE = "pause"
    RESUME = "resume"

class SubscriptionChange(Base):
    __tablename__ = "subscription_changes"
    __table_args__ = {
        'comment': '订阅变更记录表'
    }
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    subscription_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    user_id = Column(UUID(as_uuid=True), nullable=False)
    change_type = Column(String(50), nullable=False)  # upgrade/downgrade/renewal/cancellation
    from_plan_id = Column(UUID(as_uuid=True))
    to_plan_id = Column(UUID(as_uuid=True))
    from_billing_cycle = Column(String(20))
    to_billing_cycle = Column(String(20))
    amount = Column(Numeric(12, 2))
    effective_date = Column(DateTime(timezone=True), nullable=False)
    order_id = Column(UUID(as_uuid=True))
    reason = Column(Text)
    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc))
    created_by = Column(UUID(as_uuid=True))

class QuotaAlert(Base):
    __tablename__ = "quota_alerts"
    __table_args__ = {
        'comment': '配额预警表'
    }
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    subscription_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    user_id = Column(UUID(as_uuid=True), nullable=False)
    alert_type = Column(String(50), nullable=False)  # content/monitoring/api/storage
    threshold_percentage = Column(Integer, nullable=False)  # 80/90/100
    notification_channels = Column(JSON)  # email/sms/webhook
    notification_frequency = Column(String(50))  # once/daily/weekly
    is_active = Column(Boolean, default=True)
    last_alert_at = Column(DateTime(timezone=True))
    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))
