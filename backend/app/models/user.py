from sqlalchemy import Column, String, Boolean, DateTime, Text, Enum, Integer, JSON, Numeric, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.database import Base
import uuid
from datetime import datetime
import enum

class UserStatus(str, enum.Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"
    BANNED = "banned"

class UserRoleEnum(str, enum.Enum):
    SUPER_ADMIN = "super_admin"
    ADMIN = "admin"
    ENTERPRISE_USER = "enterprise_user"
    CHANNEL_USER = "channel_user"
    AGENT_USER = "agent_user"

class User(Base):
    __tablename__ = "users"
    __table_args__ = {
        'comment': '用户表'
    }
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    email = Column(String(255), unique=True, index=True, nullable=False)
    full_name = Column(String(100), nullable=False)
    phone = Column(String(20), unique=True, index=True)
    password_hash = Column(String(255), nullable=False)
    status = Column(Enum(UserStatus), default=UserStatus.ACTIVE)
    email_verified = Column(Boolean, default=False)
    phone_verified = Column(Boolean, default=False)
    referral_code = Column(String(20), unique=True, index=True)
    # referred_by = Column(UUID(as_uuid=True), nullable=True)  # 暂时注释，数据库中不存在此字段
    last_login_at = Column(DateTime(timezone=True), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关联关系
    user_roles = relationship("UserRole", foreign_keys="UserRole.user_id", back_populates="user", cascade="all, delete-orphan")
    ruanwenjie_orders = relationship("RuanwenjieOrder", back_populates="user", cascade="all, delete-orphan")

# UserRoleAssignment 已移除，使用新的权限系统中的 UserRole 模型

class VerificationCode(Base):
    __tablename__ = "verification_codes"
    __table_args__ = {
        'comment': '验证码表'
    }

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    email = Column(String(255), nullable=False, index=True)
    code = Column(String(10), nullable=False)
    code_type = Column(String(50), nullable=False)  # register, login, reset_password, bind_email
    is_used = Column(Boolean, default=False)
    expires_at = Column(DateTime(timezone=True), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

class UserStatistics(Base):
    __tablename__ = "user_statistics"
    __table_args__ = {
        'comment': '用户统计表'
    }

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), nullable=False, index=True, unique=True)
    total_orders = Column(Integer, default=0)
    total_spent = Column(Numeric(10, 2), default=0.00)
    active_projects = Column(Integer, default=0)
    referral_count = Column(Integer, default=0)
    login_count = Column(Integer, default=0)
    last_activity_at = Column(DateTime(timezone=True), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

class UserActivity(Base):
    __tablename__ = "user_activities"
    __table_args__ = {
        'comment': '用户活动表'
    }

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    action = Column(String(50), nullable=False)
    description = Column(Text)
    ip_address = Column(String(45))
    user_agent = Column(Text)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

class UserRoleHistory(Base):
    __tablename__ = "user_role_history"
    __table_args__ = {
        'comment': '用户角色历史表'
    }

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    old_roles = Column(JSON)
    new_roles = Column(JSON)
    reason = Column(Text)
    operator_id = Column(UUID(as_uuid=True), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
