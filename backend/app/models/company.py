from sqlalchemy import Column, String, Boolean, DateTime, Text, Enum, Numeric, JSON, Integer
from sqlalchemy.sql import func
from sqlalchemy.dialects.postgresql import UUID
from app.database import Base
import uuid
from datetime import datetime
import enum

class CompanySize(str, enum.Enum):
    STARTUP = "startup"      # 创业公司 (<50人)
    SMALL = "small"          # 小型企业 (50-200人)
    MEDIUM = "medium"        # 中型企业 (200-1000人)
    LARGE = "large"          # 大型企业 (1000-5000人)
    ENTERPRISE = "enterprise" # 超大型企业 (>5000人)

class VerificationStatus(str, enum.Enum):
    PENDING = "pending"      # 待审核
    VERIFIED = "verified"    # 已认证
    REJECTED = "rejected"    # 已拒绝

class Company(Base):
    __tablename__ = "companies"
    __table_args__ = {
        'comment': '企业信息表'
    }
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), nullable=False, index=True)  # 关联用户ID
    
    # 基本信息
    company_name = Column(String(200), nullable=False, index=True)
    company_name_en = Column(String(200))
    company_code = Column(String(50), nullable=False, unique=True, index=True)  # 统一社会信用代码（营业执照号）
    legal_person = Column(String(100), nullable=False)
    
    # 联系信息
    contact_phone = Column(String(20))
    contact_email = Column(String(255), nullable=False)
    headquarters_location = Column(String(500), nullable=False)
    
    # 企业属性
    industry = Column(String(100), nullable=False)
    company_size = Column(String(50), nullable=False)
    founded_year = Column(Integer)
    business_scope = Column(Text)
    company_description = Column(Text)
    official_website = Column(String(255))
    
    # 资质文件
    business_license_url = Column(String(500))  # 营业执照
    other_files = Column(JSON)  # 其他资质文件
    
    # 社交媒体
    social_media_links = Column(JSON)
    
    # 审核状态
    verification_status = Column(String(50), default="pending")
    verification_time = Column(DateTime(timezone=True))
    verification_note = Column(Text)
    verifier_id = Column(UUID(as_uuid=True))
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    submitted_at = Column(DateTime(timezone=True))  # 提交审核时间
