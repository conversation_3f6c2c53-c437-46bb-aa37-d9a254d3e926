from sqlalchemy import Column, String, Boolean, DateTime, Text, Enum, Integer, JSON, Numeric, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from app.database import Base
import uuid
import enum


class AIServiceType(str, enum.Enum):
    CONTENT_GENERATION = "content_generation"    # 内容生成
    CONTENT_OPTIMIZATION = "content_optimization" # 内容优化
    KEYWORD_ANALYSIS = "keyword_analysis"        # 关键词分析
    SEO_ANALYSIS = "seo_analysis"               # SEO分析
    TREND_ANALYSIS = "trend_analysis"           # 趋势分析
    CHAT = "chat"                               # 对话服务
    EMBEDDING = "embedding"                     # 向量化服务
    COMPLETION = "completion"                   # 文本补全
    TRANSLATION = "translation"                 # 翻译服务
    SUMMARIZATION = "summarization"             # 摘要服务

class AIRequestStatus(str, enum.Enum):
    PENDING = "pending"       # 待处理
    PROCESSING = "processing" # 处理中
    COMPLETED = "completed"   # 已完成
    FAILED = "failed"        # 失败
    CANCELLED = "cancelled"   # 已取消

class AIModelType(str, enum.Enum):
    GPT = "gpt"             # OpenAI GPT系列
    CLAUDE = "claude"       # Anthropic Claude系列
    DOUBAO = "doubao"       # 字节跳动豆包
    QWEN = "qwen"           # 阿里通义千问
    BAICHUAN = "baichuan"   # 百川智能
    KIMI = "kimi"           # 月之暗面Kimi
    DEEPSEEK = "deepseek"   # DeepSeek
    GROK = "grok"           # xAI Grok
    PERPLEXITY = "perplexity" # Perplexity
    CUSTOM = "custom"       # 自定义模型

class AIRequest(Base):
    __tablename__ = "ai_requests"
    __table_args__ = {
        'comment': 'AI服务请求表'
    }

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    company_id = Column(UUID(as_uuid=True), index=True)

    # 请求信息
    service_type = Column(Enum(AIServiceType), nullable=False)
    ai_model = Column(Enum(AIModelType), nullable=False)
    request_title = Column(String(200))
    request_prompt = Column(Text, nullable=False)
    request_parameters = Column(JSON)  # 请求参数

    # 输入内容
    input_content = Column(Text)
    input_keywords = Column(JSON)  # 输入关键词
    input_metadata = Column(JSON)  # 输入元数据

    # 响应信息
    status = Column(Enum(AIRequestStatus), default=AIRequestStatus.PENDING)
    response_content = Column(Text)
    response_data = Column(JSON)  # 结构化响应数据
    response_metadata = Column(JSON)  # 响应元数据

    # 质量评估
    quality_score = Column(Numeric(3, 2))  # 质量评分 0-5
    user_rating = Column(Integer)  # 用户评分 1-5
    user_feedback = Column(Text)  # 用户反馈

    # 使用统计
    tokens_used = Column(Integer, default=0)  # 使用的token数
    processing_time = Column(Integer)  # 处理时间（毫秒）
    cost_amount = Column(Numeric(10, 4))  # 成本金额

    # 业务关联
    business_type = Column(String(50))  # 关联业务类型
    business_id = Column(String(100))  # 关联业务ID
    conversation_id = Column(UUID(as_uuid=True), ForeignKey('ai_conversations.id'))  # 关联对话ID

    # 时间信息
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    started_at = Column(DateTime(timezone=True))
    completed_at = Column(DateTime(timezone=True))
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关系
    conversation = relationship("AIConversation", back_populates="requests")


class AIQuota(Base):
    __tablename__ = "ai_quotas"
    __table_args__ = {
        'comment': 'AI服务配额表'
    }

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), nullable=False, index=True)

    # 配额信息
    service_type = Column(Enum(AIServiceType), nullable=False)
    total_quota = Column(Integer, nullable=False)  # 总配额
    used_quota = Column(Integer, default=0)  # 已使用配额
    remaining_quota = Column(Integer, nullable=False)  # 剩余配额

    # 周期信息
    quota_period = Column(String(20), default="monthly")  # 配额周期
    period_start = Column(DateTime(timezone=True), nullable=False)
    period_end = Column(DateTime(timezone=True), nullable=False)

    # 状态
    is_active = Column(Boolean, default=True)

    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())


class AITemplate(Base):
    __tablename__ = "ai_templates"
    __table_args__ = {
        'comment': 'AI模板表'
    }

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)

    # 模板信息
    template_name = Column(String(200), nullable=False)
    template_description = Column(Text)

    # 模板内容
    prompt_template = Column(Text, nullable=False)
    default_parameters = Column(JSON)

    # 使用统计
    usage_count = Column(Integer, default=0)
    success_rate = Column(Numeric(5, 4), default=0.0000)
    avg_quality_score = Column(Numeric(3, 2))

    # 状态
    is_active = Column(Boolean, default=True)

    # 创建信息
    created_by = Column(UUID(as_uuid=True))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关系
    conversations = relationship("AIConversation", back_populates="template")


class AIUsageStatistics(Base):
    __tablename__ = "ai_usage_statistics"
    __table_args__ = {
        'comment': 'AI使用统计表'
    }

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), nullable=False, index=True)

    # 统计周期
    date = Column(DateTime(timezone=True), nullable=False, index=True)
    period_type = Column(String(20), default="daily")  # daily/weekly/monthly

    # 使用统计
    service_type = Column(Enum(AIServiceType), nullable=False)
    total_requests = Column(Integer, default=0)
    successful_requests = Column(Integer, default=0)
    failed_requests = Column(Integer, default=0)
    total_tokens = Column(Integer, default=0)
    total_cost = Column(Numeric(10, 4), default=0.0000)

    # 质量统计
    avg_quality_score = Column(Numeric(3, 2))
    avg_user_rating = Column(Numeric(3, 2))
    avg_processing_time = Column(Integer)  # 平均处理时间（毫秒）

    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())


class AIModelConfig(Base):
    """AI模型配置表"""
    __tablename__ = "ai_model_configs"
    __table_args__ = {
        'comment': 'AI模型配置表'
    }

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    model_name = Column(String(100), nullable=False, unique=True)  # 模型名称
    model_type = Column(Enum(AIModelType), nullable=False)         # 模型类型
    service_type = Column(Enum(AIServiceType), nullable=False)     # 服务类型

    # 模型配置
    api_endpoint = Column(String(500))     # API端点
    api_key_name = Column(String(100))     # API密钥环境变量名
    max_tokens = Column(Integer)           # 最大token数
    context_window = Column(Integer)       # 上下文窗口大小

    # 定价信息（每1000 tokens）
    input_price = Column(Numeric(10, 6))   # 输入价格
    output_price = Column(Numeric(10, 6))  # 输出价格

    # 模型特性
    supports_streaming = Column(Boolean, default=False)    # 是否支持流式输出
    supports_function_calling = Column(Boolean, default=False)  # 是否支持函数调用
    supports_vision = Column(Boolean, default=False)       # 是否支持视觉理解

    # 状态
    is_enabled = Column(Boolean, default=True)
    is_default = Column(Boolean, default=False)  # 是否为默认模型

    # 描述和配置
    description = Column(Text)             # 模型描述
    config_json = Column(JSON)             # 额外配置

    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
