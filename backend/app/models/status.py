"""
订单状态日志ORM模型
"""
from sqlalchemy import Column, String, Text, UUID, DateTime, ForeignKey
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime
import uuid

from app.database import Base


class OrderStatusLog(Base):
    """订单状态变更日志"""
    __tablename__ = "order_status_logs"
    __table_args__ = {
        'comment': '订单状态日志表'
    }
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    order_id = Column(UUID(as_uuid=True), ForeignKey("orders.id"), nullable=False, index=True)
    from_status = Column(String(20), nullable=True)  # 原状态，首次创建时为空
    to_status = Column(String(20), nullable=False, index=True)
    changed_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)  # 系统变更时可为空
    change_reason = Column(Text, nullable=True)
    meta_info = Column("metadata", JSONB, nullable=True)  # 存储额外信息，在数据库中列名为metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now(), index=True)
    
    # 关系
    order = relationship("Order", back_populates="status_logs")
    changer = relationship("User", foreign_keys=[changed_by])
    
    def __repr__(self):
        return f"<OrderStatusLog {self.id}: {self.from_status} -> {self.to_status}>"