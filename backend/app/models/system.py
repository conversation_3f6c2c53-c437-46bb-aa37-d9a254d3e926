from sqlalchemy import Column, String, Boolean, DateTime, Text, Enum, Integer, JSON, Numeric
from sqlalchemy.sql import func
from sqlalchemy.dialects.postgresql import UUID
from app.database import Base
import uuid
from datetime import datetime
import enum

class LogLevel(str, enum.Enum):
    DEBUG = "debug"
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

class ConfigCategory(str, enum.Enum):
    SYSTEM = "system"           # 系统配置
    SECURITY = "security"       # 安全配置
    PAYMENT = "payment"         # 支付配置
    AI_SERVICE = "ai_service"   # AI服务配置
    EMAIL = "email"             # 邮件配置
    STORAGE = "storage"         # 存储配置
    REFERRAL = "referral"       # 推广链接配置

class SystemLog(Base):
    __tablename__ = "system_logs"
    __table_args__ = {
        'comment': '系统日志表'
    }
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # 日志基本信息
    level = Column(Enum(LogLevel), nullable=False, index=True)
    message = Column(Text, nullable=False)
    module = Column(String(100), nullable=False, index=True)  # 模块名称
    function = Column(String(100))  # 函数名称
    
    # 用户信息
    user_id = Column(UUID(as_uuid=True), index=True)
    user_email = Column(String(255))
    user_role = Column(String(50))
    
    # 请求信息
    request_id = Column(String(100), index=True)
    ip_address = Column(String(45))
    user_agent = Column(Text)
    request_method = Column(String(10))
    request_path = Column(String(500))
    request_params = Column(JSON)
    
    # 响应信息
    response_status = Column(Integer)
    response_time = Column(Integer)  # 响应时间（毫秒）
    
    # 错误信息
    error_type = Column(String(100))
    error_message = Column(Text)
    stack_trace = Column(Text)
    
    # 额外数据
    extra_data = Column(JSON)
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), index=True)

class SystemConfig(Base):
    __tablename__ = "system_configs"
    __table_args__ = {
        'comment': '系统配置表'
    }
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # 配置信息
    config_key = Column(String(200), unique=True, nullable=False, index=True)
    config_value = Column(Text)
    config_type = Column(String(50), default="string")  # string/integer/boolean/json
    category = Column(Enum(ConfigCategory), nullable=False, index=True)
    
    # 配置描述
    display_name = Column(String(200), nullable=False)
    description = Column(Text)
    default_value = Column(Text)
    
    # 验证规则
    validation_rules = Column(JSON)  # 验证规则
    is_sensitive = Column(Boolean, default=False)  # 是否敏感信息
    is_readonly = Column(Boolean, default=False)   # 是否只读
    
    # 状态
    is_active = Column(Boolean, default=True)
    
    # 更新信息
    updated_by = Column(UUID(as_uuid=True))
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    created_at = Column(DateTime(timezone=True), server_default=func.now())

class SystemStatistics(Base):
    __tablename__ = "system_statistics"
    __table_args__ = {
        'comment': '系统统计表'
    }
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # 统计日期
    date = Column(DateTime(timezone=True), nullable=False, index=True)
    period_type = Column(String(20), default="daily")  # daily/weekly/monthly
    
    # 用户统计
    total_users = Column(Integer, default=0)
    new_users = Column(Integer, default=0)
    active_users = Column(Integer, default=0)
    enterprise_users = Column(Integer, default=0)
    channel_users = Column(Integer, default=0)
    agent_users = Column(Integer, default=0)
    
    # 业务统计
    total_orders = Column(Integer, default=0)
    new_orders = Column(Integer, default=0)
    total_revenue = Column(Numeric(12, 2), default=0.00)
    new_revenue = Column(Numeric(12, 2), default=0.00)
    
    # 内容统计
    total_content_requests = Column(Integer, default=0)
    new_content_requests = Column(Integer, default=0)
    completed_content_requests = Column(Integer, default=0)
    
    # 监控统计
    total_monitoring_projects = Column(Integer, default=0)
    new_monitoring_projects = Column(Integer, default=0)
    active_monitoring_projects = Column(Integer, default=0)
    
    # AI服务统计
    total_ai_requests = Column(Integer, default=0)
    new_ai_requests = Column(Integer, default=0)
    ai_tokens_used = Column(Integer, default=0)
    ai_cost = Column(Numeric(10, 4), default=0.0000)
    
    # 系统统计
    total_files = Column(Integer, default=0)
    total_file_size = Column(Integer, default=0)  # 字节
    api_requests = Column(Integer, default=0)
    error_count = Column(Integer, default=0)
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

class SystemAlert(Base):
    __tablename__ = "system_alerts"
    __table_args__ = {
        'comment': '系统告警表'
    }
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # 告警信息
    alert_type = Column(String(50), nullable=False)  # system_error/performance/security
    alert_level = Column(String(20), default="medium")  # low/medium/high/critical
    alert_title = Column(String(200), nullable=False)
    alert_message = Column(Text, nullable=False)
    
    # 告警数据
    alert_data = Column(JSON)
    threshold_value = Column(Numeric(10, 2))
    current_value = Column(Numeric(10, 2))
    
    # 状态
    is_resolved = Column(Boolean, default=False)
    resolved_by = Column(UUID(as_uuid=True))
    resolved_at = Column(DateTime(timezone=True))
    resolution_note = Column(Text)
    
    # 通知状态
    is_notified = Column(Boolean, default=False)
    notification_channels = Column(JSON)  # 通知渠道
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

class SystemHealthCheck(Base):
    __tablename__ = "system_health_checks"
    __table_args__ = {
        'comment': '系统健康检查表'
    }
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # 检查信息
    component_name = Column(String(100), nullable=False)  # database/redis/ai_service/storage
    check_type = Column(String(50), nullable=False)       # connectivity/performance/capacity
    
    # 检查结果
    status = Column(String(20), nullable=False)  # healthy/warning/critical/unknown
    response_time = Column(Integer)  # 响应时间（毫秒）
    
    # 详细信息
    check_details = Column(JSON)
    error_message = Column(Text)
    
    # 时间戳
    checked_at = Column(DateTime(timezone=True), server_default=func.now())
