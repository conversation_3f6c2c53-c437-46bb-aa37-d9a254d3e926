#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
软文街相关数据模型

该模块定义软文街业务相关的数据库表结构，包括：
1. 软文街媒体资源表
2. 软文街订单表
3. 软文街订单状态记录表
"""

from sqlalchemy import Column, String, Integer, Text, DateTime, Boolean, JSON, Numeric, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.database import Base
import uuid


class RuanwenjieMedia(Base):
    """软文街媒体资源表"""
    __tablename__ = "ruanwenjie_media"
    __table_args__ = {'comment': '软文街媒体资源表'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, comment="媒体资源唯一标识")
    external_id = Column(Integer, unique=True, nullable=False, comment="软文街媒体ID")
    media_name = Column(String(200), nullable=False, comment="媒体名称")
    media_url = Column(String(500), comment="媒体网站URL")
    
    # 媒体分类信息
    taxonomy = Column(String(100), comment="频道类型，如'新闻资讯'")
    platform = Column(String(100), comment="综合门户，如'知名媒体'")
    area = Column(String(100), comment="区域，如'全国'、'北京'")
    
    # 媒体属性
    is_baidu_news = Column(Boolean, default=False, comment="是否百度新闻源")
    entrance_level = Column(Integer, comment="入口级别：0不限、1首页入口、2频道首页、3栏目页面、4没有入口")
    url_type = Column(String(50), comment="链接类型，如'不带'、'可带'")
    
    # 价格信息
    price = Column(Numeric(10, 2), comment="发布价格")
    currency = Column(String(10), default='CNY', comment="货币单位")
    
    # 媒体质量指标
    weight = Column(Integer, comment="媒体权重")
    daily_visits = Column(Integer, comment="日访问量")
    alexa_rank = Column(Integer, comment="Alexa排名")
    
    # 发布要求
    content_requirements = Column(JSON, comment="内容要求，JSON格式存储")
    publish_rules = Column(JSON, comment="发布规则，JSON格式存储")
    
    # 状态信息
    is_active = Column(Boolean, default=True, comment="是否可用")
    is_test_media = Column(Boolean, default=False, comment="是否为测试媒体")
    last_sync_at = Column(DateTime(timezone=True), comment="最后同步时间")
    
    # 元数据
    extra_data = Column(JSON, comment="其他元数据信息")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")

    def __repr__(self):
        return f"<RuanwenjieMedia(id={self.id}, name={self.media_name})>"


class RuanwenjieOrder(Base):
    """软文街订单表"""
    __tablename__ = "ruanwenjie_orders"
    __table_args__ = {'comment': '软文街订单表'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, comment="订单唯一标识")
    external_order_id = Column(String(100), unique=True, nullable=False, comment="软文街订单ID")
    
    # 关联信息
    user_id = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=False, comment="创建订单的用户ID")
    media_id = Column(UUID(as_uuid=True), ForeignKey('ruanwenjie_media.id'), comment="关联的媒体资源ID")
    external_media_id = Column(Integer, nullable=False, comment="软文街媒体ID")
    
    # 订单内容
    title = Column(String(500), nullable=False, comment="文章标题")
    content = Column(Text, nullable=False, comment="文章内容")
    content_hash = Column(String(64), comment="内容哈希值，用于去重")
    
    # 订单状态
    status = Column(String(50), default='pending', comment="订单状态：pending(待处理), processing(处理中), published(已发布), failed(失败), cancelled(已取消)")
    payment_status = Column(String(50), default='unpaid', comment="支付状态：unpaid(未支付), paid(已支付), refunded(已退款)")
    
    # 发布信息
    published_url = Column(String(500), comment="发布后的文章URL")
    published_at = Column(DateTime(timezone=True), comment="发布时间")
    
    # 价格信息
    original_price = Column(Numeric(10, 2), comment="原价")
    actual_price = Column(Numeric(10, 2), comment="实际支付价格")
    currency = Column(String(10), default='CNY', comment="货币单位")
    
    # 错误信息
    error_message = Column(Text, comment="错误信息")
    retry_count = Column(Integer, default=0, comment="重试次数")
    
    # 业务关联
    business_type = Column(String(50), comment="业务类型，如'content_request'")
    business_id = Column(String(100), comment="业务ID，关联到具体的业务记录")
    
    # 元数据
    extra_data = Column(JSON, comment="订单元数据")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")

    # 关联关系
    user = relationship("User", back_populates="ruanwenjie_orders")
    media = relationship("RuanwenjieMedia")
    status_records = relationship("RuanwenjieOrderStatus", back_populates="order", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<RuanwenjieOrder(id={self.id}, external_id={self.external_order_id}, status={self.status})>"


class RuanwenjieOrderStatus(Base):
    """软文街订单状态记录表"""
    __tablename__ = "ruanwenjie_order_status"
    __table_args__ = {'comment': '软文街订单状态记录表'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, comment="状态记录唯一标识")
    order_id = Column(UUID(as_uuid=True), ForeignKey('ruanwenjie_orders.id'), nullable=False, comment="关联的订单ID")
    
    # 状态信息
    old_status = Column(String(50), comment="变更前状态")
    new_status = Column(String(50), nullable=False, comment="变更后状态")
    status_code = Column(Integer, comment="软文街返回的状态码")
    
    # 变更详情
    change_reason = Column(String(200), comment="状态变更原因")
    response_message = Column(Text, comment="软文街返回的响应消息")
    published_url = Column(String(500), comment="发布URL（如果有）")
    
    # 回调信息
    callback_data = Column(JSON, comment="完整的回调数据")
    callback_ip = Column(String(45), comment="回调来源IP")
    callback_user_agent = Column(String(500), comment="回调User-Agent")
    
    # 处理信息
    processed_by = Column(String(100), comment="处理方式：callback(回调), manual(手动), system(系统)")
    processed_at = Column(DateTime(timezone=True), server_default=func.now(), comment="处理时间")
    
    # 元数据
    extra_data = Column(JSON, comment="其他元数据")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")

    # 关联关系
    order = relationship("RuanwenjieOrder", back_populates="status_records")

    def __repr__(self):
        return f"<RuanwenjieOrderStatus(id={self.id}, order_id={self.order_id}, status={self.new_status})>"


class RuanwenjieConfig(Base):
    """软文街配置表"""
    __tablename__ = "ruanwenjie_config"
    __table_args__ = {'comment': '软文街配置表'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, comment="配置唯一标识")
    config_key = Column(String(100), unique=True, nullable=False, comment="配置键")
    config_value = Column(Text, comment="配置值")
    config_type = Column(String(50), default='string', comment="配置类型：string, json, number, boolean")
    description = Column(String(500), comment="配置描述")
    
    # 配置属性
    is_encrypted = Column(Boolean, default=False, comment="是否加密存储")
    is_system = Column(Boolean, default=False, comment="是否系统配置")
    is_active = Column(Boolean, default=True, comment="是否启用")
    
    # 元数据
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")

    def __repr__(self):
        return f"<RuanwenjieConfig(key={self.config_key}, value={self.config_value[:50]})>"
