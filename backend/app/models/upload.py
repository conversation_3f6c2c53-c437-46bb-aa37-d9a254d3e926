from sqlalchemy import Column, String, Boolean, DateTime, Text, Enum, Integer, JSON
from sqlalchemy.sql import func
from sqlalchemy.dialects.postgresql import UUID
from app.database import Base
import uuid
from datetime import datetime
import enum

class FileType(str, enum.Enum):
    IMAGE = "image"           # 图片文件
    VIDEO = "video"           # 视频文件
    DOCUMENT = "document"     # 文档文件
    AUDIO = "audio"           # 音频文件
    ARCHIVE = "archive"       # 压缩文件
    OTHER = "other"           # 其他文件

class FileStatus(str, enum.Enum):
    UPLOADING = "uploading"   # 上传中
    COMPLETED = "completed"   # 上传完成
    FAILED = "failed"         # 上传失败
    DELETED = "deleted"       # 已删除

class UploadedFile(Base):
    __tablename__ = "uploaded_files"
    __table_args__ = {
        'comment': '上传文件表'
    }
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    
    # 文件基本信息
    original_filename = Column(String(255), nullable=False)
    file_extension = Column(String(10))
    file_type = Column(Enum(FileType), nullable=False)
    file_size = Column(Integer, nullable=False)  # 文件大小（字节）
    mime_type = Column(String(100))
    
    # 存储信息
    storage_path = Column(String(500), nullable=False)  # TOS存储路径
    storage_bucket = Column(String(100), nullable=False)
    storage_key = Column(String(500), nullable=False)   # TOS对象键
    storage_url = Column(String(1000))                  # 访问URL
    
    # 文件元数据
    file_metadata = Column(JSON)  # 文件元数据（如图片尺寸、视频时长等）
    
    # 上传信息
    upload_session_id = Column(String(100))  # 上传会话ID
    upload_status = Column(Enum(FileStatus), default=FileStatus.UPLOADING)
    upload_progress = Column(Integer, default=0)  # 上传进度百分比
    
    # 业务关联
    business_type = Column(String(50))  # 业务类型（如content、profile、document等）
    business_id = Column(String(100))   # 业务ID
    
    # 安全信息
    file_hash = Column(String(64))      # 文件SHA256哈希
    is_public = Column(Boolean, default=False)  # 是否公开访问
    access_token = Column(String(100))  # 访问令牌
    
    # 时间信息
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    expires_at = Column(DateTime(timezone=True))       # 过期时间
    deleted_at = Column(DateTime(timezone=True))       # 删除时间

class UploadSession(Base):
    __tablename__ = "upload_sessions"
    __table_args__ = {
        'comment': '上传会话表'
    }
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    session_id = Column(String(100), unique=True, index=True, nullable=False)
    user_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    
    # 会话信息
    total_files = Column(Integer, nullable=False)
    completed_files = Column(Integer, default=0)
    failed_files = Column(Integer, default=0)
    total_size = Column(Integer, default=0)
    uploaded_size = Column(Integer, default=0)
    
    # 状态
    status = Column(String(20), default="active")  # active/completed/failed/cancelled
    
    # 配置
    business_type = Column(String(50))
    business_id = Column(String(100))
    upload_config = Column(JSON)  # 上传配置
    
    # 时间
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    completed_at = Column(DateTime(timezone=True))
    expires_at = Column(DateTime(timezone=True))

class FileAccessLog(Base):
    __tablename__ = "file_access_logs"
    __table_args__ = {
        'comment': '文件访问日志表'
    }
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    file_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    user_id = Column(UUID(as_uuid=True), index=True)
    
    # 访问信息
    access_type = Column(String(20), nullable=False)  # view/download/delete
    access_ip = Column(String(45))
    user_agent = Column(Text)
    referer = Column(String(500))
    
    # 时间
    accessed_at = Column(DateTime(timezone=True), server_default=func.now())
