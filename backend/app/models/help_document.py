"""
帮助文档系统数据模型
"""
from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, ForeignKey, func
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID
from app.database import Base
from datetime import datetime


class HelpCategory(Base):
    """帮助文档分类表"""
    __tablename__ = "help_categories"
    __table_args__ = {'comment': '帮助文档分类表'}
    
    # 主键
    id = Column(Integer, primary_key=True, index=True, comment='分类ID')
    
    # 基本信息
    name = Column(String(100), nullable=False, comment='分类名称')
    description = Column(Text, comment='分类描述')
    icon = Column(String(100), comment='分类图标')
    
    # 排序和状态
    sort_order = Column(Integer, default=0, comment='排序号')
    is_active = Column(Boolean, default=True, index=True, comment='是否启用')
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment='创建时间')
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment='更新时间')
    
    # 关系
    documents = relationship("HelpDocument", back_populates="category", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<HelpCategory(id={self.id}, name='{self.name}')>"
    
    def to_dict(self, include_document_count=False):
        """转换为字典"""
        result = {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'icon': self.icon,
            'sort_order': self.sort_order,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
        
        # 只有在明确请求时才包含文档计数（避免懒加载问题）
        if include_document_count:
            try:
                result['document_count'] = len(self.documents) if self.documents else 0
            except:
                result['document_count'] = 0
                
        return result


class HelpDocument(Base):
    """帮助文档内容表"""
    __tablename__ = "help_documents"
    __table_args__ = {'comment': '帮助文档内容表'}
    
    # 主键
    id = Column(Integer, primary_key=True, index=True, comment='文档ID')
    
    # 外键
    category_id = Column(Integer, ForeignKey("help_categories.id", ondelete="CASCADE"), 
                        index=True, comment='所属分类ID')
    created_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), comment='创建人ID')
    
    # 基本信息
    title = Column(String(200), nullable=False, comment='文档标题')
    content = Column(Text, comment='文档内容（富文本）')
    
    # 排序和状态
    sort_order = Column(Integer, default=0, index=True, comment='排序号')
    is_published = Column(Boolean, default=True, index=True, comment='是否发布')
    
    # 统计
    view_count = Column(Integer, default=0, comment='浏览次数')
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment='创建时间')
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment='更新时间')
    
    # 关系
    category = relationship("HelpCategory", back_populates="documents")
    creator = relationship("User", backref="help_documents")
    media_files = relationship("HelpDocumentMedia", back_populates="document", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<HelpDocument(id={self.id}, title='{self.title}')>"
    
    def to_dict(self, include_content=False, include_media=False):
        """转换为字典"""
        result = {
            'id': self.id,
            'category_id': self.category_id,
            'title': self.title,
            'sort_order': self.sort_order,
            'is_published': self.is_published,
            'view_count': self.view_count,
            'created_by': str(self.created_by) if self.created_by else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
        
        # 可选包含内容
        if include_content:
            result['content'] = self.content
        
        # 可选包含媒体文件
        if include_media and self.media_files:
            result['media_files'] = [media.to_dict() for media in self.media_files]
        
        # 包含分类信息
        if self.category:
            result['category_name'] = self.category.name
            result['category_icon'] = self.category.icon
        
        # 包含创建者信息
        if self.creator:
            result['creator_name'] = self.creator.full_name
        
        return result
    
    async def increment_view_count(self, db):
        """增加浏览次数"""
        self.view_count += 1
        await db.commit()


class HelpDocumentMedia(Base):
    """帮助文档媒体文件表"""
    __tablename__ = "help_document_media"
    __table_args__ = {'comment': '帮助文档媒体文件表'}
    
    # 主键
    id = Column(Integer, primary_key=True, index=True, comment='媒体文件ID')
    
    # 外键
    document_id = Column(Integer, ForeignKey("help_documents.id", ondelete="CASCADE"), 
                        index=True, comment='所属文档ID')
    
    # 文件信息
    file_type = Column(String(20), nullable=False, index=True, comment='文件类型: image/video')
    file_name = Column(String(255), nullable=False, comment='原始文件名')
    file_path = Column(String(500), nullable=False, comment='文件存储路径')
    file_size = Column(Integer, comment='文件大小（字节）')
    mime_type = Column(String(100), comment='MIME类型')
    
    # 视频专用字段
    thumbnail_path = Column(String(500), comment='缩略图路径（视频用）')
    duration = Column(Integer, comment='视频时长（秒）')
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment='上传时间')
    
    # 关系
    document = relationship("HelpDocument", back_populates="media_files")
    
    def __repr__(self):
        return f"<HelpDocumentMedia(id={self.id}, file_name='{self.file_name}', type='{self.file_type}')>"
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'document_id': self.document_id,
            'file_type': self.file_type,
            'file_name': self.file_name,
            'file_path': self.file_path,
            'file_size': self.file_size,
            'mime_type': self.mime_type,
            'thumbnail_path': self.thumbnail_path,
            'duration': self.duration,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'file_url': self.get_file_url()
        }
    
    def get_file_url(self):
        """获取文件访问URL"""
        if not self.file_path:
            return None

        try:
            # 使用TOS服务生成访问URL
            from app.services.tos_service import tos_service
            return tos_service.get_file_url(self.file_path, is_public=True)
        except Exception as e:
            # 如果TOS服务不可用，返回相对路径作为备用
            return f"/uploads/help/{self.file_path}"
    
    @property
    def is_image(self):
        """判断是否为图片"""
        return self.file_type == 'image'
    
    @property
    def is_video(self):
        """判断是否为视频"""
        return self.file_type == 'video'
    
    @property
    def file_size_mb(self):
        """获取文件大小（MB）"""
        if self.file_size:
            return round(self.file_size / (1024 * 1024), 2)
        return 0
    
    @property
    def duration_formatted(self):
        """获取格式化的视频时长"""
        if self.duration:
            minutes = self.duration // 60
            seconds = self.duration % 60
            return f"{minutes:02d}:{seconds:02d}"
        return None


# 导入时注册到models/__init__.py
__all__ = ['HelpCategory', 'HelpDocument', 'HelpDocumentMedia']