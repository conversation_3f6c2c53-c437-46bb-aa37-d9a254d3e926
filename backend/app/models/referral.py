from sqlalchemy import Column, String, DateTime, Numeric, Boolean, Integer, Text
from sqlalchemy.dialects.postgresql import UUID
from app.database import Base
import uuid
from datetime import datetime, timezone

class ReferralLink(Base):
    """推广链接表"""
    __tablename__ = "referral_links"
    __table_args__ = {
        'comment': '推广链接表'
    }

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), nullable=False, index=True)  # 创建者用户ID
    agent_id = Column(UUID(as_uuid=True), nullable=True, index=True)  # 代理商ID（可选）

    # 链接基本信息
    link_name = Column(String(100), nullable=False)  # 链接名称
    referral_code = Column(String(20), nullable=False, index=True)  # 推荐码
    target_page = Column(String(50), nullable=False)  # 目标页面
    campaign_name = Column(String(100), nullable=True)  # 活动名称
    description = Column(Text, nullable=True)  # 链接描述

    # 链接URL
    referral_url = Column(String(500), nullable=False)  # 完整的推广链接

    # 统计数据
    total_clicks = Column(Integer, default=0)  # 总点击数
    total_registrations = Column(Integer, default=0)  # 总注册数
    total_conversions = Column(Integer, default=0)  # 总转化数
    total_commission = Column(Numeric(10, 2), default=0.00)  # 总佣金

    # 状态信息
    is_active = Column(Boolean, default=True)  # 是否激活

    # 时间信息
    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc))  # 创建时间
    updated_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))  # 更新时间
    last_used_at = Column(DateTime(timezone=True), nullable=True)  # 最后使用时间

class UserReferral(Base):
    """用户推荐关系表"""
    __tablename__ = "user_referrals"
    __table_args__ = {
        'comment': '用户推荐关系表'
    }
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    referred_user_id = Column(UUID(as_uuid=True), nullable=False, index=True)  # 被推荐用户ID
    referrer_user_id = Column(UUID(as_uuid=True), nullable=False, index=True)  # 推荐人用户ID
    referrer_agent_id = Column(UUID(as_uuid=True), nullable=True, index=True)  # 推荐代理商ID（可选）
    referral_code = Column(String(20), nullable=False, index=True)  # 推荐码
    referral_source = Column(String(50), nullable=True)  # 推荐来源
    conversion_status = Column(String(20), default="registered")  # 转化状态
    first_order_id = Column(UUID(as_uuid=True), nullable=True)  # 首次订单ID
    first_order_amount = Column(Numeric(10, 2), nullable=True)  # 首次订单金额
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))  # 创建时间
    converted_at = Column(DateTime, nullable=True)  # 转化时间
