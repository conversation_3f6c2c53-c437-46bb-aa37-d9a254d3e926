from sqlalchemy import Column, String, Boolean, DateTime, Text, Enum, Numeric, Integer, JSON
from sqlalchemy.sql import func
from sqlalchemy.dialects.postgresql import UUID
from app.database import Base
import uuid
from datetime import datetime
import enum

class AgentLevel(str, enum.Enum):
    BRONZE = "bronze"       # 青铜代理商
    SILVER = "silver"       # 白银代理商
    GOLD = "gold"          # 黄金代理商
    PLATINUM = "platinum"   # 铂金代理商
    DIAMOND = "diamond"     # 钻石代理商

class VerificationStatus(str, enum.Enum):
    PENDING = "pending"     # 待审核
    VERIFIED = "verified"   # 已认证
    REJECTED = "rejected"   # 已拒绝

class Agent(Base):
    __tablename__ = "agents"
    __table_args__ = {
        'comment': '代理商表'
    }
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), nullable=False, index=True)  # 关联用户ID
    
    # 基本信息
    agent_name = Column(String(200), nullable=False, index=True)
    agent_code = Column(String(20), unique=True, index=True)  # 代理商代码
    agent_level = Column(String(50), default="bronze")
    
    # 联系信息
    contact_phone = Column(String(20))
    contact_email = Column(String(255), nullable=False)
    office_address = Column(String(500))
    
    # 业务信息
    agent_description = Column(Text)
    service_regions = Column(JSON)  # 服务区域
    specialties = Column(JSON)      # 专业领域
    
    # 佣金配置
    subscription_rate = Column(Numeric(5, 4), default=0.1000)      # 套餐订单佣金比例
    content_service_rate = Column(Numeric(5, 4), default=0.0800)   # 内容服务佣金比例
    settlement_cycle = Column(String(20), default="monthly")        # 结算周期
    
    # 审核状态
    verification_status = Column(String(50), default="pending")
    verification_time = Column(DateTime(timezone=True))
    verification_note = Column(Text)
    verifier_id = Column(UUID(as_uuid=True))
    
    # 业务状态
    is_active = Column(Boolean, default=True)
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

class Commission(Base):
    __tablename__ = "commissions"
    __table_args__ = {
        'comment': '佣金记录表'
    }
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    agent_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    order_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    customer_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    
    # 订单信息
    order_no = Column(String(50), nullable=False)
    order_type = Column(String(50), nullable=False)  # subscription/content_service
    product_name = Column(String(200))
    order_amount = Column(Numeric(12, 2), nullable=False)
    
    # 佣金信息
    commission_rate = Column(Numeric(5, 4), nullable=False)
    commission_amount = Column(Numeric(12, 2), nullable=False)
    status = Column(String(20), default="pending")  # pending/settled/cancelled
    
    # 结算信息
    settlement_id = Column(UUID(as_uuid=True))
    settled_at = Column(DateTime(timezone=True))
    
    # 时间戳
    commission_date = Column(DateTime(timezone=True), server_default=func.now())
    created_at = Column(DateTime(timezone=True), server_default=func.now())

class CommissionSettlement(Base):
    __tablename__ = "commission_settlements"
    __table_args__ = {
        'comment': '佣金结算表'
    }
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    agent_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    
    # 结算信息
    settlement_amount = Column(Numeric(12, 2), nullable=False)
    commission_records_count = Column(Integer, nullable=False)
    status = Column(String(20), default="pending")  # pending/approved/rejected/completed
    
    # 收款账户
    bank_account = Column(JSON, nullable=False)
    
    # 审核信息
    note = Column(Text)
    approver_id = Column(UUID(as_uuid=True))
    approved_at = Column(DateTime(timezone=True))
    
    # 时间戳
    applied_at = Column(DateTime(timezone=True), server_default=func.now())
    expected_settlement_date = Column(DateTime(timezone=True))
    completed_at = Column(DateTime(timezone=True))
