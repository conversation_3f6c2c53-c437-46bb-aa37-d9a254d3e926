from sqlalchemy import Column, String, Boolean, DateTime, Text, Enum, Numeric, Integer, JSON, TypeDecorator
from sqlalchemy.sql import func
from sqlalchemy.dialects.postgresql import UUID, TIMESTAMP
import json
from app.database import Base
import uuid
from datetime import datetime
import enum

class UTF8JSON(TypeDecorator):
    """自定义JSON类型，确保中文字符不被转义"""
    impl = JSON
    cache_ok = True

    def process_bind_param(self, value, dialect):
        if value is not None:
            # 使用ensure_ascii=False来避免Unicode转义
            return json.dumps(value, ensure_ascii=False, separators=(',', ':'))
        return value

    def process_result_value(self, value, dialect):
        if value is not None:
            # 如果是字符串，解析为Python对象
            if isinstance(value, str):
                try:
                    return json.loads(value)
                except:
                    return value
            return value
        return value

class RequestType(str, enum.Enum):
    PUBLISH_CONTENT = "PUBLISH_CONTENT"    # 企业提供稿件
    CREATE_CONTENT = "CREATE_CONTENT"      # 需求创作

class RequestStatus(str, enum.Enum):
    PENDING = "PENDING"        # 待处理
    ACCEPTED = "ACCEPTED"      # 已接受
    REJECTED = "REJECTED"      # 已拒绝
    IN_PROGRESS = "IN_PROGRESS" # 进行中
    DELIVERED = "DELIVERED"    # 已交付
    COMPLETED = "COMPLETED"    # 已完成
    CANCELLED = "CANCELLED"    # 已取消

class ReviewStatus(str, enum.Enum):
    PENDING = "PENDING"        # 待验收
    APPROVED = "APPROVED"      # 验收通过
    REJECTED = "REJECTED"      # 验收拒绝
    REVISION_REQUIRED = "REVISION_REQUIRED"  # 需要修改

class ContentRequest(Base):
    __tablename__ = "content_requests"
    __table_args__ = {
        'comment': '内容需求表'
    }
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    group_id = Column(UUID(as_uuid=True), nullable=False, default=uuid.uuid4, index=True)  # 分组标识，同一次发布的需求使用相同的group_id
    company_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    provider_id = Column(UUID(as_uuid=True), nullable=False, index=True)  # 渠道商ID
    service_id = Column(UUID(as_uuid=True), nullable=False, index=True)   # 服务ID
    
    # 基本信息
    request_type = Column(Enum(RequestType, name='requesttype'), nullable=False)
    request_title = Column(String(200), nullable=False)
    request_description = Column(Text, nullable=False)
    
    # 企业提供稿件模式字段
    provided_content_title = Column(String(200))
    provided_content_text = Column(Text)
    provided_content_files = Column(UTF8JSON)  # 附件列表，使用UTF8JSON避免中文转义
    
    # 需求创作模式字段
    creation_requirements = Column(Text)
    
    # 公共字段
    tags = Column(UTF8JSON, nullable=False)  # 标签数组，使用UTF8JSON避免中文转义
    deadline = Column(DateTime(timezone=True), nullable=False)  # 使用带时区的时间戳
    
    # 状态信息
    status = Column(Enum(RequestStatus, name='requeststatus'), default=RequestStatus.PENDING)
    # 预计送达天数
    estimated_delivery_days = Column(Integer)
    # 接受服务时发送的消息
    accept_message = Column(Text)
    
    # 价格信息
    fixed_price = Column(Numeric(10, 2))
    
    # 分配信息（增强字段）
    assigned_service_id = Column(UUID(as_uuid=True))  # 分配的服务ID
    assigned_at = Column(DateTime(timezone=True))  # 分配时间
    cancel_reason = Column(Text)  # 取消原因
    cancelled_at = Column(DateTime(timezone=True))  # 取消时间
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    accepted_at = Column(DateTime(timezone=True))

class ContentDelivery(Base):
    __tablename__ = "content_deliveries"
    __table_args__ = {
        'comment': '内容交付表'
    }
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    request_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    
    # 内容信息
    content_title = Column(String(200), nullable=False)
    content_text = Column(Text, nullable=False)
    content_summary = Column(Text)
    
    # 媒体资源
    content_images = Column(JSON)      # 图片列表
    content_videos = Column(JSON)      # 视频列表
    content_attachments = Column(JSON) # 附件列表
    
    # 内容元数据
    content_metadata = Column(JSON)    # 字数、阅读时间、关键词等
    
    # 交付信息
    delivery_note = Column(Text, nullable=False)
    
    # 验收状态
    review_status = Column(Enum(ReviewStatus, name='reviewstatus'), default=ReviewStatus.PENDING)
    review_note = Column(Text)
    review_score = Column(Integer)  # 1-5分评分
    reviewed_at = Column(DateTime(timezone=True))
    reviewer_id = Column(UUID(as_uuid=True))
    
    # 修改记录
    revision_count = Column(Integer, default=0)
    revision_history = Column(JSON)
    
    # 版本控制（增强字段）
    version = Column(Integer, default=1)  # 版本号
    parent_delivery_id = Column(UUID(as_uuid=True))  # 父交付ID（用于修改版本）
    acceptance_status = Column(String(20))  # accepted/rejected/pending
    acceptance_reason = Column(Text)  # 接受/拒绝原因
    accepted_at = Column(DateTime(timezone=True))  # 接受时间
    rejected_at = Column(DateTime(timezone=True))  # 拒绝时间
    
    # 时间戳
    delivered_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

class ContentRevision(Base):
    __tablename__ = "content_revisions"
    __table_args__ = {
        'comment': '内容修改记录表'
    }
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    delivery_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    
    # 修改要求
    revision_requirements = Column(Text, nullable=False)
    revision_deadline = Column(DateTime(timezone=True))
    
    # 修改后内容
    revised_content_title = Column(String(200))
    revised_content_text = Column(Text)
    revised_content_images = Column(JSON)
    revised_content_videos = Column(JSON)
    revised_delivery_note = Column(Text)
    
    # 状态
    status = Column(String(20), default="pending")  # pending/completed/expired
    
    # 时间戳
    requested_at = Column(DateTime(timezone=True), server_default=func.now())
    completed_at = Column(DateTime(timezone=True))

class ContentServiceOrder(Base):
    """内容服务订单模型"""
    __tablename__ = "content_service_orders"
    __table_args__ = {
        'comment': '内容服务订单表'
    }
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    order_id = Column(UUID(as_uuid=True), nullable=False, unique=True, index=True)
    request_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    
    # 路由信息
    route_type = Column(String(20), nullable=False)  # official/channel/platform
    provider_id = Column(UUID(as_uuid=True), index=True)
    provider_type = Column(String(50))
    
    # 工作状态
    work_status = Column(String(20), default='pending')  # pending/accepted/creating/delivered/reviewing/completed
    
    # 时间信息
    assigned_at = Column(DateTime(timezone=True))
    delivered_at = Column(DateTime(timezone=True))
    
    # 额外字段（可能需要但不在数据库中）
    estimated_delivery_days = Column(Integer)
    price = Column(Numeric(12, 2))
    notes = Column(Text)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
