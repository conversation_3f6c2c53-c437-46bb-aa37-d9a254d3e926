from pydantic import BaseModel
from typing import Optional, Any, Dict, List
from datetime import datetime
from decimal import Decimal

class ApiResponse(BaseModel):
    """通用API响应模型"""
    success: bool
    data: Optional[Any] = None
    message: str

class PaginationInfo(BaseModel):
    """分页信息模型"""
    page: int
    size: int
    total: int
    pages: int

class ListResponse(BaseModel):
    """通用列表响应模型"""
    items: List[Any]
    pagination: PaginationInfo

class ErrorResponse(BaseModel):
    """错误响应模型"""
    success: bool = False
    error_code: Optional[str] = None
    error_message: str
    details: Optional[Dict[str, Any]] = None

class SuccessResponse(BaseModel):
    """成功响应模型"""
    success: bool = True
    data: Optional[Any] = None
    message: str = "操作成功"

class ValidationErrorDetail(BaseModel):
    """验证错误详情"""
    field: str
    message: str
    value: Optional[Any] = None

class ValidationErrorResponse(BaseModel):
    """验证错误响应"""
    success: bool = False
    error_code: str = "VALIDATION_ERROR"
    error_message: str = "数据验证失败"
    validation_errors: List[ValidationErrorDetail]
