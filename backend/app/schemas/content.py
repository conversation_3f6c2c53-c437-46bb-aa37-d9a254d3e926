from pydantic import BaseModel, validator
from typing import Optional, List, Dict, Any, Union
from datetime import datetime, timezone
from decimal import Decimal
import pytz

class ContentRequestCreate(BaseModel):
    group_id: Optional[str] = None  # 分组标识，同一次发布的需求使用相同的group_id
    provider_id: str  # 渠道商ID
    service_id: str   # 服务ID
    request_type: str  # publish_content/create_content
    request_title: str
    request_description: str
    
    # 企业提供稿件模式字段
    provided_content_title: Optional[str] = None
    provided_content_text: Optional[str] = None
    provided_content_files: Optional[Union[List[str], List[Dict[str, Any]]]] = None

    @validator('provided_content_title', pre=True, always=True)
    def validate_provided_content_title(cls, v):
        # 处理前端发送的 "null" 字符串
        if v == "null" or v == "" or v is None:
            return None
        return v

    @validator('provided_content_files', pre=True, always=True)
    def validate_provided_content_files(cls, v):
        # 处理前端发送的 "null" 字符串
        if v == "null" or v == "" or v is None:
            return None
        return v
    
    # 需求创作模式字段
    creation_requirements: Optional[str] = None
    
    # 公共字段
    tags: List[str]
    deadline: datetime
    fixed_price: Optional[Decimal] = None  # 添加价格字段

    # 移除时区转换，直接使用带时区的 datetime
    
    @validator('request_type')
    def validate_request_type(cls, v):
        if v not in ['publish_content', 'create_content', 'PUBLISH_CONTENT', 'CREATE_CONTENT']:
            raise ValueError('请求类型必须是 publish_content 或 create_content')
        # 转换为大写格式以匹配数据库枚举
        if v == 'publish_content':
            return 'PUBLISH_CONTENT'
        elif v == 'create_content':
            return 'CREATE_CONTENT'
        return v
    
    @validator('request_title')
    def validate_request_title(cls, v):
        if not v or len(v.strip()) < 5:
            raise ValueError('需求标题至少需要5个字符')
        if len(v) > 200:
            raise ValueError('需求标题不能超过200个字符')
        return v.strip()
    
    @validator('request_description')
    def validate_request_description(cls, v):
        if not v or len(v.strip()) < 10:
            raise ValueError('需求描述至少需要10个字符')
        return v.strip()
    
    @validator('provided_content_text', pre=True, always=True)
    def validate_provided_content_text(cls, v, values):
        # 处理前端发送的 "null" 字符串
        if v == "null" or v == "" or v is None:
            v = None
        # 验证企业提供稿件模式
        if values.get('request_type') in ['publish_content', 'PUBLISH_CONTENT'] and not v:
            raise ValueError('企业提供稿件模式必须提供内容正文')
        return v
    
    @validator('creation_requirements', pre=True, always=True)
    def validate_creation_requirements(cls, v, values):
        # 处理前端发送的 "null" 字符串
        if v == "null" or v == "" or v is None:
            v = None
        # 验证需求创作模式
        if values.get('request_type') in ['create_content', 'CREATE_CONTENT'] and not v:
            raise ValueError('需求创作模式必须提供创作要求')
        return v
    
    @validator('tags')
    def validate_tags(cls, v):
        if not v or len(v) == 0:
            raise ValueError('至少需要提供一个标签')
        if len(v) > 10:
            raise ValueError('标签数量不能超过10个')
        return v
    
    @validator('deadline')
    def validate_deadline(cls, v):
        # 使用UTC时间进行比较，确保时区一致性
        now = datetime.now(timezone.utc)
        # 如果传入的时间没有时区信息，假设为UTC
        if v.tzinfo is None:
            v = v.replace(tzinfo=timezone.utc)
        if v <= now:
            raise ValueError('截止时间必须是未来时间')
        return v

class ContentRequestQuery(BaseModel):
    page: int = 1
    size: int = 20
    status: Optional[str] = None
    request_type: Optional[str] = None
    provider_id: Optional[str] = None
    service_id: Optional[str] = None
    keyword: Optional[str] = None
    tag: Optional[str] = None
    deadline_before: Optional[datetime] = None
    my_requests: Optional[bool] = None
    
    @validator('page')
    def validate_page(cls, v):
        if v < 1:
            raise ValueError('页码必须大于0')
        return v
    
    @validator('size')
    def validate_size(cls, v):
        if v < 1 or v > 100:
            raise ValueError('每页数量必须在1-100之间')
        return v
    
    @validator('status')
    def validate_status(cls, v):
        if not v:
            return v

        valid_statuses = ['pending', 'accepted', 'rejected', 'in_progress', 'delivered', 'completed', 'cancelled',
                         'PENDING', 'ACCEPTED', 'REJECTED', 'IN_PROGRESS', 'DELIVERED', 'COMPLETED', 'CANCELLED']

        # 支持逗号分隔的多个状态值
        if ',' in v:
            status_list = [status.strip() for status in v.split(',') if status.strip()]
            for status in status_list:
                if status not in valid_statuses:
                    raise ValueError(f'状态值不合法: {status}')
        else:
            if v not in valid_statuses:
                raise ValueError(f'状态值不合法: {v}')
        return v
    
    @validator('request_type')
    def validate_request_type(cls, v):
        if v and v not in ['publish_content', 'create_content', 'PUBLISH_CONTENT', 'CREATE_CONTENT']:
            raise ValueError('请求类型必须是 publish_content 或 create_content')
        return v

class ApplyRequestRequest(BaseModel):
    estimated_delivery_days: int
    accept_message: Optional[str] = None
    
    @validator('estimated_delivery_days')
    def validate_delivery_days(cls, v):
        if v < 1 or v > 30:
            raise ValueError('预计交付天数必须在1-30天之间')
        return v
    
    @validator('accept_message')
    def validate_accept_message(cls, v):
        if v and len(v) > 500:
            raise ValueError('接单留言不能超过500个字符')
        return v

class ContentDeliveryRequest(BaseModel):
    content_title: str
    content_text: str
    content_summary: Optional[str] = None
    content_images: Optional[List[Dict[str, Any]]] = None
    content_videos: Optional[List[Dict[str, Any]]] = None
    content_attachments: Optional[List[Dict[str, Any]]] = None
    content_metadata: Optional[Dict[str, Any]] = None
    delivery_note: str
    
    @validator('content_title')
    def validate_content_title(cls, v):
        if not v or len(v.strip()) < 5:
            raise ValueError('内容标题至少需要5个字符')
        if len(v) > 200:
            raise ValueError('内容标题不能超过200个字符')
        return v.strip()
    
    @validator('content_text')
    def validate_content_text(cls, v):
        if not v or len(v.strip()) < 50:
            raise ValueError('内容正文至少需要50个字符')
        return v.strip()
    
    @validator('delivery_note')
    def validate_delivery_note(cls, v):
        if not v or len(v.strip()) < 10:
            raise ValueError('交付说明至少需要10个字符')
        return v.strip()

class ContentReviewRequest(BaseModel):
    review_status: str  # approved/rejected/revision_required
    review_note: str
    review_score: Optional[int] = None
    revision_requirements: Optional[str] = None
    revision_deadline: Optional[datetime] = None
    
    @validator('review_status')
    def validate_review_status(cls, v):
        valid_statuses = ['approved', 'rejected', 'revision_required']
        if v not in valid_statuses:
            raise ValueError(f'验收状态必须是以下之一: {", ".join(valid_statuses)}')
        return v
    
    @validator('review_note')
    def validate_review_note(cls, v):
        if not v or len(v.strip()) < 5:
            raise ValueError('验收备注至少需要5个字符')
        if len(v) > 1000:
            raise ValueError('验收备注不能超过1000个字符')
        return v.strip()
    
    @validator('review_score')
    def validate_review_score(cls, v):
        if v is not None and (v < 1 or v > 5):
            raise ValueError('评分必须在1-5分之间')
        return v
    
    @validator('revision_requirements')
    def validate_revision_fields(cls, v, values):
        if values.get('review_status') == 'revision_required' and not v:
            raise ValueError('要求修改时必须提供修改要求')
        if v and len(v.strip()) < 10:
            raise ValueError('修改要求至少需要10个字符')
        return v
    
    @validator('revision_deadline')
    def validate_revision_deadline(cls, v, values):
        if values.get('review_status') == 'revision_required' and not v:
            raise ValueError('要求修改时必须提供修改截止时间')
        if v:
            # 使用UTC时间进行比较，确保时区一致性
            now = datetime.now(timezone.utc)
            # 如果传入的时间没有时区信息，假设为UTC
            if v.tzinfo is None:
                v = v.replace(tzinfo=timezone.utc)
            if v <= now:
                raise ValueError('修改截止时间必须是未来时间')
        return v

class ContentRequestResponse(BaseModel):
    id: str
    group_id: str  # 分组标识，同一次发布的需求使用相同的group_id
    company_id: str
    provider_id: str
    service_id: str
    request_type: str
    request_title: str
    request_description: str
    provided_content_title: Optional[str]
    provided_content_text: Optional[str]
    provided_content_files: Optional[Union[List[str], List[Dict[str, Any]]]]
    creation_requirements: Optional[str]
    tags: List[str]
    deadline: datetime
    status: str
    estimated_delivery_days: Optional[int]
    accept_message: Optional[str]
    fixed_price: Optional[Decimal]
    created_at: datetime
    updated_at: datetime
    accepted_at: Optional[datetime]
    
    # 关联信息
    company_info: Optional[Dict[str, Any]] = None
    provider_info: Optional[Dict[str, Any]] = None

class ContentDeliveryResponse(BaseModel):
    id: str
    request_id: str
    content_title: str
    content_text: str
    content_summary: Optional[str]
    content_images: Optional[List[Dict[str, Any]]]
    content_videos: Optional[List[Dict[str, Any]]]
    content_attachments: Optional[List[Dict[str, Any]]]
    content_metadata: Optional[Dict[str, Any]]
    delivery_note: str
    review_status: str
    review_note: Optional[str]
    review_score: Optional[int]
    reviewed_at: Optional[datetime]
    revision_count: int
    delivered_at: datetime
    updated_at: datetime

class ContentListResponse(BaseModel):
    items: List[ContentRequestResponse]
    pagination: Dict[str, int]
    statistics: Dict[str, Any]

class ApplyRequestResponse(BaseModel):
    id: str
    company_id: str
    provider_id: str
    service_id: str
    request_title: str
    status: str
    estimated_delivery_days: int
    fixed_price: Optional[Decimal]
    accepted_at: datetime
    updated_at: datetime

class ContentDeliveryCreateResponse(BaseModel):
    request_id: str
    delivery_id: str
    content_title: str
    delivery_note: str
    delivered_at: datetime
    review_status: str

class ContentReviewResponse(BaseModel):
    request_id: str
    delivery_id: str
    review_status: str
    review_note: Optional[str] = None
    review_score: Optional[int] = None
    reviewed_at: Optional[datetime] = None
    request_status: str

# 通用响应模式
class ContentApiResponse(BaseModel):
    success: bool
    data: Optional[Any] = None
    message: str

# ============= 增强的内容请求管理 Schemas =============

class ContentRequestUpdate(BaseModel):
    """更新内容请求"""
    request_title: Optional[str] = None
    request_description: Optional[str] = None
    tags: Optional[List[str]] = None
    deadline: Optional[datetime] = None
    creation_requirements: Optional[str] = None
    fixed_price: Optional[Decimal] = None
    
    @validator('request_title')
    def validate_title(cls, v):
        if v is not None and (len(v) < 5 or len(v) > 200):
            raise ValueError('标题长度必须在5-200个字符之间')
        return v

class CancelRequest(BaseModel):
    """取消内容请求"""
    reason: str
    cancel_related_orders: bool = False
    
    @validator('reason')
    def validate_reason(cls, v):
        if not v or len(v.strip()) < 10:
            raise ValueError('取消原因至少需要10个字符')
        return v.strip()

class ServiceAssignment(BaseModel):
    """分配渠道服务"""
    service_id: str
    provider_id: Optional[str] = None
    assignment_note: Optional[str] = None

class ContentRequestDetailResponse(BaseModel):
    """内容请求详情响应"""
    id: str
    group_id: str
    company_id: str
    provider_id: str
    service_id: str
    request_type: str
    request_title: str
    request_description: str
    tags: List[str]
    deadline: datetime
    status: str
    fixed_price: Optional[Decimal]
    created_at: datetime
    updated_at: datetime
    # 增强字段
    assigned_service_id: Optional[str]
    assigned_at: Optional[datetime]
    cancel_reason: Optional[str]
    cancelled_at: Optional[datetime]
    # 关联信息
    delivery_info: Optional[Dict[str, Any]] = None
    order_info: Optional[Dict[str, Any]] = None
    company_info: Optional[Dict[str, Any]] = None
    provider_info: Optional[Dict[str, Any]] = None

class ContentRequestListResponse(BaseModel):
    """内容请求列表响应"""
    items: List[ContentRequestResponse]
    total: int
    page: int
    size: int
    pages: int

# ============= 内容服务订单 Schemas =============

class ServiceOrderCreate(BaseModel):
    """创建服务订单"""
    request_id: str
    route_type: str  # official/channel/platform
    provider_id: Optional[str] = None
    provider_type: Optional[str] = None
    estimated_delivery_days: int
    price: Decimal
    notes: Optional[str] = None
    
    @validator('route_type')
    def validate_route_type(cls, v):
        if v not in ['official', 'channel', 'platform']:
            raise ValueError('路由类型必须是 official/channel/platform 之一')
        return v
    
    @validator('estimated_delivery_days')
    def validate_days(cls, v):
        if v < 1 or v > 90:
            raise ValueError('预计交付天数必须在1-90天之间')
        return v

class WorkStatusUpdate(BaseModel):
    """更新工作状态"""
    work_status: str
    status_note: Optional[str] = None
    estimated_completion: Optional[datetime] = None
    
    @validator('work_status')
    def validate_status(cls, v):
        valid_statuses = ['pending', 'accepted', 'creating', 'delivered', 'reviewing', 'completed']
        if v not in valid_statuses:
            raise ValueError(f'工作状态必须是以下之一: {", ".join(valid_statuses)}')
        return v

class ContentDeliverySubmit(BaseModel):
    """提交交付内容"""
    delivered_title: str
    delivered_content: str
    delivered_files: Optional[List[Dict[str, Any]]] = None
    delivery_note: Optional[str] = None
    is_final: bool = False

class DeliveryReview(BaseModel):
    """审核交付内容"""
    review_status: str  # approved/rejected/revision_required
    review_comment: str
    quality_score: Optional[int] = None
    revision_requirements: Optional[str] = None
    
    @validator('review_status')
    def validate_status(cls, v):
        if v not in ['approved', 'rejected', 'revision_required']:
            raise ValueError('审核状态无效')
        return v
    
    @validator('quality_score')
    def validate_score(cls, v):
        if v is not None and (v < 1 or v > 5):
            raise ValueError('评分必须在1-5之间')
        return v

class ServiceOrderResponse(BaseModel):
    """服务订单响应"""
    id: str
    order_id: str
    request_id: str
    route_type: str
    provider_id: Optional[str]
    provider_type: Optional[str]
    work_status: str
    assigned_at: Optional[datetime]
    delivered_at: Optional[datetime]
    estimated_delivery_days: Optional[int]
    price: Optional[Decimal]
    notes: Optional[str]
    created_at: datetime
    updated_at: datetime

class ServiceOrderDetailResponse(ServiceOrderResponse):
    """服务订单详情响应"""
    timeline: Optional[List[Dict[str, Any]]] = None
    deliveries: Optional[List[Dict[str, Any]]] = None
    request_info: Optional[Dict[str, Any]] = None
    order_info: Optional[Dict[str, Any]] = None

class ServiceOrderListResponse(BaseModel):
    """服务订单列表响应"""
    items: List[ServiceOrderResponse]
    total: int
    page: int
    size: int
    pages: int

# ============= 内容交付管理 Schemas =============

class DeliveryCreate(BaseModel):
    """创建交付记录"""
    order_id: str
    request_id: str
    delivered_title: str
    delivered_content: str
    delivered_files: Optional[List[Dict[str, Any]]] = None
    delivery_type: str = 'initial'  # initial/revision
    parent_delivery_id: Optional[str] = None
    version: int = 1

class DeliveryAcceptance(BaseModel):
    """接受交付"""
    acceptance_note: Optional[str] = None
    quality_score: Optional[int] = None
    publish_immediately: bool = False
    
    @validator('quality_score')
    def validate_score(cls, v):
        if v is not None and (v < 1 or v > 5):
            raise ValueError('评分必须在1-5之间')
        return v

class DeliveryRejection(BaseModel):
    """拒绝交付"""
    rejection_reason: str
    revision_requirements: str
    max_revision_days: int = 3
    allow_resubmission: bool = True
    
    @validator('rejection_reason')
    def validate_reason(cls, v):
        if not v or len(v.strip()) < 10:
            raise ValueError('拒绝原因至少需要10个字符')
        return v.strip()

class DeliveryResponse(BaseModel):
    """交付响应"""
    id: str
    request_id: str
    content_title: str
    content_text: str
    content_summary: Optional[str]
    delivery_note: str
    review_status: str
    version: int
    parent_delivery_id: Optional[str]
    acceptance_status: Optional[str]
    acceptance_reason: Optional[str]
    accepted_at: Optional[datetime]
    rejected_at: Optional[datetime]
    delivered_at: datetime
    updated_at: datetime

class DeliveryDetailResponse(DeliveryResponse):
    """交付详情响应"""
    content_images: Optional[List[Dict[str, Any]]]
    content_videos: Optional[List[Dict[str, Any]]]
    content_attachments: Optional[List[Dict[str, Any]]]
    content_metadata: Optional[Dict[str, Any]]
    history: Optional[List[Dict[str, Any]]] = None

class DeliveryListResponse(BaseModel):
    """交付列表响应"""
    items: List[DeliveryResponse]
    total: int
    page: int
    size: int
    pages: int

class OperationResponse(BaseModel):
    """操作响应"""
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None

class ReviewResponse(BaseModel):
    """审核响应"""
    success: bool
    message: str
    order_id: str
    delivery_id: Optional[str]
    review_status: str
    next_action: Optional[str]
