from pydantic import BaseModel, validator
from typing import Optional, List, Dict, Any
from datetime import datetime
from decimal import Decimal

class ReferralLinkCreateRequest(BaseModel):
    """创建推广链接请求"""
    link_name: str
    target_page: str = "auth/register"  # auth/register 或空字符串(首页)
    campaign_name: Optional[str] = None
    description: Optional[str] = None

    @validator('link_name')
    def validate_link_name(cls, v):
        if not v or len(v.strip()) < 2:
            raise ValueError('链接名称至少需要2个字符')
        if len(v) > 100:
            raise ValueError('链接名称不能超过100个字符')
        return v.strip()

    @validator('target_page')
    def validate_target_page(cls, v):
        allowed_pages = ['auth/register', '']  # 空字符串代表首页
        if v not in allowed_pages:
            raise ValueError(f'目标页面必须是以下之一: 注册页面, 首页')
        return v

class ReferralLinkUpdateRequest(BaseModel):
    """修改推广链接请求"""
    link_name: Optional[str] = None
    target_page: Optional[str] = None
    campaign_name: Optional[str] = None
    description: Optional[str] = None
    is_active: Optional[bool] = None

    @validator('link_name')
    def validate_link_name(cls, v):
        if v is not None:
            if not v or len(v.strip()) < 2:
                raise ValueError('链接名称至少需要2个字符')
            if len(v) > 100:
                raise ValueError('链接名称不能超过100个字符')
            return v.strip()
        return v

    @validator('target_page')
    def validate_target_page(cls, v):
        if v is not None:
            allowed_pages = ['auth/register', '']  # 空字符串代表首页
            if v not in allowed_pages:
                raise ValueError(f'目标页面必须是以下之一: 注册页面, 首页')
        return v

class TrackReferralClickRequest(BaseModel):
    """跟踪推广链接点击请求"""
    referral_code: str
    target_page: str
    campaign: Optional[str] = None
    user_agent: Optional[str] = None
    ip_address: Optional[str] = None

    @validator('referral_code')
    def validate_referral_code(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('推荐码不能为空')
        return v.strip()

    @validator('target_page')
    def validate_target_page(cls, v):
        if v is None:
            return ''
        return v

class ReferralLinkResponse(BaseModel):
    """推广链接响应"""
    id: str
    link_name: str
    referral_code: str
    referral_url: str
    target_page: str
    campaign_name: Optional[str]
    description: Optional[str]
    
    # 统计数据
    total_clicks: int
    total_registrations: int
    total_conversions: int
    total_commission: Decimal
    
    # 状态信息
    is_active: bool
    created_at: datetime
    last_used_at: Optional[datetime]

class ReferralLinkStatsResponse(BaseModel):
    """推广链接统计响应"""
    link_id: str
    link_name: str
    referral_code: str
    
    # 基础统计
    total_clicks: int
    total_registrations: int
    total_conversions: int
    conversion_rate: float
    
    # 收益统计
    total_commission: Decimal
    pending_commission: Decimal
    settled_commission: Decimal
    
    # 时间统计
    created_at: datetime
    last_used_at: Optional[datetime]
    
    # 最近推荐用户
    recent_referrals: List[Dict[str, Any]]

class ReferralLinkListResponse(BaseModel):
    """推广链接列表响应"""
    items: List[ReferralLinkResponse]
    total: int
    page: int
    size: int
    pages: int
    
    # 汇总统计
    summary: Dict[str, Any]

class ReferralLinkApiResponse(BaseModel):
    """推广链接API通用响应"""
    success: bool
    data: Optional[Any] = None
    message: str
