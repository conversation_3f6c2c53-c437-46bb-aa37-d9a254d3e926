"""
帮助文档系统的Pydantic模式定义
"""
from pydantic import BaseModel, Field, validator
from typing import Optional, List
from datetime import datetime
from uuid import UUID


# ============== 分类相关 Schema ==============

class HelpCategoryBase(BaseModel):
    """分类基础模型"""
    name: str = Field(..., min_length=1, max_length=100, description="分类名称")
    description: Optional[str] = Field(None, description="分类描述")
    icon: Optional[str] = Field(None, max_length=100, description="分类图标")
    sort_order: Optional[int] = Field(0, ge=0, description="排序号")
    is_active: Optional[bool] = Field(True, description="是否启用")


class HelpCategoryCreate(HelpCategoryBase):
    """创建分类请求"""
    pass


class HelpCategoryUpdate(BaseModel):
    """更新分类请求"""
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = None
    icon: Optional[str] = Field(None, max_length=100)
    sort_order: Optional[int] = Field(None, ge=0)
    is_active: Optional[bool] = None


class HelpCategoryResponse(HelpCategoryBase):
    """分类响应"""
    id: int
    created_at: datetime
    updated_at: datetime
    document_count: Optional[int] = 0
    
    class Config:
        from_attributes = True


class HelpCategoryListResponse(BaseModel):
    """分类列表响应"""
    items: List[HelpCategoryResponse]
    total: int


# ============== 文档相关 Schema ==============

class HelpDocumentBase(BaseModel):
    """文档基础模型"""
    category_id: int = Field(..., gt=0, description="所属分类ID")
    title: str = Field(..., min_length=1, max_length=200, description="文档标题")
    content: str = Field(..., description="文档内容（富文本）")
    sort_order: Optional[int] = Field(0, ge=0, description="排序号")
    is_published: Optional[bool] = Field(True, description="是否发布")


class HelpDocumentCreate(HelpDocumentBase):
    """创建文档请求"""
    pass


class HelpDocumentUpdate(BaseModel):
    """更新文档请求"""
    category_id: Optional[int] = Field(None, gt=0)
    title: Optional[str] = Field(None, min_length=1, max_length=200)
    content: Optional[str] = None
    sort_order: Optional[int] = Field(None, ge=0)
    is_published: Optional[bool] = None


class HelpDocumentResponse(BaseModel):
    """文档响应（不含内容）"""
    id: int
    category_id: int
    title: str
    sort_order: int
    is_published: bool
    view_count: int
    created_by: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    category_name: Optional[str] = None
    category_icon: Optional[str] = None
    creator_name: Optional[str] = None
    
    class Config:
        from_attributes = True


class HelpDocumentDetailResponse(HelpDocumentResponse):
    """文档详情响应（含内容）"""
    content: str
    media_files: Optional[List["HelpDocumentMediaResponse"]] = []


class HelpDocumentListResponse(BaseModel):
    """文档列表响应"""
    items: List[HelpDocumentResponse]
    total: int
    page: int = 1
    page_size: int = 20


# ============== 媒体文件相关 Schema ==============

class HelpDocumentMediaBase(BaseModel):
    """媒体文件基础模型"""
    file_type: str = Field(..., pattern="^(image|video)$", description="文件类型: image或video")
    file_name: str = Field(..., min_length=1, max_length=255, description="文件名")


class HelpDocumentMediaCreate(HelpDocumentMediaBase):
    """创建媒体文件请求"""
    document_id: int = Field(..., gt=0, description="所属文档ID")
    file_path: str = Field(..., description="文件路径")
    file_size: Optional[int] = Field(None, gt=0, description="文件大小（字节）")
    mime_type: Optional[str] = Field(None, description="MIME类型")
    thumbnail_path: Optional[str] = Field(None, description="缩略图路径")
    duration: Optional[int] = Field(None, gt=0, description="视频时长（秒）")


class HelpDocumentMediaResponse(BaseModel):
    """媒体文件响应"""
    id: int
    document_id: int
    file_type: str
    file_name: str
    file_path: str
    file_size: Optional[int] = None
    mime_type: Optional[str] = None
    thumbnail_path: Optional[str] = None
    duration: Optional[int] = None
    created_at: datetime
    file_url: Optional[str] = None
    
    class Config:
        from_attributes = True


class HelpDocumentMediaUploadResponse(BaseModel):
    """媒体文件上传响应"""
    id: int
    file_url: str
    file_type: str
    file_name: str
    file_size: int
    message: str = "上传成功"


# ============== 批量操作 Schema ==============

class BatchSortUpdate(BaseModel):
    """批量更新排序请求"""
    items: List[dict] = Field(..., description="排序项列表 [{id: 1, sort_order: 0}, ...]")
    
    @validator('items')
    def validate_items(cls, v):
        for item in v:
            if 'id' not in item or 'sort_order' not in item:
                raise ValueError("每个项必须包含id和sort_order字段")
            if not isinstance(item['id'], int) or item['id'] <= 0:
                raise ValueError("id必须是正整数")
            if not isinstance(item['sort_order'], int) or item['sort_order'] < 0:
                raise ValueError("sort_order必须是非负整数")
        return v


class BatchDeleteRequest(BaseModel):
    """批量删除请求"""
    ids: List[int] = Field(..., min_items=1, description="要删除的ID列表")
    
    @validator('ids')
    def validate_ids(cls, v):
        if not all(id > 0 for id in v):
            raise ValueError("所有ID必须是正整数")
        return v


class BatchOperationResponse(BaseModel):
    """批量操作响应"""
    success: bool
    affected_count: int
    message: str


# ============== 搜索相关 Schema ==============

class HelpDocumentSearchRequest(BaseModel):
    """文档搜索请求"""
    keyword: Optional[str] = Field(None, description="搜索关键词")
    category_id: Optional[int] = Field(None, gt=0, description="分类ID")
    is_published: Optional[bool] = Field(None, description="发布状态")
    page: int = Field(1, ge=1, description="页码")
    page_size: int = Field(20, ge=1, le=100, description="每页数量")
    sort_by: str = Field("sort_order", pattern="^(sort_order|created_at|updated_at|view_count)$")
    sort_order: str = Field("asc", pattern="^(asc|desc)$")


# ============== 统计相关 Schema ==============

class HelpDocumentStatistics(BaseModel):
    """文档统计信息"""
    total_categories: int
    active_categories: int
    total_documents: int
    published_documents: int
    total_views: int
    total_media_files: int
    total_images: int
    total_videos: int


# 更新前向引用
HelpDocumentDetailResponse.model_rebuild()