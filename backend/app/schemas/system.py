from pydantic import BaseModel, validator
from typing import Optional, List, Dict, Any
from datetime import datetime
from decimal import Decimal

class SystemStatisticsQuery(BaseModel):
    period: str = "daily"  # daily/weekly/monthly
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    metrics: Optional[List[str]] = None  # 指定统计指标
    
    @validator('period')
    def validate_period(cls, v):
        if v not in ['daily', 'weekly', 'monthly']:
            raise ValueError('统计周期必须是 daily、weekly 或 monthly')
        return v
    
    @validator('end_date')
    def validate_date_range(cls, v, values):
        if v and values.get('start_date') and v < values['start_date']:
            raise ValueError('结束日期不能早于开始日期')
        return v

class SystemLogQuery(BaseModel):
    page: int = 1
    size: int = 50
    level: Optional[str] = None
    module: Optional[str] = None
    user_id: Optional[str] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    keyword: Optional[str] = None
    sort_by: str = "created_at"
    sort_order: str = "desc"
    
    @validator('page')
    def validate_page(cls, v):
        if v < 1:
            raise ValueError('页码必须大于0')
        return v
    
    @validator('size')
    def validate_size(cls, v):
        if v < 1 or v > 100:
            raise ValueError('每页数量必须在1-100之间')
        return v
    
    @validator('level')
    def validate_level(cls, v):
        if v and v not in ['debug', 'info', 'warning', 'error', 'critical']:
            raise ValueError('日志级别必须是 debug、info、warning、error 或 critical')
        return v
    
    @validator('sort_by')
    def validate_sort_by(cls, v):
        allowed_fields = ['created_at', 'level', 'module', 'user_id', 'response_time']
        if v not in allowed_fields:
            raise ValueError(f'排序字段必须是以下之一: {", ".join(allowed_fields)}')
        return v
    
    @validator('sort_order')
    def validate_sort_order(cls, v):
        if v not in ['asc', 'desc']:
            raise ValueError('排序方式必须是 asc 或 desc')
        return v
    
    @validator('end_date')
    def validate_date_range(cls, v, values):
        if v and values.get('start_date') and v < values['start_date']:
            raise ValueError('结束日期不能早于开始日期')
        return v

class SystemConfigUpdate(BaseModel):
    configs: Dict[str, Any]  # 配置键值对
    
    @validator('configs')
    def validate_configs(cls, v):
        if not v:
            raise ValueError('配置不能为空')
        if len(v) > 50:
            raise ValueError('单次更新配置项不能超过50个')
        return v

class SystemStatisticsResponse(BaseModel):
    # 用户统计
    user_stats: Dict[str, int]
    # 业务统计
    business_stats: Dict[str, Any]
    # 系统统计
    system_stats: Dict[str, Any]
    # 趋势数据
    trends: List[Dict[str, Any]]
    # 统计时间范围
    period_start: datetime
    period_end: datetime

class SystemConfigResponse(BaseModel):
    config_key: str
    config_value: Any
    config_type: str
    category: str
    display_name: str
    description: Optional[str]
    default_value: Optional[str]
    is_sensitive: bool
    is_readonly: bool
    updated_at: datetime

class SystemLogResponse(BaseModel):
    id: str
    level: str
    message: str
    module: str
    function: Optional[str]
    user_id: Optional[str]
    user_email: Optional[str]
    ip_address: Optional[str]
    request_method: Optional[str]
    request_path: Optional[str]
    response_status: Optional[int]
    response_time: Optional[int]
    error_type: Optional[str]
    error_message: Optional[str]
    created_at: datetime

class SystemHealthResponse(BaseModel):
    overall_status: str  # healthy/warning/critical
    components: List[Dict[str, Any]]
    last_check_time: datetime
    uptime: int  # 系统运行时间（秒）
    version: str
    environment: str

class SystemConfigListResponse(BaseModel):
    items: List[SystemConfigResponse]
    categories: List[str]

class SystemLogListResponse(BaseModel):
    items: List[SystemLogResponse]
    pagination: Dict[str, int]
    statistics: Dict[str, Any]

class SystemConfigUpdateResponse(BaseModel):
    updated_configs: List[str]
    failed_configs: List[Dict[str, str]]
    total_updated: int
    total_failed: int

# 通用响应模式
class SystemApiResponse(BaseModel):
    success: bool
    data: Optional[Any] = None
    message: str
