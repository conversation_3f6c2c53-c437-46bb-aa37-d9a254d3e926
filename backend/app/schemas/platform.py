"""
Platform Integration Schemas
平台集成相关的Pydantic模型
"""

from pydantic import BaseModel, Field, validator
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum


class PlatformType(str, Enum):
    """平台类型枚举"""
    CONTENT = "content"  # 内容平台
    AI = "ai"  # AI平台
    SEARCH = "search"  # 搜索平台
    SOCIAL = "social"  # 社交平台


class AuthType(str, Enum):
    """认证类型枚举"""
    API_KEY = "api_key"
    OAUTH2 = "oauth2"
    CUSTOM = "custom"
    PASSWORD = "password"


class RouteType(str, Enum):
    """路由类型枚举"""
    ORDER = "order"
    CONTENT = "content"
    QUERY = "query"


class RouteStatus(str, Enum):
    """路由状态枚举"""
    PENDING = "pending"
    SENT = "sent"
    SUCCESS = "success"
    FAILED = "failed"


# ============= Platform Config Schemas =============

class PlatformConfigBase(BaseModel):
    """平台配置基础模型"""
    platform_code: str = Field(..., min_length=1, max_length=50)
    platform_name: str = Field(..., min_length=1, max_length=100)
    platform_type: PlatformType
    api_base_url: Optional[str] = Field(None, max_length=500)
    auth_type: Optional[AuthType] = None
    config_schema: Optional[Dict[str, Any]] = None
    features: Optional[List[str]] = None
    rate_limits: Optional[Dict[str, Any]] = None
    is_active: bool = True
    display_order: int = 0


class PlatformConfigCreate(PlatformConfigBase):
    """创建平台配置"""
    pass


class PlatformConfigUpdate(BaseModel):
    """更新平台配置"""
    platform_name: Optional[str] = None
    platform_type: Optional[PlatformType] = None
    api_base_url: Optional[str] = None
    auth_type: Optional[AuthType] = None
    config_schema: Optional[Dict[str, Any]] = None
    features: Optional[List[str]] = None
    rate_limits: Optional[Dict[str, Any]] = None
    is_active: Optional[bool] = None
    display_order: Optional[int] = None


class PlatformConfigResponse(PlatformConfigBase):
    """平台配置响应"""
    id: str
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class PlatformConfigListResponse(BaseModel):
    """平台配置列表响应"""
    items: List[PlatformConfigResponse]
    total: int
    page: int
    pages: int


# ============= Platform Credential Schemas =============

class CredentialType(str, Enum):
    """凭证类型枚举"""
    API_KEY = "api_key"
    CLIENT_CREDENTIALS = "client_credentials"
    PASSWORD = "password"


class PlatformCredentialBase(BaseModel):
    """平台凭证基础模型"""
    platform_code: str
    credential_type: CredentialType
    expires_at: Optional[datetime] = None


class PlatformCredentialCreate(PlatformCredentialBase):
    """创建平台凭证"""
    credentials: Dict[str, Any]  # 原始凭证数据
    
    @validator('credentials')
    def validate_credentials(cls, v):
        if not v:
            raise ValueError("凭证不能为空")
        return v


class PlatformCredentialUpdate(BaseModel):
    """更新平台凭证"""
    credentials: Optional[Dict[str, Any]] = None
    expires_at: Optional[datetime] = None
    is_valid: Optional[bool] = None


class PlatformCredentialResponse(PlatformCredentialBase):
    """平台凭证响应（不包含敏感信息）"""
    id: str
    user_id: Optional[str] = None
    is_valid: bool
    last_verified_at: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class PlatformCredentialDetailResponse(PlatformCredentialResponse):
    """平台凭证详情响应（包含部分脱敏的凭证信息）"""
    credential_hint: Optional[Dict[str, str]] = None  # 脱敏后的凭证提示


class TestConnectionRequest(BaseModel):
    """测试连接请求"""
    use_cached_credentials: bool = True  # 是否使用缓存的凭证


class TestConnectionResponse(BaseModel):
    """测试连接响应"""
    success: bool
    platform_code: str
    connection_status: str
    response_time_ms: Optional[int] = None
    details: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    tested_at: datetime


# ============= Platform Token Schemas =============

class PlatformTokenBase(BaseModel):
    """平台令牌基础模型"""
    platform_code: str
    token_type: Optional[str] = "Bearer"
    expires_at: datetime
    scope: Optional[str] = None


class PlatformTokenCreate(PlatformTokenBase):
    """创建平台令牌"""
    access_token: str
    refresh_token: Optional[str] = None
    credential_id: Optional[str] = None


class PlatformTokenResponse(PlatformTokenBase):
    """平台令牌响应（不包含实际令牌）"""
    id: str
    credential_id: Optional[str] = None
    is_active: bool
    created_at: datetime
    refreshed_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class RefreshTokenRequest(BaseModel):
    """刷新令牌请求"""
    force_refresh: bool = False  # 是否强制刷新


class RefreshTokenResponse(BaseModel):
    """刷新令牌响应"""
    success: bool
    platform_code: str
    expires_at: datetime
    refreshed_at: datetime


class RevokeTokenRequest(BaseModel):
    """撤销令牌请求"""
    reason: Optional[str] = None


class RevokeTokenResponse(BaseModel):
    """撤销令牌响应"""
    success: bool
    platform_code: str
    revoked_at: datetime


# ============= Platform Route Schemas =============

class PlatformRouteBase(BaseModel):
    """平台路由基础模型"""
    platform_code: str
    route_type: RouteType
    request_data: Dict[str, Any]


class PlatformRouteCreate(PlatformRouteBase):
    """创建平台路由"""
    order_id: Optional[str] = None
    request_id: Optional[str] = None


class PlatformRouteResponse(PlatformRouteBase):
    """平台路由响应"""
    id: str
    order_id: Optional[str] = None
    request_id: Optional[str] = None
    route_status: RouteStatus
    response_data: Optional[Dict[str, Any]] = None
    platform_order_id: Optional[str] = None
    retry_count: int
    last_retry_at: Optional[datetime] = None
    error_message: Optional[str] = None
    created_at: datetime
    completed_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class PlatformRouteDetailResponse(PlatformRouteResponse):
    """平台路由详情响应"""
    platform_config: Optional[PlatformConfigResponse] = None


class PlatformRouteListResponse(BaseModel):
    """平台路由列表响应"""
    items: List[PlatformRouteResponse]
    total: int
    page: int
    pages: int


class RetryRouteRequest(BaseModel):
    """重试路由请求"""
    max_retries: int = 3
    retry_delay: int = 5  # 秒


class RetryRouteResponse(BaseModel):
    """重试路由响应"""
    success: bool
    route_id: str
    retry_count: int
    route_status: RouteStatus
    message: str


# ============= Common Response Models =============

class OperationResponse(BaseModel):
    """通用操作响应"""
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None