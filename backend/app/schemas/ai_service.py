from pydantic import BaseModel, validator
from typing import Optional, List, Dict, Any
from datetime import datetime
from decimal import Decimal

class ContentGenerationRequest(BaseModel):
    content_type: str  # article/social_post/product_description/email
    topic: str
    keywords: List[str]
    target_audience: Optional[str] = None
    tone: Optional[str] = "professional"  # professional/casual/friendly/formal
    length: Optional[str] = "medium"      # short/medium/long
    language: str = "zh-CN"
    additional_requirements: Optional[str] = None
    ai_model: str = "doubao"

    # 对话相关字段
    conversation_id: Optional[str] = None      # 对话ID，首次请求为None
    template_id: Optional[str] = None          # 模板ID
    template_parameters: Optional[Dict] = None # 模板参数
    knowledge_bases: Optional[List[str]] = None # 知识库ID列表
    is_first_request: bool = True              # 是否首次请求
    context_messages: Optional[List[Dict]] = None # 上下文消息
    
    @validator('content_type')
    def validate_content_type(cls, v):
        valid_types = ['article', 'social_post', 'product_description', 'email', 'blog_post']
        if v not in valid_types:
            raise ValueError(f'内容类型必须是以下之一: {", ".join(valid_types)}')
        return v
    
    @validator('topic')
    def validate_topic(cls, v):
        if not v or len(v.strip()) < 2:
            raise ValueError('主题至少需要2个字符')
        if len(v) > 200:
            raise ValueError('主题不能超过200个字符')
        return v.strip()
    
    @validator('keywords')
    def validate_keywords(cls, v):
        if not v or len(v) == 0:
            raise ValueError('至少需要一个关键词')
        if len(v) > 10:
            raise ValueError('关键词数量不能超过10个')
        for keyword in v:
            if not keyword or len(keyword.strip()) < 2:
                raise ValueError('关键词至少需要2个字符')
        return [keyword.strip() for keyword in v]
    
    @validator('tone')
    def validate_tone(cls, v):
        if v and v not in ['professional', 'casual', 'friendly', 'formal']:
            raise ValueError('语调必须是 professional、casual、friendly 或 formal')
        return v
    
    @validator('length')
    def validate_length(cls, v):
        if v and v not in ['short', 'medium', 'long']:
            raise ValueError('长度必须是 short、medium 或 long')
        return v
    
    @validator('ai_model')
    def validate_ai_model(cls, v):
        # 只允许使用豆包
        if v != 'doubao':
            raise ValueError('当前只支持豆包AI模型')
        return v

class ContentOptimizationRequest(BaseModel):
    original_content: str
    optimization_goals: List[str]  # seo/readability/engagement/conversion
    target_keywords: Optional[List[str]] = None
    target_audience: Optional[str] = None
    platform: Optional[str] = None  # wechat/weibo/xiaohongshu/website
    ai_model: str = "doubao"
    
    @validator('original_content')
    def validate_content_length(cls, v):
        if len(v) < 10:
            raise ValueError('原始内容长度不能少于10个字符')
        if len(v) > 10000:
            raise ValueError('原始内容长度不能超过10000个字符')
        return v
    
    @validator('optimization_goals')
    def validate_optimization_goals(cls, v):
        if not v or len(v) == 0:
            raise ValueError('至少需要一个优化目标')
        valid_goals = ['seo', 'readability', 'engagement', 'conversion']
        for goal in v:
            if goal not in valid_goals:
                raise ValueError(f'优化目标必须是以下之一: {", ".join(valid_goals)}')
        return v
    
    @validator('target_keywords')
    def validate_target_keywords(cls, v):
        if v and len(v) > 10:
            raise ValueError('目标关键词数量不能超过10个')
        return v
    
    @validator('platform')
    def validate_platform(cls, v):
        if v and v not in ['wechat', 'weibo', 'xiaohongshu', 'website']:
            raise ValueError('平台必须是 wechat、weibo、xiaohongshu 或 website')
        return v

    @validator('ai_model')
    def validate_ai_model(cls, v):
        # 只允许使用豆包
        if v != 'doubao':
            raise ValueError('当前只支持豆包AI模型')
        return v

class KeywordAnalysisRequest(BaseModel):
    keywords: List[str]
    industry: Optional[str] = None
    target_region: str = "China"
    analysis_depth: str = "standard"  # basic/standard/deep
    include_competitors: bool = False
    ai_model: str = "doubao"
    
    @validator('keywords')
    def validate_keywords(cls, v):
        if not v or len(v) == 0:
            raise ValueError('至少需要一个关键词')
        if len(v) > 20:
            raise ValueError('关键词数量不能超过20个')
        for keyword in v:
            if not keyword or len(keyword.strip()) < 2:
                raise ValueError('关键词至少需要2个字符')
        return [keyword.strip() for keyword in v]
    
    @validator('analysis_depth')
    def validate_analysis_depth(cls, v):
        if v not in ['basic', 'standard', 'deep']:
            raise ValueError('分析深度必须是 basic、standard 或 deep')
        return v
    
    @validator('target_region')
    def validate_target_region(cls, v):
        if not v or len(v.strip()) < 2:
            raise ValueError('目标地区至少需要2个字符')
        return v.strip()

    @validator('ai_model')
    def validate_ai_model(cls, v):
        # 只允许使用豆包
        if v != 'doubao':
            raise ValueError('当前只支持豆包AI模型')
        return v

class AIUsageQuery(BaseModel):
    page: int = 1
    size: int = 20
    service_type: Optional[str] = None
    status: Optional[str] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    sort_by: str = "created_at"
    sort_order: str = "desc"
    
    @validator('page')
    def validate_page(cls, v):
        if v < 1:
            raise ValueError('页码必须大于0')
        return v
    
    @validator('size')
    def validate_size(cls, v):
        if v < 1 or v > 100:
            raise ValueError('每页数量必须在1-100之间')
        return v
    

    
    @validator('status')
    def validate_status(cls, v):
        if v and v not in ['pending', 'completed', 'failed', 'cancelled']:
            raise ValueError('状态必须是 pending、completed、failed 或 cancelled')
        return v
    
    @validator('sort_by')
    def validate_sort_by(cls, v):
        allowed_fields = ['created_at', 'updated_at', 'completed_at', 'tokens_used', 'cost_amount']
        if v not in allowed_fields:
            raise ValueError(f'排序字段必须是以下之一: {", ".join(allowed_fields)}')
        return v
    
    @validator('sort_order')
    def validate_sort_order(cls, v):
        if v not in ['asc', 'desc']:
            raise ValueError('排序方式必须是 asc 或 desc')
        return v
    
    @validator('end_date')
    def validate_date_range(cls, v, values):
        if v and values.get('start_date') and v < values['start_date']:
            raise ValueError('结束日期不能早于开始日期')
        return v

class AIRequestResponse(BaseModel):
    id: str
    service_type: str
    ai_model: str
    request_title: Optional[str]
    status: str
    response_content: Optional[str]
    response_data: Optional[Dict[str, Any]]
    quality_score: Optional[Decimal]
    user_rating: Optional[int]
    tokens_used: int
    processing_time: Optional[int]
    cost_amount: Optional[Decimal]
    created_at: datetime
    completed_at: Optional[datetime]

class ContentGenerationResponse(BaseModel):
    request_id: str
    generated_content: str
    content_metadata: Dict[str, Any]  # 字数、关键词密度等
    suggestions: List[str]            # 优化建议
    quality_score: Optional[Decimal]
    tokens_used: int
    processing_time: int

class ContentOptimizationResponse(BaseModel):
    request_id: str
    optimized_content: str
    optimization_summary: Dict[str, Any]  # 优化摘要
    improvements: List[Dict[str, Any]]    # 具体改进点
    seo_score: Optional[Decimal]
    readability_score: Optional[Decimal]
    tokens_used: int
    processing_time: int

class KeywordAnalysisResponse(BaseModel):
    request_id: str
    keyword_insights: List[Dict[str, Any]]  # 关键词洞察
    trend_analysis: Dict[str, Any]          # 趋势分析
    competition_analysis: Optional[Dict[str, Any]]  # 竞争分析
    recommendations: List[str]              # 推荐建议
    tokens_used: int
    processing_time: int

class AIQuotaResponse(BaseModel):
    service_type: str
    total_quota: int
    used_quota: int
    remaining_quota: int
    quota_period: str
    period_start: datetime
    period_end: datetime
    usage_percentage: float

class AIUsageHistoryResponse(BaseModel):
    items: List[AIRequestResponse]
    pagination: Dict[str, int]
    statistics: Dict[str, Any]

# 通用响应模式
# AI模板相关Schema
class AITemplateCreate(BaseModel):
    template_name: str
    template_description: Optional[str] = None
    prompt_template: str
    default_parameters: Optional[Dict[str, Any]] = None

    @validator('template_name')
    def validate_template_name(cls, v):
        if not v or len(v.strip()) < 2:
            raise ValueError('模板名称至少需要2个字符')
        if len(v) > 200:
            raise ValueError('模板名称不能超过200个字符')
        return v.strip()



    @validator('prompt_template')
    def validate_prompt_template(cls, v):
        if not v or len(v.strip()) < 10:
            raise ValueError('提示词模板至少需要10个字符')
        return v.strip()

class AITemplateUpdate(BaseModel):
    template_name: Optional[str] = None
    template_description: Optional[str] = None
    prompt_template: Optional[str] = None
    default_parameters: Optional[Dict[str, Any]] = None
    is_active: Optional[bool] = None

    @validator('template_name')
    def validate_template_name(cls, v):
        if v is not None:
            if not v or len(v.strip()) < 2:
                raise ValueError('模板名称至少需要2个字符')
            if len(v) > 200:
                raise ValueError('模板名称不能超过200个字符')
            return v.strip()
        return v

    @validator('prompt_template')
    def validate_prompt_template(cls, v):
        if v is not None:
            if not v or len(v.strip()) < 10:
                raise ValueError('提示词模板至少需要10个字符')
            return v.strip()
        return v

class AITemplateResponse(BaseModel):
    id: str
    template_name: str
    template_description: Optional[str]
    prompt_template: str
    default_parameters: Optional[Dict[str, Any]]
    usage_count: int
    success_rate: Optional[Decimal]
    avg_quality_score: Optional[Decimal]
    is_active: bool
    created_at: datetime
    updated_at: datetime

class AITemplateListQuery(BaseModel):
    page: int = 1
    size: int = 20
    is_active: Optional[bool] = None
    keyword: Optional[str] = None
    sort_by: str = "created_at"
    sort_order: str = "desc"

class AITemplateListResponse(BaseModel):
    items: List[AITemplateResponse]
    pagination: Dict[str, int]
    statistics: Dict[str, Any]

class AIApiResponse(BaseModel):
    success: bool
    data: Optional[Any] = None
    message: str


# ========== 对话管理相关Schemas ==========

class ConversationCreateRequest(BaseModel):
    title: str
    template_id: Optional[str] = None
    template_parameters: Optional[Dict] = None
    knowledge_bases: Optional[List[str]] = None

class ConversationResponse(BaseModel):
    id: str
    title: str
    template_id: Optional[str] = None
    message_count: int
    created_at: datetime
    updated_at: datetime

class MessageSendRequest(BaseModel):
    content: str
    message_type: str = 'user'  # 'user' or 'assistant'
    request_id: Optional[str] = None

class MessageResponse(BaseModel):
    id: str
    conversation_id: str
    message_type: str  # USER or ASSISTANT
    content: str
    created_at: datetime

class ConversationListResponse(BaseModel):
    items: List[ConversationResponse]
    pagination: Dict[str, int]

class ConversationDetailResponse(BaseModel):
    id: str
    title: str
    template_id: Optional[str] = None
    template_parameters: Optional[Dict] = None
    knowledge_bases: Optional[List[str]] = None
    context_summary: Optional[str] = None
    message_count: int
    messages: List[MessageResponse]
    created_at: datetime
    updated_at: datetime


# ========== 知识库管理相关Schemas ==========

class KnowledgeBaseCreateRequest(BaseModel):
    name: str
    description: Optional[str] = None

class KnowledgeBaseResponse(BaseModel):
    id: str
    name: str
    description: Optional[str] = None
    document_count: int
    vector_count: int
    created_at: datetime
    updated_at: datetime

class DocumentUploadRequest(BaseModel):
    title: str
    content: str
    file_type: Optional[str] = "text"

class DocumentResponse(BaseModel):
    id: str
    knowledge_base_id: str
    title: str
    file_type: Optional[str] = None
    file_size: Optional[int] = None
    chunk_count: int
    embedding_status: str
    created_at: datetime
    updated_at: datetime

class DocumentUploadResponse(BaseModel):
    id: str
    title: str
    embedding_status: str
    message: str

class SearchRequest(BaseModel):
    query: str
    keywords: Optional[List[str]] = None
    limit: int = 5

class SearchResult(BaseModel):
    content: str
    title: str
    document_id: str
    similarity_score: float
    knowledge_base_id: str

class SearchResultResponse(BaseModel):
    results: List[SearchResult]
    total: int
    query: str
