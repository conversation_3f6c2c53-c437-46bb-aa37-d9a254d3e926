from pydantic import BaseModel, <PERSON>, validator
from typing import Optional, List, Dict, Any
from datetime import datetime, date
from decimal import Decimal
from enum import Enum
import uuid

# Enums
class BillingCycle(str, Enum):
    MONTHLY = "monthly"
    QUARTERLY = "quarterly"
    SEMI_ANNUAL = "semi_annual"
    YEARLY = "yearly"

class PlanType(str, Enum):
    FREE = "free"
    BASIC = "basic"
    PRO = "pro"
    ENTERPRISE = "enterprise"
    CUSTOM = "custom"

class UserType(str, Enum):
    ENTERPRISE = "enterprise"
    PROVIDER = "provider"
    BOTH = "both"

class ChangeType(str, Enum):
    UPGRADE = "upgrade"
    DOWNGRADE = "downgrade"
    RENEWAL = "renewal"
    CANCELLATION = "cancellation"
    PAUSE = "pause"
    RESUME = "resume"

class QuotaType(str, Enum):
    CONTENT = "content"
    MONITORING = "monitoring"
    API = "api"
    TEAM = "team"
    SERVICE = "service"
    CHANNEL = "channel"

# Request Schemas
class SubscriptionPlanCreate(BaseModel):
    plan_code: str
    plan_name: str
    plan_type: PlanType
    target_user_type: UserType
    monthly_price: Decimal
    quarterly_price: Optional[Decimal] = None
    semi_annual_price: Optional[Decimal] = None
    yearly_price: Optional[Decimal] = None

    # Enterprise quotas
    max_content_requests: int = 0
    max_monitoring_projects: int = 0
    max_api_calls: int = 0
    max_team_members: int = 1

    # Provider quotas
    max_service_orders: int = 0
    max_channels: int = 1
    commission_rate: Optional[Decimal] = None

    features: Optional[Dict[str, Any]] = None
    is_active: bool = True
    display_order: int = 0

class SubscriptionPlanUpdate(BaseModel):
    plan_name: Optional[str] = None
    plan_type: Optional[PlanType] = None
    target_user_type: Optional[UserType] = None
    monthly_price: Optional[Decimal] = None
    quarterly_price: Optional[Decimal] = None
    semi_annual_price: Optional[Decimal] = None
    yearly_price: Optional[Decimal] = None

    # Enterprise quotas
    max_content_requests: Optional[int] = None
    max_monitoring_projects: Optional[int] = None
    max_api_calls: Optional[int] = None
    max_team_members: Optional[int] = None

    # Provider quotas
    max_service_orders: Optional[int] = None
    max_channels: Optional[int] = None
    commission_rate: Optional[Decimal] = None

    features: Optional[Dict[str, Any]] = None
    is_active: Optional[bool] = None
    display_order: Optional[int] = None

class SubscriptionPlanStatusUpdate(BaseModel):
    is_active: bool
    reason: Optional[str] = None

class SubscriptionCreate(BaseModel):
    plan_id: str
    billing_cycle: BillingCycle
    payment_method: str
    auto_renewal: bool = False
    coupon_code: Optional[str] = None

class SubscriptionUpgrade(BaseModel):
    target_plan_id: str
    billing_cycle: Optional[BillingCycle] = None
    effective_immediately: bool = True
    prorate: bool = True

class SubscriptionRenewal(BaseModel):
    billing_cycle: BillingCycle
    payment_method: str
    apply_discount: bool = True

class SubscriptionCancel(BaseModel):
    reason: str
    feedback: Optional[str] = None
    cancel_immediately: bool = False

class AdminSubscriptionUpdate(BaseModel):
    """管理员编辑订阅的统一请求模型"""
    # 状态相关
    status: Optional[str] = None  # active/expired/cancelled/suspended

    # 时间相关
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None

    # 套餐相关
    plan_id: Optional[str] = None

    # 配置相关
    auto_renewal: Optional[bool] = None
    renewal_price: Optional[Decimal] = None

    # 配额相关（直接修改使用量）
    content_used: Optional[int] = None
    monitoring_used: Optional[int] = None
    ai_used: Optional[int] = None

    # 操作原因
    reason: Optional[str] = None

class QuotaIncrease(BaseModel):
    quota_type: QuotaType
    amount: int
    expires_at: Optional[datetime] = None
    reason: Optional[str] = None

class QuotaAlertSettings(BaseModel):
    quota_type: QuotaType
    threshold_percentage: int  # 0-100
    notification_channels: List[str] = ["email"]
    notification_frequency: str = "once"  # once/daily/weekly

class AutoRenewalSettings(BaseModel):
    enabled: bool = True
    billing_cycle: BillingCycle = BillingCycle.MONTHLY
    payment_method: str = "default"
    renewal_price: Optional[Decimal] = None
    notify_days_before: int = 3

class RenewalNotificationSettings(BaseModel):
    email_enabled: bool = True
    sms_enabled: bool = False
    advance_days: int = 3
    reminder_frequency: str = "once"  # once/daily/weekly

# Query Schemas
class SubscriptionListQuery(BaseModel):
    user_id: Optional[str] = None
    company_id: Optional[str] = None
    plan_type: Optional[PlanType] = None
    is_active: Optional[bool] = None
    expiring_days: Optional[int] = None  # Find subscriptions expiring in N days
    page: int = Field(default=1, ge=1)
    size: int = Field(default=20, le=100)
    sort_by: str = "created_at"
    sort_order: str = "desc"

class BillListQuery(BaseModel):
    start_date: Optional[date] = None
    end_date: Optional[date] = None
    min_amount: Optional[Decimal] = None
    max_amount: Optional[Decimal] = None
    page: int = Field(default=1, ge=1)
    size: int = Field(default=20, le=100)

# Response Schemas
class SubscriptionPlanResponse(BaseModel):
    id: str
    plan_code: str
    plan_name: str
    plan_type: str
    target_user_type: str
    monthly_price: Decimal
    quarterly_price: Optional[Decimal]
    semi_annual_price: Optional[Decimal]
    yearly_price: Optional[Decimal]

    # Quotas
    max_content_requests: int
    max_monitoring_projects: int
    max_api_calls: int
    max_team_members: int
    max_service_orders: int
    max_channels: int
    commission_rate: Optional[Decimal]

    features: Optional[Dict[str, Any]]
    is_active: bool
    display_order: int
    created_at: datetime
    updated_at: datetime

    # 新增字段：是否已购买
    is_purchased: Optional[bool] = False
    current_subscription_id: Optional[str] = None

    class Config:
        from_attributes = True

class QuotaUsageDetail(BaseModel):
    quota_type: str
    max_value: int
    used_value: int
    remaining: int
    percentage_used: float
    reset_date: Optional[datetime]

class SubscriptionResponse(BaseModel):
    id: str
    user_id: str
    company_id: Optional[str]
    order_id: str
    plan_id: str
    plan_name: str
    plan_type: str
    
    # Quotas
    content_quota: int
    content_used: int
    monitoring_quota: int
    monitoring_used: int
    ai_quota: int
    ai_used: int
    
    # Validity
    start_date: datetime
    end_date: datetime
    is_active: bool
    days_remaining: int
    billing_cycle: str

    # Renewal
    auto_renewal: bool
    renewal_price: Optional[Decimal]
    
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class SubscriptionDetailResponse(SubscriptionResponse):
    plan: SubscriptionPlanResponse
    quota_details: List[QuotaUsageDetail]
    billing_history: List[Dict[str, Any]]
    change_history: List[Dict[str, Any]]
    next_billing_date: Optional[datetime]
    can_upgrade: bool
    can_downgrade: bool
    available_plans: List[SubscriptionPlanResponse]

class QuotaUsageResponse(BaseModel):
    subscription_id: str
    user_id: str
    usage_month: str
    user_type: str
    
    # Enterprise quotas
    content_requests: QuotaUsageDetail
    monitoring_projects: QuotaUsageDetail
    api_calls: QuotaUsageDetail
    team_members: QuotaUsageDetail
    
    # Provider quotas (if applicable)
    service_orders: Optional[QuotaUsageDetail]
    channels: Optional[QuotaUsageDetail]
    
    last_updated: datetime
    alerts: List[Dict[str, Any]]

class SubscriptionChangeResponse(BaseModel):
    id: str
    subscription_id: str
    change_type: str
    from_plan: Optional[Dict[str, Any]]
    to_plan: Optional[Dict[str, Any]]
    amount: Optional[Decimal]
    effective_date: datetime
    status: str
    message: str

class BillResponse(BaseModel):
    id: str
    subscription_id: str
    billing_period: str
    amount: Decimal
    currency: str
    status: str
    due_date: Optional[datetime]
    paid_date: Optional[datetime]
    payment_method: Optional[str]
    invoice_url: Optional[str]
    created_at: datetime

class BillSummaryResponse(BaseModel):
    user_id: str
    total_amount: Decimal
    paid_amount: Decimal
    pending_amount: Decimal
    total_bills: int
    paid_bills: int
    pending_bills: int
    overdue_bills: int
    next_bill_date: Optional[datetime] = None
    next_bill_amount: Optional[Decimal] = None
    next_bill_date: Optional[datetime]
    next_bill_amount: Optional[Decimal]