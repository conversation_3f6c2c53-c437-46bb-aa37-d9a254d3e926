from pydantic import BaseModel, field_validator
from typing import Optional, List, Dict, Any
from datetime import datetime


class ConversationCreateRequest(BaseModel):
    title: str
    template_id: Optional[str] = None
    template_parameters: Optional[Dict] = None
    knowledge_bases: Optional[List[str]] = None
    
    @field_validator('title')
    @classmethod
    def validate_title(cls, v):
        if not v or len(v.strip()) < 2:
            raise ValueError('对话标题至少需要2个字符')
        if len(v) > 200:
            raise ValueError('对话标题不能超过200个字符')
        return v.strip()


class ConversationUpdateRequest(BaseModel):
    title: Optional[str] = None
    template_parameters: Optional[Dict] = None
    knowledge_bases: Optional[List[str]] = None
    
    @field_validator('title')
    @classmethod
    def validate_title(cls, v):
        if v is not None:
            if not v or len(v.strip()) < 2:
                raise ValueError('对话标题至少需要2个字符')
            if len(v) > 200:
                raise ValueError('对话标题不能超过200个字符')
            return v.strip()
        return v


class ConversationResponse(BaseModel):
    id: str
    title: str
    template_id: Optional[str] = None
    message_count: int
    created_at: datetime
    updated_at: datetime


class ConversationListQuery(BaseModel):
    page: int = 1
    size: int = 20
    keyword: Optional[str] = None
    template_id: Optional[str] = None
    sort_by: str = "updated_at"
    sort_order: str = "desc"
    
    @field_validator('page')
    @classmethod
    def validate_page(cls, v):
        if v < 1:
            raise ValueError('页码必须大于0')
        return v
    
    @field_validator('size')
    @classmethod
    def validate_size(cls, v):
        if v < 1 or v > 100:
            raise ValueError('每页数量必须在1-100之间')
        return v


class ConversationListResponse(BaseModel):
    items: List[ConversationResponse]
    pagination: Dict[str, int]


class MessageSendRequest(BaseModel):
    content: str
    
    @field_validator('content')
    @classmethod
    def validate_content(cls, v):
        if not v or len(v.strip()) < 1:
            raise ValueError('消息内容不能为空')
        if len(v) > 10000:
            raise ValueError('消息内容不能超过10000个字符')
        return v.strip()


class MessageResponse(BaseModel):
    id: str
    conversation_id: str
    message_type: str  # USER or ASSISTANT
    content: str
    request_id: Optional[str] = None
    created_at: datetime


class MessageListQuery(BaseModel):
    page: int = 1
    size: int = 50
    message_type: Optional[str] = None
    
    @field_validator('page')
    @classmethod
    def validate_page(cls, v):
        if v < 1:
            raise ValueError('页码必须大于0')
        return v
    
    @field_validator('size')
    @classmethod
    def validate_size(cls, v):
        if v < 1 or v > 100:
            raise ValueError('每页数量必须在1-100之间')
        return v


class MessageListResponse(BaseModel):
    items: List[MessageResponse]
    pagination: Dict[str, int]


class ConversationDetailResponse(BaseModel):
    id: str
    title: str
    template_id: Optional[str] = None
    template_parameters: Optional[Dict] = None
    knowledge_bases: Optional[List[str]] = None
    context_summary: Optional[str] = None
    message_count: int
    messages: List[MessageResponse]
    created_at: datetime
    updated_at: datetime


class ConversationStatistics(BaseModel):
    total_messages: int
    user_messages: int
    assistant_messages: int
    avg_response_time: Optional[float] = None
    last_activity: Optional[datetime] = None


class ConversationSummaryRequest(BaseModel):
    conversation_id: str
    force_regenerate: bool = False


class ConversationSummaryResponse(BaseModel):
    conversation_id: str
    summary: str
    generated_at: datetime


class StreamingMessageChunk(BaseModel):
    type: str  # content, final, error
    content: Optional[str] = None
    full_content: Optional[str] = None
    conversation_id: Optional[str] = None
    request_id: Optional[str] = None
    error: Optional[str] = None
    progress: Optional[float] = None


class ConversationExportRequest(BaseModel):
    format: str = "json"  # json, markdown, txt
    include_metadata: bool = True
    
    @field_validator('format')
    @classmethod
    def validate_format(cls, v):
        if v not in ['json', 'markdown', 'txt']:
            raise ValueError('导出格式必须是 json、markdown 或 txt')
        return v


class ConversationExportResponse(BaseModel):
    conversation_id: str
    format: str
    content: str
    filename: str
    exported_at: datetime


class ConversationSearchRequest(BaseModel):
    query: str
    limit: int = 10
    include_messages: bool = False
    
    @field_validator('query')
    @classmethod
    def validate_query(cls, v):
        if not v or len(v.strip()) < 2:
            raise ValueError('搜索查询至少需要2个字符')
        if len(v) > 200:
            raise ValueError('搜索查询不能超过200个字符')
        return v.strip()
    
    @field_validator('limit')
    @classmethod
    def validate_limit(cls, v):
        if v < 1 or v > 50:
            raise ValueError('搜索结果数量必须在1-50之间')
        return v


class ConversationSearchResult(BaseModel):
    conversation: ConversationResponse
    matched_messages: Optional[List[MessageResponse]] = None
    relevance_score: float


class ConversationSearchResponse(BaseModel):
    results: List[ConversationSearchResult]
    total: int
    query: str


class ConversationApiResponse(BaseModel):
    success: bool
    data: Optional[Any] = None
    message: str
    error_code: Optional[str] = None
