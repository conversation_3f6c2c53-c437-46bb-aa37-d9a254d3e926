from pydantic import BaseModel, EmailStr, HttpUrl, validator
from typing import Optional, List, Dict, Any
from datetime import datetime
from decimal import Decimal

class CompanyProfileRequest(BaseModel):
    company_name: str
    company_name_en: Optional[str] = None
    company_code: str  # 统一社会信用代码（营业执照号）
    legal_person: str
    contact_phone: Optional[str] = None
    contact_email: EmailStr
    headquarters_location: str
    industry: str
    company_size: str  # startup/small/medium/large/enterprise
    founded_year: Optional[int] = None
    business_scope: Optional[str] = None
    company_description: Optional[str] = None
    official_website: Optional[HttpUrl] = None
    business_license_url: Optional[str] = None
    other_files: Optional[List[str]] = None
    social_media_links: Optional[Dict[str, str]] = None
    
    @validator('company_name')
    def validate_company_name(cls, v):
        if not v or len(v.strip()) < 2:
            raise ValueError('企业名称至少需要2个字符')
        if len(v) > 200:
            raise ValueError('企业名称不能超过200个字符')
        return v.strip()
    
    @validator('company_code')
    def validate_company_code(cls, v):
        if not v or len(v.strip()) != 18:
            raise ValueError('统一社会信用代码必须是18位')
        # 简单校验：只允许大写字母和数字
        if not v.strip().replace('-', '').isalnum():
            raise ValueError('统一社会信用代码格式不正确')
        return v.strip().upper()
    
    @validator('legal_person')
    def validate_legal_person(cls, v):
        if not v or len(v.strip()) < 2:
            raise ValueError('法人姓名至少需要2个字符')
        if len(v) > 100:
            raise ValueError('法人姓名不能超过100个字符')
        return v.strip()
    
    @validator('company_size')
    def validate_company_size(cls, v):
        valid_sizes = ['startup', 'small', 'medium', 'large', 'enterprise']
        if v not in valid_sizes:
            raise ValueError(f'企业规模必须是以下之一: {", ".join(valid_sizes)}')
        return v
    
    @validator('founded_year')
    def validate_founded_year(cls, v):
        if v is not None:
            if v == 0:
                return None  # 将0转换为None
            if v < 1900 or v > datetime.now().year:
                raise ValueError('成立年份不合法')
        return v
    
    @validator('contact_phone')
    def validate_contact_phone(cls, v):
        if v and not v.replace('-', '').replace(' ', '').isdigit():
            raise ValueError('联系电话格式不正确')
        return v
    
    @validator('headquarters_location')
    def validate_headquarters_location(cls, v):
        if not v or len(v.strip()) < 5:
            raise ValueError('总部地址至少需要5个字符')
        return v.strip()
    
    @validator('industry')
    def validate_industry(cls, v):
        if not v or len(v.strip()) < 2:
            raise ValueError('行业信息至少需要2个字符')
        return v.strip()

    @validator('official_website', pre=True)
    def validate_official_website(cls, v):
        # 如果是空字符串，转换为 None
        if v == "":
            return None
        return v

class CompanyUpdateRequest(BaseModel):
    contact_phone: Optional[str] = None
    contact_email: Optional[EmailStr] = None
    headquarters_location: Optional[str] = None
    official_website: Optional[HttpUrl] = None
    business_scope: Optional[str] = None
    company_description: Optional[str] = None
    
    @validator('contact_phone')
    def validate_contact_phone(cls, v):
        if v and not v.replace('-', '').replace(' ', '').isdigit():
            raise ValueError('联系电话格式不正确')
        return v
    
    @validator('headquarters_location')
    def validate_headquarters_location(cls, v):
        if v and len(v.strip()) < 5:
            raise ValueError('总部地址至少需要5个字符')
        return v.strip() if v else v

    @validator('official_website', pre=True)
    def validate_official_website(cls, v):
        # 如果是空字符串，转换为 None
        if v == "":
            return None
        return v

class CompanyListQuery(BaseModel):
    page: int = 1
    size: int = 20
    verification_status: Optional[str] = None
    industry: Optional[str] = None
    company_size: Optional[str] = None
    search: Optional[str] = None
    sort_by: str = "created_at"
    sort_order: str = "desc"
    
    @validator('page')
    def validate_page(cls, v):
        if v < 1:
            raise ValueError('页码必须大于0')
        return v
    
    @validator('size')
    def validate_size(cls, v):
        if v < 1 or v > 100:
            raise ValueError('每页数量必须在1-100之间')
        return v
    
    @validator('verification_status')
    def validate_verification_status(cls, v):
        if v and v not in ['pending', 'approved', 'rejected']:
            raise ValueError('审核状态必须是 pending、approved 或 rejected')
        return v
    
    @validator('company_size')
    def validate_company_size(cls, v):
        if v and v not in ['startup', 'small', 'medium', 'large', 'enterprise']:
            raise ValueError('企业规模必须是 startup、small、medium、large 或 enterprise')
        return v
    
    @validator('sort_by')
    def validate_sort_by(cls, v):
        allowed_fields = ['created_at', 'updated_at', 'company_name', 'verification_status', 'founded_year']
        if v not in allowed_fields:
            raise ValueError(f'排序字段必须是以下之一: {", ".join(allowed_fields)}')
        return v
    
    @validator('sort_order')
    def validate_sort_order(cls, v):
        if v not in ['asc', 'desc']:
            raise ValueError('排序方式必须是 asc 或 desc')
        return v

class CompanyVerificationRequest(BaseModel):
    status: str  # verified/rejected
    note: str
    notify_user: bool = True

    @validator('status')
    def validate_status(cls, v):
        if v not in ['verified', 'rejected']:
            raise ValueError('审核状态必须是 verified 或 rejected')
        return v
    
    @validator('note')
    def validate_note(cls, v):
        if not v or len(v.strip()) < 5:
            raise ValueError('审核备注至少需要5个字符')
        if len(v) > 500:
            raise ValueError('审核备注不能超过500个字符')
        return v.strip()

class CompanyResponse(BaseModel):
    id: str
    company_name: str
    company_name_en: Optional[str]
    company_code: Optional[str]
    legal_person: str
    contact_phone: Optional[str]
    contact_email: str
    headquarters_location: str
    industry: str
    company_size: str
    founded_year: Optional[int]
    business_scope: Optional[str]
    company_description: Optional[str]
    official_website: Optional[str]
    business_license_url: Optional[str]  # 营业执照文件URL
    other_files: Optional[List[str]]     # 其他资质文件URLs
    social_media_links: Optional[Dict[str, str]]  # 社交媒体链接
    verification_status: str
    verification_time: Optional[datetime]
    verification_note: Optional[str]
    created_at: datetime
    updated_at: datetime
    submitted_at: Optional[datetime]

class CompanyListResponse(BaseModel):
    items: List[CompanyResponse]
    pagination: Dict[str, int]

class CompanyVerificationResponse(BaseModel):
    company_id: str
    company_name: str
    old_status: str
    new_status: str
    verification_note: str
    verifier: str
    verification_time: datetime

# 通用响应模式
class CompanyApiResponse(BaseModel):
    success: bool
    data: Optional[Any] = None
    message: str
