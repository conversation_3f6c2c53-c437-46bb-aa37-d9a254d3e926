from pydantic import BaseModel, Field, validator
from typing import Optional, List, Dict, Any, Union
from datetime import datetime
from decimal import Decimal
from uuid import UUID
from enum import Enum

class SubscriptionOrderCreate(BaseModel):
    plan_id: str
    service_months: int = 12  # 服务月数
    coupon_code: Optional[str] = None
    customer_note: Optional[str] = None
    auto_renewal: bool = False
    
    @validator('plan_id')
    def validate_plan_id(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('套餐ID不能为空')
        return v.strip()
    
    @validator('service_months')
    def validate_service_months(cls, v):
        if v not in [1, 3, 6, 12, 24, 36]:
            raise ValueError('服务月数必须是 1, 3, 6, 12, 24, 36 之一')
        return v
    
    @validator('coupon_code')
    def validate_coupon_code(cls, v):
        if v and len(v.strip()) < 3:
            raise ValueError('优惠券代码至少需要3个字符')
        return v.strip() if v else v
    
    @validator('customer_note')
    def validate_customer_note(cls, v):
        if v and len(v) > 500:
            raise ValueError('客户备注不能超过500个字符')
        return v

class PaymentRequest(BaseModel):
    payment_method: str  # alipay/wechat/bank_card/balance
    return_url: Optional[str] = None
    notify_url: Optional[str] = None
    
    @validator('payment_method')
    def validate_payment_method(cls, v):
        valid_methods = ['alipay', 'wechat', 'bank_card', 'balance']
        if v not in valid_methods:
            raise ValueError(f'支付方式必须是以下之一: {", ".join(valid_methods)}')
        return v
    
    @validator('return_url', 'notify_url')
    def validate_urls(cls, v):
        if v and not v.startswith(('http://', 'https://')):
            raise ValueError('URL必须以http://或https://开头')
        return v

class OrderListQuery(BaseModel):
    page: int = 1
    size: int = 20
    order_type: Optional[str] = None
    order_status: Optional[str] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    sort_by: str = "created_at"
    sort_order: str = "desc"
    
    @validator('page')
    def validate_page(cls, v):
        if v < 1:
            raise ValueError('页码必须大于0')
        return v
    
    @validator('size')
    def validate_size(cls, v):
        if v < 1 or v > 100:
            raise ValueError('每页数量必须在1-100之间')
        return v
    
    @validator('order_type')
    def validate_order_type(cls, v):
        if v and v not in ['subscription', 'content_service']:
            raise ValueError('订单类型必须是 subscription 或 content_service')
        return v
    
    @validator('order_status')
    def validate_order_status(cls, v):
        valid_statuses = ['pending', 'paid', 'processing', 'completed', 'cancelled', 'refunded']
        if v and v not in valid_statuses:
            raise ValueError(f'订单状态必须是以下之一: {", ".join(valid_statuses)}')
        return v
    
    @validator('sort_by')
    def validate_sort_by(cls, v):
        allowed_fields = ['created_at', 'updated_at', 'final_amount', 'order_status']
        if v not in allowed_fields:
            raise ValueError(f'排序字段必须是以下之一: {", ".join(allowed_fields)}')
        return v
    
    @validator('sort_order')
    def validate_sort_order(cls, v):
        if v not in ['asc', 'desc']:
            raise ValueError('排序方式必须是 asc 或 desc')
        return v

class OrderResponse(BaseModel):
    id: str
    order_no: str
    user_id: str
    company_id: Optional[str]
    order_type: str
    order_status: str
    product_name: str
    product_description: Optional[str]
    original_amount: Decimal
    discount_amount: Decimal
    final_amount: Decimal
    currency: str
    coupon_code: Optional[str]
    agent_id: Optional[str]
    commission_amount: Optional[Decimal]
    customer_note: Optional[str]
    created_at: datetime
    updated_at: datetime
    paid_at: Optional[datetime]
    completed_at: Optional[datetime]
    service_start_date: Optional[datetime]
    service_end_date: Optional[datetime]
    
    # 支付信息
    payment_info: Optional[Dict[str, Any]] = None
    # 订单项
    order_items: Optional[List[Dict[str, Any]]] = None

class PaymentResponse(BaseModel):
    payment_no: str
    order_id: str
    payment_method: str
    payment_status: str
    payment_amount: Decimal
    payment_url: Optional[str] = None  # 支付链接
    qr_code: Optional[str] = None      # 二维码
    expires_at: Optional[datetime] = None

class SubscriptionResponse(BaseModel):
    id: str
    order_id: str
    plan_name: str
    plan_type: str
    content_quota: int
    monitoring_quota: int
    ai_quota: int
    content_used: int
    monitoring_used: int
    ai_used: int
    start_date: datetime
    end_date: datetime
    is_active: bool
    auto_renewal: bool
    created_at: datetime

class OrderListResponse(BaseModel):
    items: List[OrderResponse]
    pagination: Dict[str, int]
    statistics: Dict[str, Any]

class OrderCreateResponse(BaseModel):
    id: str
    order_no: str
    order_type: str
    product_name: str
    original_amount: Decimal
    discount_amount: Decimal
    final_amount: Decimal
    order_status: str
    created_at: datetime
    service_months: Optional[int] = None

class PaymentCallbackRequest(BaseModel):
    payment_no: str
    status: str  # success/failed
    trade_no: Optional[str] = None
    failure_reason: Optional[str] = None
    callback_data: Optional[Dict[str, Any]] = None
    
    @validator('status')
    def validate_status(cls, v):
        if v not in ['success', 'failed']:
            raise ValueError('支付状态必须是 success 或 failed')
        return v

# 统一订单创建请求
class UnifiedOrderCreate(BaseModel):
    """统一订单创建请求，支持多种订单类型"""
    order_type: str  # subscription | content_service
    
    # 套餐订单字段
    plan_id: Optional[str] = None
    service_months: Optional[int] = None
    auto_renewal: Optional[bool] = False
    
    # 内容服务订单字段
    service_id: Optional[str] = None
    content_request_id: Optional[str] = None
    service_provider_id: Optional[str] = None
    
    # 通用字段
    coupon_code: Optional[str] = None
    customer_note: Optional[str] = None
    
    @validator('order_type')
    def validate_order_type(cls, v):
        if v not in ['subscription', 'content_service']:
            raise ValueError('订单类型必须是 subscription 或 content_service')
        return v
    
    @validator('service_months')
    def validate_service_months(cls, v, values):
        if values.get('order_type') == 'subscription' and v:
            if v not in [1, 3, 6, 12, 24, 36]:
                raise ValueError('服务月数必须是 1, 3, 6, 12, 24, 36 之一')
        return v

# 内容服务订单创建
class ContentServiceOrderCreate(BaseModel):
    service_id: str
    content_request_id: Optional[str] = None
    service_provider_id: str
    customer_note: Optional[str] = None
    
    @validator('service_id', 'service_provider_id')
    def validate_ids(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('ID不能为空')
        return v.strip()

# 订单更新请求
class OrderUpdateRequest(BaseModel):
    """订单更新请求，只允许更新部分字段"""
    customer_note: Optional[str] = None
    admin_note: Optional[str] = None
    
    @validator('customer_note', 'admin_note')
    def validate_notes(cls, v):
        if v and len(v) > 1000:
            raise ValueError('备注不能超过1000个字符')
        return v

# 取消订单请求
class CancelOrderRequest(BaseModel):
    reason: str
    refund_requested: bool = False  # 是否申请退款
    
    @validator('reason')
    def validate_reason(cls, v):
        if not v or len(v.strip()) < 5:
            raise ValueError('取消原因至少需要5个字符')
        if len(v) > 500:
            raise ValueError('取消原因不能超过500个字符')
        return v.strip()

# 高级搜索请求
class OrderSearchRequest(BaseModel):
    keyword: Optional[str] = None  # 搜索关键词
    order_types: Optional[List[str]] = None
    order_statuses: Optional[List[str]] = None
    min_amount: Optional[Decimal] = None
    max_amount: Optional[Decimal] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    has_coupon: Optional[bool] = None
    page: int = Field(1, ge=1)
    size: int = Field(20, ge=1, le=100)
    
    @validator('keyword')
    def validate_keyword(cls, v):
        if v and len(v.strip()) < 2:
            raise ValueError('搜索关键词至少需要2个字符')
        return v.strip() if v else v

# 订单汇总统计
class OrderSummary(BaseModel):
    total_count: int
    total_amount: Decimal
    pending_count: int
    paid_count: int
    completed_count: int
    cancelled_count: int
    
# 分页信息
class PaginationInfo(BaseModel):
    page: int
    size: int
    total: int
    pages: int
    has_next: bool
    has_prev: bool

# 通用响应模式
class OrderApiResponse(BaseModel):
    success: bool
    data: Optional[Any] = None
    message: str
