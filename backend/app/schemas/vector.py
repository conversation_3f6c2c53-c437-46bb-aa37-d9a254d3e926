from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime


# ========== 向量数据库健康检查 ==========

class VectorHealthResponse(BaseModel):
    status: str = Field(..., description="健康状态: healthy/unhealthy/error")
    message: str = Field(..., description="状态描述")
    use_mock: bool = Field(..., description="是否使用模拟客户端")


# ========== 向量集合管理 ==========

class VectorCollectionCreateRequest(BaseModel):
    name: str = Field(..., description="集合名称", min_length=1, max_length=100)
    description: Optional[str] = Field(None, description="集合描述", max_length=500)
    dimension: int = Field(1536, description="向量维度", ge=1, le=4096)


class VectorCollectionResponse(BaseModel):
    name: str = Field(..., description="集合名称")
    description: str = Field(..., description="集合描述")
    dimension: int = Field(..., description="向量维度")
    vector_count: int = Field(..., description="向量数量")
    created: bool = Field(..., description="是否创建成功")


class VectorCollectionListResponse(BaseModel):
    collections: List[VectorCollectionResponse] = Field(..., description="集合列表")
    total: int = Field(..., description="总数量")


# ========== 向量数据操作 ==========

class VectorData(BaseModel):
    id: Optional[str] = Field(None, description="向量ID，不提供则自动生成")
    vector: List[float] = Field(..., description="向量数据")
    text: Optional[str] = Field(None, description="关联的文本内容")
    metadata: Optional[Dict[str, Any]] = Field(None, description="元数据")


class VectorInsertRequest(BaseModel):
    vectors: List[VectorData] = Field(..., description="要插入的向量数据列表", min_items=1)


class VectorInsertResponse(BaseModel):
    inserted_count: int = Field(..., description="成功插入的向量数量")
    inserted_ids: List[str] = Field(..., description="插入的向量ID列表")


# ========== 向量搜索 ==========

class VectorSearchRequest(BaseModel):
    query_vector: List[float] = Field(..., description="查询向量")
    top_k: int = Field(5, description="返回最相似的K个结果", ge=1, le=100)
    include_metadata: bool = Field(True, description="是否包含元数据")
    index_name: Optional[str] = Field("default_index", description="索引名称")


class VectorSearchResult(BaseModel):
    id: str = Field(..., description="向量ID")
    score: float = Field(..., description="相似度分数")
    text: Optional[str] = Field(None, description="关联的文本内容")
    metadata: Optional[Dict[str, Any]] = Field(None, description="元数据")


class VectorSearchResponse(BaseModel):
    results: List[VectorSearchResult] = Field(..., description="搜索结果")
    total: int = Field(..., description="结果总数")


# ========== 向量删除 ==========

class VectorDeleteRequest(BaseModel):
    vector_ids: List[str] = Field(..., description="要删除的向量ID列表", min_items=1)


class VectorDeleteResponse(BaseModel):
    deleted_count: int = Field(..., description="成功删除的向量数量")
    message: str = Field(..., description="操作结果描述")


# ========== 集合信息 ==========

class VectorCollectionInfo(BaseModel):
    name: str = Field(..., description="集合名称")
    description: str = Field(..., description="集合描述")
    dimension: int = Field(..., description="向量维度")
    vector_count: int = Field(..., description="向量数量")
    created_at: Optional[str] = Field(None, description="创建时间")
    updated_at: Optional[str] = Field(None, description="更新时间")


# ========== 通用响应 ==========

class VectorApiResponse(BaseModel):
    success: bool = Field(..., description="操作是否成功")
    message: str = Field(..., description="响应消息")
    data: Optional[Any] = Field(None, description="响应数据")


# ========== 向量生成 ==========

class VectorGenerateRequest(BaseModel):
    texts: List[str] = Field(..., description="要生成向量的文本列表", min_items=1)
    model: Optional[str] = Field("text-embedding-ada-002", description="使用的embedding模型")


class VectorGenerateResponse(BaseModel):
    embeddings: List[List[float]] = Field(..., description="生成的向量列表")
    model: str = Field(..., description="使用的模型")
    total: int = Field(..., description="生成的向量数量")


# ========== 批量操作 ==========

class VectorBatchInsertRequest(BaseModel):
    collection_name: str = Field(..., description="目标集合名称")
    vectors: List[VectorData] = Field(..., description="向量数据列表", min_items=1, max_items=1000)
    batch_size: int = Field(100, description="批次大小", ge=1, le=1000)


class VectorBatchInsertResponse(BaseModel):
    total_inserted: int = Field(..., description="总插入数量")
    batch_results: List[Dict[str, Any]] = Field(..., description="批次结果详情")
    success_rate: float = Field(..., description="成功率")


# ========== 向量统计 ==========

class VectorCollectionStats(BaseModel):
    collection_name: str = Field(..., description="集合名称")
    vector_count: int = Field(..., description="向量总数")
    dimension: int = Field(..., description="向量维度")
    storage_size: Optional[int] = Field(None, description="存储大小(字节)")
    last_updated: Optional[datetime] = Field(None, description="最后更新时间")


class VectorUserStats(BaseModel):
    user_id: str = Field(..., description="用户ID")
    total_collections: int = Field(..., description="总集合数")
    total_vectors: int = Field(..., description="总向量数")
    total_storage: Optional[int] = Field(None, description="总存储大小(字节)")
    collections: List[VectorCollectionStats] = Field(..., description="集合统计详情")


# ========== 索引管理 ==========

class IndexInfo(BaseModel):
    """索引信息"""
    name: str = Field(..., description="索引名称")
    full_name: str = Field(..., description="完整索引名称")
    status: str = Field(..., description="索引状态")
    created_at: Optional[str] = Field(None, description="创建时间")


class IndexCreateRequest(BaseModel):
    """创建索引请求"""
    index_name: str = Field(..., description="索引名称")
    vector_field: str = Field("vector", description="向量字段名")
    index_type: str = Field("hnsw", description="索引类型")
    metric_type: str = Field("cosine", description="距离度量类型")


class IndexListResponse(BaseModel):
    """索引列表响应"""
    success: bool = Field(..., description="是否成功")
    indexes: List[IndexInfo] = Field(..., description="索引列表")
    total: int = Field(..., description="索引总数")
