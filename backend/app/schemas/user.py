from pydantic import BaseModel, EmailStr, validator
from typing import Optional, List, Dict, Any
from datetime import datetime
from decimal import Decimal

class UserUpdateRequest(BaseModel):
    full_name: Optional[str] = None
    phone: Optional[str] = None
    
    @validator('full_name')
    def validate_full_name(cls, v):
        if v is not None and v.strip():  # 只有当姓名不为空时才验证长度
            name_cleaned = v.strip()
            if len(name_cleaned) < 2 or len(name_cleaned) > 50:
                raise ValueError('姓名长度必须在2-50个字符之间')
            return name_cleaned
        return None if not v or not v.strip() else v
    
    @validator('phone')
    def validate_phone(cls, v):
        if v is not None and v.strip():  # 只有当手机号不为空时才验证
            import re
            phone_cleaned = v.strip()
            if not re.match(r'^1[3-9]\d{9}$', phone_cleaned):
                raise ValueError('手机号格式不正确，请输入有效的中国大陆手机号')
            return phone_cleaned
        return None if not v or not v.strip() else v

class PasswordChangeRequest(BaseModel):
    current_password: str
    new_password: str
    confirm_password: str
    
    @validator('new_password')
    def validate_new_password(cls, v):
        if len(v) < 8 or len(v) > 20:
            raise ValueError('密码长度必须在8-20位之间')
        if not any(c.isalpha() for c in v) or not any(c.isdigit() for c in v):
            raise ValueError('密码必须包含字母和数字')
        return v
    
    @validator('confirm_password')
    def passwords_match(cls, v, values):
        if 'new_password' in values and v != values['new_password']:
            raise ValueError('新密码和确认密码不匹配')
        return v


class UserListQuery(BaseModel):
    page: int = 1
    size: int = 20
    role: Optional[str] = None
    status: Optional[str] = None
    search: Optional[str] = None
    sort_by: str = "created_at"
    sort_order: str = "desc"
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    
    @validator('page')
    def validate_page(cls, v):
        if v < 1:
            raise ValueError('页码必须大于0')
        return v
    
    @validator('size')
    def validate_size(cls, v):
        if v < 1 or v > 100:
            raise ValueError('每页数量必须在1-100之间')
        return v
    
    @validator('sort_order')
    def validate_sort_order(cls, v):
        if v not in ['asc', 'desc']:
            raise ValueError('排序方式必须是asc或desc')
        return v

class UserStatusUpdateRequest(BaseModel):
    status: str  # active/inactive/banned
    reason: str
    notify_user: bool = True
    
    @validator('status')
    def validate_status(cls, v):
        allowed_statuses = ['active', 'inactive', 'banned']
        if v not in allowed_statuses:
            raise ValueError(f'状态必须是以下之一: {", ".join(allowed_statuses)}')
        return v
    
    @validator('reason')
    def validate_reason(cls, v):
        if len(v.strip()) < 5:
            raise ValueError('状态变更原因至少5个字符')
        return v.strip()

class AdminCreateUserRequest(BaseModel):
    email: EmailStr
    full_name: str
    phone: Optional[str] = None
    password: Optional[str] = None
    role: str
    status: str = "active"
    send_notification: bool = True
    force_password_reset: bool = True
    
    @validator('full_name')
    def validate_full_name(cls, v):
        if len(v.strip()) < 2 or len(v.strip()) > 50:
            raise ValueError('姓名长度必须在2-50个字符之间')
        return v.strip()
    
    @validator('phone')
    def validate_phone(cls, v):
        if v is not None and v.strip():  # 只有当手机号不为空时才验证
            import re
            phone_cleaned = v.strip()
            if not re.match(r'^1[3-9]\d{9}$', phone_cleaned):
                raise ValueError('手机号格式不正确，请输入有效的中国大陆手机号')
            return phone_cleaned
        return None if not v or not v.strip() else v

    @validator('password')
    def validate_password(cls, v):
        if v is not None and v.strip():  # 只有当密码不为空时才验证
            password_cleaned = v.strip()
            if len(password_cleaned) < 8 or len(password_cleaned) > 20:
                raise ValueError('密码长度必须在8-20位之间')
            if not any(c.isalpha() for c in password_cleaned) or not any(c.isdigit() for c in password_cleaned):
                raise ValueError('密码必须包含字母和数字')
            return password_cleaned
        return None if not v or not v.strip() else v
    
    @validator('role')
    def validate_role(cls, v):
        allowed_roles = ['regular_user', 'enterprise_user', 'channel_user', 'agent_user', 'admin']
        if v not in allowed_roles:
            raise ValueError(f'角色必须是以下之一: {", ".join(allowed_roles)}')
        return v

class UserRoleUpdateRequest(BaseModel):
    roles: List[str]
    reason: str
    effective_immediately: bool = True
    notify_user: bool = True
    
    @validator('roles')
    def validate_roles(cls, v):
        if not v:
            raise ValueError('至少需要分配一个角色')
        allowed_roles = ['regular_user', 'enterprise_user', 'channel_user', 'agent_user', 'admin', 'super_admin']
        for role in v:
            if role not in allowed_roles:
                raise ValueError(f'角色 {role} 不在允许的角色列表中')
        return v
    
    @validator('reason')
    def validate_reason(cls, v):
        if len(v.strip()) < 5:
            raise ValueError('角色变更原因至少5个字符')
        return v.strip()

# 响应模式
class UserStatisticsResponse(BaseModel):
    total_orders: int = 0
    total_spent: Decimal = Decimal('0.00')
    active_projects: int = 0
    referral_count: int = 0
    login_count: int = 0

class UserRoleInfo(BaseModel):
    role: str
    is_active: bool
    created_at: datetime

class UserDetailResponse(BaseModel):
    id: str
    email: str
    full_name: str
    phone: Optional[str]
    status: str
    email_verified: bool
    phone_verified: bool = False
    referral_code: str
    roles: List[UserRoleInfo]
    statistics: UserStatisticsResponse
    created_at: datetime
    last_login_at: Optional[datetime]
    profile_completed: bool

class UserListResponse(BaseModel):
    users: List[UserDetailResponse]
    total: int
    page: int
    size: int
    pages: int

class UserResponse(BaseModel):
    success: bool
    data: Dict[str, Any]
    message: str

class UserListQueryResponse(BaseModel):
    success: bool
    data: UserListResponse

class UserRoleBindingResponse(BaseModel):
    user_id: str
    current_roles: List[UserRoleInfo]
    role_history: List[Dict[str, Any]]
    permissions: List[str]
