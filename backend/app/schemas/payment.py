"""
支付管理模块 Pydantic Schemas
"""
from pydantic import BaseModel, Field, UUID4, validator
from typing import Optional, List, Dict, Any
from datetime import datetime
from decimal import Decimal
from enum import Enum


# ========== 枚举类型 ==========
class PaymentMethodEnum(str, Enum):
    WECHAT = "wechat"
    ALIPAY = "alipay"
    OFFLINE = "offline"
    BALANCE = "balance"
    BANK_TRANSFER = "bank_transfer"


class PaymentStatusEnum(str, Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    SUCCESS = "success"
    FAILED = "failed"
    REFUNDED = "refunded"
    PARTIAL_REFUNDED = "partial_refunded"


class RefundStatusEnum(str, Enum):
    PENDING = "pending"
    APPROVED = "approved"
    PROCESSING = "processing"
    SUCCESS = "success"
    FAILED = "failed"
    CANCELLED = "cancelled"


# ========== 支付相关 Schemas ==========
class PaymentCreateSchema(BaseModel):
    """创建支付记录"""
    order_id: UUID4
    payment_method: PaymentMethodEnum
    amount: Decimal = Field(..., gt=0, decimal_places=2)
    
    @validator('amount')
    def validate_amount(cls, v):
        if v <= 0:
            raise ValueError('支付金额必须大于0')
        return v
    
    class Config:
        json_encoders = {
            Decimal: lambda v: float(v)
        }


class PaymentUpdateSchema(BaseModel):
    """更新支付信息"""
    payment_status: Optional[PaymentStatusEnum] = None
    transaction_no: Optional[str] = None
    paid_at: Optional[datetime] = None
    refunded_at: Optional[datetime] = None


class PaymentResponseSchema(BaseModel):
    """支付信息响应"""
    id: UUID4
    order_id: UUID4
    payment_method: str
    payment_status: str
    amount: Decimal
    transaction_no: Optional[str] = None
    paid_at: Optional[datetime] = None
    refunded_at: Optional[datetime] = None
    created_at: datetime
    
    # 扩展信息
    order_no: Optional[str] = None  # 订单号
    prepay_params: Optional[Dict[str, Any]] = None  # 支付参数（如微信prepay_id）
    
    class Config:
        from_attributes = True
        json_encoders = {
            Decimal: lambda v: float(v),
            datetime: lambda v: v.isoformat() if v else None
        }


class PaymentListResponseSchema(BaseModel):
    """支付列表响应"""
    total: int
    page: int
    limit: int
    items: List[PaymentResponseSchema]


class PaymentConfirmSchema(BaseModel):
    """确认支付（线下支付）"""
    transaction_no: str = Field(..., description="交易凭证号")
    paid_at: Optional[datetime] = Field(None, description="支付时间")
    remark: Optional[str] = Field(None, description="备注")


class PaymentCallbackSchema(BaseModel):
    """支付回调数据"""
    payment_id: Optional[UUID4] = None
    order_id: Optional[UUID4] = None
    transaction_no: str
    payment_status: PaymentStatusEnum
    paid_at: datetime
    callback_data: Optional[Dict[str, Any]] = None


class OfflinePaymentSchema(BaseModel):
    """线下支付确认"""
    payment_id: UUID4
    transaction_no: str = Field(..., description="银行流水号或转账凭证")
    paid_at: datetime = Field(..., description="实际支付时间")
    remark: Optional[str] = Field(None, description="备注信息")


# ========== 退款相关 Schemas ==========
class RefundCreateSchema(BaseModel):
    """创建退款申请"""
    payment_id: Optional[UUID4] = None  # 支付ID
    order_id: Optional[UUID4] = None  # 订单ID（二选一）
    refund_amount: Decimal = Field(..., gt=0, decimal_places=2)
    refund_reason: str = Field(..., min_length=5, max_length=500)
    
    @validator('refund_amount')
    def validate_amount(cls, v):
        if v <= 0:
            raise ValueError('退款金额必须大于0')
        return v
    
    class Config:
        json_encoders = {
            Decimal: lambda v: float(v)
        }


class RefundApprovalSchema(BaseModel):
    """退款审批"""
    approve: bool = Field(..., description="是否批准")
    note: Optional[str] = Field(None, max_length=500, description="审批备注")


class RefundRejectionSchema(BaseModel):
    """拒绝退款"""
    reason: str = Field(..., min_length=5, max_length=500, description="拒绝原因")


class RefundResponseSchema(BaseModel):
    """退款信息响应"""
    id: UUID4
    payment_id: UUID4
    refund_no: str
    refund_amount: Decimal
    refund_reason: Optional[str] = None
    refund_status: str
    refund_method: Optional[str] = None
    operator_id: Optional[UUID4] = None
    approved_at: Optional[datetime] = None
    processed_at: Optional[datetime] = None
    created_at: datetime
    
    # 扩展信息
    payment_info: Optional[PaymentResponseSchema] = None
    
    class Config:
        from_attributes = True
        json_encoders = {
            Decimal: lambda v: float(v),
            datetime: lambda v: v.isoformat() if v else None
        }


class RefundListResponseSchema(BaseModel):
    """退款列表响应"""
    total: int
    page: int
    limit: int
    items: List[RefundResponseSchema]


# ========== 支付方式相关 ==========
class PaymentMethodSchema(BaseModel):
    """支付方式信息"""
    method: str
    name: str
    type: str  # online/offline
    is_active: bool
    fee_rate: float
    min_amount: float
    max_amount: float
    
    class Config:
        from_attributes = True


class PaymentMethodListSchema(BaseModel):
    """支付方式列表"""
    methods: List[PaymentMethodSchema]


# ========== 统计相关 ==========
class PaymentStatisticsSchema(BaseModel):
    """支付统计信息"""
    total_amount: Decimal
    total_count: int
    success_count: int
    failed_count: int
    refund_count: int
    refund_amount: Decimal
    
    class Config:
        json_encoders = {
            Decimal: lambda v: float(v)
        }