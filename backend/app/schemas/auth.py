from pydantic import BaseModel, EmailStr, validator
from typing import Optional, List
from datetime import datetime

class UserRegisterRequest(BaseModel):
    email: EmailStr
    password: str
    full_name: str
    phone: Optional[str] = None
    referral_code: Optional[str] = None
    verification_code: str
    
    @validator('password')
    def validate_password(cls, v):
        if len(v) < 8 or len(v) > 20:
            raise ValueError('密码长度必须在8-20位之间')
        if not any(c.isalpha() for c in v) or not any(c.isdigit() for c in v):
            raise ValueError('密码必须包含字母和数字')
        return v
    

class UserLoginRequest(BaseModel):
    email: EmailStr
    password: str

class SendVerificationCodeRequest(BaseModel):
    email: EmailStr
    code_type: str  # register/login/reset_password/bind_email
    captcha_token: Optional[str] = None
    
    @validator('code_type')
    def validate_code_type(cls, v):
        allowed_types = ['register', 'login', 'reset_password', 'bind_email']
        if v not in allowed_types:
            raise ValueError(f'验证码类型必须是以下之一: {", ".join(allowed_types)}')
        return v

class VerifyCodeRequest(BaseModel):
    email: EmailStr
    code: str
    code_type: str
    
    @validator('code')
    def validate_code(cls, v):
        if not v.isdigit() or len(v) != 6:
            raise ValueError('验证码必须是6位数字')
        return v

class RefreshTokenRequest(BaseModel):
    refresh_token: str

class ResetPasswordRequest(BaseModel):
    email: EmailStr
    new_password: str
    verification_token: str
    
    @validator('new_password')
    def validate_password(cls, v):
        if len(v) < 8 or len(v) > 20:
            raise ValueError('密码长度必须在8-20位之间')
        if not any(c.isalpha() for c in v) or not any(c.isdigit() for c in v):
            raise ValueError('密码必须包含字母和数字')
        return v

class LogoutRequest(BaseModel):
    logout_all_devices: bool = False

# 响应模式
class TokenResponse(BaseModel):
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int
    refresh_expires_in: int

class UserResponse(BaseModel):
    id: str
    email: str
    full_name: str
    phone: Optional[str]
    status: str
    email_verified: bool
    phone_verified: bool
    referral_code: str
    roles: List[str]
    created_at: datetime
    last_login_at: Optional[datetime]
    profile_completed: bool

class RegisterResponse(BaseModel):
    success: bool
    data: dict
    message: str

class LoginResponse(BaseModel):
    success: bool
    data: dict
    message: str

class VerificationCodeResponse(BaseModel):
    success: bool
    data: dict

class VerifyCodeResponse(BaseModel):
    success: bool
    data: dict
    message: str

class RefreshTokenResponse(BaseModel):
    success: bool
    data: TokenResponse

class LogoutResponse(BaseModel):
    success: bool
    data: dict
    message: str

class ResetPasswordResponse(BaseModel):
    success: bool
    data: dict
    message: str
