from pydantic import BaseModel, EmailStr, validator
from typing import Optional, List, Dict, Any
from datetime import datetime
from decimal import Decimal

class AgentProfileRequest(BaseModel):
    agent_name: str
    contact_phone: Optional[str] = None
    contact_email: EmailStr
    office_address: Optional[str] = None
    agent_description: Optional[str] = None
    service_regions: Optional[List[str]] = None
    specialties: Optional[List[str]] = None
    
    @validator('agent_name')
    def validate_agent_name(cls, v):
        if not v or len(v.strip()) < 2:
            raise ValueError('代理商名称至少需要2个字符')
        if len(v) > 200:
            raise ValueError('代理商名称不能超过200个字符')
        return v.strip()
    
    @validator('contact_phone')
    def validate_contact_phone(cls, v):
        if v and not v.replace('-', '').replace(' ', '').isdigit():
            raise ValueError('联系电话格式不正确')
        return v
    
    @validator('office_address')
    def validate_office_address(cls, v):
        if v and len(v.strip()) < 2:
            raise ValueError('办公地址至少需要2个字符')
        return v.strip() if v else v

class AgentUpdateRequest(BaseModel):
    contact_phone: Optional[str] = None
    contact_email: Optional[EmailStr] = None
    office_address: Optional[str] = None
    agent_description: Optional[str] = None
    service_regions: Optional[List[str]] = None
    specialties: Optional[List[str]] = None
    
    @validator('contact_phone')
    def validate_contact_phone(cls, v):
        if v and not v.replace('-', '').replace(' ', '').isdigit():
            raise ValueError('联系电话格式不正确')
        return v
    
    @validator('office_address')
    def validate_office_address(cls, v):
        if v and len(v.strip()) < 2:
            raise ValueError('办公地址至少需要2个字符')
        return v.strip() if v else v

class AgentListQuery(BaseModel):
    page: int = 1
    size: int = 20
    verification_status: Optional[str] = None
    agent_level: Optional[str] = None
    search: Optional[str] = None
    sort_by: str = "created_at"
    sort_order: str = "desc"

    @validator('page')
    def validate_page(cls, v):
        if v < 1:
            raise ValueError('页码必须大于0')
        return v

    @validator('size')
    def validate_size(cls, v):
        if v < 1 or v > 100:
            raise ValueError('每页数量必须在1-100之间')
        return v

    @validator('verification_status')
    def validate_verification_status(cls, v):
        if v and v not in ['pending', 'verified', 'rejected']:
            raise ValueError('审核状态必须是 pending、verified 或 rejected')
        return v

    @validator('agent_level')
    def validate_agent_level(cls, v):
        if v and v not in ['bronze', 'silver', 'gold', 'platinum', 'diamond']:
            raise ValueError('代理商等级必须是 bronze、silver、gold、platinum 或 diamond')
        return v

class ReferralListQuery(BaseModel):
    page: int = 1
    size: int = 20
    status: Optional[str] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    sort_by: str = "created_at"
    sort_order: str = "desc"

    @validator('page')
    def validate_page(cls, v):
        if v < 1:
            raise ValueError('页码必须大于0')
        return v

    @validator('size')
    def validate_size(cls, v):
        if v < 1 or v > 100:
            raise ValueError('每页数量必须在1-100之间')
        return v

    @validator('status')
    def validate_status(cls, v):
        if v and v not in ['active', 'inactive', 'pending']:
            raise ValueError('状态必须是 active、inactive 或 pending')
        return v
    
    @validator('sort_by')
    def validate_sort_by(cls, v):
        allowed_fields = ['created_at', 'updated_at', 'full_name', 'status']
        if v not in allowed_fields:
            raise ValueError(f'排序字段必须是以下之一: {", ".join(allowed_fields)}')
        return v
    
    @validator('sort_order')
    def validate_sort_order(cls, v):
        if v not in ['asc', 'desc']:
            raise ValueError('排序方式必须是 asc 或 desc')
        return v

class CommissionListQuery(BaseModel):
    page: int = 1
    size: int = 20
    status: Optional[str] = None  # pending/settled/cancelled
    order_type: Optional[str] = None  # subscription/content_service
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    
    @validator('page')
    def validate_page(cls, v):
        if v < 1:
            raise ValueError('页码必须大于0')
        return v
    
    @validator('size')
    def validate_size(cls, v):
        if v < 1 or v > 100:
            raise ValueError('每页数量必须在1-100之间')
        return v
    
    @validator('status')
    def validate_status(cls, v):
        if v and v not in ['pending', 'settled', 'cancelled']:
            raise ValueError('状态必须是 pending、settled 或 cancelled')
        return v
    
    @validator('order_type')
    def validate_order_type(cls, v):
        if v and v not in ['subscription', 'content_service']:
            raise ValueError('订单类型必须是 subscription 或 content_service')
        return v

class CommissionSettlementRequest(BaseModel):
    settlement_amount: Decimal
    bank_account: Dict[str, str]  # 收款账户信息
    note: Optional[str] = None
    
    @validator('settlement_amount')
    def validate_amount(cls, v):
        if v <= 0:
            raise ValueError('结算金额必须大于0')
        if v < 100:  # 最低结算金额100元
            raise ValueError('结算金额不能少于100元')
        if v > 999999.99:
            raise ValueError('结算金额不能超过999999.99元')
        return v
    
    @validator('bank_account')
    def validate_bank_account(cls, v):
        required_fields = ['account_name', 'bank_name', 'account_number']
        for field in required_fields:
            if field not in v or not v[field]:
                raise ValueError(f'银行账户信息缺少必填字段: {field}')
        
        # 验证账户名称
        if len(v['account_name']) < 2:
            raise ValueError('账户名称至少需要2个字符')
        
        # 验证银行名称
        if len(v['bank_name']) < 2:
            raise ValueError('银行名称至少需要2个字符')
        
        # 验证账户号码
        account_number = v['account_number'].replace(' ', '').replace('-', '')
        if not account_number.isdigit() or len(account_number) < 10:
            raise ValueError('银行账户号码格式不正确')
        
        return v
    
    @validator('note')
    def validate_note(cls, v):
        if v and len(v) > 500:
            raise ValueError('备注不能超过500个字符')
        return v

class AgentResponse(BaseModel):
    id: str
    agent_name: str
    agent_code: str
    agent_level: str
    contact_phone: Optional[str]
    contact_email: str
    office_address: Optional[str]
    agent_description: Optional[str]
    service_regions: Optional[List[str]]
    specialties: Optional[List[str]]
    verification_status: str
    verification_time: Optional[datetime]
    commission_config: Dict[str, Any]
    created_at: datetime
    updated_at: datetime

class ReferralResponse(BaseModel):
    id: str
    full_name: str
    phone: Optional[str]
    email: str
    user_type: str
    company_name: Optional[str]
    status: str
    total_orders: int
    total_spent: Decimal
    generated_commission: Decimal
    last_order_time: Optional[datetime]
    registered_at: datetime

class CommissionResponse(BaseModel):
    id: str
    order_no: str
    order_type: str
    product_name: Optional[str]
    customer_name: str
    customer_company: Optional[str]
    order_amount: Decimal
    commission_rate: Decimal
    commission_amount: Decimal
    status: str
    commission_date: datetime
    settlement_id: Optional[str]
    settled_at: Optional[datetime]

class CommissionSettlementResponse(BaseModel):
    settlement_id: str
    agent_id: str
    settlement_amount: Decimal
    commission_records_count: int
    status: str
    bank_account: Dict[str, str]
    applied_at: datetime
    expected_settlement_date: Optional[datetime]

# 审核请求模式
class AgentVerificationRequest(BaseModel):
    status: str  # verified/rejected
    note: str
    notify_user: bool = True

    @validator('status')
    def validate_status(cls, v):
        if v not in ['verified', 'rejected']:
            raise ValueError('审核状态必须是 verified 或 rejected')
        return v

    @validator('note')
    def validate_note(cls, v):
        if not v or len(v.strip()) < 5:
            raise ValueError('审核备注至少需要5个字符')
        if len(v) > 500:
            raise ValueError('审核备注不能超过500个字符')
        return v.strip()

# 通用响应模式
class AgentApiResponse(BaseModel):
    success: bool
    data: Optional[Any] = None
    message: str
