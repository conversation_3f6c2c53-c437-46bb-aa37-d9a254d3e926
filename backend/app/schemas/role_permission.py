from pydantic import BaseModel, validator
from typing import Optional, List, Dict, Any
from datetime import datetime


class RolePermissionBase(BaseModel):
    """角色权限基础模型"""
    role_id: str
    permission_id: str


class RolePermissionCreate(RolePermissionBase):
    """创建角色权限"""
    pass


class RolePermissionResponse(RolePermissionBase):
    """角色权限响应"""
    id: str
    role_name: str
    role_code: str
    permission_name: str
    permission_code: str
    granted_by: Optional[str] = None
    granted_by_name: Optional[str] = None
    granted_at: datetime

    class Config:
        from_attributes = True


class RolePermissionListResponse(BaseModel):
    """角色权限列表响应"""
    total: int
    items: List[RolePermissionResponse]
    page: int
    page_size: int


class RoleWithPermissions(BaseModel):
    """角色及其权限"""
    id: str
    role_name: str
    role_code: str
    role_type: str
    description: Optional[str] = None
    is_active: bool
    permissions: List[Dict[str, Any]]
    permission_count: int


class PermissionWithRoles(BaseModel):
    """权限及其角色"""
    id: str
    permission_name: str
    permission_code: str
    module: str
    action: str
    resource: str
    description: Optional[str] = None
    roles: List[Dict[str, Any]]
    role_count: int


class BatchRolePermissionRequest(BaseModel):
    """批量角色权限操作请求"""
    role_id: str
    permission_ids: List[str]
    action: str  # "add" 或 "remove"
    
    @validator('action')
    def validate_action(cls, v):
        if v not in ['add', 'remove']:
            raise ValueError('操作只能是 add 或 remove')
        return v
    
    @validator('permission_ids')
    def validate_permission_ids(cls, v):
        if not v:
            raise ValueError('权限ID列表不能为空')
        return v


class RolePermissionStats(BaseModel):
    """角色权限统计"""
    total_roles: int
    total_permissions: int
    total_assignments: int
    roles_with_permissions: List[Dict[str, Any]]
    permissions_with_roles: List[Dict[str, Any]]


class CopyRolePermissionsRequest(BaseModel):
    """复制角色权限请求"""
    source_role_id: str
    target_role_id: str
    overwrite: bool = False  # 是否覆盖目标角色现有权限
    
    @validator('source_role_id')
    def validate_source_role_id(cls, v):
        if not v:
            raise ValueError('源角色ID不能为空')
        return v
    
    @validator('target_role_id')
    def validate_target_role_id(cls, v):
        if not v:
            raise ValueError('目标角色ID不能为空')
        return v
