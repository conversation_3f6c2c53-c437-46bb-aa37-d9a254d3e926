from pydantic import BaseModel, EmailStr, validator
from typing import Optional, List, Dict, Any
from datetime import datetime
from decimal import Decimal

class ChannelProfileRequest(BaseModel):
    provider_name: str
    provider_type: str  # individual/company/studio/mcn
    
    # 个人信息（个人提供商）
    real_name: Optional[str] = None
    id_card_number: Optional[str] = None
    
    # 机构信息（公司/工作室/MCN）
    company_name: Optional[str] = None
    business_license: Optional[str] = None
    
    # 联系信息
    contact_phone: Optional[str] = None
    contact_email: EmailStr
    contact_address: Optional[str] = None
    
    # 业务信息
    business_description: Optional[str] = None
    service_categories: Optional[List[str]] = None
    platform_accounts: Optional[Dict[str, str]] = None
    portfolio_urls: Optional[List[str]] = None
    qualification_files: Optional[List[str]] = None
    
    @validator('provider_name')
    def validate_provider_name(cls, v):
        if not v or len(v.strip()) < 2:
            raise ValueError('提供商名称至少需要2个字符')
        if len(v) > 200:
            raise ValueError('提供商名称不能超过200个字符')
        return v.strip()
    
    @validator('provider_type')
    def validate_provider_type(cls, v):
        valid_types = ['individual', 'company', 'studio', 'mcn']
        if v not in valid_types:
            raise ValueError(f'提供商类型必须是以下之一: {", ".join(valid_types)}')
        return v
    
    @validator('real_name')
    def validate_real_name(cls, v, values):
        if values.get('provider_type') == 'individual':
            if not v or len(v.strip()) < 2:
                raise ValueError('个人提供商必须填写真实姓名，至少2个字符')
        return v.strip() if v else v
    
    @validator('company_name')
    def validate_company_name(cls, v, values):
        provider_type = values.get('provider_type')
        if provider_type in ['company', 'studio', 'mcn']:
            if not v or len(v.strip()) < 2:
                raise ValueError('机构提供商必须填写公司名称，至少2个字符')
        return v.strip() if v else v
    
    @validator('id_card_number')
    def validate_id_card(cls, v, values):
        if v and values.get('provider_type') == 'individual':
            if len(v) != 18:
                raise ValueError('身份证号码必须是18位')
            # 简单的身份证号码格式验证
            if not v[:17].isdigit():
                raise ValueError('身份证号码格式不正确')
        return v
    
    @validator('contact_phone')
    def validate_contact_phone(cls, v):
        if v and not v.replace('-', '').replace(' ', '').isdigit():
            raise ValueError('联系电话格式不正确')
        return v

class ChannelUpdateRequest(BaseModel):
    contact_phone: Optional[str] = None
    contact_email: Optional[EmailStr] = None
    contact_address: Optional[str] = None
    business_description: Optional[str] = None
    service_categories: Optional[List[str]] = None
    platform_accounts: Optional[Dict[str, str]] = None
    portfolio_urls: Optional[List[str]] = None
    qualification_files: Optional[List[str]] = None
    
    @validator('contact_phone')
    def validate_contact_phone(cls, v):
        if v and not v.replace('-', '').replace(' ', '').isdigit():
            raise ValueError('联系电话格式不正确')
        return v

class ChannelListQuery(BaseModel):
    page: int = 1
    size: int = 20
    provider_type: Optional[str] = None
    verification_status: Optional[str] = None
    platform_name: Optional[str] = None
    search: Optional[str] = None
    sort_by: str = "created_at"
    sort_order: str = "desc"
    
    @validator('page')
    def validate_page(cls, v):
        if v < 1:
            raise ValueError('页码必须大于0')
        return v
    
    @validator('size')
    def validate_size(cls, v):
        if v < 1 or v > 100:
            raise ValueError('每页数量必须在1-100之间')
        return v
    
    @validator('provider_type')
    def validate_provider_type(cls, v):
        if v and v not in ['individual', 'company', 'studio', 'mcn']:
            raise ValueError('提供商类型必须是 individual、company、studio 或 mcn')
        return v
    
    @validator('verification_status')
    def validate_verification_status(cls, v):
        if v and v not in ['pending', 'verified', 'rejected']:
            raise ValueError('审核状态必须是 pending、verified 或 rejected')
        return v
    
    @validator('sort_by')
    def validate_sort_by(cls, v):
        allowed_fields = ['created_at', 'updated_at', 'provider_name', 'verification_status', 'service_rating']
        if v not in allowed_fields:
            raise ValueError(f'排序字段必须是以下之一: {", ".join(allowed_fields)}')
        return v
    
    @validator('sort_order')
    def validate_sort_order(cls, v):
        if v not in ['asc', 'desc']:
            raise ValueError('排序方式必须是 asc 或 desc')
        return v

class ChannelVerificationRequest(BaseModel):
    status: str  # verified/rejected
    note: str
    notify_user: bool = True
    
    @validator('status')
    def validate_status(cls, v):
        if v not in ['verified', 'rejected']:
            raise ValueError('审核状态必须是 verified 或 rejected')
        return v
    
    @validator('note')
    def validate_note(cls, v):
        if not v or len(v.strip()) < 5:
            raise ValueError('审核备注至少需要5个字符')
        if len(v) > 500:
            raise ValueError('审核备注不能超过500个字符')
        return v.strip()

class ChannelResponse(BaseModel):
    id: str
    provider_name: str
    provider_type: str
    real_name: Optional[str]
    company_name: Optional[str]
    contact_phone: Optional[str]
    contact_email: str
    contact_address: Optional[str]
    business_description: Optional[str]
    service_categories: Optional[List[str]]
    platform_accounts: Optional[Dict[str, str]]
    portfolio_urls: Optional[List[str]]
    qualification_files: Optional[List[str]]  # 资质文件
    verification_status: str
    verification_time: Optional[datetime]
    verification_note: Optional[str]
    is_active: bool
    service_rating: Optional[Decimal]
    completed_orders: int
    created_at: datetime
    updated_at: datetime

class ChannelListResponse(BaseModel):
    items: List[ChannelResponse]
    pagination: Dict[str, int]

class ChannelVerificationResponse(BaseModel):
    provider_id: str
    provider_name: str
    old_status: str
    new_status: str
    verification_note: str
    verifier: str
    verification_time: datetime

# 可用渠道查询模型
class AvailableChannelQuery(BaseModel):
    page: int = 1
    size: int = 20
    platform_type: Optional[str] = None
    price_min: Optional[float] = None
    price_max: Optional[float] = None
    content_type: Optional[str] = None

    @validator('page')
    def validate_page(cls, v):
        if v < 1:
            raise ValueError('页码必须大于0')
        return v

    @validator('size')
    def validate_size(cls, v):
        if v < 1 or v > 100:
            raise ValueError('每页数量必须在1-100之间')
        return v

    @validator('platform_type')
    def validate_platform_type(cls, v):
        if v and v not in ['wechat', 'weibo', 'douyin', 'xiaohongshu']:
            raise ValueError('平台类型必须是 wechat、weibo、douyin 或 xiaohongshu')
        return v

    @validator('content_type')
    def validate_content_type(cls, v):
        if v and v not in ['article', 'video', 'image']:
            raise ValueError('内容类型必须是 article、video 或 image')
        return v

    @validator('price_min')
    def validate_price_min(cls, v):
        if v is not None and v < 0:
            raise ValueError('最低价格不能小于0')
        return v

    @validator('price_max')
    def validate_price_max(cls, v, values):
        if v is not None:
            if v < 0:
                raise ValueError('最高价格不能小于0')
            price_min = values.get('price_min')
            if price_min is not None and v < price_min:
                raise ValueError('最高价格不能小于最低价格')
        return v

# 收益查询模型
class EarningsQuery(BaseModel):
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    page: int = 1
    size: int = 20

    @validator('page')
    def validate_page(cls, v):
        if v < 1:
            raise ValueError('页码必须大于0')
        return v

    @validator('size')
    def validate_size(cls, v):
        if v < 1 or v > 100:
            raise ValueError('每页数量必须在1-100之间')
        return v

    @validator('end_date')
    def validate_end_date(cls, v, values):
        if v and values.get('start_date') and v < values['start_date']:
            raise ValueError('结束日期不能早于开始日期')
        return v

# 提现申请模型
class WithdrawRequest(BaseModel):
    amount: Decimal
    bank_account: str
    bank_name: str
    account_holder: str
    withdraw_reason: Optional[str] = None

    @validator('amount')
    def validate_amount(cls, v):
        if v <= 0:
            raise ValueError('提现金额必须大于0')
        if v < 100:
            raise ValueError('最低提现金额为100元')
        if v > 100000:
            raise ValueError('单次提现金额不能超过10万元')
        return v

    @validator('bank_account')
    def validate_bank_account(cls, v):
        if not v or len(v.strip()) < 10:
            raise ValueError('银行账号至少需要10位数字')
        if not v.replace(' ', '').isdigit():
            raise ValueError('银行账号只能包含数字和空格')
        return v.strip()

    @validator('bank_name')
    def validate_bank_name(cls, v):
        if not v or len(v.strip()) < 2:
            raise ValueError('银行名称至少需要2个字符')
        if len(v) > 100:
            raise ValueError('银行名称不能超过100个字符')
        return v.strip()

    @validator('account_holder')
    def validate_account_holder(cls, v):
        if not v or len(v.strip()) < 2:
            raise ValueError('账户持有人姓名至少需要2个字符')
        if len(v) > 50:
            raise ValueError('账户持有人姓名不能超过50个字符')
        return v.strip()

    @validator('withdraw_reason')
    def validate_withdraw_reason(cls, v):
        if v and len(v) > 200:
            raise ValueError('提现说明不能超过200个字符')
        return v.strip() if v else v

# 提现记录查询模型
class WithdrawQuery(BaseModel):
    status: Optional[str] = None
    page: int = 1
    size: int = 20

    @validator('page')
    def validate_page(cls, v):
        if v < 1:
            raise ValueError('页码必须大于0')
        return v

    @validator('size')
    def validate_size(cls, v):
        if v < 1 or v > 100:
            raise ValueError('每页数量必须在1-100之间')
        return v

    @validator('status')
    def validate_status(cls, v):
        if v and v not in ['pending', 'processing', 'completed', 'failed']:
            raise ValueError('状态必须是 pending、processing、completed 或 failed')
        return v

# 可用渠道响应模型
class AvailableChannelResponse(BaseModel):
    provider_id: str
    provider_name: str
    platform_name: str
    platform_type: str
    content_types: List[str]
    fixed_price: Decimal
    followers_count: Optional[int]
    rating: Optional[Decimal]
    completed_orders: int
    description: Optional[str]
    specialties: Optional[List[str]]
    response_time: Optional[str]
    delivery_time: Optional[str]

# 收益统计响应模型
class EarningsResponse(BaseModel):
    summary: Dict[str, Any]
    earnings_trend: List[Dict[str, Any]]
    earnings_details: List[Dict[str, Any]]
    pagination: Dict[str, int]

# 提现申请响应模型
class WithdrawResponse(BaseModel):
    withdraw_id: str
    amount: Decimal
    status: str
    applied_at: datetime
    estimated_arrival: Optional[datetime]

# 提现记录响应模型
class WithdrawRecordResponse(BaseModel):
    withdraw_id: str
    amount: Decimal
    bank_account: str
    bank_name: str
    status: str
    applied_at: datetime
    processed_at: Optional[datetime]
    completed_at: Optional[datetime]

# 通用响应模式
class ChannelApiResponse(BaseModel):
    success: bool
    data: Optional[Any] = None
    message: str
