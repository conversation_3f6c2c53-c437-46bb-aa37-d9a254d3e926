from pydantic import BaseModel, HttpUrl, validator
from typing import Optional, List, Dict, Any
from datetime import datetime
from decimal import Decimal

class MonitoringProjectCreate(BaseModel):
    project_name: str
    project_description: Optional[str] = None
    target_website: str  # 改为str类型，避免HttpUrl验证问题
    target_brand: str
    keywords: List[str]
    search_engines: List[str]
    monitoring_frequency: str = "daily"
    competitors: Optional[List[Dict[str, str]]] = None
    
    @validator('project_name')
    def validate_project_name(cls, v):
        if not v or len(v.strip()) < 2:
            raise ValueError('项目名称至少需要2个字符')
        if len(v) > 200:
            raise ValueError('项目名称不能超过200个字符')
        return v.strip()
    
    @validator('target_website')
    def validate_target_website(cls, v):
        if not v or len(v.strip()) < 5:
            raise ValueError('目标网站至少需要5个字符')
        return v.strip()
    
    @validator('target_brand')
    def validate_target_brand(cls, v):
        if not v or len(v.strip()) < 2:
            raise ValueError('目标品牌至少需要2个字符')
        return v.strip()
    
    @validator('keywords')
    def validate_keywords(cls, v):
        if not v or len(v) == 0:
            raise ValueError('至少需要一个监控关键词')
        if len(v) > 50:
            raise ValueError('关键词数量不能超过50个')
        # 验证每个关键词
        for keyword in v:
            if not keyword or len(keyword.strip()) < 2:
                raise ValueError('关键词至少需要2个字符')
        return [keyword.strip() for keyword in v]
    
    @validator('search_engines')
    def validate_search_engines(cls, v):
        if not v or len(v) == 0:
            raise ValueError('至少需要选择一个搜索引擎')
        valid_engines = ['doubao', 'chatgpt', 'claude', 'gemini', 'tongyi', 'wenxin']
        for engine in v:
            if engine not in valid_engines:
                raise ValueError(f'不支持的搜索引擎: {engine}')
        return v
    
    @validator('monitoring_frequency')
    def validate_frequency(cls, v):
        if v not in ['daily', 'weekly', 'monthly']:
            raise ValueError('监控频率必须是 daily, weekly 或 monthly')
        return v

class ProjectListQuery(BaseModel):
    page: int = 1
    size: int = 20
    status: Optional[str] = None
    keyword: Optional[str] = None
    sort_by: str = "created_at"
    sort_order: str = "desc"
    
    @validator('page')
    def validate_page(cls, v):
        if v < 1:
            raise ValueError('页码必须大于0')
        return v
    
    @validator('size')
    def validate_size(cls, v):
        if v < 1 or v > 100:
            raise ValueError('每页数量必须在1-100之间')
        return v
    
    @validator('status')
    def validate_status(cls, v):
        if v and v not in ['active', 'paused', 'completed', 'cancelled']:
            raise ValueError('状态必须是 active、paused、completed 或 cancelled')
        return v
    
    @validator('sort_by')
    def validate_sort_by(cls, v):
        allowed_fields = ['created_at', 'updated_at', 'project_name', 'status', 'avg_ranking']
        if v not in allowed_fields:
            raise ValueError(f'排序字段必须是以下之一: {", ".join(allowed_fields)}')
        return v
    
    @validator('sort_order')
    def validate_sort_order(cls, v):
        if v not in ['asc', 'desc']:
            raise ValueError('排序方式必须是 asc 或 desc')
        return v

class RankingQuery(BaseModel):
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    keyword: Optional[str] = None
    search_engine: Optional[str] = None
    page: int = 1
    size: int = 50
    
    @validator('page')
    def validate_page(cls, v):
        if v < 1:
            raise ValueError('页码必须大于0')
        return v
    
    @validator('size')
    def validate_size(cls, v):
        if v < 1 or v > 100:
            raise ValueError('每页数量必须在1-100之间')
        return v
    
    @validator('search_engine')
    def validate_search_engine(cls, v):
        if v and v not in ['doubao', 'chatgpt', 'claude', 'gemini', 'tongyi', 'wenxin']:
            raise ValueError('不支持的搜索引擎')
        return v
    
    @validator('end_date')
    def validate_date_range(cls, v, values):
        if v and values.get('start_date') and v < values['start_date']:
            raise ValueError('结束日期不能早于开始日期')
        return v

class MonitoringProjectResponse(BaseModel):
    id: str
    project_name: str
    project_description: Optional[str]
    target_website: str
    target_brand: str
    keywords: List[str]
    search_engines: List[str]
    monitoring_frequency: str
    competitors: Optional[List[Dict[str, str]]]
    status: str
    total_keywords: int
    total_rankings: int
    avg_ranking: Optional[Decimal]
    best_ranking: Optional[int]
    created_at: datetime
    updated_at: datetime
    last_monitored_at: Optional[datetime]
    next_monitor_at: Optional[datetime]

class RankingRecordResponse(BaseModel):
    id: str
    keyword: str
    search_engine: str
    search_query: str
    ranking_position: Optional[int]
    total_results: Optional[int]
    result_title: Optional[str]
    result_url: Optional[str]
    result_snippet: Optional[str]
    result_score: Optional[Decimal]
    competitor_rankings: Optional[Dict[str, Any]]
    monitored_at: datetime

class KeywordTrendResponse(BaseModel):
    keyword: str
    date: datetime
    avg_ranking: Optional[Decimal]
    best_ranking: Optional[int]
    worst_ranking: Optional[int]
    total_mentions: int
    engine_rankings: Optional[Dict[str, Any]]
    ranking_change: Optional[int]
    trend_direction: Optional[str]

class ProjectStatisticsResponse(BaseModel):
    total_keywords: int
    total_rankings: int
    avg_ranking: Optional[Decimal]
    best_ranking: Optional[int]
    ranking_distribution: Dict[str, int]  # 排名分布
    engine_performance: Dict[str, Any]    # 各搜索引擎表现
    trend_summary: Dict[str, Any]         # 趋势汇总
    recent_changes: List[Dict[str, Any]]  # 最近变化

class ProjectListResponse(BaseModel):
    items: List[MonitoringProjectResponse]
    pagination: Dict[str, int]
    statistics: Dict[str, Any]

class RankingListResponse(BaseModel):
    items: List[RankingRecordResponse]
    pagination: Dict[str, int]
    trends: List[KeywordTrendResponse]
    statistics: ProjectStatisticsResponse

class MonitoringProjectCreateResponse(BaseModel):
    id: str
    project_name: str
    target_website: str
    target_brand: str
    keywords: List[str]
    search_engines: List[str]
    monitoring_frequency: str
    status: str
    total_keywords: int
    created_at: datetime
    next_monitor_at: Optional[datetime]

# 通用响应模式
class MonitoringApiResponse(BaseModel):
    success: bool
    data: Optional[Any] = None
    message: str
