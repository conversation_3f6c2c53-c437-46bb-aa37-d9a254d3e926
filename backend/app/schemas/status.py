"""
状态管理Pydantic模型
"""
from typing import Optional, Dict, Any, List
from datetime import datetime
from uuid import UUID
from pydantic import BaseModel, Field, ConfigDict
from enum import Enum


# 订单状态枚举
class OrderStatusEnum(str, Enum):
    PENDING = "pending"           # 待处理/待支付
    PAID = "paid"                # 已支付
    PROCESSING = "processing"     # 处理中
    DELIVERING = "delivering"     # 配送中
    COMPLETED = "completed"       # 已完成
    CANCELLED = "cancelled"       # 已取消
    REFUNDING = "refunding"      # 退款中
    REFUNDED = "refunded"        # 已退款


# 状态信息
ORDER_STATUS_INFO = {
    "pending": {
        "name": "待处理",
        "color": "#FFA500",
        "icon": "clock",
        "description": "订单已创建，等待支付"
    },
    "paid": {
        "name": "已支付",
        "color": "#4CAF50",
        "icon": "check-circle",
        "description": "订单已支付，等待处理"
    },
    "processing": {
        "name": "处理中",
        "color": "#2196F3",
        "icon": "settings",
        "description": "订单正在处理中"
    },
    "delivering": {
        "name": "配送中",
        "color": "#9C27B0",
        "icon": "local-shipping",
        "description": "订单正在配送中"
    },
    "completed": {
        "name": "已完成",
        "color": "#4CAF50",
        "icon": "done-all",
        "description": "订单已完成"
    },
    "cancelled": {
        "name": "已取消",
        "color": "#9E9E9E",
        "icon": "cancel",
        "description": "订单已取消"
    },
    "refunding": {
        "name": "退款中",
        "color": "#FF9800",
        "icon": "refresh",
        "description": "正在处理退款"
    },
    "refunded": {
        "name": "已退款",
        "color": "#F44336",
        "icon": "money-off",
        "description": "订单已退款"
    }
}


# 状态转换规则
ORDER_STATUS_TRANSITIONS = {
    "pending": ["paid", "cancelled"],
    "paid": ["processing", "refunding", "cancelled"],
    "processing": ["delivering", "completed", "cancelled"],
    "delivering": ["completed", "cancelled"],
    "completed": ["refunding"],
    "cancelled": [],
    "refunding": ["refunded", "cancelled"],
    "refunded": []
}


# 状态转换原因
TRANSITION_REASONS = {
    "pending_to_paid": ["用户完成支付", "管理员确认支付", "线下支付确认"],
    "pending_to_cancelled": ["用户取消", "超时自动取消", "库存不足", "管理员取消"],
    "paid_to_processing": ["开始处理订单", "自动开始处理"],
    "paid_to_refunding": ["用户申请退款", "质量问题退款", "管理员发起退款"],
    "paid_to_cancelled": ["用户取消", "商品缺货", "管理员取消"],
    "processing_to_delivering": ["开始配送", "已发货"],
    "processing_to_completed": ["处理完成", "服务已提供"],
    "processing_to_cancelled": ["处理失败", "管理员取消"],
    "delivering_to_completed": ["已送达", "用户确认收货"],
    "delivering_to_cancelled": ["配送失败", "用户拒收"],
    "completed_to_refunding": ["售后退款", "质量问题退款"],
    "refunding_to_refunded": ["退款成功"],
    "refunding_to_cancelled": ["退款失败", "撤销退款"]
}


# ========== 请求模型 ==========

class OrderStatusLogCreateSchema(BaseModel):
    """创建状态日志请求"""
    order_id: UUID = Field(..., description="订单ID")
    to_status: OrderStatusEnum = Field(..., description="目标状态")
    change_reason: Optional[str] = Field(None, description="变更原因")
    metadata: Optional[Dict[str, Any]] = Field(None, description="附加信息")


class StatusQuerySchema(BaseModel):
    """状态查询参数"""
    order_id: Optional[UUID] = Field(None, description="订单ID")
    from_status: Optional[str] = Field(None, description="原状态")
    to_status: Optional[str] = Field(None, description="目标状态")
    changed_by: Optional[UUID] = Field(None, description="变更人ID")
    start_date: Optional[datetime] = Field(None, description="开始时间")
    end_date: Optional[datetime] = Field(None, description="结束时间")


# ========== 响应模型 ==========

class OrderStatusLogSchema(BaseModel):
    """状态日志响应"""
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID
    order_id: UUID
    order_no: Optional[str] = Field(None, description="订单号")
    from_status: Optional[str] = Field(None, description="原状态")
    from_status_name: Optional[str] = Field(None, description="原状态名称")
    to_status: str = Field(..., description="目标状态")
    to_status_name: str = Field(..., description="目标状态名称")
    changed_by: Optional[UUID] = Field(None, description="变更人ID")
    changed_by_name: Optional[str] = Field(None, description="变更人姓名")
    change_reason: Optional[str] = Field(None, description="变更原因")
    metadata: Optional[Dict[str, Any]] = Field(None, description="附加信息")
    created_at: datetime = Field(..., description="创建时间")
    
    def __init__(self, **data):
        super().__init__(**data)
        # 自动填充状态名称
        if self.from_status:
            self.from_status_name = ORDER_STATUS_INFO.get(self.from_status, {}).get("name", self.from_status)
        self.to_status_name = ORDER_STATUS_INFO.get(self.to_status, {}).get("name", self.to_status)


class OrderStatusHistoryItemSchema(BaseModel):
    """状态历史项"""
    id: UUID
    from_status: Optional[str]
    from_status_name: Optional[str]
    to_status: str
    to_status_name: str
    changed_by: Optional[str]
    change_reason: Optional[str]
    duration_minutes: Optional[int] = Field(None, description="在该状态停留的分钟数")
    created_at: datetime
    metadata: Optional[Dict[str, Any]]


class OrderStatusHistorySchema(BaseModel):
    """订单状态历史响应"""
    order_id: UUID
    order_no: Optional[str]
    current_status: str
    current_status_name: str
    status_history: List[OrderStatusHistoryItemSchema]
    total_duration_hours: Optional[float] = Field(None, description="总耗时（小时）")


class StatusTransitionRuleSchema(BaseModel):
    """状态转换规则"""
    allowed_next: List[str] = Field(..., description="允许的下一状态")
    auto_transition: Optional[Dict[str, Any]] = Field(None, description="自动转换配置")
    requires_approval: bool = Field(False, description="是否需要审批")


class StatusInfoSchema(BaseModel):
    """状态信息"""
    name: str
    color: str
    icon: str
    description: str


class StatusTransitionsResponseSchema(BaseModel):
    """状态流转规则响应"""
    statuses: Dict[str, StatusInfoSchema]
    transitions: Dict[str, StatusTransitionRuleSchema]
    transition_reasons: Dict[str, List[str]]


class OrderStatusLogListSchema(BaseModel):
    """状态日志列表响应"""
    total: int
    page: int
    limit: int
    items: List[OrderStatusLogSchema]


class StatusStatisticsSchema(BaseModel):
    """状态统计信息"""
    status_distribution: Dict[str, int] = Field(..., description="各状态订单数量")
    avg_transition_time: Dict[str, float] = Field(..., description="平均状态转换时间（小时）")
    abnormal_transitions: int = Field(0, description="异常状态转换次数")
    total_orders: int = Field(0, description="总订单数")
    
    
class StatusChangeEventSchema(BaseModel):
    """状态变更事件"""
    order_id: UUID
    old_status: Optional[str]
    new_status: str
    changed_by: Optional[UUID]
    change_reason: Optional[str]
    timestamp: datetime
    metadata: Optional[Dict[str, Any]]