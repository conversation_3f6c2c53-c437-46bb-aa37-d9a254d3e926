from pydantic import BaseModel, validator
from typing import Optional, List, Dict, Any
from datetime import datetime

class FileUploadRequest(BaseModel):
    business_type: Optional[str] = None
    business_id: Optional[str] = None
    is_public: bool = False
    expires_hours: Optional[int] = None  # 过期时间（小时）
    
    @validator('business_type')
    def validate_business_type(cls, v):
        if v and len(v.strip()) < 2:
            raise ValueError('业务类型至少需要2个字符')
        return v.strip() if v else v
    
    @validator('business_id')
    def validate_business_id(cls, v):
        if v and len(v.strip()) < 1:
            raise ValueError('业务ID不能为空')
        return v.strip() if v else v
    
    @validator('expires_hours')
    def validate_expires_hours(cls, v):
        if v is not None and (v < 1 or v > 8760):  # 最长1年
            raise ValueError('过期时间必须在1小时到1年之间')
        return v

class BatchUploadRequest(BaseModel):
    files_info: List[Dict[str, Any]]  # 文件信息列表
    business_type: Optional[str] = None
    business_id: Optional[str] = None
    is_public: bool = False
    expires_hours: Optional[int] = None
    
    @validator('files_info')
    def validate_files_info(cls, v):
        if not v or len(v) == 0:
            raise ValueError('至少需要一个文件')
        if len(v) > 20:  # 限制批量上传数量
            raise ValueError('批量上传文件数量不能超过20个')
        
        for i, file_info in enumerate(v):
            if not isinstance(file_info, dict):
                raise ValueError(f'文件信息{i+1}格式错误')
            if 'filename' not in file_info:
                raise ValueError(f'文件信息{i+1}缺少filename字段')
            if 'size' not in file_info:
                raise ValueError(f'文件信息{i+1}缺少size字段')
            if not isinstance(file_info['size'], int) or file_info['size'] <= 0:
                raise ValueError(f'文件信息{i+1}的size必须是正整数')
        
        return v

class FileListQuery(BaseModel):
    page: int = 1
    size: int = 20
    file_type: Optional[str] = None
    business_type: Optional[str] = None
    business_id: Optional[str] = None
    status: Optional[str] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    keyword: Optional[str] = None
    sort_by: str = "created_at"
    sort_order: str = "desc"
    
    @validator('page')
    def validate_page(cls, v):
        if v < 1:
            raise ValueError('页码必须大于0')
        return v
    
    @validator('size')
    def validate_size(cls, v):
        if v < 1 or v > 100:
            raise ValueError('每页数量必须在1-100之间')
        return v
    
    @validator('file_type')
    def validate_file_type(cls, v):
        if v and v not in ['image', 'video', 'document', 'audio', 'archive', 'other']:
            raise ValueError('文件类型必须是 image、video、document、audio、archive 或 other')
        return v
    
    @validator('status')
    def validate_status(cls, v):
        if v and v not in ['uploading', 'completed', 'failed', 'deleted']:
            raise ValueError('状态必须是 uploading、completed、failed 或 deleted')
        return v
    
    @validator('sort_by')
    def validate_sort_by(cls, v):
        allowed_fields = ['created_at', 'updated_at', 'original_filename', 'file_size', 'file_type']
        if v not in allowed_fields:
            raise ValueError(f'排序字段必须是以下之一: {", ".join(allowed_fields)}')
        return v
    
    @validator('sort_order')
    def validate_sort_order(cls, v):
        if v not in ['asc', 'desc']:
            raise ValueError('排序方式必须是 asc 或 desc')
        return v
    
    @validator('end_date')
    def validate_date_range(cls, v, values):
        if v and values.get('start_date') and v < values['start_date']:
            raise ValueError('结束日期不能早于开始日期')
        return v

class UploadedFileResponse(BaseModel):
    id: str
    original_filename: str
    file_extension: Optional[str]
    file_type: str
    file_size: int
    mime_type: Optional[str]
    storage_url: str
    file_metadata: Optional[Dict[str, Any]]
    upload_status: str
    upload_progress: int
    business_type: Optional[str]
    business_id: Optional[str]
    file_hash: Optional[str]
    is_public: bool
    created_at: datetime
    updated_at: datetime
    expires_at: Optional[datetime]

class FileUploadResponse(BaseModel):
    file_id: str
    upload_url: str  # 预签名上传URL
    access_url: str  # 访问URL
    expires_at: datetime
    upload_session_id: Optional[str] = None

class BatchUploadResponse(BaseModel):
    session_id: str
    upload_urls: List[Dict[str, str]]  # 每个文件的上传URL
    total_files: int
    expires_at: datetime

class UploadSessionResponse(BaseModel):
    session_id: str
    total_files: int
    completed_files: int
    failed_files: int
    total_size: int
    uploaded_size: int
    status: str
    progress_percentage: float
    created_at: datetime
    updated_at: datetime

class FileListResponse(BaseModel):
    items: List[UploadedFileResponse]
    pagination: Dict[str, int]
    statistics: Dict[str, Any]

class FileDeleteResponse(BaseModel):
    file_id: str
    filename: str
    deleted_at: datetime
    status: str

# 通用响应模式
class UploadApiResponse(BaseModel):
    success: bool
    data: Optional[Any] = None
    message: str
