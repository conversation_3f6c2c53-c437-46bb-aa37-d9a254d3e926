from pydantic import BaseModel, Field, validator
from typing import Optional, List, Dict, Any, Literal
from datetime import datetime
from decimal import Decimal
from enum import Enum
import uuid

# 定义channel_type的枚举值
class ChannelTypeEnum(str, Enum):
    """频道类型枚举"""
    UNLIMITED = "不限"
    NEWS = "新闻资讯"
    AUTO = "汽车网站"
    IT_TECH = "IT科技"
    LIFESTYLE = "生活消费"
    FINANCE = "财经商业"
    GAMING = "游戏电竞"
    REAL_ESTATE = "房产家居"
    FASHION = "女性时尚"
    ENTERTAINMENT = "娱乐休闲"
    HEALTH = "健康医疗"
    EDUCATION = "教育培训"
    CHANNEL_TYPE = "频道类型"
    TRAVEL = "酒店旅游"
    MUSIC_FOOD = "音乐餐饮"
    SPORTS = "体育运动"
    PUBLIC_WELFARE = "公益三农"
    CULTURE_ART = "文化艺术"
    PARENTING = "亲子包邮"
    DISCOUNT = "优惠套餐"
    ENERGY_ENV = "能源环保"

# 获取所有枚举值的列表
CHANNEL_TYPE_VALUES = [item.value for item in ChannelTypeEnum]


class ChannelCategoryBase(BaseModel):
    """渠道分类基础模型"""
    category_name: str = Field(..., min_length=1, max_length=100, description="分类名称")
    category_code: str = Field(..., min_length=1, max_length=50, description="分类代码")
    category_description: Optional[str] = Field(None, description="分类描述")
    parent_id: Optional[str] = Field(None, description="父分类ID")
    category_level: int = Field(1, ge=1, le=2, description="分类层级：1=主分类，2=子分类")
    sort_order: int = Field(0, ge=0, description="排序顺序")
    is_active: bool = Field(True, description="是否启用")


class ChannelCategoryCreate(BaseModel):
    """创建渠道分类请求"""
    category_name: str = Field(..., min_length=1, max_length=100, description="分类名称")
    category_code: str = Field(..., min_length=1, max_length=50, description="分类代码")
    category_description: Optional[str] = Field(None, description="分类描述")
    parent_id: Optional[str] = Field(None, description="父分类ID")
    category_level: int = Field(1, ge=1, le=2, description="分类层级：1=主分类，2=子分类")
    sort_order: int = Field(0, ge=0, description="排序顺序")
    is_active: bool = Field(True, description="是否启用")


class ChannelCategoryUpdate(BaseModel):
    """更新渠道分类请求"""
    category_name: Optional[str] = Field(None, min_length=1, max_length=100, description="分类名称")
    category_description: Optional[str] = Field(None, description="分类描述")
    parent_id: Optional[str] = Field(None, description="父分类ID")
    category_level: Optional[int] = Field(None, ge=1, le=2, description="分类层级：1=主分类，2=子分类")
    sort_order: Optional[int] = Field(None, ge=0, description="排序顺序")
    is_active: Optional[bool] = Field(None, description="是否启用")


class ChannelCategoryResponse(BaseModel):
    """渠道分类响应"""
    id: str
    category_name: str
    category_code: str
    category_description: Optional[str]
    parent_id: Optional[str] = None
    category_level: Optional[int] = None
    sort_order: Optional[int] = None
    is_active: bool
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    # 层级关系
    children: Optional[List['ChannelCategoryResponse']] = Field(None, description="子分类列表")
    parent: Optional['ChannelCategoryResponse'] = Field(None, description="父分类信息")

    class Config:
        from_attributes = True



# ==================== 渠道服务相关 Schema ====================

class ChannelServiceBase(BaseModel):
    """渠道服务基础模型"""
    service_name: str = Field(..., min_length=1, max_length=200, description="服务名称")
    service_code: str = Field(..., min_length=1, max_length=50, description="服务代码")
    service_description: str = Field(..., min_length=1, description="服务详细描述")
    service_features: Optional[List[str]] = Field(None, description="服务特色/亮点")

    base_price: Decimal = Field(..., ge=0, description="基础价格")
    price_unit: str = Field("次", max_length=20, description="价格单位")

    channel_type: Optional[ChannelTypeEnum] = Field(None, description="标签分类")

    is_active: bool = Field(True, description="是否启用")


class ChannelServiceCreate(ChannelServiceBase):
    """创建渠道服务"""
    category_id: str = Field(..., description="分类ID")
    # service_source 字段已移除，将根据用户角色自动判断：
    # - 管理员(super_admin/admin) -> platform
    # - 渠道商(channel_user) -> provider


class ChannelServiceUpdate(BaseModel):
    """更新渠道服务"""
    service_name: Optional[str] = Field(None, min_length=1, max_length=200, description="服务名称")
    service_code: Optional[str] = Field(None, min_length=1, max_length=50, description="服务代码")
    service_description: Optional[str] = Field(None, min_length=1, description="服务详细描述")
    service_features: Optional[List[str]] = Field(None, description="服务特色/亮点")

    category_id: Optional[str] = Field(None, description="分类ID")

    base_price: Optional[Decimal] = Field(None, ge=0, description="基础价格")
    price_unit: Optional[str] = Field(None, max_length=20, description="价格单位")

    channel_type: Optional[ChannelTypeEnum] = Field(None, description="标签分类")

    is_active: Optional[bool] = Field(None, description="是否启用")


class BoundProviderInfo(BaseModel):
    """绑定的内容提供商信息（简化版）"""
    id: str  # 绑定关系ID
    provider_id: str  # 内容提供商ID
    provider_name: str  # 内容提供商名称

class ChannelServiceResponse(BaseModel):
    """渠道服务响应"""
    id: str
    category_id: str

    # 服务来源和审核信息
    service_source: Optional[str] = None
    provider_id: Optional[str] = None
    approval_status: Optional[str] = None
    approval_note: Optional[str] = None
    approval_time: Optional[datetime] = None
    approver_id: Optional[str] = None

    service_name: str
    service_code: str
    service_description: str
    service_features: Optional[List[str]]

    base_price: Decimal
    discount_price: Optional[Decimal] = None
    price_unit: str

    # 服务规格
    service_specs: Optional[Dict[str, Any]] = None
    delivery_time: Optional[int] = None
    revision_count: Optional[int] = None

    channel_type: Optional[str] = None
    portal_type: Optional[str] = None
    platform_specs: Optional[Dict[str, Any]] = None
    coverage_area: Optional[List[str]] = None

    is_active: bool

    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    # 关联数据
    category: Optional[ChannelCategoryResponse] = Field(None, description="所属分类")
    creator_info: Optional[Dict[str, Any]] = Field(None, description="创建者信息")
    bound_providers: Optional[List[BoundProviderInfo]] = Field(None, description="绑定的内容提供商列表")

    class Config:
        from_attributes = True


# ==================== 服务审核相关 Schema ====================

class ChannelServiceApprovalRequest(BaseModel):
    """服务审核请求"""
    status: str = Field(..., description="审核状态：approved/rejected")
    note: Optional[str] = Field(None, description="审核备注")

    @validator('status')
    def validate_status(cls, v):
        if v not in ['approved', 'rejected']:
            raise ValueError('审核状态必须是 approved 或 rejected')
        return v


# ==================== 渠道商服务绑定相关 Schema ====================

class ChannelCategoryMappingBase(BaseModel):
    """渠道商服务绑定基础模型"""
    is_active: bool = Field(True, description="是否启用该服务")


class ChannelCategoryMappingCreate(ChannelCategoryMappingBase):
    """创建渠道商服务绑定请求"""
    pass


class ChannelCategoryMappingUpdate(BaseModel):
    """更新渠道商服务绑定请求"""
    is_active: Optional[bool] = Field(None, description="是否启用该服务")

    # 自定义配置
    custom_price: Optional[Decimal] = Field(None, ge=0, description="自定义价格")
    custom_delivery_time: Optional[int] = Field(None, ge=1, description="自定义交付时间（小时）")
    custom_revision_count: Optional[int] = Field(None, ge=0, description="自定义修改次数")
    custom_coverage_area: Optional[List[str]] = Field(None, description="自定义覆盖区域")
    custom_channel_type: Optional[str] = Field(None, max_length=50, description="自定义频道类型")
    custom_portal_type: Optional[str] = Field(None, max_length=50, description="自定义门户类型")
    custom_service_specs: Optional[Dict[str, Any]] = Field(None, description="自定义服务规格")
    custom_platform_specs: Optional[Dict[str, Any]] = Field(None, description="自定义平台规格")


class ChannelCategoryMappingResponse(BaseModel):
    """渠道商服务绑定响应"""
    id: str
    provider_id: str
    service_id: str
    is_active: bool

    created_at: datetime
    updated_at: datetime

    # 关联数据
    service: Optional[ChannelServiceResponse] = Field(None, description="服务信息")
    category: Optional[ChannelCategoryResponse] = Field(None, description="分类信息")

    class Config:
        from_attributes = True


# ==================== 渠道商服务评价相关 Schema ====================

class ChannelServiceEvaluationBase(BaseModel):
    """渠道商服务评价基础模型"""
    service_rating: Decimal = Field(Decimal('0.00'), ge=0, le=5, description="服务评分")
    quality_rating: Decimal = Field(Decimal('0.00'), ge=0, le=5, description="质量评分")
    delivery_rating: Decimal = Field(Decimal('0.00'), ge=0, le=5, description="交付评分")
    communication_rating: Decimal = Field(Decimal('0.00'), ge=0, le=5, description="沟通评分")

    total_orders: int = Field(0, ge=0, description="总订单数")
    completed_orders: int = Field(0, ge=0, description="完成订单数")
    cancelled_orders: int = Field(0, ge=0, description="取消订单数")

    is_primary: bool = Field(False, description="是否为主要服务")
    is_recommended: bool = Field(False, description="是否推荐该服务")
    is_active: bool = Field(True, description="是否启用")


class ChannelServiceEvaluationCreate(ChannelServiceEvaluationBase):
    """创建渠道商服务评价请求"""
    pass


class ChannelServiceEvaluationUpdate(BaseModel):
    """更新渠道商服务评价请求"""
    service_rating: Optional[Decimal] = Field(None, ge=0, le=5, description="服务评分")
    quality_rating: Optional[Decimal] = Field(None, ge=0, le=5, description="质量评分")
    delivery_rating: Optional[Decimal] = Field(None, ge=0, le=5, description="交付评分")
    communication_rating: Optional[Decimal] = Field(None, ge=0, le=5, description="沟通评分")

    total_orders: Optional[int] = Field(None, ge=0, description="总订单数")
    completed_orders: Optional[int] = Field(None, ge=0, description="完成订单数")
    cancelled_orders: Optional[int] = Field(None, ge=0, description="取消订单数")

    is_primary: Optional[bool] = Field(None, description="是否为主要服务")
    is_recommended: Optional[bool] = Field(None, description="是否推荐该服务")
    is_active: Optional[bool] = Field(None, description="是否启用")


class ChannelServiceEvaluationResponse(BaseModel):
    """渠道商服务评价响应"""
    id: str
    mapping_id: str

    service_rating: Decimal
    quality_rating: Decimal
    delivery_rating: Decimal
    communication_rating: Decimal

    total_orders: int
    completed_orders: int
    cancelled_orders: int

    is_primary: bool
    is_recommended: bool
    is_active: bool

    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class ChannelCategoryMappingListQuery(BaseModel):
    """渠道商分类关联列表查询"""
    page: int = Field(1, ge=1, description="页码")
    size: int = Field(20, ge=1, le=100, description="每页数量")
    provider_id: Optional[str] = Field(None, description="渠道商ID筛选")
    category_id: Optional[str] = Field(None, description="分类ID筛选")
    service_id: Optional[str] = Field(None, description="服务ID筛选")
    is_active: Optional[bool] = Field(None, description="是否启用筛选")
    sort_by: str = Field("created_at", description="排序字段")
    sort_order: str = Field("desc", pattern="^(asc|desc)$", description="排序方式")

    @validator('provider_id', 'category_id', 'service_id')
    def validate_uuid(cls, v):
        if v is not None:
            try:
                uuid.UUID(v)
            except ValueError:
                raise ValueError('ID格式无效')
        return v


class ChannelServiceListQuery(BaseModel):
    """渠道服务列表查询"""
    page: int = Field(1, ge=1, description="页码")
    size: int = Field(20, ge=1, le=100, description="每页数量")
    category_id: Optional[str] = Field(None, description="分类ID筛选")
    portal_type: Optional[str] = Field(None, description="门户类型筛选")
    channel_type: Optional[str] = Field(None, description="频道类型筛选")
    channel_category: Optional[str] = Field(None, description="频道类别筛选")
    coverage_area: Optional[str] = Field(None, description="覆盖区域筛选，多个区域用逗号分隔")
    is_active: Optional[bool] = Field(None, description="是否启用筛选")
    min_price: Optional[Decimal] = Field(None, ge=0, description="最低价格筛选")
    max_price: Optional[Decimal] = Field(None, ge=0, description="最高价格筛选")
    max_delivery_time: Optional[int] = Field(None, ge=1, description="最大交付时间筛选（小时）")
    max_revision_count: Optional[int] = Field(None, ge=0, description="最大修改次数筛选")
    service_features: Optional[str] = Field(None, description="服务特色关键词筛选")
    search: Optional[str] = Field(None, description="搜索关键词")
    sort_by: str = Field("created_at", description="排序字段")
    sort_order: str = Field("desc", pattern="^(asc|desc)$", description="排序方式")


class ChannelCategoryListQuery(BaseModel):
    """渠道分类列表查询"""
    page: int = Field(1, ge=1, description="页码")
    size: int = Field(20, ge=1, le=100, description="每页数量")
    parent_id: Optional[str] = Field(None, description="父分类ID筛选")
    level: Optional[int] = Field(None, ge=1, le=2, description="分类层级筛选")
    is_active: Optional[bool] = Field(None, description="是否启用筛选")
    is_featured: Optional[bool] = Field(None, description="是否推荐筛选")
    search: Optional[str] = Field(None, description="搜索关键词")
    sort_by: str = Field("created_at", description="排序字段")
    sort_order: str = Field("asc", pattern="^(asc|desc)$", description="排序方式")


class ChannelCategoryApiResponse(BaseModel):
    """渠道分类API统一响应格式"""
    success: bool
    data: Optional[Any] = None
    message: str
    error_code: Optional[str] = None


# 更新前向引用
ChannelCategoryResponse.model_rebuild()
