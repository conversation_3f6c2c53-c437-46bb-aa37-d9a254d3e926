from pydantic import BaseModel, field_validator
from typing import Optional, List, Dict, Any
from datetime import datetime


class KnowledgeBaseCreateRequest(BaseModel):
    name: str
    description: Optional[str] = None
    
    @field_validator('name')
    @classmethod
    def validate_name(cls, v):
        if not v or len(v.strip()) < 2:
            raise ValueError('知识库名称至少需要2个字符')
        if len(v) > 200:
            raise ValueError('知识库名称不能超过200个字符')
        return v.strip()


class KnowledgeBaseUpdateRequest(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    
    @field_validator('name')
    @classmethod
    def validate_name(cls, v):
        if v is not None:
            if not v or len(v.strip()) < 2:
                raise ValueError('知识库名称至少需要2个字符')
            if len(v) > 200:
                raise ValueError('知识库名称不能超过200个字符')
            return v.strip()
        return v


class KnowledgeBaseResponse(BaseModel):
    id: str
    name: str
    description: Optional[str] = None
    viking_collection_name: str
    document_count: int
    vector_count: int
    created_at: datetime
    updated_at: datetime


class KnowledgeBaseListResponse(BaseModel):
    items: List[KnowledgeBaseResponse]
    pagination: Dict[str, int]


class DocumentCreateRequest(BaseModel):
    title: str
    content: str
    file_type: Optional[str] = "text"
    
    @field_validator('title')
    @classmethod
    def validate_title(cls, v):
        if not v or len(v.strip()) < 2:
            raise ValueError('文档标题至少需要2个字符')
        if len(v) > 500:
            raise ValueError('文档标题不能超过500个字符')
        return v.strip()
    
    @field_validator('content')
    @classmethod
    def validate_content(cls, v):
        if not v or len(v.strip()) < 10:
            raise ValueError('文档内容至少需要10个字符')
        if len(v) > 1000000:  # 1MB限制
            raise ValueError('文档内容不能超过1MB')
        return v.strip()


class DocumentResponse(BaseModel):
    id: str
    knowledge_base_id: str
    title: str
    file_path: Optional[str] = None
    file_type: Optional[str] = None
    file_size: Optional[int] = None
    chunk_count: int
    embedding_status: str
    error_message: Optional[str] = None
    created_at: datetime
    updated_at: datetime


class DocumentListResponse(BaseModel):
    items: List[DocumentResponse]
    pagination: Dict[str, int]


class DocumentUploadResponse(BaseModel):
    id: str
    title: str
    embedding_status: str
    message: str


class DocumentChunkResponse(BaseModel):
    id: str
    document_id: str
    chunk_index: int
    content: str
    token_count: Optional[int] = None
    viking_vector_id: Optional[str] = None
    embedding_model: Optional[str] = None
    embedding_status: str
    created_at: datetime


class SearchRequest(BaseModel):
    query: str
    keywords: Optional[List[str]] = None
    limit: int = 5
    
    @field_validator('query')
    @classmethod
    def validate_query(cls, v):
        if not v or len(v.strip()) < 2:
            raise ValueError('搜索查询至少需要2个字符')
        if len(v) > 500:
            raise ValueError('搜索查询不能超过500个字符')
        return v.strip()
    
    @field_validator('limit')
    @classmethod
    def validate_limit(cls, v):
        if v < 1 or v > 50:
            raise ValueError('搜索结果数量必须在1-50之间')
        return v


class SearchResult(BaseModel):
    content: str
    title: str
    document_id: str
    similarity_score: float
    knowledge_base_id: str
    final_score: Optional[float] = None


class SearchResultResponse(BaseModel):
    results: List[SearchResult]
    total: int
    query: str
    knowledge_base_ids: List[str]


class BatchSearchRequest(BaseModel):
    knowledge_base_ids: List[str]
    query: str
    keywords: Optional[List[str]] = None
    limit: int = 5
    
    @field_validator('knowledge_base_ids')
    @classmethod
    def validate_knowledge_base_ids(cls, v):
        if not v or len(v) == 0:
            raise ValueError('至少需要选择一个知识库')
        if len(v) > 10:
            raise ValueError('最多只能选择10个知识库')
        return v


class KnowledgeBaseStatistics(BaseModel):
    total_documents: int
    completed_documents: int
    processing_documents: int
    failed_documents: int
    total_chunks: int


class KnowledgeBaseDetailResponse(BaseModel):
    id: str
    name: str
    description: Optional[str] = None
    viking_collection_name: str
    document_count: int
    vector_count: int
    statistics: KnowledgeBaseStatistics
    recent_documents: List[DocumentResponse]
    created_at: datetime
    updated_at: datetime


class ProcessingStatusResponse(BaseModel):
    document_id: str
    title: str
    embedding_status: str
    progress: Optional[float] = None
    error_message: Optional[str] = None
    estimated_completion: Optional[datetime] = None


class KnowledgeApiResponse(BaseModel):
    success: bool
    data: Optional[Any] = None
    message: str
    error_code: Optional[str] = None
