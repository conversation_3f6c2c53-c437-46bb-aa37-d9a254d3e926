"""
公告系统相关的Pydantic模式
"""
from pydantic import BaseModel, Field, validator
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum


class AnnouncementTypeEnum(str, Enum):
    """公告类型枚举"""
    SYSTEM = "system"
    MAINTENANCE = "maintenance"
    FEATURE = "feature"
    PROMOTION = "promotion"
    NOTICE = "notice"


class AnnouncementStatusEnum(str, Enum):
    """公告状态枚举"""
    DRAFT = "draft"
    PUBLISHED = "published"
    ARCHIVED = "archived"


class AnnouncementPriorityEnum(str, Enum):
    """公告优先级枚举"""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"


class AnnouncementTargetEnum(str, Enum):
    """公告目标用户枚举"""
    ALL = "all"
    ENTERPRISE = "enterprise"
    CHANNEL = "channel"
    AGENT = "agent"
    ADMIN = "admin"


# 请求模式
class AnnouncementCreateRequest(BaseModel):
    """创建公告请求"""
    title: str = Field(..., min_length=1, max_length=200, description="公告标题")
    content: str = Field(..., min_length=1, description="公告内容")
    summary: Optional[str] = Field(None, max_length=500, description="公告摘要")
    type: AnnouncementTypeEnum = Field(AnnouncementTypeEnum.NOTICE, description="公告类型")
    priority: AnnouncementPriorityEnum = Field(AnnouncementPriorityEnum.NORMAL, description="优先级")
    target_audience: AnnouncementTargetEnum = Field(AnnouncementTargetEnum.ALL, description="目标用户")
    is_pinned: bool = Field(False, description="是否置顶")
    is_popup: bool = Field(False, description="是否弹窗显示")
    publish_time: Optional[datetime] = Field(None, description="发布时间")
    expire_time: Optional[datetime] = Field(None, description="过期时间")

    @validator('expire_time')
    def validate_expire_time(cls, v, values):
        # 临时禁用时间验证来调试问题
        # TODO: 修复时区比较问题后重新启用
        return v


class AnnouncementUpdateRequest(BaseModel):
    """更新公告请求"""
    title: Optional[str] = Field(None, min_length=1, max_length=200, description="公告标题")
    content: Optional[str] = Field(None, min_length=1, description="公告内容")
    summary: Optional[str] = Field(None, max_length=500, description="公告摘要")
    type: Optional[AnnouncementTypeEnum] = Field(None, description="公告类型")
    priority: Optional[AnnouncementPriorityEnum] = Field(None, description="优先级")
    target_audience: Optional[AnnouncementTargetEnum] = Field(None, description="目标用户")
    status: Optional[AnnouncementStatusEnum] = Field(None, description="状态")
    is_pinned: Optional[bool] = Field(None, description="是否置顶")
    is_popup: Optional[bool] = Field(None, description="是否弹窗显示")
    publish_time: Optional[datetime] = Field(None, description="发布时间")
    expire_time: Optional[datetime] = Field(None, description="过期时间")

    @validator('expire_time')
    def validate_expire_time(cls, v, values):
        # 只有当两个时间都存在时才进行验证
        if v and 'publish_time' in values and values['publish_time']:
            try:
                # 确保时间比较是正确的
                if v <= values['publish_time']:
                    raise ValueError('过期时间必须晚于发布时间')
            except (TypeError, AttributeError):
                # 如果时间比较出错，跳过验证（可能是部分更新）
                pass
        return v


class AnnouncementQueryRequest(BaseModel):
    """查询公告请求"""
    page: int = Field(1, ge=1, description="页码")
    page_size: int = Field(20, ge=1, le=100, description="每页数量")
    type: Optional[str] = Field(None, description="公告类型")
    status: Optional[str] = Field(None, description="状态")
    priority: Optional[str] = Field(None, description="优先级")
    target_audience: Optional[str] = Field(None, description="目标用户")
    is_pinned: Optional[bool] = Field(None, description="是否置顶")
    keyword: Optional[str] = Field(None, max_length=100, description="关键词搜索")
    start_date: Optional[datetime] = Field(None, description="开始日期")
    end_date: Optional[datetime] = Field(None, description="结束日期")

    @validator('type')
    def validate_type(cls, v):
        if v is not None:
            valid_types = ['system', 'maintenance', 'feature', 'promotion', 'notice']
            if v not in valid_types:
                raise ValueError(f'公告类型必须是以下值之一: {", ".join(valid_types)}')
        return v

    @validator('status')
    def validate_status(cls, v):
        if v is not None:
            valid_statuses = ['draft', 'published', 'archived']
            if v not in valid_statuses:
                raise ValueError(f'状态必须是以下值之一: {", ".join(valid_statuses)}')
        return v

    @validator('priority')
    def validate_priority(cls, v):
        if v is not None:
            valid_priorities = ['low', 'normal', 'high', 'urgent']
            if v not in valid_priorities:
                raise ValueError(f'优先级必须是以下值之一: {", ".join(valid_priorities)}')
        return v

    @validator('target_audience')
    def validate_target_audience(cls, v):
        if v is not None:
            valid_targets = ['all', 'enterprise', 'channel', 'agent', 'admin']
            if v not in valid_targets:
                raise ValueError(f'目标用户必须是以下值之一: {", ".join(valid_targets)}')
        return v


# 响应模式
class AnnouncementResponse(BaseModel):
    """公告响应"""
    id: str
    title: str
    content: str
    summary: Optional[str]
    type: str
    priority: str
    target_audience: str
    status: str
    is_pinned: bool
    is_popup: bool
    publish_time: Optional[datetime]
    expire_time: Optional[datetime]
    view_count: int
    created_by: str
    created_at: datetime
    updated_at: datetime

    # 扩展信息
    creator_name: Optional[str] = None

    class Config:
        from_attributes = True


class AnnouncementListResponse(BaseModel):
    """公告列表响应"""
    items: List[AnnouncementResponse]
    pagination: Dict[str, int]
    statistics: Optional[Dict[str, Any]] = None


class AnnouncementStatsResponse(BaseModel):
    """公告统计响应"""
    total_announcements: int
    published_announcements: int
    draft_announcements: int
    archived_announcements: int
    total_views: int
    recent_announcements: List[AnnouncementResponse]


# 通用响应模式
class AnnouncementApiResponse(BaseModel):
    """公告API通用响应"""
    success: bool
    data: Optional[Any] = None
    message: str
    error_code: Optional[str] = None