from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession
from app.database import get_db
from app.core.security import verify_token
from app.models.user import User, UserStatus
from app.services.permission_service import PermissionService
from sqlalchemy import select
from typing import Optional, Dict, Any, List

security = HTTPBearer()

async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """获取当前用户"""
    token = credentials.credentials
    payload = verify_token(token)

    if payload is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的认证令牌",
            headers={"WWW-Authenticate": "Bearer"},
        )

    user_id = payload.get("sub")
    if user_id is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="令牌中缺少用户信息",
            headers={"WWW-Authenticate": "Bearer"},
        )

    result = await db.execute(select(User).where(User.id == user_id))
    user = result.scalar_one_or_none()

    if user is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户不存在",
            headers={"WWW-Authenticate": "Bearer"},
        )

    if user.status != UserStatus.ACTIVE:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户账号已被禁用",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # 查询用户角色
    roles = []
    try:
        # 使用原生SQL查询，避免ORM复杂性
        from sqlalchemy import text

        roles_query = text("""
            SELECT r.role_code
            FROM user_roles ur
            JOIN roles r ON ur.role_id = r.id
            WHERE ur.user_id = :user_id AND ur.role_status = 'active'
        """)

        result = await db.execute(roles_query, {"user_id": str(user.id)})
        roles = [row[0] for row in result.fetchall()]

        # 如果没有角色，给一个默认角色
        if not roles:
            roles = ["regular_user"]

    except Exception as e:
        # 如果查询失败，给默认角色
        roles = ["regular_user"]

    return {
        "id": str(user.id),
        "email": user.email,
        "full_name": user.full_name,
        "phone": user.phone,
        "status": user.status.value,
        "roles": roles,
        "email_verified": user.email_verified,
        "phone_verified": user.phone_verified,
        "referral_code": user.referral_code
    }

async def get_current_admin(
    current_user: Dict[str, Any] = Depends(get_current_user)
) -> Dict[str, Any]:
    """获取当前管理员用户"""
    user_roles = current_user.get("roles", [])
    if "super_admin" not in user_roles and "admin" not in user_roles:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要管理员权限"
        )
    return current_user


async def get_current_super_admin(
    current_user: Dict[str, Any] = Depends(get_current_user)
) -> Dict[str, Any]:
    """获取当前超级管理员用户"""
    user_roles = current_user.get("roles", [])
    if "super_admin" not in user_roles:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要超级管理员权限"
        )
    return current_user

async def get_optional_user(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    db: AsyncSession = Depends(get_db)
) -> Optional[Dict[str, Any]]:
    """获取可选的当前用户（用于不需要强制登录的接口）"""
    if credentials is None:
        return None

    try:
        return await get_current_user(credentials, db)
    except HTTPException:
        return None


def check_permission(permission_code: str):
    """检查用户权限的依赖函数"""
    async def _check_permission(
        current_user: Dict[str, Any] = Depends(get_current_user),
        db: AsyncSession = Depends(get_db)
    ) -> Dict[str, Any]:
        """检查用户是否有指定权限"""
        user_id = current_user["id"]
        print(f"🔍 权限检查: user_id={user_id}, permission_code={permission_code}")

        # 使用权限服务检查权限
        permission_service = PermissionService(db)
        has_permission = await permission_service.has_permission_async(user_id, permission_code)
        print(f"🔍 权限检查结果: has_permission={has_permission}")

        if not has_permission:
            print(f"❌ 权限不足: 用户 {user_id} 缺少权限 {permission_code}")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"缺少权限: {permission_code}"
            )

        return current_user

    return _check_permission


def require_permissions(permission_codes: List[str]):
    """要求用户拥有指定权限的依赖函数"""
    async def _require_permissions(
        current_user: Dict[str, Any] = Depends(get_current_user),
        db: AsyncSession = Depends(get_db)
    ) -> Dict[str, Any]:
        """检查用户是否拥有所有指定权限"""
        user_id = current_user["id"]

        # 使用权限服务检查权限
        permission_service = PermissionService(db)

        missing_permissions = []
        for permission_code in permission_codes:
            has_permission = await permission_service.has_permission_async(user_id, permission_code)
            if not has_permission:
                missing_permissions.append(permission_code)

        if missing_permissions:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"缺少权限: {', '.join(missing_permissions)}"
            )

        return current_user

    return _require_permissions
