from fastapi import FastAPI, Request, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
from dotenv import load_dotenv
import os
import asyncio

# 确保加载.env文件
load_dotenv()

from app.config import settings
from app.middleware.logging import LoggingMiddleware
from app.middleware.auth import AuthMiddleware
from app.api.v1 import api_router
from app.database import engine, Base
from app.services.rag_knowledge_service import RAGKnowledgeService
import logging
import os

# 配置日志
# 确保日志目录存在
log_dir = os.path.dirname(settings.log_file)
if log_dir and not os.path.exists(log_dir):
    os.makedirs(log_dir, exist_ok=True)

logging.basicConfig(
    level=getattr(logging, settings.log_level),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler(settings.log_file),
        logging.StreamHandler()
    ]
)

# 关闭数据库相关的日志输出
logging.getLogger('sqlalchemy.engine').setLevel(logging.ERROR)
logging.getLogger('sqlalchemy.dialects').setLevel(logging.ERROR)
logging.getLogger('sqlalchemy.pool').setLevel(logging.ERROR)
logging.getLogger('sqlalchemy.orm').setLevel(logging.ERROR)

# 创建FastAPI应用
app = FastAPI(
    title=settings.app_name,
    description=settings.app_description,
    version=settings.app_version,
    debug=settings.debug,
    docs_url="/docs" if settings.debug else None,
    redoc_url="/redoc" if settings.debug else None,
)

# 添加CORS中间件
origins = [
    "http://localhost:3000",
    "http://localhost:3001",
    "http://localhost:8080",
    "http://localhost:8000",  # AI HTML Generator 前端
    "http://127.0.0.1:3000",
    "http://127.0.0.1:3001",
    "http://127.0.0.1:8080",
    "http://127.0.0.1:8000",  # AI HTML Generator 前端
    "http://***************:3000",
    "http://***************",
]

# 生产环境添加实际域名
if not settings.debug:
    # 在开发阶段，即使是生产环境也允许所有源
    origins = ["*"]
    # origins = [
    #     "https://yourdomain.com",
    #     "https://www.yourdomain.com",
    # ]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD", "PATCH"],
    allow_headers=["*"],
    expose_headers=["*"],
    max_age=3600,  # 预检请求缓存1小时
)

# 添加全局OPTIONS处理器
@app.middleware("http")
async def add_cors_header(request: Request, call_next):
    if request.method == "OPTIONS":
        response = Response()
        response.headers["Access-Control-Allow-Origin"] = "*"
        response.headers["Access-Control-Allow-Methods"] = "GET, POST, PUT, DELETE, OPTIONS, HEAD, PATCH"
        response.headers["Access-Control-Allow-Headers"] = "*"
        response.headers["Access-Control-Max-Age"] = "3600"
        return response

    response = await call_next(request)
    return response

# 添加启动和关闭事件处理器
@app.on_event("startup")
async def startup_event():
    """应用启动时的初始化"""
    logging.info("正在启动AI搜索引擎优化平台...")

    try:
        # 创建数据库表（如果不存在）
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        logging.info("数据库表初始化成功")
    except Exception as e:
        logging.error(f"数据库初始化失败: {e}")
        # 不要因为数据库初始化失败而停止应用启动
        # 这样可以让应用继续运行，便于调试

    # 检查RAG服务连接
    try:
        rag_service = RAGKnowledgeService()
        is_healthy = await rag_service.health_check()
        await rag_service.close()

        if is_healthy:
            logging.info(f"✅ RAG知识库服务连接成功: {settings.rag_service_url}")
        else:
            logging.warning(f"⚠️ RAG知识库服务健康检查失败: {settings.rag_service_url}")
    except Exception as e:
        logging.error(f"❌ RAG知识库服务连接失败: {e}")
        logging.warning("请检查RAG服务配置和网络连接")

    logging.info("AI搜索引擎优化平台启动完成")

@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭时的清理"""
    logging.info("AI搜索引擎优化平台正在关闭...")

    try:
        # 关闭数据库连接
        await engine.dispose()
        logging.info("数据库连接已关闭")
    except Exception as e:
        logging.error(f"关闭数据库连接时出错: {e}")

    logging.info("AI搜索引擎优化平台已关闭")

# 添加自定义中间件（注意：中间件的添加顺序很重要，后添加的先执行）
app.add_middleware(AuthMiddleware)
app.add_middleware(LoggingMiddleware)

# 注册API路由
app.include_router(api_router, prefix="/api/v1")

# 单独注册templates路由
try:
    from app.api.v1.geo.templates import router as templates_router
    app.include_router(templates_router, prefix="/api/v1/geo", tags=["GEO模板管理"])
except ImportError as e:
    logging.warning(f"Failed to import templates router: {e}")

# AI聊天功能已合并到conversations模块

# 配置静态文件服务
# 构建目录路径 - build目录在backend目录下
build_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "build")

if os.path.exists(build_path):
    # 挂载静态文件目录
    static_path = os.path.join(build_path, "static")
    if os.path.exists(static_path):
        app.mount("/static", StaticFiles(directory=static_path), name="static")
    
    # 挂载根目录下的静态文件（favicon.ico, manifest.json等）
    app.mount("/", StaticFiles(directory=build_path, html=True), name="build")
    
    # 处理SPA路由 - 对于所有非API和静态资源的路由，返回index.html
    @app.get("/{path:path}")
    async def serve_spa(path: str):
        # 跳过API路由
        if path.startswith("api/") or path == "docs" or path == "redoc":
            return {"error": "Not found"}
        
        # 检查是否是静态文件
        file_path = os.path.join(build_path, path)
        if os.path.exists(file_path) and os.path.isfile(file_path):
            return FileResponse(file_path)
        
        # 对于所有其他路由，返回index.html（SPA处理）
        index_path = os.path.join(build_path, "index.html")
        if os.path.exists(index_path):
            return FileResponse(index_path, media_type="text/html")
        
        return {"error": "Page not found"}
else:
    # 如果build目录不存在，不提供根路径接口
    pass


