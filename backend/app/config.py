from pydantic_settings import BaseSettings
from pydantic import Field
from typing import Optional
import os

class Settings(BaseSettings):
    # 数据库配置 (字节跳动云RDS PostgreSQL)
    database_url: str
    database_test_url: Optional[str] = None

    # Redis配置 (字节跳动云Redis)
    redis_url: str = "redis://username:<EMAIL>:6379/0"

    # JWT配置
    secret_key: str
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 1440
    refresh_token_expire_minutes: int = 43200

    # 应用配置
    app_name: str = "AI搜索引擎优化平台"
    app_version: str = "1.0.0"
    app_description: str = "基于人工智能的搜索引擎优化和内容管理平台，提供关键词分析、排名监控、内容生成等专业SEO服务"
    debug: bool = False
    environment: str = "production"

    # 日志配置
    log_level: str = "INFO"
    log_file: str = "logs/app.log"

    # 文件上传配置
    upload_dir: str = "uploads"
    max_file_size: int = 10485760  # 10MB

    # TOS对象存储配置 (字节跳动云存储)
    tos_access_key: Optional[str] = None
    tos_secret_key: Optional[str] = None
    tos_bucket: Optional[str] = None
    tos_region: Optional[str] = None
    tos_endpoint: Optional[str] = None

    # AI服务配置 (豆包AI - 火山引擎)
    ai_api_key: str = Field(default="", env="AI_API_KEY")
    ai_api_url: str = Field(default="https://ark.cn-beijing.volces.com/api/v3", env="AI_API_URL")
    ai_model: str = Field(default="doubao-seed-1-6-flash-250715", env="AI_MODEL")

    # RAG知识库服务配置
    rag_service_url: str = Field(default="http://*************:8000", env="RAG_SERVICE_URL")
    rag_service_timeout: int = Field(default=30, env="RAG_SERVICE_TIMEOUT")
    rag_service_enabled: bool = Field(default=True, env="RAG_SERVICE_ENABLED")
    rag_service_max_retries: int = Field(default=3, env="RAG_SERVICE_MAX_RETRIES")
    rag_service_max_chars: int = Field(default=500, env="RAG_SERVICE_MAX_CHARS")
    rag_service_overlap: int = Field(default=100, env="RAG_SERVICE_OVERLAP")

    # 邮件服务配置
    smtp_host: str = "smtp.qq.com"
    smtp_port: int = 587
    smtp_username: Optional[str] = None
    smtp_password: Optional[str] = None
    smtp_use_tls: bool = True

    # 监控配置
    monitoring_interval: int = 3600
    ranking_check_timeout: int = 30
    max_concurrent_checks: int = 10
    
    # 微信支付配置
    wechat_app_id: Optional[str] = None
    wechat_mch_id: Optional[str] = None
    wechat_api_key: Optional[str] = None
    wechat_api_v3_key: Optional[str] = None
    wechat_cert_path: Optional[str] = None
    wechat_notify_url: Optional[str] = None
    
    # 支付宝配置
    alipay_app_id: Optional[str] = None
    alipay_private_key: Optional[str] = None
    alipay_public_key: Optional[str] = None
    alipay_notify_url: Optional[str] = None
    
    # 支付配置
    payment_timeout: int = 1800  # 支付超时时间（秒）
    refund_review_required: bool = True  # 退款是否需要审核
    
    class Config:
        env_file = ".env"
        case_sensitive = False

settings = Settings()

# 支付方式配置
PAYMENT_METHODS = {
    "wechat": {
        "name": "微信支付",
        "type": "online",
        "is_active": True,
        "fee_rate": 0.006,  # 0.6%手续费
        "min_amount": 0.01,
        "max_amount": 50000
    },
    "alipay": {
        "name": "支付宝",
        "type": "online", 
        "is_active": True,
        "fee_rate": 0.006,
        "min_amount": 0.01,
        "max_amount": 50000
    },
    "offline": {
        "name": "线下支付",
        "type": "offline",
        "is_active": True,
        "fee_rate": 0,
        "min_amount": 0,
        "max_amount": 999999
    },
    "balance": {
        "name": "余额支付",
        "type": "online",
        "is_active": False,
        "fee_rate": 0,
        "min_amount": 0.01,
        "max_amount": 999999
    },
    "bank_transfer": {
        "name": "银行转账",
        "type": "offline",
        "is_active": True,
        "fee_rate": 0,
        "min_amount": 0,
        "max_amount": 999999
    }
}
