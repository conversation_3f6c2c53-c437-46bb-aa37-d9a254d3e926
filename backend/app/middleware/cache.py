from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp
import hashlib
import json
from app.core.redis import redis_client
import asyncio

class CacheMiddleware(BaseHTTPMiddleware):
    """响应缓存中间件"""
    
    def __init__(self, app: ASGIApp, expire: int = 60):
        super().__init__(app)
        self.expire = expire
    
    async def dispatch(self, request: Request, call_next):
        # 只缓存GET请求
        if request.method != "GET":
            return await call_next(request)
        
        # 跳过需要认证的接口
        if "Authorization" in request.headers:
            return await call_next(request)
        
        # 生成缓存键
        cache_key = self._generate_cache_key(request)
        
        # 尝试从缓存获取
        cached = await self._get_from_cache(cache_key)
        if cached:
            return Response(
                content=cached,
                media_type="application/json",
                headers={"X-Cache": "HIT"}
            )
        
        # 执行请求
        response = await call_next(request)
        
        # 缓存成功响应
        if response.status_code == 200:
            body = b""
            async for chunk in response.body_iterator:
                body += chunk
            
            await self._save_to_cache(cache_key, body)
            
            return Response(
                content=body,
                status_code=response.status_code,
                headers=dict(response.headers),
                media_type=response.media_type
            )
        
        return response
    
    def _generate_cache_key(self, request: Request) -> str:
        """生成缓存键"""
        key_parts = [
            request.url.path,
            str(sorted(request.query_params.items()))
        ]
        key_string = ":".join(key_parts)
        return f"cache:{hashlib.md5(key_string.encode()).hexdigest()}"
    
    async def _get_from_cache(self, key: str):
        """从缓存获取数据"""
        try:
            if redis_client:
                return await redis_client.get(key)
        except Exception:
            pass
        return None
    
    async def _save_to_cache(self, key: str, value: bytes):
        """保存到缓存"""
        try:
            if redis_client:
                await redis_client.setex(key, self.expire, value)
        except Exception:
            pass