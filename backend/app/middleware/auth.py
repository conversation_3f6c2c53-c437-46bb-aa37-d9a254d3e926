from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
import logging

logger = logging.getLogger(__name__)

class AuthMiddleware(BaseHTTPMiddleware):
    """认证中间件"""
    
    # 不需要认证的路径
    EXCLUDED_PATHS = [
        "/docs",
        "/redoc",
        "/openapi.json",
        "/api/v1/auth/register",
        "/api/v1/auth/login",
        "/api/v1/auth/send-verification-code",
        "/api/v1/auth/verify-code",
    ]
    
    async def dispatch(self, request: Request, call_next):
        # 检查是否需要认证
        path = request.url.path
        method = request.method

        # OPTIONS请求（CORS预检）直接通过
        if method == "OPTIONS":
            return await call_next(request)

        # 跳过不需要认证的路径
        if any(path.startswith(excluded) for excluded in self.EXCLUDED_PATHS):
            return await call_next(request)

        # 对于需要认证的路径，这里可以添加全局认证逻辑
        # 目前先直接通过，具体认证在各个接口中处理

        return await call_next(request)
