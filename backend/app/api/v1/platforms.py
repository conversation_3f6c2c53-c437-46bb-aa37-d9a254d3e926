"""
平台集成API端点
Platform Integration API Endpoints
"""

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional, List, Dict, Any
from uuid import UUID

from app.database import get_db
from app.dependencies import get_current_user
from app.models.user import User
from app.schemas.platform import *
from app.services.platform_service import (
    PlatformConfigService,
    PlatformCredentialService, 
    PlatformTokenService,
    PlatformRouteService
)
from app.exceptions import NotFoundError, ValidationError, BusinessLogicError

router = APIRouter(
    prefix="/platforms",
    tags=["平台集成"]
)


# ============= 平台配置接口 =============

@router.get("/configs", response_model=PlatformConfigListResponse, summary="获取平台配置列表")
async def get_platform_configs(
    platform_type: Optional[str] = Query(None, description="平台类型筛选"),
    is_active: Optional[bool] = Query(None, description="是否激活"),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    db: AsyncSession = Depends(get_db),
    current_user: Any = Depends(get_current_user)
):
    """
    获取平台配置列表

    - 支持按类型和状态筛选
    - 分页返回结果
    """
    service = PlatformConfigService(db)
    return await service.get_platform_configs(
        platform_type=platform_type,
        is_active=is_active,
        page=page,
        size=size
    )


@router.get("/configs/{platform_code}", response_model=PlatformConfigResponse, summary="获取平台配置详情")
async def get_platform_config(
    platform_code: str,
    db: AsyncSession = Depends(get_db),
    current_user: Any = Depends(get_current_user)
):
    """
    获取平台配置详情
    
    - 返回完整配置信息
    - 包含功能特性和速率限制
    """
    service = PlatformConfigService(db)
    try:
        return await service.get_platform_config(platform_code)
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))


@router.post("/configs", response_model=PlatformConfigResponse, summary="创建平台配置")
async def create_platform_config(
    config_data: PlatformConfigCreate,
    db: AsyncSession = Depends(get_db),
    current_user: Any = Depends(get_current_user)
):
    """
    创建平台配置
    
    - 需要管理员权限
    - 平台代码必须唯一
    """
    # TODO: 检查管理员权限
    service = PlatformConfigService(db)
    try:
        return await service.create_platform_config(config_data)
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/configs/{platform_code}", response_model=PlatformConfigResponse, summary="更新平台配置")
async def update_platform_config(
    platform_code: str,
    update_data: PlatformConfigUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: Any = Depends(get_current_user)
):
    """
    更新平台配置
    
    - 需要管理员权限
    - 只更新提供的字段
    """
    # TODO: 检查管理员权限
    service = PlatformConfigService(db)
    try:
        return await service.update_platform_config(platform_code, update_data)
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))


# ============= 平台凭证接口 =============

@router.get("/credentials", response_model=List[PlatformCredentialResponse], summary="获取平台凭证列表")
async def get_credentials_list(
    db: AsyncSession = Depends(get_db),
    current_user: Any = Depends(get_current_user)
):
    """
    获取平台凭证列表
    
    - 只返回当前用户的凭证
    - 不包含敏感信息
    """
    service = PlatformCredentialService(db)
    # 处理User对象和字典格式
    user_id = str(current_user.id) if hasattr(current_user, 'id') else str(current_user.get('id'))
    return await service.get_credentials_list(user_id=user_id)


@router.get("/credentials/{platform_code}", response_model=PlatformCredentialDetailResponse, summary="获取平台凭证详情")
async def get_credential(
    platform_code: str,
    db: AsyncSession = Depends(get_db),
    current_user: Any = Depends(get_current_user)
):
    """
    获取平台凭证详情
    
    - 返回脱敏后的凭证提示
    - 显示验证状态和过期时间
    """
    service = PlatformCredentialService(db)
    try:
        # Handle both User object and dict formats
        user_id = str(current_user.id) if hasattr(current_user, 'id') else str(current_user.get('id'))
        return await service.get_credential(
            platform_code,
            user_id=user_id
        )
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))


@router.post("/credentials/{platform_code}", response_model=PlatformCredentialResponse, summary="设置平台凭证")
async def set_credential(
    platform_code: str,
    credential_data: PlatformCredentialCreate,
    db: AsyncSession = Depends(get_db),
    current_user: Any = Depends(get_current_user)
):
    """
    设置平台凭证
    
    - 如果已存在则更新
    - 凭证会被加密存储
    """
    service = PlatformCredentialService(db)
    try:
        # Handle both User object and dict formats
        user_id = str(current_user.id) if hasattr(current_user, 'id') else str(current_user.get('id'))
        return await service.set_credential(
            platform_code,
            credential_data,
            user_id=user_id
        )
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))


@router.put("/credentials/{platform_code}", response_model=PlatformCredentialResponse, summary="更新平台凭证")
async def update_credential(
    platform_code: str,
    update_data: PlatformCredentialUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: Any = Depends(get_current_user)
):
    """
    更新平台凭证
    
    - 只更新提供的字段
    - 支持更新有效性状态
    """
    service = PlatformCredentialService(db)
    try:
        # Handle both User object and dict formats
        user_id = str(current_user.id) if hasattr(current_user, 'id') else str(current_user.get('id'))
        return await service.update_credential(
            platform_code,
            update_data,
            user_id=user_id
        )
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))


@router.post("/credentials/{platform_code}/test", response_model=TestConnectionResponse, summary="测试平台连接")
async def test_connection(
    platform_code: str,
    db: AsyncSession = Depends(get_db),
    current_user: Any = Depends(get_current_user)
):
    """
    测试平台连接
    
    - 使用存储的凭证测试连接
    - 返回连接状态和响应时间
    """
    service = PlatformCredentialService(db)
    try:
        # 处理User对象和字典格式
        user_id = str(current_user.id) if hasattr(current_user, 'id') else str(current_user.get('id'))
        return await service.test_connection(
            platform_code,
            user_id=user_id
        )
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))


# ============= 平台令牌接口 =============

@router.get("/tokens/{platform_code}/active", response_model=PlatformTokenResponse, summary="获取活动令牌")
async def get_active_token(
    platform_code: str,
    db: AsyncSession = Depends(get_db),
    current_user: Any = Depends(get_current_user)
):
    """
    获取当前有效令牌
    
    - 返回未过期的活动令牌
    - 不包含实际令牌值
    """
    service = PlatformTokenService(db)
    try:
        return await service.get_active_token(platform_code)
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))


@router.post("/tokens/{platform_code}/refresh", response_model=RefreshTokenResponse, summary="刷新平台令牌")
async def refresh_token(
    platform_code: str,
    request: RefreshTokenRequest = RefreshTokenRequest(),
    db: AsyncSession = Depends(get_db),
    current_user: Any = Depends(get_current_user)
):
    """
    刷新平台令牌
    
    - 自动检查是否需要刷新
    - 支持强制刷新选项
    """
    service = PlatformTokenService(db)
    try:
        return await service.refresh_token(
            platform_code,
            force_refresh=request.force_refresh
        )
    except (NotFoundError, BusinessLogicError) as e:
        raise HTTPException(
            status_code=404 if isinstance(e, NotFoundError) else 400,
            detail=str(e)
        )


@router.post("/tokens/{platform_code}/revoke", response_model=RevokeTokenResponse, summary="撤销平台令牌")
async def revoke_token(
    platform_code: str,
    request: RevokeTokenRequest = RevokeTokenRequest(),
    db: AsyncSession = Depends(get_db),
    current_user: Any = Depends(get_current_user)
):
    """
    撤销平台令牌
    
    - 使所有活动令牌失效
    - 记录撤销原因
    """
    service = PlatformTokenService(db)
    try:
        return await service.revoke_token(
            platform_code,
            reason=request.reason
        )
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))


# ============= 平台路由接口 =============

@router.get("/routes", response_model=PlatformRouteListResponse, summary="查询路由记录")
async def get_routes(
    platform_code: Optional[str] = Query(None, description="平台代码"),
    route_type: Optional[str] = Query(None, description="路由类型"),
    route_status: Optional[str] = Query(None, description="路由状态"),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    db: AsyncSession = Depends(get_db),
    current_user: Any = Depends(get_current_user)
):
    """
    查询路由记录
    
    - 支持多维度筛选
    - 按创建时间倒序排列
    """
    service = PlatformRouteService(db)
    return await service.get_routes(
        platform_code=platform_code,
        route_type=route_type,
        route_status=route_status,
        page=page,
        size=size
    )


@router.get("/routes/{route_id}", response_model=PlatformRouteDetailResponse, summary="获取路由详情")
async def get_route_detail(
    route_id: UUID,
    db: AsyncSession = Depends(get_db),
    current_user: Any = Depends(get_current_user)
):
    """
    获取路由详情
    
    - 包含请求和响应数据
    - 关联平台配置信息
    """
    service = PlatformRouteService(db)
    try:
        return await service.get_route_detail(str(route_id))
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))


@router.post("/routes", response_model=PlatformRouteResponse, summary="创建路由")
async def create_route(
    route_data: PlatformRouteCreate,
    db: AsyncSession = Depends(get_db),
    current_user: Any = Depends(get_current_user)
):
    """
    创建路由（发送到平台）
    
    - 自动路由到指定平台
    - 记录请求响应数据
    """
    service = PlatformRouteService(db)
    try:
        return await service.create_route(route_data)
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))


@router.post("/routes/{route_id}/retry", response_model=RetryRouteResponse, summary="重试路由")
async def retry_route(
    route_id: UUID,
    request: RetryRouteRequest = RetryRouteRequest(),
    db: AsyncSession = Depends(get_db),
    current_user: Any = Depends(get_current_user)
):
    """
    重试发送
    
    - 支持失败路由重试
    - 可配置重试次数和延迟
    """
    service = PlatformRouteService(db)
    try:
        return await service.retry_route(
            str(route_id),
            max_retries=request.max_retries,
            retry_delay=request.retry_delay
        )
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))