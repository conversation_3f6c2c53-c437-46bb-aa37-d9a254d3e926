"""
管理员套餐管理API
Admin Subscription Plans Management API

本模块仅包含管理员套餐管理功能：
- 用户套餐查看功能已移至 user_subscriptions.py
- 此文件专门处理管理员的套餐CRUD操作
"""

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from app.database import get_db
from app.dependencies import get_current_user
from app.services.subscription_service import SubscriptionService
from app.schemas.subscription import *
from app.exceptions import NotFoundError, ValidationError
from typing import List, Optional

# 管理员套餐管理路由
admin_plans_router = APIRouter()

# =====================================================
# 管理员套餐管理接口 (5个) - 放在前面避免路由冲突
# =====================================================

@admin_plans_router.get("/plans/management", response_model=List[SubscriptionPlanResponse], summary="管理员获取套餐列表")
async def admin_get_subscription_plans(
    target_user_type: Optional[str] = Query(None, description="按用户类型筛选: enterprise/provider/both"),
    active_only: Optional[bool] = Query(None, description="按激活状态筛选"),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, le=100, description="每页数量"),
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """管理员获取套餐列表（支持查看所有状态的套餐）"""
    user_roles = current_user.get("roles", [])
    if "admin" not in user_roles and "super_admin" not in user_roles:
        raise HTTPException(status_code=403, detail="Admin access required")
    
    service = SubscriptionService(db)
    # 管理员可以查看所有状态的套餐
    active_filter = active_only if active_only is not None else True
    plans = await service.get_plans(target_user_type, active_filter)
    
    # 应用分页
    start = (page - 1) * size
    end = start + size
    return plans[start:end]

@admin_plans_router.get("/plans/management/{plan_id}", response_model=SubscriptionPlanResponse, summary="管理员获取套餐详情")
async def admin_get_subscription_plan_detail(
    plan_id: str,
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """管理员获取指定订阅套餐的详细信息"""
    user_roles = current_user.get("roles", [])
    if "admin" not in user_roles and "super_admin" not in user_roles:
        raise HTTPException(status_code=403, detail="Admin access required")
    
    service = SubscriptionService(db)
    try:
        plan = await service.get_plan_by_id(plan_id)
        return service._plan_to_response(plan)
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))

@admin_plans_router.post("/plans/management", response_model=SubscriptionPlanResponse, summary="创建订阅套餐")
async def create_subscription_plan(
    request: SubscriptionPlanCreate,
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """创建新的订阅套餐（仅管理员）"""
    user_roles = current_user.get("roles", [])
    if "admin" not in user_roles and "super_admin" not in user_roles:
        raise HTTPException(status_code=403, detail="Admin access required")

    service = SubscriptionService(db)
    try:
        return await service.create_plan(request)
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))

@admin_plans_router.put("/plans/management/{plan_id}", response_model=SubscriptionPlanResponse, summary="更新订阅套餐")
async def update_subscription_plan(
    plan_id: str,
    request: SubscriptionPlanUpdate,
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """更新订阅套餐信息（仅管理员）"""
    user_roles = current_user.get("roles", [])
    if "admin" not in user_roles and "super_admin" not in user_roles:
        raise HTTPException(status_code=403, detail="Admin access required")

    service = SubscriptionService(db)
    try:
        return await service.update_plan(plan_id, request)
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))

@admin_plans_router.delete("/plans/management/{plan_id}", summary="删除订阅套餐")
async def delete_subscription_plan(
    plan_id: str,
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """删除订阅套餐（硬删除，永久删除，仅管理员）"""
    user_roles = current_user.get("roles", [])
    if "admin" not in user_roles and "super_admin" not in user_roles:
        raise HTTPException(status_code=403, detail="Admin access required")

    service = SubscriptionService(db)
    try:
        return await service.delete_plan(plan_id)
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))

@admin_plans_router.patch("/plans/management/{plan_id}/status", summary="更新套餐状态")
async def update_plan_status(
    plan_id: str,
    request: SubscriptionPlanStatusUpdate,
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """更新套餐激活状态（仅管理员）"""
    user_roles = current_user.get("roles", [])
    if "admin" not in user_roles and "super_admin" not in user_roles:
        raise HTTPException(status_code=403, detail="Admin access required")

    service = SubscriptionService(db)
    try:
        # 使用update_plan方法来更新状态
        update_data = SubscriptionPlanUpdate(is_active=request.is_active)
        result = await service.update_plan(plan_id, update_data)
        
        return {
            "success": True,
            "message": f"套餐状态已更新为{'激活' if request.is_active else '禁用'}",
            "plan": result,
            "reason": request.reason
        }
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))

# =====================================================
# 注意：用户套餐查看功能已移至 user_subscriptions.py
# 此文件仅保留管理员套餐管理功能
# =====================================================
