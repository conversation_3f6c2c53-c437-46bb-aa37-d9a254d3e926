from fastapi import APIRouter, Depends, HTTPException, status, Query, Path
from sqlalchemy.ext.asyncio import AsyncSession
from app.database import get_db
from app.services.monitoring_service import MonitoringService
from app.schemas.monitoring import *
from app.exceptions import *
from app.dependencies import get_current_user
from app.core.permissions import check_permission, require_admin_permission
from typing import Dict, Any
from datetime import datetime

router = APIRouter()

@router.post("/projects", response_model=MonitoringApiResponse, summary="创建监控项目")
async def create_monitoring_project(
    request: MonitoringProjectCreate,
    current_user: dict = Depends(check_permission("monitoring.projects.view")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    创建AI搜索引擎监控项目

    - **project_name**: 项目名称（必填，2-200字符）
    - **target_website**: 目标网站（必填）
    - **target_brand**: 目标品牌（必填）
    - **keywords**: 监控关键词列表（必填，1-50个）
    - **search_engines**: 监控的AI搜索引擎（必填）
      - doubao: 豆包AI
      - chatgpt: ChatGPT
      - claude: Claude
      - gemini: Gemini
      - tongyi: 通义千问
      - wenxin: 文心一言
    - **monitoring_frequency**: 监控频率（daily/weekly/monthly）
    - **competitors**: 竞争对手列表（可选）

    创建后系统将自动开始监控任务
    """
    try:
        monitoring_service = MonitoringService(db)
        result = await monitoring_service.create_monitoring_project(current_user["id"], request)

        return {
            "success": True,
            "data": result,
            "message": "监控项目创建成功，系统将开始监控"
        }
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="创建监控项目失败")

@router.get("/projects", response_model=MonitoringApiResponse, summary="获取监控项目列表")
async def get_monitoring_projects(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    status: str = Query(None, description="项目状态筛选"),
    keyword: str = Query(None, description="关键词搜索"),
    sort_by: str = Query("created_at", description="排序字段"),
    sort_order: str = Query("desc", description="排序方式"),
    current_user: dict = Depends(check_permission("monitoring.projects.view")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    获取用户的监控项目列表

    支持以下功能：
    - **分页查询**: 通过page和size参数控制
    - **状态筛选**: 按项目状态筛选
      - active: 活跃监控
      - paused: 暂停监控
      - completed: 已完成
      - cancelled: 已取消
    - **关键词搜索**: 搜索项目名称或品牌
    - **排序**: 支持多字段排序

    返回项目列表、分页信息和统计数据
    """
    try:
        query = ProjectListQuery(
            page=page,
            size=size,
            status=status,
            keyword=keyword,
            sort_by=sort_by,
            sort_order=sort_order
        )

        monitoring_service = MonitoringService(db)
        result = await monitoring_service.get_project_list(query, current_user["id"])

        return {
            "success": True,
            "data": result,
            "message": "获取监控项目列表成功"
        }
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="获取监控项目列表失败")

@router.get("/projects/{project_id}", response_model=MonitoringApiResponse, summary="获取监控项目详情")
async def get_monitoring_project(
    project_id: str = Path(..., description="项目ID"),
    current_user: dict = Depends(check_permission("monitoring.projects.view")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    获取指定监控项目的详细信息

    - **project_id**: 项目ID（必填）

    返回项目的完整信息，包括配置、统计数据等
    """
    try:
        monitoring_service = MonitoringService(db)
        result = await monitoring_service.get_project_detail(current_user["id"], project_id)

        return {
            "success": True,
            "data": result,
            "message": "获取项目详情成功"
        }
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="获取项目详情失败")

@router.put("/projects/{project_id}", response_model=MonitoringApiResponse, summary="更新监控项目")
async def update_monitoring_project(
    request: MonitoringProjectCreate,
    project_id: str = Path(..., description="项目ID"),
    current_user: dict = Depends(check_permission("monitoring.projects.create")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    更新AI搜索引擎监控项目

    - **project_id**: 项目ID（必填）
    - **project_name**: 项目名称（必填，2-200字符）
    - **target_website**: 目标网站（必填）
    - **target_brand**: 目标品牌（必填）
    - **keywords**: 监控关键词列表（必填，1-50个）
    - **search_engines**: 监控的AI搜索引擎（必填）
      - doubao: 豆包AI
      - chatgpt: ChatGPT
      - claude: Claude
      - gemini: Gemini
      - tongyi: 通义千问
      - wenxin: 文心一言
    - **monitoring_frequency**: 监控频率（daily/weekly/monthly）
    - **competitors**: 竞争对手列表（可选）

    更新后系统将根据新配置调整监控任务
    """
    try:
        monitoring_service = MonitoringService(db)
        result = await monitoring_service.update_monitoring_project(current_user["id"], project_id, request)

        return {
            "success": True,
            "data": result,
            "message": "监控项目更新成功"
        }
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="更新监控项目失败")

@router.get("/projects/{project_id}/rankings", response_model=MonitoringApiResponse, summary="获取项目排名数据")
async def get_project_rankings(
    project_id: str = Path(..., description="项目ID"),
    start_date: datetime = Query(None, description="开始日期"),
    end_date: datetime = Query(None, description="结束日期"),
    keyword: str = Query(None, description="关键词筛选"),
    search_engine: str = Query(None, description="搜索引擎筛选"),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(50, ge=1, le=100, description="每页数量"),
    current_user: dict = Depends(check_permission("monitoring.projects.view")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    获取监控项目的排名数据

    支持以下功能：
    - **时间筛选**: 按监控时间筛选数据
    - **关键词筛选**: 按特定关键词筛选
    - **搜索引擎筛选**: 按特定AI搜索引擎筛选
    - **分页查询**: 通过page和size参数控制

    返回数据包括：
    - **排名记录**: 详细的排名数据列表
    - **趋势数据**: 关键词排名趋势
    - **统计信息**: 项目整体统计数据

    排名数据说明：
    - ranking_position: 排名位置（null表示未找到）
    - result_score: 相关性评分（0-1）
    - competitor_rankings: 竞争对手排名情况
    """
    try:
        query = RankingQuery(
            start_date=start_date,
            end_date=end_date,
            keyword=keyword,
            search_engine=search_engine,
            page=page,
            size=size
        )

        monitoring_service = MonitoringService(db)
        result = await monitoring_service.get_project_rankings(project_id, query, current_user["id"])

        return {
            "success": True,
            "data": result,
            "message": "获取项目排名数据成功"
        }
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except PermissionError as e:
        raise HTTPException(status_code=403, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="获取项目排名数据失败")
