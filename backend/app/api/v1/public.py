"""
公开API端点 - 不需要认证的接口
"""
from fastapi import APIRouter, HTTPException, Depends
from fastapi.responses import HTMLResponse
from typing import Dict, Any
from app.schemas.channel_category import CHANNEL_TYPE_VALUES
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession
from app.database import get_db

router = APIRouter()

@router.get("/channel-types", summary="获取频道类型枚举值")
async def get_channel_types() -> Dict[str, Any]:
    """
    获取频道类型的所有枚举值
    这是一个公开接口，不需要认证
    """
    return {
        "code": 200,
        "message": "获取成功",
        "data": CHANNEL_TYPE_VALUES
    }

# ❌ 临时迁移接口已禁用 - 避免启动错误
# @router.post("/migrate-database", summary="执行数据库迁移")
# async def migrate_database(db: AsyncSession = Depends(get_db)) -> Dict[str, Any]:
#     """
#     执行数据库迁移 - 删除不需要的字段
#     这是一个临时的迁移接口 - 已禁用避免启动错误
#     """
#     return {"code": 200, "message": "迁移接口已禁用", "data": None}


# ❌ 已删除有问题的迁移函数 - 避免启动错误

@router.get("/migration-test", response_class=HTMLResponse, summary="数据库迁移测试页面")
async def migration_test_page():
    """
    返回数据库迁移测试页面
    """
    html_content = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库迁移测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        .button:hover {
            background-color: #0056b3;
        }
        .button.danger {
            background-color: #dc3545;
        }
        .button.danger:hover {
            background-color: #c82333;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 数据库迁移测试</h1>

        <div style="text-align: center;">
            <button class="button" onclick="testConnection()">测试数据库连接</button>
            <button class="button danger" onclick="runMigration()">执行数据库迁移</button>
            <button class="button" onclick="getChannelTypes()">获取频道类型</button>
        </div>

        <div id="result"></div>
    </div>

    <script>
        function showResult(message, type = 'info') {
            const resultDiv = document.getElementById('result');
            resultDiv.className = `result ${type}`;
            resultDiv.textContent = message;
        }

        async function testConnection() {
            showResult('正在测试数据库连接...', 'info');
            try {
                const response = await fetch('/api/v1/public/channel-types');
                const result = await response.json();

                if (result.code === 200) {
                    showResult('✅ 数据库连接成功！\\n获取到频道类型数量: ' + result.data.length, 'success');
                } else {
                    showResult('❌ 数据库连接失败: ' + result.message, 'error');
                }
            } catch (error) {
                showResult('❌ 连接失败: ' + error.message, 'error');
            }
        }

        async function runMigration() {
            if (!confirm('⚠️ 即将执行数据库迁移，删除不需要的字段。此操作不可逆，是否继续？')) {
                return;
            }

            showResult('🚀 正在执行数据库迁移...', 'info');
            try {
                const response = await fetch('/api/v1/public/migrate-database', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                const result = await response.json();

                if (result.code === 200) {
                    let message = '🎉 数据库迁移完成！\\n\\n';
                    message += '删除的字段: ' + JSON.stringify(result.data.deleted_fields, null, 2) + '\\n\\n';
                    message += '迁移日志:\\n' + result.data.migration_log.join('\\n');
                    showResult(message, 'success');
                } else {
                    showResult('❌ 迁移失败: ' + result.message, 'error');
                }
            } catch (error) {
                showResult('❌ 迁移失败: ' + error.message, 'error');
            }
        }

        async function getChannelTypes() {
            showResult('正在获取频道类型...', 'info');
            try {
                const response = await fetch('/api/v1/public/channel-types');
                const result = await response.json();

                if (result.code === 200) {
                    let message = '✅ 获取频道类型成功！\\n\\n';
                    message += '频道类型列表:\\n' + result.data.map((type, index) => `${index + 1}. ${type}`).join('\\n');
                    showResult(message, 'success');
                } else {
                    showResult('❌ 获取失败: ' + result.message, 'error');
                }
            } catch (error) {
                showResult('❌ 获取失败: ' + error.message, 'error');
            }
        }

        // 页面加载时自动测试连接
        window.onload = function() {
            testConnection();
        };
    </script>
</body>
</html>
    """
    return HTMLResponse(content=html_content)
