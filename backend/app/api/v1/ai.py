from fastapi import APIRouter, Depends, HTTPException, status, Query, Path
from fastapi.responses import StreamingResponse
from sqlalchemy.ext.asyncio import AsyncSession
from app.database import get_db
from app.services.ai_service import AIService, QuotaExceededError
from app.schemas.ai_service import *
from app.exceptions import *
from app.dependencies import get_current_user
from app.core.permissions import check_permission, require_admin_permission
from typing import Dict, Any, AsyncGenerator
from datetime import datetime
from pydantic import BaseModel
import json
import asyncio
import logging

logger = logging.getLogger(__name__)

router = APIRouter()
template_router = APIRouter()

@router.post("/content/generate", summary="AI内容生成（流式返回）")
async def generate_content(
    request: ContentGenerationRequest,
    current_user: dict = Depends(check_permission("ai.content.generate")),
    db: AsyncSession = Depends(get_db)
):
    """
    使用AI生成内容（流式返回）

    - **content_type**: 内容类型（必填）
      - article: 文章
      - social_post: 社交媒体帖子
      - product_description: 产品描述
      - email: 邮件内容
      - blog_post: 博客文章
    - **topic**: 内容主题（必填，2-200字符）
    - **keywords**: 关键词列表（必填，1-10个）
    - **target_audience**: 目标受众（可选）
    - **tone**: 语调（professional/casual/friendly/formal）
    - **length**: 内容长度（short/medium/long）
    - **language**: 语言（默认zh-CN）
    - **additional_requirements**: 额外要求（可选）
    - **ai_model**: AI模型（当前只支持doubao）

    返回Server-Sent Events流，实时推送生成的内容
    """

    async def generate_stream():
        try:
            logger.info(f"🎬 路由开始处理流式生成请求 - 用户: {current_user['id']}")
            ai_service = AIService(db)

            # 发送开始事件
            yield f"data: {json.dumps({'type': 'start', 'message': '开始生成内容...'}, ensure_ascii=False)}\n\n"

            logger.info(f"🎬 准备调用ai_service.generate_content_stream")
            # 流式生成内容
            async for chunk in ai_service.generate_content_stream(current_user["id"], request):
                logger.debug(f"🎬 路由收到流式数据块: {chunk}")
                yield f"data: {json.dumps(chunk, ensure_ascii=False)}\n\n"
                await asyncio.sleep(0.01)  # 小延迟确保流畅传输

            # 发送完成事件
            logger.info(f"🎬 流式生成完成，发送完成事件")
            yield f"data: {json.dumps({'type': 'complete', 'message': '内容生成完成'}, ensure_ascii=False)}\n\n"

        except QuotaExceededError as e:
            logger.error(f"🎬 配额异常: {e}")
            yield f"data: {json.dumps({'type': 'error', 'message': str(e)}, ensure_ascii=False)}\n\n"
        except ValidationError as e:
            logger.error(f"🎬 验证异常: {e}")
            yield f"data: {json.dumps({'type': 'error', 'message': str(e)}, ensure_ascii=False)}\n\n"
        except Exception as e:
            logger.error(f"🎬 路由异常: {e}")
            import traceback
            logger.error(f"🎬 异常堆栈: {traceback.format_exc()}")
            yield f"data: {json.dumps({'type': 'error', 'message': '内容生成失败'}, ensure_ascii=False)}\n\n"

    return StreamingResponse(
        generate_stream(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream"
        }
    )


@router.post("/content/generate/v2", summary="AI内容生成V2（支持对话和知识库）")
async def generate_content_v2(
    request: ContentGenerationRequest,
    current_user: dict = Depends(check_permission("ai.content.generate")),
    db: AsyncSession = Depends(get_db)
):
    """
    使用AI生成内容V2版本（支持对话上下文和知识库增强）

    新增功能：
    - **conversation_id**: 对话ID（可选，用于上下文连续性）
    - **template_id**: 模板ID（可选，首次请求时使用）
    - **template_parameters**: 模板参数（可选）
    - **knowledge_bases**: 知识库ID列表（可选，用于内容增强）
    - **is_first_request**: 是否首次请求（默认true）
    - **context_messages**: 上下文消息（可选）

    返回Server-Sent Events流，实时推送生成的内容
    """

    async def generate_stream_v2():
        try:
            ai_service = AIService(db)

            # 发送开始事件
            start_event = {
                'type': 'start',
                'message': '开始生成内容...',
                'conversation_id': request.conversation_id,
                'template_id': request.template_id,
                'knowledge_bases': request.knowledge_bases
            }
            yield f"data: {json.dumps(start_event, ensure_ascii=False)}\n\n"

            # 使用V2版本的流式生成
            async for chunk in ai_service.generate_content_stream_v2(current_user["id"], request):
                yield f"data: {json.dumps(chunk, ensure_ascii=False)}\n\n"
                await asyncio.sleep(0.01)  # 小延迟确保流畅传输

            # 发送完成事件
            complete_event = {
                'type': 'complete',
                'message': '内容生成完成',
                'conversation_id': request.conversation_id
            }
            yield f"data: {json.dumps(complete_event, ensure_ascii=False)}\n\n"

        except QuotaExceededError as e:
            error_event = {
                'type': 'error',
                'message': str(e),
                'conversation_id': request.conversation_id
            }
            yield f"data: {json.dumps(error_event, ensure_ascii=False)}\n\n"
        except ValidationError as e:
            error_event = {
                'type': 'error',
                'message': str(e),
                'conversation_id': request.conversation_id
            }
            yield f"data: {json.dumps(error_event, ensure_ascii=False)}\n\n"
        except Exception as e:
            error_event = {
                'type': 'error',
                'message': '内容生成失败，请稍后重试',
                'conversation_id': request.conversation_id
            }
            yield f"data: {json.dumps(error_event, ensure_ascii=False)}\n\n"

    return StreamingResponse(
        generate_stream_v2(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "Cache-Control"
        }
    )

@router.post("/content/optimize", response_model=AIApiResponse, summary="内容优化建议")
async def optimize_content(
    request: ContentOptimizationRequest,
    current_user: dict = Depends(check_permission("ai.content.generate")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    AI内容优化建议

    - **original_content**: 原始内容（必填，10-10000字符）
    - **optimization_goals**: 优化目标（必填）
      - seo: SEO优化
      - readability: 可读性优化
      - engagement: 参与度优化
      - conversion: 转化率优化
    - **target_keywords**: 目标关键词（可选，最多10个）
    - **target_audience**: 目标受众（可选）
    - **platform**: 发布平台（wechat/weibo/xiaohongshu/website）
    - **ai_model**: AI模型（当前只支持doubao）

    返回优化后的内容、优化摘要、具体改进点和评分
    """
    try:
        ai_service = AIService(db)
        result = await ai_service.optimize_content(current_user["id"], request)

        return {
            "success": True,
            "data": result,
            "message": "内容优化完成"
        }
    except QuotaExceededError as e:
        raise HTTPException(status_code=429, detail=str(e))
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="内容优化失败")

@router.post("/keywords/analyze", response_model=AIApiResponse, summary="关键词分析")
async def analyze_keywords(
    request: KeywordAnalysisRequest,
    current_user: dict = Depends(check_permission("ai.content.generate")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    AI关键词分析

    - **keywords**: 关键词列表（必填，1-20个）
    - **industry**: 行业（可选）
    - **target_region**: 目标地区（默认China）
    - **analysis_depth**: 分析深度（basic/standard/deep）
    - **include_competitors**: 是否包含竞争分析（默认false）
    - **ai_model**: AI模型（当前只支持doubao）

    返回关键词洞察、趋势分析、竞争分析和优化建议
    """
    try:
        ai_service = AIService(db)
        result = await ai_service.analyze_keywords(current_user["id"], request)

        return {
            "success": True,
            "data": result,
            "message": "关键词分析完成"
        }
    except QuotaExceededError as e:
        raise HTTPException(status_code=429, detail=str(e))
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="关键词分析失败")

@router.get("/usage/history", response_model=AIApiResponse, summary="获取AI使用记录")
async def get_usage_history(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    service_type: str = Query(None, description="服务类型筛选"),
    status: str = Query(None, description="状态筛选"),
    start_date: datetime = Query(None, description="开始日期"),
    end_date: datetime = Query(None, description="结束日期"),
    sort_by: str = Query("created_at", description="排序字段"),
    sort_order: str = Query("desc", description="排序方式"),
    current_user: dict = Depends(check_permission("ai.content.generate")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    获取用户的AI服务使用记录

    支持以下功能：
    - **分页查询**: 通过page和size参数控制
    - **服务类型筛选**: 按AI服务类型筛选
      - content_generation: 内容生成
      - content_optimization: 内容优化
      - keyword_analysis: 关键词分析
      - seo_analysis: SEO分析
      - trend_analysis: 趋势分析
    - **状态筛选**: 按处理状态筛选
      - pending: 处理中
      - completed: 已完成
      - failed: 失败
      - cancelled: 已取消
    - **时间筛选**: 按创建时间筛选
    - **排序**: 支持多字段排序

    返回使用记录列表、分页信息和统计数据
    """
    try:
        query = AIUsageQuery(
            page=page,
            size=size,
            service_type=service_type,
            status=status,
            start_date=start_date,
            end_date=end_date,
            sort_by=sort_by,
            sort_order=sort_order
        )

        ai_service = AIService(db)
        result = await ai_service.get_usage_history(query, current_user["id"])

        return {
            "success": True,
            "data": result,
            "message": "获取使用记录成功"
        }
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="获取使用记录失败")

@router.get("/quota/status", response_model=AIApiResponse, summary="获取AI配额状态")
async def get_quota_status(
    current_user: dict = Depends(check_permission("ai.content.generate")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    获取用户的AI服务配额状态

    返回各种AI服务的配额信息：
    - **service_type**: 服务类型
    - **total_quota**: 总配额
    - **used_quota**: 已使用配额
    - **remaining_quota**: 剩余配额
    - **quota_period**: 配额周期（monthly）
    - **period_start**: 周期开始时间
    - **period_end**: 周期结束时间
    - **usage_percentage**: 使用百分比

    默认配额：
    - 内容生成：100次/月
    - 内容优化：50次/月
    - 关键词分析：30次/月
    """
    try:
        ai_service = AIService(db)
        result = await ai_service.get_quota_status(current_user["id"])

        return {
            "success": True,
            "data": result,
            "message": "获取配额状态成功"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail="获取配额状态失败")

# AI模板管理接口
@template_router.post("/templates", response_model=AIApiResponse, summary="创建AI模板")
async def create_template(
    request: AITemplateCreate,
    current_user: dict = Depends(check_permission("ai.template.create")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    创建AI模板

    - **template_name**: 模板名称（必填，2-200字符）
    - **template_description**: 模板描述（可选）
    - **prompt_template**: 提示词模板（必填，至少10字符）
    - **default_parameters**: 默认参数（可选，JSON格式）

    创建后可在AI服务中使用该模板
    """
    try:
        ai_service = AIService(db)
        result = await ai_service.create_template(current_user["id"], request)

        return {
            "success": True,
            "data": result,
            "message": "模板创建成功"
        }
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="模板创建失败")

@template_router.get("/templates", response_model=AIApiResponse, summary="获取AI模板列表")
async def get_template_list(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    is_active: bool = Query(None, description="状态筛选"),
    keyword: str = Query(None, description="关键词搜索"),
    sort_by: str = Query("created_at", description="排序字段"),
    sort_order: str = Query("desc", description="排序方式"),
    current_user: dict = Depends(check_permission("ai.template.view")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    获取AI模板列表

    支持以下功能：
    - **分页查询**: 通过page和size参数控制
    - **状态筛选**: 按激活状态筛选
    - **关键词搜索**: 搜索模板名称和描述
    - **排序**: 支持多字段排序

    返回模板列表、分页信息和统计数据
    """
    try:
        query = AITemplateListQuery(
            page=page,
            size=size,
            is_active=is_active,
            keyword=keyword,
            sort_by=sort_by,
            sort_order=sort_order
        )

        ai_service = AIService(db)
        result = await ai_service.get_template_list(query, current_user["id"])

        return {
            "success": True,
            "data": result,
            "message": "获取模板列表成功"
        }
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="获取模板列表失败")

@template_router.get("/templates/{template_id}", response_model=AIApiResponse, summary="获取AI模板详情")
async def get_template_detail(
    template_id: str = Path(..., description="模板ID"),
    current_user: dict = Depends(check_permission("ai.template.view")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    获取指定AI模板的详细信息

    - **template_id**: 模板ID（必填）

    返回模板的完整信息，包括配置、统计数据等
    """
    try:
        ai_service = AIService(db)
        result = await ai_service.get_template_detail(template_id, current_user["id"])

        return {
            "success": True,
            "data": result,
            "message": "获取模板详情成功"
        }
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except PermissionError as e:
        raise HTTPException(status_code=403, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="获取模板详情失败")

@template_router.put("/templates/{template_id}", response_model=AIApiResponse, summary="更新AI模板")
async def update_template(
    template_id: str = Path(..., description="模板ID"),
    request: AITemplateUpdate = ...,
    current_user: dict = Depends(check_permission("ai.template.update")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    更新AI模板

    - **template_id**: 模板ID（必填）
    - **template_name**: 模板名称（可选）
    - **template_description**: 模板描述（可选）
    - **prompt_template**: 提示词模板（可选）
    - **default_parameters**: 默认参数（可选）
    - **is_active**: 是否激活（可选）

    只有模板创建者可以更新模板
    """
    try:
        ai_service = AIService(db)
        result = await ai_service.update_template(template_id, request, current_user["id"])

        return {
            "success": True,
            "data": result,
            "message": "模板更新成功"
        }
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except PermissionError as e:
        raise HTTPException(status_code=403, detail=str(e))
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="模板更新失败")

@template_router.delete("/templates/{template_id}", response_model=AIApiResponse, summary="删除AI模板")
async def delete_template(
    template_id: str = Path(..., description="模板ID"),
    current_user: dict = Depends(check_permission("ai.template.delete")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    删除AI模板（软删除）

    - **template_id**: 模板ID（必填）

    只有模板创建者可以删除模板
    删除后模板将不再显示在列表中，但历史数据保留
    """
    try:
        ai_service = AIService(db)
        result = await ai_service.delete_template(template_id, current_user["id"])

        return {
            "success": True,
            "data": {"deleted": result},
            "message": "模板删除成功"
        }
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except PermissionError as e:
        raise HTTPException(status_code=403, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="模板删除失败")


# HTML生成相关模型和端点
class HTMLGenerationRequest(BaseModel):
    prompt: str
    role: str = "admin"
    module: str = "irrigation"
    style: str = "modern"

@router.post("/html/generate", summary="AI HTML页面生成（流式返回）")
async def generate_html(
    request: HTMLGenerationRequest,
    db: AsyncSession = Depends(get_db)
):
    """
    使用AI生成HTML页面（流式返回）

    - **prompt**: 用户需求描述
    - **role**: 用户角色（admin, farmer, expert等）
    - **module**: 页面模块（irrigation, monitoring等）
    - **style**: 页面风格（modern, classic等）

    返回流式HTML内容
    """

    async def generate_html_stream():
        try:
            logger.info(f"🎨 开始生成HTML页面 - 角色: {request.role}, 模块: {request.module}")

            # 构建HTML生成提示词
            html_prompt = f"""
请根据以下需求生成一个完整的HTML单页面：

用户角色：{request.role}
页面模块：{request.module}
用户需求：{request.prompt}
页面风格：{request.style}

要求：
1. 生成完整的HTML页面，包含DOCTYPE、head、body等标签
2. 在<head>中包含<style>标签，内嵌现代化的CSS样式，使用响应式设计
3. 在页面底部包含<script>标签，内嵌必要的JavaScript交互功能
4. 页面内容要符合{request.role}角色在{request.module}模块的使用场景
5. 使用中文界面和内容
6. 页面要美观、实用、易用
7. 确保所有CSS和JavaScript都内嵌在HTML文件中，形成一个完整的单页面

请直接返回完整的HTML代码，不要包含任何解释文字。
"""

            # 创建内容生成请求
            content_request = ContentGenerationRequest(
                content_type="article",
                topic=request.prompt,
                additional_requirements=html_prompt,
                keywords=[request.role, request.module, "HTML", "响应式", "现代化"],
                target_audience="开发者",
                tone="professional",
                length="long",
                language="中文",
                ai_model="doubao"
            )

            # 发送开始事件
            yield f"data: {json.dumps({'type': 'start', 'message': 'AI正在生成您的网页...'}, ensure_ascii=False)}\n\n"

            # 模拟生成HTML内容（演示版本）
            html_content = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{request.role}角色 - {request.module}模块</title>
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}

        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }}

        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }}

        .header {{
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            padding: 30px;
            text-align: center;
        }}

        .header h1 {{
            font-size: 2.5em;
            margin-bottom: 10px;
        }}

        .header p {{
            font-size: 1.2em;
            opacity: 0.9;
        }}

        .content {{
            padding: 40px;
        }}

        .dashboard {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }}

        .card {{
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            border-left: 5px solid #4CAF50;
            transition: transform 0.3s ease;
        }}

        .card:hover {{
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }}

        .card h3 {{
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3em;
        }}

        .card p {{
            color: #666;
            line-height: 1.6;
        }}

        .stats {{
            display: flex;
            justify-content: space-around;
            background: #e8f5e8;
            padding: 30px;
            border-radius: 10px;
            margin-top: 30px;
        }}

        .stat-item {{
            text-align: center;
        }}

        .stat-number {{
            font-size: 2.5em;
            font-weight: bold;
            color: #4CAF50;
        }}

        .stat-label {{
            color: #666;
            margin-top: 5px;
        }}

        @media (max-width: 768px) {{
            .dashboard {{
                grid-template-columns: 1fr;
            }}

            .stats {{
                flex-direction: column;
                gap: 20px;
            }}

            .header h1 {{
                font-size: 2em;
            }}
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌱 {request.prompt}</h1>
            <p>为{request.role}角色定制的{request.module}管理界面</p>
        </div>

        <div class="content">
            <div class="dashboard">
                <div class="card">
                    <h3>🌾 作物状态监控</h3>
                    <p>实时监控农作物的生长状态，包括土壤湿度、温度、光照等关键指标，确保作物健康成长。</p>
                </div>

                <div class="card">
                    <h3>📊 产量预测分析</h3>
                    <p>基于历史数据和当前生长状况，智能预测本季度的产量情况，帮助制定合理的销售计划。</p>
                </div>

                <div class="card">
                    <h3>🌤️ 天气信息服务</h3>
                    <p>提供精准的天气预报和农业气象服务，帮助农户合理安排农事活动，降低天气风险。</p>
                </div>

                <div class="card">
                    <h3>⚙️ 设备运行状态</h3>
                    <p>监控灌溉设备、温室控制系统等农业设备的运行状态，及时发现并处理设备故障。</p>
                </div>
            </div>

            <div class="stats">
                <div class="stat-item">
                    <div class="stat-number">85%</div>
                    <div class="stat-label">作物健康度</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">1,250</div>
                    <div class="stat-label">预计产量(kg)</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">23°C</div>
                    <div class="stat-label">当前温度</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">98%</div>
                    <div class="stat-label">设备正常率</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 简单的交互功能
        document.addEventListener('DOMContentLoaded', function() {{
            const cards = document.querySelectorAll('.card');

            cards.forEach(card => {{
                card.addEventListener('click', function() {{
                    alert('功能开发中，敬请期待！');
                }});
            }});

            // 模拟数据更新
            setInterval(function() {{
                const tempElement = document.querySelector('.stat-item:nth-child(3) .stat-number');
                if (tempElement) {{
                    const currentTemp = parseInt(tempElement.textContent);
                    const newTemp = currentTemp + Math.floor(Math.random() * 3) - 1;
                    tempElement.textContent = newTemp + '°C';
                }}
            }}, 5000);
        }});
    </script>
</body>
</html>"""

            # 分块发送HTML内容
            chunk_size = 100
            for i in range(0, len(html_content), chunk_size):
                chunk = html_content[i:i + chunk_size]
                yield f"data: {json.dumps({'type': 'content', 'content': chunk}, ensure_ascii=False)}\n\n"
                await asyncio.sleep(0.01)

            # 发送完成事件
            yield f"data: {json.dumps({'type': 'complete', 'message': 'HTML页面生成完成'}, ensure_ascii=False)}\n\n"

        except Exception as e:
            logger.error(f"🎨 HTML生成失败: {str(e)}")
            import traceback
            logger.error(f"🎨 异常堆栈: {traceback.format_exc()}")
            yield f"data: {json.dumps({'type': 'error', 'message': 'HTML页面生成失败'}, ensure_ascii=False)}\n\n"

    return StreamingResponse(
        generate_html_stream(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream"
        }
    )
