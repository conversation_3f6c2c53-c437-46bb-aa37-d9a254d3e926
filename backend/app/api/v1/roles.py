"""
角色管理 CRUD 接口
只有超级管理员可以操作
"""
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, func, or_
from typing import List, Optional
from pydantic import BaseModel, Field
from datetime import datetime

from app.database import get_db
from app.dependencies import get_current_super_admin
from app.models.permission import Role, RolePermission, Permission

router = APIRouter()


# 请求和响应模型
class RoleCreateRequest(BaseModel):
    role_name: str = Field(..., min_length=1, max_length=50, description="角色名称")
    role_code: str = Field(..., min_length=1, max_length=50, description="角色代码")
    role_type: str = Field(..., min_length=1, max_length=20, description="角色类型")
    description: Optional[str] = Field(None, max_length=500, description="角色描述")
    is_active: bool = Field(True, description="是否激活")


class RoleUpdateRequest(BaseModel):
    role_name: Optional[str] = Field(None, min_length=1, max_length=50, description="角色名称")
    role_code: Optional[str] = Field(None, min_length=1, max_length=50, description="角色代码")
    role_type: Optional[str] = Field(None, min_length=1, max_length=20, description="角色类型")
    description: Optional[str] = Field(None, max_length=500, description="角色描述")
    is_active: Optional[bool] = Field(None, description="是否激活")


class RoleResponse(BaseModel):
    id: str
    role_name: str
    role_code: str
    role_type: str
    description: Optional[str]
    is_active: bool
    is_system_role: bool
    created_at: datetime
    updated_at: datetime
    permission_count: int = 0


class RoleListResponse(BaseModel):
    roles: List[RoleResponse]
    total: int
    page: int
    page_size: int


@router.post("/", response_model=RoleResponse, summary="创建角色")
async def create_role(
    request: RoleCreateRequest,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_super_admin)
):
    """创建新角色 - 仅超级管理员可操作"""
    try:
        # 检查角色代码是否已存在
        existing_role = await db.execute(
            select(Role).where(Role.role_code == request.role_code)
        )
        if existing_role.scalar_one_or_none():
            raise HTTPException(status_code=400, detail="角色代码已存在")

        # 创建新角色
        new_role = Role(
            role_name=request.role_name,
            role_code=request.role_code,
            role_type=request.role_type,
            description=request.description,
            is_active=request.is_active,
            is_system_role=False  # 新创建的角色不是系统角色
        )
        
        db.add(new_role)
        await db.commit()
        await db.refresh(new_role)

        return RoleResponse(
            id=str(new_role.id),
            role_name=new_role.role_name,
            role_code=new_role.role_code,
            role_type=new_role.role_type,
            description=new_role.description,
            is_active=new_role.is_active,
            is_system_role=new_role.is_system_role,
            created_at=new_role.created_at,
            updated_at=new_role.updated_at,
            permission_count=0
        )

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"创建角色失败: {str(e)}")


@router.get("/", response_model=RoleListResponse, summary="获取角色列表")
async def get_roles(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=100, description="每页数量"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    role_type: Optional[str] = Query(None, description="角色类型筛选"),
    is_active: Optional[bool] = Query(None, description="激活状态筛选"),
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_super_admin)
):
    """获取角色列表 - 仅超级管理员可操作"""
    try:
        # 构建查询条件
        conditions = []
        
        if search:
            conditions.append(
                or_(
                    Role.role_name.ilike(f"%{search}%"),
                    Role.role_code.ilike(f"%{search}%"),
                    Role.description.ilike(f"%{search}%")
                )
            )
        
        if role_type:
            conditions.append(Role.role_type == role_type)
            
        if is_active is not None:
            conditions.append(Role.is_active == is_active)

        # 获取总数
        count_query = select(func.count(Role.id))
        if conditions:
            count_query = count_query.where(and_(*conditions))
        
        total_result = await db.execute(count_query)
        total = total_result.scalar()

        # 获取角色列表
        query = select(Role).order_by(Role.created_at.desc())
        if conditions:
            query = query.where(and_(*conditions))
        
        query = query.offset((page - 1) * page_size).limit(page_size)
        
        result = await db.execute(query)
        roles = result.scalars().all()

        # 获取每个角色的权限数量
        role_responses = []
        for role in roles:
            permission_count_result = await db.execute(
                select(func.count(RolePermission.id)).where(RolePermission.role_id == role.id)
            )
            permission_count = permission_count_result.scalar()
            
            role_responses.append(RoleResponse(
                id=str(role.id),
                role_name=role.role_name,
                role_code=role.role_code,
                role_type=role.role_type,
                description=role.description,
                is_active=role.is_active,
                is_system_role=role.is_system_role,
                created_at=role.created_at,
                updated_at=role.updated_at,
                permission_count=permission_count
            ))

        return RoleListResponse(
            roles=role_responses,
            total=total,
            page=page,
            page_size=page_size
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取角色列表失败: {str(e)}")


@router.get("/{role_id}", response_model=RoleResponse, summary="获取角色详情")
async def get_role(
    role_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_super_admin)
):
    """获取角色详情 - 仅超级管理员可操作"""
    try:
        # 获取角色信息
        result = await db.execute(select(Role).where(Role.id == role_id))
        role = result.scalar_one_or_none()
        
        if not role:
            raise HTTPException(status_code=404, detail="角色不存在")

        # 获取权限数量
        permission_count_result = await db.execute(
            select(func.count(RolePermission.id)).where(RolePermission.role_id == role.id)
        )
        permission_count = permission_count_result.scalar()

        return RoleResponse(
            id=str(role.id),
            role_name=role.role_name,
            role_code=role.role_code,
            role_type=role.role_type,
            description=role.description,
            is_active=role.is_active,
            is_system_role=role.is_system_role,
            created_at=role.created_at,
            updated_at=role.updated_at,
            permission_count=permission_count
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取角色详情失败: {str(e)}")


@router.put("/{role_id}", response_model=RoleResponse, summary="更新角色")
async def update_role(
    role_id: str,
    request: RoleUpdateRequest,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_super_admin)
):
    """更新角色信息 - 仅超级管理员可操作"""
    try:
        # 获取角色
        result = await db.execute(select(Role).where(Role.id == role_id))
        role = result.scalar_one_or_none()

        if not role:
            raise HTTPException(status_code=404, detail="角色不存在")

        # 检查是否尝试修改系统角色的关键属性
        if role.is_system_role and (request.role_code or request.role_type):
            raise HTTPException(status_code=400, detail="不能修改系统角色的代码和类型")

        # 检查角色代码是否已存在（如果要修改的话）
        if request.role_code and request.role_code != role.role_code:
            existing_role = await db.execute(
                select(Role).where(and_(Role.role_code == request.role_code, Role.id != role_id))
            )
            if existing_role.scalar_one_or_none():
                raise HTTPException(status_code=400, detail="角色代码已存在")

        # 更新角色信息
        update_data = request.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(role, field, value)

        await db.commit()
        await db.refresh(role)

        # 获取权限数量
        permission_count_result = await db.execute(
            select(func.count(RolePermission.id)).where(RolePermission.role_id == role.id)
        )
        permission_count = permission_count_result.scalar()

        return RoleResponse(
            id=str(role.id),
            role_name=role.role_name,
            role_code=role.role_code,
            role_type=role.role_type,
            description=role.description,
            is_active=role.is_active,
            is_system_role=role.is_system_role,
            created_at=role.created_at,
            updated_at=role.updated_at,
            permission_count=permission_count
        )

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"更新角色失败: {str(e)}")


@router.delete("/{role_id}", summary="删除角色")
async def delete_role(
    role_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_super_admin)
):
    """删除角色 - 仅超级管理员可操作"""
    try:
        # 获取角色
        result = await db.execute(select(Role).where(Role.id == role_id))
        role = result.scalar_one_or_none()

        if not role:
            raise HTTPException(status_code=404, detail="角色不存在")

        # 检查是否为系统角色
        if role.is_system_role:
            raise HTTPException(status_code=400, detail="不能删除系统角色")

        # 检查是否有用户使用此角色
        from app.models.permission import UserRole
        user_role_result = await db.execute(
            select(func.count(UserRole.id)).where(UserRole.role_id == role_id)
        )
        user_count = user_role_result.scalar()

        if user_count > 0:
            raise HTTPException(status_code=400, detail=f"无法删除角色，还有 {user_count} 个用户使用此角色")

        # 删除角色（级联删除相关的权限关联）
        await db.delete(role)
        await db.commit()

        return {"message": "角色删除成功", "role_id": role_id}

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"删除角色失败: {str(e)}")


@router.get("/{role_id}/permissions", summary="获取角色的权限列表")
async def get_role_permissions(
    role_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_super_admin)
):
    """获取角色的权限列表 - 仅超级管理员可操作"""
    try:
        # 检查角色是否存在
        role_result = await db.execute(select(Role).where(Role.id == role_id))
        role = role_result.scalar_one_or_none()

        if not role:
            raise HTTPException(status_code=404, detail="角色不存在")

        # 获取角色的权限
        permissions_result = await db.execute(
            select(Permission).join(RolePermission).where(RolePermission.role_id == role_id)
            .order_by(Permission.module, Permission.permission_code)
        )
        permissions = permissions_result.scalars().all()

        return {
            "role": {
                "id": str(role.id),
                "role_name": role.role_name,
                "role_code": role.role_code
            },
            "permissions": [
                {
                    "id": str(perm.id),
                    "permission_name": perm.permission_name,
                    "permission_code": perm.permission_code,
                    "module": perm.module,
                    "action": perm.action,
                    "resource": perm.resource,
                    "description": perm.description
                }
                for perm in permissions
            ],
            "total_permissions": len(permissions)
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取角色权限失败: {str(e)}")
