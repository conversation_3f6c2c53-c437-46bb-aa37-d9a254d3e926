from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from app.database import get_db
from app.services.system_service import SystemService
from app.schemas.system import *
from app.exceptions import *
from app.dependencies import get_current_user, get_current_admin
from app.core.permissions import check_permission, require_admin_permission
from typing import Dict, Any, Optional
from datetime import datetime

router = APIRouter()

@router.get("/statistics", response_model=SystemApiResponse, summary="获取系统统计数据")
async def get_system_statistics(
    period: str = Query("daily", description="统计周期"),
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    metrics: Optional[str] = Query(None, description="指定统计指标，逗号分隔"),
    current_user: dict = Depends(check_permission("admin.statistics.view")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    获取系统整体统计数据

    需要管理员权限才能访问此接口。

    - **period**: 统计周期
      - daily: 日统计（默认显示最近30天）
      - weekly: 周统计（默认显示最近12周）
      - monthly: 月统计（默认显示最近12个月）
    - **start_date**: 开始日期（可选，格式：2024-01-01T00:00:00）
    - **end_date**: 结束日期（可选，格式：2024-01-31T23:59:59）
    - **metrics**: 指定统计指标（可选，逗号分隔）

    返回数据包括：
    - **用户统计**: 总用户数、新用户数、活跃用户数
    - **业务统计**: 订单数量、收入统计
    - **系统统计**: API请求数、错误数量、系统资源使用情况
    - **趋势数据**: 各指标的时间趋势
    """
    # 检查管理员权限
    user_roles = current_user.get("roles", [])
    if "super_admin" not in user_roles and "admin" not in user_roles:
        raise HTTPException(status_code=403, detail="需要管理员权限")

    try:
        metrics_list = metrics.split(",") if metrics else None
        query = SystemStatisticsQuery(
            period=period,
            start_date=start_date,
            end_date=end_date,
            metrics=metrics_list
        )

        system_service = SystemService(db)
        result = await system_service.get_system_statistics(query)

        return {
            "success": True,
            "data": result,
            "message": "获取系统统计数据成功"
        }
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="获取系统统计数据失败")

@router.get("/users", response_model=SystemApiResponse, summary="管理员获取用户列表")
async def get_admin_users(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    current_user: dict = Depends(check_permission("admin.users.view")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    管理员获取用户列表（重定向到用户管理接口）
    """
    try:
        from app.api.v1.users import get_user_list
        from app.schemas.user import UserListQuery

        # 构建查询参数
        query = UserListQuery(
            page=page,
            size=size,
            sort_by="created_at",
            sort_order="desc"
        )

        # 调用用户管理的列表接口
        from app.services.user_service import UserService
        user_service = UserService(db)
        result = await user_service.get_user_list(query, current_user["id"])

        return {
            "success": True,
            "data": result,
            "message": "获取用户列表成功"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail="获取用户列表失败")

@router.get("/config", response_model=SystemApiResponse, summary="获取系统配置")
async def get_system_config(
    current_user: dict = Depends(check_permission("admin.config.view")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    获取系统配置列表

    需要管理员权限才能访问此接口。

    返回所有系统配置项，包括：
    - **配置键值**: 配置项的键和值
    - **配置类型**: string/integer/boolean/json
    - **配置分类**: system/security/payment/ai_service/email/storage
    - **配置描述**: 配置项的说明
    - **敏感信息**: 敏感配置会自动脱敏显示
    - **只读状态**: 标识配置是否可以修改

    配置分类说明：
    - **system**: 系统基础配置
    - **security**: 安全相关配置
    - **payment**: 支付相关配置
    - **ai_service**: AI服务配置
    - **email**: 邮件服务配置
    - **storage**: 存储服务配置
    """
    # 检查管理员权限
    user_roles = current_user.get("roles", [])
    if "super_admin" not in user_roles and "admin" not in user_roles:
        raise HTTPException(status_code=403, detail="需要管理员权限")

    try:
        system_service = SystemService(db)
        result = await system_service.get_system_config()

        return {
            "success": True,
            "data": result,
            "message": "获取系统配置成功"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail="获取系统配置失败")

@router.put("/config", response_model=SystemApiResponse, summary="更新系统配置")
async def update_system_config(
    request: SystemConfigUpdate,
    current_user: dict = Depends(check_permission("admin.config.update")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    更新系统配置

    需要管理员权限才能访问此接口。

    - **configs**: 配置键值对字典
      - 键：配置项的config_key
      - 值：新的配置值

    配置更新规则：
    - 只能更新存在的配置项
    - 不能更新只读配置项
    - 配置值会根据配置类型进行验证
    - 敏感配置的更新会记录在系统日志中

    返回结果包括：
    - **updated_configs**: 成功更新的配置项列表
    - **failed_configs**: 更新失败的配置项及失败原因
    - **total_updated**: 成功更新的配置项数量
    - **total_failed**: 更新失败的配置项数量

    示例请求：
    ```json
    {
      "configs": {
        "system.max_upload_size": 10485760,
        "ai_service.default_model": "gpt-3.5-turbo",
        "email.smtp_enabled": true
      }
    }
    ```
    """
    # 检查管理员权限
    user_roles = current_user.get("roles", [])
    if "super_admin" not in user_roles and "admin" not in user_roles:
        raise HTTPException(status_code=403, detail="需要管理员权限")

    try:
        system_service = SystemService(db)
        result = await system_service.update_system_config(request, current_user["id"])

        return {
            "success": True,
            "data": result,
            "message": f"配置更新完成，成功{result.total_updated}项，失败{result.total_failed}项"
        }
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="更新系统配置失败")

@router.get("/logs", response_model=SystemApiResponse, summary="查询系统日志")
async def get_system_logs(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(50, ge=1, le=100, description="每页数量"),
    level: Optional[str] = Query(None, description="日志级别筛选"),
    module: Optional[str] = Query(None, description="模块筛选"),
    user_id: Optional[str] = Query(None, description="用户ID筛选"),
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    keyword: Optional[str] = Query(None, description="关键词搜索"),
    sort_by: str = Query("created_at", description="排序字段"),
    sort_order: str = Query("desc", description="排序方式"),
    current_user: dict = Depends(check_permission("admin.logs.view")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    查询系统操作日志

    需要管理员权限才能访问此接口。

    支持以下功能：
    - **分页查询**: 通过page和size参数控制
    - **日志级别筛选**: 按日志级别筛选
      - debug: 调试信息
      - info: 一般信息
      - warning: 警告信息
      - error: 错误信息
      - critical: 严重错误
    - **模块筛选**: 按系统模块筛选
    - **用户筛选**: 按操作用户筛选
    - **时间筛选**: 按日志创建时间筛选
    - **关键词搜索**: 在日志消息和错误信息中搜索
    - **排序**: 支持多字段排序
      - created_at: 创建时间
      - level: 日志级别
      - module: 模块名称
      - user_id: 用户ID
      - response_time: 响应时间

    返回数据包括：
    - **日志列表**: 符合条件的日志记录
    - **分页信息**: 总数、页码、页数等
    - **统计信息**: 各级别日志的数量统计
    """
    # 检查管理员权限
    user_roles = current_user.get("roles", [])
    if "super_admin" not in user_roles and "admin" not in user_roles:
        raise HTTPException(status_code=403, detail="需要管理员权限")

    try:
        query = SystemLogQuery(
            page=page,
            size=size,
            level=level,
            module=module,
            user_id=user_id,
            start_date=start_date,
            end_date=end_date,
            keyword=keyword,
            sort_by=sort_by,
            sort_order=sort_order
        )

        system_service = SystemService(db)
        result = await system_service.get_system_logs(query)

        return {
            "success": True,
            "data": result,
            "message": "获取系统日志成功"
        }
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="获取系统日志失败")

@router.get("/health", response_model=SystemApiResponse, summary="系统健康检查")
async def get_system_health(
    current_user: dict = Depends(check_permission("admin.health.view")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    检查系统各组件健康状态

    需要管理员权限才能访问此接口。

    检查的组件包括：
    - **Database**: 数据库连接和响应时间
    - **Redis**: 缓存服务连接状态
    - **Storage**: 文件存储服务状态
    - **AI Service**: AI服务连接状态

    健康状态说明：
    - **healthy**: 组件运行正常
    - **warning**: 组件有轻微问题但仍可用
    - **critical**: 组件存在严重问题
    - **unknown**: 无法确定组件状态

    返回数据包括：
    - **overall_status**: 系统整体健康状态
    - **components**: 各组件的详细健康信息
    - **last_check_time**: 最后检查时间
    - **uptime**: 系统运行时间（秒）
    - **version**: 系统版本号
    - **environment**: 运行环境
    """
    # 检查管理员权限
    user_roles = current_user.get("roles", [])
    if "super_admin" not in user_roles and "admin" not in user_roles:
        raise HTTPException(status_code=403, detail="需要管理员权限")

    try:
        system_service = SystemService(db)
        result = await system_service.get_system_health()

        return {
            "success": True,
            "data": result,
            "message": "系统健康检查完成"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail="系统健康检查失败")
