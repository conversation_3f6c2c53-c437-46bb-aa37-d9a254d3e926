from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from app.database import get_db
from app.services.channel_service import ChannelService
from app.schemas.channel import *
from app.exceptions import *
from app.dependencies import get_current_user
from app.core.permissions import check_permission
from typing import Dict, Any

# 创建三个不同的router用于不同的tags分类
router = APIRouter()  # 保持原有的router用于向后兼容
user_channel_router = APIRouter()  # 用户渠道商管理
admin_channel_router = APIRouter()  # 管理员渠道商管理
channel_withdraw_router = APIRouter()  # 渠道商提现

@user_channel_router.post("/profile", response_model=ChannelApiResponse, summary="普通用户申请渠道商角色")
async def create_channel_profile(
    request: ChannelProfileRequest,
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    普通用户申请渠道商角色，提交渠道商信息

    **权限要求**: 仅需要登录认证，任何用户都可以申请渠道商角色

    - **provider_name**: 提供商名称（必填）
    - **provider_type**: 提供商类型（必填）individual/company/studio/mcn
    - **real_name**: 真实姓名（个人提供商必填）
    - **id_card_number**: 身份证号（个人提供商可选）
    - **company_name**: 公司名称（机构提供商必填）
    - **business_license**: 营业执照号（机构提供商可选）
    - **contact_email**: 联系邮箱（必填）
    - **contact_phone**: 联系电话（可选）
    - **contact_address**: 联系地址（可选）
    - **business_description**: 业务描述（可选）
    - **service_categories**: 服务类别（可选）
    - **platform_accounts**: 平台账号信息（可选）
    - **portfolio_urls**: 作品集链接（可选）
    - **qualification_files**: 资质文件（可选）
    """
    # 渠道商信息创建后需要等待管理员审核，审核通过后才会分配渠道商用户角色

    try:
        channel_service = ChannelService(db)
        result = await channel_service.create_channel_profile(current_user["id"], request)

        return {
            "success": True,
            "data": result,
            "message": "渠道商信息创建成功，等待审核"
        }
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        import logging
        import traceback
        logging.error(f"创建渠道商信息失败: {str(e)}")
        logging.error(f"错误堆栈: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"创建渠道商信息失败: {str(e)}")

@user_channel_router.get("/me", response_model=ChannelApiResponse, summary="获取渠道商信息")
async def get_channel_info(
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    获取当前用户的渠道商信息

    普通用户可以查看自己提交的渠道商信息和审核状态

    **权限要求**: 仅需要登录认证，用户可以查看自己的渠道商信息
    """
    try:

        channel_service = ChannelService(db)
        result = await channel_service.get_channel_info(current_user["id"])

        return {
            "success": True,
            "data": result,
            "message": "获取渠道商信息成功"
        }
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="获取渠道商信息失败")



@user_channel_router.put("/me", response_model=ChannelApiResponse, summary="更新渠道商信息")
async def update_channel_info(
    request: ChannelUpdateRequest,
    current_user: dict = Depends(check_permission("channels.profile.edit")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    更新渠道商信息

    只允许更新部分字段：
    - **contact_phone**: 联系电话
    - **contact_email**: 联系邮箱
    - **contact_address**: 联系地址
    - **business_description**: 业务描述
    - **service_categories**: 服务类别
    - **platform_accounts**: 平台账号信息
    - **portfolio_urls**: 作品集链接
    - **qualification_files**: 资质文件

    注意：提供商名称、类型等关键信息不允许修改

    **权限要求**: 需要 `channels.profile.edit` 权限，且必须是渠道商用户或管理员角色
    """
    try:
        # 检查用户角色是否为渠道商用户或管理员
        user_roles = current_user.get("roles", [])
        is_admin = any(role in ["admin", "super_admin"] for role in user_roles)
        if "channel_user" not in user_roles and not is_admin:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="只有渠道商用户或管理员可以更新渠道商信息"
            )

        channel_service = ChannelService(db)
        result = await channel_service.update_channel_info(current_user["id"], request)

        return {
            "success": True,
            "data": result,
            "message": "渠道商信息更新成功"
        }
    except (ValidationError, NotFoundError) as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="更新渠道商信息失败")


@user_channel_router.get("/available-providers", response_model=ChannelApiResponse, summary="获取可用渠道商列表")
async def get_available_providers(
    service_id: str = Query(..., description="服务ID"),
    tags: str = Query(None, description="标签筛选，多个标签用逗号分隔"),
    min_price: float = Query(None, ge=0, description="最低价格筛选"),
    max_price: float = Query(None, ge=0, description="最高价格筛选"),
    min_rating: float = Query(None, ge=0, le=5, description="最低评分筛选"),
    platform_type: str = Query(None, description="平台类型筛选"),
    delivery_time: int = Query(None, ge=1, description="最大交付时间筛选（小时）"),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    sort_by: str = Query("rating_score", description="排序字段"),
    sort_order: str = Query("desc", pattern="^(asc|desc)$", description="排序方式"),
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    企业用户获取提供指定服务的可用渠道商列表

    支持以下功能：
    - **服务筛选**: 根据服务ID获取提供该服务的渠道商
    - **标签筛选**: 支持多个标签筛选，用逗号分隔
    - **价格筛选**: 支持价格范围筛选
    - **评分筛选**: 支持最低评分筛选
    - **平台筛选**: 支持平台类型筛选
    - **交付时间**: 支持最大交付时间筛选
    - **排序**: 支持多字段排序（price/rating_score/delivery_time）
    - **分页**: 支持分页查询

    只返回已验证且活跃的渠道商
    """
    try:
        # 解析标签
        tag_list = []
        if tags:
            tag_list = [tag.strip() for tag in tags.split(',') if tag.strip()]

        # 构建查询参数
        query_params = {
            'service_id': service_id,
            'tags': tag_list,
            'min_price': min_price,
            'max_price': max_price,
            'min_rating': min_rating,
            'platform_type': platform_type,
            'delivery_time': delivery_time,
            'page': page,
            'size': size,
            'sort_by': sort_by,
            'sort_order': sort_order
        }

        channel_service = ChannelService(db)
        result = await channel_service.get_available_providers(query_params)

        return {
            "success": True,
            "data": result,
            "message": "获取可用渠道商列表成功"
        }
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="获取可用渠道商列表失败")


@admin_channel_router.get("", response_model=ChannelApiResponse, summary="渠道商列表管理")
async def get_channel_list(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    provider_type: str = Query(None, description="提供商类型筛选"),
    verification_status: str = Query(None, description="审核状态筛选"),
    platform_name: str = Query(None, description="平台名称筛选"),
    search: str = Query(None, description="搜索关键词"),
    sort_by: str = Query("created_at", description="排序字段"),
    sort_order: str = Query("desc", description="排序方式"),
    current_admin: dict = Depends(check_permission("channels.list.view")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    管理员查看渠道商列表

    支持以下功能：
    - **分页查询**: 通过page和size参数控制
    - **类型筛选**: 按提供商类型筛选（individual/company/studio/mcn）
    - **状态筛选**: 按审核状态筛选（pending/verified/rejected）
    - **平台筛选**: 按平台名称筛选
    - **关键词搜索**: 搜索提供商名称、公司名称、真实姓名、邮箱
    - **排序**: 支持多字段排序

    返回渠道商列表、分页信息和统计数据
    """
    try:
        query = ChannelListQuery(
            page=page,
            size=size,
            provider_type=provider_type,
            verification_status=verification_status,
            platform_name=platform_name,
            search=search,
            sort_by=sort_by,
            sort_order=sort_order
        )

        channel_service = ChannelService(db)
        result = await channel_service.get_channel_list(query, current_admin["id"])

        return {
            "success": True,
            "data": result,
            "message": "获取渠道商列表成功"
        }
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="获取渠道商列表失败")

@admin_channel_router.get("/pending", response_model=ChannelApiResponse, summary="获取待审批渠道商列表")
async def get_pending_channels(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    search: str = Query(None, description="搜索关键词"),
    sort_by: str = Query("created_at", description="排序字段"),
    sort_order: str = Query("desc", description="排序方式"),
    current_admin: dict = Depends(check_permission("channels.list.view")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    管理员获取待审批渠道商列表

    支持以下功能：
    - **分页查询**: 通过page和size参数控制
    - **关键词搜索**: 搜索提供商名称、公司名称、真实姓名、邮箱
    - **排序**: 支持多字段排序

    只返回审核状态为pending的渠道商
    """
    try:
        from app.schemas.channel import ChannelListQuery

        query = ChannelListQuery(
            page=page,
            size=size,
            verification_status="pending",  # 固定为待审批状态
            search=search,
            sort_by=sort_by,
            sort_order=sort_order
        )

        channel_service = ChannelService(db)
        result = await channel_service.get_channel_list(query, current_admin["id"])

        return {
            "success": True,
            "data": result,
            "message": "获取待审批渠道商列表成功"
        }
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="获取待审批渠道商列表失败")

@admin_channel_router.put("/{provider_id}/verification", response_model=ChannelApiResponse, summary="渠道商审核")
async def verify_channel(
    provider_id: str,
    request: ChannelVerificationRequest,
    current_admin: dict = Depends(check_permission("channels.verification")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    管理员审核渠道商资质

    - **status**: 审核结果（verified/rejected）
    - **note**: 审核备注（必填，至少5个字符）
    - **notify_user**: 是否通知用户（默认true）

    审核通过后，渠道商用户可以正常使用平台功能
    审核拒绝后，渠道商用户需要重新提交资料
    """
    try:
        channel_service = ChannelService(db)
        result = await channel_service.verify_channel(provider_id, request, current_admin["id"])

        return {
            "success": True,
            "data": result,
            "message": f"渠道商审核完成，状态已更新为{request.status}"
        }
    except (ValidationError, NotFoundError) as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="渠道商审核失败")







@channel_withdraw_router.post("/withdraw", response_model=ChannelApiResponse, summary="渠道商提现申请")
async def apply_withdraw(
    request: WithdrawRequest,
    current_user: dict = Depends(check_permission("channels.withdraw.apply")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    渠道商申请提现

    - **amount**: 提现金额（必填，最低100元）
    - **bank_account**: 银行账号（必填）
    - **bank_name**: 银行名称（必填）
    - **account_holder**: 账户持有人（必填）
    - **withdraw_reason**: 提现说明（可选）

    注意：提现金额不能超过当前可提现余额
    """
    try:
        # 检查用户角色是否为渠道商用户
        user_roles = current_user.get("roles", [])
        if "channel_user" not in user_roles:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="只有渠道商用户可以申请提现"
            )

        channel_service = ChannelService(db)
        result = await channel_service.apply_withdraw(current_user["id"], request)

        return {
            "success": True,
            "data": result,
            "message": "提现申请提交成功，预计2个工作日内处理"
        }
    except (ValidationError, NotFoundError) as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="提现申请失败")

@channel_withdraw_router.get("/withdraw", response_model=ChannelApiResponse, summary="获取渠道商提现记录")
async def get_withdraw_records(
    status: str = Query(None, description="状态筛选"),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    current_user: dict = Depends(check_permission("channels.withdraw.view")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    获取渠道商提现记录

    渠道商查看自己的提现记录，包括提现状态跟踪。

    - **status**: 状态筛选（pending/processing/completed/failed）
    - **page**: 页码
    - **size**: 每页数量

    返回提现记录列表和分页信息。
    """
    try:
        # 检查用户角色是否为渠道商用户
        user_roles = current_user.get("roles", [])
        if "channel_user" not in user_roles:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="只有渠道商用户可以查看提现记录"
            )

        query = WithdrawQuery(
            status=status,
            page=page,
            size=size
        )

        channel_service = ChannelService(db)
        result = await channel_service.get_withdraw_records(current_user["id"], query)

        return {
            "success": True,
            "data": result,
            "message": "提现记录获取成功"
        }
    except (ValidationError, NotFoundError) as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="获取提现记录失败")
