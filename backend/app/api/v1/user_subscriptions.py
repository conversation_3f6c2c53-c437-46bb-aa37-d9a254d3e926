"""
订阅管理API - 统一管理套餐查看和用户订阅
Subscription Management API - Unified Plans and User Subscriptions Management

本模块包含两大功能：
1. 套餐查看功能：用户浏览和选择可用的订阅套餐（产品目录）
2. 订阅管理功能：用户管理自己已购买的订阅实例（订单管理）
"""

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from app.database import get_db
from app.dependencies import get_current_user, get_current_admin, get_optional_user

from app.services.subscription_service import SubscriptionService
from app.schemas.subscription import *
from app.exceptions import NotFoundError, ValidationError
from typing import List, Optional
from datetime import datetime, timezone

router = APIRouter()

# =====================================================
# 第一部分：套餐查看功能 (2个接口)
# 功能说明：用户浏览可选择的订阅套餐，类似商品目录
# 数据来源：subscription_plans 表
# =====================================================

@router.get("/available-plans", response_model=List[SubscriptionPlanResponse], summary="获取可用订阅套餐")
async def get_available_subscription_plans(
    target_user_type: Optional[str] = Query(None, description="按用户类型筛选: enterprise/provider/both"),
    active_only: bool = Query(True, description="仅显示激活的套餐"),
    billing_cycle: Optional[str] = Query(None, description="付费周期: monthly/quarterly/yearly"),
    current_user: Optional[dict] = Depends(get_optional_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取可用的订阅套餐列表 - 产品目录功能

    用途：
    - 用户在购买前浏览所有可选择的套餐
    - 展示套餐的价格、功能、配额等基本信息
    - 帮助用户做出购买决策
    - 标识用户已购买的套餐（基于特定付费周期）

    返回数据：
    - 套餐基本信息（名称、价格、功能描述）
    - 配额限制（内容请求数、监控项目数等）
    - 适用用户类型
    - 是否已购买标识（针对指定的付费周期）

    权限：所有用户都可以查看
    """
    service = SubscriptionService(db)
    user_id = current_user["id"] if current_user else None
    return await service.get_plans_with_purchase_status(target_user_type, active_only, user_id, billing_cycle)

@router.get("/available-plans/{plan_id}", response_model=SubscriptionPlanResponse, summary="获取单个订阅套餐详情")
async def get_subscription_plan_detail(
    plan_id: str,
    db: AsyncSession = Depends(get_db)
):
    """
    获取指定订阅套餐的详细信息 - 产品详情功能

    用途：
    - 用户查看某个套餐的完整介绍
    - 了解套餐的具体功能和限制
    - 在购买前获取详细的产品信息

    返回数据：
    - 套餐完整信息（价格、功能、配额、描述等）
    - 适用场景和用户类型
    - 计费周期和续费规则

    权限：所有用户都可以查看
    """
    # 输入验证
    if not plan_id or not plan_id.strip():
        raise HTTPException(status_code=400, detail="Plan ID cannot be empty")

    service = SubscriptionService(db)
    try:
        plan = await service.get_plan_by_id(plan_id.strip())
        return service._plan_to_response(plan)
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get plan details: {str(e)}")

# =====================================================
# 第二部分：用户订阅管理功能 (9个接口)
# 功能说明：用户管理自己的订阅，包括购买、查看、变更等操作
# 数据来源：subscriptions 表
# =====================================================

@router.get("/my", response_model=List[SubscriptionResponse], summary="获取我的订阅列表")
async def get_my_subscriptions(
    is_active: Optional[bool] = Query(None, description="按激活状态筛选"),
    page: int = Query(1, ge=1),
    size: int = Query(20, le=100),
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取当前用户的订阅列表 - 我的订阅管理

    用途：
    - 用户查看自己购买的所有订阅
    - 管理和监控订阅状态
    - 支持按激活状态筛选和分页

    返回数据：
    - 用户的订阅实例列表
    - 每个订阅的状态、到期时间、使用情况
    - 支持分页显示

    权限：用户只能查看自己的订阅
    """
    service = SubscriptionService(db)

    # 使用数据库级别的分页和筛选
    subscriptions = await service.get_user_subscriptions_paginated(
        user_id=current_user["id"],
        is_active=is_active,
        page=page,
        size=size
    )
    return subscriptions


@router.get("", response_model=List[SubscriptionResponse], summary="获取所有订阅列表（管理员）")
async def get_all_subscriptions(
    is_active: Optional[bool] = Query(None, description="按激活状态筛选"),
    page: int = Query(1, ge=1),
    size: int = Query(20, le=100),
    current_admin: dict = Depends(get_current_admin),  # 要求管理员权限
    db: AsyncSession = Depends(get_db)
):
    """
    获取所有用户的订阅列表 - 管理员订阅监控

    用途：
    - 管理员查看系统中所有用户的订阅
    - 监控订阅状态和使用情况
    - 进行订阅数据分析和管理

    返回数据：
    - 所有用户的订阅实例列表
    - 支持按激活状态筛选
    - 支持分页显示

    权限：仅管理员可访问
    """
    service = SubscriptionService(db)

    try:
        all_subscriptions = await service.get_all_subscriptions_for_admin(
            page=page,
            size=size,
            is_active=is_active
        )
        return all_subscriptions
    except NotImplementedError:
        # 如果管理员方法未实现，返回空列表
        return []
    except Exception as e:
        # 记录错误并返回HTTP异常
        raise HTTPException(status_code=500, detail=f"Failed to get subscriptions: {str(e)}")

@router.get("/current", response_model=Optional[SubscriptionResponse], summary="获取当前订阅")
async def get_current_subscription(
    role_context: Optional[str] = Query(None, description="角色上下文: enterprise/provider，用于多角色用户"),
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取用户当前激活的订阅 - 当前订阅状态查询

    用途：
    - 快速获取用户当前正在使用的订阅
    - 显示用户的订阅状态和剩余配额
    - 用于权限验证和功能限制
    - 支持多角色用户根据角色上下文返回对应订阅

    参数：
    - role_context: 角色上下文，当用户有多个角色时指定要查询的角色对应的订阅

    返回数据：
    - 当前激活的订阅信息
    - 如果没有激活订阅则返回 null
    - 包含配额使用情况和到期时间

    权限：用户只能查看自己的当前订阅
    """
    service = SubscriptionService(db)

    # 自动解决订阅冲突（如果存在）
    await service.resolve_subscription_conflicts(current_user["id"])

    return await service.get_active_subscription_by_role(current_user["id"], role_context)

@router.post("/resolve-conflicts", summary="解决订阅冲突")
async def resolve_subscription_conflicts(
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    解决用户的订阅冲突 - 保留最高级套餐

    用途：
    - 当用户有多个活跃订阅时，自动保留最高级的套餐
    - 取消其他低级套餐，避免重复计费
    - 确保用户享受最优服务

    规则：
    - 按套餐价格降序排列（最高价格优先）
    - 相同价格时按有效期降序（最长有效期优先）
    - 相同有效期时按更新时间降序（最新更新优先）

    权限：用户只能解决自己的订阅冲突
    """
    service = SubscriptionService(db)
    result = await service.resolve_subscription_conflicts(current_user["id"])
    return result

@router.get("/{subscription_id}", response_model=SubscriptionDetailResponse, summary="获取订阅详情")
async def get_subscription_detail(
    subscription_id: str,
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取指定订阅的详细信息 - 订阅实例详情查询

    用途：
    - 查看已购买订阅的完整状态信息
    - 了解订阅的使用情况和配额消耗
    - 管理订阅设置和续费选项

    返回数据：
    - 订阅的完整详细信息
    - 配额使用统计和剩余量
    - 订阅历史和续费记录
    - 自动续费设置

    权限：用户只能查看自己的订阅详情
    注意：这里返回的是用户已购买的订阅实例信息，不是套餐模板信息
    """
    # 输入验证
    if not subscription_id or not subscription_id.strip():
        raise HTTPException(status_code=400, detail="Subscription ID cannot be empty")

    service = SubscriptionService(db)
    try:
        return await service.get_subscription_detail(subscription_id.strip(), current_user["id"])
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except PermissionError as e:
        raise HTTPException(status_code=403, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get subscription details: {str(e)}")

@router.post("", response_model=SubscriptionResponse, summary="创建订阅（用户购买）")
async def create_subscription(
    request: SubscriptionCreate,
    current_user: dict = Depends(get_current_user),  # 用户购买订阅
    db: AsyncSession = Depends(get_db)
):
    """
    创建新订阅 - 用户购买订阅功能

    用途：
    - 用户购买选定的订阅套餐
    - 开始使用订阅服务
    - 获得对应的功能和配额

    功能：
    - 用户选择套餐并创建订阅
    - 自动计算价格和到期时间
    - 创建对应的配额记录
    - 生成订单和支付信息
    - 激活订阅服务

    权限：所有用户都可以购买订阅
    流程：选择套餐 → 创建订阅 → 支付 → 激活服务
    """
    service = SubscriptionService(db)
    try:
        # 用户为自己创建订阅
        return await service.create_subscription(current_user["id"], request)
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))



@router.get("/my-history", response_model=List[SubscriptionResponse], summary="获取我的订阅历史")
async def get_my_subscription_history(
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取我的订阅历史记录 - 订阅历史查询

    用途：
    - 用户查看自己的所有订阅记录
    - 了解订阅购买和使用历史
    - 进行订阅数据分析和统计

    返回数据：
    - 用户的所有订阅记录（包括已过期的）
    - 按时间倒序排列
    - 包含订阅状态和基本信息

    权限：用户只能查看自己的订阅历史
    """
    service = SubscriptionService(db)
    return await service.get_subscription_history(current_user["id"])

# =====================================================
# 第三部分：订阅变更功能 (4个接口)
# 功能说明：用户对已购买订阅进行升级、降级、续费、取消等操作
# =====================================================

@router.post("/upgrade", response_model=SubscriptionChangeResponse, summary="升级订阅")
async def upgrade_subscription(
    request: SubscriptionUpgrade,
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    升级当前订阅套餐 - 订阅升级功能

    用途：
    - 用户将当前订阅升级到更高级的套餐
    - 获得更多配额和功能
    - 立即生效，按比例计费

    功能：
    - 自动计算升级费用和剩余时间
    - 更新配额和功能权限
    - 保持原有订阅周期
    - 生成升级记录和账单

    权限：用户只能升级自己的订阅
    """
    from app.services.order_service import OrderService
    from app.exceptions import ValidationError, NotFoundError
    from uuid import UUID
    
    order_service = OrderService(db)
    subscription_service = SubscriptionService(db)

    # 获取用户当前订阅
    current_sub = await subscription_service.get_active_subscription(current_user["id"])
    if not current_sub:
        raise HTTPException(status_code=404, detail="No active subscription found")

    try:
        # 通过订单服务创建升级订单
        order_result = await order_service.create_upgrade_order(
            user_id=UUID(current_user["id"]),
            upgrade_request=request
        )

        # 等待一小段时间确保所有数据库操作完成
        import asyncio
        await asyncio.sleep(0.1)

        # 获取升级后的套餐信息
        target_plan = await subscription_service.get_plan_by_id(request.target_plan_id)
        current_plan = await subscription_service.get_plan_by_id(current_sub.plan_id)

        # 返回 SubscriptionChangeResponse 格式的响应
        return SubscriptionChangeResponse(
            id=order_result.id,  # 使用订单ID
            subscription_id=str(current_sub.id),
            change_type="upgrade",
            from_plan={"id": str(current_plan.id), "name": current_plan.plan_name},
            to_plan={"id": str(target_plan.id), "name": target_plan.plan_name},
            amount=order_result.final_amount,
            effective_date=datetime.now(timezone.utc) if request.effective_immediately else current_sub.end_date,
            status="success",
            message="订阅升级成功"
        )
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except PermissionError as e:
        raise HTTPException(status_code=403, detail=str(e))
    except Exception as e:
        import traceback
        print(f"ERROR: 升级订阅失败 - {str(e)}")
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"升级订阅失败: {str(e)}")

@router.post("/downgrade", response_model=SubscriptionChangeResponse, summary="降级订阅")
async def downgrade_subscription(
    request: SubscriptionUpgrade,  # Same model as upgrade
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    降级当前订阅套餐 - 订阅降级功能

    用途：
    - 用户将当前订阅降级到更低级的套餐
    - 减少费用支出，调整配额需求
    - 通常在下个计费周期生效

    功能：
    - 计算降级后的费用差异
    - 调整配额和功能权限
    - 设置降级生效时间
    - 生成降级记录和退费信息

    权限：用户只能降级自己的订阅
    注意：降级可能导致功能受限，需要用户确认
    """
    service = SubscriptionService(db)

    # 获取用户当前订阅
    current_sub = await service.get_active_subscription(current_user["id"])
    if not current_sub:
        raise HTTPException(status_code=404, detail="No active subscription found")

    try:
        return await service.downgrade_subscription(
            current_sub.id,
            current_user["id"],
            request
        )
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except PermissionError as e:
        raise HTTPException(status_code=403, detail=str(e))

@router.post("/renew", response_model=SubscriptionChangeResponse, summary="续费订阅")
async def renew_subscription(
    request: SubscriptionRenewal,
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    续费当前订阅 - 订阅续费功能

    用途：
    - 用户为即将到期的订阅进行续费
    - 延长订阅有效期
    - 保持服务连续性

    功能：
    - 计算续费价格和周期
    - 延长订阅到期时间
    - 重置配额使用量（可选）
    - 生成续费记录和账单
    - 支持自动续费设置

    权限：用户只能续费自己的订阅
    """
    service = SubscriptionService(db)

    # 获取用户当前订阅
    current_sub = await service.get_active_subscription(current_user["id"])
    if not current_sub:
        raise HTTPException(status_code=404, detail="No active subscription found")

    try:
        return await service.renew_subscription(
            current_sub.id,
            current_user["id"],
            request
        )
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except PermissionError as e:
        raise HTTPException(status_code=403, detail=str(e))

@router.post("/cancel", response_model=SubscriptionChangeResponse, summary="取消订阅")
async def cancel_subscription(
    request: SubscriptionCancel,
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    取消当前订阅 - 订阅取消功能

    用途：
    - 用户主动取消当前订阅
    - 停止自动续费
    - 申请退费（如适用）

    功能：
    - 设置订阅取消状态
    - 计算退费金额（按政策）
    - 保留数据到期末
    - 禁用自动续费
    - 生成取消记录

    权限：用户只能取消自己的订阅
    注意：取消后订阅将在当前周期结束后失效
    """
    service = SubscriptionService(db)

    # 获取用户当前订阅
    current_sub = await service.get_active_subscription(current_user["id"])
    if not current_sub:
        raise HTTPException(status_code=404, detail="No active subscription found")

    try:
        return await service.cancel_subscription(
            current_sub.id,
            current_user["id"],
            request
        )
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except PermissionError as e:
        raise HTTPException(status_code=403, detail=str(e))


@router.put("/{subscription_id}/admin-edit", response_model=SubscriptionResponse, summary="管理员编辑订阅")
async def admin_edit_subscription(
    subscription_id: str,
    request: AdminSubscriptionUpdate,
    current_admin: dict = Depends(get_current_admin),  # 要求管理员权限
    db: AsyncSession = Depends(get_db)
):
    """
    管理员编辑订阅 - 统一的订阅管理接口

    用途：
    - 管理员可以修改任何用户的订阅信息
    - 支持修改状态、时间、套餐、配置等所有字段
    - 一个接口搞定所有订阅编辑需求

    可修改的字段：
    - status: 订阅状态 (active/expired/cancelled/suspended)
    - start_date/end_date: 订阅时间范围
    - plan_id: 更换套餐
    - auto_renewal: 自动续费设置
    - renewal_price: 续费价格
    - content_used/monitoring_used/ai_used: 配额使用量
    - reason: 修改原因（用于记录）

    权限：仅管理员可访问
    """
    service = SubscriptionService(db)

    try:
        return await service.admin_update_subscription(subscription_id, request)
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to update subscription: {str(e)}")
