"""
Enhanced Content API Endpoints
包含内容请求管理、内容服务订单、内容交付管理的增强API
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query, Path, Body
from sqlalchemy.ext.asyncio import AsyncSession
from app.database import get_db
from app.services.content_service_enhanced import (
    ContentRequestServiceEnhanced,
    ContentServiceOrderService,
    ContentDeliveryService
)
from app.schemas.content import *
from app.exceptions import ValidationError, NotFoundError, PermissionError
from app.dependencies import get_current_user
from app.core.permissions import check_permission
from typing import Dict, Any, List, Optional
from datetime import datetime

router = APIRouter(tags=["内容增强"])

# ============= 内容请求管理 API (6个端点) =============

@router.get("/content-requests", response_model=ContentRequestListResponse, summary="查询内容请求列表")
async def list_content_requests(
    status: Optional[str] = Query(None, description="请求状态"),
    request_type: Optional[str] = Query(None, description="请求类型"),
    provider_id: Optional[str] = Query(None, description="渠道商ID"),
    company_id: Optional[str] = Query(None, description="企业ID"),
    group_id: Optional[str] = Query(None, description="分组ID"),
    tags: Optional[List[str]] = Query(None, description="标签列表"),
    date_from: Optional[datetime] = Query(None, description="开始时间"),
    date_to: Optional[datetime] = Query(None, description="结束时间"),
    sort_by: str = Query("created_at", description="排序字段"),
    sort_order: str = Query("desc", description="排序方式"),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, le=100, description="每页数量"),
    current_user: dict = Depends(check_permission("content.requests.view")),
    db: AsyncSession = Depends(get_db)
) -> ContentRequestListResponse:
    """
    查询内容请求列表（支持多条件筛选）
    
    支持的筛选条件：
    - status: 请求状态 (pending/accepted/rejected/in_progress/delivered/completed/cancelled)
    - request_type: 请求类型 (PUBLISH_CONTENT/CREATE_CONTENT)
    - provider_id: 渠道商ID
    - company_id: 企业ID
    - group_id: 分组ID
    - tags: 标签列表（包含任意指定标签）
    - date_from/date_to: 时间范围
    - sort_by: 排序字段 (created_at/deadline/updated_at)
    - sort_order: 排序方式 (asc/desc)
    """
    service = ContentRequestServiceEnhanced(db)
    
    filters = {
        'status': status,
        'request_type': request_type,
        'provider_id': provider_id,
        'company_id': company_id,
        'group_id': group_id,
        'tags': tags,
        'date_from': date_from,
        'date_to': date_to,
        'sort_by': sort_by,
        'sort_order': sort_order
    }
    
    pagination = {
        'page': page,
        'size': size
    }
    
    try:
        # 根据用户角色添加权限过滤
        from app.models.company import Company
        from app.models.channel import ContentProvider
        from sqlalchemy import select

        user_role = current_user.get("role")
        user_id = current_user.get("id")

        if user_role == "enterprise_user":
            # 企业用户只能看自己的需求
            company_result = await db.execute(
                select(Company.id).where(Company.user_id == user_id)
            )
            company_id = company_result.scalar_one_or_none()
            if company_id:
                filters['company_id'] = str(company_id)
            else:
                # 如果企业用户没有关联企业，返回空结果
                return ContentRequestListResponse(
                    items=[],
                    total=0,
                    page=pagination['page'],
                    size=pagination['size'],
                    pages=0
                )
        elif user_role == "channel_user":
            # 渠道商只能看分配给自己的需求
            provider_result = await db.execute(
                select(ContentProvider.id).where(ContentProvider.user_id == user_id)
            )
            provider_id = provider_result.scalar_one_or_none()
            if provider_id:
                filters['provider_id'] = str(provider_id)
            else:
                # 如果渠道商用户没有关联渠道商，返回空结果
                return ContentRequestListResponse(
                    items=[],
                    total=0,
                    page=pagination['page'],
                    size=pagination['size'],
                    pages=0
                )

        result = await service.search_requests(filters, pagination)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")

@router.get("/content-requests/{request_id}", response_model=ContentRequestDetailResponse, summary="获取内容请求详情")
async def get_content_request_detail(
    request_id: str = Path(..., description="请求ID"),
    include_delivery: bool = Query(False, description="是否包含交付信息"),
    include_order: bool = Query(False, description="是否包含订单信息"),
    current_user: dict = Depends(check_permission("content.requests.detail")),
    db: AsyncSession = Depends(get_db)
) -> ContentRequestDetailResponse:
    """
    获取内容请求详情（统一接口）
    
    返回内容请求的完整信息，可选包含：
    - 交付信息 (include_delivery=true)
    - 订单信息 (include_order=true)
    """
    from app.services.content_service import ContentService
    service = ContentService(db)
    
    try:
        # 使用现有服务获取基本信息
        result = await service.get_content_request_detail(request_id)
        
        # 构建详细响应
        response = ContentRequestDetailResponse(
            id=result['id'],
            group_id=result['group_id'],
            company_id=result['company_id'],
            provider_id=result['provider_id'],
            service_id=result['service_id'],
            request_type=result['request_type'],
            request_title=result['request_title'],
            request_description=result['request_description'],
            tags=result['tags'],
            deadline=result['deadline'],
            status=result['status'],
            fixed_price=result.get('fixed_price'),
            created_at=result['created_at'],
            updated_at=result['updated_at'],
            assigned_service_id=result.get('assigned_service_id'),
            assigned_at=result.get('assigned_at'),
            cancel_reason=result.get('cancel_reason'),
            cancelled_at=result.get('cancelled_at'),
            company_info=result.get('company_info'),
            provider_info=result.get('provider_info')
        )
        
        # 添加交付信息
        if include_delivery:
            delivery_info = await service.get_delivered_content(request_id) if result['status'] == 'DELIVERED' else None
            response.delivery_info = delivery_info
        
        # 添加订单信息
        if include_order:
            # TODO: 获取关联的订单信息
            response.order_info = None
        
        return response
    except Exception as e:
        raise HTTPException(status_code=404, detail=str(e))

@router.post("/content-requests", response_model=ContentRequestResponse, summary="创建内容请求")
async def create_content_request(
    request: ContentRequestCreate,
    current_user: dict = Depends(check_permission("content.requests.create")),
    db: AsyncSession = Depends(get_db)
) -> ContentRequestResponse:
    """
    创建内容请求（统一接口）
    
    支持两种模式：
    - PUBLISH_CONTENT: 企业提供稿件模式
    - CREATE_CONTENT: 需求创作模式
    
    支持批量创建（通过provider_ids字段）
    """
    from app.services.content_service import ContentService
    from app.models.company import Company
    from sqlalchemy import select
    
    # 获取用户的企业ID
    company_result = await db.execute(
        select(Company).where(Company.user_id == current_user["id"])
    )
    company = company_result.scalar_one_or_none()
    if not company:
        raise HTTPException(status_code=400, detail="用户未关联企业")
    
    service = ContentService(db)
    
    try:
        result = await service.create_content_request(str(company.id), request)
        return result
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.put("/content-requests/{request_id}", response_model=ContentRequestResponse, summary="更新内容请求")
async def update_content_request(
    request_id: str = Path(..., description="请求ID"),
    update_data: ContentRequestUpdate = Body(...),
    current_user: dict = Depends(check_permission("content.requests.update")),
    db: AsyncSession = Depends(get_db)
) -> ContentRequestResponse:
    """
    更新内容请求（标题、描述、标签等）
    
    只有请求所有者可以更新
    只有待处理和已接受状态的请求可以更新
    """
    service = ContentRequestServiceEnhanced(db)
    
    try:
        result = await service.update_content_request(request_id, update_data, current_user["id"])
        return result
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except PermissionError as e:
        raise HTTPException(status_code=403, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新失败: {str(e)}")

@router.post("/content-requests/{request_id}/cancel", response_model=OperationResponse, summary="取消内容请求")
async def cancel_content_request(
    request_id: str = Path(..., description="请求ID"),
    cancel_request: CancelRequest = Body(...),
    current_user: dict = Depends(check_permission("content.requests.cancel")),
    db: AsyncSession = Depends(get_db)
) -> OperationResponse:
    """
    取消内容请求
    
    - 只有请求所有者可以取消
    - 已完成或已取消的请求不能再取消
    - 可选同时取消相关订单
    """
    service = ContentRequestServiceEnhanced(db)
    
    try:
        result = await service.cancel_content_request(request_id, cancel_request, current_user["id"])
        return result
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except PermissionError as e:
        raise HTTPException(status_code=403, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"取消失败: {str(e)}")

@router.post("/content-requests/{request_id}/assign-service", response_model=OperationResponse, summary="分配渠道服务")
async def assign_service(
    request_id: str = Path(..., description="请求ID"),
    assignment: ServiceAssignment = Body(...),
    current_user: dict = Depends(check_permission("content.requests.assign")),
    db: AsyncSession = Depends(get_db)
) -> OperationResponse:
    """
    分配渠道服务

    - 只有请求所有者可以分配
    - 只有待处理状态的请求可以分配
    - 如果不指定provider_id，系统将自动选择
    """
    service = ContentRequestServiceEnhanced(db)

    try:
        result = await service.assign_service(request_id, assignment, current_user["id"])
        return result
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except PermissionError as e:
        raise HTTPException(status_code=403, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"分配失败: {str(e)}")

@router.post("/content-requests/{request_id}/apply", response_model=ApplyRequestResponse, summary="申请接单")
async def apply_content_request(
    request_id: str = Path(..., description="请求ID"),
    apply_request: ApplyRequestRequest = Body(...),
    current_user: dict = Depends(check_permission("content.requests.apply")),
    db: AsyncSession = Depends(get_db)
) -> ApplyRequestResponse:
    """
    渠道商申请接单

    - 只有渠道商可以申请接单
    - 只有待处理状态的请求可以接单
    """
    from app.services.content_service import ContentService
    service = ContentService(db)

    try:
        result = await service.apply_content_request(request_id, apply_request, current_user["id"])
        return result
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"申请接单失败: {str(e)}")

@router.post("/content-requests/{request_id}/delivery", response_model=ContentDeliveryCreateResponse, summary="提交交付内容")
async def deliver_content_request(
    request_id: str = Path(..., description="请求ID"),
    delivery_request: ContentDeliveryRequest = Body(...),
    current_user: dict = Depends(check_permission("content.delivery.create")),
    db: AsyncSession = Depends(get_db)
) -> ContentDeliveryCreateResponse:
    """
    渠道商提交交付内容

    - 只有接单的渠道商可以提交交付
    - 只有进行中状态的请求可以交付
    """
    from app.services.content_service import ContentService
    service = ContentService(db)

    try:
        result = await service.deliver_content(request_id, delivery_request, current_user["id"])
        return result
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"提交交付失败: {str(e)}")

@router.get("/content-requests/{request_id}/content", response_model=ContentDeliveryResponse, summary="查看交付内容")
async def get_delivered_content(
    request_id: str = Path(..., description="请求ID"),
    current_user: dict = Depends(check_permission("content.delivery.view")),
    db: AsyncSession = Depends(get_db)
) -> ContentDeliveryResponse:
    """
    查看交付内容详情

    - 企业用户和渠道商都可以查看
    """
    from app.services.content_service import ContentService
    service = ContentService(db)

    try:
        result = await service.get_delivered_content(request_id)
        return result
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取交付内容失败: {str(e)}")

@router.post("/content-requests/{request_id}/review", response_model=ContentReviewResponse, summary="审核交付内容")
async def review_content_request(
    request_id: str = Path(..., description="请求ID"),
    review_request: ContentReviewRequest = Body(...),
    current_user: dict = Depends(check_permission("content.review.create")),
    db: AsyncSession = Depends(get_db)
) -> ContentReviewResponse:
    """
    企业用户审核交付内容

    - 只有请求所有者可以审核
    - 只有已交付状态的请求可以审核
    """
    from app.services.content_service import ContentService
    service = ContentService(db)

    try:
        result = await service.review_content(request_id, review_request, current_user["id"])
        return result
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"审核失败: {str(e)}")

# ============= 内容服务订单 API (6个端点) =============

@router.get("/content-service-orders", response_model=ServiceOrderListResponse, summary="查询服务订单列表")
async def list_service_orders(
    work_status: Optional[str] = Query(None, description="工作状态"),
    provider_id: Optional[str] = Query(None, description="服务提供方ID"),
    request_id: Optional[str] = Query(None, description="关联请求ID"),
    route_type: Optional[str] = Query(None, description="路由类型"),
    date_from: Optional[datetime] = Query(None, description="开始时间"),
    date_to: Optional[datetime] = Query(None, description="结束时间"),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, le=100, description="每页数量"),
    current_user: dict = Depends(check_permission("content.orders.view")),
    db: AsyncSession = Depends(get_db)
) -> ServiceOrderListResponse:
    """
    查询内容服务订单列表
    
    支持的筛选条件：
    - work_status: 工作状态 (pending/accepted/creating/delivered/reviewing/completed)
    - provider_id: 服务提供方ID
    - request_id: 关联的内容请求ID
    - route_type: 路由类型 (official/channel/platform)
    - date_from/date_to: 时间范围
    """
    service = ContentServiceOrderService(db)
    
    filters = {
        'work_status': work_status,
        'provider_id': provider_id,
        'request_id': request_id,
        'route_type': route_type,
        'date_from': date_from,
        'date_to': date_to
    }
    
    pagination = {
        'page': page,
        'size': size
    }
    
    try:
        result = await service.list_service_orders(filters, current_user["id"], pagination)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")

@router.get("/content-service-orders/{order_id}", response_model=ServiceOrderDetailResponse, summary="获取服务订单详情")
async def get_service_order(
    order_id: str = Path(..., description="订单ID"),
    include_timeline: bool = Query(False, description="包含时间线"),
    include_deliveries: bool = Query(False, description="包含交付记录"),
    current_user: dict = Depends(check_permission("content.orders.detail")),
    db: AsyncSession = Depends(get_db)
) -> ServiceOrderDetailResponse:
    """
    获取服务订单详情
    
    返回服务订单的完整信息，可选包含：
    - 时间线 (include_timeline=true)
    - 交付记录 (include_deliveries=true)
    """
    service = ContentServiceOrderService(db)
    
    try:
        result = await service.get_service_order(order_id, current_user["id"])
        
        # TODO: 添加时间线和交付记录
        if include_timeline:
            result.timeline = []
        if include_deliveries:
            result.deliveries = []
        
        return result
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取失败: {str(e)}")

@router.post("/content-service-orders", response_model=ServiceOrderResponse, summary="创建内容服务订单")
async def create_service_order(
    order_data: ServiceOrderCreate = Body(...),
    current_user: dict = Depends(check_permission("content.orders.create")),
    db: AsyncSession = Depends(get_db)
) -> ServiceOrderResponse:
    """
    创建内容服务订单
    
    创建新的内容服务订单，关联到指定的内容请求
    """
    service = ContentServiceOrderService(db)
    
    try:
        result = await service.create_service_order(order_data, current_user["id"])
        return result
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建失败: {str(e)}")

@router.patch("/content-service-orders/{order_id}/status", response_model=OperationResponse, summary="更新工作状态")
async def update_work_status(
    order_id: str = Path(..., description="订单ID"),
    status_update: WorkStatusUpdate = Body(...),
    current_user: dict = Depends(check_permission("content.orders.update")),
    db: AsyncSession = Depends(get_db)
) -> OperationResponse:
    """
    更新工作状态（PATCH）
    
    更新服务订单的工作状态
    只有服务提供方或管理员可以更新
    """
    service = ContentServiceOrderService(db)
    
    try:
        result = await service.update_work_status(order_id, status_update, current_user["id"])
        return result
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新失败: {str(e)}")

@router.post("/content-service-orders/{order_id}/deliver", response_model=DeliveryResponse, summary="提交交付内容")
async def deliver_content(
    order_id: str = Path(..., description="订单ID"),
    delivery: ContentDeliverySubmit = Body(...),
    current_user: dict = Depends(check_permission("content.delivery.create")),
    db: AsyncSession = Depends(get_db)
) -> DeliveryResponse:
    """
    提交交付内容（增强版）
    
    服务提供方提交交付内容
    支持标记是否为最终版本
    """
    service = ContentServiceOrderService(db)
    
    try:
        result = await service.deliver_content(order_id, delivery, current_user["id"])
        return result
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"交付失败: {str(e)}")

@router.post("/content-service-orders/{order_id}/review", response_model=ReviewResponse, summary="审核交付内容")
async def review_delivery(
    order_id: str = Path(..., description="订单ID"),
    review: DeliveryReview = Body(...),
    current_user: dict = Depends(check_permission("content.review.create")),
    db: AsyncSession = Depends(get_db)
) -> ReviewResponse:
    """
    审核交付内容（增强版）
    
    订单所有者审核交付的内容
    支持三种状态：approved/rejected/revision_required
    """
    service = ContentServiceOrderService(db)
    
    try:
        result = await service.review_delivery(order_id, review, current_user["id"])
        return result
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"审核失败: {str(e)}")

# ============= 内容交付管理 API (5个端点) =============

@router.get("/content-deliveries", response_model=DeliveryListResponse, summary="查询交付记录列表")
async def list_deliveries(
    order_id: Optional[str] = Query(None, description="订单ID"),
    request_id: Optional[str] = Query(None, description="请求ID"),
    provider_id: Optional[str] = Query(None, description="提供方ID"),
    acceptance_status: Optional[str] = Query(None, description="接受状态"),
    version: Optional[int] = Query(None, description="版本号"),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, le=100, description="每页数量"),
    current_user: dict = Depends(check_permission("content.delivery.view")),
    db: AsyncSession = Depends(get_db)
) -> DeliveryListResponse:
    """
    查询交付记录列表
    
    支持的筛选条件：
    - order_id: 关联的服务订单ID
    - request_id: 关联的内容请求ID
    - provider_id: 提供方ID
    - acceptance_status: 接受状态 (pending/accepted/rejected)
    - version: 版本号
    """
    service = ContentDeliveryService(db)
    
    filters = {
        'order_id': order_id,
        'request_id': request_id,
        'provider_id': provider_id,
        'acceptance_status': acceptance_status,
        'version': version
    }
    
    pagination = {
        'page': page,
        'size': size
    }
    
    try:
        result = await service.list_deliveries(filters, current_user["id"], pagination)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")

@router.get("/content-deliveries/{delivery_id}", response_model=DeliveryDetailResponse, summary="获取交付详情")
async def get_delivery(
    delivery_id: str = Path(..., description="交付ID"),
    include_history: bool = Query(False, description="包含版本历史"),
    current_user: dict = Depends(check_permission("content.delivery.detail")),
    db: AsyncSession = Depends(get_db)
) -> DeliveryDetailResponse:
    """
    获取交付详情
    
    返回交付记录的完整信息，可选包含版本历史
    """
    service = ContentDeliveryService(db)
    
    try:
        result = await service.get_delivery(delivery_id, current_user["id"])
        return result
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取失败: {str(e)}")

@router.post("/content-deliveries", response_model=DeliveryResponse, summary="创建交付记录")
async def create_delivery(
    delivery_data: DeliveryCreate = Body(...),
    current_user: dict = Depends(check_permission("content.delivery.create")),
    db: AsyncSession = Depends(get_db)
) -> DeliveryResponse:
    """
    创建交付记录
    
    服务提供方创建新的交付记录
    支持创建修订版本（通过parent_delivery_id）
    """
    service = ContentDeliveryService(db)
    
    try:
        result = await service.create_delivery(delivery_data, current_user["id"])
        return result
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建失败: {str(e)}")

@router.post("/content-deliveries/{delivery_id}/accept", response_model=OperationResponse, summary="接受交付")
async def accept_delivery(
    delivery_id: str = Path(..., description="交付ID"),
    acceptance: DeliveryAcceptance = Body(...),
    current_user: dict = Depends(check_permission("content.delivery.accept")),
    db: AsyncSession = Depends(get_db)
) -> OperationResponse:
    """
    接受交付
    
    订单所有者接受交付的内容
    可选提供质量评分和发布选项
    """
    service = ContentDeliveryService(db)
    
    try:
        result = await service.accept_delivery(delivery_id, acceptance, current_user["id"])
        return result
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"接受失败: {str(e)}")

@router.post("/content-deliveries/{delivery_id}/reject", response_model=OperationResponse, summary="拒绝交付")
async def reject_delivery(
    delivery_id: str = Path(..., description="交付ID"),
    rejection: DeliveryRejection = Body(...),
    current_user: dict = Depends(check_permission("content.delivery.reject")),
    db: AsyncSession = Depends(get_db)
) -> OperationResponse:
    """
    拒绝交付（含原因）
    
    订单所有者拒绝交付的内容
    必须提供拒绝原因和修改要求
    """
    service = ContentDeliveryService(db)
    
    try:
        result = await service.reject_delivery(delivery_id, rejection, current_user["id"])
        return result
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"拒绝失败: {str(e)}")