from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from app.database import get_db
from app.services.channel_service import ChannelService
from app.schemas.channel_category import *
from app.schemas.common import ApiResponse
from app.dependencies import check_permission, get_current_admin
from app.exceptions import ValidationError, NotFoundError
from typing import Dict, Any, Optional

router = APIRouter()

# 通用响应模型
class ChannelCategoryMappingApiResponse(ApiResponse):
    data: Optional[ChannelCategoryMappingResponse] = None

class ChannelCategoryMappingListApiResponse(ApiResponse):
    data: Optional[Dict[str, Any]] = None

class ChannelServiceProvidersApiResponse(ApiResponse):
    data: Optional[List[Dict[str, Any]]] = None


@router.get("", response_model=ChannelCategoryMappingListApiResponse, summary="获取我绑定的系统服务列表")
async def get_my_bound_services(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    category_id: Optional[str] = Query(None, description="分类ID筛选"),
    service_id: Optional[str] = Query(None, description="服务ID筛选"),

    is_active: Optional[bool] = Query(None, description="是否启用筛选"),
    sort_by: str = Query("created_at", description="排序字段"),
    sort_order: str = Query("desc", pattern="^(asc|desc)$", description="排序方式"),
    current_user: dict = Depends(check_permission("channels.my_services.view")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    渠道商获取自己绑定的系统服务列表

    支持分页、筛选和排序功能。
    """
    try:
        query = ChannelCategoryMappingListQuery(
            page=page,
            size=size,
            provider_id=None,  # 将在服务中设置为当前用户的渠道商ID
            category_id=category_id,
            service_id=service_id,

            is_active=is_active,
            sort_by=sort_by,
            sort_order=sort_order
        )

        channel_service = ChannelService(db)
        result = await channel_service.get_my_bound_services(current_user["id"], query)

        return {
            "success": True,
            "data": result,
            "message": "获取绑定服务列表成功"
        }
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        import traceback
        print(f"获取绑定服务列表失败详细错误: {str(e)}")
        print(f"错误堆栈: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"获取绑定服务列表失败: {str(e)}")



@router.put("/{service_id}", response_model=ChannelCategoryMappingApiResponse, summary="渠道商绑定系统服务")
async def bind_system_service(
    service_id: str,
    request: ChannelCategoryMappingCreate,
    current_user: dict = Depends(check_permission("channels.my_services.manage")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    渠道商绑定系统中的服务

    渠道商从系统服务库中选择并绑定自己想要提供的服务
    """
    try:
        channel_service = ChannelService(db)
        mapping_result = await channel_service.bind_provider_to_service(
            user_id=current_user["id"],
            service_id=service_id,
            request=request
        )

        return {
            "success": True,
            "data": mapping_result,
            "message": "绑定系统服务成功"
        }
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        import traceback
        print(f"绑定系统服务失败详细错误: {str(e)}")
        print(f"错误堆栈: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"绑定系统服务失败: {str(e)}")


@router.patch("/{service_id}", response_model=ChannelCategoryMappingApiResponse, summary="更新渠道商服务绑定状态")
async def update_service_binding(
    service_id: str,
    request: ChannelCategoryMappingUpdate,
    current_user: dict = Depends(check_permission("channels.my_services.manage")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    渠道商更新服务绑定状态

    可以更新以下属性：
    - is_active: 是否启用该服务
    """
    try:
        channel_service = ChannelService(db)
        mapping_result = await channel_service.update_provider_service_binding(
            user_id=current_user["id"],
            service_id=service_id,
            request=request
        )

        return {
            "success": True,
            "data": mapping_result,
            "message": "更新服务绑定状态成功"
        }
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        import traceback
        print(f"更新服务绑定状态失败详细错误: {str(e)}")
        print(f"错误堆栈: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"更新服务绑定状态失败: {str(e)}")


@router.delete("/{service_id}", response_model=ApiResponse, summary="取消绑定系统服务")
async def unbind_system_service(
    service_id: str,
    current_user: dict = Depends(check_permission("channels.my_services.manage")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    渠道商取消绑定系统服务
    """
    try:
        channel_service = ChannelService(db)
        await channel_service.unbind_provider_from_service(
            user_id=current_user["id"],
            service_id=service_id
        )

        return {
            "success": True,
            "data": None,
            "message": "取消绑定系统服务成功"
        }
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="取消绑定系统服务失败")


# 管理员接口
@router.get("/admin/service/{service_id}", response_model=ChannelServiceProvidersApiResponse, summary="管理员获取服务绑定的渠道商列表")
async def admin_get_service_providers(
    service_id: str,
    current_admin: dict = Depends(get_current_admin),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    管理员获取特定服务绑定的所有渠道商列表
    """
    try:
        print(f"🔍 管理员获取服务绑定的渠道商列表: service_id={service_id}")
        print(f"🔍 当前用户: {current_admin}")

        channel_service = ChannelService(db)
        providers_result = await channel_service.admin_get_service_providers(service_id=service_id)

        return {
            "success": True,
            "data": providers_result,
            "message": "获取服务绑定的渠道商列表成功"
        }
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        import traceback
        print(f"获取服务绑定的渠道商列表失败详细错误: {str(e)}")
        print(f"错误堆栈: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"获取服务绑定的渠道商列表失败: {str(e)}")


@router.get("/admin/link", response_model=ChannelCategoryMappingApiResponse, summary="管理员查询服务和渠道商关联状态")
async def admin_get_provider_service_link(
    service_id: str = Query(..., description="服务ID"),
    provider_id: str = Query(..., description="渠道商ID"),
    current_admin: dict = Depends(get_current_admin),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    管理员查询服务和渠道商的关联状态

    查询指定的渠道商和服务之间的关联关系
    """
    try:
        channel_service = ChannelService(db)
        mapping_result = await channel_service.get_provider_service_mapping(
            service_id=service_id,
            provider_id=provider_id
        )

        return {
            "success": True,
            "data": mapping_result,
            "message": "查询关联状态成功"
        }
    except NotFoundError as e:
        return {
            "success": False,
            "data": None,
            "message": "未找到关联关系"
        }
    except Exception as e:
        import traceback
        print(f"查询关联状态失败详细错误: {str(e)}")
        print(f"错误堆栈: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"查询关联状态失败: {str(e)}")


@router.post("/admin/link", response_model=ChannelCategoryMappingApiResponse, summary="管理员为服务关联渠道商")
async def admin_link_provider_to_service(
    service_id: str = Query(..., description="服务ID"),
    provider_id: str = Query(..., description="渠道商ID"),
    current_admin: dict = Depends(get_current_admin),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    管理员为服务关联渠道商

    将指定的渠道商关联到指定的服务，使该渠道商可以提供该服务
    """
    try:
        print(f"🔍 管理员关联渠道商请求: service_id={service_id}, provider_id={provider_id}")
        print(f"🔍 当前用户: {current_admin}")

        channel_service = ChannelService(db)
        mapping_result = await channel_service.admin_link_provider_to_service(
            service_id=service_id,
            provider_id=provider_id
        )

        return {
            "success": True,
            "data": mapping_result,
            "message": "关联渠道商成功"
        }
    except ValidationError as e:
        print(f"❌ 验证错误: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except NotFoundError as e:
        print(f"❌ 未找到错误: {str(e)}")
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        import traceback
        print(f"❌ 关联渠道商失败详细错误: {str(e)}")
        print(f"错误堆栈: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"关联渠道商失败: {str(e)}")
