"""
权限管理 CRUD 接口
只有超级管理员可以操作
"""
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, func, or_, delete
from typing import List, Optional
from pydantic import BaseModel, Field
from datetime import datetime

from app.database import get_db
from app.dependencies import get_current_super_admin, get_current_admin
from app.models.permission import Permission, RolePermission, Role

router = APIRouter()


# 请求和响应模型
class PermissionCreateRequest(BaseModel):
    permission_name: str = Field(..., min_length=1, max_length=100, description="权限名称")
    permission_code: str = Field(..., min_length=1, max_length=100, description="权限代码")
    module: str = Field(..., min_length=1, max_length=50, description="所属模块")
    action: str = Field(..., min_length=1, max_length=50, description="操作类型")
    resource: Optional[str] = Field(None, max_length=50, description="资源类型")
    description: Optional[str] = Field(None, max_length=500, description="权限描述")


class PermissionUpdateRequest(BaseModel):
    permission_name: Optional[str] = Field(None, min_length=1, max_length=100, description="权限名称")
    permission_code: Optional[str] = Field(None, min_length=1, max_length=100, description="权限代码")
    module: Optional[str] = Field(None, min_length=1, max_length=50, description="所属模块")
    action: Optional[str] = Field(None, min_length=1, max_length=50, description="操作类型")
    resource: Optional[str] = Field(None, max_length=50, description="资源类型")
    description: Optional[str] = Field(None, max_length=500, description="权限描述")


class PermissionResponse(BaseModel):
    id: str
    permission_name: str
    permission_code: str
    module: str
    action: str
    resource: Optional[str]
    description: Optional[str]
    is_system_permission: bool
    created_at: datetime
    role_count: int = 0


class PermissionListResponse(BaseModel):
    permissions: List[PermissionResponse]
    total: int
    page: int
    page_size: int


@router.post("/", response_model=PermissionResponse, summary="创建权限")
async def create_permission(
    request: PermissionCreateRequest,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_super_admin)
):
    """创建新权限 - 仅超级管理员可操作"""
    try:
        # 检查权限代码是否已存在
        existing_permission = await db.execute(
            select(Permission).where(Permission.permission_code == request.permission_code)
        )
        if existing_permission.scalar_one_or_none():
            raise HTTPException(status_code=400, detail="权限代码已存在")

        # 创建新权限
        new_permission = Permission(
            permission_name=request.permission_name,
            permission_code=request.permission_code,
            module=request.module,
            action=request.action,
            resource=request.resource,
            description=request.description,
            is_system_permission=False  # 新创建的权限不是系统权限
        )
        
        db.add(new_permission)
        await db.commit()
        await db.refresh(new_permission)

        return PermissionResponse(
            id=str(new_permission.id),
            permission_name=new_permission.permission_name,
            permission_code=new_permission.permission_code,
            module=new_permission.module,
            action=new_permission.action,
            resource=new_permission.resource,
            description=new_permission.description,
            is_system_permission=new_permission.is_system_permission,
            created_at=new_permission.created_at,
            role_count=0
        )

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"创建权限失败: {str(e)}")


@router.get("/", response_model=PermissionListResponse, summary="获取权限列表")
async def get_permissions(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=100, description="每页数量"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    module: Optional[str] = Query(None, description="模块筛选"),
    action: Optional[str] = Query(None, description="操作类型筛选"),
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_super_admin)
):
    """获取权限列表 - 仅超级管理员可操作"""
    try:
        # 构建查询条件
        conditions = []
        
        if search:
            conditions.append(
                or_(
                    Permission.permission_name.ilike(f"%{search}%"),
                    Permission.permission_code.ilike(f"%{search}%"),
                    Permission.description.ilike(f"%{search}%")
                )
            )
        
        if module:
            conditions.append(Permission.module == module)
            
        if action:
            conditions.append(Permission.action == action)

        # 获取总数
        count_query = select(func.count(Permission.id))
        if conditions:
            count_query = count_query.where(and_(*conditions))
        
        total_result = await db.execute(count_query)
        total = total_result.scalar()

        # 获取权限列表
        query = select(Permission).order_by(Permission.module, Permission.permission_code)
        if conditions:
            query = query.where(and_(*conditions))
        
        query = query.offset((page - 1) * page_size).limit(page_size)
        
        result = await db.execute(query)
        permissions = result.scalars().all()

        # 获取每个权限被多少个角色使用
        permission_responses = []
        for permission in permissions:
            role_count_result = await db.execute(
                select(func.count(RolePermission.id)).where(RolePermission.permission_id == permission.id)
            )
            role_count = role_count_result.scalar()
            
            permission_responses.append(PermissionResponse(
                id=str(permission.id),
                permission_name=permission.permission_name,
                permission_code=permission.permission_code,
                module=permission.module,
                action=permission.action,
                resource=permission.resource,
                description=permission.description,
                is_system_permission=permission.is_system_permission,
                created_at=permission.created_at,
                role_count=role_count
            ))

        return PermissionListResponse(
            permissions=permission_responses,
            total=total,
            page=page,
            page_size=page_size
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取权限列表失败: {str(e)}")


@router.delete("/remove", summary="移除角色权限")
async def remove_role_permission(
    role_id: str = Query(..., description="角色ID"),
    permission_id: str = Query(..., description="权限ID"),
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_super_admin)
):
    """移除角色的权限 - 仅超级管理员可操作"""
    try:
        result = await db.execute(
            delete(RolePermission).where(
                and_(
                    RolePermission.role_id == role_id,
                    RolePermission.permission_id == permission_id
                )
            )
        )

        if result.rowcount == 0:
            raise HTTPException(status_code=404, detail="角色权限关联不存在")

        await db.commit()
        return {"message": "权限移除成功"}
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"移除权限失败: {str(e)}")


@router.post("/assign", summary="分配角色权限")
async def assign_role_permission(
    role_id: str,
    permission_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_super_admin)
):
    """为角色分配权限 - 仅超级管理员可操作"""
    try:
        # 验证角色是否存在
        role_result = await db.execute(select(Role).where(Role.id == role_id))
        role = role_result.scalar_one_or_none()
        if not role:
            raise HTTPException(status_code=404, detail="角色不存在")

        # 验证权限是否存在
        permission_result = await db.execute(select(Permission).where(Permission.id == permission_id))
        permission = permission_result.scalar_one_or_none()
        if not permission:
            raise HTTPException(status_code=404, detail="权限不存在")

        # 检查是否已经分配过该权限
        existing_assignment = await db.execute(
            select(RolePermission).where(
                and_(
                    RolePermission.role_id == role_id,
                    RolePermission.permission_id == permission_id
                )
            )
        )
        if existing_assignment.scalar_one_or_none():
            raise HTTPException(status_code=400, detail="该角色已拥有此权限")

        # 创建角色权限关联
        role_permission = RolePermission(
            role_id=role_id,
            permission_id=permission_id,
            granted_by=current_user["id"]
        )
        db.add(role_permission)
        await db.commit()

        return {"message": "权限分配成功"}
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"分配权限失败: {str(e)}")


@router.get("/{permission_id}", response_model=PermissionResponse, summary="获取权限详情")
async def get_permission(
    permission_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_super_admin)
):
    """获取权限详情 - 仅超级管理员可操作"""
    try:
        # 获取权限信息
        result = await db.execute(select(Permission).where(Permission.id == permission_id))
        permission = result.scalar_one_or_none()
        
        if not permission:
            raise HTTPException(status_code=404, detail="权限不存在")

        # 获取角色数量
        role_count_result = await db.execute(
            select(func.count(RolePermission.id)).where(RolePermission.permission_id == permission.id)
        )
        role_count = role_count_result.scalar()

        return PermissionResponse(
            id=str(permission.id),
            permission_name=permission.permission_name,
            permission_code=permission.permission_code,
            module=permission.module,
            action=permission.action,
            resource=permission.resource,
            description=permission.description,
            is_system_permission=permission.is_system_permission,
            created_at=permission.created_at,
            role_count=role_count
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取权限详情失败: {str(e)}")


@router.get("/modules/list", summary="获取所有模块列表")
async def get_modules(
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_super_admin)
):
    """获取所有模块列表 - 仅超级管理员可操作"""
    try:
        result = await db.execute(
            select(Permission.module, func.count(Permission.id).label('permission_count'))
            .group_by(Permission.module)
            .order_by(Permission.module)
        )
        modules = result.fetchall()

        return {
            "modules": [
                {
                    "module": module,
                    "permission_count": count
                }
                for module, count in modules
            ],
            "total_modules": len(modules)
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取模块列表失败: {str(e)}")


@router.get("/actions/list", summary="获取所有操作类型列表")
async def get_actions(
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_super_admin)
):
    """获取所有操作类型列表 - 仅超级管理员可操作"""
    try:
        result = await db.execute(
            select(Permission.action, func.count(Permission.id).label('permission_count'))
            .group_by(Permission.action)
            .order_by(Permission.action)
        )
        actions = result.fetchall()

        return {
            "actions": [
                {
                    "action": action,
                    "permission_count": count
                }
                for action, count in actions
            ],
            "total_actions": len(actions)
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取操作类型列表失败: {str(e)}")


@router.put("/{permission_id}", response_model=PermissionResponse, summary="更新权限")
async def update_permission(
    permission_id: str,
    request: PermissionUpdateRequest,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_super_admin)
):
    """更新权限信息 - 仅超级管理员可操作"""
    try:
        # 获取权限
        result = await db.execute(select(Permission).where(Permission.id == permission_id))
        permission = result.scalar_one_or_none()

        if not permission:
            raise HTTPException(status_code=404, detail="权限不存在")

        # 检查是否尝试修改系统权限的关键属性
        if permission.is_system_permission and (request.permission_code or request.module or request.action):
            raise HTTPException(status_code=400, detail="不能修改系统权限的代码、模块和操作类型")

        # 检查权限代码是否已存在（如果要修改的话）
        if request.permission_code and request.permission_code != permission.permission_code:
            existing_permission = await db.execute(
                select(Permission).where(
                    and_(Permission.permission_code == request.permission_code, Permission.id != permission_id)
                )
            )
            if existing_permission.scalar_one_or_none():
                raise HTTPException(status_code=400, detail="权限代码已存在")

        # 更新权限信息
        update_data = request.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(permission, field, value)

        await db.commit()
        await db.refresh(permission)

        # 获取角色数量
        role_count_result = await db.execute(
            select(func.count(RolePermission.id)).where(RolePermission.permission_id == permission.id)
        )
        role_count = role_count_result.scalar()

        return PermissionResponse(
            id=str(permission.id),
            permission_name=permission.permission_name,
            permission_code=permission.permission_code,
            module=permission.module,
            action=permission.action,
            resource=permission.resource,
            description=permission.description,
            is_system_permission=permission.is_system_permission,
            created_at=permission.created_at,
            role_count=role_count
        )

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"更新权限失败: {str(e)}")


@router.delete("/{permission_id}", summary="删除权限")
async def delete_permission(
    permission_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_super_admin)
):
    """删除权限 - 仅超级管理员可操作"""
    try:
        # 获取权限
        result = await db.execute(select(Permission).where(Permission.id == permission_id))
        permission = result.scalar_one_or_none()

        if not permission:
            raise HTTPException(status_code=404, detail="权限不存在")

        # 检查是否为系统权限
        if permission.is_system_permission:
            raise HTTPException(status_code=400, detail="不能删除系统权限")

        # 检查是否有角色使用此权限
        role_permission_result = await db.execute(
            select(func.count(RolePermission.id)).where(RolePermission.permission_id == permission_id)
        )
        role_count = role_permission_result.scalar()

        if role_count > 0:
            raise HTTPException(status_code=400, detail=f"无法删除权限，还有 {role_count} 个角色使用此权限")

        # 删除权限
        await db.delete(permission)
        await db.commit()

        return {"message": "权限删除成功", "permission_id": permission_id}

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"删除权限失败: {str(e)}")


@router.get("/{permission_id}/roles", summary="获取使用此权限的角色列表")
async def get_permission_roles(
    permission_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_super_admin)
):
    """获取使用此权限的角色列表 - 仅超级管理员可操作"""
    try:
        # 检查权限是否存在
        permission_result = await db.execute(select(Permission).where(Permission.id == permission_id))
        permission = permission_result.scalar_one_or_none()

        if not permission:
            raise HTTPException(status_code=404, detail="权限不存在")

        # 获取使用此权限的角色
        roles_result = await db.execute(
            select(Role).join(RolePermission).where(RolePermission.permission_id == permission_id)
            .order_by(Role.role_code)
        )
        roles = roles_result.scalars().all()

        return {
            "permission": {
                "id": str(permission.id),
                "permission_name": permission.permission_name,
                "permission_code": permission.permission_code
            },
            "roles": [
                {
                    "id": str(role.id),
                    "role_name": role.role_name,
                    "role_code": role.role_code,
                    "role_type": role.role_type,
                    "is_active": role.is_active,
                    "is_system_role": role.is_system_role
                }
                for role in roles
            ],
            "total_roles": len(roles)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取角色未拥有权限列表失败: {str(e)}")

@router.get("/modules/statistics", summary="统计权限模块列表")
async def get_permission_modules_statistics(
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_super_admin)
):
    """获取权限模块统计信息 - 仅超级管理员可操作"""
    try:
        # 查询所有模块及其权限数量
        result = await db.execute(
            select(Permission.module, func.count(Permission.id).label('permission_count'))
            .group_by(Permission.module)
            .order_by(Permission.module)
        )
        modules = result.fetchall()

        # 计算总权限数
        total_permissions_result = await db.execute(
            select(func.count(Permission.id))
        )
        total_permissions = total_permissions_result.scalar()

        return {
            "modules": [
                {
                    "module": module,
                    "permission_count": count,
                    "percentage": round((count / total_permissions * 100), 2) if total_permissions > 0 else 0
                }
                for module, count in modules
            ],
            "total_modules": len(modules),
            "total_permissions": total_permissions
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取权限模块统计失败: {str(e)}")


@router.get("/actions/statistics", summary="统计权限操作列表")
async def get_permission_actions_statistics(
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_super_admin)
):
    """获取权限操作统计信息 - 仅超级管理员可操作"""
    try:
        # 查询所有操作类型及其权限数量
        result = await db.execute(
            select(Permission.action, func.count(Permission.id).label('permission_count'))
            .group_by(Permission.action)
            .order_by(Permission.action)
        )
        actions = result.fetchall()

        # 计算总权限数
        total_permissions_result = await db.execute(
            select(func.count(Permission.id))
        )
        total_permissions = total_permissions_result.scalar()

        return {
            "actions": [
                {
                    "action": action,
                    "permission_count": count,
                    "percentage": round((count / total_permissions * 100), 2) if total_permissions > 0 else 0
                }
                for action, count in actions
            ],
            "total_actions": len(actions),
            "total_permissions": total_permissions
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取权限操作统计失败: {str(e)}")


@router.get("/roles/{role_id}/available", summary="获取角色未拥有的权限列表")
async def get_available_permissions_for_role(
    role_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_super_admin)
):
    """获取某个角色未拥有的权限列表 - 仅超级管理员可操作"""
    try:
        # 验证角色是否存在
        role_result = await db.execute(select(Role).where(Role.id == role_id))
        role = role_result.scalar_one_or_none()
        if not role:
            raise HTTPException(status_code=404, detail="角色不存在")

        # 获取角色已拥有的权限ID列表
        existing_permissions_result = await db.execute(
            select(RolePermission.permission_id).where(RolePermission.role_id == role_id)
        )
        existing_permission_ids = [str(row[0]) for row in existing_permissions_result.fetchall()]

        # 查询角色未拥有的权限
        if existing_permission_ids:
            # 如果角色已有权限，排除这些权限
            permissions_result = await db.execute(
                select(Permission)
                .where(~Permission.id.in_(existing_permission_ids))
                .order_by(Permission.module, Permission.permission_code)
            )
        else:
            # 如果角色没有任何权限，返回所有权限
            permissions_result = await db.execute(
                select(Permission)
                .order_by(Permission.module, Permission.permission_code)
            )

        permissions = permissions_result.scalars().all()

        # 构建响应数据
        available_permissions = []
        for permission in permissions:
            available_permissions.append({
                "id": str(permission.id),
                "permission_name": permission.permission_name,
                "permission_code": permission.permission_code,
                "module": permission.module,
                "action": permission.action,
                "resource": permission.resource,
                "description": permission.description,
                "is_system_permission": permission.is_system_permission,
                "created_at": permission.created_at.isoformat()
            })

        return {
            "role": {
                "id": str(role.id),
                "role_name": role.role_name,
                "role_code": role.role_code,
                "description": role.description
            },
            "available_permissions": available_permissions,
            "total_available": len(available_permissions),
            "total_existing": len(existing_permission_ids)
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取角色未拥有权限列表失败: {str(e)}")


# ==================== 角色权限分配管理 ====================

class UpdateRolePermissionsRequest(BaseModel):
    permission_ids: List[str]
    """要分配给角色的权限ID列表（UUID格式）"""


@router.put("/roles/{role_id}/permissions", summary="更新角色权限")
async def update_role_permissions(
    role_id: str,
    request: UpdateRolePermissionsRequest,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_admin)
):
    """更新角色的权限分配"""
    try:
        # 验证角色是否存在
        role_result = await db.execute(
            select(Role).where(and_(Role.id == role_id, Role.is_active == True))
        )
        role = role_result.scalar_one_or_none()
        if not role:
            raise HTTPException(status_code=404, detail="角色不存在")

        # 验证所有权限ID是否存在
        if request.permission_ids:
            permissions_result = await db.execute(
                select(Permission.id).where(Permission.id.in_(request.permission_ids))
            )
            existing_permission_ids = [str(row[0]) for row in permissions_result.fetchall()]

            invalid_ids = set(request.permission_ids) - set(existing_permission_ids)
            if invalid_ids:
                raise HTTPException(
                    status_code=400,
                    detail=f"以下权限ID不存在: {list(invalid_ids)}"
                )

        # 删除该角色的所有现有权限
        await db.execute(
            delete(RolePermission).where(RolePermission.role_id == role_id)
        )

        # 添加新的权限分配
        if request.permission_ids:
            new_assignments = [
                RolePermission(role_id=role_id, permission_id=permission_id)
                for permission_id in request.permission_ids
            ]
            db.add_all(new_assignments)

        await db.commit()

        # 返回更新后的角色权限信息
        permissions_result = await db.execute(
            select(Permission).join(RolePermission).where(RolePermission.role_id == role_id)
        )
        permissions = permissions_result.scalars().all()

        return {
            "message": "角色权限更新成功",
            "role": {
                "id": role.id,
                "role_code": role.role_code,
                "role_name": role.role_name,
                "description": role.description
            },
            "permissions": [
                {
                    "id": str(perm.id),
                    "permission_name": perm.permission_name,
                    "permission_code": perm.permission_code,
                    "module": perm.module,
                    "action": perm.action,
                    "resource": perm.resource,
                    "description": perm.description
                }
                for perm in permissions
            ],
            "permission_count": len(permissions)
        }

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"更新角色权限失败: {str(e)}")


@router.get("/roles-with-permissions", summary="获取角色权限列表")
async def get_roles_with_permissions(
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_admin)
):
    """获取所有角色及其拥有的权限"""
    try:
        # 查询所有角色
        roles_result = await db.execute(
            select(Role).where(Role.is_active == True).order_by(Role.role_code)
        )
        roles = roles_result.scalars().all()

        result = []
        for role in roles:
            # 获取角色的权限
            permissions_result = await db.execute(
                select(
                    Permission.id,
                    Permission.permission_name,
                    Permission.permission_code,
                    Permission.module
                ).select_from(
                    RolePermission
                ).join(
                    Permission, RolePermission.permission_id == Permission.id
                ).where(
                    RolePermission.role_id == role.id
                ).order_by(Permission.module, Permission.permission_code)
            )

            permissions = []
            for perm in permissions_result.fetchall():
                permissions.append({
                    "id": str(perm.id),
                    "permission_name": perm.permission_name,
                    "permission_code": perm.permission_code,
                    "module": perm.module
                })

            result.append({
                "id": str(role.id),
                "role_name": role.role_name,
                "role_code": role.role_code,
                "permissions": permissions,
                "permission_count": len(permissions)
            })

        return result

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取角色权限列表失败: {str(e)}")
