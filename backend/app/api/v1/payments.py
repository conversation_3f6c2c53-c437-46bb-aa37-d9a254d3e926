"""
支付管理API路由
"""
from typing import Optional, List
from uuid import UUID
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, status, Request, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.database import get_db
from app.dependencies import get_current_user, get_current_admin
from app.models.user import User
from app.services.payment_service import PaymentService, RefundService
from app.schemas.payment import (
    PaymentCreateSchema,
    PaymentResponseSchema,
    PaymentListResponseSchema,
    PaymentConfirmSchema,
    PaymentCallbackSchema,
    OfflinePaymentSchema,
    PaymentMethodSchema,
    PaymentMethodListSchema,
    PaymentStatisticsSchema,
    RefundCreateSchema,
    RefundApprovalSchema,
    RefundRejectionSchema,
    RefundResponseSchema,
    RefundListResponseSchema,
)
from app.config import PAYMENT_METHODS

router = APIRouter(prefix="/payments", tags=["支付管理"])


# ========== 支付记录管理 ==========
@router.get("", response_model=PaymentListResponseSchema, summary="查询支付记录")
async def get_payments(
    order_id: Optional[UUID] = Query(None, description="订单ID"),
    payment_status: Optional[str] = Query(None, description="支付状态"),
    payment_method: Optional[str] = Query(None, description="支付方式"),
    start_date: Optional[datetime] = Query(None, description="开始时间"),
    end_date: Optional[datetime] = Query(None, description="结束时间"),
    page: int = Query(1, ge=1, description="页码"),
    limit: int = Query(20, ge=1, le=100, description="每页数量"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    查询支付记录
    - 普通用户只能查看自己的支付记录
    - 管理员可以查看所有支付记录
    """
    service = PaymentService(db)
    
    # 普通用户只能查看自己的支付记录
    user_id = None if current_user.role == 'admin' else current_user.id
    
    payments, total = await service.list_payments(
        user_id=user_id,
        order_id=order_id,
        payment_status=payment_status,
        payment_method=payment_method,
        start_date=start_date,
        end_date=end_date,
        page=page,
        limit=limit
    )
    
    return PaymentListResponseSchema(
        total=total,
        page=page,
        limit=limit,
        items=[PaymentResponseSchema.model_validate(p) for p in payments]
    )


@router.get("/methods", response_model=PaymentMethodListSchema, summary="获取支付方式")
async def get_payment_methods():
    """获取可用的支付方式列表（公开接口）"""
    methods = []
    for method_key, method_config in PAYMENT_METHODS.items():
        if method_config["is_active"]:
            methods.append(PaymentMethodSchema(
                method=method_key,
                **method_config
            ))
    
    return PaymentMethodListSchema(methods=methods)


@router.get("/statistics", response_model=PaymentStatisticsSchema, summary="获取支付统计")
async def get_payment_statistics(
    start_date: Optional[datetime] = Query(None, description="开始时间"),
    end_date: Optional[datetime] = Query(None, description="结束时间"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取支付统计信息"""
    service = PaymentService(db)
    
    # 普通用户只能查看自己的统计
    user_id = None if current_user.role == 'admin' else current_user.id
    
    stats = await service.get_payment_statistics(
        user_id=user_id,
        start_date=start_date,
        end_date=end_date
    )
    
    return PaymentStatisticsSchema(**stats)


@router.get("/{payment_id}", response_model=PaymentResponseSchema, summary="获取支付详情")
async def get_payment_detail(
    payment_id: UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取支付详情，包含完整的支付信息和关联订单信息"""
    service = PaymentService(db)
    payment = await service.get_payment(payment_id)
    
    if not payment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="支付记录不存在"
        )
    
    # 权限检查：普通用户只能查看自己的支付记录
    if current_user.role != 'admin':
        # 需要通过订单检查用户权限
        from app.models.order import Order
        from sqlalchemy import select
        
        order_query = select(Order).where(Order.id == payment.order_id)
        result = await db.execute(order_query)
        order = result.scalar_one_or_none()
        
        if not order or order.user_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权查看此支付记录"
            )
    
    return PaymentResponseSchema.model_validate(payment)


@router.post("", response_model=PaymentResponseSchema, summary="创建支付记录")
async def create_payment(
    payment_data: PaymentCreateSchema,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    创建支付记录（通常由订单系统调用）
    1. 验证订单状态
    2. 创建支付记录
    3. 调用第三方支付接口
    4. 返回支付参数（如微信支付的prepay_id）
    """
    service = PaymentService(db)
    
    try:
        # 创建支付记录
        payment = await service.create_payment(
            order_id=payment_data.order_id,
            payment_method=payment_data.payment_method,
            amount=payment_data.amount
        )
        
        # 根据支付方式处理
        response = PaymentResponseSchema.model_validate(payment)
        
        if payment_data.payment_method == 'wechat':
            # 处理微信支付
            prepay_params = await service.process_wechat_payment(payment)
            response.prepay_params = prepay_params
        elif payment_data.payment_method == 'alipay':
            # 处理支付宝支付
            prepay_params = await service.process_alipay_payment(payment)
            response.prepay_params = prepay_params
        
        return response
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建支付记录失败: {str(e)}"
        )


@router.post("/{payment_id}/confirm", response_model=PaymentResponseSchema, summary="确认支付")
async def confirm_payment(
    payment_id: UUID,
    confirm_data: PaymentConfirmSchema,
    current_user: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_db)
):
    """管理员手动确认支付（用于线下支付）"""
    service = PaymentService(db)

    try:
        payment = await service.confirm_offline_payment(
            payment_id=payment_id,
            transaction_no=confirm_data.transaction_no,
            paid_at=confirm_data.paid_at
        )
        return PaymentResponseSchema.model_validate(payment)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.post("/{payment_id}/mock-success", response_model=PaymentResponseSchema, summary="模拟支付成功")
async def mock_payment_success(
    payment_id: UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    模拟支付成功（开发测试用）
    在没有真实支付系统时，用户可以直接"支付成功"
    """
    service = PaymentService(db)

    try:
        # 生成模拟交易号
        mock_transaction_no = f"mock_{int(datetime.utcnow().timestamp())}_{payment_id}"

        # 更新支付状态为成功
        payment = await service.update_payment_status(
            payment_id=payment_id,
            status='success',
            transaction_no=mock_transaction_no
        )

        return PaymentResponseSchema.model_validate(payment)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.post("/{payment_id}/cancel", response_model=PaymentResponseSchema, summary="取消支付")
async def cancel_payment(
    payment_id: UUID,
    reason: str = Query(..., description="取消原因"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """取消未完成的支付"""
    service = PaymentService(db)
    
    try:
        payment = await service.cancel_payment(payment_id, reason)
        return PaymentResponseSchema.model_validate(payment)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


# ========== 支付回调接口 ==========
@router.post("/callback/wechat", summary="微信支付回调")
async def wechat_payment_callback(
    request: Request,
    db: AsyncSession = Depends(get_db)
):
    """
    微信支付异步回调
    1. 验证签名
    2. 解密回调数据
    3. 更新支付状态
    4. 更新订单状态
    5. 发送通知
    6. 返回应答
    """
    service = PaymentService(db)
    
    try:
        # 获取回调数据
        callback_data = await request.json()
        
        # 处理回调
        payment = await service.handle_payment_callback(callback_data, 'wechat')
        
        # 返回成功响应
        return {"code": "SUCCESS", "message": "成功"}
    except Exception as e:
        # 返回失败响应
        return {"code": "FAIL", "message": str(e)}


@router.post("/callback/alipay", summary="支付宝支付回调")
async def alipay_payment_callback(
    request: Request,
    db: AsyncSession = Depends(get_db)
):
    """支付宝异步回调处理"""
    service = PaymentService(db)
    
    try:
        # 获取回调数据
        callback_data = await request.form()
        callback_dict = dict(callback_data)
        
        # 处理回调
        payment = await service.handle_payment_callback(callback_dict, 'alipay')
        
        # 返回成功响应
        return "success"
    except Exception as e:
        # 返回失败响应
        return "fail"


@router.post("/callback/offline", response_model=PaymentResponseSchema, summary="线下支付确认")
async def offline_payment_confirm(
    payment_data: OfflinePaymentSchema,
    current_user: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_db)
):
    """管理员确认线下支付"""
    service = PaymentService(db)
    
    try:
        payment = await service.confirm_offline_payment(
            payment_id=payment_data.payment_id,
            transaction_no=payment_data.transaction_no,
            paid_at=payment_data.paid_at
        )
        return PaymentResponseSchema.model_validate(payment)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


# ========== 退款管理 ==========
@router.get("/refunds", response_model=RefundListResponseSchema, summary="查询退款记录")
async def get_refunds(
    order_id: Optional[UUID] = Query(None, description="订单ID"),
    refund_status: Optional[str] = Query(None, description="退款状态"),
    start_date: Optional[datetime] = Query(None, description="开始时间"),
    end_date: Optional[datetime] = Query(None, description="结束时间"),
    page: int = Query(1, ge=1, description="页码"),
    limit: int = Query(20, ge=1, le=100, description="每页数量"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """查询退款记录"""
    service = RefundService(db)
    
    # 普通用户只能查看自己的退款记录
    user_id = None if current_user.role == 'admin' else current_user.id
    
    refunds, total = await service.list_refunds(
        user_id=user_id,
        order_id=order_id,
        refund_status=refund_status,
        start_date=start_date,
        end_date=end_date,
        page=page,
        limit=limit
    )
    
    return RefundListResponseSchema(
        total=total,
        page=page,
        limit=limit,
        items=[RefundResponseSchema.model_validate(r) for r in refunds]
    )


@router.get("/refunds/{refund_id}", response_model=RefundResponseSchema, summary="获取退款详情")
async def get_refund_detail(
    refund_id: UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取退款详情"""
    service = RefundService(db)
    refund = await service.get_refund(refund_id)
    
    if not refund:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="退款记录不存在"
        )
    
    # 权限检查（需要通过支付记录和订单检查）
    if current_user.role != 'admin':
        payment_service = PaymentService(db)
        payment = await payment_service.get_payment(refund.payment_id)
        
        from app.models.order import Order
        from sqlalchemy import select
        
        order_query = select(Order).where(Order.id == payment.order_id)
        result = await db.execute(order_query)
        order = result.scalar_one_or_none()
        
        if not order or order.user_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权查看此退款记录"
            )
    
    return RefundResponseSchema.model_validate(refund)


@router.post("/refunds", response_model=RefundResponseSchema, summary="申请退款")
async def create_refund(
    refund_data: RefundCreateSchema,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    申请退款
    1. 验证订单状态和支付状态
    2. 创建退款申请
    3. 发送审批通知
    """
    service = RefundService(db)
    
    try:
        refund = await service.create_refund_request(
            payment_id=refund_data.payment_id,
            order_id=refund_data.order_id,
            amount=refund_data.refund_amount,
            reason=refund_data.refund_reason
        )
        return RefundResponseSchema.model_validate(refund)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.post("/refunds/{refund_id}/approve", response_model=RefundResponseSchema, summary="批准退款")
async def approve_refund(
    refund_id: UUID,
    approval_data: RefundApprovalSchema,
    current_user: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_db)
):
    """管理员批准退款申请"""
    service = RefundService(db)
    
    try:
        if approval_data.approve:
            refund = await service.approve_refund(
                refund_id=refund_id,
                approver_id=current_user.id,
                note=approval_data.note
            )
        else:
            refund = await service.reject_refund(
                refund_id=refund_id,
                approver_id=current_user.id,
                reason=approval_data.note or "管理员拒绝"
            )
        return RefundResponseSchema.model_validate(refund)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.post("/refunds/{refund_id}/reject", response_model=RefundResponseSchema, summary="拒绝退款")
async def reject_refund(
    refund_id: UUID,
    rejection_data: RefundRejectionSchema,
    current_user: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_db)
):
    """管理员拒绝退款申请"""
    service = RefundService(db)
    
    try:
        refund = await service.reject_refund(
            refund_id=refund_id,
            approver_id=current_user.id,
            reason=rejection_data.reason
        )
        return RefundResponseSchema.model_validate(refund)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.post("/refunds/{refund_id}/process", response_model=RefundResponseSchema, summary="处理退款")
async def process_refund(
    refund_id: UUID,
    current_user: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_db)
):
    """
    财务人员处理退款
    1. 调用第三方退款接口
    2. 更新退款状态
    3. 更新订单状态
    4. 记录退款流水
    """
    service = RefundService(db)
    
    try:
        refund = await service.process_refund(refund_id)
        return RefundResponseSchema.model_validate(refund)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.post("/refunds/{refund_id}/cancel", response_model=RefundResponseSchema, summary="取消退款申请")
async def cancel_refund(
    refund_id: UUID,
    reason: str = Query(..., description="取消原因"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """申请人取消退款申请"""
    service = RefundService(db)
    
    try:
        refund = await service.cancel_refund(
            refund_id=refund_id,
            user_id=current_user.id,
            reason=reason
        )
        return RefundResponseSchema.model_validate(refund)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


import logging
import json
import uuid
from datetime import datetime
import time
import hashlib

from fastapi.responses import HTMLResponse
from wechatpayv3 import WeChatPay, WeChatPayType

# 微信支付配置
MCHID = '1719963038'
PRIVATE_KEY='''***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'''
CERT_SERIAL_NO = '151F61A94DEF2470D3BF6397273D475B3BA9C3AD'
APIV3_KEY = '6WyJA7mlKSJzD1rRJKHQgnRDn4Ousq5R'
APPID = 'wx56bce02bd73c93fa'
NOTIFY_URL = 'https://cloud.web.webideploy.com/api/public/payment/wechat/callback'
CERT_DIR = None

# 日志配置
LOGGER = logging.getLogger("demo")

# 其他配置
PARTNER_MODE = False
PROXY = None
TIMEOUT = (10, 30)

# 初始化微信支付
wxpay = WeChatPay(
    wechatpay_type=WeChatPayType.NATIVE,
    mchid=MCHID,
    private_key=PRIVATE_KEY,
    cert_serial_no=CERT_SERIAL_NO,
    apiv3_key=APIV3_KEY,
    appid=APPID,
    notify_url=NOTIFY_URL,
    cert_dir=CERT_DIR,
    logger=LOGGER,
    partner_mode=PARTNER_MODE,
    proxy=PROXY,
    timeout=TIMEOUT)

@router.post("/wxpay/native", response_model=PaymentResponseSchema, summary="微信支付native 测试接口")
async def wxpay_native(
):
    """Native扫码支付"""
    # 获取当前时间
    now = datetime.now()
    out_trade_no = f'demo_trade_no_{now.strftime("%Y%m%d%H%M%S")}'
    description = 'demo-description'
    amount = 100
    code, message = wxpay.pay(
        description=description,
        out_trade_no=out_trade_no,
        amount={'total': amount},
        pay_type=WeChatPayType.NATIVE
    )
    LOGGER.info(out_trade_no)
    LOGGER.info(message)
    payment = {
        "id": str(uuid.uuid4()),
        "order_id": str(uuid.uuid4()),
        "amount": amount,
        "payment_method": "wechat",
        "payment_status": "PENDING",
        "created_at": now,
        "prepay_params": json.loads(message)
    }
    return PaymentResponseSchema.model_validate(payment)

@router.get("/wxpay/withdraw", response_model=PaymentResponseSchema, summary="微信提现withdraw 测试接口")
async def wxpay_native(
):
    now = datetime.now()
    out_bill_no = f'demotradeno{now.strftime("%Y%m%d%H%M%S")}'
    amount = 50
    code, message = wxpay.mch_transfer_bills(
        out_bill_no=out_bill_no,  # 商户系统内部唯一单号（数字/字母组成）
        transfer_scene_id="1005",  # 转账场景ID（如1001表示福利发放，参考微信支付文档）
        openid="oN2envloqb_NE0VrzEVwYaLWpdIw",  # 收款用户的OpenID（商户APPID下的用户）
        transfer_amount=amount,  # 转账金额（分）
        transfer_remark="提现",  # 收款用户可见的备注（最多32字符）
        user_name="黄华",  # 收款用户姓名（金额≥2000元时必填，自动加密）
        # 可选参数
        #user_recv_perception="提现",  # 用户收款时感知到的原因
        transfer_scene_report_infos=[  # 转账场景报备信息（根据场景要求填写）
            {
                        "info_type": "岗位类型",
                        "info_content": "推广员"
                    },
                    {
                        "info_type": "报酬说明",
                        "info_content": "推广佣金提现"
                    }
        ]
    )

    LOGGER.info(out_bill_no)
    LOGGER.info(message)

    payment = json.loads(message)
    
    timestamp = str(int(time.time()))
    nonce = 'test123'
    token = 'whatsthefuck666666'
    tmp_list = [token, timestamp, nonce]
    # 2. 将三个参数字符串拼接成一个字符串
    tmp_str = ''.join(tmp_list)

    # 3. 进行sha1加密
    sha1_obj = hashlib.sha1()
    sha1_obj.update(tmp_str.encode('utf-8'))
    
    signature = sha1_obj.hexdigest()


    # 生成确认页面HTML
    html_content = f"""
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>确认提现</title>
        <script src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>
        <style>
            * {{ margin: 0; padding: 0; box-sizing: border-box; }}
            body {{ font-family: -apple-system, BlinkMacSystemFont, sans-serif; background: #f5f5f5; padding: 20px; }}
            .container {{ max-width: 400px; margin: 0 auto; background: white; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
            .header {{ background: #07c160; color: white; padding: 20px; text-align: center; }}
            .content {{ padding: 20px; }}
            .withdrawal-info {{ background: #f0f9ff; border: 1px solid #0ea5e9; border-radius: 8px; padding: 15px; margin-bottom: 20px; }}
            .amount {{ font-size: 24px; font-weight: bold; color: #0ea5e9; text-align: center; margin-bottom: 10px; }}
            .info-row {{ display: flex; justify-content: space-between; margin-bottom: 8px; font-size: 14px; }}
            .label {{ color: #666; }}
            .value {{ font-weight: 500; }}
            .form-group {{ margin-bottom: 15px; }}
            .form-label {{ display: block; margin-bottom: 5px; font-weight: 500; color: #333; }}
            .form-input {{ width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 16px; }}
            .security-tip {{ background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 6px; padding: 12px; margin-bottom: 20px; font-size: 12px; color: #856404; }}
            .buttons {{ display: flex; gap: 10px; }}
            .btn {{ flex: 1; padding: 12px; border: none; border-radius: 6px; font-size: 16px; cursor: pointer; }}
            .btn-cancel {{ background: #f5f5f5; color: #666; }}
            .btn-confirm {{ background: #07c160; color: white; }}
            .status {{ text-align: center; padding: 10px; margin-top: 15px; border-radius: 6px; font-size: 14px; display: none; }}
            .status.success {{ background: #d4edda; color: #155724; }}
            .status.error {{ background: #f8d7da; color: #721c24; }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>💰 确认提现</h1>
                <p>微信提现确认</p>
            </div>
            
            <div class="content">
                <div class="withdrawal-info">
                    <div class="amount">¥0.5</div>
                    <div class="info-row">
                        <span class="label">提现单号：</span>
                        <span class="value">2025081401001</span>
                    </div>
                </div>
                
                <div class="security-tip">
                    <strong>🔒 安全提示：</strong><br>
                    • 请确认提现金额无误<br>
                    • 资金将转入您绑定的微信账户<br>
                    • 如有疑问请联系客服
                </div>
                
                <div class="buttons">
                    <button class="btn btn-cancel" onclick="cancelWithdrawal()">取消</button>
                    <button class="btn btn-confirm" onclick="confirmWithdrawal()">确认提现</button>
                </div>
                
                <div id="status" class="status"></div>
            </div>
        </div>

        <script>
            wx.config({{
                debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
                appId: '{APPID}', // 必填，服务号的唯一标识
                timestamp: {timestamp}, // 必填，生成签名的时间戳
                nonceStr: '{nonce}', // 必填，生成签名的随机串
                signature: '{signature}',// 必填，签名
                jsApiList: ['requestMerchantTransfer'] // 必填，需要使用的JS接口列表
            }});
            wx.ready(function () {{
                wx.checkJsApi({{
                    jsApiList: ['requestMerchantTransfer'],
                    success: function (res) {{
                        if (res.checkResult['requestMerchantTransfer']) {{
                            WeixinJSBridge.invoke('requestMerchantTransfer', {{
                                mchId: '{MCHID}',
                                appId: '{APPID}',
                                package: '{payment['package_info']}',
                            }},
                            function (res) {{
                                if (res.err_msg === 'requestMerchantTransfer:ok') {{
                                // res.err_msg将在页面展示成功后返回应用时返回success，并不代表付款成功
                                }}
                            }}
                            );
                        }} else {{
                            alert('你的微信版本过低，请更新至最新版本。');
                        }}
                    }}
                }});
            }});
        </script>
    </body>
    </html>
    """


    return HTMLResponse(content=html_content) 