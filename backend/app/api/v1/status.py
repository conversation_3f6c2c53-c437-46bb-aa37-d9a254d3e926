"""
状态管理API路由
"""
from typing import Optional, List
from uuid import UUID
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.database import get_db
from app.dependencies import get_current_user, get_current_admin
from app.models.user import User
from app.services.status_service import StatusService
from app.core.status_machine import OrderStateMachine
from app.schemas.status import (
    OrderStatusLogCreateSchema,
    OrderStatusLogSchema,
    OrderStatusLogListSchema,
    OrderStatusHistorySchema,
    StatusTransitionsResponseSchema,
    StatusStatisticsSchema,
    StatusInfoSchema,
    StatusTransitionRuleSchema,
    ORDER_STATUS_INFO,
    ORDER_STATUS_TRANSITIONS,
    TRANSITION_REASONS
)

router = APIRouter(prefix="/status", tags=["状态管理"])


@router.get("/order-status-logs", response_model=OrderStatusLogListSchema, summary="获取状态日志")
async def get_status_logs(
    order_id: Optional[UUID] = Query(None, description="订单ID"),
    from_status: Optional[str] = Query(None, description="原状态"),
    to_status: Optional[str] = Query(None, description="目标状态"),
    changed_by: Optional[UUID] = Query(None, description="变更人ID"),
    start_date: Optional[datetime] = Query(None, description="开始时间"),
    end_date: Optional[datetime] = Query(None, description="结束时间"),
    page: int = Query(1, ge=1, description="页码"),
    limit: int = Query(20, ge=1, le=100, description="每页数量"),
    current_user: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_db)
):
    """
    查询状态变更记录（管理员权限）
    
    支持多种过滤条件：
    - 按订单ID过滤
    - 按状态转换过滤
    - 按变更人过滤
    - 按时间范围过滤
    """
    service = StatusService(db)
    
    try:
        logs, total = await service.list_status_logs(
            order_id=order_id,
            from_status=from_status,
            to_status=to_status,
            changed_by=changed_by,
            start_date=start_date,
            end_date=end_date,
            page=page,
            limit=limit
        )
        
        # 转换为响应模型
        items = []
        for log in logs:
            item = OrderStatusLogSchema.model_validate(log)
            # 添加订单号和变更人姓名
            if log.order:
                item.order_no = log.order.order_no
            if log.changer:
                item.changed_by_name = log.changer.full_name
            items.append(item)
        
        return OrderStatusLogListSchema(
            total=total,
            page=page,
            limit=limit,
            items=items
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"查询状态日志失败: {str(e)}"
        )


@router.get("/order-status-logs/{order_id}", response_model=OrderStatusHistorySchema, summary="获取订单状态历史")
async def get_order_status_history(
    order_id: UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取订单状态历史
    
    返回订单的完整状态变更历史，包括：
    - 状态转换时间线
    - 每个状态的停留时间
    - 变更原因和操作人
    - 总处理时长
    """
    service = StatusService(db)
    
    try:
        # 权限检查：管理员可以查看所有，普通用户只能查看自己的订单
        if current_user.role != 'admin':
            from app.models.order import Order
            from sqlalchemy import select
            
            order_query = select(Order).where(Order.id == order_id)
            result = await db.execute(order_query)
            order = result.scalar_one_or_none()
            
            if not order:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="订单不存在"
                )
            
            if order.user_id != current_user.id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="无权查看此订单状态历史"
                )
        
        history = await service.get_order_status_history(order_id)
        return history
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取状态历史失败: {str(e)}"
        )


@router.post("/order-status-logs", response_model=OrderStatusLogSchema, summary="创建状态日志")
async def create_status_log(
    log_data: OrderStatusLogCreateSchema,
    current_user: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_db)
):
    """
    记录状态变更（系统级接口）
    
    通常由其他模块调用，用于记录订单状态变化。
    包含状态转换验证，确保符合业务规则。
    """
    service = StatusService(db)
    
    try:
        # 记录状态变更
        log = await service.log_status_change(
            order_id=log_data.order_id,
            to_status=log_data.to_status,
            changed_by=current_user.id,
            change_reason=log_data.change_reason,
            metadata=log_data.metadata
        )
        
        # 获取订单信息
        from app.models.order import Order
        from sqlalchemy import select
        
        order_query = select(Order).where(Order.id == log_data.order_id)
        result = await db.execute(order_query)
        order = result.scalar_one_or_none()
        
        # 构建响应
        response = OrderStatusLogSchema.model_validate(log)
        if order:
            response.order_no = order.order_no
        response.changed_by_name = current_user.full_name
        
        return response
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"记录状态变更失败: {str(e)}"
        )


@router.get("/order-status/transitions", response_model=StatusTransitionsResponseSchema, summary="获取状态转换规则")
async def get_status_transitions():
    """
    获取状态流转规则（公开接口）
    
    返回：
    - 所有状态的详细信息（名称、颜色、图标等）
    - 状态转换规则（每个状态允许的下一状态）
    - 状态转换的可能原因
    
    前端可以使用这些信息来显示状态流程图和控制状态操作按钮。
    """
    # 构建状态信息
    statuses = {}
    for status_code, info in ORDER_STATUS_INFO.items():
        statuses[status_code] = StatusInfoSchema(**info)
    
    # 构建转换规则
    transitions = {}
    for status_code, allowed_next in ORDER_STATUS_TRANSITIONS.items():
        transitions[status_code] = StatusTransitionRuleSchema(
            allowed_next=allowed_next,
            auto_transition=None,  # 可以扩展自动转换配置
            requires_approval=False  # 可以扩展审批要求
        )
    
    return StatusTransitionsResponseSchema(
        statuses=statuses,
        transitions=transitions,
        transition_reasons=TRANSITION_REASONS
    )


@router.post("/order-status/validate", summary="验证状态转换")
async def validate_status_transition(
    order_id: UUID,
    to_status: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    验证状态转换是否合法
    
    在执行状态变更前调用此接口验证是否允许转换。
    返回验证结果和详细信息。
    """
    service = StatusService(db)
    
    try:
        is_valid, message = await service.validate_status_transition(order_id, to_status)
        
        if not is_valid:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=message
            )
        
        # 获取可能的转换原因
        from app.models.order import Order
        from sqlalchemy import select
        
        order_query = select(Order).where(Order.id == order_id)
        result = await db.execute(order_query)
        order = result.scalar_one_or_none()
        
        if order:
            current_status = order.order_status.value if hasattr(order.order_status, 'value') else order.order_status
            state_machine = OrderStateMachine(current_status)
            reasons = state_machine.get_transition_reasons(to_status)
        else:
            reasons = []
        
        return {
            "valid": True,
            "message": message,
            "suggested_reasons": reasons
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"验证失败: {str(e)}"
        )


@router.get("/order-status/statistics", response_model=StatusStatisticsSchema, summary="获取状态统计")
async def get_status_statistics(
    start_date: Optional[datetime] = Query(None, description="开始时间"),
    end_date: Optional[datetime] = Query(None, description="结束时间"),
    current_user: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_db)
):
    """
    获取状态统计信息（管理员权限）
    
    返回：
    - 各状态订单数量分布
    - 平均状态转换时间
    - 异常状态转换统计
    """
    service = StatusService(db)
    
    try:
        stats = await service.get_status_statistics(start_date, end_date)
        return stats
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取统计信息失败: {str(e)}"
        )


@router.post("/order-status/batch-update", summary="批量更新状态")
async def batch_update_status(
    order_ids: List[UUID],
    to_status: str,
    change_reason: str,
    current_user: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_db)
):
    """
    批量更新订单状态（管理员权限）
    
    用于批量处理订单状态，如批量取消、批量完成等。
    会验证每个订单的状态转换合法性。
    """
    service = StatusService(db)
    
    try:
        logs = await service.batch_update_status(
            order_ids=order_ids,
            to_status=to_status,
            changed_by=current_user.id,
            change_reason=change_reason
        )
        
        return {
            "success_count": len(logs),
            "failed_count": len(order_ids) - len(logs),
            "updated_orders": [str(log.order_id) for log in logs]
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"批量更新失败: {str(e)}"
        )