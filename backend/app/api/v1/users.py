from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from app.database import get_db
from app.services.user_service import UserService
from app.schemas.user import *
from app.exceptions import *
from app.dependencies import get_current_user, get_current_admin
from app.core.permissions import check_permission, require_admin_permission
from typing import Dict, Any

# 创建两个不同的router用于不同的tags分类
router = APIRouter()  # 保持原有的router用于向后兼容
user_self_router = APIRouter()  # 用户自身管理
admin_user_router = APIRouter()  # 管理员用户管理

@user_self_router.get("/me", response_model=UserResponse, summary="获取当前用户信息")
async def get_current_user_info(
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    获取当前登录用户的详细信息

    包含用户基本信息、角色信息、统计数据等

    **权限要求**: 仅需要登录认证，用户可以查看自己的信息
    """
    try:
        user_service = UserService(db)
        result = await user_service.get_current_user_info(current_user["id"])

        return {
            "success": True,
            "data": result,
            "message": "获取用户信息成功"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail="获取用户信息失败")

@user_self_router.put("/me", response_model=UserResponse, summary="更新用户基本信息")
async def update_user_info(
    request: UserUpdateRequest,
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    更新用户基本信息

    - **full_name**: 真实姓名
    - **phone**: 手机号

    **权限要求**: 仅需要登录认证，用户可以编辑自己的基本信息
    """
    try:
        user_service = UserService(db)
        result = await user_service.update_user_info(current_user["id"], request)

        return {
            "success": True,
            "data": result,
            "message": "用户信息更新成功"
        }
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="更新用户信息失败")

@user_self_router.put("/me/password", response_model=UserResponse, summary="修改密码")
async def change_password(
    request: PasswordChangeRequest,
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    修改用户密码

    - **current_password**: 当前密码
    - **new_password**: 新密码，8-20位，包含字母和数字
    - **confirm_password**: 确认新密码

    **权限要求**: 仅需要登录认证，用户可以修改自己的密码
    """
    try:
        user_service = UserService(db)
        result = await user_service.change_password(current_user["id"], request)

        return {
            "success": True,
            "data": result,
            "message": "密码修改成功"
        }
    except (AuthenticationError, ValidationError) as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="修改密码失败")


@admin_user_router.get("", response_model=UserListQueryResponse, summary="用户列表管理")
async def get_user_list(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    role: str = Query(None, description="角色筛选"),
    status: str = Query(None, description="状态筛选"),
    search: str = Query(None, description="搜索关键词"),
    sort_by: str = Query("created_at", description="排序字段"),
    sort_order: str = Query("desc", description="排序方式"),
    current_admin: dict = Depends(check_permission("users.list.view")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    管理员查看用户列表

    支持分页、筛选、搜索、排序功能
    """
    try:
        query = UserListQuery(
            page=page,
            size=size,
            role=role,
            status=status,
            search=search,
            sort_by=sort_by,
            sort_order=sort_order
        )

        user_service = UserService(db)
        result = await user_service.get_user_list(query, current_admin["id"])

        return {
            "success": True,
            "data": result
        }
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="获取用户列表失败")

@admin_user_router.get("/{user_id}", response_model=UserResponse, summary="用户详情查看")
async def get_user_detail(
    user_id: str,
    current_admin: dict = Depends(check_permission("users.detail.view")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    管理员查看指定用户的详细信息
    """
    try:
        user_service = UserService(db)
        result = await user_service.get_user_detail(user_id, current_admin["id"])

        return {
            "success": True,
            "data": result,
            "message": "获取用户详情成功"
        }
    except ValidationError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="获取用户详情失败")

@admin_user_router.put("/{user_id}", response_model=UserResponse, summary="管理员更新用户信息")
async def admin_update_user(
    user_id: str,
    request: UserUpdateRequest,
    current_admin: dict = Depends(check_permission("users.edit")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    管理员更新用户基本信息

    - **full_name**: 真实姓名
    - **phone**: 手机号

    **权限要求**: 需要 `users.edit` 权限
    """
    try:
        user_service = UserService(db)
        result = await user_service.admin_update_user_info(user_id, request, current_admin["id"])

        return {
            "success": True,
            "data": result,
            "message": "用户信息更新成功"
        }
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="更新用户信息失败")

@admin_user_router.put("/{user_id}/status", response_model=UserResponse, summary="用户状态管理")
async def update_user_status(
    user_id: str,
    request: UserStatusUpdateRequest,
    current_admin: dict = Depends(check_permission("users.edit")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    管理员管理用户状态

    - **status**: 用户状态（active/inactive/banned）
    - **reason**: 状态变更原因
    - **notify_user**: 是否通知用户
    """
    try:
        user_service = UserService(db)
        result = await user_service.update_user_status(user_id, request, current_admin["id"])

        return {
            "success": True,
            "data": result,
            "message": "用户状态更新成功"
        }
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="更新用户状态失败")

@admin_user_router.post("/create", response_model=UserResponse, summary="管理员创建用户")
async def admin_create_user(
    request: AdminCreateUserRequest,
    current_admin: dict = Depends(check_permission("users.create")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    管理员创建用户账号

    - **email**: 邮箱地址
    - **full_name**: 真实姓名
    - **phone**: 手机号（可选）
    - **password**: 密码（可选，不提供则自动生成）
    - **role**: 用户角色
    - **status**: 用户状态
    - **send_notification**: 是否发送通知邮件
    - **force_password_reset**: 是否强制首次登录修改密码
    """
    try:
        print(f"🔍 收到创建用户请求: {request}")
        print(f"🔍 当前管理员: {current_admin}")

        user_service = UserService(db)
        result = await user_service.admin_create_user(request, current_admin["id"])

        return {
            "success": True,
            "data": result,
            "message": "用户创建成功"
        }
    except ValidationError as e:
        print(f"❌ 验证错误: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        print(f"❌ 创建用户异常: {str(e)}")
        print(f"❌ 异常类型: {type(e)}")
        import traceback
        print(f"❌ 异常堆栈: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"创建用户失败: {str(e)}")

@admin_user_router.put("/{user_id}/roles", response_model=UserResponse, summary="管理员分配角色")
async def update_user_roles(
    user_id: str,
    request: UserRoleUpdateRequest,
    current_admin: dict = Depends(check_permission("users.roles.manage")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    管理员分配用户角色

    - **roles**: 角色列表
    - **reason**: 角色变更原因
    - **effective_immediately**: 是否立即生效
    - **notify_user**: 是否通知用户
    """
    try:
        user_service = UserService(db)
        result = await user_service.update_user_roles(user_id, request, current_admin["id"])

        return {
            "success": True,
            "data": result,
            "message": "用户角色更新成功"
        }
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="更新用户角色失败")

@admin_user_router.get("/{user_id}/role-bindings", response_model=UserResponse, summary="用户角色绑定状态")
async def get_user_role_bindings(
    user_id: str,
    current_admin: dict = Depends(check_permission("users.detail.view")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    查看用户角色绑定状态和历史记录
    """
    try:
        user_service = UserService(db)
        result = await user_service.get_user_role_bindings(user_id, current_admin["id"])

        return {
            "success": True,
            "data": result,
            "message": "获取角色绑定状态成功"
        }
    except ValidationError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="获取角色绑定状态失败")


@admin_user_router.delete("/{user_id}", response_model=UserResponse, summary="删除用户")
async def delete_user(
    user_id: str,
    current_admin: dict = Depends(check_permission("users.delete")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    管理员删除用户

    注意：此操作会：
    1. 软删除用户（标记为已删除状态）
    2. 清除用户的所有角色绑定
    3. 保留用户的历史数据用于审计

    需要 users.delete 权限
    """
    try:
        print(f"🔍 收到删除用户请求: {user_id}")
        print(f"🔍 当前管理员: {current_admin}")

        user_service = UserService(db)
        result = await user_service.delete_user(user_id, current_admin["id"])

        return {
            "success": True,
            "data": result,
            "message": "用户删除成功"
        }
    except ValidationError as e:
        print(f"❌ 删除用户验证错误: {str(e)}")
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        print(f"❌ 删除用户异常: {str(e)}")
        print(f"❌ 异常类型: {type(e)}")
        import traceback
        print(f"❌ 异常堆栈: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"删除用户失败: {str(e)}")
