"""
帮助文档管理员API接口
"""
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form, Query, Path
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional, Dict, Any
import os
import uuid
from datetime import datetime

from app.database import get_db
from app.services.help_document_service import HelpDocumentService, HelpCategoryService
from app.schemas.help_document import (
    HelpCategoryCreate, HelpCategoryUpdate, HelpCategoryResponse,
    HelpDocumentCreate, HelpDocumentUpdate, HelpDocumentResponse, HelpDocumentDetailResponse,
    HelpDocumentSearchRequest, BatchSortUpdate, BatchOperationResponse
)
from app.exceptions import NotFoundError, ValidationError, PermissionError
from app.core.permissions import check_permission

# 创建不同的路由器用于分类
categories_router = APIRouter(prefix="/admin/help/categories", tags=["帮助文档-分类管理"])
documents_router = APIRouter(prefix="/admin/help/documents", tags=["帮助文档-内容管理"])
media_router = APIRouter(prefix="/admin/help/media", tags=["帮助文档-媒体管理"])

# 主路由器
router = APIRouter()

# ========== 分类管理接口 ==========

@categories_router.post("", summary="创建分类")
async def create_category(
    data: HelpCategoryCreate,
    current_user: dict = Depends(check_permission("help.category.create")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    创建帮助文档分类

    需要权限：help.category.create
    """
    try:
        service = HelpCategoryService(db)
        category = await service.create_category(data)
        return {
            "success": True,
            "data": category,
            "message": "分类创建成功"
        }
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="创建分类失败")


@categories_router.get("", summary="获取分类列表")
async def get_categories(
    is_active: Optional[bool] = Query(None, description="是否启用"),
    current_user: dict = Depends(check_permission("help.category.read")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    获取帮助文档分类列表
    
    需要权限：help.category.read
    """
    try:
        service = HelpCategoryService(db)
        categories = await service.get_categories(is_active=is_active)
        return {
            "success": True,
            "data": categories,
            "message": "获取分类列表成功"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail="获取分类列表失败")


@categories_router.get("/{category_id}", response_model=HelpCategoryResponse, summary="获取分类详情")
async def get_category_detail(
    category_id: int = Path(..., description="分类ID"),
    current_user: dict = Depends(check_permission("help.category.read")),
    db: AsyncSession = Depends(get_db)
) -> HelpCategoryResponse:
    """
    获取分类详情
    
    需要权限：help.category.read
    """
    try:
        service = HelpCategoryService(db)
        return await service.get_category_by_id(category_id)
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="获取分类详情失败")


@categories_router.put("/{category_id}", summary="更新分类")
async def update_category(
    category_id: int = Path(..., description="分类ID"),
    data: HelpCategoryUpdate = None,
    current_user: dict = Depends(check_permission("help.category.update")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    更新分类信息

    需要权限：help.category.update
    """
    try:
        service = HelpCategoryService(db)
        category = await service.update_category(category_id, data)
        return {
            "success": True,
            "data": category,
            "message": "分类更新成功"
        }
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="更新分类失败")


@categories_router.delete("/{category_id}", summary="删除分类")
async def delete_category(
    category_id: int = Path(..., description="分类ID"),
    current_user: dict = Depends(check_permission("help.category.delete")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    删除分类
    
    需要权限：help.category.delete
    """
    try:
        service = HelpCategoryService(db)
        await service.delete_category(category_id)
        return {
            "success": True,
            "message": "删除分类成功"
        }
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="删除分类失败")


@categories_router.put("/sort", response_model=BatchOperationResponse, summary="批量更新分类排序")
async def batch_sort_categories(
    data: BatchSortUpdate,
    current_user: dict = Depends(check_permission("help.category.update")),
    db: AsyncSession = Depends(get_db)
) -> BatchOperationResponse:
    """
    批量更新分类排序
    
    需要权限：help.category.update
    """
    try:
        service = HelpCategoryService(db)
        return await service.batch_update_sort(data)
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="批量更新排序失败")

# ========== 文档管理接口 ==========

@documents_router.post("", summary="创建文档")
async def create_document(
    data: HelpDocumentCreate,
    current_user: dict = Depends(check_permission("help.document.create")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    创建帮助文档

    需要权限：help.document.create
    """
    try:
        service = HelpDocumentService(db)
        document = await service.create_document(data, current_user["id"])
        return {
            "success": True,
            "data": document,
            "message": "文档创建成功"
        }
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="创建文档失败")


@documents_router.get("", summary="获取文档列表")
async def get_documents(
    category_id: Optional[int] = Query(None, description="分类ID"),
    is_published: Optional[bool] = Query(None, description="是否发布"),
    keyword: Optional[str] = Query(None, description="搜索关键词"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    sort_by: str = Query("sort_order", description="排序字段"),
    sort_order: str = Query("asc", description="排序方式"),
    current_user: dict = Depends(check_permission("help.document.read")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    获取帮助文档列表
    
    需要权限：help.document.read
    """
    try:
        request = HelpDocumentSearchRequest(
            category_id=category_id,
            is_published=is_published,
            keyword=keyword,
            page=page,
            page_size=page_size,
            sort_by=sort_by,
            sort_order=sort_order
        )
        
        service = HelpDocumentService(db)
        result = await service.get_documents(request, is_admin=True)
        
        return {
            "success": True,
            "data": result,
            "message": "获取文档列表成功"
        }
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="获取文档列表失败")


@documents_router.get("/{document_id}", summary="获取文档详情")
async def get_document_detail(
    document_id: int = Path(..., description="文档ID"),
    current_user: dict = Depends(check_permission("help.document.read")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    获取文档详情

    需要权限：help.document.read
    """
    try:
        service = HelpDocumentService(db)
        document = await service.get_document_by_id(document_id, increment_view=False, is_admin=True)
        return {
            "success": True,
            "data": document,
            "message": "获取文档详情成功"
        }
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="获取文档详情失败")


@documents_router.put("/{document_id}", summary="更新文档")
async def update_document(
    document_id: int = Path(..., description="文档ID"),
    data: HelpDocumentUpdate = None,
    current_user: dict = Depends(check_permission("help.document.update")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    更新文档信息

    需要权限：help.document.update
    """
    try:
        service = HelpDocumentService(db)
        document = await service.update_document(document_id, data)
        return {
            "success": True,
            "data": document,
            "message": "文档更新成功"
        }
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="更新文档失败")


@documents_router.delete("/{document_id}", summary="删除文档")
async def delete_document(
    document_id: int = Path(..., description="文档ID"),
    current_user: dict = Depends(check_permission("help.document.delete")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    删除文档
    
    需要权限：help.document.delete
    """
    try:
        service = HelpDocumentService(db)
        await service.delete_document(document_id)
        return {
            "success": True,
            "message": "删除文档成功"
        }
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="删除文档失败")


@documents_router.put("/sort", response_model=BatchOperationResponse, summary="批量更新文档排序")
async def batch_sort_documents(
    data: BatchSortUpdate,
    current_user: dict = Depends(check_permission("help.document.update")),
    db: AsyncSession = Depends(get_db)
) -> BatchOperationResponse:
    """
    批量更新文档排序
    
    需要权限：help.document.update
    """
    try:
        service = HelpDocumentService(db)
        return await service.batch_sort_documents(data)
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="批量更新排序失败")


# ========== 媒体文件管理接口 ==========

@media_router.post("/upload/image", summary="上传图片")
async def upload_image(
    document_id: int = Form(..., description="文档ID"),
    file: UploadFile = File(..., description="图片文件"),
    current_user: dict = Depends(check_permission("help.media.create")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    上传图片文件

    - 支持格式：JPG, PNG, GIF
    - 文件大小限制：5MB

    需要权限：help.media.create
    """
    try:
        # 验证文件类型
        if not file.content_type or not file.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="只能上传图片文件")

        # 验证文件大小（5MB）
        contents = await file.read()
        if len(contents) > 5 * 1024 * 1024:
            raise HTTPException(status_code=400, detail="图片大小不能超过5MB")

        # 验证文档是否存在
        service = HelpDocumentService(db)
        await service.get_document_by_id(document_id, is_admin=True)

        # 上传文件
        media = await service.upload_media(
            document_id=document_id,
            file_type="image",
            file_data=contents,
            file_name=file.filename,
            mime_type=file.content_type
        )

        # 生成访问URL
        from app.services.tos_service import tos_service
        access_url = tos_service.get_file_url(media.file_path, is_public=True)

        return {
            "success": True,
            "data": {
                "id": media.id,
                "file_name": media.file_name,
                "file_path": media.file_path,  # TOS storage_key
                "access_url": access_url,      # 可访问的URL
                "file_size": media.file_size,
                "mime_type": media.mime_type
            },
            "message": "图片上传成功"
        }
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="图片上传失败")


@media_router.post("/upload/video", summary="上传视频")
async def upload_video(
    document_id: int = Form(..., description="文档ID"),
    file: UploadFile = File(..., description="视频文件"),
    current_user: dict = Depends(check_permission("help.media.create")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    上传视频文件

    - 支持格式：MP4, AVI, MOV
    - 文件大小限制：100MB

    需要权限：help.media.create
    """
    try:
        # 验证文件类型
        allowed_types = ['video/mp4', 'video/avi', 'video/quicktime']
        if not file.content_type or file.content_type not in allowed_types:
            raise HTTPException(status_code=400, detail="只支持MP4、AVI、MOV格式")

        # 验证文件大小（100MB）
        contents = await file.read()
        if len(contents) > 100 * 1024 * 1024:
            raise HTTPException(status_code=400, detail="视频大小不能超过100MB")

        # 验证文档是否存在
        service = HelpDocumentService(db)
        await service.get_document_by_id(document_id, is_admin=True)

        # 上传文件
        media = await service.upload_media(
            document_id=document_id,
            file_type="video",
            file_data=contents,
            file_name=file.filename,
            mime_type=file.content_type
        )

        # 生成访问URL
        from app.services.tos_service import tos_service
        access_url = tos_service.get_file_url(media.file_path, is_public=True)

        return {
            "success": True,
            "data": {
                "id": media.id,
                "file_name": media.file_name,
                "file_path": media.file_path,  # TOS storage_key
                "access_url": access_url,      # 可访问的URL
                "file_size": media.file_size,
                "mime_type": media.mime_type,
                "duration": media.duration
            },
            "message": "视频上传成功"
        }
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="视频上传失败")


@media_router.get("/{media_id}", summary="获取媒体文件信息")
async def get_media_info(
    media_id: int = Path(..., description="媒体文件ID"),
    current_user: dict = Depends(check_permission("help.media.read")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    获取媒体文件信息

    需要权限：help.media.read
    """
    try:
        service = HelpDocumentService(db)
        media = await service.get_media_by_id(media_id)

        # 生成访问URL
        from app.services.tos_service import tos_service
        access_url = tos_service.get_file_url(media.file_path, is_public=True)

        return {
            "success": True,
            "data": {
                "id": media.id,
                "document_id": media.document_id,
                "file_type": media.file_type,
                "file_name": media.file_name,
                "file_path": media.file_path,  # TOS storage_key
                "access_url": access_url,      # 可访问的URL
                "file_size": media.file_size,
                "mime_type": media.mime_type,
                "thumbnail_path": media.thumbnail_path,
                "duration": media.duration,
                "created_at": media.created_at
            },
            "message": "获取媒体文件信息成功"
        }
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="获取媒体文件信息失败")


@media_router.delete("/{media_id}", summary="删除媒体文件")
async def delete_media(
    media_id: int = Path(..., description="媒体文件ID"),
    current_user: dict = Depends(check_permission("help.media.delete")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    删除媒体文件

    需要权限：help.media.delete
    """
    try:
        service = HelpDocumentService(db)
        await service.delete_media(media_id)

        return {
            "success": True,
            "message": "删除媒体文件成功"
        }
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="删除媒体文件失败")


# ========== 路由注册 ==========
# 将所有子路由注册到主路由器
router.include_router(categories_router)
router.include_router(documents_router)
router.include_router(media_router)
