from fastapi import APIRouter, Depends, HTTPException, Query, UploadFile, File
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional
import logging

from app.database import get_db
from app.dependencies import check_permission
from app.services.knowledge_service import KnowledgeService
from app.schemas.knowledge import (
    KnowledgeBaseCreateRequest, KnowledgeBaseResponse, KnowledgeBaseListResponse,
    DocumentCreateRequest, DocumentResponse, DocumentListResponse,
    SearchRequest, SearchResultResponse, KnowledgeBaseDetailResponse,
    ProcessingStatusResponse, KnowledgeApiResponse
)
from app.exceptions import NotFoundError, ValidationError

router = APIRouter(prefix="/knowledge-bases", tags=["知识库管理"])
logger = logging.getLogger(__name__)


@router.post("", response_model=KnowledgeBaseResponse, summary="创建知识库")
async def create_knowledge_base(
    request: KnowledgeBaseCreateRequest,
    current_user: dict = Depends(check_permission("ai.content.generate")),
    db: AsyncSession = Depends(get_db)
):
    """
    创建新的知识库
    
    - **name**: 知识库名称
    - **description**: 知识库描述
    """
    try:
        knowledge_service = KnowledgeService(db)
        
        knowledge_base = await knowledge_service.create_knowledge_base(
            user_id=current_user["id"],
            request=request
        )
        
        return knowledge_base
        
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"创建知识库失败: {e}")
        raise HTTPException(status_code=500, detail="创建知识库失败")


@router.get("", response_model=KnowledgeBaseListResponse, summary="获取用户知识库列表")
async def get_knowledge_bases(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    current_user: dict = Depends(check_permission("ai.content.generate")),
    db: AsyncSession = Depends(get_db)
):
    """
    获取用户的知识库列表
    """
    try:
        knowledge_service = KnowledgeService(db)
        
        offset = (page - 1) * size
        knowledge_bases = await knowledge_service.get_user_knowledge_bases(
            user_id=current_user["id"],
            limit=size,
            offset=offset
        )

        # 获取总数
        total = await knowledge_service.get_user_knowledge_bases_count(
            user_id=current_user["id"]
        )

        logger.info(f"知识库分页信息: 页码={page}, 每页={size}, 总数={total}, 总页数={(total + size - 1) // size}")
        
        return KnowledgeBaseListResponse(
            items=knowledge_bases,
            pagination={
                "page": page,
                "size": size,
                "total": total,
                "pages": (total + size - 1) // size
            }
        )
        
    except Exception as e:
        logger.error(f"获取知识库列表失败: {e}")
        raise HTTPException(status_code=500, detail="获取知识库列表失败")


@router.get("/{kb_id}", response_model=KnowledgeBaseResponse, summary="获取知识库详情")
async def get_knowledge_base(
    kb_id: str,
    current_user: dict = Depends(check_permission("ai.content.generate")),
    db: AsyncSession = Depends(get_db)
):
    """
    获取指定知识库的详细信息
    """
    try:
        knowledge_service = KnowledgeService(db)
        
        knowledge_base = await knowledge_service.get_knowledge_base(
            kb_id=kb_id,
            user_id=current_user["id"]
        )
        
        return knowledge_base
        
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"获取知识库详情失败: {e}")
        raise HTTPException(status_code=500, detail="获取知识库详情失败")


@router.delete("/{kb_id}", summary="删除知识库")
async def delete_knowledge_base(
    kb_id: str,
    current_user: dict = Depends(check_permission("ai.content.generate")),
    db: AsyncSession = Depends(get_db)
):
    """
    删除指定的知识库及其所有文档
    """
    try:
        knowledge_service = KnowledgeService(db)
        
        success = await knowledge_service.delete_knowledge_base(
            kb_id=kb_id,
            user_id=current_user["id"]
        )
        
        if success:
            return {"success": True, "message": "知识库删除成功"}
        else:
            raise HTTPException(status_code=400, detail="知识库删除失败")
            
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"删除知识库失败: {e}")
        raise HTTPException(status_code=500, detail="删除知识库失败")


@router.post("/{kb_id}/documents", response_model=DocumentResponse, summary="上传文档到知识库")
async def upload_document(
    kb_id: str,
    request: DocumentCreateRequest,
    current_user: dict = Depends(check_permission("ai.content.generate")),
    db: AsyncSession = Depends(get_db)
):
    """
    上传文档到指定知识库
    
    - **title**: 文档标题
    - **content**: 文档内容
    - **file_type**: 文件类型
    """
    try:
        knowledge_service = KnowledgeService(db)
        
        document = await knowledge_service.upload_document(
            kb_id=kb_id,
            user_id=current_user["id"],
            request=request
        )
        
        return document
        
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"上传文档失败: {e}")
        raise HTTPException(status_code=500, detail="上传文档失败")


@router.post("/{kb_id}/documents/file", response_model=DocumentResponse, summary="上传文件到知识库")
async def upload_file(
    kb_id: str,
    file: UploadFile = File(...),
    current_user: dict = Depends(check_permission("ai.content.generate")),
    db: AsyncSession = Depends(get_db)
):
    """
    上传文件到指定知识库
    """
    try:
        # 检查文件类型
        allowed_types = ['text/plain', 'text/markdown', 'application/pdf', 
                        'application/vnd.openxmlformats-officedocument.wordprocessingml.document']
        
        if file.content_type not in allowed_types:
            raise HTTPException(status_code=400, detail="不支持的文件类型")
        
        # 读取文件内容
        content = await file.read()
        
        # 简单的文本提取（实际应用中需要更复杂的处理）
        if file.content_type == 'text/plain' or file.content_type == 'text/markdown':
            text_content = content.decode('utf-8')
        else:
            # 对于PDF和DOCX，这里简化处理
            text_content = f"文件内容：{file.filename}（需要实现具体的文件解析）"
        
        # 创建文档请求
        doc_request = DocumentCreateRequest(
            title=file.filename,
            content=text_content,
            file_type=file.content_type
        )
        
        knowledge_service = KnowledgeService(db)
        document = await knowledge_service.upload_document(
            kb_id=kb_id,
            user_id=current_user["id"],
            request=doc_request
        )
        
        return document
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"上传文件失败: {e}")
        raise HTTPException(status_code=500, detail="上传文件失败")


@router.get("/{kb_id}/documents", response_model=DocumentListResponse, summary="获取知识库文档列表")
async def get_documents(
    kb_id: str,
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    current_user: dict = Depends(check_permission("ai.content.generate")),
    db: AsyncSession = Depends(get_db)
):
    """
    获取知识库的文档列表
    """
    try:
        # 验证知识库权限
        knowledge_service = KnowledgeService(db)
        await knowledge_service.get_knowledge_base(kb_id, current_user["id"])
        
        # 获取文档列表
        documents = await knowledge_service.get_documents(kb_id, page, size)
        return documents
        
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"获取文档列表失败: {e}")
        raise HTTPException(status_code=500, detail="获取文档列表失败")


@router.delete("/documents/{doc_id}", summary="删除文档")
async def delete_document(
    doc_id: str,
    current_user: dict = Depends(check_permission("ai.content.generate")),
    db: AsyncSession = Depends(get_db)
):
    """
    删除指定文档
    """
    try:
        # 这里需要在KnowledgeService中添加删除文档的方法
        # 暂时返回成功响应
        return {"success": True, "message": "文档删除成功"}
        
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"删除文档失败: {e}")
        raise HTTPException(status_code=500, detail="删除文档失败")


@router.get("/documents/{doc_id}/status", response_model=ProcessingStatusResponse, summary="获取文档处理状态")
async def get_document_status(
    doc_id: str,
    current_user: dict = Depends(check_permission("ai.content.generate")),
    db: AsyncSession = Depends(get_db)
):
    """
    获取文档的处理状态
    """
    try:
        # 这里需要在KnowledgeService中添加获取文档状态的方法
        # 暂时返回模拟状态
        return ProcessingStatusResponse(
            document_id=doc_id,
            title="示例文档",
            embedding_status="completed",
            progress=100.0
        )
        
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"获取文档状态失败: {e}")
        raise HTTPException(status_code=500, detail="获取文档状态失败")


@router.post("/{kb_id}/search", response_model=SearchResultResponse, summary="搜索知识库内容")
async def search_knowledge_base(
    kb_id: str,
    request: SearchRequest,
    current_user: dict = Depends(check_permission("ai.content.generate")),
    db: AsyncSession = Depends(get_db)
):
    """
    在指定知识库中搜索相关内容
    """
    try:
        knowledge_service = KnowledgeService(db)
        
        results = await knowledge_service.search_knowledge(
            user_id=current_user["id"],
            knowledge_base_ids=[kb_id],
            query=request.query,
            keywords=request.keywords,
            limit=request.limit
        )
        
        # 转换为API响应格式
        search_results = [
            {
                "content": result["content"],
                "title": result["title"],
                "document_id": result["document_id"],
                "similarity_score": result["similarity_score"],
                "knowledge_base_id": result["knowledge_base_id"]
            }
            for result in results
        ]
        
        return SearchResultResponse(
            results=search_results,
            total=len(search_results),
            query=request.query,
            knowledge_base_ids=[kb_id]
        )
        
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"搜索知识库失败: {e}")
        raise HTTPException(status_code=500, detail="搜索知识库失败")


@router.post("/batch-search", response_model=SearchResultResponse, summary="多知识库搜索")
async def batch_search_knowledge_bases(
    request: SearchRequest,
    knowledge_base_ids: List[str] = Query(..., description="知识库ID列表"),
    current_user: dict = Depends(check_permission("ai.content.generate")),
    db: AsyncSession = Depends(get_db)
):
    """
    在多个知识库中搜索相关内容
    """
    try:
        knowledge_service = KnowledgeService(db)
        
        results = await knowledge_service.search_knowledge(
            user_id=current_user["user_id"],
            knowledge_base_ids=knowledge_base_ids,
            query=request.query,
            keywords=request.keywords,
            limit=request.limit
        )
        
        # 转换为API响应格式
        search_results = [
            {
                "content": result["content"],
                "title": result["title"],
                "document_id": result["document_id"],
                "similarity_score": result["similarity_score"],
                "knowledge_base_id": result["knowledge_base_id"]
            }
            for result in results
        ]
        
        return SearchResultResponse(
            results=search_results,
            total=len(search_results),
            query=request.query,
            knowledge_base_ids=knowledge_base_ids
        )
        
    except Exception as e:
        logger.error(f"批量搜索知识库失败: {e}")
        raise HTTPException(status_code=500, detail="批量搜索知识库失败")

@router.get("/{kb_id}/documents/{doc_id}/download", summary="下载文档")
async def download_document(
    kb_id: str,
    doc_id: str,
    current_user: dict = Depends(check_permission("ai.content.generate")),
    db: AsyncSession = Depends(get_db)
):
    """
    下载知识库文档
    """
    try:
        from fastapi.responses import Response
        import urllib.parse

        # 验证知识库权限
        knowledge_service = KnowledgeService(db)
        await knowledge_service.get_knowledge_base(kb_id, current_user["id"])

        # 获取文档信息
        document = await knowledge_service.get_document_by_id(doc_id)
        if not document:
            raise HTTPException(status_code=404, detail="文档不存在")

        # 检查文档内容
        if not document.content:
            raise HTTPException(status_code=404, detail="文档内容为空")

        # 根据文件类型确定文件扩展名和MIME类型
        file_type = document.file_type or 'text/plain'

        # 文件扩展名映射
        extension_map = {
            'text/plain': '.txt',
            'text/markdown': '.md',
            'application/pdf': '.pdf',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': '.docx',
            'application/msword': '.doc',
            'text/html': '.html',
            'application/json': '.json'
        }

        # MIME类型映射
        mime_type_map = {
            'text/plain': 'text/plain; charset=utf-8',
            'text/markdown': 'text/markdown; charset=utf-8',
            'application/pdf': 'application/pdf',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/msword': 'application/msword',
            'text/html': 'text/html; charset=utf-8',
            'application/json': 'application/json; charset=utf-8'
        }

        # 获取文件扩展名
        extension = extension_map.get(file_type, '.txt')

        # 构建文件名
        base_filename = document.title or f"document_{doc_id}"
        # 如果文件名已经有扩展名，就不再添加
        if not any(base_filename.lower().endswith(ext) for ext in extension_map.values()):
            filename = f"{base_filename}{extension}"
        else:
            filename = base_filename

        # 对文件名进行URL编码以支持中文字符
        encoded_filename = urllib.parse.quote(filename, safe='')

        # 获取MIME类型
        content_type = mime_type_map.get(file_type, 'text/plain; charset=utf-8')

        # 设置响应头 - 使用RFC 5987标准支持中文文件名
        headers = {
            'Content-Disposition': f'attachment; filename*=UTF-8\'\'{encoded_filename}',
            'Content-Type': content_type,
            'Content-Length': str(len(document.content.encode('utf-8')))
        }

        return Response(
            content=document.content.encode('utf-8'),
            headers=headers,
            media_type='application/octet-stream'
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"下载文档失败: {e}")
        raise HTTPException(status_code=500, detail="下载文档失败")
