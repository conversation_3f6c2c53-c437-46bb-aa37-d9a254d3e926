from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from decimal import Decimal
from app.database import get_db
from app.services.channel_service import ChannelService
from app.schemas.channel_category import *
from app.schemas.common import ApiResponse
from app.core.permissions import check_permission
from app.exceptions import ValidationError, NotFoundError
from typing import Dict, Any, Optional

router = APIRouter()

# 通用响应模型
class ChannelServiceApiResponse(ApiResponse):
    data: Optional[ChannelServiceResponse] = None

class ChannelServiceListApiResponse(ApiResponse):
    data: Optional[Dict[str, Any]] = None


# ==================== 管理员审批相关接口 ====================

@router.get("/pending", response_model=ChannelServiceListApiResponse, summary="获取待审批服务列表")
async def get_pending_services(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    category_id: Optional[str] = Query(None, description="分类ID筛选"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    sort_by: str = Query("created_at", description="排序字段"),
    sort_order: str = Query("desc", pattern="^(asc|desc)$", description="排序方式"),
    current_admin: dict = Depends(check_permission("channels.services.approve")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    管理员获取待审批服务列表

    只返回渠道商创建的待审批服务（service_source='provider', approval_status='pending'）

    需要权限：channels.services.approve
    """
    try:
        query = ChannelServiceListQuery(
            page=page,
            size=size,
            category_id=category_id,
            portal_type=None,
            coverage_area=None,
            is_active=None,
            min_price=None,
            max_price=None,
            search=search,
            sort_by=sort_by,
            sort_order=sort_order
        )

        channel_service = ChannelService(db)
        # 使用专门的方法获取待审批服务
        result = await channel_service.get_pending_services_list(query)

        return {
            "success": True,
            "data": result,
            "message": "获取待审批服务列表成功"
        }
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="获取待审批服务列表失败")


# ==================== 管理员服务管理接口 ====================

@router.get("", response_model=ChannelServiceListApiResponse, summary="获取渠道服务列表")
async def get_service_list(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    category_id: Optional[str] = Query(None, description="分类ID筛选"),
    portal_type: Optional[str] = Query(None, description="门户类型筛选"),
    channel_type: Optional[str] = Query(None, description="频道类型筛选"),
    coverage_area: Optional[str] = Query(None, description="覆盖区域筛选，多个区域用逗号分隔"),
    is_active: Optional[bool] = Query(None, description="是否启用筛选"),
    min_price: Optional[Decimal] = Query(None, ge=0, description="最低价格筛选"),
    max_price: Optional[Decimal] = Query(None, ge=0, description="最高价格筛选"),
    max_delivery_time: Optional[int] = Query(None, ge=1, description="最大交付时间筛选（小时）"),
    max_revision_count: Optional[int] = Query(None, ge=0, description="最大修改次数筛选"),
    service_features: Optional[str] = Query(None, description="服务特色关键词筛选"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    sort_by: str = Query("created_at", description="排序字段"),
    sort_order: str = Query("desc", pattern="^(asc|desc)$", description="排序方式"),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    获取渠道服务列表

    支持分页、筛选和搜索功能：
    - 可按分类、平台类型、状态、价格范围筛选
    - 支持关键词搜索服务名称、描述和代码
    - 支持多种排序方式

    需要权限：channels.services.view
    """
    try:
        query = ChannelServiceListQuery(
            page=page,
            size=size,
            category_id=category_id,
            portal_type=portal_type,
            channel_type=channel_type,
            coverage_area=coverage_area,
            is_active=is_active,
            min_price=min_price,
            max_price=max_price,
            max_delivery_time=max_delivery_time,
            max_revision_count=max_revision_count,
            service_features=service_features,
            search=search,
            sort_by=sort_by,
            sort_order=sort_order
        )

        channel_service = ChannelService(db)
        result = await channel_service.get_all_services_list(query)

        return {
            "success": True,
            "data": result,
            "message": "获取服务列表成功"
        }
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        import traceback
        print(f"获取服务列表失败详细错误: {str(e)}")
        print(f"错误堆栈: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"获取服务列表失败: {str(e)}")


@router.post("", response_model=ChannelServiceApiResponse, summary="创建渠道服务")
async def create_service(
    request: ChannelServiceCreate,
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    管理员创建平台服务

    创建的服务将自动设置为：
    - service_source = 'platform' (平台服务)
    - approval_status = 'approved' (直接审核通过)
    - provider_id = null (无关联渠道商)

    需要权限：channels.services.create
    """
    try:
        # 添加调试日志
        print(f"收到创建服务请求: {request}")

        channel_service = ChannelService(db)
        # 临时使用固定的用户ID进行测试
        temp_user_id = "00000000-0000-0000-0000-000000000001"
        service = await channel_service.create_service(temp_user_id, request)

        return {
            "success": True,
            "data": service,
            "message": "创建服务成功"
        }
    except ValidationError as e:
        print(f"验证错误: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except NotFoundError as e:
        print(f"未找到错误: {str(e)}")
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        print(f"创建服务异常: {str(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"创建服务失败: {str(e)}")


@router.put("/{service_id}", response_model=ChannelServiceApiResponse, summary="更新渠道服务")
async def update_service(
    service_id: str,
    request: ChannelServiceUpdate,
    current_user: dict = Depends(check_permission("channels.services.edit")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    更新渠道服务

    需要权限：channels.services.edit
    """
    try:
        channel_service = ChannelService(db)
        service = await channel_service.update_service(current_user["id"], service_id, request)

        return {
            "success": True,
            "data": service,
            "message": "更新服务成功"
        }
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="更新服务失败")


@router.delete("/{service_id}", response_model=ApiResponse, summary="删除渠道服务")
async def delete_service(
    service_id: str,
    current_user: dict = Depends(check_permission("channels.services.delete")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    删除渠道服务

    需要权限：channels.services.delete
    """
    try:
        channel_service = ChannelService(db)
        await channel_service.delete_service(current_user["id"], service_id)

        return {
            "success": True,
            "data": None,
            "message": "删除服务成功"
        }
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="删除服务失败")



@router.get("/{service_id}", response_model=ChannelServiceApiResponse, summary="获取服务详情")
async def get_service_detail(
    service_id: str,
    current_user: dict = Depends(check_permission("channels.services.view")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    获取服务详情（管理员版本）

    需要权限：channels.services.view
    """
    try:
        channel_service = ChannelService(db)
        service = await channel_service.get_admin_service_detail(service_id)

        return {
            "success": True,
            "data": service,
            "message": "获取服务详情成功"
        }
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="获取服务详情失败")


@router.put("/{service_id}/approval", response_model=ChannelServiceApiResponse, summary="审批渠道服务")
async def approve_service(
    service_id: str,
    request: ChannelServiceApprovalRequest,
    current_admin: dict = Depends(check_permission("channels.services.approve")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    管理员审批渠道商创建的服务

    - **status**: 审批结果（approved/rejected）
    - **note**: 审批备注（可选）

    审批通过后，服务将自动绑定到创建该服务的渠道商
    审批拒绝后，服务状态更新为rejected

    需要权限：channels.services.approve
    """
    try:
        channel_service = ChannelService(db)
        service = await channel_service.approve_service(service_id, current_admin["id"], request)

        return {
            "success": True,
            "data": service,
            "message": f"服务审批{'通过' if request.status == 'approved' else '拒绝'}成功"
        }
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="审批服务失败")
