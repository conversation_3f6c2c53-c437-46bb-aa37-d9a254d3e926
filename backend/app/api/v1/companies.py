from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from app.database import get_db
from app.services.company_service import CompanyService
from app.schemas.company import *
from app.exceptions import *
from app.dependencies import get_current_user
from app.core.permissions import check_permission
from typing import Dict, Any

# 创建两个不同的router用于不同的tags分类
router = APIRouter()  # 保持原有的router用于向后兼容
user_company_router = APIRouter()  # 用户自身企业管理
admin_company_router = APIRouter()  # 管理员企业管理

@user_company_router.post("/profile", response_model=CompanyApiResponse, summary="普通用户申请企业角色")
async def create_company_profile(
    request: CompanyProfileRequest,
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    普通用户申请企业角色，提交企业信息

    **权限要求**: 仅需要登录认证，任何用户都可以申请企业角色

    - **company_name**: 企业名称（必填）
    - **company_code**: 统一社会信用代码/营业执照号（必填，18位）
    - **legal_person**: 法人姓名（必填）
    - **contact_email**: 联系邮箱（必填）
    - **headquarters_location**: 总部地址（必填）
    - **industry**: 所属行业（必填）
    - **company_size**: 企业规模（必填）
    - **company_name_en**: 英文名称（可选）
    - **contact_phone**: 联系电话（可选）
    - **founded_year**: 成立年份（可选）
    - **business_scope**: 经营范围（可选）
    - **company_description**: 企业描述（可选）
    - **official_website**: 官方网站（可选）
    - **business_license_url**: 营业执照URL（可选）
    - **other_files**: 其他资质文件（可选）
    - **social_media_links**: 社交媒体链接（可选）
    """
    # 企业信息创建后需要等待管理员审核，审核通过后才会分配企业用户角色

    try:
        company_service = CompanyService(db)
        result = await company_service.create_company_profile(current_user["id"], request)

        return {
            "success": True,
            "data": result,
            "message": "企业信息创建成功，等待审核"
        }
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建企业信息失败: {str(e)}")

@user_company_router.get("/me", response_model=CompanyApiResponse, summary="获取企业信息")
async def get_company_info(
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    获取当前用户的企业信息

    返回企业的详细信息，包括基本信息、审核状态、统计数据等

    **权限要求**: 仅需要登录认证，用户可以查看自己提交的企业信息和审核状态
    """
    try:
        # 普通用户可以查看自己提交的企业信息和审核状态

        company_service = CompanyService(db)
        result = await company_service.get_company_info(current_user["id"])

        return {
            "success": True,
            "data": result,
            "message": "获取企业信息成功"
        }
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="获取企业信息失败")



@user_company_router.put("/me", response_model=CompanyApiResponse, summary="更新企业信息")
async def update_company_info(
    request: CompanyUpdateRequest,
    current_user: dict = Depends(check_permission("companies.profile.edit")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    更新企业信息

    只允许更新部分字段：
    - **contact_phone**: 联系电话
    - **contact_email**: 联系邮箱
    - **headquarters_location**: 总部地址
    - **official_website**: 官方网站
    - **business_scope**: 经营范围
    - **company_description**: 企业描述

    注意：企业名称、法人等关键信息不允许修改

    **权限要求**: 需要 `companies.profile.edit` 权限，且必须是企业用户或管理员角色
    """
    try:
        # 检查用户角色是否为企业用户或管理员
        user_roles = current_user.get("roles", [])
        is_admin = any(role in ["admin", "super_admin"] for role in user_roles)
        if "enterprise_user" not in user_roles and not is_admin:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="只有企业用户或管理员可以更新企业信息"
            )

        company_service = CompanyService(db)
        result = await company_service.update_company_info(current_user["id"], request)

        return {
            "success": True,
            "data": result,
            "message": "企业信息更新成功"
        }
    except (ValidationError, NotFoundError) as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        import logging
        import traceback
        logging.error(f"更新企业信息失败: {str(e)}")
        logging.error(f"错误堆栈: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"更新企业信息失败: {str(e)}")

@admin_company_router.get("", response_model=CompanyApiResponse, summary="企业列表管理")
async def get_company_list(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    verification_status: str = Query(None, description="审核状态筛选"),
    industry: str = Query(None, description="行业筛选"),
    company_size: str = Query(None, description="企业规模筛选"),
    search: str = Query(None, description="搜索关键词"),
    sort_by: str = Query("created_at", description="排序字段"),
    sort_order: str = Query("desc", description="排序方式"),
    current_admin: dict = Depends(check_permission("companies.list.view")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    管理员查看企业列表

    支持以下功能：
    - **分页查询**: 通过page和size参数控制
    - **状态筛选**: 按审核状态筛选（pending/approved/rejected）
    - **行业筛选**: 按行业类型筛选
    - **规模筛选**: 按企业规模筛选
    - **关键词搜索**: 搜索企业名称、法人姓名、企业代码
    - **排序**: 支持多字段排序

    返回企业列表、分页信息和统计数据
    """
    try:
        query = CompanyListQuery(
            page=page,
            size=size,
            verification_status=verification_status,
            industry=industry,
            company_size=company_size,
            search=search,
            sort_by=sort_by,
            sort_order=sort_order
        )

        company_service = CompanyService(db)
        result = await company_service.get_company_list(query, current_admin["id"])

        return {
            "success": True,
            "data": result,
            "message": "获取企业列表成功"
        }
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="获取企业列表失败")

@admin_company_router.get("/pending", response_model=CompanyApiResponse, summary="获取待审批企业列表")
async def get_pending_companies(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    search: str = Query(None, description="搜索关键词"),
    sort_by: str = Query("created_at", description="排序字段"),
    sort_order: str = Query("desc", description="排序方式"),
    current_admin: dict = Depends(check_permission("companies.list.view")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    管理员获取待审批企业列表

    支持以下功能：
    - **分页查询**: 通过page和size参数控制
    - **关键词搜索**: 搜索企业名称、法人姓名、企业代码
    - **排序**: 支持多字段排序

    只返回审核状态为pending的企业
    """
    try:
        query = CompanyListQuery(
            page=page,
            size=size,
            verification_status="pending",  # 固定为待审批状态
            search=search,
            sort_by=sort_by,
            sort_order=sort_order
        )

        company_service = CompanyService(db)
        result = await company_service.get_company_list(query, current_admin["id"])

        return {
            "success": True,
            "data": result,
            "message": "获取待审批企业列表成功"
        }
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="获取待审批企业列表失败")

@admin_company_router.put("/{company_id}/verification", response_model=CompanyApiResponse, summary="企业审核")
async def verify_company(
    company_id: str,
    request: CompanyVerificationRequest,
    current_admin: dict = Depends(check_permission("companies.verification")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    管理员审核企业资质

    - **status**: 审核结果（verified/rejected）
    - **note**: 审核备注（必填，至少5个字符）
    - **notify_user**: 是否通知用户（默认true）

    审核通过后，企业用户可以正常使用平台功能
    审核拒绝后，企业用户需要重新提交资料
    """
    try:
        company_service = CompanyService(db)
        result = await company_service.verify_company(company_id, request, current_admin["id"])

        return {
            "success": True,
            "data": result,
            "message": f"企业审核完成，状态已更新为{request.status}"
        }
    except (ValidationError, NotFoundError) as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="企业审核失败")
