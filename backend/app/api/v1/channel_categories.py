from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from decimal import Decimal
from app.database import get_db
from app.services.channel_service import ChannelService
from app.schemas.channel_category import *
from app.exceptions import *
from app.core.permissions import check_permission
from app.dependencies import get_current_user
from typing import Dict, Any

router = APIRouter()

# ==================== 公开接口 ====================

# ==================== 管理员接口 ====================

@router.get("/list", response_model=ChannelCategoryApiResponse, summary="获取渠道分类列表")
async def get_category_list(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    is_active: bool = Query(None, description="是否启用筛选"),
    category_level: int = Query(None, ge=1, le=2, description="分类层级筛选"),
    parent_id: str = Query(None, description="父分类ID筛选"),
    search: str = Query(None, description="搜索关键词"),
    sort_by: str = Query("sort_order", description="排序字段"),
    sort_order: str = Query("asc", description="排序方式"),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    获取渠道分类列表（无需权限验证）

    支持分页、筛选和搜索功能：
    - 可按父分类、层级、状态筛选
    - 支持关键词搜索分类名称和描述
    - 支持多种排序方式
    """
    try:
        query = ChannelCategoryListQuery(
            page=page,
            size=size,
            parent_id=parent_id,
            level=category_level,
            is_active=is_active,
            is_featured=None,
            search=search,
            sort_by=sort_by,
            sort_order=sort_order
        )

        channel_service = ChannelService(db)
        result = await channel_service.get_category_list(query)

        return {
            "success": True,
            "data": result,
            "message": "获取渠道分类列表成功"
        }
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        import traceback
        print(f"获取渠道分类列表失败详细错误: {str(e)}")
        print(f"错误堆栈: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"获取渠道分类列表失败: {str(e)}")





@router.post("", response_model=ChannelCategoryApiResponse, summary="创建渠道分类")
async def create_category(
    request: ChannelCategoryCreate,
    current_admin: dict = Depends(get_current_user),  # 临时简化权限检查
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    管理员创建渠道分类
    
    支持创建多层级分类结构，自动处理层级关系。
    """
    try:
        channel_service = ChannelService(db)
        category = await channel_service.create_category(request)

        return {
            "success": True,
            "data": category,
            "message": "创建渠道分类成功"
        }
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="创建渠道分类失败")


@router.put("/{category_id}", response_model=ChannelCategoryApiResponse, summary="更新渠道分类")
async def update_category(
    category_id: str,
    request: ChannelCategoryUpdate,
    current_admin: dict = Depends(check_permission("channels.categories.update")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    管理员更新渠道分类信息
    """
    try:
        channel_service = ChannelService(db)
        category = await channel_service.update_category(category_id, request)

        return {
            "success": True,
            "data": category,
            "message": "更新渠道分类成功"
        }
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        import traceback
        print(f"更新渠道分类失败详细错误: {str(e)}")
        print(f"错误堆栈: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"更新渠道分类失败: {str(e)}")


@router.get("/{category_id}", response_model=ChannelCategoryApiResponse, summary="获取渠道分类详情")
async def get_category_detail(
    category_id: str,
    current_admin: dict = Depends(check_permission("channels.categories.view")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    获取渠道分类详情

    返回指定分类的详细信息，包括：
    - 分类基本信息

    需要权限：channels.categories.view
    """
    try:
        channel_service = ChannelService(db)
        category = await channel_service.get_category_detail(category_id)

        return {
            "success": True,
            "data": category,
            "message": "获取分类详情成功"
        }
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        import traceback
        print(f"获取分类详情失败详细错误: {str(e)}")
        print(f"错误堆栈: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"获取分类详情失败: {str(e)}")


@router.delete("/{category_id}", response_model=ChannelCategoryApiResponse, summary="删除渠道分类")
async def delete_category(
    category_id: str,
    current_admin: dict = Depends(check_permission("channels.categories.delete")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    管理员删除渠道分类

    只能删除没有子分类和渠道商的分类。
    """
    try:
        channel_service = ChannelService(db)
        await channel_service.delete_category(category_id)

        return {
            "success": True,
            "data": None,
            "message": "删除渠道分类成功"
        }
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        import traceback
        print(f"删除渠道分类失败详细错误: {str(e)}")
        print(f"错误堆栈: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"删除渠道分类失败: {str(e)}")





# ==================== 公开接口 ====================

@router.get("/channel-types", summary="获取频道类型枚举值")
async def get_channel_types() -> Dict[str, Any]:
    """
    获取频道类型的所有枚举值
    """
    try:
        return {
            "code": 200,
            "message": "获取成功",
            "data": CHANNEL_TYPE_VALUES
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取频道类型失败: {str(e)}")
