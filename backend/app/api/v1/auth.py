from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from app.database import get_db
from app.services.auth_service import AuthService
from app.schemas.auth import *
from app.exceptions import *
from app.dependencies import get_current_user
from typing import Dict, Any

router = APIRouter()

@router.post("/register", response_model=RegisterResponse, summary="用户注册")
async def register(
    request: UserRegisterRequest,
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    用户注册创建账号并选择角色

    - **email**: 邮箱地址，用于登录
    - **password**: 密码，8-20位，包含字母和数字
    - **full_name**: 真实姓名
    - **phone**: 手机号（可选）
    - **referral_code**: 推荐码（可选）
    - **verification_code**: 邮箱验证码
    """
    try:
        auth_service = AuthService(db)
        result = await auth_service.register_user(request)

        return {
            "success": True,
            "data": result,
            "message": "注册成功，请完善角色信息"
        }
    except (ValidationError, BusinessLogicError) as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="注册失败")

@router.post("/login", response_model=LoginResponse, summary="用户登录")
async def login(
    request: UserLoginRequest,
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    用户登录获取访问令牌

    - **email**: 邮箱地址
    - **password**: 密码
    """
    try:
        auth_service = AuthService(db)
        result = await auth_service.login_user(request)

        return {
            "success": True,
            "data": result,
            "message": "登录成功"
        }
    except AuthenticationError as e:
        raise HTTPException(status_code=401, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="登录失败")

@router.post("/send-verification-code", response_model=VerificationCodeResponse, summary="发送验证码")
async def send_verification_code(
    request: SendVerificationCodeRequest,
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    发送邮箱验证码

    - **email**: 邮箱地址
    - **code_type**: 验证码类型（register/login/reset_password/bind_email）
    - **captcha_token**: 图形验证码token（可选，高频请求时需要）
    """
    try:
        auth_service = AuthService(db)
        result = await auth_service.send_verification_code(request)

        return {
            "success": True,
            "data": result
        }
    except BusinessLogicError as e:
        raise HTTPException(status_code=429, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="发送验证码失败，请稍后重试")

@router.post("/verify-code", response_model=VerifyCodeResponse, summary="验证码校验")
async def verify_code(
    request: VerifyCodeRequest,
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    校验邮箱验证码

    - **email**: 邮箱地址
    - **code**: 6位验证码
    - **code_type**: 验证码类型
    """
    try:
        auth_service = AuthService(db)
        result = await auth_service.verify_code(request)

        return {
            "success": True,
            "data": result,
            "message": "验证成功"
        }
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="验证失败，请稍后重试")

@router.post("/refresh", response_model=RefreshTokenResponse, summary="刷新令牌")
async def refresh_token(
    request: RefreshTokenRequest,
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    刷新访问令牌

    - **refresh_token**: 刷新令牌
    """
    try:
        auth_service = AuthService(db)
        result = await auth_service.refresh_token(request)

        return {
            "success": True,
            "data": result
        }
    except AuthenticationError as e:
        raise HTTPException(status_code=401, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="刷新令牌失败，请稍后重试")

@router.post("/logout", response_model=LogoutResponse, summary="用户登出")
async def logout(
    request: LogoutRequest,
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    用户登出清除会话

    - **logout_all_devices**: 是否登出所有设备
    """
    try:
        auth_service = AuthService(db)
        result = await auth_service.logout_user(current_user["id"], request)

        return {
            "success": True,
            "data": result,
            "message": "登出成功"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail="登出失败，请稍后重试")

@router.post("/reset-password", response_model=ResetPasswordResponse, summary="重置密码")
async def reset_password(
    request: ResetPasswordRequest,
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    通过验证码重置密码

    - **email**: 邮箱地址
    - **new_password**: 新密码
    - **verification_token**: 验证码校验后获得的临时令牌
    """
    try:
        auth_service = AuthService(db)
        result = await auth_service.reset_password(request)

        return {
            "success": True,
            "data": result,
            "message": "密码重置成功，请重新登录"
        }
    except (AuthenticationError, ValidationError) as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="重置密码失败，请稍后重试")



