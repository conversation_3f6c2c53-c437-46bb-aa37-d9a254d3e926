#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
软文街API接口

该模块提供软文街API的集成接口，包括：
1. 认证管理
2. 媒体资源查询
3. 订单创建
4. 违禁词查询
5. 订单状态回调处理
"""

from fastapi import APIRouter, Depends, HTTPException, Request, Query
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Dict, Any, Optional, List
from pydantic import BaseModel, validator

from app.database import get_db
from app.services.ruanwenjie_service import RuanwenjieService
from app.dependencies import check_permission
from app.exceptions import ValidationError, BusinessLogicError


router = APIRouter()


# 请求模型定义
class AuthenticateRequest(BaseModel):
    mobile: str
    password: str
    
    @validator('mobile')
    def validate_mobile(cls, v):
        if not v or len(v.strip()) < 11:
            raise ValueError('手机号格式不正确')
        return v.strip()
    
    @validator('password')
    def validate_password(cls, v):
        if not v or len(v.strip()) < 6:
            raise ValueError('密码长度至少6位')
        return v.strip()


class CreateOrderRequest(BaseModel):
    title: str
    content: str
    resource_id: int
    
    @validator('title')
    def validate_title(cls, v):
        if not v or len(v.strip()) < 5:
            raise ValueError('标题至少需要5个字符')
        if len(v) > 200:
            raise ValueError('标题不能超过200个字符')
        return v.strip()
    
    @validator('content')
    def validate_content(cls, v):
        if not v or len(v.strip()) < 50:
            raise ValueError('内容至少需要50个字符')
        return v.strip()
    
    @validator('resource_id')
    def validate_resource_id(cls, v):
        if v <= 0:
            raise ValueError('媒体资源ID必须大于0')
        return v


class MediaResourceQuery(BaseModel):
    page: int = 1
    taxonomy: Optional[str] = None  # 频道类型
    platform: Optional[str] = None  # 综合门户
    area: Optional[str] = None  # 区域
    baidu: Optional[int] = None  # 新闻源 (0非新闻源、1百度新闻源)
    in_level: Optional[int] = None  # 入口级别
    url_type: Optional[str] = None  # 链接类型
    
    @validator('page')
    def validate_page(cls, v):
        if v < 1:
            raise ValueError('页码必须大于0')
        return v


# 响应模型定义
class RuanwenjieApiResponse(BaseModel):
    success: bool
    data: Optional[Any] = None
    message: str


@router.post("/authenticate", response_model=RuanwenjieApiResponse, summary="软文街认证")
async def authenticate(
    request: AuthenticateRequest,
    current_user: dict = Depends(check_permission("ruanwenjie.auth")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    软文街API认证，获取访问token
    
    - **mobile**: 软文街登录手机号
    - **password**: 软文街登录密码
    
    返回token用于后续API调用
    """
    try:
        service = RuanwenjieService(db)
        token = await service.authenticate(request.mobile, request.password)
        
        return {
            "success": True,
            "data": {"token": token},
            "message": "软文街认证成功"
        }
        
    except (ValidationError, BusinessLogicError) as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"认证失败: {str(e)}")


@router.get("/media-resources", response_model=RuanwenjieApiResponse, summary="获取媒体资源列表")
async def get_media_resources(
    page: int = Query(1, ge=1, description="页码"),
    taxonomy: Optional[str] = Query(None, description="频道类型"),
    platform: Optional[str] = Query(None, description="综合门户"),
    area: Optional[str] = Query(None, description="区域"),
    baidu: Optional[int] = Query(None, description="新闻源 (0非新闻源、1百度新闻源)"),
    in_level: Optional[int] = Query(None, description="入口级别"),
    url_type: Optional[str] = Query(None, description="链接类型"),
    current_user: dict = Depends(check_permission("ruanwenjie.media.view")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    获取软文街媒体资源列表
    
    支持多种筛选条件：
    - **taxonomy**: 频道类型，如"新闻资讯"
    - **platform**: 综合门户，如"知名媒体"
    - **area**: 区域，如"全国"、"北京"
    - **baidu**: 新闻源，0非新闻源、1百度新闻源
    - **in_level**: 入口级别，0不限、1首页入口、2频道首页、3栏目页面、4没有入口
    - **url_type**: 链接类型，如"不带"、"可带"
    """
    try:
        service = RuanwenjieService(db)
        
        # 构建筛选条件
        filters = {}
        if taxonomy:
            filters['taxonomy'] = taxonomy
        if platform:
            filters['platform'] = platform
        if area:
            filters['area'] = area
        if baidu is not None:
            filters['baidu'] = baidu
        if in_level is not None:
            filters['in_level'] = in_level
        if url_type:
            filters['url_type'] = url_type
        
        result = await service.get_media_resources(page=page, **filters)
        
        return {
            "success": True,
            "data": result["data"],
            "pagination": result["pagination"],
            "message": "获取媒体资源成功"
        }
        
    except (ValidationError, BusinessLogicError) as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取媒体资源失败: {str(e)}")


@router.post("/orders", response_model=RuanwenjieApiResponse, summary="创建软文发布订单")
async def create_order(
    request: CreateOrderRequest,
    current_user: dict = Depends(check_permission("ruanwenjie.order.create")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    创建软文发布订单
    
    - **title**: 文章标题
    - **content**: 文章内容
    - **resource_id**: 媒体资源ID（从媒体资源列表获取）
    
    注意：测试媒体resource_id=3100（软媒网测试预览）
    """
    try:
        service = RuanwenjieService(db)
        result = await service.create_order(
            title=request.title,
            content=request.content,
            resource_id=request.resource_id,
            user_id=current_user["id"]
        )
        
        return {
            "success": True,
            "data": {
                "order_id": result["order_id"]
            },
            "message": result["message"]
        }
        
    except (ValidationError, BusinessLogicError) as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建订单失败: {str(e)}")


@router.get("/banned-words", response_model=RuanwenjieApiResponse, summary="获取违禁词列表")
async def get_banned_words(
    current_user: dict = Depends(check_permission("ruanwenjie.content.check")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    获取软文街违禁词列表
    
    用于内容审核，确保发布的内容符合平台要求
    """
    try:
        service = RuanwenjieService(db)
        banned_words = await service.get_banned_words()
        
        return {
            "success": True,
            "data": {
                "banned_words": banned_words,
                "count": len(banned_words)
            },
            "message": "获取违禁词列表成功"
        }
        
    except (ValidationError, BusinessLogicError) as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取违禁词失败: {str(e)}")


@router.post("/callback", response_model=RuanwenjieApiResponse, summary="订单状态回调")
async def order_callback(
    request: Request,
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    处理软文街订单状态推送回调
    
    软文街会向此接口推送订单状态更新：
    - status=200: 发布成功，response_message为发布地址
    - status=400: 发布失败，response_message为失败原因
    """
    try:
        # 获取回调数据
        callback_data = await request.json()
        
        service = RuanwenjieService(db)
        result = service.process_order_callback(callback_data)
        
        return {
            "success": True,
            "data": result,
            "message": f"处理了 {result.get('processed_count', 0)} 个订单状态更新"
        }
        
    except Exception as e:
        # 回调接口不应该抛出异常，记录日志即可
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"处理订单回调失败: {e}")
        
        return {
            "success": False,
            "data": None,
            "message": f"处理回调失败: {str(e)}"
        }


@router.get("/token-status", response_model=RuanwenjieApiResponse, summary="检查token状态")
async def check_token_status(
    current_user: dict = Depends(check_permission("ruanwenjie.auth")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    检查当前软文街token的状态
    """
    try:
        service = RuanwenjieService(db)
        
        # 尝试获取token
        try:
            token = await service.get_token()
            return {
                "success": True,
                "data": {
                    "has_token": True,
                    "token_valid": True,
                    "expires_at": service.token_expires_at.isoformat() if service.token_expires_at else None
                },
                "message": "Token状态正常"
            }
        except BusinessLogicError:
            return {
                "success": True,
                "data": {
                    "has_token": False,
                    "token_valid": False,
                    "expires_at": None
                },
                "message": "需要重新认证"
            }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"检查token状态失败: {str(e)}")
