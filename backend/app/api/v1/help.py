"""
帮助文档公开展示API接口（前台使用）
"""
from fastapi import APIRouter, Depends, HTTPException, Query, Path
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional, Dict, Any

from app.database import get_db
from app.services.help_document_service import HelpDocumentPublicService
from app.schemas.help_document import HelpDocumentDetailResponse
from app.exceptions import NotFoundError, PermissionError

router = APIRouter(prefix="/help", tags=["帮助文档"])


@router.get("/categories", summary="获取所有分类")
async def get_public_categories(
    db: AsyncSession = Depends(get_db)
) -> List[Dict[str, Any]]:
    """
    获取所有公开的帮助文档分类
    
    - 只返回启用的分类
    - 只返回包含已发布文档的分类
    - 包含每个分类的文档数量
    - 按排序号和ID排序
    
    无需登录即可访问
    """
    service = HelpDocumentPublicService(db)
    return await service.get_public_categories()


@router.get("/documents", summary="按分类获取文档列表")
async def get_public_documents(
    category_id: Optional[int] = Query(None, description="分类ID，不传则获取所有分类的文档"),
    keyword: Optional[str] = Query(None, description="搜索关键词，在标题和内容中搜索"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    获取公开的帮助文档列表
    
    - 只返回已发布的文档
    - 支持按分类筛选
    - 支持关键词搜索
    - 支持分页
    - 按排序号升序排列
    
    无需登录即可访问
    """
    service = HelpDocumentPublicService(db)
    result = await service.get_public_documents(
        category_id=category_id,
        keyword=keyword,
        page=page,
        page_size=page_size
    )
    
    return {
        "items": result["items"],
        "total": result["total"],
        "page": result["page"],
        "page_size": result["page_size"],
        "total_pages": (result["total"] + result["page_size"] - 1) // result["page_size"]
    }


@router.get("/documents/{document_id}", response_model=HelpDocumentDetailResponse, summary="获取文档详情")
async def get_public_document_detail(
    document_id: int = Path(..., description="文档ID"),
    db: AsyncSession = Depends(get_db)
) -> HelpDocumentDetailResponse:
    """
    获取公开文档的详细内容
    
    - 只能访问已发布的文档
    - 包含文档的完整内容
    - 包含关联的媒体文件信息
    - 会自动增加文档的浏览次数
    
    无需登录即可访问
    """
    service = HelpDocumentPublicService(db)
    try:
        return await service.get_public_document_detail(document_id)
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except PermissionError as e:
        raise HTTPException(status_code=403, detail=str(e))


@router.get("/search", summary="搜索帮助文档")
async def search_help_documents(
    keyword: str = Query(..., description="搜索关键词"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    搜索帮助文档

    - 在文档标题和内容中搜索关键词
    - 只返回已发布的文档
    - 支持分页

    无需登录即可访问
    """
    service = HelpDocumentPublicService(db)
    result = await service.get_public_documents(
        keyword=keyword,
        page=page,
        page_size=page_size
    )

    return {
        "success": True,
        "data": result,
        "message": "搜索完成"
    }


@router.get("/media/{storage_key:path}", summary="获取媒体文件")
async def get_public_media_file(
    storage_key: str = Path(..., description="媒体文件存储键"),
    db: AsyncSession = Depends(get_db)
):
    """
    获取公开的帮助文档媒体文件

    - 通过存储键获取媒体文件
    - 只能访问已发布文档的媒体文件
    - 重定向到实际的文件URL

    无需登录即可访问
    """
    try:
        from app.services.help_document_service import HelpDocumentService
        from app.services.tos_service import tos_service
        from fastapi.responses import RedirectResponse

        service = HelpDocumentService(db)

        # 查找媒体文件
        from sqlalchemy import select
        from app.models.help_document import HelpDocumentMedia, HelpDocument

        result = await db.execute(
            select(HelpDocumentMedia)
            .join(HelpDocument)
            .where(
                HelpDocumentMedia.file_path == storage_key,
                HelpDocument.is_published == True
            )
        )
        media = result.scalar_one_or_none()

        if not media:
            raise HTTPException(status_code=404, detail="媒体文件不存在或未发布")

        # 生成访问URL并重定向
        access_url = tos_service.get_file_url(media.file_path, is_public=True)
        return RedirectResponse(url=access_url, status_code=302)

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail="获取媒体文件失败")
