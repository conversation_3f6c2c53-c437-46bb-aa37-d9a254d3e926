from fastapi import APIRouter
from .auth import router as auth_router
from .users import user_self_router, admin_user_router
from .companies import user_company_router, admin_company_router
from .channels import user_channel_router, admin_channel_router, channel_withdraw_router
from .channel_categories import router as channel_categories_router
from .channel_services import router as channel_services_router
from .channel_category_mappings import router as channel_category_mappings_router
from .provider_services import router as provider_services_router
from .agents import router as agents_router, admin_router as admin_agents_router, withdraw_router as agent_withdraw_router, referral_links_router as agent_referral_links_router, customer_router as agent_customer_router
from .content_enhanced import router as content_enhanced_router
from .orders import router as orders_router
from .monitoring import router as monitoring_router
from .upload import router as upload_router
from .ai import router as ai_router, template_router
from .conversations import router as conversations_router
from .knowledge import router as knowledge_router
from .admin import router as admin_router
from .ruanwenjie import router as ruanwenjie_router

from .roles import router as roles_router
from .permissions import router as permissions_router

from .help import router as help_router
from .admin_help_documents import router as admin_help_documents_router
from .announcements import router as announcements_router
from .subscription_plans import admin_plans_router
from .user_subscriptions import router as user_subscriptions_router

from .platforms import router as platforms_router
from .payments import router as payments_router
from .status import router as status_router
from .public import router as public_router

# Geo监控中心相关路由
from .geo.ai_analysis import router as geo_ai_router
from .geo.statistics import router as geo_stats_router
from .geo.config import router as geo_config_router
from .geo.templates import router as geo_templates_router

api_router = APIRouter()

# 注册所有路由
# 公开接口（不需要认证）
api_router.include_router(public_router, prefix="/public", tags=["公开接口"])
api_router.include_router(auth_router, prefix="/auth", tags=["认证"])
# 用户管理分为两个不同的tags
api_router.include_router(user_self_router, prefix="/users", tags=["用户管理"])
api_router.include_router(admin_user_router, prefix="/users", tags=["管理员用户管理"])
# 企业管理分为两个不同的tags
api_router.include_router(user_company_router, prefix="/companies", tags=["用户企业管理"])
api_router.include_router(admin_company_router, prefix="/companies", tags=["管理员企业管理"])
# 渠道商管理分为三个不同的tags
api_router.include_router(user_channel_router, prefix="/channels", tags=["用户渠道商管理"])
api_router.include_router(admin_channel_router, prefix="/channels", tags=["管理员渠道商管理"])
api_router.include_router(channel_withdraw_router, prefix="/channels", tags=["用户渠道商提现"])
api_router.include_router(channel_categories_router, prefix="/channel-categories", tags=["管理员渠道分类管理"])
api_router.include_router(channel_services_router, prefix="/channel-services", tags=["管理员渠道服务管理"])
api_router.include_router(provider_services_router, prefix="/provider-services", tags=["渠道商服务管理"])
api_router.include_router(channel_category_mappings_router, prefix="/channel-category-mappings", tags=["用户渠道服务管理"])

# 代理商管理分为五个不同的tags
api_router.include_router(agents_router, prefix="/agents", tags=["用户代理商管理"])
api_router.include_router(admin_agents_router, prefix="/agents", tags=["管理员代理商管理"])
api_router.include_router(agent_withdraw_router, prefix="/agents", tags=["用户代理商提现"])
api_router.include_router(agent_referral_links_router, prefix="/agents", tags=["用户代理商推广链接管理"])
api_router.include_router(agent_customer_router, prefix="/agents", tags=["用户代理商客户管理"])

# 注册增强的内容管理路由（不加prefix，因为在路由定义中已包含/api/v1）
api_router.include_router(content_enhanced_router, tags=["内容管理增强"])

api_router.include_router(orders_router, prefix="/orders", tags=["订单管理"])
api_router.include_router(monitoring_router, prefix="/monitoring", tags=["监控管理"])
api_router.include_router(upload_router, prefix="/upload", tags=["文件上传"])
api_router.include_router(ai_router, prefix="/ai", tags=["AI服务"])
api_router.include_router(template_router, prefix="/ai", tags=["AI模板管理"])
api_router.include_router(conversations_router, tags=["对话管理"])
api_router.include_router(knowledge_router, tags=["知识库管理"])
api_router.include_router(admin_router, prefix="/admin", tags=["系统管理"])
api_router.include_router(ruanwenjie_router, prefix="/ruanwenjie", tags=["软文街API"])
# 角色权限管理已合并到permissions模块
api_router.include_router(roles_router, prefix="/roles", tags=["超级管理员角色管理"])
api_router.include_router(permissions_router, prefix="/permissions", tags=["超级管理员权限管理"])



# 帮助文档管理
api_router.include_router(help_router, tags=["帮助文档"])
# 不设置tags，让子路由的tags生效
api_router.include_router(admin_help_documents_router)

# 公告管理
api_router.include_router(announcements_router, prefix="/announcements", tags=["公告管理"])

# 订阅管理 - 统一管理模块
api_router.include_router(admin_plans_router, prefix="/subscriptions", tags=["管理员套餐管理"])
api_router.include_router(user_subscriptions_router, prefix="/subscriptions", tags=["订阅管理"])
# 已删除的订阅模块: quotas, renewals, bills (未实现或依赖不存在)

# 平台集成管理
api_router.include_router(platforms_router, tags=["平台集成"])

# 支付管理
api_router.include_router(payments_router, tags=["支付管理"])

# 状态管理
api_router.include_router(status_router, tags=["状态管理"])

# Geo监控中心
api_router.include_router(geo_ai_router, prefix="/geo", tags=["GEO智能分析"])
api_router.include_router(geo_stats_router, prefix="/geo", tags=["GEO系统统计"])
api_router.include_router(geo_config_router, prefix="/geo", tags=["GEO系统配置"])
api_router.include_router(geo_templates_router, prefix="/geo", tags=["GEO模板管理"])
