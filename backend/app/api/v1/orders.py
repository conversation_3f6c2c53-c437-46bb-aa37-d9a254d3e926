from fastapi import APIRouter, Depends, HTTPException, status, Query, Path, Body
from sqlalchemy.ext.asyncio import AsyncSession
from app.database import get_db
from app.services.order_service import OrderService
# from app.services.subscription_order_service import SubscriptionOrderService
from app.schemas.order import *
from app.exceptions import *
from app.dependencies import get_current_user
from app.core.permissions import check_permission, require_admin_permission
from typing import Dict, Any, Union
from datetime import datetime

router = APIRouter()

@router.post("/subscription", response_model=OrderApiResponse, summary="创建套餐订单")
async def create_subscription_order(
    request: SubscriptionOrderCreate,
    current_user: dict = Depends(check_permission("orders.create")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    创建套餐订单

    - **plan_id**: 套餐ID（必填）
    - **service_months**: 服务月数（1, 3, 6, 12, 24, 36）
    - **coupon_code**: 优惠券代码（可选）
    - **customer_note**: 客户备注（可选）
    - **auto_renewal**: 是否自动续费（默认false）

    支持的套餐类型：
    - **basic**: 基础版（299元/月）
    - **standard**: 标准版（599元/月）
    - **premium**: 高级版（999元/月）

    支持的优惠券：
    - **DISCOUNT10**: 10%折扣
    - **SAVE50**: 最多优惠50元
    """
    try:
        order_service = OrderService(db)
        result = await order_service.create_subscription_order(current_user["id"], request)

        return {
            "success": True,
            "data": result,
            "message": "套餐订单创建并支付成功"
        }
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        import traceback
        print(f"ERROR: 创建套餐订单失败 - {str(e)}")
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"创建套餐订单失败: {str(e)}")

@router.post("/{order_id}/payment", response_model=OrderApiResponse, summary="订单支付")
async def process_payment(
    order_id: str = Path(..., description="订单ID"),
    request: PaymentRequest = ...,
    current_user: dict = Depends(check_permission("orders.create")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    处理订单支付

    - **payment_method**: 支付方式（必填）
      - alipay: 支付宝
      - wechat: 微信支付
      - bank_card: 银行卡
      - balance: 余额支付
    - **return_url**: 支付成功返回URL（可选）
    - **notify_url**: 支付结果通知URL（可选）

    返回支付信息：
    - 第三方支付：返回支付链接和二维码
    - 余额支付：直接完成支付
    """
    try:
        order_service = OrderService(db)
        result = await order_service.process_payment(order_id, request, current_user["id"])

        return {
            "success": True,
            "data": result,
            "message": "支付处理成功"
        }
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except PermissionError as e:
        raise HTTPException(status_code=403, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="支付处理失败")

@router.get("", response_model=OrderApiResponse, summary="获取订单列表")
async def get_order_list(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    order_type: str = Query(None, description="订单类型筛选"),
    order_status: str = Query(None, description="订单状态筛选"),
    start_date: datetime = Query(None, description="开始日期"),
    end_date: datetime = Query(None, description="结束日期"),
    sort_by: str = Query("created_at", description="排序字段"),
    sort_order: str = Query("desc", description="排序方式"),
    current_user: dict = Depends(check_permission("orders.list.view")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    获取用户订单列表

    支持以下功能：
    - **分页查询**: 通过page和size参数控制
    - **类型筛选**: 按订单类型筛选（subscription/content_service）
    - **状态筛选**: 按订单状态筛选
    - **时间筛选**: 按创建时间筛选
    - **排序**: 支持多字段排序

    订单状态说明：
    - **pending**: 待支付
    - **paid**: 已支付
    - **processing**: 处理中
    - **completed**: 已完成
    - **cancelled**: 已取消
    - **refunded**: 已退款

    返回订单列表、分页信息和统计数据
    """
    try:
        query = OrderListQuery(
            page=page,
            size=size,
            order_type=order_type,
            order_status=order_status,
            start_date=start_date,
            end_date=end_date,
            sort_by=sort_by,
            sort_order=sort_order
        )

        # 使用OrderService来处理orders表
        order_service = OrderService(db)
        result = await order_service.get_order_list(query)  # 管理员查看所有订单

        return {
            "success": True,
            "data": result,
            "message": "获取订单列表成功"
        }
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="获取订单列表失败")

@router.get("/my", response_model=OrderApiResponse, summary="获取我的订单")
async def get_my_orders(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    order_status: str = Query(None, description="订单状态筛选"),
    current_user: dict = Depends(check_permission("orders.create")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    获取当前用户的订单列表

    返回当前用户的所有订单，支持分页和状态筛选
    """
    try:
        order_service = OrderService(db)
        # 构建查询参数，只查询当前用户的订单
        query = OrderListQuery(
            page=page,
            size=size,
            order_status=order_status,
            sort_by="created_at",
            sort_order="desc"
        )
        result = await order_service.get_order_list(query, current_user["id"])

        return {
            "success": True,
            "data": result,
            "message": "获取我的订单成功"
        }
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="获取我的订单失败")

@router.get("/{order_id}", response_model=OrderApiResponse, summary="获取订单详情")
async def get_order_detail(
    order_id: str = Path(..., description="订单ID"),
    current_user: dict = Depends(check_permission("orders.create")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    获取订单详情

    返回订单的完整信息，包括：
    - 基本信息：订单号、类型、状态、金额等
    - 商品信息：套餐名称、规格、价格等
    - 支付信息：支付方式、支付状态、支付时间等
    - 订单项：详细的商品项目列表
    - 优惠信息：优惠券、折扣金额等
    - 代理商信息：推荐代理商、佣金等
    - 服务期限：套餐的开始和结束时间
    """
    try:
        # 使用OrderService来处理orders表
        order_service = OrderService(db)
        result = await order_service.get_order_detail(order_id, current_user["id"])

        return {
            "success": True,
            "data": result,
            "message": "获取订单详情成功"
        }
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except PermissionError as e:
        raise HTTPException(status_code=403, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="获取订单详情失败")

# ==================== 新增统一订单接口 ====================

@router.post("", response_model=OrderApiResponse, summary="创建统一订单")
async def create_unified_order(
    request: UnifiedOrderCreate,
    current_user: dict = Depends(check_permission("orders.create")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    创建统一订单接口，自动识别订单类型
    
    支持的订单类型：
    - **subscription**: 套餐订单
    - **content_service**: 内容服务订单
    
    根据order_type自动调用对应的创建逻辑
    """
    try:
        order_service = OrderService(db)
        
        # 根据订单类型调用不同的创建方法
        if request.order_type == "subscription":
            # 转换为套餐订单创建请求
            subscription_request = SubscriptionOrderCreate(
                plan_id=request.plan_id,
                service_months=request.service_months or 12,
                coupon_code=request.coupon_code,
                customer_note=request.customer_note,
                auto_renewal=request.auto_renewal
            )
            result = await order_service.create_subscription_order(current_user["id"], subscription_request)
        elif request.order_type == "content_service":
            # 转换为内容服务订单创建请求
            content_request = ContentServiceOrderCreate(
                service_id=request.service_id,
                content_request_id=request.content_request_id,
                service_provider_id=request.service_provider_id,
                customer_note=request.customer_note
            )
            result = await order_service.create_content_service_order(current_user["id"], content_request)
        else:
            raise ValidationError(f"不支持的订单类型: {request.order_type}")
        
        return {
            "success": True,
            "data": result,
            "message": f"{request.order_type}订单创建成功"
        }
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建订单失败: {str(e)}")

@router.put("/{order_id}", response_model=OrderApiResponse, summary="更新订单信息")
async def update_order(
    order_id: str = Path(..., description="订单ID"),
    request: OrderUpdateRequest = Body(...),
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    更新订单信息
    
    只允许更新以下字段：
    - **customer_note**: 客户备注
    - **admin_note**: 管理员备注（需要管理员权限）
    
    注意：订单状态变更请使用专门的状态变更接口
    """
    try:
        order_service = OrderService(db)
        
        # 检查是否有管理员权限
        is_admin = any(role in ["admin", "super_admin"] for role in current_user.get("roles", []))
        
        # 构建更新数据
        update_data = {}
        if request.customer_note is not None:
            update_data["customer_note"] = request.customer_note
        if request.admin_note is not None:
            if not is_admin:
                raise PermissionError("只有管理员可以更新管理员备注")
            update_data["admin_note"] = request.admin_note
        
        if not update_data:
            raise ValidationError("没有需要更新的字段")
        
        result = await order_service.update_order(order_id, update_data, current_user["id"], is_admin)
        
        return {
            "success": True,
            "data": result,
            "message": "订单信息更新成功"
        }
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except PermissionError as e:
        raise HTTPException(status_code=403, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新订单失败: {str(e)}")

@router.post("/{order_id}/cancel", response_model=OrderApiResponse, summary="取消订单")
async def cancel_order(
    order_id: str = Path(..., description="订单ID"),
    request: CancelOrderRequest = Body(...),
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    取消订单
    
    取消条件：
    - 订单状态必须是pending或paid
    - 如果已支付，可以申请退款
    - 套餐订单在服务开始后不能取消
    
    取消后会：
    - 更新订单状态为cancelled
    - 如果申请退款，创建退款记录
    - 恢复相关配额或库存
    """
    try:
        order_service = OrderService(db)
        
        # 检查是否有管理员权限
        is_admin = any(role in ["admin", "super_admin"] for role in current_user.get("roles", []))
        
        result = await order_service.cancel_order(
            order_id=order_id,
            user_id=current_user["id"],
            reason=request.reason,
            refund_requested=request.refund_requested,
            is_admin=is_admin
        )
        
        return {
            "success": True,
            "data": result,
            "message": "订单已成功取消"
        }
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except PermissionError as e:
        raise HTTPException(status_code=403, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"取消订单失败: {str(e)}")

# ==================== 订单查询增强接口 ====================

@router.post("/search", response_model=OrderApiResponse, summary="高级搜索订单")
async def search_orders(
    request: OrderSearchRequest = Body(...),
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    高级搜索订单
    
    支持的搜索条件：
    - **keyword**: 搜索订单号、产品名称等
    - **order_types**: 订单类型列表
    - **order_statuses**: 订单状态列表
    - **min_amount/max_amount**: 金额范围
    - **start_date/end_date**: 时间范围
    - **has_coupon**: 是否使用优惠券
    """
    try:
        order_service = OrderService(db)
        
        # 检查是否有管理员权限
        is_admin = any(role in ["admin", "super_admin"] for role in current_user.get("roles", []))
        
        result = await order_service.search_orders(
            search_params=request.dict(exclude_none=True),
            user_id=None if is_admin else current_user["id"],
            page=request.page,
            size=request.size
        )
        
        return {
            "success": True,
            "data": result,
            "message": "搜索完成"
        }
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"搜索订单失败: {str(e)}")

@router.get("/by-type/{order_type}", response_model=OrderApiResponse, summary="按类型查询订单")
async def get_orders_by_type(
    order_type: str = Path(..., description="订单类型"),
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    按类型查询订单
    
    订单类型：
    - **subscription**: 套餐订单
    - **content_service**: 内容服务订单
    """
    try:
        if order_type not in ['subscription', 'content_service']:
            raise ValidationError(f"不支持的订单类型: {order_type}")
        
        order_service = OrderService(db)
        
        # 检查是否有管理员权限
        is_admin = any(role in ["admin", "super_admin"] for role in current_user.get("roles", []))
        
        query = OrderListQuery(
            page=page,
            size=size,
            order_type=order_type,
            sort_by="created_at",
            sort_order="desc"
        )
        
        result = await order_service.get_order_list(
            query=query,
            user_id=None if is_admin else current_user["id"]
        )
        
        return {
            "success": True,
            "data": result,
            "message": f"获取{order_type}订单列表成功"
        }
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="查询订单失败")

@router.get("/by-status/{order_status}", response_model=OrderApiResponse, summary="按状态查询订单")
async def get_orders_by_status(
    order_status: str = Path(..., description="订单状态"),
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    按状态查询订单
    
    订单状态：
    - **pending**: 待支付
    - **paid**: 已支付
    - **processing**: 处理中
    - **completed**: 已完成
    - **cancelled**: 已取消
    - **refunded**: 已退款
    """
    try:
        valid_statuses = ['pending', 'paid', 'processing', 'completed', 'cancelled', 'refunded']
        if order_status not in valid_statuses:
            raise ValidationError(f"不支持的订单状态: {order_status}")
        
        order_service = OrderService(db)
        
        # 检查是否有管理员权限
        is_admin = any(role in ["admin", "super_admin"] for role in current_user.get("roles", []))
        
        query = OrderListQuery(
            page=page,
            size=size,
            order_status=order_status,
            sort_by="created_at",
            sort_order="desc"
        )
        
        result = await order_service.get_order_list(
            query=query,
            user_id=None if is_admin else current_user["id"]
        )
        
        return {
            "success": True,
            "data": result,
            "message": f"获取{order_status}状态订单列表成功"
        }
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="查询订单失败")

@router.get("/recent", response_model=OrderApiResponse, summary="获取最近订单")
async def get_recent_orders(
    days: int = Query(7, ge=1, le=30, description="最近天数"),
    size: int = Query(10, ge=1, le=50),
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    获取最近的订单
    
    默认返回最近7天的订单，最多30天
    """
    try:
        from datetime import timedelta
        
        order_service = OrderService(db)
        
        # 计算开始日期
        start_date = datetime.utcnow() - timedelta(days=days)
        
        query = OrderListQuery(
            page=1,
            size=size,
            start_date=start_date,
            sort_by="created_at",
            sort_order="desc"
        )
        
        result = await order_service.get_order_list(
            query=query,
            user_id=current_user["id"]
        )
        
        return {
            "success": True,
            "data": result,
            "message": f"获取最近{days}天的订单成功"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail="获取最近订单失败")

@router.get("/pending-payment", response_model=OrderApiResponse, summary="获取待支付订单")
async def get_pending_payment_orders(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    获取待支付订单
    
    返回当前用户所有待支付的订单
    """
    try:
        order_service = OrderService(db)
        
        query = OrderListQuery(
            page=page,
            size=size,
            order_status="pending",
            sort_by="created_at",
            sort_order="desc"
        )
        
        result = await order_service.get_order_list(
            query=query,
            user_id=current_user["id"]
        )
        
        return {
            "success": True,
            "data": result,
            "message": "获取待支付订单成功"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail="获取待支付订单失败")
