"""
系统统计接口
"""
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel

from ....services.geo.geo_service import geo_service
from ....core.geo.config import AIModelType

router = APIRouter(prefix="/stats", tags=["GEO系统统计"])

# 响应模型
class SystemStats(BaseModel):
    """系统统计"""
    today_analysis: int
    total_analysis: int
    active_models: int
    success_rate: float
    avg_response_time: float
    last_update: str

class ModelStats(BaseModel):
    """AI模型统计"""
    model_type: str
    model_name: str
    usage_count: int
    success_count: int
    success_rate: float
    avg_response_time: float
    status: str

    class Config:
        # 解决Pydantic命名空间冲突警告
        protected_namespaces = ()

class AnalysisTypeStats(BaseModel):
    """分析类型统计"""
    analysis_type: str
    count: int
    percentage: float

class DailyStats(BaseModel):
    """每日统计"""
    date: str
    analysis_count: int
    success_count: int
    success_rate: float

class SystemStatsResponse(BaseModel):
    """系统统计响应"""
    system: SystemStats
    models: List[ModelStats]
    analysis_types: List[AnalysisTypeStats]
    daily_stats: List[DailyStats]

# 模拟数据存储（实际项目中应该使用数据库）
_stats_cache = {
    "total_analysis": 1256,
    "today_analysis": 156,
    "success_count": 1238,
    "total_response_time": 2890.5,
    "model_usage": {
        "doubao": {"count": 450, "success": 445, "response_time": 1200.5},
        "kimi": {"count": 320, "success": 318, "response_time": 890.2},
        "deepseek": {"count": 280, "success": 275, "response_time": 750.8},
        "gpt": {"count": 120, "success": 118, "response_time": 580.3},
        "grok": {"count": 56, "success": 54, "response_time": 320.1},
        "perplexity": {"count": 30, "success": 28, "response_time": 148.6}
    },
    "analysis_types": {
        "keyword_search": 520,
        "platform_recommend": 310,
        "account_recommend": 280,
        "platform_identify": 146,
        "keyword_ranking": 0
    },
    "last_update": datetime.now().isoformat()
}

@router.get("/", response_model=SystemStatsResponse, summary="获取系统统计信息")
async def get_system_stats():
    """获取系统统计信息"""
    try:
        # 获取可用模型
        available_models = geo_service.get_available_models()
        
        # 计算系统统计
        total_analysis = _stats_cache["total_analysis"]
        success_count = _stats_cache["success_count"]
        success_rate = (success_count / total_analysis * 100) if total_analysis > 0 else 0
        avg_response_time = (_stats_cache["total_response_time"] / total_analysis) if total_analysis > 0 else 0
        
        system_stats = SystemStats(
            today_analysis=_stats_cache["today_analysis"],
            total_analysis=total_analysis,
            active_models=len(available_models),
            success_rate=round(success_rate, 1),
            avg_response_time=round(avg_response_time, 2),
            last_update=_stats_cache["last_update"]
        )
        
        # 模型统计
        model_stats = []
        for model_type, usage in _stats_cache["model_usage"].items():
            model_info = next((m for m in available_models if m["model_type"] == model_type), None)
            if model_info:
                model_success_rate = (usage["success"] / usage["count"] * 100) if usage["count"] > 0 else 0
                model_avg_time = (usage["response_time"] / usage["count"]) if usage["count"] > 0 else 0
                
                model_stats.append(ModelStats(
                    model_type=model_type,
                    model_name=model_info["model_name"],
                    usage_count=usage["count"],
                    success_count=usage["success"],
                    success_rate=round(model_success_rate, 1),
                    avg_response_time=round(model_avg_time, 2),
                    status="可用" if model_info["enabled"] else "不可用"
                ))
        
        # 分析类型统计
        total_types = sum(_stats_cache["analysis_types"].values())
        analysis_type_stats = []
        for analysis_type, count in _stats_cache["analysis_types"].items():
            percentage = (count / total_types * 100) if total_types > 0 else 0
            analysis_type_stats.append(AnalysisTypeStats(
                analysis_type=analysis_type,
                count=count,
                percentage=round(percentage, 1)
            ))
        
        # 每日统计（最近7天）
        daily_stats = []
        for i in range(7):
            date = (datetime.now() - timedelta(days=i)).strftime("%Y-%m-%d")
            # 模拟每日数据
            daily_count = max(0, _stats_cache["today_analysis"] - i * 20 + (i % 3) * 15)
            daily_success = int(daily_count * 0.985)
            daily_success_rate = (daily_success / daily_count * 100) if daily_count > 0 else 0
            
            daily_stats.append(DailyStats(
                date=date,
                analysis_count=daily_count,
                success_count=daily_success,
                success_rate=round(daily_success_rate, 1)
            ))
        
        return SystemStatsResponse(
            system=system_stats,
            models=model_stats,
            analysis_types=analysis_type_stats,
            daily_stats=daily_stats
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取系统统计失败: {str(e)}")

@router.post("/analysis", summary="记录分析统计")
async def record_analysis(
    analysis_type: str,
    ai_model: str,
    success: bool,
    response_time: float
):
    """记录分析统计"""
    try:
        # 更新统计数据
        _stats_cache["total_analysis"] += 1
        _stats_cache["today_analysis"] += 1
        
        if success:
            _stats_cache["success_count"] += 1
        
        _stats_cache["total_response_time"] += response_time
        
        # 更新模型统计
        if ai_model in _stats_cache["model_usage"]:
            _stats_cache["model_usage"][ai_model]["count"] += 1
            if success:
                _stats_cache["model_usage"][ai_model]["success"] += 1
            _stats_cache["model_usage"][ai_model]["response_time"] += response_time
        
        # 更新分析类型统计
        if analysis_type in _stats_cache["analysis_types"]:
            _stats_cache["analysis_types"][analysis_type] += 1
        
        _stats_cache["last_update"] = datetime.now().isoformat()
        
        return {"message": "统计记录成功", "success": True}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"记录统计失败: {str(e)}")

@router.get("/models", summary="获取AI模型统计")
async def get_model_stats():
    """获取AI模型统计"""
    try:
        available_models = geo_service.get_available_models()
        
        model_stats = []
        for model_type, usage in _stats_cache["model_usage"].items():
            model_info = next((m for m in available_models if m["model_type"] == model_type), None)
            if model_info:
                model_success_rate = (usage["success"] / usage["count"] * 100) if usage["count"] > 0 else 0
                model_avg_time = (usage["response_time"] / usage["count"]) if usage["count"] > 0 else 0
                
                model_stats.append({
                    "model_type": model_type,
                    "model_name": model_info["model_name"],
                    "usage_count": usage["count"],
                    "success_count": usage["success"],
                    "success_rate": round(model_success_rate, 1),
                    "avg_response_time": round(model_avg_time, 2),
                    "status": "可用" if model_info["enabled"] else "不可用",
                    "cost_level": model_info["cost_level"]
                })
        
        return {"models": model_stats}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取模型统计失败: {str(e)}")

@router.get("/daily/{days}", summary="获取每日统计")
async def get_daily_stats(days: int = 7):
    """获取每日统计（最近N天）"""
    try:
        if days > 30:
            days = 30  # 限制最多30天
        
        daily_stats = []
        for i in range(days):
            date = (datetime.now() - timedelta(days=i)).strftime("%Y-%m-%d")
            # 模拟每日数据
            daily_count = max(0, _stats_cache["today_analysis"] - i * 20 + (i % 3) * 15)
            daily_success = int(daily_count * 0.985)
            daily_success_rate = (daily_success / daily_count * 100) if daily_count > 0 else 0
            
            daily_stats.append({
                "date": date,
                "analysis_count": daily_count,
                "success_count": daily_success,
                "success_rate": round(daily_success_rate, 1)
            })
        
        return {"daily_stats": daily_stats}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取每日统计失败: {str(e)}")

@router.delete("/reset", summary="重置统计数据")
async def reset_stats():
    """重置统计数据（仅开发环境）"""
    try:
        global _stats_cache
        _stats_cache = {
            "total_analysis": 0,
            "today_analysis": 0,
            "success_count": 0,
            "total_response_time": 0.0,
            "model_usage": {
                "doubao": {"count": 0, "success": 0, "response_time": 0.0},
                "kimi": {"count": 0, "success": 0, "response_time": 0.0},
                "deepseek": {"count": 0, "success": 0, "response_time": 0.0},
                "gpt": {"count": 0, "success": 0, "response_time": 0.0},
                "grok": {"count": 0, "success": 0, "response_time": 0.0},
                "perplexity": {"count": 0, "success": 0, "response_time": 0.0}
            },
            "analysis_types": {
                "keyword_search": 0,
                "platform_recommend": 0,
                "account_recommend": 0,
                "platform_identify": 0,
                "keyword_ranking": 0
            },
            "last_update": datetime.now().isoformat()
        }
        
        return {"message": "统计数据已重置", "success": True}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"重置统计失败: {str(e)}")
