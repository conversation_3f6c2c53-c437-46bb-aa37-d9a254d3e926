"""
模板管理接口
"""
from typing import List, Optional
from fastapi import APIRouter, HTTPException, Request
from fastapi.responses import HTMLResponse
from pydantic import BaseModel

from ....services.geo.template_service import template_service
from ....models.geo.template_config import TemplateInfo, TemplateType

router = APIRouter(prefix="/templates", tags=["GEO模板管理"])

# 响应模型
class TemplateListResponse(BaseModel):
    """模板列表响应"""
    templates: List[dict]
    total: int

class TemplateDetailResponse(BaseModel):
    """模板详情响应"""
    name: str
    type: str
    title: str
    description: str
    file_path: str
    css_files: List[str]
    js_files: List[str]

@router.get("/", response_model=TemplateListResponse, summary="获取模板列表")
async def get_template_list():
    """获取模板列表"""
    try:
        templates = template_service.get_template_list()
        template_data = []

        for template in templates:
            template_data.append({
                "name": template.name,
                "type": template.type.value,
                "title": template.title,
                "description": template.description,
                "file_path": template.file_path,
                "css_files": template.css_files,
                "js_files": template.js_files
            })

        return TemplateListResponse(
            templates=template_data,
            total=len(template_data)
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取模板列表失败: {str(e)}")

@router.get("/standard", response_model=dict, summary="获取标准模板列表")
async def get_standard_templates():
    """获取标准模板列表"""
    try:
        templates = template_service.get_template_list()
        template_data = []

        for template in templates:
            template_data.append({
                "template_key": template.name,
                "title": template.title,
                "description": template.description,
                "category": template.type.value,
                "icon": "description",
                "file_size": "2.5KB",
                "is_active": True,
                "sort_order": 1
            })

        return {
            "success": True,
            "data": template_data,
            "total": len(template_data)
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取标准模板列表失败: {str(e)}")

@router.get("/custom", response_model=dict, summary="获取自定义模板列表")
async def get_custom_templates():
    """获取用户自定义模板列表"""
    try:
        # 暂时返回空列表，后续可以实现用户自定义模板功能
        return {
            "success": True,
            "data": [],
            "total": 0
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取用户模板列表失败: {str(e)}")

@router.get("/standard/{template_key}", response_model=dict, summary="获取标准模板详情")
async def get_standard_template_detail(template_key: str):
    """获取标准模板详情"""
    try:
        template = template_service.get_template(template_key)
        if not template:
            raise HTTPException(status_code=404, detail=f"模板 {template_key} 不存在")

        return {
            "success": True,
            "data": {
                "template_key": template.name,
                "title": template.title,
                "description": template.description,
                "category": template.type.value,
                "html_page": "",  # 将在渲染时提供
                "variables": {},
                "file_size": "2.5KB",
                "is_active": True,
                "sort_order": 1
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取标准模板详情失败: {str(e)}")

@router.get("/standard/{template_key}/render", response_class=HTMLResponse, summary="渲染标准模板")
async def render_standard_template(template_key: str, request: Request):
    """渲染标准模板"""
    try:
        # 获取查询参数作为模板数据
        template_data = dict(request.query_params)

        # 解析variables参数
        if 'variables' in template_data:
            import json
            try:
                variables = json.loads(template_data['variables'])
                template_data.update(variables)
                del template_data['variables']
            except json.JSONDecodeError:
                pass

        # 渲染模板
        result = await template_service.render_template_async(template_key, template_data)

        if not result.success:
            raise HTTPException(status_code=500, detail=f"模板渲染失败: {result.error}")

        return HTMLResponse(content=result.content)

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"渲染标准模板失败: {str(e)}")

@router.get("/{template_name}", response_model=TemplateDetailResponse, summary="获取模板详情")
async def get_template_detail(template_name: str):
    """获取模板详情"""
    try:
        template = template_service.get_template(template_name)
        if not template:
            raise HTTPException(status_code=404, detail=f"模板 {template_name} 不存在")

        return TemplateDetailResponse(
            name=template.name,
            type=template.type.value,
            title=template.title,
            description=template.description,
            file_path=template.file_path,
            css_files=template.css_files,
            js_files=template.js_files
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取模板详情失败: {str(e)}")

@router.get("/{template_name}/render", response_class=HTMLResponse, summary="渲染模板")
async def render_template(template_name: str, request: Request):
    """渲染模板"""
    try:
        # 获取查询参数作为模板数据
        template_data = dict(request.query_params)

        # 渲染模板
        result = await template_service.render_template_async(template_name, template_data)

        if not result.success:
            raise HTTPException(status_code=500, detail=f"模板渲染失败: {result.error}")

        return HTMLResponse(content=result.content)

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"渲染模板失败: {str(e)}")

@router.post("/{template_name}/render", response_class=HTMLResponse, summary="使用数据渲染模板")
async def render_template_with_data(template_name: str, data: dict):
    """使用POST数据渲染模板"""
    try:
        # 渲染模板
        result = await template_service.render_template_async(template_name, data)

        if not result.success:
            raise HTTPException(status_code=500, detail=f"模板渲染失败: {result.error}")

        return HTMLResponse(content=result.content)

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"渲染模板失败: {str(e)}")