"""
AI分析相关接口
"""
from typing import List, Optional
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, Field

from ....services.geo.geo_service import geo_service
from ....core.geo.config import AIModelType
from ....core.geo.exceptions import AIModelNotAvailableException, GeoException

router = APIRouter(prefix="/ai", tags=["GEO智能分析"])

# 请求模型
class KeywordSearchRequest(BaseModel):
    """关键词搜索请求"""
    keyword: str = Field(..., description="目标关键词")
    ai_model: str = Field(..., description="AI模型类型")

class PlatformRecommendRequest(BaseModel):
    """平台推荐请求"""
    keyword: str = Field(..., description="目标关键词")
    platform_resources: str = Field(..., description="平台资源信息")
    ai_model: str = Field(..., description="AI模型类型")

class AccountRecommendRequest(BaseModel):
    """账号推荐请求"""
    keyword: str = Field(..., description="目标关键词")
    account_info: str = Field(..., description="账号信息")
    ai_model: str = Field(..., description="AI模型类型")

class PlatformIdentifyRequest(BaseModel):
    """平台识别请求"""
    source_content: str = Field(..., description="信源内容")
    ai_model: str = Field(..., description="AI模型类型")

class KeywordRankingRequest(BaseModel):
    """关键词排名分析请求"""
    keywords: List[str] = Field(..., description="目标关键词列表")
    content: str = Field(..., description="分析内容")
    ai_model: str = Field(..., description="AI模型类型")

# 响应模型
class SearchResponse(BaseModel):
    """搜索响应"""
    keyword: str
    ai_model: str
    success: bool
    sources: List[dict]
    response_time: float
    error: Optional[str] = None

class PlatformRecommendationResponse(BaseModel):
    """平台推荐响应"""
    keyword: str
    ai_model: str
    success: bool
    recommendations: List[dict]
    response_time: float
    error: Optional[str] = None

class AccountRecommendationResponse(BaseModel):
    """账号推荐响应"""
    keyword: str
    ai_model: str
    success: bool
    recommendations: List[dict]
    response_time: float
    error: Optional[str] = None

class PlatformIdentifyResponse(BaseModel):
    """平台识别响应"""
    success: bool
    platforms: List[dict]
    response_time: float
    error: Optional[str] = None

class KeywordRankingResponse(BaseModel):
    """关键词排名响应"""
    success: bool
    rankings: List[dict]
    response_time: float
    error: Optional[str] = None

class AIModelInfo(BaseModel):
    """AI模型信息"""
    model_id: str
    model_name: str
    model_type: str
    status: str
    cost_level: str

    class Config:
        # 解决Pydantic命名空间冲突警告
        protected_namespaces = ()

@router.post("/search", response_model=SearchResponse, summary="关键词搜索分析")
async def search_keyword(request: KeywordSearchRequest):
    """关键词搜索并提取信息来源"""
    try:
        # 验证AI模型类型
        try:
            ai_model = AIModelType(request.ai_model)
        except ValueError:
            raise HTTPException(
                status_code=400,
                detail=f"不支持的AI模型类型: {request.ai_model}"
            )

        # 执行搜索
        result = await geo_service.search_keyword_sources(request.keyword, ai_model)

        if not result.success:
            return SearchResponse(
                keyword=request.keyword,
                ai_model=request.ai_model,
                success=False,
                sources=[],
                response_time=0,
                error=result.error
            )

        return SearchResponse(
            keyword=result.keyword,
            ai_model=result.ai_model.value,
            success=result.success,
            sources=result.structured_data.content.get("sources", []),
            response_time=result.raw_analysis.response_time,
            error=result.error
        )

    except AIModelNotAvailableException as e:
        raise HTTPException(status_code=400, detail=e.message)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"关键词搜索失败: {str(e)}")

@router.post("/platform-recommend", response_model=PlatformRecommendationResponse, summary="平台推荐分析")
async def recommend_platforms(request: PlatformRecommendRequest):
    """平台推荐"""
    try:
        # 验证AI模型类型
        try:
            ai_model = AIModelType(request.ai_model)
        except ValueError:
            raise HTTPException(
                status_code=400,
                detail=f"不支持的AI模型类型: {request.ai_model}"
            )

        # 执行平台推荐
        result = await geo_service.analyze_platform_recommendations(
            request.keyword, 
            request.platform_resources, 
            ai_model
        )

        if not result.success:
            return PlatformRecommendationResponse(
                keyword=request.keyword,
                ai_model=request.ai_model,
                success=False,
                recommendations=[],
                response_time=0,
                error=result.error
            )

        return PlatformRecommendationResponse(
            keyword=result.keyword,
            ai_model=result.ai_model.value,
            success=result.success,
            recommendations=result.structured_data.content.get("platform_recommendations", []),
            response_time=result.raw_analysis.response_time,
            error=result.error
        )

    except AIModelNotAvailableException as e:
        raise HTTPException(status_code=400, detail=e.message)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"平台推荐失败: {str(e)}")

@router.post("/account-recommend", response_model=AccountRecommendationResponse, summary="账号推荐分析")
async def recommend_accounts(request: AccountRecommendRequest):
    """账号推荐"""
    try:
        # 验证AI模型类型
        try:
            ai_model = AIModelType(request.ai_model)
        except ValueError:
            raise HTTPException(
                status_code=400,
                detail=f"不支持的AI模型类型: {request.ai_model}"
            )

        # 执行账号推荐
        result = await geo_service.analyze_account_recommendations(
            request.keyword, 
            request.account_info, 
            ai_model
        )

        if not result.success:
            return AccountRecommendationResponse(
                keyword=request.keyword,
                ai_model=request.ai_model,
                success=False,
                recommendations=[],
                response_time=0,
                error=result.error
            )

        return AccountRecommendationResponse(
            keyword=result.keyword,
            ai_model=result.ai_model.value,
            success=result.success,
            recommendations=result.structured_data.content.get("account_recommendations", []),
            response_time=result.raw_analysis.response_time,
            error=result.error
        )

    except AIModelNotAvailableException as e:
        raise HTTPException(status_code=400, detail=e.message)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"账号推荐失败: {str(e)}")

@router.post("/platform-identify", response_model=PlatformIdentifyResponse, summary="平台识别分析")
async def identify_platforms(request: PlatformIdentifyRequest):
    """平台识别"""
    try:
        # 验证AI模型类型
        try:
            ai_model = AIModelType(request.ai_model)
        except ValueError:
            raise HTTPException(
                status_code=400,
                detail=f"不支持的AI模型类型: {request.ai_model}"
            )

        # 执行平台识别
        result = await geo_service.identify_platforms(
            request.source_content,
            ai_model
        )

        if not result.success:
            return PlatformIdentifyResponse(
                success=False,
                platforms=[],
                response_time=0,
                error=result.error
            )

        return PlatformIdentifyResponse(
            success=result.success,
            platforms=result.structured_data.content.get("platforms", []),
            response_time=result.raw_analysis.response_time,
            error=result.error
        )

    except AIModelNotAvailableException as e:
        raise HTTPException(status_code=400, detail=e.message)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"平台识别失败: {str(e)}")

@router.post("/keyword-ranking", response_model=KeywordRankingResponse, summary="关键词排名分析")
async def analyze_keyword_ranking(request: KeywordRankingRequest):
    """关键词排名分析"""
    try:
        # 验证AI模型类型
        try:
            ai_model = AIModelType(request.ai_model)
        except ValueError:
            raise HTTPException(
                status_code=400,
                detail=f"不支持的AI模型类型: {request.ai_model}"
            )

        # 执行关键词排名分析
        result = await geo_service.analyze_keyword_ranking(
            request.keywords,
            request.content,
            ai_model
        )

        if not result.success:
            return KeywordRankingResponse(
                success=False,
                rankings=[],
                response_time=0,
                error=result.error
            )

        return KeywordRankingResponse(
            success=result.success,
            rankings=result.structured_data.content.get("keyword_rankings", []),
            response_time=result.raw_analysis.response_time,
            error=result.error
        )

    except AIModelNotAvailableException as e:
        raise HTTPException(status_code=400, detail=e.message)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"关键词排名分析失败: {str(e)}")

@router.get("/models", summary="获取AI模型列表")
async def get_ai_models():
    """获取可用AI模型列表"""
    try:
        models = geo_service.get_available_models()

        # 转换为响应格式
        model_list = []
        for model in models:
            model_list.append(AIModelInfo(
                model_id=model["model_id"],
                model_name=model["model_name"],
                model_type=model["model_type"],
                status="可用" if model["enabled"] else "不可用",
                cost_level=model["cost_level"]
            ))

        return {"models": model_list}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取模型列表失败: {str(e)}")
