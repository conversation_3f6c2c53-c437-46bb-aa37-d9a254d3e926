"""
系统配置接口
"""
import os
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel

from ....core.geo.config import AIModelType
from ....services.geo.geo_service import geo_service

router = APIRouter(prefix="/config", tags=["GEO系统配置"])

# 请求模型
class ModelConfigUpdate(BaseModel):
    """AI模型配置更新"""
    model_type: str
    enabled: bool
    api_key: Optional[str] = None
    base_url: Optional[str] = None
    max_tokens: Optional[int] = None
    temperature: Optional[float] = None

    class Config:
        # 解决Pydantic命名空间冲突警告
        protected_namespaces = ()

class SystemConfigUpdate(BaseModel):
    """系统配置更新"""
    cache_enabled: bool = True
    cache_ttl: int = 3600
    max_concurrent_requests: int = 10
    request_timeout: int = 30
    debug_mode: bool = False

# 响应模型
class ModelConfig(BaseModel):
    """AI模型配置"""
    model_type: str
    model_name: str
    enabled: bool
    has_api_key: bool
    base_url: Optional[str] = None
    max_tokens: Optional[int] = None
    temperature: Optional[float] = None
    cost_level: str
    description: str

    class Config:
        # 解决Pydantic命名空间冲突警告
        protected_namespaces = ()

class SystemConfig(BaseModel):
    """系统配置"""
    cache_enabled: bool
    cache_ttl: int
    max_concurrent_requests: int
    request_timeout: int
    debug_mode: bool
    version: str
    environment: str

class ConfigResponse(BaseModel):
    """配置响应"""
    system: SystemConfig
    models: List[ModelConfig]

# 模拟配置存储（实际项目中应该使用数据库或配置文件）
_system_config = {
    "cache_enabled": True,
    "cache_ttl": 3600,
    "max_concurrent_requests": 10,
    "request_timeout": 30,
    "debug_mode": False,
    "version": "1.0.0",
    "environment": "development"
}

@router.get("/", response_model=ConfigResponse)
async def get_config():
    """获取系统配置"""
    try:
        # 获取AI模型配置
        available_models = geo_service.get_available_models()
        
        model_configs = []
        for model in available_models:
            # 检查是否有API密钥
            env_key = f"{model['model_type'].upper()}_API_KEY"
            has_api_key = bool(os.getenv(env_key))
            
            model_configs.append(ModelConfig(
                model_type=model["model_type"],
                model_name=model["model_name"],
                enabled=model["enabled"],
                has_api_key=has_api_key,
                base_url=model.get("base_url"),
                max_tokens=model.get("max_tokens"),
                temperature=model.get("temperature"),
                cost_level=model["cost_level"],
                description=model.get("description", "")
            ))
        
        # 获取系统配置
        system_config = SystemConfig(
            cache_enabled=_system_config["cache_enabled"],
            cache_ttl=_system_config["cache_ttl"],
            max_concurrent_requests=_system_config["max_concurrent_requests"],
            request_timeout=_system_config["request_timeout"],
            debug_mode=_system_config["debug_mode"],
            version=_system_config["version"],
            environment=_system_config["environment"]
        )
        
        return ConfigResponse(
            system=system_config,
            models=model_configs
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取配置失败: {str(e)}")

@router.put("/system")
async def update_system_config(config: SystemConfigUpdate):
    """更新系统配置"""
    try:
        # 更新系统配置
        _system_config.update({
            "cache_enabled": config.cache_enabled,
            "cache_ttl": config.cache_ttl,
            "max_concurrent_requests": config.max_concurrent_requests,
            "request_timeout": config.request_timeout,
            "debug_mode": config.debug_mode
        })
        
        # 应用缓存配置
        from ....services.geo.template_service import template_service
        template_service.set_cache_enabled(config.cache_enabled)
        
        return {
            "message": "系统配置更新成功",
            "success": True,
            "config": _system_config
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新系统配置失败: {str(e)}")

@router.put("/models/{model_type}")
async def update_model_config(model_type: str, config: ModelConfigUpdate):
    """更新AI模型配置"""
    try:
        # 验证模型类型
        try:
            AIModelType(model_type)
        except ValueError:
            raise HTTPException(status_code=400, detail=f"不支持的模型类型: {model_type}")
        
        # 这里应该更新模型配置
        # 实际项目中需要更新配置文件或数据库
        
        return {
            "message": f"模型 {model_type} 配置更新成功",
            "success": True,
            "model_type": model_type,
            "enabled": config.enabled
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新模型配置失败: {str(e)}")

@router.get("/models")
async def get_model_configs():
    """获取AI模型配置列表"""
    try:
        available_models = geo_service.get_available_models()
        
        model_configs = []
        for model in available_models:
            # 检查是否有API密钥
            env_key = f"{model['model_type'].upper()}_API_KEY"
            has_api_key = bool(os.getenv(env_key))
            
            model_configs.append({
                "model_type": model["model_type"],
                "model_name": model["model_name"],
                "enabled": model["enabled"],
                "has_api_key": has_api_key,
                "base_url": model.get("base_url"),
                "max_tokens": model.get("max_tokens"),
                "temperature": model.get("temperature"),
                "cost_level": model["cost_level"],
                "description": model.get("description", "")
            })
        
        return {"models": model_configs}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取模型配置失败: {str(e)}")

@router.get("/environment", summary="获取环境信息")
async def get_environment_info():
    """获取环境信息"""
    try:
        # 检查环境变量
        env_vars = {}
        ai_models = ["DOUBAO", "KIMI", "DEEPSEEK", "GPT", "GROK", "PERPLEXITY"]
        
        for model in ai_models:
            api_key = f"{model}_API_KEY"
            base_url = f"{model}_BASE_URL"
            
            env_vars[model.lower()] = {
                "has_api_key": bool(os.getenv(api_key)),
                "has_base_url": bool(os.getenv(base_url)),
                "api_key_length": len(os.getenv(api_key, "")) if os.getenv(api_key) else 0
            }
        
        # 系统信息
        system_info = {
            "python_version": os.sys.version,
            "platform": os.name,
            "environment": _system_config["environment"],
            "debug_mode": _system_config["debug_mode"]
        }
        
        return {
            "system": system_info,
            "environment_variables": env_vars,
            "config": _system_config
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取环境信息失败: {str(e)}")

@router.post("/test-model/{model_type}")
async def test_model(model_type: str):
    """测试AI模型连接"""
    try:
        # 验证模型类型
        try:
            ai_model = AIModelType(model_type)
        except ValueError:
            raise HTTPException(status_code=400, detail=f"不支持的模型类型: {model_type}")
        
        # 执行简单的测试请求
        test_result = await geo_service.search_keyword_sources("测试", ai_model)
        
        if test_result.success:
            return {
                "success": True,
                "message": f"模型 {model_type} 连接测试成功",
                "response_time": test_result.raw_analysis.response_time,
                "model_type": model_type
            }
        else:
            return {
                "success": False,
                "message": f"模型 {model_type} 连接测试失败",
                "error": test_result.error,
                "model_type": model_type
            }
        
    except HTTPException:
        raise
    except Exception as e:
        return {
            "success": False,
            "message": f"模型 {model_type} 连接测试失败",
            "error": str(e),
            "model_type": model_type
        }

@router.delete("/cache")
async def clear_all_cache():
    """清空所有缓存"""
    try:
        # 清空模板缓存
        from ....services.geo.template_service import template_service
        template_service.clear_cache()
        
        # 清空其他缓存（如果有的话）
        
        return {
            "message": "所有缓存已清空",
            "success": True
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"清空缓存失败: {str(e)}")

@router.get("/health")
async def health_check():
    """详细健康检查"""
    try:
        health_status = {
            "status": "healthy",
            "timestamp": os.time.time(),
            "checks": {}
        }
        
        # 检查AI模型
        available_models = geo_service.get_available_models()
        health_status["checks"]["ai_models"] = {
            "status": "healthy" if available_models else "warning",
            "count": len(available_models),
            "details": [{"type": m["model_type"], "enabled": m["enabled"]} for m in available_models]
        }
        
        # 检查模板系统
        from ....services.geo.template_service import template_service
        template_list = template_service.get_template_list()
        health_status["checks"]["templates"] = {
            "status": "healthy" if template_list else "error",
            "count": len(template_list)
        }
        
        # 检查静态文件
        static_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))), "static")
        health_status["checks"]["static_files"] = {
            "status": "healthy" if os.path.exists(static_path) else "error",
            "path_exists": os.path.exists(static_path)
        }
        
        # 总体状态
        all_healthy = all(check["status"] == "healthy" for check in health_status["checks"].values())
        health_status["status"] = "healthy" if all_healthy else "degraded"
        
        return health_status
        
    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "timestamp": os.time.time()
        }
