"""
公告管理API路由
"""
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Dict, Any
from datetime import datetime

from app.database import get_db
from app.dependencies import check_permission
from app.services.announcement_service import AnnouncementService
from app.schemas.announcement import (
    AnnouncementCreateRequest, AnnouncementUpdateRequest, AnnouncementQueryRequest,
    AnnouncementApiResponse
)
from app.exceptions import NotFoundError, ValidationError

router = APIRouter()


@router.post("", response_model=AnnouncementApiResponse, summary="创建公告")
async def create_announcement(
    request: AnnouncementCreateRequest,
    current_user: dict = Depends(check_permission("announcements.create")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    创建公告

    需要权限：announcements.create
    """
    try:
        service = AnnouncementService(db)
        announcement = await service.create_announcement(request, current_user["id"])

        return {
            "success": True,
            "data": announcement,
            "message": "公告创建成功"
        }
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="创建公告失败")


@router.get("", response_model=AnnouncementApiResponse, summary="获取公告列表")
async def get_announcements(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    type: str = Query(None, description="公告类型"),
    status: str = Query(None, description="状态"),
    priority: str = Query(None, description="优先级"),
    target_audience: str = Query(None, description="目标用户"),
    is_pinned: bool = Query(None, description="是否置顶"),
    keyword: str = Query(None, description="关键词搜索"),
    current_user: dict = Depends(check_permission("announcements.view")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    获取公告列表
    
    需要权限：announcements.view
    """
    try:
        # 构建查询请求
        query_request = AnnouncementQueryRequest(
            page=page,
            page_size=page_size,
            type=type,
            status=status,
            priority=priority,
            target_audience=target_audience,
            is_pinned=is_pinned,
            keyword=keyword
        )
        
        service = AnnouncementService(db)
        result = await service.get_announcements(query_request)
        
        return {
            "success": True,
            "data": result,
            "message": "获取公告列表成功"
        }
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="获取公告列表失败")



@router.get("/{announcement_id}", response_model=AnnouncementApiResponse, summary="获取公告详情")
async def get_announcement(
    announcement_id: str,
    current_user: dict = Depends(check_permission("announcements.view")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    获取公告详情
    
    需要权限：announcements.view
    """
    try:
        service = AnnouncementService(db)
        announcement = await service.get_announcement_by_id(announcement_id)
        
        return {
            "success": True,
            "data": announcement,
            "message": "获取公告详情成功"
        }
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="获取公告详情失败")


@router.put("/{announcement_id}", response_model=AnnouncementApiResponse, summary="更新公告")
async def update_announcement(
    announcement_id: str,
    request: AnnouncementUpdateRequest,
    current_user: dict = Depends(check_permission("announcements.update")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    更新公告
    
    需要权限：announcements.update
    """
    try:
        service = AnnouncementService(db)
        announcement = await service.update_announcement(announcement_id, request)
        
        return {
            "success": True,
            "data": announcement,
            "message": "公告更新成功"
        }
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="更新公告失败")


@router.delete("/{announcement_id}", response_model=AnnouncementApiResponse, summary="删除公告")
async def delete_announcement(
    announcement_id: str,
    current_user: dict = Depends(check_permission("announcements.delete")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    删除公告
    
    需要权限：announcements.delete
    """
    try:
        service = AnnouncementService(db)
        await service.delete_announcement(announcement_id)
        
        return {
            "success": True,
            "data": None,
            "message": "公告删除成功"
        }
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="删除公告失败")


# 公共接口（不需要权限）
@router.get("/public/list", response_model=AnnouncementApiResponse, summary="获取公开公告列表")
async def get_public_announcements(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=50, description="每页数量"),
    target_audience: str = Query("all", description="目标用户"),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    获取公开发布的公告列表（无需权限）
    
    只返回已发布且未过期的公告
    """
    try:
        # 构建查询请求，只获取已发布的公告
        query_request = AnnouncementQueryRequest(
            page=page,
            page_size=page_size,
            status="published",
            target_audience=target_audience
        )
        
        service = AnnouncementService(db)
        result = await service.get_announcements(query_request)
        
        # 过滤掉过期的公告
        current_time = datetime.now()
        filtered_items = []
        for item in result.items:
            if not item.expire_time or item.expire_time > current_time:
                filtered_items.append(item)
        
        result.items = filtered_items
        result.pagination['total'] = len(filtered_items)
        
        return {
            "success": True,
            "data": result,
            "message": "获取公告列表成功"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail="获取公告列表失败")
