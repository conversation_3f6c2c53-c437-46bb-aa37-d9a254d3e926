from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from decimal import Decimal
from app.database import get_db
from app.services.channel_service import ChannelService
from app.schemas.channel_category import *
from app.schemas.common import ApiResponse
from app.core.permissions import check_permission
from app.exceptions import ValidationError, NotFoundError
from typing import Dict, Any, Optional

router = APIRouter()

# 通用响应模型
class ProviderServiceApiResponse(ApiResponse):
    data: Optional[ChannelServiceResponse] = None

class ProviderServiceListApiResponse(ApiResponse):
    data: Optional[Dict[str, Any]] = None


@router.get("/my-services", response_model=ProviderServiceListApiResponse, summary="获取我创建的服务列表")
async def get_my_services(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    category_id: Optional[str] = Query(None, description="分类ID筛选"),
    approval_status: Optional[str] = Query(None, description="审核状态筛选"),
    is_active: Optional[bool] = Query(None, description="是否启用筛选"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    sort_by: str = Query("created_at", description="排序字段"),
    sort_order: str = Query("desc", pattern="^(asc|desc)$", description="排序方式"),
    current_user: dict = Depends(check_permission("channels.my_services.view")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    渠道商获取自己创建的服务列表
    
    支持分页、筛选和搜索功能：
    - 可按分类、审核状态、启用状态筛选
    - 支持关键词搜索服务名称、描述和代码
    - 支持多种排序方式
    
    需要权限：channels.my_services.view
    """
    try:
        query = ChannelServiceListQuery(
            page=page,
            size=size,
            category_id=category_id,
            portal_type=None,
            coverage_area=None,
            is_active=is_active,
            is_featured=None,
            min_price=None,
            max_price=None,
            search=search,
            sort_by=sort_by,
            sort_order=sort_order
        )

        channel_service = ChannelService(db)
        result = await channel_service.get_my_created_services(current_user["id"], query, approval_status)

        return {
            "success": True,
            "data": result,
            "message": "获取我的服务列表成功"
        }
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="获取我的服务列表失败")


@router.post("/create-service", response_model=ProviderServiceApiResponse, summary="渠道商创建服务")
async def create_provider_service(
    request: ChannelServiceCreate,
    current_user: dict = Depends(check_permission("channels.my_services.create")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    渠道商创建自己的服务

    创建的服务将自动设置为：
    - service_source = 'provider'
    - approval_status = 'pending' (待审核)
    - provider_id = 当前渠道商ID

    需要权限：channels.my_services.create
    """
    try:
        channel_service = ChannelService(db)
        service = await channel_service.create_provider_service(current_user["id"], request)

        return {
            "success": True,
            "data": service,
            "message": "创建服务成功，等待管理员审核"
        }
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="创建服务失败")


@router.put("/{service_id}", response_model=ProviderServiceApiResponse, summary="更新我的服务")
async def update_my_service(
    service_id: str,
    request: ChannelServiceUpdate,
    current_user: dict = Depends(check_permission("channels.my_services.update")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    渠道商更新自己创建的服务
    
    只能更新自己创建的服务
    如果服务已审核通过，某些字段可能不允许修改
    
    需要权限：channels.my_services.update
    """
    try:
        channel_service = ChannelService(db)
        service = await channel_service.update_my_service(current_user["id"], service_id, request)

        return {
            "success": True,
            "data": service,
            "message": "更新服务成功"
        }
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="更新服务失败")


@router.get("/{service_id}", response_model=ProviderServiceApiResponse, summary="获取我的服务详情")
async def get_my_service_detail(
    service_id: str,
    current_user: dict = Depends(check_permission("channels.my_services.view")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    获取我创建的服务详情
    
    只能查看自己创建的服务
    
    需要权限：channels.my_services.view
    """
    try:
        channel_service = ChannelService(db)
        service = await channel_service.get_my_service_detail(current_user["id"], service_id)

        return {
            "success": True,
            "data": service,
            "message": "获取服务详情成功"
        }
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="获取服务详情失败")


@router.delete("/{service_id}", response_model=ApiResponse, summary="删除我的服务")
async def delete_my_service(
    service_id: str,
    current_user: dict = Depends(check_permission("channels.my_services.delete")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    删除我创建的服务
    
    只能删除自己创建的服务
    已审核通过的服务可能不允许删除
    
    需要权限：channels.my_services.delete
    """
    try:
        channel_service = ChannelService(db)
        await channel_service.delete_my_service(current_user["id"], service_id)

        return {
            "success": True,
            "data": None,
            "message": "删除服务成功"
        }
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="删除服务失败")
