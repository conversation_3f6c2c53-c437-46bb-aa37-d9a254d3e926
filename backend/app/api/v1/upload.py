from fastapi import APIRouter, Depends, HTTPException, status, Query, Path, UploadFile, File, Form
from fastapi.responses import RedirectResponse
from sqlalchemy.ext.asyncio import AsyncSession
from app.database import get_db
from app.services.upload_service import UploadService
from app.schemas.upload import *
from app.exceptions import *
from app.dependencies import get_current_user
from app.core.permissions import check_permission, require_admin_permission
from typing import Dict, Any, List, Optional
from datetime import datetime

router = APIRouter()

@router.post("/file", response_model=UploadApiResponse, summary="文件上传")
async def upload_file(
    file: UploadFile = File(...),
    business_type: Optional[str] = Form(None),
    business_id: Optional[str] = Form(None),
    is_public: bool = Form(False),
    expires_hours: Optional[int] = Form(None),
    current_user: dict = Depends(check_permission("upload.file.create")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    上传单个文件到TOS对象存储

    - **file**: 上传的文件（必填）
    - **business_type**: 业务类型（可选）
      - content: 内容相关文件
      - profile: 用户头像
      - document: 文档文件
      - attachment: 附件
    - **business_id**: 业务ID（可选）
    - **is_public**: 是否公开访问（默认false）
    - **expires_hours**: 过期时间（小时，可选）

    支持的文件类型：
    - **图片**: jpg, jpeg, png, gif, bmp, webp
    - **视频**: mp4, avi, mov, wmv, flv, webm
    - **文档**: pdf, doc, docx, xls, xlsx, ppt, pptx, txt
    - **音频**: mp3, wav, flac, aac, ogg
    - **压缩包**: zip, rar, 7z, tar, gz

    文件大小限制：100MB
    """
    try:
        # 读取文件数据
        file_data = await file.read()

        # 构建请求对象
        request = FileUploadRequest(
            business_type=business_type,
            business_id=business_id,
            is_public=is_public,
            expires_hours=expires_hours
        )

        upload_service = UploadService(db)
        result = await upload_service.upload_file(current_user["id"], file_data, file.filename, request)

        return {
            "success": True,
            "data": result,
            "message": "文件上传成功"
        }
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="文件上传失败")

@router.post("/batch", response_model=UploadApiResponse, summary="批量文件上传")
async def batch_upload_files(
    request: BatchUploadRequest,
    current_user: dict = Depends(check_permission("upload.file.create")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    批量文件上传（预签名URL方式）

    - **files_info**: 文件信息列表（必填，最多20个）
      - filename: 文件名
      - size: 文件大小（字节）
      - mime_type: MIME类型（可选）
    - **business_type**: 业务类型（可选）
    - **business_id**: 业务ID（可选）
    - **is_public**: 是否公开访问（默认false）
    - **expires_hours**: 过期时间（小时，可选）

    返回每个文件的预签名上传URL，客户端需要：
    1. 使用返回的upload_url上传文件
    2. 上传完成后调用确认接口

    适用场景：
    - 大文件上传
    - 多文件并行上传
    - 客户端直传TOS
    """
    try:
        upload_service = UploadService(db)
        result = await upload_service.batch_upload_files(current_user["id"], request)

        return {
            "success": True,
            "data": result,
            "message": "批量上传会话创建成功"
        }
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="批量上传创建失败")

@router.get("/files", response_model=UploadApiResponse, summary="获取文件列表")
async def get_file_list(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    file_type: str = Query(None, description="文件类型筛选"),
    business_type: str = Query(None, description="业务类型筛选"),
    business_id: str = Query(None, description="业务ID筛选"),
    status: str = Query(None, description="状态筛选"),
    start_date: datetime = Query(None, description="开始日期"),
    end_date: datetime = Query(None, description="结束日期"),
    keyword: str = Query(None, description="关键词搜索"),
    sort_by: str = Query("created_at", description="排序字段"),
    sort_order: str = Query("desc", description="排序方式"),
    current_user: dict = Depends(check_permission("upload.file.create")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    获取用户上传的文件列表

    支持以下功能：
    - **分页查询**: 通过page和size参数控制
    - **类型筛选**: 按文件类型筛选
      - image: 图片文件
      - video: 视频文件
      - document: 文档文件
      - audio: 音频文件
      - archive: 压缩文件
      - other: 其他文件
    - **业务筛选**: 按业务类型和业务ID筛选
    - **状态筛选**: 按上传状态筛选
      - uploading: 上传中
      - completed: 上传完成
      - failed: 上传失败
    - **时间筛选**: 按上传时间筛选
    - **关键词搜索**: 搜索文件名
    - **排序**: 支持多字段排序

    返回文件列表、分页信息和统计数据
    """
    try:
        query = FileListQuery(
            page=page,
            size=size,
            file_type=file_type,
            business_type=business_type,
            business_id=business_id,
            status=status,
            start_date=start_date,
            end_date=end_date,
            keyword=keyword,
            sort_by=sort_by,
            sort_order=sort_order
        )

        upload_service = UploadService(db)
        result = await upload_service.get_file_list(query, current_user["id"])

        return {
            "success": True,
            "data": result,
            "message": "获取文件列表成功"
        }
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="获取文件列表失败")

@router.get("/files/{file_id}/download", summary="文件下载")
async def download_file(
    file_id: str = Path(..., description="文件ID"),
    current_user: dict = Depends(check_permission("upload.file.read")),
    db: AsyncSession = Depends(get_db)
):
    """
    下载文件

    根据文件ID生成下载链接或直接重定向到文件下载地址
    - 对于TOS存储的文件，生成预签名下载URL并重定向
    - 对于本地存储的文件，直接返回文件内容
    """
    try:
        upload_service = UploadService(db)
        download_url = await upload_service.get_file_download_url(file_id, current_user["id"])

        # 重定向到下载URL
        return RedirectResponse(url=download_url, status_code=302)
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="文件下载失败")

@router.get("/files/{file_id}/url", response_model=UploadApiResponse, summary="获取文件下载链接")
async def get_file_download_url(
    file_id: str = Path(..., description="文件ID"),
    expires_hours: int = Query(1, ge=1, le=24, description="链接有效期(小时)"),
    current_user: dict = Depends(check_permission("upload.file.read")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    获取文件下载链接

    返回文件的临时下载链接，适用于前端需要获取下载URL的场景
    - **file_id**: 文件ID
    - **expires_hours**: 链接有效期，1-24小时，默认1小时

    返回包含下载URL和过期时间的响应
    """
    try:
        upload_service = UploadService(db)
        result = await upload_service.get_file_download_info(file_id, current_user["id"], expires_hours)

        return {
            "success": True,
            "data": result,
            "message": "获取下载链接成功"
        }
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="获取下载链接失败")
