from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from app.database import get_db
from app.services.agent_service import AgentService
from app.schemas.agent import *
from app.schemas.referral_link import *
from app.exceptions import *
from app.dependencies import get_current_user
from app.core.permissions import check_permission
from app.services.referral_link_service import ReferralLinkService
from typing import Dict, Any
from datetime import datetime

router = APIRouter()
admin_router = APIRouter()
withdraw_router = APIRouter()
referral_links_router = APIRouter()
customer_router = APIRouter()

@router.post("/profile", response_model=AgentApiResponse, summary="普通用户申请代理商角色")
async def create_agent_profile(
    request: AgentProfileRequest,
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    普通用户申请代理商角色，提交代理商信息

    **权限要求**: 仅需要登录认证，任何用户都可以申请代理商角色

    - **agent_name**: 代理商名称（必填）
    - **contact_email**: 联系邮箱（必填）
    - **contact_phone**: 联系电话（可选）
    - **office_address**: 办公地址（可选）
    - **agent_description**: 代理商描述（可选）
    - **service_regions**: 服务区域（可选）
    - **specialties**: 专业领域（可选）
    """
    # 代理商信息创建后需要等待管理员审核，审核通过后才会分配代理商用户角色

    try:
        agent_service = AgentService(db)
        result = await agent_service.create_agent_profile(current_user["id"], request)

        return {
            "success": True,
            "data": result,
            "message": "代理商信息创建成功，等待审核"
        }
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        import logging
        import traceback
        logging.error(f"创建代理商信息失败: {str(e)}")
        logging.error(f"错误堆栈: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"创建代理商信息失败: {str(e)}")

@router.get("/me", response_model=AgentApiResponse, summary="获取代理商信息")
async def get_agent_info(
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    获取当前用户的代理商信息

    普通用户可以查看自己提交的代理商信息和审核状态

    **权限要求**: 仅需要登录认证，用户可以查看自己的代理商信息
    """
    try:

        agent_service = AgentService(db)
        result = await agent_service.get_agent_info(current_user["id"])

        return {
            "success": True,
            "data": result,
            "message": "获取代理商信息成功"
        }
    except NotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="获取代理商信息失败")



@admin_router.get("/list", response_model=AgentApiResponse, summary="获取代理商列表")
async def get_agent_list(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    verification_status: str = Query(None, description="审核状态筛选"),
    agent_level: str = Query(None, description="代理商等级筛选"),
    search: str = Query(None, description="搜索关键词"),
    sort_by: str = Query("created_at", description="排序字段"),
    sort_order: str = Query("desc", description="排序方式"),
    current_user: dict = Depends(check_permission("agents.list.view")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    管理员获取代理商列表

    支持以下功能：
    - **分页查询**: 通过page和size参数控制
    - **状态筛选**: 按审核状态筛选（pending/verified/rejected）
    - **等级筛选**: 按代理商等级筛选（bronze/silver/gold/platinum/diamond）
    - **关键词搜索**: 搜索代理商名称、代理商代码、联系邮箱
    - **排序**: 支持多字段排序

    返回代理商列表、分页信息
    """
    try:
        from app.schemas.agent import AgentListQuery

        query = AgentListQuery(
            page=page,
            size=size,
            verification_status=verification_status,
            agent_level=agent_level,
            search=search,
            sort_by=sort_by,
            sort_order=sort_order
        )

        agent_service = AgentService(db)
        result = await agent_service.get_agent_list(query)

        return {
            "success": True,
            "data": result,
            "message": "获取代理商列表成功"
        }
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="获取代理商列表失败")

@admin_router.get("/pending", response_model=AgentApiResponse, summary="获取待审批代理商列表")
async def get_pending_agents(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    search: str = Query(None, description="搜索关键词"),
    sort_by: str = Query("created_at", description="排序字段"),
    sort_order: str = Query("desc", description="排序方式"),
    current_admin: dict = Depends(check_permission("agents.list.view")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    管理员获取待审批代理商列表

    支持以下功能：
    - **分页查询**: 通过page和size参数控制
    - **关键词搜索**: 搜索代理商名称、代理商代码、联系邮箱
    - **排序**: 支持多字段排序

    只返回审核状态为pending的代理商
    """
    try:
        from app.schemas.agent import AgentListQuery

        query = AgentListQuery(
            page=page,
            size=size,
            verification_status="pending",  # 固定为待审批状态
            search=search,
            sort_by=sort_by,
            sort_order=sort_order
        )

        agent_service = AgentService(db)
        result = await agent_service.get_agent_list(query)

        return {
            "success": True,
            "data": result,
            "message": "获取待审批代理商列表成功"
        }
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="获取待审批代理商列表失败")

@admin_router.put("/{agent_id}/verification", response_model=AgentApiResponse, summary="代理商审核")
async def verify_agent(
    agent_id: str,
    request: AgentVerificationRequest,
    current_admin: dict = Depends(check_permission("agents.verification")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    管理员审核代理商资质

    - **status**: 审核结果（verified/rejected）
    - **note**: 审核备注（必填，至少5个字符）
    - **notify_user**: 是否通知用户（默认true）

    审核通过后，代理商用户可以正常使用平台功能
    审核拒绝后，代理商用户需要重新提交资料
    """
    try:
        agent_service = AgentService(db)
        result = await agent_service.verify_agent(agent_id, request, current_admin["id"])

        return {
            "success": True,
            "data": result,
            "message": f"代理商审核完成，状态已更新为{request.status}"
        }
    except (ValidationError, NotFoundError) as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="代理商审核失败")

@router.put("/me", response_model=AgentApiResponse, summary="更新代理商信息")
async def update_agent_info(
    request: AgentUpdateRequest,
    current_user: dict = Depends(check_permission("agents.profile.edit")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    更新代理商信息

    只允许更新部分字段：
    - **contact_phone**: 联系电话
    - **contact_email**: 联系邮箱
    - **office_address**: 办公地址
    - **agent_description**: 代理商描述
    - **service_regions**: 服务区域
    - **specialties**: 专业领域

    注意：代理商名称、等级等关键信息不允许修改

    **权限要求**: 需要 `agents.profile.edit` 权限，且必须是代理商用户或管理员角色
    """
    try:
        # 检查用户角色是否为代理商用户或管理员
        user_roles = current_user.get("roles", [])
        is_admin = any(role in ["admin", "super_admin"] for role in user_roles)
        if "agent_user" not in user_roles and not is_admin:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="只有代理商用户或管理员可以更新代理商信息"
            )

        agent_service = AgentService(db)
        result = await agent_service.update_agent_info(current_user["id"], request)

        return {
            "success": True,
            "data": result,
            "message": "代理商信息更新成功"
        }
    except (ValidationError, NotFoundError) as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        import logging
        logging.error(f"更新代理商信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新代理商信息失败: {str(e)}")

@customer_router.get("/customers", response_model=AgentApiResponse, summary="获取代理商客户列表")
async def get_agent_customers(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    status: str = Query(None, description="客户状态筛选"),
    level: str = Query(None, description="客户等级筛选"),
    source: str = Query(None, description="推广来源筛选"),
    search: str = Query(None, description="搜索关键词"),
    start_date: datetime = Query(None, description="开始日期"),
    end_date: datetime = Query(None, description="结束日期"),
    sort_by: str = Query("register_date", description="排序字段"),
    sort_order: str = Query("desc", description="排序方式"),
    current_user: dict = Depends(check_permission("agents.customers.view")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    获取代理商客户列表（合并推荐用户和客户功能）

    支持以下功能：
    - **分页查询**: 通过page和size参数控制
    - **状态筛选**: 按客户状态筛选（active/trial/inactive）
    - **等级筛选**: 按客户等级筛选（VIP/活跃/试用/未激活）
    - **来源筛选**: 按推广来源筛选
    - **关键词搜索**: 搜索姓名、邮箱、公司名称
    - **时间筛选**: 按注册时间筛选
    - **排序**: 支持多字段排序

    返回客户列表、分页信息和统计数据
    """
    try:
        # 检查用户角色是否为代理商用户
        user_roles = current_user.get("roles", [])
        if "agent_user" not in user_roles:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="只有代理商用户可以查看客户列表"
            )

        referral_service = ReferralLinkService(db)
        result = await referral_service.get_agent_customers(
            agent_user_id=current_user["id"],
            page=page,
            size=size,
            status=status,
            level=level,
            source=source,
            search=search,
            start_date=start_date,
            end_date=end_date,
            sort_by=sort_by,
            sort_order=sort_order
        )

        return {
            "success": True,
            "data": result,
            "message": "获取客户列表成功"
        }

    except (ValidationError, NotFoundError) as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        import logging
        import traceback
        logging.error(f"获取客户列表失败: {str(e)}")
        logging.error(f"错误堆栈: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"获取客户列表失败: {str(e)}")

@withdraw_router.get("/commissions", response_model=AgentApiResponse, summary="获取佣金记录")
async def get_commission_list(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    status: str = Query(None, description="佣金状态筛选"),
    order_type: str = Query(None, description="订单类型筛选"),
    start_date: datetime = Query(None, description="开始日期"),
    end_date: datetime = Query(None, description="结束日期"),
    current_user: dict = Depends(check_permission("agents.commissions.view")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    获取代理商佣金记录列表

    支持以下功能：
    - **分页查询**: 通过page和size参数控制
    - **状态筛选**: 按佣金状态筛选（pending/settled/cancelled）
    - **类型筛选**: 按订单类型筛选（subscription/content_service）
    - **时间筛选**: 按佣金产生时间筛选

    返回佣金记录列表、分页信息和汇总数据
    """
    try:
        # 检查用户角色是否为代理商用户
        user_roles = current_user.get("roles", [])
        if "agent_user" not in user_roles:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="只有代理商用户可以查看佣金记录"
            )

        query = CommissionListQuery(
            page=page,
            size=size,
            status=status,
            order_type=order_type,
            start_date=start_date,
            end_date=end_date
        )

        agent_service = AgentService(db)
        result = await agent_service.get_commission_list(current_user["id"], query)

        return {
            "success": True,
            "data": result,
            "message": "获取佣金记录成功"
        }
    except (ValidationError, NotFoundError) as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="获取佣金记录失败")

@withdraw_router.post("/settlements", response_model=AgentApiResponse, summary="申请佣金结算")
async def apply_commission_settlement(
    request: CommissionSettlementRequest,
    current_user: dict = Depends(check_permission("agents.settlements.apply")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    申请佣金结算

    - **settlement_amount**: 结算金额（必填，最低100元）
    - **bank_account**: 收款账户信息（必填）
      - account_name: 账户名称
      - bank_name: 银行名称
      - account_number: 账户号码
    - **note**: 备注信息（可选）

    注意：结算金额不能超过当前待结算佣金总额
    """
    try:
        # 检查用户角色是否为代理商用户
        user_roles = current_user.get("roles", [])
        if "agent_user" not in user_roles:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="只有代理商用户可以申请佣金结算"
            )

        agent_service = AgentService(db)
        result = await agent_service.apply_commission_settlement(current_user["id"], request)

        return {
            "success": True,
            "data": result,
            "message": "佣金结算申请提交成功，预计5个工作日内处理"
        }
    except (ValidationError, NotFoundError) as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="申请佣金结算失败")

@withdraw_router.get("/settlements", response_model=AgentApiResponse, summary="获取佣金结算记录")
async def get_commission_settlements(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    status: str = Query(None, description="结算状态筛选"),
    current_user: dict = Depends(check_permission("agents.settlements.view")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    获取代理商佣金结算记录

    支持以下功能：
    - **分页查询**: 通过page和size参数控制
    - **状态筛选**: 按结算状态筛选（pending/approved/rejected/completed）

    返回结算记录列表、分页信息和汇总数据
    """
    try:
        # 检查用户角色是否为代理商用户
        user_roles = current_user.get("roles", [])
        if "agent_user" not in user_roles:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="只有代理商用户可以查看佣金结算记录"
            )

        agent_service = AgentService(db)
        result = await agent_service.get_commission_settlements(current_user["id"], page, size, status)

        return {
            "success": True,
            "data": result,
            "message": "获取佣金结算记录成功"
        }
    except (ValidationError, NotFoundError) as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="获取佣金结算记录失败")

@referral_links_router.post("/referral-links", response_model=ReferralLinkApiResponse, summary="生成推广链接")
async def create_referral_link(
    request: ReferralLinkCreateRequest,
    current_user: dict = Depends(check_permission("agents.referral_links.create")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    代理商生成推广链接

    - **link_name**: 链接名称（必填，2-100字符）
    - **target_page**: 目标页面（必填）
      - register: 注册页面
      - home: 首页
      - pricing: 价格页面
      - services: 服务页面
    - **campaign_name**: 活动名称（可选）
    - **description**: 链接描述（可选）

    返回生成的推广链接和统计信息
    """
    try:
        # 检查用户角色是否为代理商用户
        user_roles = current_user.get("roles", [])
        if "agent_user" not in user_roles:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="只有代理商用户可以生成推广链接"
            )

        referral_service = ReferralLinkService(db)
        result = await referral_service.create_referral_link(current_user["id"], request)

        return {
            "success": True,
            "data": result,
            "message": "推广链接生成成功"
        }
    except (ValidationError, NotFoundError) as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        import logging
        import traceback
        logging.error(f"生成推广链接失败: {str(e)}")
        logging.error(f"错误堆栈: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"生成推广链接失败: {str(e)}")

@referral_links_router.get("/referral-links", response_model=ReferralLinkApiResponse, summary="获取推广链接列表")
async def get_referral_links(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    current_user: dict = Depends(check_permission("agents.referral_links.view")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    获取代理商推广链接列表

    支持以下功能：
    - **分页查询**: 通过page和size参数控制
    - **统计信息**: 包含点击量、注册量、转化量、佣金等统计数据

    返回推广链接列表、分页信息和汇总统计
    """
    try:
        # 检查用户角色是否为代理商用户
        user_roles = current_user.get("roles", [])
        if "agent_user" not in user_roles:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="只有代理商用户可以查看推广链接"
            )

        referral_service = ReferralLinkService(db)
        result = await referral_service.get_referral_links(current_user["id"], page, size)

        return {
            "success": True,
            "data": result,
            "message": "获取推广链接列表成功"
        }
    except (ValidationError, NotFoundError) as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        import logging
        import traceback
        logging.error(f"获取推广链接列表失败: {str(e)}")
        logging.error(f"错误堆栈: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"获取推广链接列表失败: {str(e)}")

@referral_links_router.put("/referral-links/{link_id}", response_model=ReferralLinkApiResponse, summary="修改推广链接")
async def update_referral_link(
    link_id: str,
    request: ReferralLinkUpdateRequest,
    current_user: dict = Depends(check_permission("agents.referral_links.update")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    修改推广链接

    - **link_id**: 推广链接ID（必填）
    - **link_name**: 链接名称（可选，2-100字符）
    - **target_page**: 目标页面（可选）
      - register: 注册页面
      - home: 首页
      - pricing: 价格页面
      - services: 服务页面
    - **campaign_name**: 活动名称（可选）
    - **description**: 链接描述（可选）
    - **is_active**: 是否激活（可选）

    返回修改后的推广链接信息
    """
    try:
        # 检查用户角色是否为代理商用户
        user_roles = current_user.get("roles", [])
        if "agent_user" not in user_roles:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="只有代理商用户可以修改推广链接"
            )

        referral_service = ReferralLinkService(db)
        result = await referral_service.update_referral_link(current_user["id"], link_id, request)

        return {
            "success": True,
            "data": result,
            "message": "推广链接修改成功"
        }
    except (ValidationError, NotFoundError) as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        import logging
        import traceback
        logging.error(f"修改推广链接失败: {str(e)}")
        logging.error(f"错误堆栈: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"修改推广链接失败: {str(e)}")

@referral_links_router.patch("/referral-links/{link_id}/toggle-status", response_model=ReferralLinkApiResponse, summary="切换推广链接状态")
async def toggle_referral_link_status(
    link_id: str,
    current_user: dict = Depends(check_permission("agents.referral_links.update")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    切换推广链接状态（启用/停用）

    - **link_id**: 推广链接ID（必填）

    返回切换状态后的推广链接信息
    """
    try:
        # 检查用户角色是否为代理商用户
        user_roles = current_user.get("roles", [])
        if "agent_user" not in user_roles:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="只有代理商用户可以切换推广链接状态"
            )

        referral_service = ReferralLinkService(db)
        result = await referral_service.toggle_referral_link_status(current_user["id"], link_id)

        status_text = "启用" if result.get("is_active") else "停用"
        return {
            "success": True,
            "data": result,
            "message": f"推广链接已{status_text}"
        }
    except (ValidationError, NotFoundError) as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        import logging
        import traceback
        logging.error(f"切换推广链接状态失败: {str(e)}")
        logging.error(f"错误堆栈: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"切换推广链接状态失败: {str(e)}")


@referral_links_router.post("/referral-links/track-click", summary="跟踪推广链接点击")
async def track_referral_click(
    request: TrackReferralClickRequest,
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    跟踪推广链接点击

    - **referral_code**: 推荐码（必填）
    - **target_page**: 目标页面（必填）
    - **campaign**: 活动名称（可选）
    - **user_agent**: 用户代理（可选）
    - **ip_address**: IP地址（可选）

    返回跟踪结果
    """
    try:
        referral_service = ReferralLinkService(db)
        result = await referral_service.track_referral_click(
            referral_code=request.referral_code,
            target_page=request.target_page,
            campaign=request.campaign,
            user_agent=request.user_agent,
            ip_address=request.ip_address
        )

        return {
            "success": True,
            "data": result,
            "message": "推广链接点击跟踪成功"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"推广链接点击跟踪失败: {str(e)}")


@referral_links_router.delete("/referral-links/{link_id}", response_model=ReferralLinkApiResponse, summary="删除推广链接")
async def delete_referral_link(
    link_id: str,
    current_user: dict = Depends(check_permission("agents.referral_links.delete")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    删除推广链接

    - **link_id**: 推广链接ID（必填）

    返回删除的推广链接信息
    """
    try:
        # 检查用户角色是否为代理商用户
        user_roles = current_user.get("roles", [])
        if "agent_user" not in user_roles:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="只有代理商用户可以删除推广链接"
            )

        referral_service = ReferralLinkService(db)
        result = await referral_service.delete_referral_link(current_user["id"], link_id)

        return {
            "success": True,
            "data": result,
            "message": "推广链接删除成功"
        }
    except (ValidationError, NotFoundError) as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        import logging
        import traceback
        logging.error(f"删除推广链接失败: {str(e)}")
        logging.error(f"错误堆栈: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"删除推广链接失败: {str(e)}")






@customer_router.get("/customers/{customer_id}", response_model=AgentApiResponse, summary="获取客户详情")
async def get_customer_detail(
    customer_id: str,
    current_user: dict = Depends(check_permission("agents.customers.detail")),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    获取指定客户的详细信息

    需要代理商用户权限才能访问此接口。
    只能查看属于当前代理商推荐的客户详情。

    - **customer_id**: 客户ID（必填）

    **权限要求**: 需要 `agents.customers.detail` 权限，且必须是代理商用户角色
    """
    try:
        # 检查用户角色是否为代理商用户
        user_roles = current_user.get("roles", [])
        if "agent_user" not in user_roles:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="只有代理商用户可以查看客户详情"
            )

        referral_service = ReferralLinkService(db)
        result = await referral_service.get_customer_detail(
            agent_user_id=current_user["id"],
            customer_id=customer_id
        )

        return {
            "success": True,
            "data": result,
            "message": "获取客户详情成功"
        }

    except (ValidationError, NotFoundError) as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        import logging
        import traceback
        logging.error(f"获取客户详情失败: {str(e)}")
        logging.error(f"错误堆栈: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"获取客户详情失败: {str(e)}")
