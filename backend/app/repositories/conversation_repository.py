from typing import List, Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete, desc, and_
from sqlalchemy.orm import selectinload
from datetime import datetime
import uuid

from app.models.knowledge import AIConversation, AIConversationMessage, MessageType


class ConversationRepository:
    """对话数据访问层"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_conversation(self, conversation_data: Dict[str, Any]) -> AIConversation:
        """创建新对话"""
        conversation = AIConversation(**conversation_data)
        self.db.add(conversation)
        await self.db.commit()
        await self.db.refresh(conversation)
        return conversation
    
    async def get_conversation_by_id(self, conversation_id: str, user_id: str = None) -> Optional[AIConversation]:
        """根据ID获取对话（可选用户权限验证）"""
        query = select(AIConversation).where(AIConversation.id == conversation_id)
        
        if user_id:
            query = query.where(AIConversation.user_id == user_id)
        
        result = await self.db.execute(query)
        return result.scalar_one_or_none()
    
    async def get_user_conversations(self, user_id: str, business_type: Optional[str] = None, limit: int = 20, offset: int = 0) -> List[AIConversation]:
        """获取用户的对话列表"""
        query = (
            select(AIConversation)
            .where(AIConversation.user_id == user_id)
        )

        # 如果指定了业务类型，添加筛选条件
        if business_type:
            # 假设我们在conversation表中有business_type字段，或者通过其他方式标识
            # 这里先简单处理，可以根据实际数据模型调整
            query = query.where(AIConversation.template_parameters.contains(f'"business_type":"{business_type}"'))

        query = query.order_by(desc(AIConversation.updated_at)).limit(limit).offset(offset)
        
        result = await self.db.execute(query)
        return list(result.scalars().all())
    
    async def update_conversation(self, conversation_id: str, update_data: Dict[str, Any]) -> bool:
        """更新对话信息"""
        update_data['updated_at'] = datetime.utcnow()
        
        result = await self.db.execute(
            update(AIConversation)
            .where(AIConversation.id == conversation_id)
            .values(**update_data)
        )
        
        await self.db.commit()
        return result.rowcount > 0
    
    async def delete_conversation(self, conversation_id: str, user_id: str = None) -> bool:
        """删除对话"""
        query = delete(AIConversation).where(AIConversation.id == conversation_id)
        
        if user_id:
            query = query.where(AIConversation.user_id == user_id)
        
        result = await self.db.execute(query)
        await self.db.commit()
        return result.rowcount > 0
    
    async def add_message(self, message_data: Dict[str, Any]) -> AIConversationMessage:
        """添加消息到对话"""
        message = AIConversationMessage(**message_data)
        self.db.add(message)
        
        # 更新对话统计
        await self.db.execute(
            update(AIConversation)
            .where(AIConversation.id == message_data['conversation_id'])
            .values(
                message_count=AIConversation.message_count + 1,
                updated_at=datetime.utcnow()
            )
        )
        
        await self.db.commit()
        await self.db.refresh(message)
        return message
    
    async def get_conversation_messages(self, conversation_id: str, limit: int = 50, offset: int = 0) -> List[AIConversationMessage]:
        """获取对话消息历史"""
        query = (
            select(AIConversationMessage)
            .where(AIConversationMessage.conversation_id == conversation_id)
            .order_by(AIConversationMessage.created_at.asc())
            .limit(limit)
            .offset(offset)
        )
        
        result = await self.db.execute(query)
        return list(result.scalars().all())
    
    async def get_recent_messages(self, conversation_id: str, limit: int = 10) -> List[AIConversationMessage]:
        """获取最近的消息"""
        query = (
            select(AIConversationMessage)
            .where(AIConversationMessage.conversation_id == conversation_id)
            .order_by(desc(AIConversationMessage.created_at))
            .limit(limit)
        )
        
        result = await self.db.execute(query)
        messages = list(result.scalars().all())
        return list(reversed(messages))  # 按时间正序返回
    
    async def update_conversation_summary(self, conversation_id: str, summary: str) -> bool:
        """更新对话上下文摘要"""
        result = await self.db.execute(
            update(AIConversation)
            .where(AIConversation.id == conversation_id)
            .values(
                context_summary=summary,
                updated_at=datetime.utcnow()
            )
        )
        
        await self.db.commit()
        return result.rowcount > 0
    
    async def get_conversation_with_messages(self, conversation_id: str, user_id: str = None) -> Optional[AIConversation]:
        """获取对话及其消息（预加载）"""
        query = (
            select(AIConversation)
            .options(selectinload(AIConversation.messages))
            .where(AIConversation.id == conversation_id)
        )
        
        if user_id:
            query = query.where(AIConversation.user_id == user_id)
        
        result = await self.db.execute(query)
        return result.scalar_one_or_none()
    
    async def count_user_conversations(self, user_id: str) -> int:
        """统计用户对话数量"""
        from sqlalchemy import func
        
        result = await self.db.execute(
            select(func.count(AIConversation.id))
            .where(AIConversation.user_id == user_id)
        )
        
        return result.scalar() or 0
    
    async def get_conversation_statistics(self, conversation_id: str) -> Dict[str, Any]:
        """获取对话统计信息"""
        from sqlalchemy import func
        
        # 获取消息统计
        message_stats = await self.db.execute(
            select(
                func.count(AIConversationMessage.id).label('total_messages'),
                func.count(AIConversationMessage.id).filter(
                    AIConversationMessage.message_type == MessageType.USER
                ).label('user_messages'),
                func.count(AIConversationMessage.id).filter(
                    AIConversationMessage.message_type == MessageType.ASSISTANT
                ).label('assistant_messages')
            )
            .where(AIConversationMessage.conversation_id == conversation_id)
        )
        
        stats = message_stats.first()
        
        return {
            'total_messages': stats.total_messages or 0,
            'user_messages': stats.user_messages or 0,
            'assistant_messages': stats.assistant_messages or 0
        }
