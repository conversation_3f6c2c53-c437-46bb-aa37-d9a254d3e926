from typing import List, Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete, desc, and_, func
from sqlalchemy.orm import selectinload
from datetime import datetime
import uuid

from app.models.knowledge import UserKnowledgeBase, KnowledgeDocument, DocumentChunk, EmbeddingStatus


class KnowledgeRepository:
    """知识库数据访问层"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    # ========== 知识库管理 ==========
    
    async def create_knowledge_base(self, kb_data: Dict[str, Any]) -> UserKnowledgeBase:
        """创建知识库"""
        kb = UserKnowledgeBase(**kb_data)
        self.db.add(kb)
        await self.db.commit()
        await self.db.refresh(kb)
        return kb
    
    async def get_knowledge_base_by_id(self, kb_id: str, user_id: str = None) -> Optional[UserKnowledgeBase]:
        """根据ID获取知识库"""
        query = select(UserKnowledgeBase).where(UserKnowledgeBase.id == kb_id)
        
        if user_id:
            query = query.where(UserKnowledgeBase.user_id == user_id)
        
        result = await self.db.execute(query)
        return result.scalar_one_or_none()
    
    async def get_user_knowledge_bases(self, user_id: str, limit: int = 50, offset: int = 0) -> List[UserKnowledgeBase]:
        """获取用户的知识库列表"""
        query = (
            select(UserKnowledgeBase)
            .where(UserKnowledgeBase.user_id == user_id)
            .order_by(desc(UserKnowledgeBase.updated_at))
            .limit(limit)
            .offset(offset)
        )
        
        result = await self.db.execute(query)
        return list(result.scalars().all())

    async def get_user_knowledge_bases_count(self, user_id: str) -> int:
        """获取用户知识库总数"""
        from sqlalchemy import func
        query = (
            select(func.count(UserKnowledgeBase.id))
            .where(UserKnowledgeBase.user_id == user_id)
        )

        result = await self.db.execute(query)
        return result.scalar() or 0

    async def update_knowledge_base(self, kb_id: str, update_data: Dict[str, Any]) -> bool:
        """更新知识库信息"""
        update_data['updated_at'] = datetime.utcnow()
        
        result = await self.db.execute(
            update(UserKnowledgeBase)
            .where(UserKnowledgeBase.id == kb_id)
            .values(**update_data)
        )
        
        await self.db.commit()
        return result.rowcount > 0
    
    async def delete_knowledge_base(self, kb_id: str, user_id: str = None) -> bool:
        """删除知识库（级联删除相关数据）"""
        try:
            # 1. 先删除文档分块
            chunk_query = delete(DocumentChunk).where(
                DocumentChunk.document_id.in_(
                    select(KnowledgeDocument.id).where(
                        KnowledgeDocument.knowledge_base_id == kb_id
                    )
                )
            )
            await self.db.execute(chunk_query)

            # 2. 删除文档
            doc_query = delete(KnowledgeDocument).where(
                KnowledgeDocument.knowledge_base_id == kb_id
            )
            await self.db.execute(doc_query)

            # 3. 删除知识库
            kb_query = delete(UserKnowledgeBase).where(UserKnowledgeBase.id == kb_id)
            if user_id:
                kb_query = kb_query.where(UserKnowledgeBase.user_id == user_id)

            result = await self.db.execute(kb_query)
            await self.db.commit()
            return result.rowcount > 0

        except Exception as e:
            await self.db.rollback()
            raise e
    
    # ========== 文档管理 ==========
    
    async def create_document(self, doc_data: Dict[str, Any]) -> KnowledgeDocument:
        """创建文档"""
        document = KnowledgeDocument(**doc_data)
        self.db.add(document)
        
        # 更新知识库文档计数
        await self.db.execute(
            update(UserKnowledgeBase)
            .where(UserKnowledgeBase.id == doc_data['knowledge_base_id'])
            .values(
                document_count=UserKnowledgeBase.document_count + 1,
                updated_at=datetime.utcnow()
            )
        )
        
        await self.db.commit()
        await self.db.refresh(document)
        return document
    
    async def get_document_by_id(self, doc_id: str) -> Optional[KnowledgeDocument]:
        """根据ID获取文档"""
        result = await self.db.execute(
            select(KnowledgeDocument).where(KnowledgeDocument.id == doc_id)
        )
        return result.scalar_one_or_none()
    
    async def get_knowledge_base_documents(self, kb_id: str, limit: int = 100, offset: int = 0) -> List[KnowledgeDocument]:
        """获取知识库的文档列表"""
        query = (
            select(KnowledgeDocument)
            .where(KnowledgeDocument.knowledge_base_id == kb_id)
            .order_by(desc(KnowledgeDocument.created_at))
            .limit(limit)
            .offset(offset)
        )
        
        result = await self.db.execute(query)
        return list(result.scalars().all())
    
    async def update_document(self, doc_id: str, update_data: Dict[str, Any]) -> bool:
        """更新文档信息"""
        update_data['updated_at'] = datetime.utcnow()
        
        result = await self.db.execute(
            update(KnowledgeDocument)
            .where(KnowledgeDocument.id == doc_id)
            .values(**update_data)
        )
        
        await self.db.commit()
        return result.rowcount > 0
    
    async def delete_document(self, doc_id: str) -> bool:
        """删除文档"""
        # 先获取文档信息
        document = await self.get_document_by_id(doc_id)
        if not document:
            return False
        
        # 删除文档
        result = await self.db.execute(
            delete(KnowledgeDocument).where(KnowledgeDocument.id == doc_id)
        )
        
        # 更新知识库文档计数
        if result.rowcount > 0:
            await self.db.execute(
                update(UserKnowledgeBase)
                .where(UserKnowledgeBase.id == document.knowledge_base_id)
                .values(
                    document_count=UserKnowledgeBase.document_count - 1,
                    updated_at=datetime.utcnow()
                )
            )
        
        await self.db.commit()
        return result.rowcount > 0
    
    async def get_documents_by_status(self, status: EmbeddingStatus, limit: int = 10) -> List[KnowledgeDocument]:
        """根据状态获取文档列表"""
        query = (
            select(KnowledgeDocument)
            .where(KnowledgeDocument.embedding_status == status)
            .order_by(KnowledgeDocument.created_at.asc())
            .limit(limit)
        )
        
        result = await self.db.execute(query)
        return list(result.scalars().all())
    
    # ========== 文档分块管理 ==========
    
    async def create_document_chunk(self, chunk_data: Dict[str, Any]) -> DocumentChunk:
        """创建文档分块"""
        chunk = DocumentChunk(**chunk_data)
        self.db.add(chunk)
        
        # 更新文档分块计数
        await self.db.execute(
            update(KnowledgeDocument)
            .where(KnowledgeDocument.id == chunk_data['document_id'])
            .values(
                chunk_count=KnowledgeDocument.chunk_count + 1,
                updated_at=datetime.utcnow()
            )
        )
        
        await self.db.commit()
        await self.db.refresh(chunk)
        return chunk
    
    async def get_document_chunks(self, doc_id: str) -> List[DocumentChunk]:
        """获取文档的所有分块"""
        query = (
            select(DocumentChunk)
            .where(DocumentChunk.document_id == doc_id)
            .order_by(DocumentChunk.chunk_index.asc())
        )
        
        result = await self.db.execute(query)
        return list(result.scalars().all())
    
    async def update_chunk(self, chunk_id: str, update_data: Dict[str, Any]) -> bool:
        """更新分块信息"""
        result = await self.db.execute(
            update(DocumentChunk)
            .where(DocumentChunk.id == chunk_id)
            .values(**update_data)
        )
        
        await self.db.commit()
        return result.rowcount > 0
    
    async def delete_document_chunks(self, doc_id: str) -> int:
        """删除文档的所有分块"""
        result = await self.db.execute(
            delete(DocumentChunk).where(DocumentChunk.document_id == doc_id)
        )
        
        await self.db.commit()
        return result.rowcount
    
    # ========== 统计查询 ==========
    
    async def get_knowledge_base_statistics(self, kb_id: str) -> Dict[str, Any]:
        """获取知识库统计信息"""
        # 文档统计
        doc_stats = await self.db.execute(
            select(
                func.count(KnowledgeDocument.id).label('total_documents'),
                func.count(KnowledgeDocument.id).filter(
                    KnowledgeDocument.embedding_status == EmbeddingStatus.COMPLETED
                ).label('completed_documents'),
                func.count(KnowledgeDocument.id).filter(
                    KnowledgeDocument.embedding_status == EmbeddingStatus.PROCESSING
                ).label('processing_documents'),
                func.count(KnowledgeDocument.id).filter(
                    KnowledgeDocument.embedding_status == EmbeddingStatus.FAILED
                ).label('failed_documents'),
                func.sum(KnowledgeDocument.chunk_count).label('total_chunks')
            )
            .where(KnowledgeDocument.knowledge_base_id == kb_id)
        )
        
        stats = doc_stats.first()
        
        return {
            'total_documents': stats.total_documents or 0,
            'completed_documents': stats.completed_documents or 0,
            'processing_documents': stats.processing_documents or 0,
            'failed_documents': stats.failed_documents or 0,
            'total_chunks': stats.total_chunks or 0
        }
    
    async def get_user_knowledge_base_count(self, user_id: str) -> int:
        """获取用户知识库数量"""
        result = await self.db.execute(
            select(func.count(UserKnowledgeBase.id))
            .where(UserKnowledgeBase.user_id == user_id)
        )
        
        return result.scalar() or 0

    async def get_documents_by_kb_id(self, kb_id: str, page: int = 1, size: int = 20) -> List[KnowledgeDocument]:
        """获取知识库的文档列表（分页）"""
        offset = (page - 1) * size

        result = await self.db.execute(
            select(KnowledgeDocument)
            .where(KnowledgeDocument.knowledge_base_id == kb_id)
            .order_by(KnowledgeDocument.created_at.desc())
            .offset(offset)
            .limit(size)
        )

        return result.scalars().all()

    async def get_documents_count_by_kb_id(self, kb_id: str) -> int:
        """获取知识库的文档总数"""
        result = await self.db.execute(
            select(func.count(KnowledgeDocument.id))
            .where(KnowledgeDocument.knowledge_base_id == kb_id)
        )

        return result.scalar() or 0

    async def get_document_by_id(self, doc_id: str) -> KnowledgeDocument:
        """根据ID获取文档"""
        result = await self.db.execute(
            select(KnowledgeDocument)
            .where(KnowledgeDocument.id == doc_id)
        )

        return result.scalar_one_or_none()
