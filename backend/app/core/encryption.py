"""
Encryption utilities for platform credentials
平台凭证加密工具
"""

import json
import base64
import os
from typing import Dict, Any
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC


class CredentialEncryption:
    """凭证加密/解密工具类"""
    
    def __init__(self):
        # 从环境变量获取密钥，如果没有则生成一个
        encryption_key = os.getenv("PLATFORM_ENCRYPTION_KEY")
        if not encryption_key:
            # 在生产环境中应该从环境变量读取
            # 这里为了测试生成一个默认密钥
            encryption_key = self._generate_key("default-salt-for-testing")
        
        self.cipher = Fernet(encryption_key.encode() if isinstance(encryption_key, str) else encryption_key)
    
    def _generate_key(self, salt: str) -> bytes:
        """生成加密密钥"""
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt.encode(),
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(b"platform-integration-secret"))
        return key
    
    def encrypt(self, data: Dict[str, Any]) -> str:
        """
        加密凭证数据
        
        Args:
            data: 要加密的字典数据
            
        Returns:
            加密后的base64字符串
        """
        try:
            # 将字典转换为JSON字符串
            json_str = json.dumps(data, ensure_ascii=False)
            # 加密
            encrypted = self.cipher.encrypt(json_str.encode())
            # 转换为base64字符串便于存储
            return base64.b64encode(encrypted).decode()
        except Exception as e:
            raise ValueError(f"Encryption failed: {str(e)}")
    
    def decrypt(self, encrypted_data: str) -> Dict[str, Any]:
        """
        解密凭证数据
        
        Args:
            encrypted_data: 加密的base64字符串
            
        Returns:
            解密后的字典数据
        """
        try:
            # 从base64解码
            decoded = base64.b64decode(encrypted_data.encode())
            # 解密
            decrypted = self.cipher.decrypt(decoded)
            # 转换回字典
            return json.loads(decrypted.decode())
        except Exception as e:
            raise ValueError(f"Decryption failed: {str(e)}")
    
    def encrypt_token(self, token: str) -> str:
        """
        加密令牌
        
        Args:
            token: 要加密的令牌字符串
            
        Returns:
            加密后的字符串
        """
        try:
            encrypted = self.cipher.encrypt(token.encode())
            return base64.b64encode(encrypted).decode()
        except Exception as e:
            raise ValueError(f"Token encryption failed: {str(e)}")
    
    def decrypt_token(self, encrypted_token: str) -> str:
        """
        解密令牌
        
        Args:
            encrypted_token: 加密的令牌字符串
            
        Returns:
            解密后的令牌
        """
        try:
            decoded = base64.b64decode(encrypted_token.encode())
            decrypted = self.cipher.decrypt(decoded)
            return decrypted.decode()
        except Exception as e:
            raise ValueError(f"Token decryption failed: {str(e)}")


# 全局加密实例
credential_encryption = CredentialEncryption()