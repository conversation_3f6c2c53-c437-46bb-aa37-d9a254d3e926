import redis.asyncio as redis
from app.config import settings

# 创建Redis连接池 (连接字节跳动云Redis)
redis_pool = redis.ConnectionPool.from_url(
    settings.redis_url,
    encoding="utf-8",
    decode_responses=True,
    max_connections=20,
    retry_on_timeout=True,
    socket_keepalive=True,
    socket_keepalive_options={},
    health_check_interval=30,
)

# 创建Redis客户端
redis_client = redis.Redis(connection_pool=redis_pool)

# Redis依赖
async def get_redis():
    return redis_client
