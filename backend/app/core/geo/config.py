"""
Geo监控中心配置管理
"""
import os
from typing import Dict, Any, Optional
from dataclasses import dataclass
from enum import Enum

class AIModelType(Enum):
    """AI模型类型枚举"""
    DOUBAO = "doubao"
    KIMI = "kimi"
    DEEPSEEK = "deepseek"
    GPT = "gpt"
    GROK = "grok"
    PERPLEXITY = "perplexity"

@dataclass
class AIModelConfig:
    """AI模型配置"""
    model_id: str
    api_key: str
    base_url: str
    model_name: str
    max_tokens: int = 4000
    temperature: float = 0.7
    timeout: int = 30
    enabled: bool = True

    class Config:
        # 解决Pydantic命名空间冲突警告
        protected_namespaces = ()

class GeoConfig:
    """Geo监控中心配置管理器"""
    
    def __init__(self):
        self._ai_models: Dict[AIModelType, AIModelConfig] = {}
        self._load_config()
    
    def _load_config(self):
        """加载配置"""
        # 豆包配置 - 支持通用AI配置
        doubao_api_key = os.getenv("DOUBAO_API_KEY") or os.getenv("AI_API_KEY")
        print(f"检查豆包配置: AI_API_KEY={'已配置' if doubao_api_key else '未配置'}")
        if doubao_api_key:
            model_id = os.getenv("AI_MODEL", "doubao-seed-1-6-flash-250615")  # 使用flash模型
            base_url = os.getenv("AI_API_URL", "https://ark.cn-beijing.volces.com/api/v3")
            print(f"✅ 配置豆包AI: {model_id}")
            self._ai_models[AIModelType.DOUBAO] = AIModelConfig(
                model_id=model_id,
                api_key=doubao_api_key,
                base_url=base_url,
                model_name="豆包AI",
                max_tokens=4000,
                temperature=0.7
            )
        
        # KIMI配置
        if os.getenv("KIMI_API_KEY"):
            self._ai_models[AIModelType.KIMI] = AIModelConfig(
                model_id="moonshot-v1-8k",
                api_key=os.getenv("KIMI_API_KEY", ""),
                base_url="https://api.moonshot.cn/v1",
                model_name="KIMI",
                max_tokens=4000,
                temperature=0.7
            )
        
        # DeepSeek配置
        if os.getenv("DEEPSEEK_API_KEY"):
            self._ai_models[AIModelType.DEEPSEEK] = AIModelConfig(
                model_id="deepseek-chat",
                api_key=os.getenv("DEEPSEEK_API_KEY", ""),
                base_url="https://api.deepseek.com/v1",
                model_name="DeepSeek",
                max_tokens=4000,
                temperature=0.7
            )
        
        # GPT配置
        if os.getenv("OPENAI_API_KEY"):
            self._ai_models[AIModelType.GPT] = AIModelConfig(
                model_id="gpt-4",
                api_key=os.getenv("OPENAI_API_KEY", ""),
                base_url="https://api.openai.com/v1",
                model_name="GPT-4",
                max_tokens=4000,
                temperature=0.7
            )
        
        # Grok配置
        if os.getenv("XAI_API_KEY"):
            self._ai_models[AIModelType.GROK] = AIModelConfig(
                model_id="grok-beta",
                api_key=os.getenv("XAI_API_KEY", ""),
                base_url="https://api.x.ai/v1",
                model_name="Grok",
                max_tokens=4000,
                temperature=0.7
            )
        
        # Perplexity配置
        if os.getenv("PERPLEXITY_API_KEY"):
            self._ai_models[AIModelType.PERPLEXITY] = AIModelConfig(
                model_id="llama-3.1-sonar-small-128k-online",
                api_key=os.getenv("PERPLEXITY_API_KEY", ""),
                base_url="https://api.perplexity.ai",
                model_name="Perplexity",
                max_tokens=4000,
                temperature=0.7
            )

        # 添加默认测试模型（用于演示）- 仅在没有其他模型时
        if not self._ai_models:
            print("⚠️ 没有配置任何AI模型，添加默认GPT模型用于演示")
            self._ai_models[AIModelType.GPT] = AIModelConfig(
                model_id="gpt-3.5-turbo",
                api_key="demo-key",
                base_url="https://api.openai.com/v1",
                model_name="GPT-3.5-Turbo (Demo)",
                max_tokens=4000,
                temperature=0.7,
                enabled=True
            )
        else:
            print(f"✅ 已配置 {len(self._ai_models)} 个AI模型")
    
    def get_ai_model_config(self, model_type: AIModelType) -> Optional[AIModelConfig]:
        """获取AI模型配置"""
        return self._ai_models.get(model_type)
    
    def get_available_models(self) -> Dict[AIModelType, AIModelConfig]:
        """获取所有可用的AI模型"""
        return {k: v for k, v in self._ai_models.items() if v.enabled}
    
    def is_model_available(self, model_type: AIModelType) -> bool:
        """检查模型是否可用"""
        config = self._ai_models.get(model_type)
        return config is not None and config.enabled and config.api_key
    
    def disable_model(self, model_type: AIModelType):
        """禁用模型"""
        if model_type in self._ai_models:
            self._ai_models[model_type].enabled = False
    
    def enable_model(self, model_type: AIModelType):
        """启用模型"""
        if model_type in self._ai_models:
            self._ai_models[model_type].enabled = True

# 全局配置实例 - 延迟初始化
_geo_config = None

def get_geo_config():
    """获取Geo配置实例（单例模式）"""
    global _geo_config
    if _geo_config is None:
        _geo_config = GeoConfig()
    return _geo_config

# 为了向后兼容，保留原来的变量名
geo_config = get_geo_config()
