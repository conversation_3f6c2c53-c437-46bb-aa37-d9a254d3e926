"""
Geo监控中心异常定义
"""

class GeoException(Exception):
    """Geo系统基础异常"""
    
    def __init__(self, message: str, error_code: str = None):
        self.message = message
        self.error_code = error_code
        super().__init__(self.message)

class AIModelNotAvailableException(GeoException):
    """AI模型不可用异常"""
    
    def __init__(self, model_type: str, reason: str = None):
        message = f"AI模型 {model_type} 不可用"
        if reason:
            message += f": {reason}"
        super().__init__(message, "AI_MODEL_NOT_AVAILABLE")
        self.model_type = model_type
        self.reason = reason

class PromptTemplateException(GeoException):
    """提示词模板异常"""
    
    def __init__(self, template_name: str, reason: str = None):
        message = f"提示词模板 {template_name} 错误"
        if reason:
            message += f": {reason}"
        super().__init__(message, "PROMPT_TEMPLATE_ERROR")
        self.template_name = template_name
        self.reason = reason

class ConfigurationException(GeoException):
    """配置异常"""
    
    def __init__(self, config_key: str, reason: str = None):
        message = f"配置项 {config_key} 错误"
        if reason:
            message += f": {reason}"
        super().__init__(message, "CONFIGURATION_ERROR")
        self.config_key = config_key
        self.reason = reason

class AIAPIException(GeoException):
    """AI API调用异常"""
    
    def __init__(self, model_type: str, api_error: str, status_code: int = None):
        message = f"AI模型 {model_type} API调用失败: {api_error}"
        super().__init__(message, "AI_API_ERROR")
        self.model_type = model_type
        self.api_error = api_error
        self.status_code = status_code

class DataParsingException(GeoException):
    """数据解析异常"""
    
    def __init__(self, data_type: str, reason: str = None):
        message = f"数据解析失败: {data_type}"
        if reason:
            message += f" - {reason}"
        super().__init__(message, "DATA_PARSING_ERROR")
        self.data_type = data_type
        self.reason = reason

class TimeoutException(GeoException):
    """超时异常"""
    
    def __init__(self, operation: str, timeout_seconds: int):
        message = f"操作超时: {operation} (超时时间: {timeout_seconds}秒)"
        super().__init__(message, "TIMEOUT_ERROR")
        self.operation = operation
        self.timeout_seconds = timeout_seconds

class RateLimitException(GeoException):
    """频率限制异常"""

    def __init__(self, model_type: str, retry_after: int = None):
        message = f"AI模型 {model_type} 达到频率限制"
        if retry_after:
            message += f"，请在 {retry_after} 秒后重试"
        super().__init__(message, "RATE_LIMIT_ERROR")
        self.model_type = model_type
        self.retry_after = retry_after

class TemplateNotFoundException(GeoException):
    """模板未找到异常"""

    def __init__(self, template_name: str, reason: str = None):
        message = f"模板 {template_name} 未找到"
        if reason:
            message += f": {reason}"
        super().__init__(message, "TEMPLATE_NOT_FOUND")
        self.template_name = template_name
        self.reason = reason
