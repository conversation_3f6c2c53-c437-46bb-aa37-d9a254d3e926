"""
权限装饰器和依赖项
"""
from functools import wraps
from typing import List, Optional, Callable, Any
from fastapi import HTTPException, status, Depends
from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession

from app.database import get_db
from app.services.permission_service import PermissionService
from app.dependencies import get_current_user
from app.models.user import User


def require_permission(permission_code: str):
    """
    权限检查装饰器
    
    Args:
        permission_code: 需要的权限代码
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 从kwargs中获取current_user和db
            current_user = kwargs.get('current_user')
            db = kwargs.get('db')
            
            if not current_user or not db:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="未授权访问"
                )
            
            # 检查权限
            permission_service = PermissionService(db)
            has_permission = await permission_service.has_permission(
                str(current_user.id), 
                permission_code
            )
            
            if not has_permission:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"缺少权限: {permission_code}"
                )
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator


def require_any_permission(permission_codes: List[str]):
    """
    要求拥有任意一个权限
    
    Args:
        permission_codes: 权限代码列表
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            current_user = kwargs.get('current_user')
            db = kwargs.get('db')
            
            if not current_user or not db:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="未授权访问"
                )
            
            permission_service = PermissionService(db)
            
            # 检查是否有任意一个权限
            for permission_code in permission_codes:
                has_permission = await permission_service.has_permission(
                    str(current_user.id), 
                    permission_code
                )
                if has_permission:
                    return await func(*args, **kwargs)
            
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"缺少权限，需要以下任意一个权限: {', '.join(permission_codes)}"
            )
        return wrapper
    return decorator


def require_role(role_codes: List[str]):
    """
    角色检查装饰器
    
    Args:
        role_codes: 需要的角色代码列表
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            current_user = kwargs.get('current_user')
            db = kwargs.get('db')
            
            if not current_user or not db:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="未授权访问"
                )
            
            permission_service = PermissionService(db)
            has_role = await permission_service.check_user_has_any_role(
                str(current_user.id), 
                role_codes
            )
            
            if not has_role:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"需要以下任意一个角色: {', '.join(role_codes)}"
                )
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator


# 权限依赖项函数
async def require_admin_permission(
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """管理员权限依赖项"""
    permission_service = PermissionService(db)
    is_admin = await permission_service.is_admin_user_async(str(current_user["id"]))

    if not is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要管理员权限"
        )
    return current_user


async def require_enterprise_permission(
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """企业用户权限依赖项"""
    permission_service = PermissionService(db)
    is_enterprise = await permission_service.is_enterprise_user_async(str(current_user["id"]))

    if not is_enterprise:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要企业用户权限"
        )
    return current_user


async def require_channel_permission(
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """渠道商用户权限依赖项"""
    permission_service = PermissionService(db)
    is_channel = await permission_service.is_channel_user_async(str(current_user["id"]))

    if not is_channel:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要渠道商用户权限"
        )
    return current_user


async def require_agent_permission(
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """代理商用户权限依赖项"""
    permission_service = PermissionService(db)
    is_agent = await permission_service.is_agent_user_async(str(current_user["id"]))

    if not is_agent:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要代理商用户权限"
        )
    return current_user


def check_permission(permission_code: str):
    """
    权限检查依赖项工厂

    Args:
        permission_code: 权限代码
    """
    async def permission_checker(
        current_user: dict = Depends(get_current_user),
        db: AsyncSession = Depends(get_db)
    ):
        permission_service = PermissionService(db)
        has_permission = await permission_service.has_permission_async(
            str(current_user["id"]),
            permission_code
        )

        if not has_permission:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"缺少权限: {permission_code}"
            )
        return current_user

    return permission_checker


def check_any_permission(permission_codes: List[str]):
    """
    任意权限检查依赖项工厂

    Args:
        permission_codes: 权限代码列表
    """
    async def permission_checker(
        current_user: dict = Depends(get_current_user),
        db: AsyncSession = Depends(get_db)
    ):
        permission_service = PermissionService(db)

        for permission_code in permission_codes:
            has_permission = await permission_service.has_permission_async(
                str(current_user["id"]),
                permission_code
            )
            if has_permission:
                return current_user

        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=f"缺少权限，需要以下任意一个权限: {', '.join(permission_codes)}"
        )

    return permission_checker
