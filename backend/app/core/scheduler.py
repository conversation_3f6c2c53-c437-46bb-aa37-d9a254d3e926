"""
订阅系统定时任务调度器
实现自动续费、通知发送等核心定时任务
"""
import asyncio
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.triggers.interval import IntervalTrigger
from sqlalchemy.ext.asyncio import AsyncSession
from app.database import get_db

logger = logging.getLogger(__name__)

class SubscriptionScheduler:
    """订阅系统定时任务调度器"""
    
    def __init__(self):
        self.scheduler = AsyncIOScheduler()
        self.is_running = False
        
    async def start(self):
        """启动调度器"""
        if self.is_running:
            logger.warning("调度器已经在运行中")
            return
            
        try:
            # 添加定时任务
            await self._setup_jobs()
            
            # 启动调度器
            self.scheduler.start()
            self.is_running = True
            logger.info("订阅系统定时任务调度器启动成功")
            
        except Exception as e:
            logger.error(f"调度器启动失败: {e}")
            raise
        
    async def stop(self):
        """停止调度器"""
        if not self.is_running:
            return
            
        try:
            self.scheduler.shutdown(wait=False)
            self.is_running = False
            logger.info("订阅系统定时任务调度器已停止")
        except Exception as e:
            logger.error(f"调度器停止失败: {e}")
        
    async def _setup_jobs(self):
        """设置定时任务"""

        # 1. 自动续费任务
        self.scheduler.add_job(
            self._process_auto_renewals,
            trigger=CronTrigger(hour=2, minute=0),
            id='auto_renewal',
            name='自动续费处理',
            max_instances=1,
            coalesce=True,
            misfire_grace_time=3600
        )

        # 2. 到期提醒任务
        self.scheduler.add_job(
            self._send_expiry_notifications,
            trigger=CronTrigger(hour=9, minute=0),
            id='expiry_notifications',
            name='发送到期提醒',
            max_instances=1,
            coalesce=True,
            misfire_grace_time=1800
        )

        # 3. 续费提醒任务
        self.scheduler.add_job(
            self._send_renewal_notifications,
            trigger=CronTrigger(hour=10, minute=0),
            id='renewal_notifications',
            name='发送续费提醒',
            max_instances=1,
            coalesce=True,
            misfire_grace_time=1800
        )

        # 4. 订阅状态检查任务
        self.scheduler.add_job(
            self._check_subscription_status,
            trigger=IntervalTrigger(hours=1),
            id='subscription_status_check',
            name='订阅状态检查',
            max_instances=1,
            coalesce=True
        )

        # 5. 订阅冲突解决任务
        self.scheduler.add_job(
            self._resolve_subscription_conflicts,
            trigger=IntervalTrigger(hours=6),
            id='resolve_subscription_conflicts',
            name='解决订阅冲突',
            max_instances=1,
            coalesce=True
        )

        # 6. 月度配额重置任务
        self.scheduler.add_job(
            self._reset_monthly_quotas,
            trigger=CronTrigger(day=1, hour=3, minute=0),
            id='quota_reset',
            name='月度配额重置',
            max_instances=1,
            coalesce=True
        )

        logger.info("定时任务设置完成，共添加5个任务")
        
    async def _process_auto_renewals(self):
        """处理自动续费"""
        logger.info("开始执行自动续费任务...")
        
        success_count = 0
        failed_count = 0
        
        try:
            async for db in get_db():
                from app.services.subscription_service import SubscriptionService
                service = SubscriptionService(db)
                
                # 获取需要自动续费的订阅（明天到期）
                subscriptions = await service.get_auto_renewal_subscriptions()
                
                logger.info(f"找到 {len(subscriptions)} 个需要自动续费的订阅")
                
                for subscription in subscriptions:
                    try:
                        # 执行自动续费
                        result = await service.process_auto_renewal(str(subscription.id))
                        
                        if result.get('success'):
                            success_count += 1
                            logger.info(f"订阅 {subscription.id} 自动续费成功")
                        else:
                            failed_count += 1
                            logger.error(f"订阅 {subscription.id} 自动续费失败: {result.get('error')}")
                            
                            # 处理续费失败的重试逻辑
                            await self._handle_renewal_failure(db, subscription, result.get('error'))
                            
                    except Exception as e:
                        failed_count += 1
                        logger.error(f"处理订阅 {subscription.id} 自动续费时发生异常: {e}")
                        
                logger.info(f"自动续费任务完成 - 成功: {success_count}, 失败: {failed_count}")
                break
                
        except Exception as e:
            logger.error(f"自动续费任务执行失败: {e}")
            
    async def _send_expiry_notifications(self):
        """发送到期提醒"""
        logger.info("开始发送到期提醒...")
        
        sent_count = 0
        failed_count = 0
        
        try:
            async for db in get_db():
                from app.services.subscription_service import SubscriptionService
                from app.services.notification_service import NotificationService
                
                subscription_service = SubscriptionService(db)
                notification_service = NotificationService(db)
                
                # 获取3天内到期的订阅
                subscriptions = await subscription_service.get_expiring_subscriptions(days=3)
                
                logger.info(f"找到 {len(subscriptions)} 个即将到期的订阅")
                
                for subscription in subscriptions:
                    try:
                        await notification_service.send_expiry_notification(subscription)
                        sent_count += 1
                        logger.info(f"订阅 {subscription.id} 到期提醒发送成功")
                    except Exception as e:
                        failed_count += 1
                        logger.error(f"订阅 {subscription.id} 到期提醒发送失败: {e}")
                        
                logger.info(f"到期提醒发送完成 - 成功: {sent_count}, 失败: {failed_count}")
                break
                
        except Exception as e:
            logger.error(f"发送到期提醒任务失败: {e}")
            
    async def _send_renewal_notifications(self):
        """发送续费提醒"""
        logger.info("开始发送续费提醒...")
        
        sent_count = 0
        failed_count = 0
        
        try:
            async for db in get_db():
                from app.services.subscription_service import SubscriptionService
                from app.services.notification_service import NotificationService
                
                subscription_service = SubscriptionService(db)
                notification_service = NotificationService(db)
                
                # 获取7天内到期且未启用自动续费的订阅
                subscriptions = await subscription_service.get_renewal_reminder_subscriptions(days=7)
                
                logger.info(f"找到 {len(subscriptions)} 个需要续费提醒的订阅")
                
                for subscription in subscriptions:
                    try:
                        await notification_service.send_renewal_notification(subscription)
                        sent_count += 1
                        logger.info(f"订阅 {subscription.id} 续费提醒发送成功")
                    except Exception as e:
                        failed_count += 1
                        logger.error(f"订阅 {subscription.id} 续费提醒发送失败: {e}")
                        
                logger.info(f"续费提醒发送完成 - 成功: {sent_count}, 失败: {failed_count}")
                break
                
        except Exception as e:
            logger.error(f"发送续费提醒任务失败: {e}")
            
    async def _check_subscription_status(self):
        """检查订阅状态"""
        logger.info("开始检查订阅状态...")
        
        try:
            async for db in get_db():
                from app.services.subscription_service import SubscriptionService
                service = SubscriptionService(db)
                
                # 处理已到期的订阅
                expired_count = await service.process_expired_subscriptions()
                
                if expired_count > 0:
                    logger.info(f"处理了 {expired_count} 个到期订阅")
                    
                break
                
        except Exception as e:
            logger.error(f"订阅状态检查失败: {e}")

    async def _resolve_subscription_conflicts(self):
        """解决订阅冲突 - 保留最高级套餐"""
        logger.info("开始解决订阅冲突...")

        try:
            async for db in get_db():
                from app.services.subscription_service import SubscriptionService
                service = SubscriptionService(db)

                # 获取所有有多个活跃订阅的用户
                from sqlalchemy import text
                result = await db.execute(text("""
                    SELECT user_id, COUNT(*) as subscription_count
                    FROM subscriptions
                    WHERE status = 'active' AND end_date >= NOW()
                    GROUP BY user_id
                    HAVING COUNT(*) > 1
                """))

                conflict_users = result.fetchall()
                resolved_count = 0

                for user_row in conflict_users:
                    user_id = str(user_row.user_id)
                    result = await service.resolve_subscription_conflicts(user_id)
                    if result.get('conflicts_found'):
                        resolved_count += 1
                        logger.info(f"解决用户 {user_id} 的订阅冲突: {result.get('message')}")

                if resolved_count > 0:
                    logger.info(f"总共解决了 {resolved_count} 个用户的订阅冲突")
                else:
                    logger.info("没有发现订阅冲突")

                break

        except Exception as e:
            logger.error(f"解决订阅冲突失败: {e}")
            
    async def _reset_monthly_quotas(self):
        """重置月度配额"""
        logger.info("开始重置月度配额...")
        
        try:
            async for db in get_db():
                from app.services.quota_service import QuotaService
                service = QuotaService(db)
                
                reset_count = await service.reset_monthly_quotas()
                logger.info(f"月度配额重置完成，重置了 {reset_count} 个订阅的配额")
                break
                
        except Exception as e:
            logger.error(f"月度配额重置失败: {e}")
            
    async def _handle_renewal_failure(self, db: AsyncSession, subscription, error_message: str):
        """处理续费失败的重试逻辑"""
        try:
            from app.services.subscription_service import SubscriptionService
            service = SubscriptionService(db)
            
            # 记录失败次数和原因
            await service.record_renewal_failure(str(subscription.id), error_message)
            
            # 发送失败通知
            await service.send_renewal_failure_notification(subscription, error_message)
            
        except Exception as e:
            logger.error(f"处理续费失败时发生异常: {e}")

# 全局调度器实例
scheduler = SubscriptionScheduler()

async def start_scheduler():
    """启动调度器"""
    await scheduler.start()
    
async def stop_scheduler():
    """停止调度器"""
    await scheduler.stop()
    
def get_scheduler_status():
    """获取调度器状态"""
    return {
        "running": scheduler.is_running,
        "jobs": [
            {
                "id": job.id,
                "name": job.name,
                "next_run": job.next_run_time.isoformat() if job.next_run_time else None
            }
            for job in scheduler.scheduler.get_jobs()
        ] if scheduler.is_running else []
    }
