"""
订单状态机实现
"""
from typing import List, Optional, Dict, Any
from app.schemas.status import (
    ORDER_STATUS_TRANSITIONS,
    ORDER_STATUS_INFO,
    TRANSITION_REASONS,
    OrderStatusEnum
)


class OrderStateMachine:
    """
    订单状态机
    管理订单状态的转换规则和验证
    """
    
    def __init__(self, current_status: str):
        """
        初始化状态机
        
        Args:
            current_status: 当前订单状态
        """
        self.current_status = current_status
        self.transitions = ORDER_STATUS_TRANSITIONS
        self.status_info = ORDER_STATUS_INFO
        
    def can_transition_to(self, new_status: str) -> bool:
        """
        检查是否可以转换到新状态
        
        Args:
            new_status: 目标状态
            
        Returns:
            bool: 是否允许转换
        """
        if self.current_status not in self.transitions:
            return False
            
        allowed_states = self.transitions.get(self.current_status, [])
        return new_status in allowed_states
    
    def get_available_transitions(self) -> List[str]:
        """
        获取当前可用的状态转换
        
        Returns:
            List[str]: 可转换的状态列表
        """
        return self.transitions.get(self.current_status, [])
    
    def get_transition_reasons(self, new_status: str) -> List[str]:
        """
        获取状态转换的可能原因
        
        Args:
            new_status: 目标状态
            
        Returns:
            List[str]: 转换原因列表
        """
        transition_key = f"{self.current_status}_to_{new_status}"
        return TRANSITION_REASONS.get(transition_key, ["状态变更"])
    
    def validate_transition(self, new_status: str, reason: Optional[str] = None) -> tuple[bool, str]:
        """
        验证状态转换的合法性
        
        Args:
            new_status: 目标状态
            reason: 转换原因
            
        Returns:
            tuple[bool, str]: (是否合法, 错误信息/成功信息)
        """
        # 检查目标状态是否存在
        if new_status not in OrderStatusEnum.__members__.values():
            return False, f"无效的状态: {new_status}"
        
        # 检查是否允许转换
        if not self.can_transition_to(new_status):
            allowed = self.get_available_transitions()
            if not allowed:
                return False, f"状态 {self.current_status} 不允许任何转换"
            return False, f"不允许从 {self.current_status} 转换到 {new_status}，允许的状态: {', '.join(allowed)}"
        
        # 检查是否需要原因
        if not reason:
            reasons = self.get_transition_reasons(new_status)
            if reasons and len(reasons) > 0:
                return True, f"状态转换有效，建议提供原因: {', '.join(reasons[:3])}"
        
        return True, "状态转换有效"
    
    def get_status_info(self, status: Optional[str] = None) -> Dict[str, Any]:
        """
        获取状态信息
        
        Args:
            status: 状态码，如果为空则返回当前状态信息
            
        Returns:
            Dict: 状态详细信息
        """
        target_status = status or self.current_status
        return self.status_info.get(target_status, {
            "name": target_status,
            "color": "#000000",
            "icon": "help",
            "description": "未知状态"
        })
    
    def is_final_status(self, status: Optional[str] = None) -> bool:
        """
        检查是否为终态（不能再转换的状态）
        
        Args:
            status: 状态码，如果为空则检查当前状态
            
        Returns:
            bool: 是否为终态
        """
        target_status = status or self.current_status
        next_states = self.transitions.get(target_status, [])
        return len(next_states) == 0
    
    def get_transition_path(self, target_status: str) -> Optional[List[str]]:
        """
        获取到达目标状态的路径（简单BFS实现）
        
        Args:
            target_status: 目标状态
            
        Returns:
            Optional[List[str]]: 状态转换路径，如果无法到达返回None
        """
        if self.current_status == target_status:
            return [self.current_status]
        
        # BFS搜索
        from collections import deque
        
        queue = deque([(self.current_status, [self.current_status])])
        visited = {self.current_status}
        
        while queue:
            state, path = queue.popleft()
            
            for next_state in self.transitions.get(state, []):
                if next_state == target_status:
                    return path + [next_state]
                
                if next_state not in visited:
                    visited.add(next_state)
                    queue.append((next_state, path + [next_state]))
        
        return None
    
    @classmethod
    def get_all_transitions(cls) -> Dict[str, List[str]]:
        """
        获取所有状态转换规则
        
        Returns:
            Dict: 完整的状态转换规则
        """
        return ORDER_STATUS_TRANSITIONS
    
    @classmethod
    def get_all_status_info(cls) -> Dict[str, Dict[str, Any]]:
        """
        获取所有状态信息
        
        Returns:
            Dict: 所有状态的详细信息
        """
        return ORDER_STATUS_INFO