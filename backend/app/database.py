from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
try:
    from sqlalchemy.ext.asyncio import async_sessionmaker
except ImportError:
    from sqlalchemy.orm import sessionmaker
    async_sessionmaker = sessionmaker
from sqlalchemy.orm import declarative_base
from app.config import settings

# 创建异步数据库引擎 (连接字节跳动云RDS PostgreSQL)
engine = create_async_engine(
    settings.database_url,
    echo=settings.debug,  # 根据配置决定是否开启SQL日志输出
    echo_pool=settings.debug,  # 根据配置决定是否开启连接池日志
    pool_pre_ping=True,
    pool_recycle=300,
    pool_size=20,
    max_overflow=0,
    # 云数据库连接优化配置
    connect_args={
        "server_settings": {
            "application_name": "ai_seo_platform",
            "jit": "off"
        }
    }
)

# 创建会话工厂
AsyncSessionLocal = async_sessionmaker(
    engine,
    class_=AsyncSession,
    expire_on_commit=False
)

# 创建基础模型类
Base = declarative_base()

# 依赖注入：获取数据库会话
async def get_db():
    async with AsyncSessionLocal() as session:
        try:
            yield session
        finally:
            await session.close()

def get_db_session():
    """获取新的数据库会话（用于异步任务）"""
    return AsyncSessionLocal()
