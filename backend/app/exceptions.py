from fastapi import HTTPException, status

class BaseCustomException(HTTPException):
    """自定义异常基类"""
    def __init__(self, detail: str, status_code: int = status.HTTP_400_BAD_REQUEST):
        super().__init__(status_code=status_code, detail=detail)

class ValidationError(BaseCustomException):
    """数据验证错误"""
    def __init__(self, detail: str):
        super().__init__(detail=detail, status_code=status.HTTP_400_BAD_REQUEST)

class NotFoundError(BaseCustomException):
    """资源不存在错误"""
    def __init__(self, detail: str):
        super().__init__(detail=detail, status_code=status.HTTP_404_NOT_FOUND)

class PermissionError(BaseCustomException):
    """权限不足错误"""
    def __init__(self, detail: str):
        super().__init__(detail=detail, status_code=status.HTTP_403_FORBIDDEN)

class AuthenticationError(BaseCustomException):
    """认证错误"""
    def __init__(self, detail: str):
        super().__init__(detail=detail, status_code=status.HTTP_401_UNAUTHORIZED)

class QuotaExceededError(BaseCustomException):
    """配额超限错误"""
    def __init__(self, detail: str):
        super().__init__(detail=detail, status_code=status.HTTP_429_TOO_MANY_REQUESTS)

class BusinessLogicError(BaseCustomException):
    """业务逻辑错误"""
    def __init__(self, detail: str):
        super().__init__(detail=detail, status_code=status.HTTP_422_UNPROCESSABLE_ENTITY)
