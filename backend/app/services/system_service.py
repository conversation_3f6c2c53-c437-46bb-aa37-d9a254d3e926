from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func, desc, asc, text
from app.models.system import SystemLog, SystemConfig, SystemStatistics, SystemAlert, SystemHealthCheck, LogLevel, ConfigCategory
from app.models.user import User
from app.models.order import Order
from app.models.content import ContentRequest
from app.models.monitoring import MonitoringProject
from app.models.ai_service import AIRequest
from app.schemas.system import *
from app.exceptions import ValidationError, NotFoundError, PermissionError
from typing import Optional, Dict, Any, List
import time
import json
from datetime import datetime, timedelta
from decimal import Decimal

class SystemService:
    def __init__(self, db: AsyncSession):
        self.db = db
        self.app_version = "1.0.0"
        self.environment = "development"
    
    async def get_system_statistics(self, query: SystemStatisticsQuery) -> SystemStatisticsResponse:
        """获取系统统计数据"""
        # 计算时间范围
        end_date = query.end_date or datetime.utcnow()
        if query.start_date:
            start_date = query.start_date
        else:
            if query.period == "daily":
                start_date = end_date - timedelta(days=30)
            elif query.period == "weekly":
                start_date = end_date - timedelta(weeks=12)
            else:  # monthly
                start_date = end_date - timedelta(days=365)
        
        # 获取用户统计
        user_stats = await self._get_user_statistics(start_date, end_date)
        
        # 获取业务统计
        business_stats = await self._get_business_statistics(start_date, end_date)
        
        # 获取系统统计
        system_stats = await self._get_system_statistics_data(start_date, end_date)
        
        # 获取趋势数据
        trends = await self._get_trend_data(start_date, end_date, query.period)
        
        return SystemStatisticsResponse(
            user_stats=user_stats,
            business_stats=business_stats,
            system_stats=system_stats,
            trends=trends,
            period_start=start_date,
            period_end=end_date
        )
    
    async def get_system_config(self) -> SystemConfigListResponse:
        """获取系统配置"""
        result = await self.db.execute(
            select(SystemConfig).where(SystemConfig.is_active == True).order_by(SystemConfig.category, SystemConfig.config_key)
        )
        configs = result.scalars().all()
        
        items = []
        categories = set()
        
        for config in configs:
            # 敏感信息脱敏
            config_value = config.config_value
            if config.is_sensitive and config_value:
                config_value = "*" * min(8, len(str(config_value)))
            
            config_response = SystemConfigResponse(
                config_key=config.config_key,
                config_value=self._parse_config_value(config_value, config.config_type),
                config_type=config.config_type,
                category=config.category.value,
                display_name=config.display_name,
                description=config.description,
                default_value=config.default_value,
                is_sensitive=config.is_sensitive,
                is_readonly=config.is_readonly,
                updated_at=config.updated_at
            )
            items.append(config_response)
            categories.add(config.category.value)
        
        return SystemConfigListResponse(
            items=items,
            categories=sorted(list(categories))
        )
    
    async def update_system_config(self, request: SystemConfigUpdate, admin_user_id: str) -> SystemConfigUpdateResponse:
        """更新系统配置"""
        updated_configs = []
        failed_configs = []
        
        for config_key, config_value in request.configs.items():
            try:
                # 获取配置项
                result = await self.db.execute(
                    select(SystemConfig).where(SystemConfig.config_key == config_key)
                )
                config = result.scalar_one_or_none()
                
                if not config:
                    failed_configs.append({
                        "config_key": config_key,
                        "error": "配置项不存在"
                    })
                    continue
                
                if config.is_readonly:
                    failed_configs.append({
                        "config_key": config_key,
                        "error": "配置项为只读"
                    })
                    continue
                
                # 验证配置值
                validated_value = self._validate_config_value(config_value, config.config_type, config.validation_rules)
                
                # 保存旧值用于日志
                old_value = config.config_value
                
                # 更新配置
                config.config_value = str(validated_value)
                config.updated_by = admin_user_id
                config.updated_at = datetime.utcnow()
                
                updated_configs.append(config_key)
                
                # 记录系统日志
                await self._log_system_event(
                    level=LogLevel.INFO,
                    message=f"系统配置更新: {config_key}",
                    module="system_config",
                    user_id=admin_user_id,
                    extra_data={
                        "config_key": config_key,
                        "old_value": old_value,
                        "new_value": str(validated_value)
                    }
                )
                
            except Exception as e:
                failed_configs.append({
                    "config_key": config_key,
                    "error": str(e)
                })
        
        await self.db.commit()
        
        return SystemConfigUpdateResponse(
            updated_configs=updated_configs,
            failed_configs=failed_configs,
            total_updated=len(updated_configs),
            total_failed=len(failed_configs)
        )
    
    async def get_system_logs(self, query: SystemLogQuery) -> SystemLogListResponse:
        """获取系统日志"""
        # 构建查询条件
        conditions = []
        
        if query.level:
            conditions.append(SystemLog.level == query.level)
        if query.module:
            conditions.append(SystemLog.module == query.module)
        if query.user_id:
            conditions.append(SystemLog.user_id == query.user_id)
        if query.start_date:
            conditions.append(SystemLog.created_at >= query.start_date)
        if query.end_date:
            conditions.append(SystemLog.created_at <= query.end_date)
        if query.keyword:
            search_condition = or_(
                SystemLog.message.ilike(f"%{query.keyword}%"),
                SystemLog.error_message.ilike(f"%{query.keyword}%")
            )
            conditions.append(search_condition)
        
        # 构建查询
        base_query = select(SystemLog)
        if conditions:
            base_query = base_query.where(and_(*conditions))
        
        # 排序
        if query.sort_order == "desc":
            base_query = base_query.order_by(desc(getattr(SystemLog, query.sort_by)))
        else:
            base_query = base_query.order_by(asc(getattr(SystemLog, query.sort_by)))
        
        # 分页
        offset = (query.page - 1) * query.size
        paginated_query = base_query.offset(offset).limit(query.size)
        
        # 执行查询
        result = await self.db.execute(paginated_query)
        logs = result.scalars().all()
        
        # 获取总数
        count_query = select(func.count(SystemLog.id))
        if conditions:
            count_query = count_query.where(and_(*conditions))
        total_result = await self.db.execute(count_query)
        total = total_result.scalar()
        
        # 构建响应数据
        items = []
        for log in logs:
            log_response = SystemLogResponse(
                id=str(log.id),
                level=log.level.value,
                message=log.message,
                module=log.module,
                function=log.function,
                user_id=str(log.user_id) if log.user_id else None,
                user_email=log.user_email,
                ip_address=log.ip_address,
                request_method=log.request_method,
                request_path=log.request_path,
                response_status=log.response_status,
                response_time=log.response_time,
                error_type=log.error_type,
                error_message=log.error_message,
                created_at=log.created_at
            )
            items.append(log_response)
        
        return SystemLogListResponse(
            items=items,
            pagination={
                "total": total,
                "page": query.page,
                "size": query.size,
                "pages": (total + query.size - 1) // query.size
            },
            statistics=await self._get_log_statistics()
        )
    
    async def get_system_health(self) -> SystemHealthResponse:
        """获取系统健康状态"""
        components = []
        overall_status = "healthy"
        
        # 检查数据库
        db_status = await self._check_database_health()
        components.append(db_status)
        if db_status["status"] != "healthy":
            overall_status = "warning" if overall_status == "healthy" else "critical"
        
        # 检查Redis（模拟）
        redis_status = await self._check_redis_health()
        components.append(redis_status)
        if redis_status["status"] != "healthy":
            overall_status = "warning" if overall_status == "healthy" else "critical"
        
        # 检查文件存储
        storage_status = await self._check_storage_health()
        components.append(storage_status)
        if storage_status["status"] != "healthy":
            overall_status = "warning" if overall_status == "healthy" else "critical"
        
        # 检查AI服务
        ai_status = await self._check_ai_service_health()
        components.append(ai_status)
        if ai_status["status"] != "healthy":
            overall_status = "warning" if overall_status == "healthy" else "critical"
        
        # 系统信息
        uptime = self._get_system_uptime()
        
        return SystemHealthResponse(
            overall_status=overall_status,
            components=components,
            last_check_time=datetime.utcnow(),
            uptime=uptime,
            version=self.app_version,
            environment=self.environment
        )

    async def _get_user_statistics(self, start_date: datetime, end_date: datetime) -> Dict[str, int]:
        """获取用户统计"""
        # 总用户数
        total_users_result = await self.db.execute(select(func.count(User.id)))
        total_users = total_users_result.scalar() or 0

        # 新用户数
        new_users_result = await self.db.execute(
            select(func.count(User.id)).where(
                and_(User.created_at >= start_date, User.created_at <= end_date)
            )
        )
        new_users = new_users_result.scalar() or 0

        # 活跃用户数（最近30天有登录）
        active_users_result = await self.db.execute(
            select(func.count(User.id)).where(
                User.last_login_at >= datetime.utcnow() - timedelta(days=30)
            )
        )
        active_users = active_users_result.scalar() or 0

        return {
            "total_users": total_users,
            "new_users": new_users,
            "active_users": active_users
        }

    async def _get_business_statistics(self, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """获取业务统计"""
        # 订单统计
        total_orders_result = await self.db.execute(select(func.count(Order.id)))
        total_orders = total_orders_result.scalar() or 0

        new_orders_result = await self.db.execute(
            select(func.count(Order.id)).where(
                and_(Order.created_at >= start_date, Order.created_at <= end_date)
            )
        )
        new_orders = new_orders_result.scalar() or 0

        # 收入统计
        total_revenue_result = await self.db.execute(
            select(func.sum(Order.final_amount)).where(Order.order_status.in_(["paid", "completed"]))
        )
        total_revenue = float(total_revenue_result.scalar() or 0)

        new_revenue_result = await self.db.execute(
            select(func.sum(Order.final_amount)).where(
                and_(
                    Order.created_at >= start_date,
                    Order.created_at <= end_date,
                    Order.order_status.in_(["paid", "completed"])
                )
            )
        )
        new_revenue = float(new_revenue_result.scalar() or 0)

        return {
            "total_orders": total_orders,
            "new_orders": new_orders,
            "total_revenue": total_revenue,
            "new_revenue": new_revenue
        }

    async def _get_system_statistics_data(self, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """获取系统统计数据"""
        # API请求统计
        api_requests_result = await self.db.execute(
            select(func.count(SystemLog.id)).where(
                and_(
                    SystemLog.created_at >= start_date,
                    SystemLog.created_at <= end_date,
                    SystemLog.request_path.isnot(None)
                )
            )
        )
        api_requests = api_requests_result.scalar() or 0

        # 错误统计
        error_count_result = await self.db.execute(
            select(func.count(SystemLog.id)).where(
                and_(
                    SystemLog.created_at >= start_date,
                    SystemLog.created_at <= end_date,
                    SystemLog.level.in_(["error", "critical"])
                )
            )
        )
        error_count = error_count_result.scalar() or 0

        return {
            "api_requests": api_requests,
            "error_count": error_count,
            "cpu_usage": 25.5,  # 模拟CPU使用率
            "memory_usage": 68.2,  # 模拟内存使用率
            "disk_usage": 45.8  # 模拟磁盘使用率
        }

    async def _get_trend_data(self, start_date: datetime, end_date: datetime, period: str) -> List[Dict[str, Any]]:
        """获取趋势数据"""
        # 简化的趋势数据
        trends = []
        current_date = start_date

        while current_date <= end_date:
            if period == "daily":
                next_date = current_date + timedelta(days=1)
            elif period == "weekly":
                next_date = current_date + timedelta(weeks=1)
            else:  # monthly
                next_date = current_date + timedelta(days=30)

            # 模拟趋势数据
            trends.append({
                "date": current_date.isoformat(),
                "users": 100 + len(trends) * 5,
                "orders": 20 + len(trends) * 2,
                "revenue": 1000.0 + len(trends) * 100.0,
                "api_requests": 500 + len(trends) * 50
            })

            current_date = next_date
            if len(trends) >= 30:  # 限制数据点数量
                break

        return trends

    def _parse_config_value(self, value: str, config_type: str) -> Any:
        """解析配置值"""
        if value is None:
            return None

        try:
            if config_type == "integer":
                return int(value)
            elif config_type == "boolean":
                return value.lower() in ("true", "1", "yes", "on")
            elif config_type == "json":
                return json.loads(value)
            else:
                return value
        except (ValueError, json.JSONDecodeError):
            return value

    def _validate_config_value(self, value: Any, config_type: str, validation_rules: Dict) -> Any:
        """验证配置值"""
        # 基本类型验证
        if config_type == "integer":
            if not isinstance(value, int):
                try:
                    value = int(value)
                except (ValueError, TypeError):
                    raise ValueError("配置值必须是整数")
        elif config_type == "boolean":
            if not isinstance(value, bool):
                if isinstance(value, str):
                    value = value.lower() in ("true", "1", "yes", "on")
                else:
                    raise ValueError("配置值必须是布尔值")
        elif config_type == "json":
            if isinstance(value, str):
                try:
                    json.loads(value)
                except json.JSONDecodeError:
                    raise ValueError("配置值必须是有效的JSON格式")

        # TODO: 实现更复杂的验证规则
        return value

    async def _log_system_event(self, level: LogLevel, message: str, module: str,
                               user_id: str = None, extra_data: Dict = None):
        """记录系统事件"""
        system_log = SystemLog(
            level=level,
            message=message,
            module=module,
            user_id=user_id,
            extra_data=extra_data
        )
        self.db.add(system_log)
        # 注意：这里不提交，由调用方决定何时提交

    async def _check_database_health(self) -> Dict[str, Any]:
        """检查数据库健康状态"""
        try:
            start_time = time.time()
            await self.db.execute(text("SELECT 1"))
            response_time = int((time.time() - start_time) * 1000)

            return {
                "name": "Database",
                "status": "healthy",
                "response_time": response_time,
                "details": {"type": "PostgreSQL", "connection": "active"}
            }
        except Exception as e:
            return {
                "name": "Database",
                "status": "critical",
                "error": str(e),
                "details": {"type": "PostgreSQL", "connection": "failed"}
            }

    async def _check_redis_health(self) -> Dict[str, Any]:
        """检查Redis健康状态"""
        try:
            # 模拟Redis检查
            return {
                "name": "Redis",
                "status": "healthy",
                "response_time": 5,
                "details": {"connection": "active"}
            }
        except Exception as e:
            return {
                "name": "Redis",
                "status": "warning",
                "error": str(e),
                "details": {"connection": "not_configured"}
            }

    async def _check_storage_health(self) -> Dict[str, Any]:
        """检查存储健康状态"""
        try:
            # 模拟TOS存储检查
            return {
                "name": "Storage",
                "status": "healthy",
                "response_time": 120,
                "details": {"type": "TOS", "connection": "active"}
            }
        except Exception as e:
            return {
                "name": "Storage",
                "status": "warning",
                "error": str(e),
                "details": {"type": "TOS", "connection": "failed"}
            }

    async def _check_ai_service_health(self) -> Dict[str, Any]:
        """检查AI服务健康状态"""
        try:
            # 模拟AI服务检查
            return {
                "name": "AI Service",
                "status": "healthy",
                "response_time": 800,
                "details": {"providers": ["OpenAI", "Claude"], "connection": "active"}
            }
        except Exception as e:
            return {
                "name": "AI Service",
                "status": "warning",
                "error": str(e),
                "details": {"connection": "failed"}
            }

    def _get_system_uptime(self) -> int:
        """获取系统运行时间"""
        # 模拟系统运行时间（7天）
        return 7 * 24 * 3600

    async def _get_log_statistics(self) -> Dict[str, Any]:
        """获取日志统计"""
        # 各级别日志统计
        level_stats = {}
        for level in LogLevel:
            count_result = await self.db.execute(
                select(func.count(SystemLog.id)).where(SystemLog.level == level)
            )
            level_stats[f"{level.value}_logs"] = count_result.scalar() or 0

        return level_stats
