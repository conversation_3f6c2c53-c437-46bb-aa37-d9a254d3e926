from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func, desc, asc
from app.models.upload import UploadedFile, UploadSession, FileAccessLog, FileType, FileStatus
from app.schemas.upload import *
from app.exceptions import ValidationError, NotFoundError, PermissionError
from app.config import settings
from app.services.tos_service import TOSService
from typing import Optional, Dict, Any, List
import hashlib
import secrets
import string
from datetime import datetime, timedelta
import mimetypes
import os
import logging
import base64
try:
    from tos import TosClientV2, HttpMethodType
except ImportError:
    TosClientV2 = None
    HttpMethodType = None

logger = logging.getLogger(__name__)

class UploadService:
    def __init__(self, db: AsyncSession):
        self.db = db
        # 配置
        self.max_file_size = 100 * 1024 * 1024  # 100MB
        # 从配置文件读取TOS设置
        self.tos_access_key = settings.tos_access_key
        self.tos_secret_key = settings.tos_secret_key
        self.tos_bucket = settings.tos_bucket
        self.tos_region = settings.tos_region
        self.tos_endpoint = settings.tos_endpoint
    
    async def upload_file(self, user_id: str, file_data: bytes, filename: str, request: FileUploadRequest) -> FileUploadResponse:
        """上传单个文件"""
        # 验证文件
        file_info = self._validate_file(file_data, filename)
        
        # 生成存储路径
        storage_key = self._generate_storage_key(user_id, filename, file_info['file_type'])
        
        # 计算文件哈希
        file_hash = hashlib.sha256(file_data).hexdigest()
        
        # 检查文件是否已存在
        existing_file = await self._check_duplicate_file(user_id, file_hash)
        if existing_file:
            return await self._build_upload_response(existing_file)
        
        # 创建文件记录
        uploaded_file = UploadedFile(
            user_id=user_id,
            original_filename=filename,
            file_extension=file_info['file_extension'],
            file_type=file_info['file_type'],
            file_size=len(file_data),
            mime_type=file_info['mime_type'],
            storage_path=f"{self.tos_bucket}/{storage_key}",
            storage_bucket=self.tos_bucket,
            storage_key=storage_key,
            file_metadata=file_info.get('metadata', {}),
            business_type=request.business_type,
            business_id=request.business_id,
            file_hash=file_hash,
            is_public=request.is_public,
            access_token=self._generate_access_token(),
            expires_at=datetime.utcnow() + timedelta(hours=request.expires_hours) if request.expires_hours else None
        )
        
        self.db.add(uploaded_file)
        await self.db.commit()
        await self.db.refresh(uploaded_file)
        
        try:
            # 上传到TOS存储
            upload_result = await self._upload_to_tos(storage_key, file_data, file_info['mime_type'])

            # 更新文件状态
            uploaded_file.upload_status = FileStatus.COMPLETED
            uploaded_file.upload_progress = 100
            uploaded_file.storage_url = upload_result['url']
            uploaded_file.updated_at = datetime.utcnow()

            await self.db.commit()

            return await self._build_upload_response(uploaded_file)

        except Exception as e:
            # 上传失败，更新状态
            uploaded_file.upload_status = FileStatus.FAILED
            await self.db.commit()
            raise ValidationError(f"文件上传失败: {str(e)}")
    
    async def batch_upload_files(self, user_id: str, request: BatchUploadRequest) -> BatchUploadResponse:
        """批量文件上传"""
        # 创建上传会话
        session_id = self._generate_session_id()
        total_size = sum(file_info.get('size', 0) for file_info in request.files_info)
        
        upload_session = UploadSession(
            session_id=session_id,
            user_id=user_id,
            total_files=len(request.files_info),
            total_size=total_size,
            business_type=request.business_type,
            business_id=request.business_id,
            upload_config={
                'is_public': request.is_public,
                'expires_hours': request.expires_hours
            },
            expires_at=datetime.utcnow() + timedelta(hours=2)  # 会话2小时过期
        )
        
        self.db.add(upload_session)
        await self.db.commit()
        await self.db.refresh(upload_session)
        
        # 为每个文件生成预签名上传URL
        upload_urls = []
        for i, file_info in enumerate(request.files_info):
            filename = file_info.get('filename')
            file_size = file_info.get('size', 0)
            
            # 验证文件信息
            self._validate_file_info(filename, file_size)
            
            # 生成存储路径
            storage_key = self._generate_storage_key(user_id, filename, self._get_file_type(filename))
            
            # 创建文件记录
            uploaded_file = UploadedFile(
                user_id=user_id,
                original_filename=filename,
                file_extension=os.path.splitext(filename)[1].lower(),
                file_type=self._get_file_type(filename),
                file_size=file_size,
                mime_type=mimetypes.guess_type(filename)[0],
                storage_path=f"{self.tos_bucket}/{storage_key}",
                storage_bucket=self.tos_bucket,
                storage_key=storage_key,
                upload_session_id=session_id,
                business_type=request.business_type,
                business_id=request.business_id,
                is_public=request.is_public,
                access_token=self._generate_access_token(),
                expires_at=datetime.utcnow() + timedelta(hours=request.expires_hours) if request.expires_hours else None
            )
            
            self.db.add(uploaded_file)
            
            # 生成预签名上传URL
            presigned_url = await self._generate_presigned_upload_url(storage_key, file_size)
            
            upload_urls.append({
                'file_id': str(uploaded_file.id),
                'filename': filename,
                'upload_url': presigned_url,
                'storage_key': storage_key
            })
        
        await self.db.commit()
        
        return BatchUploadResponse(
            session_id=session_id,
            upload_urls=upload_urls,
            total_files=len(request.files_info),
            expires_at=upload_session.expires_at
        )
    
    async def get_file_list(self, query: FileListQuery, user_id: str) -> Dict[str, Any]:
        """获取文件列表"""
        # 构建查询条件
        conditions = [UploadedFile.user_id == user_id, UploadedFile.upload_status != FileStatus.DELETED]
        
        if query.file_type:
            conditions.append(UploadedFile.file_type == query.file_type)
        if query.business_type:
            conditions.append(UploadedFile.business_type == query.business_type)
        if query.business_id:
            conditions.append(UploadedFile.business_id == query.business_id)
        if query.status:
            conditions.append(UploadedFile.upload_status == query.status)
        if query.start_date:
            conditions.append(UploadedFile.created_at >= query.start_date)
        if query.end_date:
            conditions.append(UploadedFile.created_at <= query.end_date)
        if query.keyword:
            conditions.append(UploadedFile.original_filename.ilike(f"%{query.keyword}%"))
        
        # 构建查询
        base_query = select(UploadedFile).where(and_(*conditions))
        
        # 排序
        if query.sort_order == "desc":
            base_query = base_query.order_by(desc(getattr(UploadedFile, query.sort_by)))
        else:
            base_query = base_query.order_by(asc(getattr(UploadedFile, query.sort_by)))
        
        # 分页
        offset = (query.page - 1) * query.size
        paginated_query = base_query.offset(offset).limit(query.size)
        
        # 执行查询
        result = await self.db.execute(paginated_query)
        files = result.scalars().all()
        
        # 获取总数
        count_query = select(func.count(UploadedFile.id)).where(and_(*conditions))
        total_result = await self.db.execute(count_query)
        total = total_result.scalar()
        
        # 构建响应数据
        items = []
        for file in files:
            file_response = UploadedFileResponse(
                id=str(file.id),
                original_filename=file.original_filename,
                file_extension=file.file_extension,
                file_type=file.file_type.value,
                file_size=file.file_size,
                mime_type=file.mime_type,
                storage_url=self._build_access_url(file),
                file_metadata=file.file_metadata,
                upload_status=file.upload_status.value,
                upload_progress=file.upload_progress,
                business_type=file.business_type,
                business_id=file.business_id,
                file_hash=file.file_hash,
                is_public=file.is_public,
                created_at=file.created_at,
                updated_at=file.updated_at,
                expires_at=file.expires_at
            )
            items.append(file_response)
        
        return {
            "items": items,
            "pagination": {
                "total": total,
                "page": query.page,
                "size": query.size,
                "pages": (total + query.size - 1) // query.size
            },
            "statistics": await self._get_file_statistics(user_id)
        }

    async def delete_file(self, file_id: str, user_id: str) -> FileDeleteResponse:
        """删除文件"""
        # 获取文件记录
        result = await self.db.execute(
            select(UploadedFile).where(
                and_(UploadedFile.id == file_id, UploadedFile.user_id == user_id)
            )
        )
        file = result.scalar_one_or_none()
        if not file:
            raise NotFoundError("文件不存在或无权限删除")

        try:
            # 从TOS删除文件（模拟）
            await self._delete_from_tos(file.storage_key)

            # 更新文件状态
            file.upload_status = FileStatus.DELETED
            file.deleted_at = datetime.utcnow()
            file.updated_at = datetime.utcnow()

            await self.db.commit()

            # 记录访问日志
            await self._log_file_access(file.id, user_id, "delete")

            return FileDeleteResponse(
                file_id=file_id,
                filename=file.original_filename,
                deleted_at=file.deleted_at,
                status="success"
            )

        except Exception as e:
            raise ValidationError(f"文件删除失败: {str(e)}")

    def _validate_file(self, file_data: bytes, filename: str) -> Dict[str, Any]:
        """验证文件"""
        file_size = len(file_data)

        # 检查文件大小
        if file_size > self.max_file_size:
            raise ValidationError(f"文件大小超过限制 ({self.max_file_size} bytes)")

        # 获取文件扩展名和类型
        file_extension = os.path.splitext(filename)[1].lower()
        mime_type = mimetypes.guess_type(filename)[0]
        file_type = self._get_file_type(filename)

        # 验证文件类型
        allowed_extensions = {
            'image': ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'],
            'video': ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm'],
            'document': ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt'],
            'audio': ['.mp3', '.wav', '.flac', '.aac', '.ogg'],
            'archive': ['.zip', '.rar', '.7z', '.tar', '.gz']
        }

        if file_type != 'other' and file_extension not in allowed_extensions.get(file_type, []):
            raise ValidationError(f"不支持的文件类型: {file_extension}")

        # 提取文件元数据
        metadata = self._extract_file_metadata(file_data, file_type, mime_type)

        return {
            'file_extension': file_extension,
            'mime_type': mime_type,
            'file_type': file_type,
            'metadata': metadata
        }

    def _validate_file_info(self, filename: str, file_size: int):
        """验证文件信息"""
        if file_size > self.max_file_size:
            raise ValidationError(f"文件 {filename} 大小超过限制")

        file_extension = os.path.splitext(filename)[1].lower()
        if not file_extension:
            raise ValidationError(f"文件 {filename} 缺少扩展名")

    def _get_file_type(self, filename: str) -> str:
        """根据文件名获取文件类型"""
        file_extension = os.path.splitext(filename)[1].lower()

        image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']
        video_extensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm']
        document_extensions = ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt']
        audio_extensions = ['.mp3', '.wav', '.flac', '.aac', '.ogg']
        archive_extensions = ['.zip', '.rar', '.7z', '.tar', '.gz']

        if file_extension in image_extensions:
            return 'image'
        elif file_extension in video_extensions:
            return 'video'
        elif file_extension in document_extensions:
            return 'document'
        elif file_extension in audio_extensions:
            return 'audio'
        elif file_extension in archive_extensions:
            return 'archive'
        else:
            return 'other'

    def _generate_storage_key(self, user_id: str, filename: str, file_type: str) -> str:
        """生成存储键"""
        timestamp = datetime.utcnow().strftime("%Y%m%d/%H%M%S")
        random_suffix = ''.join(secrets.choice(string.ascii_lowercase + string.digits) for _ in range(8))
        safe_filename = "".join(c for c in filename if c.isalnum() or c in ".-_")
        return f"{file_type}/{user_id}/{timestamp}/{random_suffix}_{safe_filename}"

    def _generate_access_token(self) -> str:
        """生成访问令牌"""
        return ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(32))

    def _generate_session_id(self) -> str:
        """生成会话ID"""
        timestamp = datetime.utcnow().strftime("%Y%m%d%H%M%S")
        random_suffix = ''.join(secrets.choice(string.ascii_uppercase + string.digits) for _ in range(8))
        return f"UPLOAD_{timestamp}_{random_suffix}"

    def _extract_file_metadata(self, file_data: bytes, file_type: str, mime_type: str) -> Dict[str, Any]:
        """提取文件元数据"""
        metadata = {}

        if file_type == 'image':
            # TODO: 使用PIL提取图片元数据
            metadata = {
                'width': 1920,
                'height': 1080,
                'format': mime_type
            }
        elif file_type == 'video':
            # TODO: 使用ffmpeg提取视频元数据
            metadata = {
                'duration': 120,
                'width': 1920,
                'height': 1080,
                'format': mime_type
            }

        return metadata

    async def _check_duplicate_file(self, user_id: str, file_hash: str) -> Optional[UploadedFile]:
        """检查重复文件"""
        result = await self.db.execute(
            select(UploadedFile).where(
                and_(
                    UploadedFile.user_id == user_id,
                    UploadedFile.file_hash == file_hash,
                    UploadedFile.upload_status == FileStatus.COMPLETED
                )
            )
        )
        return result.scalar_one_or_none()

    async def _upload_to_tos(self, storage_key: str, file_data: bytes, mime_type: str) -> Dict[str, str]:
        """上传文件到TOS云存储"""
        try:
            import tos
        except ImportError:
            raise ValidationError("TOS SDK未安装，请安装: pip install tos")

        try:
            # 验证TOS配置
            if not all([self.tos_access_key, self.tos_secret_key, self.tos_bucket, self.tos_region, self.tos_endpoint]):
                raise ValidationError("TOS配置不完整，请检查.env文件中的TOS配置")

            # 创建TOS客户端（使用配置文件中的设置）
            client = tos.TosClientV2(
                ak=self.tos_access_key,
                sk=self.tos_secret_key,
                endpoint=self.tos_endpoint,
                region=self.tos_region
            )

            # 上传文件到TOS
            response = client.put_object(
                bucket=self.tos_bucket,
                key=storage_key,
                content=file_data,
                content_type=mime_type
            )

            # 构建TOS访问URL
            url = f"https://{self.tos_bucket}.{self.tos_endpoint}/{storage_key}"

            return {
                'url': url,
                'etag': response.etag,
                'request_id': response.request_id
            }

        except Exception as e:
            raise ValidationError(f"TOS文件上传失败: {str(e)}")

    async def _generate_presigned_upload_url(self, storage_key: str, file_size: int) -> str:
        """生成预签名上传URL"""
        try:
            import tos
            from tos import HttpMethodType
        except ImportError:
            raise ValidationError("TOS SDK未安装，请安装: pip install tos")
        
        try:
            # 验证TOS配置
            if not all([self.tos_access_key, self.tos_secret_key, self.tos_bucket, self.tos_region, self.tos_endpoint]):
                return f"https://upload.example.com/presigned/{storage_key}"  # 模拟URL
            
            # 创建TOS客户端
            client = tos.TosClientV2(
                ak=self.tos_access_key,
                sk=self.tos_secret_key,
                endpoint=self.tos_endpoint,
                region=self.tos_region
            )
            
            # 生成预签名URL，有效期2小时
            presigned_url = client.pre_signed_url(
                http_method=HttpMethodType.Http_Method_Put,
                bucket=self.tos_bucket,
                key=storage_key,
                expires=7200  # 2小时
            )
            
            logger.info(f"生成预签名URL成功: key={storage_key}")
            return presigned_url
        except Exception as e:
            logger.error(f"生成预签名URL失败: {e}")
            raise ValidationError(f"生成上传URL失败: {str(e)}")

    async def _delete_from_tos(self, storage_key: str):
        """从TOS删除文件"""
        try:
            import tos
        except ImportError:
            raise ValidationError("TOS SDK未安装，无法删除文件")

        try:
            # 验证TOS配置
            if not all([self.tos_access_key, self.tos_secret_key, self.tos_bucket, self.tos_region, self.tos_endpoint]):
                raise ValidationError("TOS配置不完整，请检查.env文件中的TOS配置")

            # 创建TOS客户端（使用配置文件中的设置）
            client = tos.TosClientV2(
                ak=self.tos_access_key,
                sk=self.tos_secret_key,
                endpoint=self.tos_endpoint,
                region=self.tos_region
            )

            # 从TOS删除文件
            client.delete_object(bucket=self.tos_bucket, key=storage_key)

        except Exception as e:
            raise ValidationError(f"TOS文件删除失败: {str(e)}")

    def _build_access_url(self, file: UploadedFile) -> str:
        """构建文件访问URL - 直接返回TOS链接供渠道商下载"""
        # 对于内容文件，直接返回TOS链接，方便渠道商下载
        return file.storage_url or f"https://{self.tos_bucket}.{self.tos_endpoint}/{file.storage_key}"

    async def _build_upload_response(self, file: UploadedFile) -> FileUploadResponse:
        """构建上传响应"""
        return FileUploadResponse(
            file_id=str(file.id),
            upload_url="",  # 已上传完成，无需上传URL
            access_url=self._build_access_url(file),
            expires_at=file.expires_at or datetime.utcnow() + timedelta(days=365)
        )

    async def _log_file_access(self, file_id: str, user_id: str, access_type: str):
        """记录文件访问日志"""
        access_log = FileAccessLog(
            file_id=file_id,
            user_id=user_id,
            access_type=access_type
        )
        self.db.add(access_log)
        await self.db.commit()

    async def _get_file_statistics(self, user_id: str) -> Dict[str, Any]:
        """获取文件统计信息"""
        # 总文件数
        total_files_result = await self.db.execute(
            select(func.count(UploadedFile.id)).where(
                and_(UploadedFile.user_id == user_id, UploadedFile.upload_status != FileStatus.DELETED)
            )
        )
        total_files = total_files_result.scalar() or 0

        # 总文件大小
        total_size_result = await self.db.execute(
            select(func.sum(UploadedFile.file_size)).where(
                and_(UploadedFile.user_id == user_id, UploadedFile.upload_status != FileStatus.DELETED)
            )
        )
        total_size = total_size_result.scalar() or 0

        # 各类型文件数量
        type_stats = {}
        for file_type in FileType:
            type_count_result = await self.db.execute(
                select(func.count(UploadedFile.id)).where(
                    and_(
                        UploadedFile.user_id == user_id,
                        UploadedFile.file_type == file_type,
                        UploadedFile.upload_status != FileStatus.DELETED
                    )
                )
            )
            type_stats[f"{file_type.value}_files"] = type_count_result.scalar() or 0

        return {
            "total_files": total_files,
            "total_size": total_size,
            **type_stats
        }

    async def get_file_download_url(self, file_id: str, user_id: str) -> str:
        """获取文件下载URL"""
        # 获取文件记录
        result = await self.db.execute(
            select(UploadedFile).where(
                and_(UploadedFile.id == file_id, UploadedFile.user_id == user_id)
            )
        )
        file = result.scalar_one_or_none()
        if not file:
            raise NotFoundError("文件不存在或无权限访问")

        if file.upload_status != FileStatus.COMPLETED:
            raise ValidationError("文件未上传完成")

        # 记录访问日志
        await self._log_file_access(file.id, user_id, "download")

        # 如果文件是公开的且有storage_url，直接返回
        if file.is_public and file.storage_url:
            return file.storage_url

        # 生成预签名下载URL
        try:
            tos_service = TOSService()
            download_url = await tos_service.generate_presigned_download_url(
                file.storage_key,
                expires_in=3600  # 1小时有效期
            )
            return download_url
        except Exception as e:
            logger.error(f"生成下载URL失败: {e}")
            # 如果TOS不可用，返回存储URL或API端点
            return file.storage_url or f"/api/v1/upload/files/{file_id}/download"

    async def get_file_download_info(self, file_id: str, user_id: str, expires_hours: int = 1) -> Dict[str, Any]:
        """获取文件下载信息"""
        # 获取文件记录
        result = await self.db.execute(
            select(UploadedFile).where(
                and_(UploadedFile.id == file_id, UploadedFile.user_id == user_id)
            )
        )
        file = result.scalar_one_or_none()
        if not file:
            raise NotFoundError("文件不存在或无权限访问")

        if file.upload_status != FileStatus.COMPLETED:
            raise ValidationError("文件未上传完成")

        # 记录访问日志
        await self._log_file_access(file.id, user_id, "get_download_url")

        # 生成下载URL
        expires_in = expires_hours * 3600  # 转换为秒
        try:
            tos_service = TOSService()
            download_url = await tos_service.generate_presigned_download_url(
                file.storage_key,
                expires_in=expires_in
            )
        except Exception as e:
            logger.error(f"生成下载URL失败: {e}")
            # 如果TOS不可用，使用存储URL或API端点
            download_url = file.storage_url or f"/api/v1/upload/files/{file_id}/download"

        return {
            "file_id": file_id,
            "filename": file.original_filename,
            "file_size": file.file_size,
            "file_type": file.file_type.value,
            "download_url": download_url,
            "expires_at": datetime.utcnow() + timedelta(seconds=expires_in),
            "mime_type": file.mime_type
        }
