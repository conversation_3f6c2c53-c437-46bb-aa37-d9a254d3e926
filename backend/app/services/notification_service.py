"""
通知服务 - 简化版本
专注于自动续费和订阅到期通知
"""
import logging
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.header import Header
from typing import Dict, Optional
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from app.models.order import Subscription, SubscriptionPlan
from app.models.user import User

logger = logging.getLogger(__name__)

class NotificationService:
    """通知服务"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        # 邮件配置 - 实际使用时应该从配置文件读取
        self.smtp_server = "smtp.qq.com"
        self.smtp_port = 587
        self.smtp_username = "<EMAIL>"
        self.smtp_password = "your_password"
        self.from_email = "<EMAIL>"
        self.from_name = "订阅系统"
        
    async def send_expiry_notification(self, subscription: Subscription):
        """发送到期提醒通知"""
        try:
            # 获取用户信息
            user = await self._get_user(subscription.user_id)
            if not user or not user.email:
                logger.warning(f"用户 {subscription.user_id} 邮箱不存在，跳过到期提醒")
                return
                
            # 获取套餐信息
            plan = await self._get_plan(subscription.plan_id)
            if not plan:
                logger.error(f"套餐 {subscription.plan_id} 不存在")
                return
                
            # 计算剩余天数
            days_remaining = (subscription.end_date - datetime.utcnow()).days
            
            # 发送邮件
            subject = f"【重要提醒】您的{plan.plan_name}套餐即将到期"
            content = self._build_expiry_email_content(
                user_name=user.full_name or user.email,
                plan_name=plan.plan_name,
                expiry_date=subscription.end_date.strftime("%Y-%m-%d"),
                days_remaining=days_remaining
            )
            
            await self._send_email(user.email, subject, content)
            logger.info(f"到期提醒发送成功 - 订阅ID: {subscription.id}")
            
        except Exception as e:
            logger.error(f"发送到期提醒失败 - 订阅ID: {subscription.id}, 错误: {e}")
            
    async def send_renewal_notification(self, subscription: Subscription):
        """发送续费提醒通知"""
        try:
            # 获取用户信息
            user = await self._get_user(subscription.user_id)
            if not user or not user.email:
                logger.warning(f"用户 {subscription.user_id} 邮箱不存在，跳过续费提醒")
                return
                
            # 获取套餐信息
            plan = await self._get_plan(subscription.plan_id)
            if not plan:
                logger.error(f"套餐 {subscription.plan_id} 不存在")
                return
                
            # 计算剩余天数
            days_remaining = (subscription.end_date - datetime.utcnow()).days
            
            # 发送邮件
            subject = f"续费提醒：{plan.plan_name}套餐即将到期"
            content = self._build_renewal_email_content(
                user_name=user.full_name or user.email,
                plan_name=plan.plan_name,
                expiry_date=subscription.end_date.strftime("%Y-%m-%d"),
                days_remaining=days_remaining,
                renewal_url=f"/subscriptions/{subscription.id}/renew"
            )
            
            await self._send_email(user.email, subject, content)
            logger.info(f"续费提醒发送成功 - 订阅ID: {subscription.id}")
            
        except Exception as e:
            logger.error(f"发送续费提醒失败 - 订阅ID: {subscription.id}, 错误: {e}")
            
    async def send_auto_renewal_failed_notification(self, subscription: Subscription, error_message: str):
        """发送自动续费失败通知"""
        try:
            # 获取用户信息
            user = await self._get_user(subscription.user_id)
            if not user or not user.email:
                logger.warning(f"用户 {subscription.user_id} 邮箱不存在，跳过续费失败通知")
                return
                
            # 获取套餐信息
            plan = await self._get_plan(subscription.plan_id)
            if not plan:
                logger.error(f"套餐 {subscription.plan_id} 不存在")
                return
            
            # 发送邮件
            subject = f"自动续费失败：{plan.plan_name}套餐需要您的关注"
            content = self._build_renewal_failed_email_content(
                user_name=user.full_name or user.email,
                plan_name=plan.plan_name,
                error_message=error_message,
                renewal_url=f"/subscriptions/{subscription.id}/renew"
            )
            
            await self._send_email(user.email, subject, content)
            logger.info(f"自动续费失败通知发送成功 - 订阅ID: {subscription.id}")
            
        except Exception as e:
            logger.error(f"发送自动续费失败通知失败 - 订阅ID: {subscription.id}, 错误: {e}")
    
    async def send_payment_success_notification(self, subscription: Subscription, payment_amount: float):
        """发送支付成功通知"""
        try:
            # 获取用户信息
            user = await self._get_user(subscription.user_id)
            if not user or not user.email:
                logger.warning(f"用户 {subscription.user_id} 邮箱不存在，跳过支付成功通知")
                return
                
            # 获取套餐信息
            plan = await self._get_plan(subscription.plan_id)
            if not plan:
                logger.error(f"套餐 {subscription.plan_id} 不存在")
                return
            
            # 发送邮件
            subject = f"支付成功：{plan.plan_name}套餐已续费"
            content = self._build_payment_success_email_content(
                user_name=user.full_name or user.email,
                plan_name=plan.plan_name,
                payment_amount=payment_amount,
                new_expiry_date=subscription.end_date.strftime("%Y-%m-%d")
            )
            
            await self._send_email(user.email, subject, content)
            logger.info(f"支付成功通知发送成功 - 订阅ID: {subscription.id}")
            
        except Exception as e:
            logger.error(f"发送支付成功通知失败 - 订阅ID: {subscription.id}, 错误: {e}")
    
    async def _get_user(self, user_id: str) -> Optional[User]:
        """获取用户信息"""
        result = await self.db.execute(
            select(User).where(User.id == user_id)
        )
        return result.scalar_one_or_none()
        
    async def _get_plan(self, plan_id: str) -> Optional[SubscriptionPlan]:
        """获取套餐信息"""
        result = await self.db.execute(
            select(SubscriptionPlan).where(SubscriptionPlan.id == plan_id)
        )
        return result.scalar_one_or_none()
    
    async def _send_email(self, to_email: str, subject: str, content: str) -> bool:
        """发送邮件"""
        try:
            # 创建邮件对象
            msg = MIMEMultipart()
            msg['From'] = f"{self.from_name} <{self.from_email}>"
            msg['To'] = to_email
            msg['Subject'] = Header(subject, 'utf-8')
            
            # 添加邮件内容
            msg.attach(MIMEText(content, 'html', 'utf-8'))
            
            # 发送邮件（简化版本，实际使用时需要配置真实的SMTP）
            logger.info(f"模拟发送邮件到 {to_email}: {subject}")
            return True
            
            # 真实发送邮件的代码（需要配置SMTP）
            # with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
            #     server.starttls()
            #     server.login(self.smtp_username, self.smtp_password)
            #     server.sendmail(self.from_email, [to_email], msg.as_string())
            # return True
            
        except Exception as e:
            logger.error(f"邮件发送失败: {to_email}, 错误: {e}")
            return False
    
    def _build_expiry_email_content(self, user_name: str, plan_name: str, expiry_date: str, days_remaining: int) -> str:
        """构建到期提醒邮件内容"""
        return f"""
        <html>
        <body>
            <h2>订阅到期提醒</h2>
            <p>尊敬的 {user_name}，</p>
            <p>您的 <strong>{plan_name}</strong> 套餐将在 <strong>{days_remaining}</strong> 天后到期。</p>
            <p>到期时间：<strong>{expiry_date}</strong></p>
            <p>为了避免服务中断，请及时续费。</p>
            <p><a href="/subscriptions/renew" style="background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">立即续费</a></p>
            <hr>
            <p>如有疑问，请联系客服。</p>
            <p>此邮件由系统自动发送，请勿回复。</p>
        </body>
        </html>
        """
    
    def _build_renewal_email_content(self, user_name: str, plan_name: str, expiry_date: str, days_remaining: int, renewal_url: str) -> str:
        """构建续费提醒邮件内容"""
        return f"""
        <html>
        <body>
            <h2>续费提醒</h2>
            <p>尊敬的 {user_name}，</p>
            <p>您的 <strong>{plan_name}</strong> 套餐将在 <strong>{days_remaining}</strong> 天后（{expiry_date}）到期。</p>
            <p>为了确保服务的连续性，建议您提前续费。</p>
            <p><a href="{renewal_url}" style="background-color: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">点击续费</a></p>
            <hr>
            <p>如有疑问，请联系客服。</p>
            <p>此邮件由系统自动发送，请勿回复。</p>
        </body>
        </html>
        """
    
    def _build_renewal_failed_email_content(self, user_name: str, plan_name: str, error_message: str, renewal_url: str) -> str:
        """构建自动续费失败邮件内容"""
        return f"""
        <html>
        <body>
            <h2>自动续费失败通知</h2>
            <p>尊敬的 {user_name}，</p>
            <p>很抱歉，您的 <strong>{plan_name}</strong> 套餐自动续费失败。</p>
            <p>失败原因：<strong>{error_message}</strong></p>
            <p>为了避免服务中断，请尽快手动续费：</p>
            <p><a href="{renewal_url}" style="background-color: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">立即续费</a></p>
            <p><strong>重要提醒：</strong>自动续费功能已被暂时禁用，续费成功后您可以重新启用。</p>
            <hr>
            <p>如有疑问，请联系客服。</p>
            <p>此邮件由系统自动发送，请勿回复。</p>
        </body>
        </html>
        """
    
    def _build_payment_success_email_content(self, user_name: str, plan_name: str, payment_amount: float, new_expiry_date: str) -> str:
        """构建支付成功邮件内容"""
        return f"""
        <html>
        <body>
            <h2>支付成功通知</h2>
            <p>尊敬的 {user_name}，</p>
            <p>您的 <strong>{plan_name}</strong> 套餐续费成功！</p>
            <p>支付金额：<strong>¥{payment_amount}</strong></p>
            <p>新的到期时间：<strong>{new_expiry_date}</strong></p>
            <p>感谢您的支持！</p>
            <hr>
            <p>如有疑问，请联系客服。</p>
            <p>此邮件由系统自动发送，请勿回复。</p>
        </body>
        </html>
        """
