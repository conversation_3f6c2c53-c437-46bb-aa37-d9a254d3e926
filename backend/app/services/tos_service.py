"""
火山引擎TOS对象存储服务
"""

import secrets
import string
from datetime import datetime
from typing import Dict, Any

try:
    import tos
    TOS_AVAILABLE = True
except ImportError:
    TOS_AVAILABLE = False
    print("警告: TOS SDK未安装，将使用模拟模式")

class TOSService:
    """TOS对象存储服务"""
    
    def __init__(self):
        # TOS配置 - 直接硬编码
        self.access_key = "AKLTODVlODc5NjRhOGFkNDY0MTgwZjg0ZmQ5ZDYxOTc0MGM"
        self.secret_key = "WlRCaU1ERTJZVFl4WWpKaE5Ea3hNV0ppT0dZNE5qVTJOV1UyWVRnNVpXSQ=="
        self.bucket = "kgeo"
        self.region = "cn-beijing"
        self.endpoint = "tos-cn-shanghai.volces.com"
        
        # 初始化客户端
        self.client = None
        self._init_client()
    
    def _init_client(self):
        """初始化TOS客户端"""
        if not TOS_AVAILABLE:
            print("警告: TOS SDK未安装，将使用模拟模式")
            self.client = None
            return

        try:
            self.client = tos.TosClientV2(
                region=self.region,
                ak=self.access_key,
                sk=self.secret_key,
                endpoint=self.endpoint
            )
            print("TOS客户端初始化成功")
        except Exception as e:
            raise Exception(f"TOS客户端初始化失败: {e}")
    
    def generate_storage_key(self, user_id: str, filename: str, file_type: str) -> str:
        """生成存储键"""
        timestamp = datetime.utcnow().strftime("%Y%m%d/%H%M%S")
        random_suffix = ''.join(secrets.choice(string.ascii_lowercase + string.digits) for _ in range(8))
        safe_filename = "".join(c for c in filename if c.isalnum() or c in ".-_")
        return f"{file_type}/{user_id}/{timestamp}/{random_suffix}_{safe_filename}"
    
    async def upload_file(self, storage_key: str, file_data: bytes, mime_type: str) -> Dict[str, str]:
        """上传文件到TOS（仅使用TOS，不使用本地存储）"""
        if not self.client:
            raise Exception("TOS客户端未初始化，无法上传文件")

        try:
            # 使用真实的TOS上传
            response = self.client.put_object(
                bucket=self.bucket,
                key=storage_key,
                content=file_data,
                content_type=mime_type
            )

            # 构建访问URL
            url = f"https://{self.bucket}.{self.endpoint}/{storage_key}"

            return {
                'url': url,
                'etag': response.etag,
                'request_id': response.request_id,
                'storage_type': 'tos'
            }
        except Exception as e:
            raise Exception(f"TOS上传失败: {e}")
    

    async def delete_file(self, storage_key: str) -> bool:
        """删除文件（仅从TOS删除）"""
        if not self.client:
            raise Exception("TOS客户端未初始化，无法删除文件")

        try:
            # 从TOS删除
            self.client.delete_object(
                bucket=self.bucket,
                key=storage_key
            )
            return True
        except Exception as e:
            raise Exception(f"TOS删除失败: {e}")
    
    async def generate_presigned_upload_url(self, storage_key: str, expires_in: int = 3600) -> str:
        """生成预签名上传URL"""
        if self.client:
            try:
                url = self.client.pre_signed_url(
                    http_method='PUT',
                    bucket=self.bucket,
                    key=storage_key,
                    expires=expires_in
                )
                return url
            except Exception as e:
                print(f"生成预签名URL失败: {e}")
        
        # 本地存储不支持预签名URL，返回普通上传端点
        return f"/api/v1/upload/file"
    
    async def generate_presigned_download_url(self, storage_key: str, expires_in: int = 3600) -> str:
        """生成预签名下载URL"""
        if self.client:
            try:
                url = self.client.pre_signed_url(
                    http_method='GET',
                    bucket=self.bucket,
                    key=storage_key,
                    expires=expires_in
                )
                return url
            except Exception as e:
                print(f"生成预签名下载URL失败: {e}")
        
        # 本地存储使用相对路径
        return f"/uploads/{storage_key}"
    
    def get_file_url(self, storage_key: str, is_public: bool = False) -> str:
        """获取文件访问URL"""
        if self.client:
            try:
                # 生成预签名下载URL，有效期24小时
                url = self.client.pre_signed_url(
                    http_method='GET',
                    bucket=self.bucket,
                    key=storage_key,
                    expires=86400  # 24小时
                )
                print(f"成功生成预签名URL: {storage_key}")
                return url
            except Exception as e:
                print(f"生成预签名URL失败 - storage_key: {storage_key}, error: {e}")
                # 如果预签名失败，返回正确的API端点
                # 注意：这里需要一个特殊的媒体文件ID，但我们只有storage_key
                # 暂时返回直接URL，如果还是不行，需要实现专门的媒体文件下载端点
                return f"https://{self.bucket}.{self.endpoint}/{storage_key}"
        else:
            print(f"TOS客户端未初始化，使用备用URL: {storage_key}")
            # 如果TOS不可用，返回直接URL
            return f"https://{self.bucket}.{self.endpoint}/{storage_key}"
    
    async def test_connection(self) -> Dict[str, Any]:
        """测试TOS连接"""
        if not self.client:
            return {
                'success': False,
                'error': 'TOS客户端未初始化',
                'storage_type': 'local'
            }
        
        try:
            # 测试列出存储桶
            response = self.client.list_buckets()
            
            # 检查目标存储桶是否存在
            bucket_exists = any(bucket.name == self.bucket for bucket in response.buckets)
            
            return {
                'success': True,
                'bucket_count': len(response.buckets),
                'target_bucket_exists': bucket_exists,
                'storage_type': 'tos'
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'storage_type': 'local'
            }

# 全局TOS服务实例
tos_service = TOSService()
