from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, text
from app.models.user import User, VerificationCode, UserStatus, UserRoleEnum
from app.models.permission import UserRole, Role
from app.schemas.auth import *
from app.core.security import *
from app.exceptions import AuthenticationError, ValidationError, BusinessLogicError
from app.core.redis import redis_client
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
import asyncio
import json

class AuthService:
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def register_user(self, request: UserRegisterRequest) -> Dict[str, Any]:
        """用户注册"""
        # 验证邮箱是否已存在
        existing_user = await self.db.execute(
            select(User).where(User.email == request.email)
        )
        if existing_user.scalar_one_or_none():
            raise ValidationError("邮箱已存在")
        
        # 验证手机号是否已存在（如果提供）
        if request.phone:
            existing_phone = await self.db.execute(
                select(User).where(User.phone == request.phone)
            )
            if existing_phone.scalar_one_or_none():
                raise ValidationError("手机号已存在")
        
        # 验证验证码
        await self._verify_verification_code(request.email, request.verification_code, "register")
        
        # 处理推荐码
        referred_by_user = None
        if request.referral_code:
            referred_by_user = await self.db.execute(
                select(User).where(User.referral_code == request.referral_code)
            )
            referred_by_user = referred_by_user.scalar_one_or_none()
            if not referred_by_user:
                raise ValidationError("推荐码无效")
        
        # 生成用户专属推荐码
        user_referral_code = await self._generate_unique_referral_code()
        
        # 创建用户
        new_user = User(
            email=request.email,
            full_name=request.full_name,
            phone=request.phone,
            password_hash=get_password_hash(request.password),
            status=UserStatus.ACTIVE,
            email_verified=True,  # 通过验证码验证后直接设为已验证
            referral_code=user_referral_code,
            # referred_by=referred_by_user.id if referred_by_user else None  # 暂时注释，数据库中不存在此字段
        )
        
        self.db.add(new_user)
        await self.db.flush()  # 获取用户ID

        # 如果有推荐码，创建推荐关系记录
        if referred_by_user:
            try:
                # 检查是否存在user_referrals表
                await self.db.execute(text("SELECT 1 FROM user_referrals LIMIT 1"))

                # 创建推荐关系记录
                await self.db.execute(
                    text("""
                        INSERT INTO user_referrals (id, referrer_user_id, referred_user_id, referral_code, status, created_at)
                        VALUES (gen_random_uuid(), :referrer_id, :referred_id, :referral_code, 'active', CURRENT_TIMESTAMP)
                    """),
                    {
                        "referrer_id": str(referred_by_user.id),
                        "referred_id": str(new_user.id),
                        "referral_code": request.referral_code
                    }
                )

                # 更新推广链接统计数据
                try:
                    from app.services.referral_link_service import ReferralLinkService
                    referral_service = ReferralLinkService(self.db)
                    await referral_service.update_referral_link_stats(request.referral_code)
                    print(f"✅ 推广链接统计数据已更新: {request.referral_code}")
                except Exception as stats_error:
                    print(f"⚠️  更新推广链接统计失败（不影响注册）: {str(stats_error)}")

            except Exception as e:
                # 如果推荐表不存在或插入失败，记录日志但不影响注册流程
                print(f"创建推荐关系失败（不影响注册）: {str(e)}")

        # 分配默认角色
        from app.services.permission_service import PermissionService
        permission_service = PermissionService(self.db)

        # 根据请求的角色分配相应角色，默认为普通用户
        role_code = "regular_user"
        if hasattr(request, 'role') and request.role:
            role_mapping = {
                "ENTERPRISE_USER": "enterprise_user",
                "CHANNEL_USER": "channel_user",
                "AGENT_USER": "agent_user"
            }
            role_code = role_mapping.get(request.role, "regular_user")

        # 异步调用角色分配
        await permission_service.assign_role_to_user(str(new_user.id), role_code)

        await self.db.commit()
        
        return {
            "user": {
                "id": str(new_user.id),
                "email": new_user.email,
                "full_name": new_user.full_name,
                "phone": new_user.phone,
                "status": new_user.status.value,
                "email_verified": new_user.email_verified,
                "phone_verified": new_user.phone_verified,
                "referral_code": new_user.referral_code,
                "roles": [],
                "created_at": new_user.created_at.isoformat()
            },
            "referral_info": {
                "agent_name": referred_by_user.full_name if referred_by_user else None,
                "referral_established": bool(referred_by_user)
            }
        }
    
    async def login_user(self, request: UserLoginRequest) -> Dict[str, Any]:
        """用户登录"""
        try:
            print(f"🔐 开始登录用户: {request.email}")

            # 查找用户
            result = await self.db.execute(
                select(User).where(User.email == request.email)
            )
            users = result.scalars().all()
            print(f"📊 查询到 {len(users)} 个用户")

            # 检查是否有多个用户使用相同邮箱
            if len(users) > 1:
                print(f"❌ 发现多个用户使用相同邮箱: {len(users)}")
                raise AuthenticationError(f"系统错误：发现 {len(users)} 个用户使用相同邮箱 {request.email}，请联系管理员")

            user = users[0] if users else None

            if not user:
                print("❌ 用户不存在")
                raise AuthenticationError("邮箱或密码错误")

            print(f"✅ 找到用户: {user.email}, 状态: {user.status}")

            if not verify_password(request.password, user.password_hash):
                print("❌ 密码验证失败")
                raise AuthenticationError("邮箱或密码错误")

            print("✅ 密码验证成功")

            if user.status != UserStatus.ACTIVE:
                print(f"❌ 用户状态异常: {user.status}")
                raise AuthenticationError("账号已被禁用")

            print("✅ 用户状态正常")

        except AuthenticationError:
            raise
        except Exception as e:
            print(f"❌ 登录过程中发生未知错误: {e}")
            import traceback
            traceback.print_exc()
            raise AuthenticationError("登录失败")
        
        # 获取用户角色（修复后的异步查询）
        try:
            print(f"🔍 开始获取用户角色: {user.email}")
            user_roles_result = await self.db.execute(
                select(UserRole, Role).join(Role, UserRole.role_id == Role.id).where(
                    and_(
                        UserRole.user_id == user.id,
                        UserRole.role_status == "active"
                    )
                )
            )
            user_roles = user_roles_result.fetchall()
            roles = [role.Role.role_code for role in user_roles]
            print(f"✅ 获取到用户角色: {roles}")

            # 如果没有角色，给一个默认角色
            if not roles:
                roles = ["regular_user"]
                print(f"⚠️ 用户无角色，使用默认角色: {roles}")

        except Exception as e:
            print(f"❌ 获取用户角色失败: {e}")
            import traceback
            traceback.print_exc()
            # 如果获取角色失败，使用默认角色
            roles = ["regular_user"]
        
        # 更新最后登录时间
        user.last_login_at = datetime.utcnow()
        await self.db.commit()
        
        # 生成令牌
        token_data = {"sub": str(user.id), "email": user.email, "roles": roles}
        access_token = create_access_token(token_data)
        refresh_token = create_refresh_token(token_data)
        
        return {
            "access_token": access_token,
            "refresh_token": refresh_token,
            "token_type": "bearer",
            "expires_in": settings.access_token_expire_minutes * 60,
            "refresh_expires_in": settings.refresh_token_expire_minutes * 60,
            "user": {
                "id": str(user.id),
                "email": user.email,
                "full_name": user.full_name,
                "phone": user.phone,
                "roles": roles,
                "last_login_at": user.last_login_at.isoformat(),
                "profile_completed": len(roles) > 0  # 简单判断是否完善了角色信息
            }
        }
    
    async def send_verification_code(self, request: SendVerificationCodeRequest) -> Dict[str, Any]:
        """发送验证码"""
        # 检查发送频率限制
        cache_key = f"verification_code_sent:{request.email}:{request.code_type}"
        if await redis_client.exists(cache_key):
            raise BusinessLogicError("验证码发送过于频繁，请稍后再试")
        
        # 生成验证码
        code = generate_verification_code()
        expires_at = datetime.utcnow() + timedelta(minutes=10)  # 10分钟有效期
        
        # 保存验证码到数据库
        verification_code = VerificationCode(
            email=request.email,
            code=code,
            code_type=request.code_type,
            expires_at=expires_at
        )
        self.db.add(verification_code)
        await self.db.commit()
        
        # 设置发送频率限制（1分钟）
        await redis_client.setex(cache_key, 60, "sent")

        # 发送邮件
        try:
            from app.services.email_service import email_service
            email_result = await email_service.send_verification_code(
                request.email, code, request.code_type
            )

            if not email_result.get("success"):
                # 如果邮件发送失败，删除数据库记录和缓存
                await self.db.delete(verification_code)
                await self.db.commit()
                await redis_client.delete(cache_key)
                raise BusinessLogicError(f"邮件发送失败: {email_result.get('message', '未知错误')}")

        except Exception as e:
            # 如果邮件发送失败，删除数据库记录和缓存
            await self.db.delete(verification_code)
            await self.db.commit()
            try:
                await redis_client.delete(cache_key)
            except Exception:
                pass  # Redis删除失败不影响主流程
            raise BusinessLogicError(f"发送验证码失败: {str(e)}")

        return {
            "message": "验证码已发送",
            "expires_in": 600,  # 10分钟
            "next_send_time": 60,  # 1分钟后可再次发送
            "masked_email": self._mask_email(request.email)
        }
    
    async def verify_code(self, request: VerifyCodeRequest) -> Dict[str, Any]:
        """验证码校验"""
        await self._verify_verification_code(request.email, request.code, request.code_type)
        
        # 生成临时验证令牌
        verification_token = create_access_token(
            {"email": request.email, "verified": True, "code_type": request.code_type},
            expires_delta=timedelta(minutes=10)
        )
        
        return {
            "verified": True,
            "verification_token": verification_token,
            "expires_in": 600,
            "message": "验证成功"
        }
    
    async def refresh_token(self, request: RefreshTokenRequest) -> Dict[str, Any]:
        """刷新令牌"""
        payload = verify_token(request.refresh_token, "refresh")
        if not payload:
            raise AuthenticationError("刷新令牌无效或已过期")
        
        # 验证用户是否仍然有效
        user_id = payload.get("sub")
        result = await self.db.execute(
            select(User).where(User.id == user_id)
        )
        user = result.scalar_one_or_none()
        
        if not user or user.status != UserStatus.ACTIVE:
            raise AuthenticationError("用户账号无效")
        
        # 获取最新角色信息（修复后的异步查询）
        try:
            user_roles_result = await self.db.execute(
                select(UserRole, Role).join(Role, UserRole.role_id == Role.id).where(
                    and_(
                        UserRole.user_id == user.id,
                        UserRole.role_status == "active"
                    )
                )
            )
            user_roles = user_roles_result.fetchall()
            roles = [role.Role.role_code for role in user_roles]

            # 如果没有角色，给一个默认角色
            if not roles:
                roles = ["regular_user"]

        except Exception as e:
            print(f"获取用户角色失败: {e}")
            # 如果获取角色失败，使用默认角色
            roles = ["regular_user"]
        
        # 生成新令牌
        token_data = {"sub": str(user.id), "email": user.email, "roles": roles}
        new_access_token = create_access_token(token_data)
        new_refresh_token = create_refresh_token(token_data)
        
        return {
            "access_token": new_access_token,
            "refresh_token": new_refresh_token,
            "token_type": "bearer",
            "expires_in": settings.access_token_expire_minutes * 60,
            "refresh_expires_in": settings.refresh_token_expire_minutes * 60
        }
    
    async def logout_user(self, user_id: str, request: LogoutRequest) -> Dict[str, Any]:
        """用户登出"""
        # TODO: 实现令牌黑名单机制
        # 这里可以将令牌加入Redis黑名单
        
        logout_time = datetime.utcnow()
        
        return {
            "message": "登出成功",
            "logout_time": logout_time.isoformat()
        }
    
    async def reset_password(self, request: ResetPasswordRequest) -> Dict[str, Any]:
        """重置密码"""
        # 验证重置令牌
        payload = verify_token(request.verification_token, "access")
        if not payload or payload.get("email") != request.email or not payload.get("verified"):
            raise AuthenticationError("验证令牌无效或已过期")
        
        # 查找用户
        result = await self.db.execute(
            select(User).where(User.email == request.email)
        )
        user = result.scalar_one_or_none()
        
        if not user:
            raise ValidationError("用户不存在")
        
        # 更新密码
        user.password_hash = get_password_hash(request.new_password)
        await self.db.commit()
        
        # TODO: 使所有令牌失效
        
        return {
            "message": "密码重置成功，请重新登录",
            "reset_time": datetime.utcnow().isoformat()
        }
    
    async def _verify_verification_code(self, email: str, code: str, code_type: str):
        """验证验证码"""
        result = await self.db.execute(
            select(VerificationCode).where(
                and_(
                    VerificationCode.email == email,
                    VerificationCode.code == code,
                    VerificationCode.code_type == code_type,
                    VerificationCode.is_used == False,
                    VerificationCode.expires_at > datetime.utcnow()
                )
            )
        )
        verification_code = result.scalar_one_or_none()

        if not verification_code:
            raise ValidationError("验证码无效或已过期")

        # 标记验证码为已使用
        verification_code.is_used = True
        await self.db.commit()
    
    async def _generate_unique_referral_code(self) -> str:
        """生成唯一推荐码"""
        max_attempts = 10
        for _ in range(max_attempts):
            code = generate_referral_code()
            result = await self.db.execute(
                select(User).where(User.referral_code == code)
            )
            if not result.scalar_one_or_none():
                return code
        
        raise BusinessLogicError("生成推荐码失败，请重试")
    
    def _mask_email(self, email: str) -> str:
        """邮箱脱敏"""
        local, domain = email.split('@')
        if len(local) <= 2:
            masked_local = local[0] + '*' * (len(local) - 1)
        else:
            masked_local = local[:2] + '*' * (len(local) - 4) + local[-2:]
        return f"{masked_local}@{domain}"
