from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, desc
from sqlalchemy.orm import selectinload
from app.models.user import User
from app.models.referral import UserReferral, ReferralLink
from app.models.agent import Agent
from app.schemas.referral_link import *
from app.exceptions import *
from app.services.config_service import ConfigService
from typing import Dict, Any, List
from datetime import datetime, timedelta, timezone
from decimal import Decimal
import secrets
import string
import uuid


class ReferralLinkService:
    def __init__(self, db: AsyncSession):
        self.db = db
        self.config_service = ConfigService(db)

    async def create_referral_link(self, user_id: str, request: ReferralLinkCreateRequest) -> Dict[str, Any]:
        """创建推广链接"""
        # 获取代理商信息
        agent = await self._get_agent_by_user_id(user_id)

        # 获取用户信息（获取推荐码）
        user = await self._get_user_by_id(user_id)

        # 从配置获取基础URL
        config = await self.config_service.get_referral_config()
        base_url = config["base_url"]

        # 生成推广链接
        if request.target_page:
            referral_url = f"{base_url}/{request.target_page}?ref={user.referral_code}"
        else:
            # 首页情况
            referral_url = f"{base_url}?ref={user.referral_code}"

        if request.campaign_name:
            referral_url += f"&campaign={request.campaign_name}"

        # 创建推广链接记录
        referral_link = ReferralLink(
            user_id=uuid.UUID(user_id),
            agent_id=agent.id,
            link_name=request.link_name,
            referral_code=user.referral_code,
            target_page=request.target_page,
            campaign_name=request.campaign_name,
            description=request.description,
            referral_url=referral_url
        )

        self.db.add(referral_link)
        await self.db.commit()
        await self.db.refresh(referral_link)

        # 获取统计数据
        stats = await self._get_referral_stats(user.referral_code)

        return {
            "id": str(referral_link.id),
            "link_name": referral_link.link_name,
            "referral_code": referral_link.referral_code,
            "referral_url": referral_link.referral_url,
            "target_page": referral_link.target_page,
            "campaign_name": referral_link.campaign_name,
            "description": referral_link.description,
            "total_clicks": referral_link.total_clicks,
            "total_registrations": stats["total_registrations"],
            "total_conversions": stats["total_conversions"],
            "total_commission": stats["total_commission"],
            "is_active": referral_link.is_active,
            "created_at": referral_link.created_at,
            "last_used_at": referral_link.last_used_at
        }

    async def update_referral_link(self, user_id: str, link_id: str, request: ReferralLinkUpdateRequest) -> Dict[
        str, Any]:
        """修改推广链接"""
        # 获取推广链接
        referral_link = await self._get_referral_link_by_id(user_id, link_id)

        # 获取用户信息（获取推荐码）
        user = await self._get_user_by_id(user_id)

        # 更新字段
        updated_fields = []
        if request.link_name is not None:
            referral_link.link_name = request.link_name
            updated_fields.append("link_name")

        if request.target_page is not None:
            referral_link.target_page = request.target_page
            updated_fields.append("target_page")

        if request.campaign_name is not None:
            referral_link.campaign_name = request.campaign_name
            updated_fields.append("campaign_name")

        if request.description is not None:
            referral_link.description = request.description
            updated_fields.append("description")

        if request.is_active is not None:
            referral_link.is_active = request.is_active
            updated_fields.append("is_active")

        # 如果目标页面或活动名称发生变化，需要重新生成URL
        if request.target_page is not None or request.campaign_name is not None:
            config = await self.config_service.get_referral_config()
            base_url = config["base_url"]

            if referral_link.target_page:
                referral_url = f"{base_url}/{referral_link.target_page}?ref={referral_link.referral_code}"
            else:
                # 首页情况
                referral_url = f"{base_url}?ref={referral_link.referral_code}"
            if referral_link.campaign_name:
                referral_url += f"&campaign={referral_link.campaign_name}"

            referral_link.referral_url = referral_url
            updated_fields.append("referral_url")

        # 更新时间戳
        referral_link.updated_at = datetime.now(timezone.utc)

        await self.db.commit()
        await self.db.refresh(referral_link)

        # 获取统计数据
        stats = await self._get_referral_stats(user.referral_code)

        return {
            "id": str(referral_link.id),
            "link_name": referral_link.link_name,
            "referral_code": referral_link.referral_code,
            "referral_url": referral_link.referral_url,
            "target_page": referral_link.target_page,
            "campaign_name": referral_link.campaign_name,
            "description": referral_link.description,
            "total_clicks": referral_link.total_clicks,
            "total_registrations": referral_link.total_registrations,
            "total_conversions": referral_link.total_conversions,
            "total_commission": str(referral_link.total_commission),
            "is_active": referral_link.is_active,
            "created_at": referral_link.created_at.isoformat(),
            "last_used_at": referral_link.last_used_at.isoformat() if referral_link.last_used_at else None,
            "updated_fields": updated_fields
        }

    async def toggle_referral_link_status(self, user_id: str, link_id: str) -> Dict[str, Any]:
        """切换推广链接状态（启用/停用）"""
        # 获取推广链接
        referral_link = await self._get_referral_link_by_id(user_id, link_id)

        # 切换状态
        old_status = referral_link.is_active
        referral_link.is_active = not old_status
        referral_link.updated_at = datetime.now(timezone.utc)

        await self.db.commit()
        await self.db.refresh(referral_link)

        return {
            "id": str(referral_link.id),
            "link_name": referral_link.link_name,
            "referral_code": referral_link.referral_code,
            "referral_url": referral_link.referral_url,
            "target_page": referral_link.target_page,
            "campaign_name": referral_link.campaign_name,
            "description": referral_link.description,
            "total_clicks": referral_link.total_clicks,
            "total_registrations": referral_link.total_registrations,
            "total_conversions": referral_link.total_conversions,
            "total_commission": str(referral_link.total_commission),
            "is_active": referral_link.is_active,
            "created_at": referral_link.created_at.isoformat(),
            "last_used_at": referral_link.last_used_at.isoformat() if referral_link.last_used_at else None
        }

    async def delete_referral_link(self, user_id: str, link_id: str) -> Dict[str, Any]:
        """删除推广链接"""
        # 获取推广链接
        referral_link = await self._get_referral_link_by_id(user_id, link_id)

        # 记录删除的链接信息
        deleted_info = {
            "id": str(referral_link.id),
            "link_name": referral_link.link_name,
            "referral_code": referral_link.referral_code,
            "target_page": referral_link.target_page,
            "total_clicks": referral_link.total_clicks,
            "total_registrations": referral_link.total_registrations,
            "total_conversions": referral_link.total_conversions,
            "total_commission": str(referral_link.total_commission),
            "created_at": referral_link.created_at.isoformat(),
            "deleted_at": datetime.now(timezone.utc).isoformat()
        }

        # 删除推广链接
        await self.db.delete(referral_link)
        await self.db.commit()

        return deleted_info

    async def get_referral_links(self, user_id: str, page: int = 1, size: int = 20) -> Dict[str, Any]:
        """获取推广链接列表"""
        # 获取用户信息
        user = await self._get_user_by_id(user_id)

        # 获取统计数据
        stats = await self._get_referral_stats(user.referral_code)

        # 获取最近推荐用户详情
        recent_referrals = await self.get_referral_details(user.referral_code, limit=5)

        # 从数据库获取用户的推广链接
        offset = (page - 1) * size
        result = await self.db.execute(
            select(ReferralLink)
            .where(ReferralLink.user_id == uuid.UUID(user_id))
            .order_by(ReferralLink.created_at.desc())
            .offset(offset)
            .limit(size)
        )
        referral_links = result.scalars().all()

        # 获取总数
        count_result = await self.db.execute(
            select(func.count(ReferralLink.id))
            .where(ReferralLink.user_id == uuid.UUID(user_id))
        )
        total = count_result.scalar() or 0

        # 构建响应数据
        links = []
        for link in referral_links:
            links.append({
                "id": str(link.id),
                "link_name": link.link_name,
                "referral_code": link.referral_code,
                "referral_url": link.referral_url,
                "target_page": link.target_page,
                "campaign_name": link.campaign_name,
                "description": link.description,
                "total_clicks": link.total_clicks,
                "total_registrations": stats["total_registrations"],
                "total_conversions": stats["total_conversions"],
                "total_commission": stats["total_commission"],
                "is_active": link.is_active,
                "created_at": link.created_at,
                "last_used_at": link.last_used_at
            })

        # 计算汇总统计
        active_links = sum(1 for link in links if link.get("is_active", True))
        total_clicks = sum(link.get("total_clicks", 0) for link in links)

        summary = {
            "total_links": total,
            "active_links": active_links,
            "total_clicks": total_clicks,
            "total_registrations": stats["total_registrations"],
            "total_conversions": stats["total_conversions"],
            "total_commission": stats["total_commission"],
            "conversion_rate": stats["conversion_rate"],
            "recent_referrals": recent_referrals
        }

        return {
            "items": links,
            "total": total,
            "page": page,
            "size": size,
            "pages": (total + size - 1) // size,
            "summary": summary
        }

    async def _create_default_referral_links(self, user_id: str, referral_code: str) -> None:
        """为用户创建默认的推广链接"""
        try:
            # 获取配置
            config = await self.config_service.get_referral_config()
            base_url = config["base_url"]
            default_pages = config["default_target_pages"]

            # 获取代理商信息
            agent = await self._get_agent_by_user_id(user_id)

            # 为每个默认页面创建推广链接
            for page_config in default_pages:
                if page_config['page']:
                    referral_url = f"{base_url}/{page_config['page']}?ref={referral_code}"
                else:
                    # 首页情况
                    referral_url = f"{base_url}?ref={referral_code}"

                referral_link = ReferralLink(
                    user_id=uuid.UUID(user_id),
                    agent_id=agent.id,
                    link_name=page_config["name"],
                    referral_code=referral_code,
                    target_page=page_config["page"],
                    description=page_config["description"],
                    referral_url=referral_url
                )

                self.db.add(referral_link)

            await self.db.commit()

        except Exception as e:
            await self.db.rollback()
            print(f"创建默认推广链接失败: {e}")

    async def _get_agent_by_user_id(self, user_id: str) -> Agent:
        """根据用户ID获取代理商信息"""
        result = await self.db.execute(
            select(Agent).where(Agent.user_id == user_id)
        )
        agent = result.scalar_one_or_none()
        if not agent:
            raise NotFoundError("代理商信息不存在")
        return agent

    async def _get_user_by_id(self, user_id: str) -> User:
        """根据用户ID获取用户信息"""
        result = await self.db.execute(
            select(User).where(User.id == user_id)
        )
        user = result.scalar_one_or_none()
        if not user:
            raise NotFoundError("用户信息不存在")
        return user

    async def _get_referral_link_by_id(self, user_id: str, link_id: str) -> ReferralLink:
        """根据用户ID和链接ID获取推广链接"""
        result = await self.db.execute(
            select(ReferralLink).where(
                and_(
                    ReferralLink.id == uuid.UUID(link_id),
                    ReferralLink.user_id == uuid.UUID(user_id)
                )
            )
        )
        referral_link = result.scalar_one_or_none()
        if not referral_link:
            raise NotFoundError("推广链接不存在或无权限访问")
        return referral_link

    async def track_referral_click(
            self,
            referral_code: str,
            target_page: str,
            campaign: str = None,
            user_agent: str = None,
            ip_address: str = None
    ) -> Dict[str, Any]:
        """跟踪推广链接点击"""
        try:
            print(
                f"🔍 开始跟踪推广链接点击: referral_code={referral_code}, target_page='{target_page}', campaign={campaign}")
            # 验证推荐码是否存在
            user_result = await self.db.execute(
                select(User).where(User.referral_code == referral_code)
            )
            user = user_result.scalar_one_or_none()
            if not user:
                print(f"❌ 推荐码不存在: {referral_code}")
                raise NotFoundError("推荐码不存在")

            print(f"✅ 找到用户: {user.email}, 推荐码: {user.referral_code}")

            # 首先尝试查找精确匹配的推广链接
            link_result = await self.db.execute(
                select(ReferralLink).where(
                    and_(
                        ReferralLink.referral_code == referral_code,
                        ReferralLink.target_page == target_page,
                        ReferralLink.is_active == True
                    )
                )
            )
            referral_link = link_result.scalar_one_or_none()

            if referral_link:
                print(f"✅ 找到精确匹配的推广链接: {referral_link.link_name}")
            else:
                print(f"⚠️  没有找到精确匹配 (target_page='{target_page}')，尝试查找同一推荐码的任何活跃链接")
                # 如果没有找到精确匹配，尝试查找同一推荐码的任何活跃链接
                fallback_result = await self.db.execute(
                    select(ReferralLink).where(
                        and_(
                            ReferralLink.referral_code == referral_code,
                            ReferralLink.is_active == True
                        )
                    ).limit(1)
                )
                referral_link = fallback_result.scalar_one_or_none()

                if referral_link:
                    print(f"✅ 找到备用推广链接: {referral_link.link_name}, target_page='{referral_link.target_page}'")

            if referral_link:
                old_clicks = referral_link.total_clicks
                # 更新链接的点击统计和最后使用时间
                referral_link.total_clicks += 1
                referral_link.last_used_at = datetime.now(timezone.utc)
                await self.db.commit()


            # 这里可以添加更详细的点击记录逻辑
            # 比如记录到专门的点击统计表中

            return {
                "referral_code": referral_code,
                "target_page": target_page,
                "campaign": campaign,
                "tracked_at": datetime.now(timezone.utc).isoformat(),
                "link_found": referral_link is not None
            }

        except Exception as e:
            import logging
            logging.error(f"跟踪推广链接点击失败: {str(e)}")
            raise

    async def _get_referral_stats(self, referral_code: str) -> Dict[str, Any]:
        """获取推荐统计数据"""
        try:
            # 首先确保表存在（一次性检查）
            await self._ensure_user_referrals_table_exists()
            # 获取推荐用户总数
            referral_count_result = await self.db.execute(
                select(func.count(UserReferral.id)).where(
                    UserReferral.referral_code == referral_code
                )
            )
            total_registrations = referral_count_result.scalar() or 0

            # 获取转化用户数（有订单的用户）
            conversion_count_result = await self.db.execute(
                select(func.count(UserReferral.id)).where(
                    and_(
                        UserReferral.referral_code == referral_code,
                        UserReferral.first_order_id.isnot(None)
                    )
                )
            )
            total_conversions = conversion_count_result.scalar() or 0

            # 获取总佣金（基于转化用户数计算）
            config = await self.config_service.get_referral_config()
            commission_per_conversion = config["commission_per_conversion"]
            total_commission = Decimal(str(total_conversions)) * commission_per_conversion

            # 获取最后使用时间
            last_used_result = await self.db.execute(
                select(func.max(UserReferral.created_at)).where(
                    UserReferral.referral_code == referral_code
                )
            )
            last_used_at = last_used_result.scalar()

            # 计算转化率
            conversion_rate = (total_conversions / total_registrations * 100) if total_registrations > 0 else 0.0

            return {
                "total_registrations": total_registrations,
                "total_conversions": total_conversions,
                "total_commission": total_commission,
                "last_used_at": last_used_at,
                "conversion_rate": round(conversion_rate, 2)
            }

        except Exception as e:
            # 如果查询失败，记录错误并返回空数据
            print(f"查询推荐统计失败: {e}")
            return {
                "total_registrations": 0,
                "total_conversions": 0,
                "total_commission": Decimal("0.00"),
                "last_used_at": None,
                "conversion_rate": 0.0
            }

    async def get_referral_details(self, referral_code: str, limit: int = 10) -> List[Dict[str, Any]]:
        """获取推荐详情列表"""
        try:
            # 获取最近的推荐记录
            result = await self.db.execute(
                select(UserReferral, User.email)
                .join(User, UserReferral.referred_user_id == User.id)
                .where(UserReferral.referral_code == referral_code)
                .order_by(UserReferral.created_at.desc())
                .limit(limit)
            )

            referrals = []
            for referral, email in result.fetchall():
                referrals.append({
                    "id": str(referral.id),
                    "referred_user_email": email,
                    "referral_source": referral.referral_source,
                    "conversion_status": referral.conversion_status,
                    "first_order_amount": float(referral.first_order_amount) if referral.first_order_amount else None,
                    "created_at": referral.created_at.isoformat() if referral.created_at else None,
                    "converted_at": referral.converted_at.isoformat() if referral.converted_at else None
                })

            return referrals

        except Exception as e:
            print(f"获取推荐详情失败: {e}")
            return []

    async def create_referral_record(self, referrer_user_id: str, referred_user_id: str,
                                     referral_code: str, referral_source: str = None) -> bool:
        """创建推荐记录"""
        try:
            # 检查是否已存在推荐记录
            existing_result = await self.db.execute(
                select(UserReferral).where(
                    and_(
                        UserReferral.referred_user_id == referred_user_id,
                        UserReferral.referrer_user_id == referrer_user_id
                    )
                )
            )

            if existing_result.scalar_one_or_none():
                return False  # 已存在推荐记录

            # 创建新的推荐记录
            referral = UserReferral(
                referred_user_id=referred_user_id,
                referrer_user_id=referrer_user_id,
                referral_code=referral_code,
                referral_source=referral_source or "referral_link",
                conversion_status="registered"
            )

            self.db.add(referral)
            await self.db.commit()
            return True

        except Exception as e:
            await self.db.rollback()
            print(f"创建推荐记录失败: {e}")
            return False

    async def _ensure_user_referrals_table_exists(self) -> bool:
        """确保user_referrals表存在"""
        try:
            from sqlalchemy import text

            # 检查表是否存在
            result = await self.db.execute(text(
                "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'user_referrals')"
            ))
            exists = result.scalar()

            if not exists:
                print("⚠️  user_referrals表不存在，请执行数据库迁移：")
                print("   python -m alembic upgrade head")
                raise Exception("user_referrals表不存在，请先执行数据库迁移")

            return True

        except Exception as e:
            print(f"检查user_referrals表时出错: {e}")
            raise e

    async def update_referral_link_stats(self, referral_code: str) -> bool:
        """更新推广链接的统计数据"""
        try:
            print(f"🔄 开始更新推广链接统计数据: referral_code={referral_code}")

            # 获取最新的统计数据
            stats = await self._get_referral_stats(referral_code)

            # 更新所有使用该推荐码的推广链接
            from sqlalchemy import text
            update_result = await self.db.execute(
                text("""
                    UPDATE referral_links
                    SET total_registrations = :total_registrations,
                        total_conversions = :total_conversions,
                        total_commission = :total_commission,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE referral_code = :referral_code
                """),
                {
                    "referral_code": referral_code,
                    "total_registrations": stats["total_registrations"],
                    "total_conversions": stats["total_conversions"],
                    "total_commission": float(stats["total_commission"])
                }
            )

            await self.db.commit()

            rows_updated = update_result.rowcount
            print(f"✅ 已更新 {rows_updated} 个推广链接的统计数据")
            print(
                f"   注册数: {stats['total_registrations']}, 转化数: {stats['total_conversions']}, 佣金: {stats['total_commission']}")

            return True

        except Exception as e:
            print(f"❌ 更新推广链接统计数据失败: {e}")
            await self.db.rollback()
            return False

    async def get_agent_customers(
            self,
            agent_user_id: str,
            page: int = 1,
            size: int = 20,
            status: str = None,
            level: str = None,
            source: str = None,
            search: str = None,
            start_date = None,
            end_date = None,
            sort_by: str = "register_date",
            sort_order: str = "desc"
    ) -> Dict[str, Any]:
        """获取代理商的客户列表"""
        try:
            from sqlalchemy import and_, or_, func
            from app.models.company import Company
            from app.models.order import Order

            print(f"🔍 获取代理商客户列表: agent_user_id={agent_user_id}")

            # 构建基础查询 - 获取推荐的用户
            query = (
                select(
                    User.id,
                    User.full_name,
                    User.email,
                    User.phone,
                    User.created_at,
                    User.status,
                    User.last_login_at,
                    UserReferral.referral_code,
                    UserReferral.created_at.label('referral_date'),
                    Company.company_name
                )
                .join(UserReferral, User.id == UserReferral.referred_user_id)
                .outerjoin(Company, User.id == Company.user_id)
                .where(
                    and_(
                        UserReferral.referrer_user_id == agent_user_id,
                        UserReferral.conversion_status.in_(['registered', 'converted'])
                    )
                )
            )

            # 添加筛选条件
            conditions = []

            if status:
                if status == 'active':
                    conditions.append(User.status == 'active')
                elif status == 'inactive':
                    conditions.append(User.status == 'inactive')
                elif status == 'trial':
                    conditions.append(User.status == 'active')  # 试用用户也是active状态

            if search:
                search_pattern = f"%{search}%"
                conditions.append(
                    or_(
                        User.full_name.ilike(search_pattern),
                        User.email.ilike(search_pattern),
                        Company.company_name.ilike(search_pattern)
                    )
                )

            # 添加时间筛选
            if start_date:
                conditions.append(User.created_at >= start_date)
            if end_date:
                conditions.append(User.created_at <= end_date)

            if conditions:
                query = query.where(and_(*conditions))

            # 获取总数
            count_query = select(func.count()).select_from(query.subquery())
            count_result = await self.db.execute(count_query)
            total = count_result.scalar() or 0

            # 添加排序
            if sort_by == "last_login":
                if sort_order.lower() == "desc":
                    query = query.order_by(User.last_login_at.desc())
                else:
                    query = query.order_by(User.last_login_at.asc())
            elif sort_by == "name":
                if sort_order.lower() == "desc":
                    query = query.order_by(User.full_name.desc())
                else:
                    query = query.order_by(User.full_name.asc())
            else:  # 默认按注册时间排序
                if sort_order.lower() == "desc":
                    query = query.order_by(User.created_at.desc())
                else:
                    query = query.order_by(User.created_at.asc())

            # 添加分页
            query = query.offset((page - 1) * size).limit(size)

            # 执行查询
            result = await self.db.execute(query)
            rows = result.fetchall()

            # 转换数据格式
            customers = []
            for row in rows:
                # 获取订单统计（简化版本，避免复杂子查询）
                order_result = await self.db.execute(
                    select(
                        func.sum(Order.final_amount).label('total_spent'),
                        func.count(Order.id).label('orders_count')
                    )
                    .where(
                        and_(
                            Order.user_id == row.id,
                            Order.order_status.in_(['completed', 'paid'])
                        )
                    )
                )
                order_stats = order_result.fetchone()
                total_spent = float(order_stats.total_spent or 0)
                orders_count = order_stats.orders_count or 0

                # 计算客户等级
                if total_spent > 10000:
                    level = 'VIP'
                elif total_spent > 1000:
                    level = '活跃'
                elif total_spent > 0:
                    level = '试用'
                else:
                    level = '未激活'

                # 确定状态
                if row.status == 'active':
                    status = 'active'
                elif row.status == 'inactive':
                    status = 'inactive'
                elif row.status == 'banned':
                    status = 'inactive'
                else:
                    status = 'trial'

                customer = {
                    "id": str(row.id),
                    "name": row.full_name or "未设置",
                    "email": row.email,
                    "phone": self._mask_phone(row.phone) if row.phone else "未设置",
                    "company": row.company_name or "未设置",
                    "register_date": row.created_at.strftime("%Y/%m/%d") if row.created_at else "",
                    "status": status,
                    "total_spent": total_spent,
                    "last_login": row.last_login_at.strftime("%Y-%m-%d") if row.last_login_at else "从未登录",
                    "source": f"推荐码: {row.referral_code}",
                    "level": level,
                    "orders_count": orders_count,
                    "avatar": None
                }
                customers.append(customer)

            # 获取统计数据
            statistics = await self._get_customers_statistics(agent_user_id)

            return {
                "items": customers,
                "pagination": {
                    "total": total,
                    "page": page,
                    "size": size,
                    "pages": (total + size - 1) // size
                },
                "statistics": statistics
            }

        except Exception as e:
            print(f"❌ 获取代理商客户列表失败: {e}")
            import traceback
            print(f"错误堆栈: {traceback.format_exc()}")
            raise e

    def _mask_phone(self, phone: str) -> str:
        """手机号脱敏处理"""
        if not phone or len(phone) < 7:
            return phone
        return phone[:3] + "****" + phone[-4:]

    async def _get_customers_statistics(self, agent_user_id: str) -> Dict[str, Any]:
        """获取客户统计数据"""
        try:
            from sqlalchemy import and_, func, case
            from app.models.order import Order

            # 获取推荐用户的基础统计
            stats_query = (
                select(
                    func.count(func.distinct(User.id)).label('total_customers'),
                    func.count(func.distinct(case((User.status == 'active', User.id)))).label('active_customers'),
                    func.count(func.distinct(case((User.status == 'inactive', User.id)))).label('inactive_customers'),
                    func.count(func.distinct(case((User.status == 'banned', User.id)))).label('banned_customers')
                )
                .join(UserReferral, User.id == UserReferral.referred_user_id)
                .where(
                    and_(
                        UserReferral.referrer_user_id == agent_user_id,
                        UserReferral.conversion_status.in_(['registered', 'converted'])
                    )
                )
            )

            result = await self.db.execute(stats_query)
            row = result.fetchone()

            # 获取收入统计
            revenue_query = (
                select(
                    func.sum(Order.final_amount).label('total_revenue'),
                    func.avg(Order.final_amount).label('avg_spent_per_customer')
                )
                .join(UserReferral, User.id == UserReferral.referred_user_id)
                .join(Order, User.id == Order.user_id)
                .where(
                    and_(
                        UserReferral.referrer_user_id == agent_user_id,
                        UserReferral.conversion_status.in_(['registered', 'converted']),
                        Order.order_status.in_(['completed', 'paid'])
                    )
                )
            )

            revenue_result = await self.db.execute(revenue_query)
            revenue_row = revenue_result.fetchone()

            if row:
                return {
                    "total_customers": row.total_customers or 0,
                    "active_customers": row.active_customers or 0,
                    "inactive_customers": row.inactive_customers or 0,
                    "trial_customers": row.banned_customers or 0,  # 将banned用户当作trial
                    "total_revenue": float(revenue_row.total_revenue or 0) if revenue_row else 0.0,
                    "avg_spent_per_customer": float(revenue_row.avg_spent_per_customer or 0) if revenue_row else 0.0
                }
            else:
                return {
                    "total_customers": 0,
                    "active_customers": 0,
                    "inactive_customers": 0,
                    "trial_customers": 0,
                    "total_revenue": 0.0,
                    "avg_spent_per_customer": 0.0
                }

        except Exception as e:
            print(f"❌ 获取客户统计失败: {e}")
            return {
                "total_customers": 0,
                "active_customers": 0,
                "inactive_customers": 0,
                "trial_customers": 0,
                "total_revenue": 0.0,
                "avg_spent_per_customer": 0.0
            }

    async def get_customer_detail(self, agent_user_id: str, customer_id: str) -> Dict[str, Any]:
        """获取客户详情"""
        try:
            from sqlalchemy import and_, func
            from app.models.company import Company
            from app.models.order import Order

            # 验证客户是否属于该代理商
            verify_query = (
                select(UserReferral.id)
                .where(
                    and_(
                        UserReferral.referrer_user_id == agent_user_id,
                        UserReferral.referred_user_id == customer_id,
                        UserReferral.conversion_status.in_(['registered', 'converted'])
                    )
                )
            )

            verify_result = await self.db.execute(verify_query)
            if not verify_result.fetchone():
                raise NotFoundError("客户不存在或不属于当前代理商")

            # 获取客户详细信息
            detail_query = (
                select(
                    User.id,
                    User.full_name,
                    User.email,
                    User.phone,
                    User.created_at,
                    User.status,
                    User.last_login_at,
                    UserReferral.referral_code,
                    UserReferral.created_at.label('referral_date'),
                    Company.company_name
                )
                .join(UserReferral, User.id == UserReferral.referred_user_id)
                .outerjoin(Company, User.id == Company.user_id)
                .where(
                    and_(
                        UserReferral.referrer_user_id == agent_user_id,
                        UserReferral.referred_user_id == customer_id,
                        UserReferral.conversion_status.in_(['registered', 'converted'])
                    )
                )
            )

            result = await self.db.execute(detail_query)
            row = result.fetchone()

            if not row:
                raise NotFoundError("客户详情不存在")

            # 获取订单统计
            order_result = await self.db.execute(
                select(
                    func.sum(Order.final_amount).label('total_spent'),
                    func.count(Order.id).label('orders_count'),
                    func.max(Order.created_at).label('last_order_date')
                )
                .where(
                    and_(
                        Order.user_id == row.id,
                        Order.order_status.in_(['completed', 'paid'])
                    )
                )
            )
            order_stats = order_result.fetchone()

            # 确定客户等级
            total_spent = float(order_stats.total_spent or 0)
            if total_spent > 10000:
                level = 'VIP'
            elif total_spent > 1000:
                level = '活跃'
            elif total_spent > 0:
                level = '试用'
            else:
                level = '未激活'

            # 确定状态
            if row.status == 'active':
                status = 'active'
            elif row.status == 'inactive':
                status = 'inactive'
            elif row.status == 'banned':
                status = 'inactive'
            else:
                status = 'trial'

            return {
                "id": str(row.id),
                "name": row.full_name or "未设置",
                "email": row.email,
                "phone": self._mask_phone(row.phone) if row.phone else "未设置",
                "company": row.company_name or "未设置",
                "register_date": row.created_at.strftime("%Y/%m/%d") if row.created_at else "",
                "status": status,
                "total_spent": total_spent,
                "last_login": row.last_login_at.strftime("%Y-%m-%d") if row.last_login_at else "从未登录",
                "referral_code": row.referral_code,
                "referral_date": row.referral_date.strftime("%Y/%m/%d") if row.referral_date else "",
                "level": level,
                "orders_count": order_stats.orders_count or 0,
                "last_order_date": order_stats.last_order_date.strftime(
                    "%Y-%m-%d") if order_stats.last_order_date else "无订单",
                "avatar": None
            }

        except Exception as e:
            print(f"❌ 获取客户详情失败: {e}")
            raise e

    async def get_customers_statistics(self, agent_user_id: str) -> Dict[str, Any]:
        """获取客户统计数据（独立接口）"""
        return await self._get_customers_statistics(agent_user_id)

    async def get_referral_links_statistics(self, agent_user_id: str) -> Dict[str, Any]:
        """获取推广链接统计数据"""
        try:
            from sqlalchemy import func, and_, case

            # 获取推广链接统计
            stats_query = (
                select(
                    func.count(ReferralLink.id).label('total_links'),
                    func.count(case((ReferralLink.is_active == True, ReferralLink.id))).label('active_links'),
                    func.sum(ReferralLink.total_clicks).label('total_clicks'),
                    func.sum(ReferralLink.total_conversions).label('total_conversions')
                )
                .where(ReferralLink.agent_user_id == agent_user_id)
            )

            result = await self.db.execute(stats_query)
            row = result.fetchone()

            if row:
                total_clicks = row.total_clicks or 0
                total_conversions = row.total_conversions or 0
                conversion_rate = (total_conversions / total_clicks * 100) if total_clicks > 0 else 0

                return {
                    "total_links": row.total_links or 0,
                    "active_links": row.active_links or 0,
                    "total_clicks": total_clicks,
                    "total_conversions": total_conversions,
                    "conversion_rate": round(conversion_rate, 2),
                    "total_commission": 0  # 暂时设为0，后续可以根据业务需求计算
                }
            else:
                return {
                    "total_links": 0,
                    "active_links": 0,
                    "total_clicks": 0,
                    "total_conversions": 0,
                    "conversion_rate": 0,
                    "total_commission": 0
                }

        except Exception as e:
            print(f"❌ 获取推广链接统计失败: {e}")
            return {
                "total_links": 0,
                "active_links": 0,
                "total_clicks": 0,
                "total_conversions": 0,
                "conversion_rate": 0,
                "total_commission": 0
            }
