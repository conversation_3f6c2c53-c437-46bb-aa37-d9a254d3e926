"""
Geo监控中心核心服务
"""
from typing import List, Dict, Any

from ...core.geo.config import AIModelType
from ...services.ai.prompt_templates.base_prompts import PromptType
from ...models.geo.geo_analysis import (
    AnalysisRequest, 
    AnalysisResult, 
    CompleteAnalysisResult
)
from .multi_ai_service import MultiAIService
from .data_structure_service import DataStructureService

class GeoService:
    """Geo监控中心核心服务"""
    
    def __init__(self):
        self.multi_ai = MultiAIService()
        self.data_structure = DataStructureService()
    
    async def search_keyword_sources(
        self, 
        keyword: str, 
        ai_model: AIModelType
    ) -> CompleteAnalysisResult:
        """关键词搜索并提取信息来源"""
        try:
            # 基础搜索分析
            search_request = AnalysisRequest(
                keyword=keyword,
                ai_model=ai_model,
                prompt_type=PromptType.BASIC_SEARCH
            )
            search_result = await self.multi_ai.analyze_single(search_request)
            
            if not search_result.success:
                return CompleteAnalysisResult(
                    keyword=keyword,
                    ai_model=ai_model,
                    raw_analysis=search_result,
                    structured_data=None,
                    success=False,
                    error=f"关键词搜索失败: {search_result.error}"
                )
            
            # 解析搜索结果
            structured_data = self.data_structure.parse_ai_json_result(
                search_result.content, keyword, "keyword_search"
            )
            
            return CompleteAnalysisResult(
                keyword=keyword,
                ai_model=ai_model,
                raw_analysis=search_result,
                structured_data=structured_data,
                success=True
            )
            
        except Exception as e:
            return CompleteAnalysisResult(
                keyword=keyword,
                ai_model=ai_model,
                raw_analysis=None,
                structured_data=None,
                success=False,
                error=str(e)
            )
    
    async def identify_platforms(
        self, 
        source_content: str, 
        ai_model: AIModelType
    ) -> CompleteAnalysisResult:
        """平台识别分析"""
        try:
            # 平台识别分析
            identify_request = AnalysisRequest(
                keyword="",  # 平台识别不需要关键词
                ai_model=ai_model,
                prompt_type=PromptType.PLATFORM_IDENTIFY,
                additional_params={"source_content": source_content}
            )
            identify_result = await self.multi_ai.analyze_single(identify_request)
            
            if not identify_result.success:
                return CompleteAnalysisResult(
                    keyword="",
                    ai_model=ai_model,
                    raw_analysis=identify_result,
                    structured_data=None,
                    success=False,
                    error=f"平台识别失败: {identify_result.error}"
                )
            
            # 解析识别结果
            structured_data = self.data_structure.parse_ai_json_result(
                identify_result.content, "", "platform_identify"
            )
            
            return CompleteAnalysisResult(
                keyword="",
                ai_model=ai_model,
                raw_analysis=identify_result,
                structured_data=structured_data,
                success=True
            )
            
        except Exception as e:
            return CompleteAnalysisResult(
                keyword="",
                ai_model=ai_model,
                raw_analysis=None,
                structured_data=None,
                success=False,
                error=str(e)
            )
    
    async def analyze_keyword_ranking(
        self, 
        keywords: List[str], 
        content: str, 
        ai_model: AIModelType
    ) -> CompleteAnalysisResult:
        """关键词排名分析"""
        try:
            # 关键词排名分析
            ranking_request = AnalysisRequest(
                keyword=",".join(keywords),
                ai_model=ai_model,
                prompt_type=PromptType.KEYWORD_RANKING,
                additional_params={
                    "keywords": keywords,
                    "content": content
                }
            )
            ranking_result = await self.multi_ai.analyze_single(ranking_request)
            
            if not ranking_result.success:
                return CompleteAnalysisResult(
                    keyword=",".join(keywords),
                    ai_model=ai_model,
                    raw_analysis=ranking_result,
                    structured_data=None,
                    success=False,
                    error=f"关键词排名分析失败: {ranking_result.error}"
                )
            
            # 解析排名结果
            structured_data = self.data_structure.parse_ai_json_result(
                ranking_result.content, ",".join(keywords), "keyword_ranking"
            )
            
            return CompleteAnalysisResult(
                keyword=",".join(keywords),
                ai_model=ai_model,
                raw_analysis=ranking_result,
                structured_data=structured_data,
                success=True
            )
            
        except Exception as e:
            return CompleteAnalysisResult(
                keyword=",".join(keywords),
                ai_model=ai_model,
                raw_analysis=None,
                structured_data=None,
                success=False,
                error=str(e)
            )
    
    async def analyze_platform_recommendations(
        self,
        keyword: str,
        platform_resources: str,
        ai_model: AIModelType
    ) -> CompleteAnalysisResult:
        """平台推荐分析"""
        try:
            # 平台推荐分析
            platform_request = AnalysisRequest(
                keyword=keyword,
                ai_model=ai_model,
                prompt_type=PromptType.PLATFORM_MATCH,
                additional_params={"platform_resources": platform_resources}
            )
            platform_result = await self.multi_ai.analyze_single(platform_request)

            if not platform_result.success:
                return CompleteAnalysisResult(
                    keyword=keyword,
                    ai_model=ai_model,
                    raw_analysis=platform_result,
                    structured_data=None,
                    success=False,
                    error=f"平台分析失败: {platform_result.error}"
                )

            # 解析平台推荐结果
            structured_data = self.data_structure.parse_ai_json_result(
                platform_result.content, keyword, "platform_recommendations"
            )

            return CompleteAnalysisResult(
                keyword=keyword,
                ai_model=ai_model,
                raw_analysis=platform_result,
                structured_data=structured_data,
                success=True
            )

        except Exception as e:
            return CompleteAnalysisResult(
                keyword=keyword,
                ai_model=ai_model,
                raw_analysis=None,
                structured_data=None,
                success=False,
                error=str(e)
            )

    async def analyze_account_recommendations(
        self,
        keyword: str,
        account_info: str,
        ai_model: AIModelType
    ) -> CompleteAnalysisResult:
        """账号推荐分析"""
        try:
            # 账号推荐分析
            account_request = AnalysisRequest(
                keyword=keyword,
                ai_model=ai_model,
                prompt_type=PromptType.ACCOUNT_MATCH,
                additional_params={"account_info": account_info}
            )
            account_result = await self.multi_ai.analyze_single(account_request)

            if not account_result.success:
                return CompleteAnalysisResult(
                    keyword=keyword,
                    ai_model=ai_model,
                    raw_analysis=account_result,
                    structured_data=None,
                    success=False,
                    error=f"账号分析失败: {account_result.error}"
                )

            # 解析账号推荐结果
            structured_data = self.data_structure.parse_ai_json_result(
                account_result.content, keyword, "account_recommendations"
            )

            return CompleteAnalysisResult(
                keyword=keyword,
                ai_model=ai_model,
                raw_analysis=account_result,
                structured_data=structured_data,
                success=True
            )

        except Exception as e:
            return CompleteAnalysisResult(
                keyword=keyword,
                ai_model=ai_model,
                raw_analysis=None,
                structured_data=None,
                success=False,
                error=str(e)
            )
    
    def get_available_models(self) -> List[Dict[str, Any]]:
        """获取可用的AI模型列表"""
        return self.multi_ai.get_available_models()

# 全局Geo服务实例 - 延迟初始化
_geo_service = None

def get_geo_service():
    """获取Geo服务实例（单例模式）"""
    global _geo_service
    if _geo_service is None:
        _geo_service = GeoService()
    return _geo_service

# 为了向后兼容，保留原来的变量名
geo_service = get_geo_service()
