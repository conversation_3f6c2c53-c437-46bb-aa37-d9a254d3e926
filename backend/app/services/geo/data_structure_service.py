"""
数据结构化服务（JSON解析服务）
"""
import json
import re
from typing import Dict, Any, Optional

from ...core.geo.exceptions import DataParsingException
from ...models.geo.geo_analysis import StructuredData

class DataStructureService:
    """数据结构化服务"""
    
    def parse_ai_json_result(
        self, 
        ai_content: str, 
        keyword: str, 
        data_type: str
    ) -> StructuredData:
        """解析AI返回的JSON结果"""
        try:
            # 尝试直接解析JSON
            json_content = self._extract_json_from_text(ai_content)
            
            if json_content:
                parsed_data = json.loads(json_content)
                
                # 验证JSON结构
                validated_data = self._validate_json_structure(parsed_data, data_type)
                
                return StructuredData(
                    content=validated_data,
                    data_type=data_type,
                    keyword=keyword,
                    success=True
                )
            else:
                # 如果没有找到JSON，尝试从文本中提取结构化信息
                fallback_data = self._extract_structured_info(ai_content, data_type)
                
                return StructuredData(
                    content=fallback_data,
                    data_type=data_type,
                    keyword=keyword,
                    success=True
                )
                
        except json.JSONDecodeError as e:
            return StructuredData(
                content={},
                data_type=data_type,
                keyword=keyword,
                success=False,
                error=f"JSON解析失败: {str(e)}"
            )
        except Exception as e:
            return StructuredData(
                content={},
                data_type=data_type,
                keyword=keyword,
                success=False,
                error=f"数据结构化失败: {str(e)}"
            )
    
    def _extract_json_from_text(self, text: str) -> Optional[str]:
        """从文本中提取JSON内容"""
        # 查找JSON代码块
        json_pattern = r'```json\s*(.*?)\s*```'
        match = re.search(json_pattern, text, re.DOTALL | re.IGNORECASE)
        
        if match:
            return match.group(1).strip()
        
        # 查找花括号包围的JSON
        brace_pattern = r'\{.*\}'
        match = re.search(brace_pattern, text, re.DOTALL)
        
        if match:
            return match.group(0).strip()
        
        return None
    
    def _validate_json_structure(self, data: Dict[str, Any], data_type: str) -> Dict[str, Any]:
        """验证JSON结构"""
        if not isinstance(data, dict):
            raise DataParsingException(data_type, "数据不是有效的字典格式")
        
        # 根据数据类型进行特定验证
        if data_type == "keyword_search":
            return self._validate_keyword_search(data)
        elif data_type == "platform_identify":
            return self._validate_platform_identify(data)
        elif data_type == "keyword_ranking":
            return self._validate_keyword_ranking(data)
        elif data_type == "platform_recommendations":
            return self._validate_platform_recommendations(data)
        elif data_type == "account_recommendations":
            return self._validate_account_recommendations(data)
        else:
            return data
    
    def _validate_keyword_search(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """验证关键词搜索数据"""
        required_fields = ["sources"]
        for field in required_fields:
            if field not in data:
                data[field] = []
        
        # 确保sources是列表
        if not isinstance(data["sources"], list):
            data["sources"] = []
        
        return data
    
    def _validate_platform_identify(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """验证平台识别数据"""
        if "platforms" not in data:
            data["platforms"] = []
        
        if not isinstance(data["platforms"], list):
            data["platforms"] = []
        
        return data
    
    def _validate_keyword_ranking(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """验证关键词排名数据"""
        if "keyword_rankings" not in data:
            data["keyword_rankings"] = []
        
        if not isinstance(data["keyword_rankings"], list):
            data["keyword_rankings"] = []
        
        return data
    
    def _validate_platform_recommendations(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """验证平台推荐数据"""
        if "platform_recommendations" not in data:
            data["platform_recommendations"] = []
        
        if not isinstance(data["platform_recommendations"], list):
            data["platform_recommendations"] = []
        
        return data
    
    def _validate_account_recommendations(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """验证账号推荐数据"""
        if "account_recommendations" not in data:
            data["account_recommendations"] = []
        
        if not isinstance(data["account_recommendations"], list):
            data["account_recommendations"] = []
        
        return data
    
    def _extract_structured_info(self, text: str, data_type: str) -> Dict[str, Any]:
        """从纯文本中提取结构化信息（备用方案）"""
        # 这是一个简单的备用方案，当AI没有返回JSON时使用
        return {
            "raw_text": text,
            "extracted": True,
            "data_type": data_type,
            "note": "从纯文本中提取的信息"
        }
