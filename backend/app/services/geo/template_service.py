"""
模板服务
"""
import time
from typing import Dict, Any, List
from pathlib import Path
from jinja2 import Environment, FileSystemLoader, select_autoescape, TemplateNotFound

from ...models.geo.template_config import (
    TemplateInfo, 
    TemplateContext, 
    RenderResult, 
    template_registry
)
from ...core.geo.exceptions import TemplateNotFoundException

class TemplateService:
    """模板服务"""
    
    def __init__(self, templates_dir: str = None):
        # 如果没有指定模板目录，使用backend目录
        if templates_dir is None:
            import os
            # 获取backend目录的绝对路径 (从app/services/geo向上3级到backend)
            backend_dir = Path(__file__).parent.parent.parent.parent
            self.templates_dir = backend_dir
        else:
            self.templates_dir = Path(templates_dir)

        self.cache = {}
        self.cache_enabled = True

        # 初始化Jinja2环境
        self.env = Environment(
            loader=FileSystemLoader(str(self.templates_dir)),
            autoescape=select_autoescape(['html', 'xml']),
            enable_async=True
        )
        
        # 注册自定义过滤器
        self._register_filters()
    
    def _register_filters(self):
        """注册自定义过滤器"""
        def format_number(value):
            """格式化数字"""
            try:
                num = float(value)
                if num >= 1000000:
                    return f"{num/1000000:.1f}M"
                elif num >= 1000:
                    return f"{num/1000:.1f}K"
                else:
                    return str(int(num))
            except (ValueError, TypeError):
                return str(value)
        
        def format_percentage(value):
            """格式化百分比"""
            try:
                num = float(value)
                return f"{num:.1f}%"
            except (ValueError, TypeError):
                return str(value)
    

        # 注册过滤器到环境
        self.env.filters['format_number'] = format_number
        self.env.filters['format_percentage'] = format_percentage
    async def render_template(
        self,
        template_name: str,
        context: TemplateContext
    ) -> RenderResult:
        """渲染模板"""
        start_time = time.time()
        
        try:
            # 检查缓存
            cache_key = f"{template_name}_{hash(str(context.data))}"
            if self.cache_enabled and cache_key in self.cache:
                cached_result = self.cache[cache_key]
                return RenderResult(
                    content=cached_result,
                    template_name=template_name,
                    success=True,
                    render_time=time.time() - start_time
                )
            
            # 获取模板信息
            template_info = template_registry.get_template(template_name)
            if not template_info:
                return RenderResult(
                    content="",
                    template_name=template_name,
                    success=False,
                    error=f"模板 {template_name} 未注册"
                )
            
            # 构建完整上下文
            full_context = self._build_context(template_info, context)
            
            # 加载并渲染模板
            template = self.env.get_template(template_info.file_path)
            rendered_content = await template.render_async(**full_context)
            
            # 缓存结果
            if self.cache_enabled:
                self.cache[cache_key] = rendered_content
            
            return RenderResult(
                content=rendered_content,
                template_name=template_name,
                success=True,
                render_time=time.time() - start_time
            )
            
        except TemplateNotFound:
            return RenderResult(
                content="",
                template_name=template_name,
                success=False,
                error=f"模板文件 {template_name} 不存在"
            )
        except Exception as e:
            return RenderResult(
                content="",
                template_name=template_name,
                success=False,
                error=f"模板渲染失败: {str(e)}"
            )
    
    async def get_raw_template(self, template_name: str) -> str:
        """获取原始模板内容"""
        try:
            template_info = template_registry.get_template(template_name)
            if not template_info:
                raise TemplateNotFoundException(template_name)
            
            template_path = self.templates_dir / template_info.file_path
            
            if not template_path.exists():
                raise TemplateNotFoundException(template_name)
            
            # 异步读取文件
            with open(template_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            return content
            
        except Exception as e:
            raise TemplateNotFoundException(template_name, str(e))
    
    def _build_context(self, template_info: TemplateInfo, context: TemplateContext) -> Dict[str, Any]:
        """构建完整的模板上下文"""
        full_context = {
            # 基础信息
            'title': context.title or template_info.title,
            'description': context.description or template_info.description,
            
            # 用户数据
            'data': context.data,
            
            # 资源文件
            'css_files': context.css_files or template_info.css_files,
            'js_files': context.js_files or template_info.js_files,
            
            # Meta标签
            'meta_tags': context.meta_tags or {},
            
            # 模板信息
            'template_name': template_info.name,
            'template_type': template_info.type.value,
            
            # 系统信息
            'system_name': 'Geo监控中心',
            'version': '1.0.0',
            'current_time': time.strftime('%Y-%m-%d %H:%M:%S')
        }
        
        return full_context
    
    async def render_template_async(self, template_name: str, data: dict) -> RenderResult:
        """异步版本的模板渲染"""
        start_time = time.time()

        try:
            # 获取模板信息
            template_info = template_registry.get_template(template_name)
            if not template_info:
                return RenderResult(
                    content="",
                    template_name=template_name,
                    success=False,
                    error=f"模板 {template_name} 未注册"
                )

            # 构建完整上下文
            context = TemplateContext(
                title=template_info.title,
                description=template_info.description,
                data=data
            )
            full_context = self._build_context(template_info, context)

            # 加载并渲染模板
            template = self.env.get_template(template_info.file_path)
            rendered_content = await template.render_async(**full_context)

            return RenderResult(
                content=rendered_content,
                template_name=template_name,
                success=True,
                render_time=time.time() - start_time
            )

        except TemplateNotFound:
            return RenderResult(
                content="",
                template_name=template_name,
                success=False,
                error=f"模板文件 {template_name} 不存在"
            )
        except Exception as e:
            return RenderResult(
                content="",
                template_name=template_name,
                success=False,
                error=f"模板渲染失败: {str(e)}"
            )

    def render_template(self, template_name: str, data: dict) -> RenderResult:
        """同步版本的模板渲染（用于测试）"""
        import asyncio

        # 创建新的事件循环来运行异步代码
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            result = loop.run_until_complete(self.render_template_async(template_name, data))
            loop.close()
            return result
        except Exception as e:
            return RenderResult(
                content="",
                template_name=template_name,
                success=False,
                error=f"模板渲染失败: {str(e)}"
            )

    def get_template_list(self) -> List[TemplateInfo]:
        """获取模板列表"""
        return template_registry.get_all_templates()

    def get_template(self, template_name: str) -> TemplateInfo:
        """获取单个模板信息"""
        return template_registry.get_template(template_name)
    
    def clear_cache(self):
        """清空缓存"""
        self.cache.clear()
    
    def set_cache_enabled(self, enabled: bool):
        """设置缓存开关"""
        self.cache_enabled = enabled
        if not enabled:
            self.clear_cache()

# 全局模板服务实例
template_service = TemplateService()
