"""
多AI模型分析服务
"""
import time
import asyncio
from typing import Dict, Any, List, Optional

from ...core.geo.config import geo_config, AIModelType
from ...core.geo.exceptions import AIModelNotAvailableException, AIAPIException
from ...services.ai.ai_adapters import ai_adapter_factory
from ...services.ai.prompt_templates import prompt_manager, PromptType
from ...models.geo.geo_analysis import AnalysisRequest, AnalysisResult

class MultiAIService:
    """多AI模型分析服务"""
    
    def __init__(self):
        self.adapters_cache = {}
    
    async def analyze_single(self, request: AnalysisRequest) -> AnalysisResult:
        """单个AI模型分析"""
        start_time = time.time()
        
        try:
            # 检查AI模型是否可用
            if not geo_config.is_model_available(request.ai_model):
                return AnalysisResult(
                    keyword=request.keyword,
                    ai_model=request.ai_model,
                    success=False,
                    content="",
                    sources=[],
                    response_time=time.time() - start_time,
                    error=f"AI模型 {request.ai_model.value} 不可用"
                )
            
            # 获取AI适配器
            adapter = await self._get_adapter(request.ai_model)
            
            # 构建提示词
            prompt = self._build_prompt(request)
            
            # 调用AI模型
            ai_response = await adapter.analyze_with_prompt(
                system_prompt="你是一位专业的SEO分析专家。",
                user_prompt=prompt
            )
            
            if not ai_response.success:
                return AnalysisResult(
                    keyword=request.keyword,
                    ai_model=request.ai_model,
                    success=False,
                    content="",
                    sources=[],
                    response_time=time.time() - start_time,
                    error=ai_response.error
                )
            
            # 提取信息来源
            sources = self._extract_sources(ai_response.content, request.prompt_type)
            
            return AnalysisResult(
                keyword=request.keyword,
                ai_model=request.ai_model,
                success=True,
                content=ai_response.content,
                sources=sources,
                response_time=ai_response.response_time or (time.time() - start_time),
                usage=ai_response.usage
            )
                
        except Exception as e:
            return AnalysisResult(
                keyword=request.keyword,
                ai_model=request.ai_model,
                success=False,
                content="",
                sources=[],
                response_time=time.time() - start_time,
                error=str(e)
            )
    
    async def analyze_batch(self, requests: List[AnalysisRequest]) -> List[AnalysisResult]:
        """批量AI模型分析"""
        tasks = [self.analyze_single(request) for request in requests]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理异常结果
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append(AnalysisResult(
                    keyword=requests[i].keyword,
                    ai_model=requests[i].ai_model,
                    success=False,
                    content="",
                    sources=[],
                    response_time=0,
                    error=str(result)
                ))
            else:
                processed_results.append(result)
        
        return processed_results
    
    async def _get_adapter(self, model_type: AIModelType):
        """获取AI适配器（带缓存）"""
        if model_type not in self.adapters_cache:
            config = geo_config.get_ai_model_config(model_type)
            if not config:
                raise AIModelNotAvailableException(
                    model_type.value, 
                    "模型配置不存在"
                )
            
            adapter = ai_adapter_factory.create_adapter(config)
            self.adapters_cache[model_type] = adapter
        
        return self.adapters_cache[model_type]
    
    def _build_prompt(self, request: AnalysisRequest) -> str:
        """构建提示词"""
        # 根据提示词类型决定是否附加信息来源请求
        add_source_request = (request.prompt_type == PromptType.BASIC_SEARCH)
        
        # 构建参数
        params = {"keyword": request.keyword}
        if request.additional_params:
            params.update(request.additional_params)
        
        # 获取提示词
        if add_source_request:
            return prompt_manager.get_ai_analysis_prompt(
                request.prompt_type, 
                **params
            )
        else:
            return prompt_manager.get_prompt(
                request.prompt_type,
                **params
            )
    
    def _extract_sources(self, content: str, prompt_type: PromptType) -> List[Dict[str, Any]]:
        """从AI响应中提取信息来源"""
        sources = []
        
        # 仅在基础搜索时提取来源信息
        if prompt_type == PromptType.BASIC_SEARCH:
            # 简单的URL提取逻辑（可以后续优化）
            import re
            urls = re.findall(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', content)
            
            for i, url in enumerate(urls):
                sources.append({
                    "title": f"信息来源 {i+1}",
                    "url": url,
                    "description": "AI分析提供的信息来源"
                })
        
        return sources
    
    def get_available_models(self) -> List[Dict[str, Any]]:
        """获取可用的AI模型列表"""
        available_models = geo_config.get_available_models()
        
        models_info = []
        for model_type, config in available_models.items():
            models_info.append({
                "model_type": model_type.value,
                "model_name": config.model_name,
                "model_id": config.model_id,
                "enabled": config.enabled,
                "cost_level": "中等"  # 可以根据实际情况调整
            })
        
        return models_info
