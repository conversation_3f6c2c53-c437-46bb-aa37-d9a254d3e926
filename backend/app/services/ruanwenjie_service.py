#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
软文街API集成服务

该服务用于集成软文街的API接口，包括：
1. 认证获取token
2. 获取软文资源列表
3. 提交软文发布订单
4. 获取违禁词列表
5. 处理订单状态推送
"""

import httpx
import json
import logging
import hashlib
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text, select, insert, update
from sqlalchemy.orm import selectinload

from app.config import settings
from app.exceptions import ValidationError, BusinessLogicError
from app.models.ruanwenjie import RuanwenjieMedia, RuanwenjieOrder, RuanwenjieOrderStatus


logger = logging.getLogger(__name__)


class RuanwenjieService:
    """软文街API服务"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.base_url = "https://api.ruanwen.la"
        self.api_key = "3b98c40be00c15f9ec69131076646eb7"  # 从文档中获取的API密钥
        self.token = None
        self.token_expires_at = None
        
    async def _get_stored_token(self) -> Optional[str]:
        """从数据库获取存储的token"""
        try:
            result = await self.db.execute(text("""
                SELECT token_value, expires_at 
                FROM api_tokens 
                WHERE provider = 'ruanwenjie' 
                AND expires_at > NOW()
                ORDER BY created_at DESC 
                LIMIT 1
            """))
            row = result.fetchone()
            if row:
                self.token = row.token_value
                self.token_expires_at = row.expires_at
                return row.token_value
            return None
        except Exception as e:
            logger.warning(f"获取存储的token失败: {e}")
            return None
    
    async def _store_token(self, token: str, expires_hours: int = 100):
        """存储token到数据库"""
        try:
            expires_at = datetime.utcnow() + timedelta(hours=expires_hours)
            await self.db.execute(text("""
                INSERT INTO api_tokens (id, provider, token_value, expires_at, created_at, updated_at)
                VALUES (gen_random_uuid(), 'ruanwenjie', :token, :expires_at, NOW(), NOW())
                ON CONFLICT (provider) DO UPDATE SET
                    token_value = :token,
                    expires_at = :expires_at,
                    updated_at = NOW()
            """), {
                "token": token,
                "expires_at": expires_at
            })
            await self.db.commit()
            self.token = token
            self.token_expires_at = expires_at
        except Exception as e:
            logger.error(f"存储token失败: {e}")
            await self.db.rollback()
    
    async def authenticate(self, mobile: str, password: str) -> str:
        """
        软文街认证获取token
        
        Args:
            mobile: 登录手机号
            password: 登录密码
            
        Returns:
            str: JWT token
        """
        url = f"{self.base_url}/api/auth/authenticate"
        
        data = {
            "mobile": mobile,
            "password": password,
            "identity": "advertiser",
            "captcha_token": "advertiser",
            "captcha": "advertiser",
            "api_key": self.api_key
        }
        
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(url, data=data)
                response.raise_for_status()
                
                result = response.json()
                
                if result.get("success") and result.get("status") == 200:
                    token = result["data"]["token"]
                    await self._store_token(token)
                    logger.info("软文街认证成功")
                    return token
                else:
                    raise BusinessLogicError(f"软文街认证失败: {result.get('message', '未知错误')}")
                    
        except httpx.HTTPError as e:
            logger.error(f"软文街认证请求失败: {e}")
            raise BusinessLogicError(f"软文街认证请求失败: {str(e)}")
        except Exception as e:
            logger.error(f"软文街认证异常: {e}")
            raise BusinessLogicError(f"软文街认证异常: {str(e)}")
    
    async def get_token(self) -> str:
        """获取有效的token"""
        # 先尝试从内存获取
        if self.token and self.token_expires_at and self.token_expires_at > datetime.utcnow():
            return self.token
        
        # 从数据库获取
        stored_token = await self._get_stored_token()
        if stored_token:
            return stored_token
        
        # 如果没有有效token，抛出异常要求重新认证
        raise BusinessLogicError("软文街token已过期，请重新认证")
    
    async def get_media_resources(self, page: int = 1, **filters) -> Dict[str, Any]:
        """
        获取软文媒体资源列表
        
        Args:
            page: 页码
            **filters: 筛选条件
                - taxonomy: 频道类型
                - platform: 综合门户
                - area: 区域
                - baidu: 新闻源 (0非新闻源、1百度新闻源)
                - in_level: 入口级别 (0不限、1首页入口、2频道首页、3栏目页面、4没有入口)
                - url_type: 链接类型
                
        Returns:
            Dict: 媒体资源列表和分页信息
        """
        token = await self.get_token()
        url = f"{self.base_url}/api/news_resource_2/data"
        
        params = {
            "token": token,
            "page": page
        }
        
        # 添加筛选条件
        for key, value in filters.items():
            if value is not None:
                params[key] = value
        
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.get(url, params=params)
                response.raise_for_status()
                
                result = response.json()
                
                if result.get("success") and result.get("status") == 200:
                    media_data = result["data"]

                    # 将媒体资源同步到数据库
                    await self._sync_media_to_database(media_data)

                    return {
                        "success": True,
                        "data": media_data,
                        "pagination": result["pagination"]
                    }
                else:
                    # 如果token过期，清除缓存的token
                    if result.get("status") == 401:
                        self.token = None
                        self.token_expires_at = None
                        raise BusinessLogicError("软文街token已过期，请重新认证")
                    
                    raise BusinessLogicError(f"获取媒体资源失败: {result.get('message', '未知错误')}")
                    
        except httpx.HTTPError as e:
            logger.error(f"获取媒体资源请求失败: {e}")
            raise BusinessLogicError(f"获取媒体资源请求失败: {str(e)}")
        except Exception as e:
            logger.error(f"获取媒体资源异常: {e}")
            raise BusinessLogicError(f"获取媒体资源异常: {str(e)}")
    
    async def create_order(self, title: str, content: str, resource_id: int, user_id: str) -> Dict[str, Any]:
        """
        创建软文发布订单

        Args:
            title: 文章标题
            content: 文章内容
            resource_id: 媒体资源ID
            user_id: 用户ID

        Returns:
            Dict: 订单创建结果
        """
        token = await self.get_token()
        url = "https://api.xinmeibao.com/api/news_order"  # 注意这里使用的是不同的域名
        
        data = {
            "token": token,
            "title": title,
            "content": content,
            "resource_id": resource_id
        }
        
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(url, data=data)
                response.raise_for_status()
                
                result = response.json()
                
                if result.get("success") and result.get("status") == 200:
                    order_id = result["order_id"]

                    # 将订单信息存储到数据库
                    await self._save_order_to_database(
                        external_order_id=order_id,
                        user_id=user_id,
                        title=title,
                        content=content,
                        resource_id=resource_id
                    )

                    return {
                        "success": True,
                        "order_id": order_id,
                        "message": result.get("message", "订单创建成功")
                    }
                else:
                    # 如果token过期，清除缓存的token
                    if result.get("status") == 401:
                        self.token = None
                        self.token_expires_at = None
                        raise BusinessLogicError("软文街token已过期，请重新认证")
                    
                    raise BusinessLogicError(f"创建订单失败: {result.get('message', '未知错误')}")
                    
        except httpx.HTTPError as e:
            logger.error(f"创建订单请求失败: {e}")
            raise BusinessLogicError(f"创建订单请求失败: {str(e)}")
        except Exception as e:
            logger.error(f"创建订单异常: {e}")
            raise BusinessLogicError(f"创建订单异常: {str(e)}")
    
    async def get_banned_words(self) -> List[str]:
        """
        获取违禁词列表
        
        Returns:
            List[str]: 违禁词列表
        """
        token = await self.get_token()
        url = "https://api.xinmeibao.com/api/bannedword"
        
        params = {
            "token": token
        }
        
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.get(url, params=params)
                response.raise_for_status()
                
                result = response.json()
                
                if result.get("success") and result.get("status") == 200:
                    return result.get("data", [])
                else:
                    # 如果token过期，清除缓存的token
                    if result.get("status") == 401:
                        self.token = None
                        self.token_expires_at = None
                        raise BusinessLogicError("软文街token已过期，请重新认证")
                    
                    raise BusinessLogicError(f"获取违禁词失败: {result.get('message', '未知错误')}")
                    
        except httpx.HTTPError as e:
            logger.error(f"获取违禁词请求失败: {e}")
            raise BusinessLogicError(f"获取违禁词请求失败: {str(e)}")
        except Exception as e:
            logger.error(f"获取违禁词异常: {e}")
            raise BusinessLogicError(f"获取违禁词异常: {str(e)}")
    
    def process_order_callback(self, callback_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理订单状态推送回调
        
        Args:
            callback_data: 推送的回调数据
            
        Returns:
            Dict: 处理结果
        """
        try:
            processed_orders = []
            
            for order_data in callback_data.get("data", []):
                order_id = order_data.get("order_id")
                status = order_data.get("status")
                response_message = order_data.get("response_message")
                
                if status == 200:
                    # 发布成功
                    processed_orders.append({
                        "order_id": order_id,
                        "status": "published",
                        "published_url": response_message,
                        "message": "发布成功"
                    })
                elif status == 400:
                    # 发布失败
                    processed_orders.append({
                        "order_id": order_id,
                        "status": "failed",
                        "message": response_message
                    })
                else:
                    # 其他状态
                    processed_orders.append({
                        "order_id": order_id,
                        "status": "unknown",
                        "message": response_message
                    })
            
            return {
                "success": True,
                "processed_count": len(processed_orders),
                "orders": processed_orders
            }
            
        except Exception as e:
            logger.error(f"处理订单回调异常: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def _sync_media_to_database(self, media_data: List[Dict]) -> None:
        """将媒体资源同步到数据库"""
        try:
            for media_item in media_data:
                external_id = media_item.get("id")
                if not external_id:
                    continue

                # 检查媒体是否已存在
                stmt = select(RuanwenjieMedia).where(RuanwenjieMedia.external_id == external_id)
                result = await self.db.execute(stmt)
                existing_media = result.scalar_one_or_none()

                media_data_dict = {
                    "external_id": external_id,
                    "media_name": media_item.get("media_name", ""),
                    "media_url": media_item.get("media_url"),
                    "taxonomy": media_item.get("taxonomy"),
                    "platform": media_item.get("platform"),
                    "area": media_item.get("area"),
                    "is_baidu_news": media_item.get("is_baidu_news", False),
                    "entrance_level": media_item.get("entrance_level"),
                    "url_type": media_item.get("url_type"),
                    "price": float(media_item.get("price", 0)) if media_item.get("price") else None,
                    "weight": media_item.get("weight"),
                    "daily_visits": media_item.get("daily_visits"),
                    "alexa_rank": media_item.get("alexa_rank"),
                    "content_requirements": media_item.get("content_requirements"),
                    "publish_rules": media_item.get("publish_rules"),
                    "is_active": True,
                    "is_test_media": "测试" in media_item.get("media_name", ""),
                    "last_sync_at": datetime.utcnow(),
                    "extra_data": media_item
                }

                if existing_media:
                    # 更新现有媒体
                    stmt = update(RuanwenjieMedia).where(
                        RuanwenjieMedia.external_id == external_id
                    ).values(**media_data_dict)
                    await self.db.execute(stmt)
                else:
                    # 创建新媒体
                    new_media = RuanwenjieMedia(**media_data_dict)
                    self.db.add(new_media)

            await self.db.commit()
            logger.info(f"成功同步 {len(media_data)} 个媒体资源到数据库")

        except Exception as e:
            await self.db.rollback()
            logger.error(f"同步媒体资源到数据库失败: {e}")
            # 不抛出异常，避免影响API响应

    async def _save_order_to_database(self, external_order_id: str, user_id: str, title: str, content: str, resource_id: int) -> None:
        """将订单信息保存到数据库"""
        try:
            # 查找对应的媒体资源
            media_stmt = select(RuanwenjieMedia).where(RuanwenjieMedia.external_id == resource_id)
            media_result = await self.db.execute(media_stmt)
            media = media_result.scalar_one_or_none()

            # 生成内容哈希
            content_hash = hashlib.md5(content.encode('utf-8')).hexdigest()

            # 创建订单记录
            order_data = {
                "external_order_id": external_order_id,
                "user_id": user_id,
                "media_id": media.id if media else None,
                "external_media_id": resource_id,
                "title": title,
                "content": content,
                "content_hash": content_hash,
                "status": "pending",
                "payment_status": "unpaid",
                "original_price": media.price if media else None,
                "actual_price": media.price if media else None,
                "business_type": "api_test",
                "extra_data": {
                    "created_via": "api",
                    "media_name": media.media_name if media else None
                }
            }

            new_order = RuanwenjieOrder(**order_data)
            self.db.add(new_order)

            # 先提交订单，获取订单ID
            await self.db.flush()  # 刷新到数据库但不提交事务

            # 创建初始状态记录
            status_record = RuanwenjieOrderStatus(
                order_id=new_order.id,
                old_status=None,
                new_status="pending",
                change_reason="订单创建",
                response_message="通过API创建订单",
                processed_by="system"
            )
            self.db.add(status_record)

            await self.db.commit()
            logger.info(f"成功保存订单到数据库: {external_order_id}")

        except Exception as e:
            await self.db.rollback()
            logger.error(f"保存订单到数据库失败: {e}")
            # 不抛出异常，避免影响API响应
