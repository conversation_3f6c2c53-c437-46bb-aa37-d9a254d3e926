from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func, desc, asc
from app.models.order import Order, Payment, OrderItem, OrderType, OrderStatus, PaymentMethod, PaymentStatus, Subscription
from app.models.user import User
from app.models.company import Company
from app.schemas.order import *
from app.exceptions import ValidationError, NotFoundError, PermissionError
from typing import Optional, Dict, Any, List
import secrets
import string
from datetime import datetime, timezone
from dateutil.relativedelta import relativedelta
from decimal import Decimal
import uuid

class OrderService:
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_subscription_order(self, user_id: str, request: SubscriptionOrderCreate) -> OrderCreateResponse:
        """创建套餐订单"""
        # 获取用户信息
        user_result = await self.db.execute(
            select(User).where(User.id == user_id)
        )
        user = user_result.scalar_one_or_none()
        if not user:
            raise NotFoundError("用户不存在")
        
        # 获取企业信息（如果是企业用户）
        company_id = None
        company_result = await self.db.execute(
            select(Company).where(Company.user_id == user_id)
        )
        company = company_result.scalar_one_or_none()
        if company:
            company_id = company.id
        
        # 获取套餐信息和价格
        plan_info = await self._get_plan_info(request.plan_id)
        if not plan_info:
            raise NotFoundError("套餐不存在")
        
        # 计算价格
        original_amount = plan_info['price'] * request.service_months
        discount_amount = Decimal('0.00')
        
        # 处理优惠券
        if request.coupon_code:
            discount_amount = await self._apply_coupon(request.coupon_code, original_amount)
        
        final_amount = original_amount - discount_amount
        
        # 计算代理商佣金
        agent_id = None
        commission_rate = None
        commission_amount = None
        # TODO: 实现代理商推荐逻辑
        
        # 生成订单号
        order_no = self._generate_order_no()
        
        # 创建订单
        order = Order(
            id=uuid.uuid4(),
            user_id=user_id,
            company_id=company_id,
            order_no=order_no,
            order_type="subscription",
            product_name=plan_info['name'],
            product_description=plan_info['description'],
            product_specs={
                "plan_id": request.plan_id,
                "service_months": request.service_months,
                "auto_renewal": request.auto_renewal
            },
            original_amount=original_amount,
            discount_amount=discount_amount,
            final_amount=final_amount,
            currency="CNY",
            order_status="pending",
            coupon_code=request.coupon_code,
            customer_note=request.customer_note,
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc)
        )
        
        self.db.add(order)

        # 创建订阅订单记录
        from app.models.order import SubscriptionOrder

        # 将 service_months 转换为 billing_cycle
        billing_cycle_map = {1: 'monthly', 3: 'quarterly', 12: 'yearly'}
        billing_cycle = billing_cycle_map.get(request.service_months, 'yearly')

        # 计算订阅开始和结束时间
        start_date = datetime.now(timezone.utc)
        end_date = start_date + relativedelta(months=request.service_months)

        subscription_order = SubscriptionOrder(
            id=uuid.uuid4(),
            order_id=order.id,
            plan_id=request.plan_id,
            billing_cycle=billing_cycle,
            start_date=start_date,
            end_date=end_date
        )

        self.db.add(subscription_order)

        # 直接完成支付和订阅创建（跳过支付过程）

        # 创建支付记录并标记为成功
        from app.models.order import Payment
        payment = Payment(
            id=uuid.uuid4(),
            order_id=order.id,
            payment_method=PaymentMethod.BALANCE,
            payment_status=PaymentStatus.SUCCESS,
            amount=order.final_amount,  # 字段名是 amount 不是 payment_amount
            paid_at=datetime.now(timezone.utc),
            created_at=datetime.now(timezone.utc)
        )
        self.db.add(payment)

        # 更新订单状态为已支付
        order.order_status = OrderStatus.PAID
        order.paid_at = datetime.now(timezone.utc)
        
        # 创建订单状态日志
        try:
            from app.models.status import OrderStatusLog
            status_log = OrderStatusLog(
                order_id=order.id,
                from_status="pending",
                to_status=OrderStatus.PAID.value,
                changed_by=user_id,
                change_reason="订单自动完成支付（开发模式）",
                meta_info={
                    "plan_name": order.product_name,  # Use the product_name from order which already has the plan name
                    "amount": str(order.final_amount),
                    "billing_cycle": subscription_order.billing_cycle,
                    "auto_payment": True,
                    "payment_method": "balance"
                }
            )
            self.db.add(status_log)

        except Exception as e:
            print(f"ERROR: 创建订单状态日志失败: {e}")
            import traceback
            traceback.print_exc()
            raise

        # 创建订阅记录
        subscription = await self._create_subscription_from_order(order, subscription_order)

        # 确保所有更改都已提交
        await self.db.commit()

        return OrderCreateResponse(
            id=str(order.id),
            order_no=order.order_no,
            order_type="subscription",
            product_name=order.product_name,
            original_amount=original_amount,
            discount_amount=discount_amount,
            final_amount=order.final_amount,
            order_status="paid",  # 直接返回已支付状态
            created_at=order.created_at,
            service_months=request.service_months
        )

    async def create_upgrade_order(self, user_id: UUID, upgrade_request) -> OrderCreateResponse:
        """创建升级订单"""
        from app.services.subscription_service import SubscriptionService

        # 获取用户当前订阅
        subscription_service = SubscriptionService(self.db)
        current_subscription = await subscription_service.get_active_subscription(str(user_id))

        if not current_subscription:
            raise ValidationError("用户没有活跃的订阅")

        # 获取当前套餐和目标套餐信息
        current_plan = await subscription_service.get_plan_by_id(current_subscription.plan_id)
        target_plan = await subscription_service.get_plan_by_id(upgrade_request.target_plan_id)

        # 计算升级费用
        current_price = subscription_service._get_plan_price(current_plan, current_subscription.billing_cycle or "monthly")
        target_price = subscription_service._get_plan_price(target_plan, upgrade_request.billing_cycle or current_subscription.billing_cycle or "monthly")

        # 计算按比例的升级费用
        if upgrade_request.prorate:
            # 使用更精确的日期计算
            remaining_days = (current_subscription.end_date.date() - datetime.now(timezone.utc).date()).days
            total_days_in_cycle = 30  # 简化处理，可根据billing_cycle调整
            proration_factor = max(remaining_days / total_days_in_cycle, 0)
            upgrade_amount = (target_price - current_price) * Decimal(str(proration_factor))
        else:
            upgrade_amount = target_price - current_price

        # 确保升级费用不为负数
        upgrade_amount = max(upgrade_amount, Decimal('0.00'))

        # 生成订单号
        order_no = f"UPG{datetime.now(timezone.utc).strftime('%Y%m%d%H%M%S')}{str(uuid.uuid4())[:8].upper()}"


        # 创建升级订单
        order = Order(
            id=uuid.uuid4(),
            user_id=user_id,
            company_id=getattr(current_subscription, 'company_id', None),
            order_no=order_no,
            order_type=OrderType.SUBSCRIPTION,  # 暂时使用 subscription 类型，直到数据库迁移完成
            product_name=f"升级到{target_plan.plan_name}",
            product_description=f"从{current_plan.plan_name}升级到{target_plan.plan_name}",
            product_specs={
                "current_subscription_id": str(current_subscription.id),
                "current_plan_id": str(current_plan.id),
                "target_plan_id": str(target_plan.id),
                "billing_cycle": upgrade_request.billing_cycle or current_subscription.billing_cycle,
                "prorate": upgrade_request.prorate,
                "effective_immediately": upgrade_request.effective_immediately
            },
            original_amount=upgrade_amount,
            discount_amount=Decimal('0.00'),
            final_amount=upgrade_amount,
            currency="CNY",
            order_status="pending",
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc)
        )

        self.db.add(order)

        # 创建订阅订单记录
        from app.models.order import SubscriptionOrder
        
        # 计算新的订阅周期
        billing_cycle = upgrade_request.billing_cycle or current_subscription.billing_cycle or "monthly"
        
        # 如果立即生效，从今天开始；否则从当前订阅结束后开始
        if upgrade_request.effective_immediately:
            start_date = datetime.now(timezone.utc)
            # 根据billing_cycle计算结束日期
            months_map = {'monthly': 1, 'quarterly': 3, 'yearly': 12}
            months = months_map.get(billing_cycle, 1)
            end_date = start_date + relativedelta(months=months)
        else:
            start_date = current_subscription.end_date
            months_map = {'monthly': 1, 'quarterly': 3, 'yearly': 12}
            months = months_map.get(billing_cycle, 1)
            end_date = start_date + relativedelta(months=months)
        
        subscription_order = SubscriptionOrder(
            id=uuid.uuid4(),
            order_id=order.id,
            plan_id=upgrade_request.target_plan_id,
            billing_cycle=billing_cycle,
            start_date=start_date,
            end_date=end_date
        )
        
        self.db.add(subscription_order)

        # 直接完成支付和升级处理（开发阶段）

        # 创建支付记录并标记为成功
        from app.models.order import Payment
        payment = Payment(
            id=uuid.uuid4(),
            order_id=order.id,
            payment_method="balance",
            payment_status="success",
            amount=order.final_amount,
            paid_at=datetime.now(timezone.utc),
            created_at=datetime.now(timezone.utc)
        )
        self.db.add(payment)

        # 更新订单状态为已支付
        order.order_status = OrderStatus.PAID
        order.paid_at = datetime.now(timezone.utc)
        
        # 创建订单状态日志
        try:
            from app.models.status import OrderStatusLog
            upgrade_status_log = OrderStatusLog(
                order_id=order.id,
                from_status="pending",
                to_status=OrderStatus.PAID.value,
                changed_by=user_id,
                change_reason="升级订单自动完成支付（开发模式）",
                meta_info={
                    "upgrade_type": "subscription_upgrade",
                    "from_plan": current_plan.plan_name,
                    "to_plan": target_plan.plan_name,
                    "amount": str(order.final_amount),
                    "effective_immediately": upgrade_request.effective_immediately,
                    "auto_payment": True
                }
            )
            self.db.add(upgrade_status_log)

        except Exception as e:
            print(f"ERROR: 创建升级订单状态日志失败: {e}")
            import traceback
            traceback.print_exc()
            raise

        await self.db.commit()
        await self.db.refresh(order)

        # 执行升级逻辑
        await self._process_subscription_upgrade(order, current_subscription, upgrade_request)

        # 确保所有更改都已提交
        await self.db.commit()

        return OrderCreateResponse(
            id=str(order.id),
            order_no=order.order_no,
            order_type=OrderType.SUBSCRIPTION.value,  # 暂时使用 subscription 类型
            product_name=order.product_name,
            original_amount=upgrade_amount,
            discount_amount=Decimal('0.00'),
            final_amount=order.final_amount,
            order_status="paid",
            created_at=order.created_at,
            service_months=None
        )

    async def _process_subscription_upgrade(self, order: Order, current_subscription, upgrade_request):
        """处理订阅升级逻辑"""
        try:
            from app.services.subscription_service import SubscriptionService
            subscription_service = SubscriptionService(self.db)

            # 获取目标套餐信息
            target_plan = await subscription_service.get_plan_by_id(upgrade_request.target_plan_id)

            # 获取真正的数据库模型对象（current_subscription是SubscriptionResponse，不是数据库模型）
            subscription_db = await subscription_service._get_subscription(current_subscription.id)

            # 更新订阅记录
            if upgrade_request.effective_immediately:
                old_plan_id = subscription_db.plan_id
                old_billing_cycle = subscription_db.billing_cycle

                subscription_db.plan_id = target_plan.id
                if upgrade_request.billing_cycle:
                    subscription_db.billing_cycle = upgrade_request.billing_cycle
                    # 如果改变了付费周期，需要更新结束日期
                    months_map = {'monthly': 1, 'quarterly': 3, 'yearly': 12}
                    months = months_map.get(upgrade_request.billing_cycle, 1)
                    subscription_db.end_date = datetime.now(timezone.utc) + relativedelta(months=months)
                subscription_db.updated_at = datetime.now(timezone.utc)

                # 立即刷新订阅记录到数据库
                await self.db.flush()
                await self.db.refresh(subscription_db)

                # 更新配额使用记录
                await subscription_service._update_quota_limits(str(subscription_db.id), target_plan)

            # 创建订阅变更记录
            from app.models.order import SubscriptionChange

            # 修正变更记录中的付费周期信息
            from_billing_cycle = order.product_specs.get("billing_cycle", "monthly") if hasattr(order, 'product_specs') and order.product_specs else "monthly"
            to_billing_cycle = upgrade_request.billing_cycle or subscription_db.billing_cycle or "monthly"

            change = SubscriptionChange(
                id=uuid.uuid4(),
                subscription_id=subscription_db.id,
                user_id=subscription_db.user_id,
                change_type="upgrade",
                from_plan_id=order.product_specs.get("current_plan_id"),
                to_plan_id=str(target_plan.id),
                from_billing_cycle=from_billing_cycle,
                to_billing_cycle=to_billing_cycle,
                amount=order.final_amount,
                effective_date=datetime.now(timezone.utc) if upgrade_request.effective_immediately else subscription_db.end_date,
                reason="User requested upgrade via order",
                created_at=datetime.now(timezone.utc)
            )

            self.db.add(change)

            # 立即刷新变更记录到数据库
            await self.db.flush()

        except Exception as e:
            import traceback
            print(f"ERROR: 处理订阅升级失败: {e}")
            print(f"ERROR: 详细错误信息: {traceback.format_exc()}")
            raise
    
    async def process_payment(self, order_id: str, request: PaymentRequest, user_id: str) -> PaymentResponse:
        """处理订单支付"""
        # 获取订单
        order = await self._get_order(order_id)
        
        # 验证用户权限
        if str(order.user_id) != user_id:
            raise PermissionError("无权限操作该订单")
        
        # 检查订单状态
        if order.order_status != OrderStatus.PENDING:
            raise ValidationError("订单状态不允许支付")
        
        # 生成支付单号
        payment_no = self._generate_payment_no()
        
        # 创建支付记录
        payment = Payment(
            payment_no=payment_no,
            order_id=order.id,
            payment_method=PaymentMethod(request.payment_method),
            payment_amount=order.final_amount
        )
        
        self.db.add(payment)
        await self.db.commit()
        await self.db.refresh(payment)
        
        # 根据支付方式处理
        payment_result = await self._process_payment_method(payment, request)
        
        return PaymentResponse(
            payment_no=payment.payment_no,
            order_id=str(order.id),
            payment_method=payment.payment_method.value,
            payment_status=payment.payment_status.value,
            payment_amount=payment.payment_amount,
            payment_url=payment_result.get('payment_url'),
            qr_code=payment_result.get('qr_code'),
            expires_at=payment_result.get('expires_at')
        )
    
    async def get_order_list(self, query: OrderListQuery, user_id: str = None) -> Dict[str, Any]:
        """获取订单列表"""
        try:
            # 构建查询条件
            conditions = []
            if user_id:  # 如果指定了user_id，则只查询该用户的订单
                conditions.append(Order.user_id == user_id)

            if query.order_type:
                try:
                    # 将字符串转换为枚举类型
                    order_type_enum = OrderType(query.order_type)
                    conditions.append(Order.order_type == order_type_enum)
                except ValueError:
                    # 如果传入的order_type不是有效的枚举值，忽略此条件
                    pass
            if query.order_status:
                try:
                    # 将字符串转换为枚举类型
                    order_status_enum = OrderStatus(query.order_status)
                    conditions.append(Order.order_status == order_status_enum)
                except ValueError:
                    # 如果传入的order_status不是有效的枚举值，忽略此条件
                    pass
            if query.start_date:
                conditions.append(Order.created_at >= query.start_date)
            if query.end_date:
                conditions.append(Order.created_at <= query.end_date)

            # 构建查询
            if conditions:
                base_query = select(Order).where(and_(*conditions))
            else:
                base_query = select(Order)

            # 排序（添加安全检查）
            valid_sort_fields = ["created_at", "updated_at", "final_amount", "order_status"]
            sort_field = query.sort_by if query.sort_by in valid_sort_fields else "created_at"

            if query.sort_order == "desc":
                base_query = base_query.order_by(desc(getattr(Order, sort_field)))
            else:
                base_query = base_query.order_by(asc(getattr(Order, sort_field)))

            # 分页
            offset = (query.page - 1) * query.size
            paginated_query = base_query.offset(offset).limit(query.size)

            # 执行查询
            result = await self.db.execute(paginated_query)
            orders = result.scalars().all()

            # 获取总数
            if conditions:
                count_query = select(func.count(Order.id)).where(and_(*conditions))
            else:
                count_query = select(func.count(Order.id))
            total_result = await self.db.execute(count_query)
            total = total_result.scalar()

            # 构建响应数据
            items = []
            for order in orders:
                order_response = await self._build_order_response(order)
                items.append(order_response)

            return {
                "items": items,
                "pagination": {
                    "total": total,
                    "page": query.page,
                    "size": query.size,
                    "pages": (total + query.size - 1) // query.size
                },
                "statistics": {
                    "total_orders": total,
                    "paid_orders": 0,
                    "total_spent": 0.0
                }
            }
        except Exception as e:
            raise e
    
    async def get_order_detail(self, order_id: str, user_id: str) -> OrderResponse:
        """获取订单详情"""
        order = await self._get_order(order_id)
        
        # 验证用户权限
        if str(order.user_id) != user_id:
            raise PermissionError("无权限查看该订单")
        
        return await self._build_order_response(order, include_details=True)
    
    async def handle_payment_callback(self, callback_request: PaymentCallbackRequest) -> bool:
        """处理支付回调"""
        # 获取支付记录
        payment_result = await self.db.execute(
            select(Payment).where(Payment.payment_no == callback_request.payment_no)
        )
        payment = payment_result.scalar_one_or_none()
        if not payment:
            return False
        
        # 验证回调数据
        if not self._verify_payment_callback(payment, callback_request.callback_data or {}):
            return False
        
        # 更新支付状态
        if callback_request.status == 'success':
            payment.payment_status = PaymentStatus.SUCCESS
            payment.paid_at = datetime.now(timezone.utc)
            payment.third_party_trade_no = callback_request.trade_no
            payment.callback_data = callback_request.callback_data
            
            # 更新订单状态
            order_result = await self.db.execute(
                select(Order).where(Order.id == payment.order_id)
            )
            order = order_result.scalar_one_or_none()
            if order:
                order.order_status = OrderStatus.PAID
                order.paid_at = datetime.now(timezone.utc)
                
                # 如果是套餐订单，创建订阅记录
                if order.order_type == OrderType.SUBSCRIPTION:
                    await self._create_subscription(order)
        else:
            payment.payment_status = PaymentStatus.FAILED
            payment.failed_at = datetime.now(timezone.utc)
            payment.failure_reason = callback_request.failure_reason
        
        await self.db.commit()
        return True

    async def _build_order_response(self, order: Order, include_details: bool = False) -> OrderResponse:
        """构建订单响应数据"""
        # 获取支付信息
        payment_info = None
        if include_details:
            payment_result = await self.db.execute(
                select(Payment).where(Payment.order_id == order.id).order_by(desc(Payment.created_at))
            )
            payment = payment_result.scalar_one_or_none()
            if payment:
                payment_info = {
                    "payment_no": payment.payment_no,
                    "payment_method": payment.payment_method.value,
                    "payment_status": payment.payment_status.value,
                    "payment_amount": payment.payment_amount,
                    "paid_at": payment.paid_at
                }

        # 获取订单项
        order_items = None
        if include_details:
            items_result = await self.db.execute(
                select(OrderItem).where(OrderItem.order_id == order.id)
            )
            items = items_result.scalars().all()
            order_items = [
                {
                    "item_type": item.item_type,
                    "item_name": item.item_name,
                    "item_description": item.item_description,
                    "quantity": item.quantity,
                    "unit_price": item.unit_price,
                    "total_price": item.total_price
                }
                for item in items
            ]

        return OrderResponse(
            id=str(order.id),
            order_no=order.order_no,
            user_id=str(order.user_id),
            company_id=str(order.company_id) if order.company_id else None,
            order_type=order.order_type.value if order.order_type else "unknown",
            order_status=order.order_status.value if order.order_status else "unknown",
            product_name=order.product_name or "",
            product_description=order.product_description,
            original_amount=order.original_amount or Decimal('0.00'),
            discount_amount=order.discount_amount or Decimal('0.00'),
            final_amount=order.final_amount or Decimal('0.00'),
            currency=order.currency or "CNY",
            coupon_code=order.coupon_code,
            agent_id=str(order.agent_id) if order.agent_id else None,
            commission_amount=order.commission_amount,
            customer_note=order.customer_note,
            created_at=order.created_at,
            updated_at=order.updated_at,
            paid_at=order.paid_at,
            completed_at=order.completed_at,
            service_start_date=order.service_start_date,
            service_end_date=order.service_end_date,
            payment_info=payment_info,
            order_items=order_items
        )

    async def _get_order(self, order_id: str) -> Order:
        """获取订单"""
        result = await self.db.execute(
            select(Order).where(Order.id == order_id)
        )
        order = result.scalar_one_or_none()
        if not order:
            raise NotFoundError("订单不存在")
        return order

    def _generate_order_no(self) -> str:
        """生成订单号"""
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        random_suffix = ''.join(secrets.choice(string.digits) for _ in range(6))
        return f"ORD{timestamp}{random_suffix}"

    def _generate_payment_no(self) -> str:
        """生成支付单号"""
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        random_suffix = ''.join(secrets.choice(string.digits) for _ in range(6))
        return f"PAY{timestamp}{random_suffix}"

    async def _get_plan_info(self, plan_id: str) -> Optional[Dict[str, Any]]:
        """获取套餐信息"""
        try:
            from app.services.subscription_service import SubscriptionService
            subscription_service = SubscriptionService(self.db)
            plan = await subscription_service.get_plan_by_id(plan_id)

            # 转换为兼容的格式
            return {
                "id": str(plan.id),
                "name": plan.plan_name,
                "description": f"{plan.plan_type}套餐",
                "price": plan.monthly_price,
                "content_quota": plan.max_content_requests,
                "monitoring_quota": plan.max_monitoring_projects,
                "ai_quota": plan.max_api_calls
            }
        except Exception:
            # 如果从数据库获取失败，返回None
            return None

    async def _apply_coupon(self, coupon_code: str, original_amount: Decimal) -> Decimal:
        """应用优惠券"""
        # TODO: 实现优惠券逻辑
        # 简化处理，返回固定优惠
        if coupon_code == "DISCOUNT10":
            return original_amount * Decimal('0.1')  # 10%优惠
        elif coupon_code == "SAVE50":
            return min(Decimal('50.00'), original_amount)  # 最多优惠50元
        return Decimal('0.00')

    async def _process_payment_method(self, payment: Payment, request: PaymentRequest) -> Dict[str, Any]:
        """处理支付方式 - 开发阶段自动完成所有支付"""

        # 开发阶段：所有支付方式都自动成功
        payment.payment_status = PaymentStatus.SUCCESS
        payment.paid_at = datetime.now(timezone.utc)
        payment.third_party_trade_no = f"auto_{payment.payment_no}_{int(datetime.now(timezone.utc).timestamp())}"

        # 更新订单状态为已支付
        order_result = await self.db.execute(
            select(Order).where(Order.id == payment.order_id)
        )
        order = order_result.scalar_one_or_none()
        if order:
            order.order_status = OrderStatus.PAID
            order.paid_at = datetime.now(timezone.utc)

            # 如果是套餐订单，创建订阅记录
            if order.order_type == OrderType.SUBSCRIPTION:
                await self._create_subscription(order)

        await self.db.commit()
        return {
            "status": "success",
            "payment_status": "success",
            "trade_no": payment.third_party_trade_no,
            "paid_at": payment.paid_at.isoformat()
        }

    def _verify_payment_callback(self, payment: Payment, callback_data: Dict[str, Any]) -> bool:
        """验证支付回调"""
        # TODO: 实现回调验证逻辑
        return True

    async def _create_subscription(self, order: Order):
        """创建订阅记录"""
        service_months = order.product_specs.get('service_months', 12)
        start_date = datetime.now(timezone.utc)
        end_date = start_date + relativedelta(months=service_months)

        subscription = Subscription(
            order_id=order.id,
            user_id=order.user_id,
            company_id=order.company_id,
            plan_id=order.product_id,
            plan_name=order.product_name,
            plan_type=order.product_id,  # 简化处理
            content_quota=order.product_specs.get('content_quota', 0),
            monitoring_quota=order.product_specs.get('monitoring_quota', 0),
            ai_quota=order.product_specs.get('ai_quota', 0),
            start_date=start_date,
            end_date=end_date
        )

        self.db.add(subscription)

        # 更新订单服务期
        order.service_start_date = start_date
        order.service_end_date = end_date
        order.order_status = OrderStatus.PROCESSING

        await self.db.commit()

    async def _get_order_statistics(self, user_id: str) -> Dict[str, Any]:
        """获取订单统计"""
        # 总订单数
        total_orders_result = await self.db.execute(
            select(func.count(Order.id)).where(Order.user_id == user_id)
        )
        total_orders = total_orders_result.scalar()

        # 已支付订单数
        paid_orders_result = await self.db.execute(
            select(func.count(Order.id)).where(
                and_(Order.user_id == user_id, Order.order_status.in_([OrderStatus.PAID, OrderStatus.PROCESSING, OrderStatus.COMPLETED]))
            )
        )
        paid_orders = paid_orders_result.scalar()

        # 总消费金额
        total_spent_result = await self.db.execute(
            select(func.sum(Order.final_amount)).where(
                and_(Order.user_id == user_id, Order.order_status.in_([OrderStatus.PAID, OrderStatus.PROCESSING, OrderStatus.COMPLETED]))
            )
        )
        total_spent = total_spent_result.scalar() or Decimal('0.00')

        return {
            "total_orders": total_orders,
            "paid_orders": paid_orders,
            "total_spent": total_spent
        }
    
    async def create_content_service_order(self, user_id: str, request: ContentServiceOrderCreate) -> OrderCreateResponse:
        """创建内容服务订单"""
        # 获取用户信息
        user_result = await self.db.execute(
            select(User).where(User.id == user_id)
        )
        user = user_result.scalar_one_or_none()
        if not user:
            raise NotFoundError("用户不存在")
        
        # TODO: 获取服务信息和价格
        # 暂时使用模拟数据
        service_info = {
            'name': f'内容服务 - {request.service_id}',
            'description': '专业内容创作服务',
            'price': Decimal('500.00')
        }
        
        # 生成订单号
        order_no = self._generate_order_no()
        
        # 创建订单
        order = Order(
            id=uuid.uuid4(),
            user_id=user_id,
            order_no=order_no,
            order_type=OrderType.CONTENT_SERVICE,
            product_id=request.service_id,
            product_name=service_info['name'],
            product_description=service_info['description'],
            product_specs={
                "service_id": request.service_id,
                "content_request_id": request.content_request_id,
                "service_provider_id": request.service_provider_id
            },
            original_amount=service_info['price'],
            discount_amount=Decimal('0.00'),
            final_amount=service_info['price'],
            currency="CNY",
            order_status=OrderStatus.PENDING,
            customer_note=request.customer_note,
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc)
        )
        
        self.db.add(order)
        await self.db.commit()
        await self.db.refresh(order)
        
        return OrderCreateResponse(
            id=str(order.id),
            order_no=order.order_no,
            order_type=order.order_type.value,
            product_name=order.product_name,
            original_amount=order.original_amount,
            discount_amount=order.discount_amount,
            final_amount=order.final_amount,
            order_status=order.order_status.value,
            created_at=order.created_at
        )
    
    async def update_order(self, order_id: str, update_data: Dict[str, Any], user_id: str, is_admin: bool = False) -> OrderResponse:
        """更新订单信息"""
        # 获取订单
        order = await self._get_order(order_id)
        
        # 验证权限
        if not is_admin and str(order.user_id) != user_id:
            raise PermissionError("无权限更新该订单")
        
        # 更新允许的字段
        for field, value in update_data.items():
            if field in ['customer_note', 'admin_note']:
                setattr(order, field, value)
        
        order.updated_at = datetime.now(timezone.utc)
        
        await self.db.commit()
        await self.db.refresh(order)
        
        return await self._build_order_response(order)
    
    async def cancel_order(self, order_id: str, user_id: str, reason: str, refund_requested: bool = False, is_admin: bool = False) -> OrderResponse:
        """取消订单"""
        # 获取订单
        order = await self._get_order(order_id)
        
        # 验证权限
        if not is_admin and str(order.user_id) != user_id:
            raise PermissionError("无权限取消该订单")
        
        # 检查订单是否可以取消
        if order.order_status not in [OrderStatus.PENDING, OrderStatus.PAID]:
            raise ValidationError(f"订单状态 {order.order_status.value} 不允许取消")
        
        # 如果是套餐订单且已开始服务，不允许取消
        if order.order_type == OrderType.SUBSCRIPTION and order.service_start_date:
            if order.service_start_date <= datetime.now(timezone.utc):
                raise ValidationError("套餐服务已开始，不能取消")
        
        # 记录原始状态
        original_status = order.order_status
        
        # 更新订单状态
        order.order_status = OrderStatus.CANCELLED
        order.cancelled_at = datetime.now(timezone.utc)
        order.admin_note = f"取消原因: {reason}"
        
        # 如果已支付且申请退款，创建退款记录
        if original_status == OrderStatus.PAID and refund_requested:
            # TODO: 创建退款记录
            order.order_status = OrderStatus.REFUNDED
        
        await self.db.commit()
        await self.db.refresh(order)
        
        return await self._build_order_response(order)
    
    async def search_orders(self, search_params: Dict[str, Any], user_id: Optional[str], page: int = 1, size: int = 20) -> Dict[str, Any]:
        """高级搜索订单"""
        # 构建查询条件
        conditions = []
        
        # 如果指定了用户ID（非管理员），只查询该用户的订单
        if user_id:
            conditions.append(Order.user_id == user_id)
        
        # 关键词搜索
        if search_params.get('keyword'):
            keyword = f"%{search_params['keyword']}%"
            conditions.append(or_(
                Order.order_no.like(keyword),
                Order.product_name.like(keyword),
                Order.customer_note.like(keyword)
            ))
        
        # 订单类型筛选
        if search_params.get('order_types'):
            order_types = [OrderType(ot) for ot in search_params['order_types']]
            conditions.append(Order.order_type.in_(order_types))
        
        # 订单状态筛选
        if search_params.get('order_statuses'):
            order_statuses = [OrderStatus(os) for os in search_params['order_statuses']]
            conditions.append(Order.order_status.in_(order_statuses))
        
        # 金额范围
        if search_params.get('min_amount'):
            conditions.append(Order.final_amount >= search_params['min_amount'])
        if search_params.get('max_amount'):
            conditions.append(Order.final_amount <= search_params['max_amount'])
        
        # 时间范围
        if search_params.get('start_date'):
            conditions.append(Order.created_at >= search_params['start_date'])
        if search_params.get('end_date'):
            conditions.append(Order.created_at <= search_params['end_date'])
        
        # 是否使用优惠券
        if search_params.get('has_coupon') is not None:
            if search_params['has_coupon']:
                conditions.append(Order.coupon_code.isnot(None))
            else:
                conditions.append(Order.coupon_code.is_(None))
        
        # 构建查询
        base_query = select(Order)
        if conditions:
            base_query = base_query.where(and_(*conditions))
        
        # 排序
        base_query = base_query.order_by(desc(Order.created_at))
        
        # 分页
        offset = (page - 1) * size
        paginated_query = base_query.offset(offset).limit(size)
        
        # 执行查询
        result = await self.db.execute(paginated_query)
        orders = result.scalars().all()
        
        # 获取总数
        count_query = select(func.count(Order.id))
        if conditions:
            count_query = count_query.where(and_(*conditions))
        total_result = await self.db.execute(count_query)
        total = total_result.scalar()
        
        # 构建响应
        items = []
        for order in orders:
            order_response = await self._build_order_response(order)
            items.append(order_response)
        
        # 计算统计信息
        stats_query = select(
            func.count(Order.id),
            func.sum(Order.final_amount)
        )
        if conditions:
            stats_query = stats_query.where(and_(*conditions))
        
        stats_result = await self.db.execute(stats_query)
        stats = stats_result.one()
        
        return {
            "items": items,
            "pagination": {
                "total": total,
                "page": page,
                "size": size,
                "pages": (total + size - 1) // size,
                "has_next": page * size < total,
                "has_prev": page > 1
            },
            "summary": {
                "total_count": stats[0] or 0,
                "total_amount": float(stats[1] or 0),
                "pending_count": 0,  # TODO: 实现各状态统计
                "paid_count": 0,
                "completed_count": 0,
                "cancelled_count": 0
            }
        }

    async def _create_subscription_from_order(self, order: Order, subscription_order):
        """从订单和订阅订单创建订阅记录"""
        try:
            # 调用订阅服务创建订阅
            from app.services.subscription_service import SubscriptionService
            subscription_service = SubscriptionService(self.db)

            subscription = await subscription_service.create_subscription_from_order(
                order_id=order.id,  # 修复：传入订单ID
                user_id=order.user_id,
                plan_id=subscription_order.plan_id,
                billing_cycle=subscription_order.billing_cycle,
                start_date=subscription_order.start_date,
                end_date=subscription_order.end_date
            )
            return subscription

        except Exception as e:
            import traceback
            print(f"ERROR: 创建订阅失败: {e}")
            print(f"ERROR: 详细错误信息: {traceback.format_exc()}")
            # 不抛出异常，避免影响支付流程
            return None
