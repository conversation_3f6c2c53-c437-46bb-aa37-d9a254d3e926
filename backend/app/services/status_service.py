"""
状态管理服务层
"""
from typing import List, Optional, Tuple, Dict, Any
from uuid import UUID
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_, desc
from sqlalchemy.orm import selectinload

from app.models.status import OrderStatusLog
from app.models.order import Order
from app.models.user import User
from app.core.status_machine import OrderStateMachine
from app.schemas.status import (
    OrderStatusLogSchema,
    OrderStatusHistorySchema,
    OrderStatusHistoryItemSchema,
    StatusStatisticsSchema,
    ORDER_STATUS_INFO
)


class StatusService:
    """状态管理服务"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def log_status_change(
        self,
        order_id: UUID,
        to_status: str,
        changed_by: Optional[UUID] = None,
        change_reason: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> OrderStatusLog:
        """
        记录状态变更
        
        Args:
            order_id: 订单ID
            to_status: 目标状态
            changed_by: 变更人ID（系统变更时为None）
            change_reason: 变更原因
            metadata: 附加信息
            
        Returns:
            OrderStatusLog: 状态日志记录
        """
        # 获取订单当前状态
        order_query = select(Order).where(Order.id == order_id)
        result = await self.db.execute(order_query)
        order = result.scalar_one_or_none()
        
        if not order:
            raise ValueError(f"订单不存在: {order_id}")
        
        current_status = order.order_status.value if hasattr(order.order_status, 'value') else order.order_status
        
        # 验证状态转换
        state_machine = OrderStateMachine(current_status)
        is_valid, message = state_machine.validate_transition(to_status, change_reason)
        
        if not is_valid:
            raise ValueError(f"状态转换失败: {message}")
        
        # 创建状态日志
        status_log = OrderStatusLog(
            order_id=order_id,
            from_status=current_status if current_status != to_status else None,
            to_status=to_status,
            changed_by=changed_by,
            change_reason=change_reason or "状态变更",
            meta_info=metadata
        )
        
        self.db.add(status_log)
        
        # 更新订单状态
        order.order_status = to_status
        order.updated_at = datetime.utcnow()
        
        # 根据状态更新相应的时间字段
        if to_status == "paid":
            order.paid_at = datetime.utcnow()
        elif to_status == "completed":
            order.completed_at = datetime.utcnow()
        elif to_status == "cancelled":
            order.cancelled_at = datetime.utcnow()
        
        await self.db.commit()
        await self.db.refresh(status_log)
        
        return status_log
    
    async def get_order_status_history(
        self,
        order_id: UUID
    ) -> OrderStatusHistorySchema:
        """
        获取订单状态历史
        
        Args:
            order_id: 订单ID
            
        Returns:
            OrderStatusHistorySchema: 状态历史信息
        """
        # 获取订单信息
        order_query = select(Order).where(Order.id == order_id)
        result = await self.db.execute(order_query)
        order = result.scalar_one_or_none()
        
        if not order:
            raise ValueError(f"订单不存在: {order_id}")
        
        # 获取状态日志
        logs_query = (
            select(OrderStatusLog)
            .options(selectinload(OrderStatusLog.changer))
            .where(OrderStatusLog.order_id == order_id)
            .order_by(OrderStatusLog.created_at)
        )
        result = await self.db.execute(logs_query)
        logs = result.scalars().all()
        
        # 构建状态历史
        history_items = []
        prev_time = None
        
        for i, log in enumerate(logs):
            # 计算在该状态停留的时间
            duration_minutes = None
            if prev_time:
                duration = log.created_at - prev_time
                duration_minutes = int(duration.total_seconds() / 60)
            
            history_item = OrderStatusHistoryItemSchema(
                id=log.id,
                from_status=log.from_status,
                from_status_name=ORDER_STATUS_INFO.get(log.from_status, {}).get("name") if log.from_status else None,
                to_status=log.to_status,
                to_status_name=ORDER_STATUS_INFO.get(log.to_status, {}).get("name", log.to_status),
                changed_by=log.changer.full_name if log.changer else "系统",
                change_reason=log.change_reason,
                duration_minutes=duration_minutes,
                created_at=log.created_at,
                metadata=log.meta_info
            )
            history_items.append(history_item)
            prev_time = log.created_at
        
        # 计算总耗时
        total_duration_hours = None
        if logs:
            total_duration = logs[-1].created_at - logs[0].created_at
            total_duration_hours = round(total_duration.total_seconds() / 3600, 2)
        
        current_status = order.order_status.value if hasattr(order.order_status, 'value') else order.order_status
        
        return OrderStatusHistorySchema(
            order_id=order_id,
            order_no=order.order_no,
            current_status=current_status,
            current_status_name=ORDER_STATUS_INFO.get(current_status, {}).get("name", current_status),
            status_history=history_items,
            total_duration_hours=total_duration_hours
        )
    
    async def list_status_logs(
        self,
        order_id: Optional[UUID] = None,
        from_status: Optional[str] = None,
        to_status: Optional[str] = None,
        changed_by: Optional[UUID] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        page: int = 1,
        limit: int = 20
    ) -> Tuple[List[OrderStatusLog], int]:
        """
        查询状态日志列表
        
        Returns:
            Tuple[List[OrderStatusLog], int]: (日志列表, 总数)
        """
        query = select(OrderStatusLog).options(
            selectinload(OrderStatusLog.order),
            selectinload(OrderStatusLog.changer)
        )
        
        # 构建查询条件
        conditions = []
        if order_id:
            conditions.append(OrderStatusLog.order_id == order_id)
        if from_status:
            conditions.append(OrderStatusLog.from_status == from_status)
        if to_status:
            conditions.append(OrderStatusLog.to_status == to_status)
        if changed_by:
            conditions.append(OrderStatusLog.changed_by == changed_by)
        if start_date:
            conditions.append(OrderStatusLog.created_at >= start_date)
        if end_date:
            conditions.append(OrderStatusLog.created_at <= end_date)
        
        if conditions:
            query = query.where(and_(*conditions))
        
        # 获取总数
        count_query = select(func.count()).select_from(OrderStatusLog)
        if conditions:
            count_query = count_query.where(and_(*conditions))
        count_result = await self.db.execute(count_query)
        total = count_result.scalar()
        
        # 分页查询
        query = query.order_by(desc(OrderStatusLog.created_at))
        query = query.limit(limit).offset((page - 1) * limit)
        
        result = await self.db.execute(query)
        logs = result.scalars().all()
        
        return logs, total
    
    async def validate_status_transition(
        self,
        order_id: UUID,
        to_status: str
    ) -> Tuple[bool, str]:
        """
        验证状态转换是否合法
        
        Args:
            order_id: 订单ID
            to_status: 目标状态
            
        Returns:
            Tuple[bool, str]: (是否合法, 消息)
        """
        # 获取订单当前状态
        order_query = select(Order).where(Order.id == order_id)
        result = await self.db.execute(order_query)
        order = result.scalar_one_or_none()
        
        if not order:
            return False, f"订单不存在: {order_id}"
        
        current_status = order.order_status.value if hasattr(order.order_status, 'value') else order.order_status
        
        # 使用状态机验证
        state_machine = OrderStateMachine(current_status)
        return state_machine.validate_transition(to_status)
    
    async def get_status_statistics(
        self,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> StatusStatisticsSchema:
        """
        获取状态统计信息
        
        Args:
            start_date: 开始时间
            end_date: 结束时间
            
        Returns:
            StatusStatisticsSchema: 统计信息
        """
        # 构建时间条件
        conditions = []
        if start_date:
            conditions.append(Order.created_at >= start_date)
        if end_date:
            conditions.append(Order.created_at <= end_date)
        
        # 统计各状态订单数量
        status_query = (
            select(Order.order_status, func.count(Order.id))
            .group_by(Order.order_status)
        )
        if conditions:
            status_query = status_query.where(and_(*conditions))
        
        result = await self.db.execute(status_query)
        status_counts = result.all()
        
        status_distribution = {}
        total_orders = 0
        for status, count in status_counts:
            status_str = status.value if hasattr(status, 'value') else status
            status_distribution[status_str] = count
            total_orders += count
        
        # 计算平均状态转换时间
        avg_transition_time = {}
        
        # 查询状态转换日志
        transition_query = (
            select(
                OrderStatusLog.from_status,
                OrderStatusLog.to_status,
                func.avg(
                    func.extract('epoch', OrderStatusLog.created_at) -
                    func.extract('epoch', func.lag(OrderStatusLog.created_at).over(
                        partition_by=OrderStatusLog.order_id,
                        order_by=OrderStatusLog.created_at
                    ))
                ).label('avg_duration')
            )
            .group_by(OrderStatusLog.from_status, OrderStatusLog.to_status)
        )
        
        if conditions:
            # 需要关联订单表来应用时间条件
            transition_query = transition_query.join(
                Order, OrderStatusLog.order_id == Order.id
            ).where(and_(*conditions))
        
        result = await self.db.execute(transition_query)
        transitions = result.all()
        
        for from_status, to_status, avg_duration in transitions:
            if avg_duration:
                key = f"{from_status}_to_{to_status}"
                avg_transition_time[key] = round(avg_duration / 3600, 2)  # 转换为小时
        
        # 统计异常状态转换（不符合规则的转换）
        abnormal_count = 0
        
        # 查询所有状态转换
        all_transitions_query = select(
            OrderStatusLog.from_status,
            OrderStatusLog.to_status
        ).where(OrderStatusLog.from_status.isnot(None))
        
        result = await self.db.execute(all_transitions_query)
        all_transitions = result.all()
        
        from app.schemas.status import ORDER_STATUS_TRANSITIONS
        
        for from_status, to_status in all_transitions:
            allowed = ORDER_STATUS_TRANSITIONS.get(from_status, [])
            if to_status not in allowed:
                abnormal_count += 1
        
        return StatusStatisticsSchema(
            status_distribution=status_distribution,
            avg_transition_time=avg_transition_time,
            abnormal_transitions=abnormal_count,
            total_orders=total_orders
        )
    
    async def batch_update_status(
        self,
        order_ids: List[UUID],
        to_status: str,
        changed_by: UUID,
        change_reason: str
    ) -> List[OrderStatusLog]:
        """
        批量更新订单状态
        
        Args:
            order_ids: 订单ID列表
            to_status: 目标状态
            changed_by: 变更人
            change_reason: 变更原因
            
        Returns:
            List[OrderStatusLog]: 状态日志列表
        """
        logs = []
        
        for order_id in order_ids:
            try:
                log = await self.log_status_change(
                    order_id=order_id,
                    to_status=to_status,
                    changed_by=changed_by,
                    change_reason=change_reason
                )
                logs.append(log)
            except ValueError as e:
                # 记录失败的订单，但继续处理其他订单
                print(f"订单 {order_id} 状态更新失败: {str(e)}")
                continue
        
        return logs