from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func, desc, asc, delete
from sqlalchemy.orm import selectinload
from datetime import datetime
from app.models.user import (
    User, UserStatistics, UserActivity,
    UserRoleHistory, VerificationCode, UserStatus, UserRoleEnum
)
from app.models.permission import UserRole, Role
from app.schemas.user import *
from app.core.security import get_password_hash, verify_password, generate_verification_code
from app.exceptions import AuthenticationError, ValidationError, BusinessLogicError, PermissionError
from app.core.redis import redis_client
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List, Tuple
import json

class UserService:
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def get_current_user_info(self, user_id: str) -> Dict[str, Any]:
        """获取当前用户详细信息"""
        # 获取用户基本信息
        result = await self.db.execute(
            select(User).where(User.id == user_id)
        )
        user = result.scalar_one_or_none()
        
        if not user:
            raise ValidationError("用户不存在")
        
        # 获取用户角色（使用新的权限系统）
        try:
            from app.services.permission_service import PermissionService
            permission_service = PermissionService(self.db)
            user_roles = permission_service.get_user_roles(str(user.id))
            roles = [
                {
                    "role": role["role_code"],
                    "is_active": role["role_status"] == "active",
                    "created_at": role.get("assigned_at", datetime.utcnow()).isoformat() if isinstance(role.get("assigned_at"), datetime) else datetime.utcnow().isoformat()
                }
                for role in user_roles
            ]
        except Exception as e:
            print(f"获取用户角色失败: {e}")
            # 使用默认角色
            roles = []
        
        # 获取用户统计信息
        stats_result = await self.db.execute(
            select(UserStatistics).where(UserStatistics.user_id == user.id)
        )
        stats = stats_result.scalar_one_or_none()
        
        if not stats:
            # 创建默认统计记录
            stats = UserStatistics(user_id=user.id)
            self.db.add(stats)
            await self.db.commit()
        
        statistics = {
            "total_orders": stats.total_orders,
            "total_spent": float(stats.total_spent),
            "active_projects": stats.active_projects,
            "referral_count": stats.referral_count,
            "login_count": stats.login_count
        }
        
        return {
            "id": str(user.id),
            "email": user.email,
            "full_name": user.full_name,
            "phone": user.phone,
            "status": user.status.value,
            "email_verified": user.email_verified,
            "phone_verified": user.phone_verified or False,
            "referral_code": user.referral_code or "",
            "roles": roles,
            "statistics": statistics,
            "created_at": user.created_at.isoformat() if user.created_at else datetime.utcnow().isoformat(),
            "last_login_at": user.last_login_at.isoformat() if user.last_login_at else None,
            "profile_completed": len(roles) > 0 and user.full_name and user.email_verified
        }
    
    async def update_user_info(self, user_id: str, request: UserUpdateRequest) -> Dict[str, Any]:
        """更新用户基本信息"""
        result = await self.db.execute(
            select(User).where(User.id == user_id)
        )
        user = result.scalar_one_or_none()
        
        if not user:
            raise ValidationError("用户不存在")
        
        # 记录变更前的信息
        old_info = {
            "full_name": user.full_name,
            "phone": user.phone
        }
        
        # 更新信息
        if request.full_name is not None:
            user.full_name = request.full_name
        
        if request.phone is not None:
            # 检查手机号是否已被其他用户使用
            if request.phone != user.phone:
                existing_phone = await self.db.execute(
                    select(User).where(and_(User.phone == request.phone, User.id != user_id))
                )
                if existing_phone.scalar_one_or_none():
                    raise ValidationError("手机号已被其他用户使用")
            user.phone = request.phone
        
        await self.db.commit()
        
        # 记录用户活动
        await self._log_user_activity(
            user_id, "update_profile", 
            f"更新个人信息: {json.dumps(old_info)} -> {json.dumps({'full_name': user.full_name, 'phone': user.phone})}"
        )
        
        return await self.get_current_user_info(user_id)
    
    async def change_password(self, user_id: str, request: PasswordChangeRequest) -> Dict[str, Any]:
        """修改密码"""
        result = await self.db.execute(
            select(User).where(User.id == user_id)
        )
        user = result.scalar_one_or_none()
        
        if not user:
            raise ValidationError("用户不存在")
        
        # 验证当前密码
        if not verify_password(request.current_password, user.password_hash):
            raise AuthenticationError("当前密码错误")
        
        # 检查新密码是否与当前密码相同
        if verify_password(request.new_password, user.password_hash):
            raise ValidationError("新密码不能与当前密码相同")
        
        # 更新密码
        user.password_hash = get_password_hash(request.new_password)
        await self.db.commit()
        
        # 记录用户活动
        await self._log_user_activity(user_id, "change_password", "用户修改密码")
        
        return {
            "message": "密码修改成功",
            "changed_at": datetime.utcnow().isoformat()
        }
    
    
    async def get_user_list(self, query: UserListQuery, admin_user_id: str) -> Dict[str, Any]:
        """管理员获取用户列表（简化版本）"""
        try:
            # 简化查询，先只获取基本用户信息
            base_query = select(User)

            # 简单的状态筛选
            if query.status:
                try:
                    base_query = base_query.where(User.status == UserStatus(query.status))
                except ValueError:
                    pass  # 忽略无效的状态值

            # 简单的搜索
            if query.search:
                search_term = f"%{query.search}%"
                base_query = base_query.where(
                    or_(
                        User.email.ilike(search_term),
                        User.full_name.ilike(search_term)
                    )
                )

            # 排序
            if query.sort_order == "desc":
                base_query = base_query.order_by(desc(User.created_at))
            else:
                base_query = base_query.order_by(asc(User.created_at))

            # 获取总数
            count_query = select(func.count(User.id))
            if query.status:
                try:
                    count_query = count_query.where(User.status == UserStatus(query.status))
                except ValueError:
                    pass
            if query.search:
                search_term = f"%{query.search}%"
                count_query = count_query.where(
                    or_(
                        User.email.ilike(search_term),
                        User.full_name.ilike(search_term)
                    )
                )

            total_result = await self.db.execute(count_query)
            total = total_result.scalar()

            # 分页查询
            offset = (query.page - 1) * query.size
            paginated_query = base_query.offset(offset).limit(query.size)

            result = await self.db.execute(paginated_query)
            users = result.scalars().all()

            # 构建用户列表，包含角色信息
            user_list = []
            for user in users:
                # 获取用户角色信息
                try:
                    from app.services.permission_service import PermissionService
                    permission_service = PermissionService(self.db)
                    user_roles = permission_service.get_user_roles(str(user.id))
                    roles = [
                        {
                            "role": role["role_code"],
                            "role_name": role.get("role_name", role["role_code"]),
                            "is_active": role["role_status"] == "active",
                            "created_at": role.get("assigned_at", datetime.utcnow()).isoformat() if isinstance(role.get("assigned_at"), datetime) else datetime.utcnow().isoformat()
                        }
                        for role in user_roles
                    ]
                except Exception as e:
                    print(f"获取用户 {user.id} 角色失败: {e}")
                    roles = []

                user_list.append({
                    "id": str(user.id),
                    "email": user.email,
                    "full_name": user.full_name or "",
                    "phone": user.phone,
                    "status": user.status.value if user.status else "unknown",
                    "email_verified": user.email_verified,
                    "phone_verified": user.phone_verified or False,
                    "referral_code": user.referral_code or "",
                    "roles": roles,
                    "statistics": {
                        "total_orders": 0,
                        "total_spent": 0.0,
                        "active_projects": 0,
                        "referral_count": 0,
                        "login_count": 0
                    },
                    "created_at": user.created_at.isoformat() if user.created_at else datetime.utcnow().isoformat(),
                    "last_login_at": user.last_login_at.isoformat() if user.last_login_at else None,
                    "profile_completed": bool(user.full_name and user.email_verified)
                })

            pages = (total + query.size - 1) // query.size if total > 0 else 1

            return {
                "users": user_list,
                "total": total,
                "page": query.page,
                "size": query.size,
                "pages": pages
            }

        except Exception as e:
            raise e
    
    async def _verify_verification_code(self, email: str, code: str, code_type: str):
        """验证验证码"""
        result = await self.db.execute(
            select(VerificationCode).where(
                and_(
                    VerificationCode.email == email,
                    VerificationCode.code == code,
                    VerificationCode.code_type == code_type,
                    VerificationCode.is_used == False,
                    VerificationCode.expires_at > datetime.utcnow()
                )
            )
        )
        verification_code = result.scalar_one_or_none()
        
        if not verification_code:
            raise ValidationError("验证码无效或已过期")
        
        # 标记验证码为已使用
        verification_code.is_used = True
        await self.db.commit()
    
    async def get_user_detail(self, user_id: str, admin_user_id: str) -> Dict[str, Any]:
        """管理员查看用户详情"""
        return await self.get_current_user_info(user_id)

    async def update_user_status(self, user_id: str, request: UserStatusUpdateRequest, admin_user_id: str) -> Dict[str, Any]:
        """管理员更新用户状态"""
        result = await self.db.execute(
            select(User).where(User.id == user_id)
        )
        user = result.scalar_one_or_none()

        if not user:
            raise ValidationError("用户不存在")

        old_status = user.status.value
        user.status = UserStatus(request.status)
        await self.db.commit()

        # 记录状态变更历史
        await self._log_user_activity(
            user_id, "status_change",
            f"管理员 {admin_user_id} 将用户状态从 {old_status} 更改为 {request.status}，原因: {request.reason}"
        )

        # TODO: 如果需要通知用户，发送邮件或站内信

        return {
            "message": "用户状态更新成功",
            "old_status": old_status,
            "new_status": request.status,
            "reason": request.reason,
            "updated_at": datetime.utcnow().isoformat()
        }

    async def admin_update_user_info(self, user_id: str, request: UserUpdateRequest, admin_user_id: str) -> Dict[str, Any]:
        """管理员更新用户基本信息"""
        # 获取用户
        user_result = await self.db.execute(
            select(User).where(User.id == user_id)
        )
        user = user_result.scalar_one_or_none()
        if not user:
            raise ValidationError("用户不存在")

        # 记录原始数据用于日志
        old_data = {
            "full_name": user.full_name,
            "phone": user.phone
        }

        # 更新字段
        if request.full_name is not None:
            user.full_name = request.full_name
        if request.phone is not None:
            user.phone = request.phone

        user.updated_at = datetime.utcnow()
        await self.db.commit()

        # 记录管理员操作
        await self._log_user_activity(
            user_id, "admin_update_info",
            f"管理员 {admin_user_id} 更新了用户基本信息"
        )

        return {
            "user": {
                "id": str(user.id),
                "email": user.email,
                "full_name": user.full_name,
                "phone": user.phone,
                "status": user.status.value,
                "updated_at": user.updated_at.isoformat()
            }
        }

    async def admin_create_user(self, request: AdminCreateUserRequest, admin_user_id: str) -> Dict[str, Any]:
        """管理员创建用户"""
        print(f"🔍 开始创建用户: {request.email}")
        print(f"🔍 请求数据: {request}")

        # 检查邮箱是否已存在
        existing_user = await self.db.execute(
            select(User).where(User.email == request.email)
        )
        if existing_user.scalar_one_or_none():
            print(f"❌ 邮箱已存在: {request.email}")
            raise ValidationError("邮箱已存在")

        # 检查手机号是否已存在
        if request.phone:
            existing_phone = await self.db.execute(
                select(User).where(User.phone == request.phone)
            )
            if existing_phone.scalar_one_or_none():
                raise ValidationError("手机号已存在")

        # 生成密码（如果未提供）
        if not request.password:
            import secrets
            import string
            request.password = ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(12))

        # 生成推荐码
        from app.core.security import generate_referral_code
        referral_code = await self._generate_unique_referral_code()

        # 创建用户
        new_user = User(
            email=request.email,
            full_name=request.full_name,
            phone=request.phone,
            password_hash=get_password_hash(request.password),
            status=UserStatus(request.status),
            email_verified=True,  # 管理员创建的用户默认邮箱已验证
            referral_code=referral_code
        )

        self.db.add(new_user)
        await self.db.flush()  # 获取用户ID

        # 分配角色（使用新的权限系统）
        from app.services.permission_service import PermissionService
        permission_service = PermissionService(self.db)

        # 角色映射
        role_mapping = {
            "ENTERPRISE_USER": "enterprise_user",
            "CHANNEL_USER": "channel_user",
            "AGENT_USER": "agent_user",
            "REGULAR_USER": "regular_user",
            "ADMIN": "admin",
            # 支持小写格式
            "enterprise_user": "enterprise_user",
            "channel_user": "channel_user",
            "agent_user": "agent_user",
            "regular_user": "regular_user",
            "admin": "admin"
        }
        role_code = role_mapping.get(request.role, "regular_user")
        # 分配角色
        try:
            # 查找角色
            role = await self.db.execute(
                select(Role).where(Role.role_code == role_code)
            )
            role_obj = role.scalar_one_or_none()

            if role_obj:
                # 创建用户角色关联
                user_role = UserRole(
                    user_id=new_user.id,
                    role_id=role_obj.id,
                    assigned_by=admin_user_id,
                    role_status="active"
                )
                self.db.add(user_role)
                print(f"角色分配成功: {role_code}")
            else:
                print(f"角色不存在: {role_code}")
                # 查看数据库中有哪些角色
                all_roles = await self.db.execute(select(Role))
                existing_roles = all_roles.scalars().all()
                print(f"数据库中存在的角色: {[r.role_code for r in existing_roles]}")
                raise ValidationError(f"角色 '{role_code}' 不存在于系统中")
        except ValidationError:
            raise  # 重新抛出验证错误
        except Exception as e:
            print(f"角色分配失败: {e}")
            raise ValidationError(f"角色分配失败: {str(e)}")

        # 创建用户统计记录
        user_stats = UserStatistics(user_id=new_user.id)
        self.db.add(user_stats)

        await self.db.commit()

        # 记录管理员操作
        await self._log_user_activity(
            str(new_user.id), "admin_create",
            f"管理员 {admin_user_id} 创建了用户账号，角色: {request.role}"
        )

        # TODO: 如果需要发送通知，发送邮件给新用户

        return {
            "user": {
                "id": str(new_user.id),
                "email": new_user.email,
                "full_name": new_user.full_name,
                "phone": new_user.phone,
                "status": new_user.status.value,
                "role": request.role,
                "referral_code": new_user.referral_code or "",
                "created_at": new_user.created_at.isoformat()
            },
            "temporary_password": request.password if request.send_notification else None,
            "force_password_reset": request.force_password_reset
        }

    async def update_user_roles(self, user_id: str, request: UserRoleUpdateRequest, admin_user_id: str) -> Dict[str, Any]:
        """管理员分配用户角色"""
        result = await self.db.execute(
            select(User).where(User.id == user_id)
        )
        user = result.scalar_one_or_none()

        if not user:
            raise ValidationError("用户不存在")

        # TODO: 暂时注释掉，等待权限系统完全迁移后再实现
        # 获取当前角色
        current_roles = []  # 临时处理

        # 记录角色变更历史
        role_history = UserRoleHistory(
            user_id=user_id,
            old_roles=current_roles,
            new_roles=request.roles,
            reason=request.reason,
            operator_id=admin_user_id
        )
        self.db.add(role_history)

        await self.db.commit()

        # 记录用户活动
        await self._log_user_activity(
            user_id, "role_change",
            f"管理员 {admin_user_id} 将用户角色从 {current_roles} 更改为 {request.roles}，原因: {request.reason}"
        )

        return {
            "message": "用户角色更新成功",
            "old_roles": current_roles,
            "new_roles": request.roles,
            "reason": request.reason,
            "updated_at": datetime.utcnow().isoformat()
        }

    async def get_user_role_bindings(self, user_id: str, admin_user_id: str) -> Dict[str, Any]:
        """查看用户角色绑定状态"""
        # TODO: 暂时注释掉，等待权限系统完全迁移后再实现
        # 获取当前角色
        current_roles = []  # 临时处理

        # 获取角色变更历史
        history_result = await self.db.execute(
            select(UserRoleHistory)
            .where(UserRoleHistory.user_id == user_id)
            .order_by(desc(UserRoleHistory.created_at))
            .limit(10)
        )
        role_history = [
            {
                "old_roles": history.old_roles,
                "new_roles": history.new_roles,
                "reason": history.reason,
                "operator_id": str(history.operator_id) if history.operator_id else None,
                "created_at": history.created_at.isoformat()
            }
            for history in history_result.scalars().all()
        ]

        # TODO: 根据角色计算权限列表
        permissions = []
        for role_info in current_roles:
            role = role_info["role"]
            if role == "super_admin":
                permissions.extend(["*"])  # 超级管理员拥有所有权限
            elif role == "admin":
                permissions.extend(["admin:*"])
            elif role == "enterprise_user":
                permissions.extend(["user:read", "user:write", "content:read", "content:write"])
            # 添加更多角色权限映射

        return {
            "user_id": user_id,
            "current_roles": current_roles,
            "role_history": role_history,
            "permissions": list(set(permissions))  # 去重
        }

    async def _generate_unique_referral_code(self) -> str:
        """生成唯一推荐码"""
        from app.core.security import generate_referral_code
        max_attempts = 10
        for _ in range(max_attempts):
            code = generate_referral_code()
            result = await self.db.execute(
                select(User).where(User.referral_code == code)
            )
            if not result.scalar_one_or_none():
                return code

        raise BusinessLogicError("生成推荐码失败，请重试")

    async def delete_user(self, user_id: str, admin_user_id: str) -> Dict[str, Any]:
        """管理员删除用户（硬删除）"""
        try:
            print(f"🔍 开始删除用户: {user_id}")

            # 查找用户
            result = await self.db.execute(select(User).where(User.id == user_id))
            user = result.scalar_one_or_none()

            if not user:
                print(f"❌ 用户不存在: {user_id}")
                raise ValidationError("用户不存在")

            # 检查是否为系统管理员用户，防止删除关键用户
            if user.email in ['<EMAIL>', '<EMAIL>', '<EMAIL>']:
                print(f"❌ 尝试删除系统管理员用户: {user.email}")
                raise ValidationError("不能删除系统管理员用户")

            # 保存用户信息用于返回
            user_info = {
                "id": str(user.id),
                "email": user.email,
                "full_name": user.full_name or "",
                "status": user.status.value,
                "deleted_at": datetime.utcnow().isoformat(),
                "deleted_by": admin_user_id
            }

            # 先清除用户的所有角色绑定
            try:
                from app.models.permission import UserRole
                await self.db.execute(
                    delete(UserRole).where(UserRole.user_id == user_id)
                )
                print(f"✅ 清除用户角色绑定成功")
            except Exception as e:
                print(f"⚠️  清除用户角色绑定失败: {e}")

            # 清除用户统计数据
            try:
                await self.db.execute(
                    delete(UserStatistics).where(UserStatistics.user_id == user_id)
                )
                print(f"✅ 清除用户统计数据成功")
            except Exception as e:
                print(f"⚠️  清除用户统计数据失败: {e}")

            # 清除用户活动记录
            try:
                await self.db.execute(
                    delete(UserActivity).where(UserActivity.user_id == user_id)
                )
                print(f"✅ 清除用户活动记录成功")
            except Exception as e:
                print(f"⚠️  清除用户活动记录失败: {e}")

            # 最后删除用户记录
            await self.db.execute(delete(User).where(User.id == user_id))
            print(f"✅ 删除用户记录成功")

            await self.db.commit()
            print(f"✅ 用户删除完成: {user_id}")

            return user_info

        except ValidationError:
            await self.db.rollback()
            raise
        except Exception as e:
            await self.db.rollback()
            raise Exception(f"删除用户失败: {str(e)}")

    async def _log_user_activity(self, user_id: str, action: str, description: str, ip_address: str = None, user_agent: str = None):
        """记录用户活动"""
        activity = UserActivity(
            user_id=user_id,
            action=action,
            description=description,
            ip_address=ip_address,
            user_agent=user_agent
        )
        self.db.add(activity)
        await self.db.commit()
