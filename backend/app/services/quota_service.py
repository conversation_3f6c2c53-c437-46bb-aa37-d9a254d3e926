from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func, update
from app.models.order import QuotaUsage, QuotaAlert, Subscription
from app.schemas.subscription import *
from app.exceptions import ValidationError, NotFoundError, PermissionError
from typing import Optional, Dict, Any, List
from datetime import datetime
from decimal import Decimal
import uuid

class QuotaService:
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def get_quota_usage(self, subscription_id: str, user_id: str) -> QuotaUsageResponse:
        """Get current quota usage for subscription"""
        # Verify subscription ownership
        subscription = await self._verify_subscription_ownership(subscription_id, user_id)
        
        # Get or create current month's quota usage
        usage_month = datetime.utcnow().strftime("%Y-%m")
        quota_usage = await self._get_or_create_quota_usage(subscription_id, user_id, usage_month)
        
        # Get active alerts
        alerts = await self._get_active_alerts(subscription_id)
        
        # Build quota details
        quota_details = self._build_quota_details(quota_usage)
        
        return QuotaUsageResponse(
            subscription_id=subscription_id,
            user_id=user_id,
            usage_month=usage_month,
            user_type=quota_usage.user_type,
            content_requests=quota_details.get("content_requests"),
            monitoring_projects=quota_details.get("monitoring_projects"),
            api_calls=quota_details.get("api_calls"),
            team_members=quota_details.get("team_members"),
            service_orders=quota_details.get("service_orders"),
            channels=quota_details.get("channels"),
            last_updated=quota_usage.updated_at,
            alerts=[self._format_alert(alert) for alert in alerts]
        )
    
    async def increase_quota(self, subscription_id: str, user_id: str, request: QuotaIncrease) -> Dict[str, Any]:
        """Increase quota for specific type"""
        # Verify subscription ownership
        await self._verify_subscription_ownership(subscription_id, user_id)
        
        # Get current month's quota usage
        usage_month = datetime.utcnow().strftime("%Y-%m")
        quota_usage = await self._get_or_create_quota_usage(subscription_id, user_id, usage_month)
        
        # Map quota type to field names
        quota_mapping = {
            QuotaType.CONTENT: ("max_content_requests", "used_content_requests"),
            QuotaType.MONITORING: ("max_monitoring_projects", "used_monitoring_projects"),
            QuotaType.API: ("max_api_calls", "used_api_calls"),
            QuotaType.TEAM: ("max_team_members", "used_team_members"),
            QuotaType.SERVICE: ("max_service_orders", "used_service_orders"),
            QuotaType.CHANNEL: ("max_channels", "used_channels")
        }
        
        if request.quota_type not in quota_mapping:
            raise ValidationError(f"Invalid quota type: {request.quota_type}")
        
        max_field, _ = quota_mapping[request.quota_type]
        
        # Increase quota
        current_max = getattr(quota_usage, max_field)
        new_max = current_max + request.amount
        setattr(quota_usage, max_field, new_max)
        quota_usage.updated_at = datetime.utcnow()
        
        await self.db.commit()
        await self.db.refresh(quota_usage)
        
        return {
            "subscription_id": subscription_id,
            "quota_type": request.quota_type,
            "previous_limit": current_max,
            "new_limit": new_max,
            "increase_amount": request.amount,
            "expires_at": request.expires_at,
            "reason": request.reason,
            "status": "success"
        }
    
    async def consume_quota(self, subscription_id: str, quota_type: QuotaType, amount: int = 1) -> Dict[str, Any]:
        """Consume quota (internal use)"""
        usage_month = datetime.utcnow().strftime("%Y-%m")
        
        # Get quota usage
        result = await self.db.execute(
            select(QuotaUsage).where(
                and_(
                    QuotaUsage.subscription_id == subscription_id,
                    QuotaUsage.usage_month == usage_month
                )
            )
        )
        quota_usage = result.scalar_one_or_none()
        
        if not quota_usage:
            raise NotFoundError("Quota usage record not found")
        
        # Map quota type to field names
        quota_mapping = {
            QuotaType.CONTENT: ("max_content_requests", "used_content_requests"),
            QuotaType.MONITORING: ("max_monitoring_projects", "used_monitoring_projects"),
            QuotaType.API: ("max_api_calls", "used_api_calls"),
            QuotaType.TEAM: ("max_team_members", "used_team_members"),
            QuotaType.SERVICE: ("max_service_orders", "used_service_orders"),
            QuotaType.CHANNEL: ("max_channels", "used_channels")
        }
        
        if quota_type not in quota_mapping:
            raise ValidationError(f"Invalid quota type: {quota_type}")
        
        max_field, used_field = quota_mapping[quota_type]
        
        # Check if quota is available
        max_value = getattr(quota_usage, max_field)
        used_value = getattr(quota_usage, used_field)
        
        if used_value + amount > max_value:
            raise ValidationError(f"Insufficient {quota_type} quota. Available: {max_value - used_value}, Requested: {amount}")
        
        # Consume quota
        setattr(quota_usage, used_field, used_value + amount)
        quota_usage.updated_at = datetime.utcnow()
        
        await self.db.commit()
        
        # Check if alert threshold is reached
        percentage_used = ((used_value + amount) / max_value * 100) if max_value > 0 else 0
        await self._check_and_trigger_alerts(subscription_id, quota_type, percentage_used)
        
        return {
            "quota_type": quota_type,
            "consumed": amount,
            "used": used_value + amount,
            "remaining": max_value - (used_value + amount),
            "max": max_value,
            "percentage_used": percentage_used
        }
    
    async def reset_quota(self, subscription_id: str, user_id: str, quota_type: QuotaType) -> Dict[str, Any]:
        """Reset quota usage for specific type"""
        # Verify subscription ownership
        await self._verify_subscription_ownership(subscription_id, user_id)
        
        usage_month = datetime.utcnow().strftime("%Y-%m")
        quota_usage = await self._get_or_create_quota_usage(subscription_id, user_id, usage_month)
        
        # Map quota type to field names
        quota_mapping = {
            QuotaType.CONTENT: "used_content_requests",
            QuotaType.MONITORING: "used_monitoring_projects",
            QuotaType.API: "used_api_calls",
            QuotaType.TEAM: "used_team_members",
            QuotaType.SERVICE: "used_service_orders",
            QuotaType.CHANNEL: "used_channels"
        }
        
        if quota_type not in quota_mapping:
            raise ValidationError(f"Invalid quota type: {quota_type}")
        
        used_field = quota_mapping[quota_type]
        previous_value = getattr(quota_usage, used_field)
        
        # Reset to 0 (except team members which resets to 1)
        reset_value = 1 if quota_type == QuotaType.TEAM else 0
        setattr(quota_usage, used_field, reset_value)
        quota_usage.updated_at = datetime.utcnow()
        
        await self.db.commit()
        
        return {
            "subscription_id": subscription_id,
            "quota_type": quota_type,
            "previous_usage": previous_value,
            "current_usage": reset_value,
            "reset_at": datetime.utcnow(),
            "status": "success"
        }
    
    async def get_quota_alerts(self, user_id: str) -> List[Dict[str, Any]]:
        """Get all quota alerts for user"""
        result = await self.db.execute(
            select(QuotaAlert).where(
                and_(
                    QuotaAlert.user_id == user_id,
                    QuotaAlert.is_active == True
                )
            ).order_by(QuotaAlert.created_at.desc())
        )
        alerts = result.scalars().all()
        
        return [self._format_alert(alert) for alert in alerts]
    
    async def set_quota_alert(self, subscription_id: str, user_id: str, request: QuotaAlertSettings) -> Dict[str, Any]:
        """Set or update quota alert settings"""
        # Verify subscription ownership
        await self._verify_subscription_ownership(subscription_id, user_id)
        
        # Check if alert already exists
        result = await self.db.execute(
            select(QuotaAlert).where(
                and_(
                    QuotaAlert.subscription_id == subscription_id,
                    QuotaAlert.alert_type == request.quota_type
                )
            )
        )
        existing_alert = result.scalar_one_or_none()
        
        if existing_alert:
            # Update existing alert
            existing_alert.threshold_percentage = request.threshold_percentage
            existing_alert.notification_channels = request.notification_channels
            existing_alert.notification_frequency = request.notification_frequency
            existing_alert.is_active = True
            existing_alert.updated_at = datetime.utcnow()
            alert = existing_alert
        else:
            # Create new alert
            alert = QuotaAlert(
                id=uuid.uuid4(),
                subscription_id=subscription_id,
                user_id=user_id,
                alert_type=request.quota_type,
                threshold_percentage=request.threshold_percentage,
                notification_channels=request.notification_channels,
                notification_frequency=request.notification_frequency,
                is_active=True,
                created_at=datetime.utcnow()
            )
            self.db.add(alert)
        
        await self.db.commit()
        await self.db.refresh(alert)
        
        return self._format_alert(alert)
    
    async def check_quota_availability(self, subscription_id: str, quota_type: QuotaType, required_amount: int) -> bool:
        """Check if enough quota is available"""
        usage_month = datetime.utcnow().strftime("%Y-%m")
        
        result = await self.db.execute(
            select(QuotaUsage).where(
                and_(
                    QuotaUsage.subscription_id == subscription_id,
                    QuotaUsage.usage_month == usage_month
                )
            )
        )
        quota_usage = result.scalar_one_or_none()
        
        if not quota_usage:
            return False
        
        # Map quota type to field names
        quota_mapping = {
            QuotaType.CONTENT: ("max_content_requests", "used_content_requests"),
            QuotaType.MONITORING: ("max_monitoring_projects", "used_monitoring_projects"),
            QuotaType.API: ("max_api_calls", "used_api_calls"),
            QuotaType.TEAM: ("max_team_members", "used_team_members"),
            QuotaType.SERVICE: ("max_service_orders", "used_service_orders"),
            QuotaType.CHANNEL: ("max_channels", "used_channels")
        }
        
        if quota_type not in quota_mapping:
            return False
        
        max_field, used_field = quota_mapping[quota_type]
        max_value = getattr(quota_usage, max_field)
        used_value = getattr(quota_usage, used_field)
        
        return (max_value - used_value) >= required_amount
    
    # ============= Helper Methods =============
    
    async def _verify_subscription_ownership(self, subscription_id: str, user_id: str) -> Subscription:
        """Verify user owns the subscription"""
        result = await self.db.execute(
            select(Subscription).where(Subscription.id == subscription_id)
        )
        subscription = result.scalar_one_or_none()
        
        if not subscription:
            raise NotFoundError(f"Subscription {subscription_id} not found")
        
        if str(subscription.user_id) != user_id:
            raise PermissionError("No permission to access this subscription")
        
        return subscription
    
    async def _get_or_create_quota_usage(self, subscription_id: str, user_id: str, usage_month: str) -> QuotaUsage:
        """Get or create quota usage record for current month"""
        result = await self.db.execute(
            select(QuotaUsage).where(
                and_(
                    QuotaUsage.subscription_id == subscription_id,
                    QuotaUsage.usage_month == usage_month
                )
            )
        )
        quota_usage = result.scalar_one_or_none()
        
        if not quota_usage:
            # Get subscription to determine quotas
            sub_result = await self.db.execute(
                select(Subscription).where(Subscription.id == subscription_id)
            )
            subscription = sub_result.scalar_one_or_none()
            
            if not subscription:
                raise NotFoundError("Subscription not found")
            
            # 通过用户ID查询是否有公司信息来判断用户类型
            from app.models.company import Company
            company_result = await self.db.execute(
                select(Company).where(Company.user_id == user_id)
            )
            company = company_result.scalar_one_or_none()
            user_type = "enterprise" if company else "provider"

            # Create new quota usage record
            quota_usage = QuotaUsage(
                id=uuid.uuid4(),
                user_id=user_id,
                subscription_id=subscription_id,
                usage_month=usage_month,
                user_type=user_type,
                max_content_requests=subscription.content_quota,
                max_monitoring_projects=subscription.monitoring_quota,
                max_api_calls=subscription.ai_quota,
                max_team_members=1,  # Default
                max_service_orders=0,  # Default for enterprise
                max_channels=1,  # Default
                used_content_requests=0,
                used_monitoring_projects=0,
                used_api_calls=0,
                used_team_members=1,
                used_service_orders=0,
                used_channels=0,
                created_at=datetime.utcnow()
            )
            self.db.add(quota_usage)
            await self.db.commit()
            await self.db.refresh(quota_usage)
        
        return quota_usage
    
    def _build_quota_details(self, quota_usage: QuotaUsage) -> Dict[str, QuotaUsageDetail]:
        """Build quota usage details"""
        details = {}
        
        # Content requests
        details["content_requests"] = QuotaUsageDetail(
            quota_type="content",
            max_value=quota_usage.max_content_requests,
            used_value=quota_usage.used_content_requests,
            remaining=quota_usage.max_content_requests - quota_usage.used_content_requests,
            percentage_used=(quota_usage.used_content_requests / quota_usage.max_content_requests * 100) if quota_usage.max_content_requests > 0 else 0,
            reset_date=self._get_next_month_start()
        )
        
        # Monitoring projects
        details["monitoring_projects"] = QuotaUsageDetail(
            quota_type="monitoring",
            max_value=quota_usage.max_monitoring_projects,
            used_value=quota_usage.used_monitoring_projects,
            remaining=quota_usage.max_monitoring_projects - quota_usage.used_monitoring_projects,
            percentage_used=(quota_usage.used_monitoring_projects / quota_usage.max_monitoring_projects * 100) if quota_usage.max_monitoring_projects > 0 else 0,
            reset_date=self._get_next_month_start()
        )
        
        # API calls
        details["api_calls"] = QuotaUsageDetail(
            quota_type="api",
            max_value=quota_usage.max_api_calls,
            used_value=quota_usage.used_api_calls,
            remaining=quota_usage.max_api_calls - quota_usage.used_api_calls,
            percentage_used=(quota_usage.used_api_calls / quota_usage.max_api_calls * 100) if quota_usage.max_api_calls > 0 else 0,
            reset_date=self._get_next_month_start()
        )
        
        # Team members
        details["team_members"] = QuotaUsageDetail(
            quota_type="team",
            max_value=quota_usage.max_team_members,
            used_value=quota_usage.used_team_members,
            remaining=quota_usage.max_team_members - quota_usage.used_team_members,
            percentage_used=(quota_usage.used_team_members / quota_usage.max_team_members * 100) if quota_usage.max_team_members > 0 else 0,
            reset_date=None  # Team members don't reset
        )
        
        # Provider-specific quotas
        if quota_usage.user_type == "provider":
            details["service_orders"] = QuotaUsageDetail(
                quota_type="service",
                max_value=quota_usage.max_service_orders,
                used_value=quota_usage.used_service_orders,
                remaining=quota_usage.max_service_orders - quota_usage.used_service_orders,
                percentage_used=(quota_usage.used_service_orders / quota_usage.max_service_orders * 100) if quota_usage.max_service_orders > 0 else 0,
                reset_date=self._get_next_month_start()
            )
            
            details["channels"] = QuotaUsageDetail(
                quota_type="channel",
                max_value=quota_usage.max_channels,
                used_value=quota_usage.used_channels,
                remaining=quota_usage.max_channels - quota_usage.used_channels,
                percentage_used=(quota_usage.used_channels / quota_usage.max_channels * 100) if quota_usage.max_channels > 0 else 0,
                reset_date=None  # Channels don't reset
            )
        
        return details
    
    async def _get_active_alerts(self, subscription_id: str) -> List[QuotaAlert]:
        """Get active alerts for subscription"""
        result = await self.db.execute(
            select(QuotaAlert).where(
                and_(
                    QuotaAlert.subscription_id == subscription_id,
                    QuotaAlert.is_active == True
                )
            )
        )
        return result.scalars().all()
    
    def _format_alert(self, alert: QuotaAlert) -> Dict[str, Any]:
        """Format alert for response"""
        return {
            "id": str(alert.id),
            "alert_type": alert.alert_type,
            "threshold_percentage": alert.threshold_percentage,
            "notification_channels": alert.notification_channels,
            "notification_frequency": alert.notification_frequency,
            "is_active": alert.is_active,
            "last_alert_at": alert.last_alert_at,
            "created_at": alert.created_at
        }
    
    async def _check_and_trigger_alerts(self, subscription_id: str, quota_type: str, percentage_used: float):
        """Check if any alerts should be triggered"""
        result = await self.db.execute(
            select(QuotaAlert).where(
                and_(
                    QuotaAlert.subscription_id == subscription_id,
                    QuotaAlert.alert_type == quota_type,
                    QuotaAlert.is_active == True,
                    QuotaAlert.threshold_percentage <= percentage_used
                )
            )
        )
        alerts = result.scalars().all()
        
        for alert in alerts:
            # Check if we should send alert based on frequency
            should_alert = False
            if alert.notification_frequency == "once":
                should_alert = alert.last_alert_at is None
            elif alert.notification_frequency == "daily":
                should_alert = alert.last_alert_at is None or (datetime.utcnow() - alert.last_alert_at).days >= 1
            elif alert.notification_frequency == "weekly":
                should_alert = alert.last_alert_at is None or (datetime.utcnow() - alert.last_alert_at).days >= 7
            
            if should_alert:
                # TODO: Send actual notification through notification service
                alert.last_alert_at = datetime.utcnow()
                await self.db.commit()
    
    def _get_next_month_start(self) -> datetime:
        """Get the start of next month"""
        today = datetime.utcnow()
        if today.month == 12:
            return datetime(today.year + 1, 1, 1)
        else:
            return datetime(today.year, today.month + 1, 1)