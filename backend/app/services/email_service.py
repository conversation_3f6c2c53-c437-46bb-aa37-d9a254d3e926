import smtplib
import asyncio
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.header import Header
from typing import Dict, Any
from app.config import settings
import logging

logger = logging.getLogger(__name__)


class EmailService:
    """邮件服务类"""
    
    def __init__(self):
        self.smtp_host = settings.smtp_host
        self.smtp_port = settings.smtp_port
        self.smtp_username = settings.smtp_username
        self.smtp_password = settings.smtp_password
        self.smtp_use_tls = settings.smtp_use_tls
        
    async def send_verification_code(
        self,
        email: str,
        code: str,
        code_type: str
    ) -> Dict[str, Any]:
        """发送验证码邮件"""
        try:
            # 检查邮件配置
            if not all([self.smtp_username, self.smtp_password]):
                logger.error("邮件服务配置不完整")
                return {
                    "success": False,
                    "message": "邮件服务配置不完整"
                }
            
            # 获取邮件主题和内容
            subject = self._get_subject(code_type)
            html_content = self._get_html_content(code, code_type, email)
            
            # 创建邮件 - 使用纯文本格式
            msg = MIMEText(html_content, 'plain', 'utf-8')
            msg['Subject'] = Header(subject, 'utf-8')
            msg['From'] = f"{self.smtp_username}"
            msg['To'] = email
            
            # 在线程池中发送邮件（避免阻塞异步事件循环）
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(None, self._send_email_sync, msg)
            
            logger.info(f"验证码邮件发送成功: {email}")
            return {
                "success": True,
                "message": "邮件发送成功"
            }
            
        except Exception as e:
            logger.error(f"发送验证码邮件失败: {email}, 错误: {str(e)}")
            return {
                "success": False,
                "message": f"邮件发送失败: {str(e)}"
            }
    
    def _send_email_sync(self, msg):
        """同步发送邮件"""
        try:
            logger.info(f"连接SMTP服务器: {self.smtp_host}:{self.smtp_port}")

            # 根据端口选择连接方式
            if self.smtp_port == 465:
                # 使用SSL连接
                server = smtplib.SMTP_SSL(self.smtp_host, self.smtp_port, timeout=30)
            else:
                # 使用普通连接
                server = smtplib.SMTP(self.smtp_host, self.smtp_port, timeout=30)
                if self.smtp_use_tls:
                    logger.info("启用TLS加密")
                    server.starttls()

            try:
                logger.info(f"使用用户名登录: {self.smtp_username}")
                server.login(self.smtp_username, self.smtp_password)

                logger.info("发送邮件")
                result = server.send_message(msg)
                logger.info(f"邮件发送成功: {result}")

            finally:
                # 手动关闭连接，忽略QUIT命令的错误
                try:
                    server.quit()
                except Exception as quit_error:
                    logger.warning(f"QUIT命令出现错误（可忽略）: {quit_error}")
                    try:
                        server.close()
                    except Exception:
                        pass

        except Exception as e:
            logger.error(f"SMTP发送邮件详细错误: {e}")
            # 如果错误信息包含成功的标识，则认为发送成功
            if "250 OK" in str(e) or "queued as" in str(e):
                logger.info("邮件实际发送成功，忽略连接关闭错误")
                return
            raise
    
    def _get_subject(self, code_type: str) -> str:
        """获取邮件主题"""
        subjects = {
            'register': '【GEO平台】注册验证码',
            'login': '【GEO平台】登录验证码',
            'reset_password': '【GEO平台】密码重置验证码',
            'bind_email': '【GEO平台】邮箱绑定验证码'
        }
        return subjects.get(code_type, '【GEO平台】验证码')
    
    def _get_html_content(self, code: str, code_type: str, email: str) -> str:
        """获取邮件HTML内容"""
        type_names = {
            'register': '注册',
            'login': '登录',
            'reset_password': '密码重置',
            'bind_email': '邮箱绑定'
        }
        
        type_name = type_names.get(code_type, '验证')
        
        # 使用简洁的文本格式
        html_content = f"""您好！

您的{type_name}验证码是：{code}

验证码有效期为10分钟，请及时使用。
如非本人操作，请忽略此邮件。

此邮件由系统自动发送，请勿回复。"""
        
        return html_content


# 创建全局邮件服务实例
email_service = EmailService()
