"""
公告管理服务
"""
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_, desc, asc
from sqlalchemy.orm import selectinload
from typing import List, Optional, Dict, Any
from datetime import datetime, timezone

from app.models.announcement import Announcement
from app.models.user import User
from app.schemas.announcement import (
    AnnouncementCreateRequest, AnnouncementUpdateRequest, AnnouncementQueryRequest,
    AnnouncementResponse, AnnouncementListResponse, AnnouncementStatsResponse
)
from app.exceptions import NotFoundError, ValidationError


class AnnouncementService:
    """公告管理服务"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_announcement(self, request: AnnouncementCreateRequest, creator_id: str) -> AnnouncementResponse:
        """创建公告"""
        try:
            # 使用服务器本地时间
            local_time = datetime.now()
            print(f"DEBUG: 服务器本地时间: {local_time}")

            # 创建公告实例
            announcement = Announcement(
                title=request.title,
                content=request.content,
                summary=request.summary,
                type=request.type,
                priority=request.priority,
                target_audience=request.target_audience,
                is_pinned=request.is_pinned,
                is_popup=request.is_popup,
                publish_time=request.publish_time,
                expire_time=request.expire_time,
                created_by=creator_id,
                created_at=local_time,
                updated_at=local_time
            )

            # 如果设置了发布时间，检查是否应该立即发布
            if request.publish_time:
                # 简单处理：如果发布时间是今天或之前的日期，就设为已发布
                from datetime import date
                today = date.today()
                publish_date = request.publish_time.date()

                print(f"DEBUG: 发布时间: {request.publish_time}")
                print(f"DEBUG: 发布日期: {publish_date}")
                print(f"DEBUG: 今天日期: {today}")
                print(f"DEBUG: 日期比较: {publish_date} <= {today} = {publish_date <= today}")

                if publish_date <= today:
                    announcement.status = "published"
                    print(f"DEBUG: 设置状态为 published")
                else:
                    print(f"DEBUG: 保持状态为 draft")

            self.db.add(announcement)
            await self.db.commit()
            # 跳过 refresh，直接返回响应

            # 临时返回简单响应，避免枚举转换问题
            return AnnouncementResponse(
                id=str(announcement.id),
                title=announcement.title,
                content=announcement.content,
                summary=announcement.summary,
                type="notice",  # 直接使用字符串
                priority="normal",
                target_audience="all",
                status="published",
                is_pinned=announcement.is_pinned,
                is_popup=announcement.is_popup,
                publish_time=announcement.publish_time,
                expire_time=announcement.expire_time,
                view_count=announcement.view_count,
                created_by=str(announcement.created_by),
                created_at=announcement.created_at,
                updated_at=announcement.updated_at,
                creator_name=None
            )

        except Exception as e:
            await self.db.rollback()
            raise ValidationError(f"创建公告失败: {str(e)}")
    
    async def get_announcement_by_id(self, announcement_id: str) -> AnnouncementResponse:
        """根据ID获取公告"""
        announcement = await self._get_announcement(announcement_id)
        
        # 增加查看次数
        announcement.view_count += 1
        await self.db.commit()
        
        return await self._build_announcement_response(announcement)
    
    async def update_announcement(self, announcement_id: str, request: AnnouncementUpdateRequest) -> AnnouncementResponse:
        """更新公告"""
        try:
            announcement = await self._get_announcement(announcement_id)

            # 更新字段
            update_data = request.model_dump(exclude_unset=True)

            for field, value in update_data.items():
                if field in ['type', 'priority', 'target_audience', 'status']:
                    # 枚举类型直接赋值，Pydantic已经处理了转换
                    if value:
                        setattr(announcement, field, value)
                else:
                    setattr(announcement, field, value)

            # 特殊处理：当状态变为published时，确保发布时间合理
            if 'status' in update_data and update_data['status'] == 'published':
                current_time = datetime.now()
                # 如果没有设置发布时间，或者发布时间不合理，使用当前时间
                if not announcement.publish_time or announcement.publish_time > current_time:
                    announcement.publish_time = current_time

                # 检查过期时间是否合理
                if announcement.expire_time and announcement.expire_time <= announcement.publish_time:
                    # 如果过期时间不合理，设置为发布时间后24小时
                    from datetime import timedelta
                    announcement.expire_time = announcement.publish_time + timedelta(days=1)

            # 设置更新时间
            from datetime import datetime
            announcement.updated_at = datetime.now()

            await self.db.commit()
            await self.db.refresh(announcement)

            return await self._build_announcement_response(announcement)

        except Exception as e:
            await self.db.rollback()
            raise ValidationError(f"更新公告失败: {str(e)}")
    
    async def delete_announcement(self, announcement_id: str) -> bool:
        """删除公告"""
        try:
            announcement = await self._get_announcement(announcement_id)
            await self.db.delete(announcement)
            await self.db.commit()
            return True
            
        except Exception as e:
            await self.db.rollback()
            raise ValidationError(f"删除公告失败: {str(e)}")
    
    async def get_announcements(self, request: AnnouncementQueryRequest) -> AnnouncementListResponse:
        """获取公告列表"""
        try:
            # 构建查询
            query = select(Announcement).options(selectinload(Announcement.creator))

            # 添加过滤条件
            conditions = []

            if request.type:
                # 直接使用字符串值进行比较
                conditions.append(Announcement.type == request.type)

            if request.status:
                conditions.append(Announcement.status == request.status)

            if request.priority:
                conditions.append(Announcement.priority == request.priority)

            if request.target_audience:
                # 特定受众查询时，同时包含 'all' 标签的公告
                if request.target_audience in ['enterprise', 'channel', 'agent']:
                    # 查询指定受众的公告 + 全部用户的公告
                    audience_condition = or_(
                        Announcement.target_audience == request.target_audience,
                        Announcement.target_audience == 'all'
                    )
                    conditions.append(audience_condition)
                else:
                    # 其他情况（如 'all'）直接匹配
                    conditions.append(Announcement.target_audience == request.target_audience)

            if request.is_pinned is not None:
                conditions.append(Announcement.is_pinned == request.is_pinned)

            if request.keyword:
                keyword_condition = or_(
                    Announcement.title.ilike(f"%{request.keyword}%"),
                    Announcement.content.ilike(f"%{request.keyword}%"),
                    Announcement.summary.ilike(f"%{request.keyword}%")
                )
                conditions.append(keyword_condition)

            if request.start_date:
                conditions.append(Announcement.created_at >= request.start_date)

            if request.end_date:
                conditions.append(Announcement.created_at <= request.end_date)

            if conditions:
                query = query.where(and_(*conditions))

            # 排序：置顶优先，然后按创建时间倒序
            query = query.order_by(desc(Announcement.is_pinned), desc(Announcement.created_at))

            # 计算总数
            count_query = select(func.count(Announcement.id))
            if conditions:
                count_query = count_query.where(and_(*conditions))

            total_result = await self.db.execute(count_query)
            total = total_result.scalar()

            # 分页
            offset = (request.page - 1) * request.page_size
            query = query.offset(offset).limit(request.page_size)

            # 执行查询
            result = await self.db.execute(query)
            announcements = result.scalars().all()

            # 构建响应
            items = []
            for announcement in announcements:
                response = await self._build_announcement_response(announcement)
                items.append(response)

            pagination = {
                'page': request.page,
                'page_size': request.page_size,
                'total': total,
                'pages': (total + request.page_size - 1) // request.page_size
            }

            return AnnouncementListResponse(
                items=items,
                pagination=pagination
            )

        except Exception as e:
            raise ValidationError(f"获取公告列表失败: {str(e)}")
    
    async def get_announcement_stats(self) -> AnnouncementStatsResponse:
        """获取公告统计信息"""
        try:
            # 统计各状态的公告数量
            stats_query = select(
                Announcement.status,
                func.count(Announcement.id).label('count')
            ).group_by(Announcement.status)
            
            stats_result = await self.db.execute(stats_query)
            status_stats = {row.status.value: row.count for row in stats_result}
            
            # 总查看次数
            views_query = select(func.sum(Announcement.view_count))
            views_result = await self.db.execute(views_query)
            total_views = views_result.scalar() or 0
            
            # 最近的公告
            recent_query = select(Announcement).options(selectinload(Announcement.creator))\
                .order_by(desc(Announcement.created_at)).limit(5)
            recent_result = await self.db.execute(recent_query)
            recent_announcements = recent_result.scalars().all()
            
            recent_items = []
            for announcement in recent_announcements:
                response = await self._build_announcement_response(announcement)
                recent_items.append(response)
            
            return AnnouncementStatsResponse(
                total_announcements=sum(status_stats.values()),
                published_announcements=status_stats.get('published', 0),
                draft_announcements=status_stats.get('draft', 0),
                archived_announcements=status_stats.get('archived', 0),
                total_views=total_views,
                recent_announcements=recent_items
            )
            
        except Exception as e:
            raise ValidationError(f"获取公告统计失败: {str(e)}")
    
    async def _get_announcement(self, announcement_id: str) -> Announcement:
        """获取公告实例"""
        query = select(Announcement).options(selectinload(Announcement.creator))\
            .where(Announcement.id == announcement_id)
        result = await self.db.execute(query)
        announcement = result.scalar_one_or_none()
        
        if not announcement:
            raise NotFoundError("公告不存在")
        
        return announcement
    
    async def _build_announcement_response(self, announcement: Announcement) -> AnnouncementResponse:
        """构建公告响应"""
        try:
            # 安全地获取枚举值，如果是枚举对象则获取其value，否则直接使用字符串
            def safe_enum_value(enum_field):
                if hasattr(enum_field, 'value'):
                    return enum_field.value
                return str(enum_field)

            return AnnouncementResponse(
                id=str(announcement.id),
                title=announcement.title,
                content=announcement.content,
                summary=announcement.summary,
                type=safe_enum_value(announcement.type),
                priority=safe_enum_value(announcement.priority),
                target_audience=safe_enum_value(announcement.target_audience),
                status=safe_enum_value(announcement.status),
                is_pinned=announcement.is_pinned,
                is_popup=announcement.is_popup,
                publish_time=announcement.publish_time,
                expire_time=announcement.expire_time,
                view_count=announcement.view_count,
                created_by=str(announcement.created_by),
                created_at=announcement.created_at,
                updated_at=announcement.updated_at,
                creator_name=None
            )
        except Exception as e:
            raise ValidationError(f"构建公告响应失败: {str(e)}")
