"""
角色权限管理服务
"""
from typing import List, Optional, Dict, Any, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, delete, and_, func, text
from sqlalchemy.orm import selectinload
from fastapi import HTTPException

from app.models.permission import Permission, Role, RolePermission, UserRole
from app.models.user import User
from app.schemas.role_permission import *
from app.exceptions import ValidationError


class RolePermissionService:
    """角色权限管理服务"""

    def __init__(self, db: AsyncSession):
        self.db = db

    async def get_role_permissions(
        self, 
        role_id: Optional[str] = None,
        permission_id: Optional[str] = None,
        page: int = 1,
        page_size: int = 20
    ) -> RolePermissionListResponse:
        """获取角色权限列表"""
        try:
            # 构建查询
            query = select(
                RolePermission.id,
                RolePermission.role_id,
                RolePermission.permission_id,
                RolePermission.granted_by,
                RolePermission.granted_at,
                Role.role_name,
                Role.role_code,
                Permission.permission_name,
                Permission.permission_code,
                User.full_name.label('granted_by_name')
            ).select_from(
                RolePermission
            ).join(
                Role, RolePermission.role_id == Role.id
            ).join(
                Permission, RolePermission.permission_id == Permission.id
            ).outerjoin(
                User, RolePermission.granted_by == User.id
            )

            # 添加过滤条件
            if role_id:
                query = query.where(RolePermission.role_id == role_id)
            if permission_id:
                query = query.where(RolePermission.permission_id == permission_id)

            # 获取总数
            count_query = select(func.count()).select_from(query.subquery())
            total_result = await self.db.execute(count_query)
            total = total_result.scalar()

            # 分页查询
            offset = (page - 1) * page_size
            query = query.offset(offset).limit(page_size).order_by(Role.role_code, Permission.permission_code)
            
            result = await self.db.execute(query)
            rows = result.fetchall()

            items = []
            for row in rows:
                items.append(RolePermissionResponse(
                    id=str(row.id),
                    role_id=str(row.role_id),
                    permission_id=str(row.permission_id),
                    role_name=row.role_name,
                    role_code=row.role_code,
                    permission_name=row.permission_name,
                    permission_code=row.permission_code,
                    granted_by=str(row.granted_by) if row.granted_by else None,
                    granted_by_name=row.granted_by_name,
                    granted_at=row.granted_at
                ))

            return RolePermissionListResponse(
                total=total,
                items=items,
                page=page,
                page_size=page_size
            )

        except Exception as e:
            raise HTTPException(status_code=500, detail=f"获取角色权限列表失败: {str(e)}")

    async def create_role_permission(self, request: RolePermissionCreate, granted_by: str) -> RolePermissionResponse:
        """创建角色权限关联"""
        try:
            # 验证角色是否存在
            role_result = await self.db.execute(select(Role).where(Role.id == request.role_id))
            role = role_result.scalar_one_or_none()
            if not role:
                raise ValidationError("角色不存在")

            # 验证权限是否存在
            permission_result = await self.db.execute(select(Permission).where(Permission.id == request.permission_id))
            permission = permission_result.scalar_one_or_none()
            if not permission:
                raise ValidationError("权限不存在")

            # 检查是否已存在
            existing_result = await self.db.execute(
                select(RolePermission).where(
                    and_(
                        RolePermission.role_id == request.role_id,
                        RolePermission.permission_id == request.permission_id
                    )
                )
            )
            if existing_result.scalar_one_or_none():
                raise ValidationError("该角色已拥有此权限")

            # 创建角色权限关联
            role_permission = RolePermission(
                role_id=request.role_id,
                permission_id=request.permission_id,
                granted_by=granted_by
            )

            self.db.add(role_permission)
            await self.db.commit()
            await self.db.refresh(role_permission)

            # 获取完整信息返回
            return await self._get_role_permission_detail(role_permission.id)

        except ValidationError:
            raise
        except Exception as e:
            await self.db.rollback()
            raise HTTPException(status_code=500, detail=f"创建角色权限关联失败: {str(e)}")

    async def delete_role_permission(self, role_permission_id: str) -> bool:
        """删除角色权限关联"""
        try:
            # 查找角色权限关联
            result = await self.db.execute(
                select(RolePermission).where(RolePermission.id == role_permission_id)
            )
            role_permission = result.scalar_one_or_none()
            
            if not role_permission:
                raise ValidationError("角色权限关联不存在")

            await self.db.delete(role_permission)
            await self.db.commit()
            
            return True

        except ValidationError:
            raise
        except Exception as e:
            await self.db.rollback()
            raise HTTPException(status_code=500, detail=f"删除角色权限关联失败: {str(e)}")

    async def get_roles_with_permissions(self) -> List[RoleWithPermissions]:
        """获取所有角色及其权限"""
        try:
            # 查询所有角色
            roles_result = await self.db.execute(
                select(Role).where(Role.is_active == True).order_by(Role.role_code)
            )
            roles = roles_result.scalars().all()

            result = []
            for role in roles:
                # 获取角色的权限
                permissions_result = await self.db.execute(
                    select(
                        Permission.id,
                        Permission.permission_name,
                        Permission.permission_code,
                        Permission.module,
                        Permission.action,
                        Permission.resource
                    ).select_from(
                        RolePermission
                    ).join(
                        Permission, RolePermission.permission_id == Permission.id
                    ).where(
                        RolePermission.role_id == role.id
                    ).order_by(Permission.module, Permission.permission_code)
                )
                
                permissions = []
                for perm in permissions_result.fetchall():
                    permissions.append({
                        "id": str(perm.id),
                        "permission_name": perm.permission_name,
                        "permission_code": perm.permission_code,
                        "module": perm.module,
                        "action": perm.action,
                        "resource": perm.resource
                    })

                result.append(RoleWithPermissions(
                    id=str(role.id),
                    role_name=role.role_name,
                    role_code=role.role_code,
                    role_type=role.role_type,
                    description=role.description,
                    is_active=role.is_active,
                    permissions=permissions,
                    permission_count=len(permissions)
                ))

            return result

        except Exception as e:
            raise HTTPException(status_code=500, detail=f"获取角色权限信息失败: {str(e)}")

    async def get_permissions_with_roles(self) -> List[PermissionWithRoles]:
        """获取所有权限及其角色"""
        try:
            # 查询所有权限
            permissions_result = await self.db.execute(
                select(Permission).order_by(Permission.module, Permission.permission_code)
            )
            permissions = permissions_result.scalars().all()

            result = []
            for permission in permissions:
                # 获取拥有此权限的角色
                roles_result = await self.db.execute(
                    select(
                        Role.id,
                        Role.role_name,
                        Role.role_code,
                        Role.role_type
                    ).select_from(
                        RolePermission
                    ).join(
                        Role, RolePermission.role_id == Role.id
                    ).where(
                        and_(
                            RolePermission.permission_id == permission.id,
                            Role.is_active == True
                        )
                    ).order_by(Role.role_code)
                )
                
                roles = []
                for role in roles_result.fetchall():
                    roles.append({
                        "id": str(role.id),
                        "role_name": role.role_name,
                        "role_code": role.role_code,
                        "role_type": role.role_type
                    })

                result.append(PermissionWithRoles(
                    id=str(permission.id),
                    permission_name=permission.permission_name,
                    permission_code=permission.permission_code,
                    module=permission.module,
                    action=permission.action,
                    resource=permission.resource,
                    description=permission.description,
                    roles=roles,
                    role_count=len(roles)
                ))

            return result

        except Exception as e:
            raise HTTPException(status_code=500, detail=f"获取权限角色信息失败: {str(e)}")

    async def batch_role_permissions(self, request: BatchRolePermissionRequest, granted_by: str) -> Dict[str, Any]:
        """批量操作角色权限"""
        try:
            # 验证角色是否存在
            role_result = await self.db.execute(select(Role).where(Role.id == request.role_id))
            role = role_result.scalar_one_or_none()
            if not role:
                raise ValidationError("角色不存在")

            success_count = 0
            failed_count = 0
            errors = []

            if request.action == "add":
                # 批量添加权限
                for permission_id in request.permission_ids:
                    try:
                        # 检查权限是否存在
                        perm_result = await self.db.execute(select(Permission).where(Permission.id == permission_id))
                        if not perm_result.scalar_one_or_none():
                            errors.append(f"权限 {permission_id} 不存在")
                            failed_count += 1
                            continue

                        # 检查是否已存在
                        existing = await self.db.execute(
                            select(RolePermission).where(
                                and_(
                                    RolePermission.role_id == request.role_id,
                                    RolePermission.permission_id == permission_id
                                )
                            )
                        )
                        if existing.scalar_one_or_none():
                            errors.append(f"角色已拥有权限 {permission_id}")
                            failed_count += 1
                            continue

                        # 创建关联
                        role_permission = RolePermission(
                            role_id=request.role_id,
                            permission_id=permission_id,
                            granted_by=granted_by
                        )
                        self.db.add(role_permission)
                        success_count += 1

                    except Exception as e:
                        errors.append(f"添加权限 {permission_id} 失败: {str(e)}")
                        failed_count += 1

            elif request.action == "remove":
                # 批量移除权限
                for permission_id in request.permission_ids:
                    try:
                        result = await self.db.execute(
                            delete(RolePermission).where(
                                and_(
                                    RolePermission.role_id == request.role_id,
                                    RolePermission.permission_id == permission_id
                                )
                            )
                        )
                        if result.rowcount > 0:
                            success_count += 1
                        else:
                            errors.append(f"角色权限关联 {permission_id} 不存在")
                            failed_count += 1

                    except Exception as e:
                        errors.append(f"移除权限 {permission_id} 失败: {str(e)}")
                        failed_count += 1

            await self.db.commit()

            return {
                "success_count": success_count,
                "failed_count": failed_count,
                "errors": errors,
                "role_name": role.role_name
            }

        except ValidationError:
            raise
        except Exception as e:
            await self.db.rollback()
            raise HTTPException(status_code=500, detail=f"批量操作角色权限失败: {str(e)}")

    async def _get_role_permission_detail(self, role_permission_id: str) -> RolePermissionResponse:
        """获取角色权限详情"""
        result = await self.db.execute(
            select(
                RolePermission.id,
                RolePermission.role_id,
                RolePermission.permission_id,
                RolePermission.granted_by,
                RolePermission.granted_at,
                Role.role_name,
                Role.role_code,
                Permission.permission_name,
                Permission.permission_code,
                User.full_name.label('granted_by_name')
            ).select_from(
                RolePermission
            ).join(
                Role, RolePermission.role_id == Role.id
            ).join(
                Permission, RolePermission.permission_id == Permission.id
            ).outerjoin(
                User, RolePermission.granted_by == User.id
            ).where(
                RolePermission.id == role_permission_id
            )
        )
        
        row = result.fetchone()
        if not row:
            raise ValidationError("角色权限关联不存在")

        return RolePermissionResponse(
            id=str(row.id),
            role_id=str(row.role_id),
            permission_id=str(row.permission_id),
            role_name=row.role_name,
            role_code=row.role_code,
            permission_name=row.permission_name,
            permission_code=row.permission_code,
            granted_by=str(row.granted_by) if row.granted_by else None,
            granted_by_name=row.granted_by_name,
            granted_at=row.granted_at
        )
