from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from app.models.system import SystemConfig, ConfigCategory
from typing import Dict, Any, Optional
import json
from decimal import Decimal


class ConfigService:
    """系统配置服务"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self._cache = {}  # 简单的内存缓存
    
    async def get_config(self, config_key: str, default_value: Any = None) -> Any:
        """获取配置值"""
        # 先从缓存获取
        if config_key in self._cache:
            return self._cache[config_key]
        
        # 从数据库获取
        result = await self.db.execute(
            select(SystemConfig).where(
                SystemConfig.config_key == config_key,
                SystemConfig.is_active == True
            )
        )
        config = result.scalar_one_or_none()
        
        if not config:
            return default_value
        
        # 根据类型转换值
        value = self._convert_config_value(config.config_value, config.config_type)
        
        # 缓存结果
        self._cache[config_key] = value
        
        return value
    
    async def set_config(self, config_key: str, config_value: Any, 
                        config_type: str = "string", category: ConfigCategory = ConfigCategory.SYSTEM,
                        display_name: str = None, description: str = None) -> bool:
        """设置配置值"""
        try:
            # 检查配置是否存在
            result = await self.db.execute(
                select(SystemConfig).where(SystemConfig.config_key == config_key)
            )
            config = result.scalar_one_or_none()
            
            # 转换值为字符串存储
            str_value = self._convert_to_string(config_value, config_type)
            
            if config:
                # 更新现有配置
                config.config_value = str_value
                config.config_type = config_type
                if display_name:
                    config.display_name = display_name
                if description:
                    config.description = description
            else:
                # 创建新配置
                config = SystemConfig(
                    config_key=config_key,
                    config_value=str_value,
                    config_type=config_type,
                    category=category,
                    display_name=display_name or config_key,
                    description=description or f"配置项: {config_key}"
                )
                self.db.add(config)
            
            await self.db.commit()
            
            # 更新缓存
            self._cache[config_key] = config_value
            
            return True
        except Exception as e:
            await self.db.rollback()
            print(f"设置配置失败: {e}")
            return False
    
    async def get_referral_config(self) -> Dict[str, Any]:
        """获取推广链接相关配置"""
        return {
            "base_url": await self.get_config("referral.base_url", "http://localhost:3000"),
            "commission_per_conversion": await self.get_config("referral.commission_per_conversion", Decimal("50.00")),
            "default_target_pages": await self.get_config("referral.default_target_pages", [
                {"page": "auth/register", "name": "注册页面推广链接", "description": "引导用户注册的推广链接"},
                {"page": "", "name": "首页推广链接", "description": "引导用户访问首页的推广链接"}
            ])
        }
    
    async def init_referral_configs(self) -> bool:
        """初始化推广链接相关配置"""
        try:
            # 基础URL配置
            await self.set_config(
                "referral.base_url",
                "http://localhost:3000",  # 开发环境使用localhost，生产环境需要修改为实际域名
                "string",
                ConfigCategory.SYSTEM,  # 暂时使用SYSTEM类别
                "推广链接基础URL",
                "推广链接的基础域名，用于生成完整的推广链接"
            )

            # 每次转化佣金配置
            await self.set_config(
                "referral.commission_per_conversion",
                "50.00",
                "decimal",
                ConfigCategory.SYSTEM,  # 暂时使用SYSTEM类别
                "每次转化佣金",
                "每个成功转化用户的佣金金额（元）"
            )

            # 默认目标页面配置
            default_pages = [
                {"page": "auth/register", "name": "注册页面推广链接", "description": "引导用户注册的推广链接"},
                {"page": "", "name": "首页推广链接", "description": "引导用户访问首页的推广链接"}  # 首页是根路径
            ]

            await self.set_config(
                "referral.default_target_pages",
                default_pages,
                "json",
                ConfigCategory.SYSTEM,  # 暂时使用SYSTEM类别
                "默认目标页面",
                "推广链接的默认目标页面列表"
            )
            
            return True
        except Exception as e:
            print(f"初始化推广链接配置失败: {e}")
            return False
    
    def _convert_config_value(self, value: str, config_type: str) -> Any:
        """将字符串配置值转换为对应类型"""
        if value is None:
            return None
        
        try:
            if config_type == "integer":
                return int(value)
            elif config_type == "boolean":
                return value.lower() in ("true", "1", "yes", "on")
            elif config_type == "json":
                return json.loads(value)
            elif config_type == "decimal":
                return Decimal(value)
            else:  # string
                return value
        except (ValueError, json.JSONDecodeError) as e:
            print(f"配置值转换失败: {e}")
            return value
    
    def _convert_to_string(self, value: Any, config_type: str) -> str:
        """将值转换为字符串存储"""
        if value is None:
            return ""
        
        if config_type == "json":
            return json.dumps(value, ensure_ascii=False)
        elif config_type == "boolean":
            return "true" if value else "false"
        else:
            return str(value)
    
    def clear_cache(self):
        """清空配置缓存"""
        self._cache.clear()
