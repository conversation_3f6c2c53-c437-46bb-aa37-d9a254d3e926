"""
Platform Integration Services
平台集成服务层
"""

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func
from sqlalchemy.orm import selectinload
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
import asyncio
import time

from app.models.platform import (
    PlatformConfig, PlatformCredential, PlatformToken, PlatformRoute
)
from app.schemas.platform import *
from app.core.encryption import credential_encryption
from app.services.platform_adapters import PlatformAdapterFactory
from app.exceptions import NotFoundError, ValidationError, BusinessLogicError


class PlatformConfigService:
    """平台配置管理服务"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def get_platform_configs(
        self, 
        platform_type: Optional[str] = None,
        is_active: Optional[bool] = None,
        page: int = 1,
        size: int = 20
    ) -> PlatformConfigListResponse:
        """获取平台配置列表"""
        query = select(PlatformConfig)
        
        # 应用过滤条件
        # platform_type 过滤暂时忽略，因为数据库中没有这个字段
        if is_active is not None:
            query = query.where(PlatformConfig.is_active == is_active)
        
        # 排序 - 只按创建时间排序，不按 display_order
        query = query.order_by(PlatformConfig.created_at)
        
        # 计算总数
        count_query = select(func.count()).select_from(query.subquery())
        total_result = await self.db.execute(count_query)
        total = total_result.scalar()
        
        # 分页
        offset = (page - 1) * size
        query = query.offset(offset).limit(size)
        
        # 执行查询
        result = await self.db.execute(query)
        configs = result.scalars().all()
        
        # 转换响应 - 使用模型的虚拟字段
        items = [
            PlatformConfigResponse(
                id=str(config.id),
                platform_code=config.platform_code,
                platform_name=config.platform_name,
                platform_type=config.platform_type,  # 使用模型的虚拟字段
                api_base_url=config.api_base_url,  # 使用模型的虚拟字段
                auth_type=config.auth_type,  # 使用模型的虚拟字段
                config_schema=config.config_schema,  # 使用模型的虚拟字段
                features=config.features,  # 使用模型的虚拟字段
                rate_limits=config.rate_limits,  # 使用模型的虚拟字段
                is_active=config.is_active,
                display_order=config.display_order,  # 使用模型的虚拟字段
                created_at=config.created_at,
                updated_at=config.updated_at
            )
            for config in configs
        ]
        
        return PlatformConfigListResponse(
            items=items,
            total=total,
            page=page,
            pages=(total + size - 1) // size
        )
    
    async def get_platform_config(self, platform_code: str) -> PlatformConfigResponse:
        """获取平台配置详情"""
        result = await self.db.execute(
            select(PlatformConfig).where(PlatformConfig.platform_code == platform_code)
        )
        config = result.scalar_one_or_none()
        
        if not config:
            raise NotFoundError(f"平台配置 {platform_code} 不存在")
        
        return PlatformConfigResponse(
            id=str(config.id),
            platform_code=config.platform_code,
            platform_name=config.platform_name,
            platform_type=config.platform_type,  # 使用模型的虚拟字段
            api_base_url=config.api_base_url,  # 使用模型的虚拟字段
            auth_type=config.auth_type,  # 使用模型的虚拟字段
            config_schema=config.config_schema,  # 使用模型的虚拟字段
            features=config.features,  # 使用模型的虚拟字段
            rate_limits=config.rate_limits,  # 使用模型的虚拟字段
            is_active=config.is_active,
            display_order=config.display_order,  # 使用模型的虚拟字段
            created_at=config.created_at,
            updated_at=config.updated_at
        )
    
    async def create_platform_config(
        self, 
        config_data: PlatformConfigCreate
    ) -> PlatformConfigResponse:
        """创建平台配置"""
        # 检查平台代码是否已存在
        existing = await self.db.execute(
            select(PlatformConfig).where(
                PlatformConfig.platform_code == config_data.platform_code
            )
        )
        if existing.scalar_one_or_none():
            raise ValidationError(f"平台代码 {config_data.platform_code} 已存在")
        
        # 创建配置 - 只保存数据库中实际存在的字段
        config = PlatformConfig(
            platform_code=config_data.platform_code,
            platform_name=config_data.platform_name,
            is_active=config_data.is_active
        )
        
        self.db.add(config)
        await self.db.commit()
        await self.db.refresh(config)
        
        return await self.get_platform_config(config.platform_code)
    
    async def update_platform_config(
        self, 
        platform_code: str,
        update_data: PlatformConfigUpdate
    ) -> PlatformConfigResponse:
        """更新平台配置"""
        result = await self.db.execute(
            select(PlatformConfig).where(PlatformConfig.platform_code == platform_code)
        )
        config = result.scalar_one_or_none()
        
        if not config:
            raise NotFoundError(f"平台配置 {platform_code} 不存在")
        
        # 只更新数据库中实际存在的字段
        if update_data.platform_name is not None:
            config.platform_name = update_data.platform_name
        if update_data.is_active is not None:
            config.is_active = update_data.is_active
        
        config.updated_at = datetime.utcnow()
        
        await self.db.commit()
        await self.db.refresh(config)
        
        return await self.get_platform_config(platform_code)


class PlatformCredentialService:
    """平台凭证管理服务"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def get_credentials_list(
        self,
        user_id: Optional[str] = None
    ) -> List[PlatformCredentialResponse]:
        """获取平台凭证列表"""
        query = select(PlatformCredential)
        
        if user_id:
            query = query.where(PlatformCredential.user_id == user_id)
        
        result = await self.db.execute(query)
        credentials = result.scalars().all()
        
        # 转换响应（不包含敏感信息）
        return [
            PlatformCredentialResponse(
                id=str(cred.id),
                platform_code=cred.platform_code,
                user_id=str(cred.user_id) if cred.user_id else None,
                credential_type=cred.credential_type,
                expires_at=cred.expires_at,
                is_valid=cred.is_valid,
                last_verified_at=cred.last_verified_at,
                created_at=cred.created_at,
                updated_at=cred.updated_at
            )
            for cred in credentials
        ]
    
    async def get_credential(
        self, 
        platform_code: str,
        user_id: Optional[str] = None
    ) -> PlatformCredentialDetailResponse:
        """获取平台凭证详情"""
        query = select(PlatformCredential).where(
            PlatformCredential.platform_code == platform_code
        )
        
        if user_id:
            query = query.where(PlatformCredential.user_id == user_id)
        
        result = await self.db.execute(query)
        credential = result.scalar_one_or_none()
        
        if not credential:
            raise NotFoundError(f"平台凭证 {platform_code} 不存在")
        
        # 解密凭证以生成提示信息
        try:
            decrypted = credential_encryption.decrypt(credential.credentials)
            # 生成脱敏提示
            credential_hint = {}
            for key, value in decrypted.items():
                if isinstance(value, str) and len(value) > 4:
                    # 显示前2个和后2个字符
                    credential_hint[key] = f"{value[:2]}...{value[-2:]}"
                else:
                    credential_hint[key] = "***"
        except:
            credential_hint = None
        
        return PlatformCredentialDetailResponse(
            id=str(credential.id),
            platform_code=credential.platform_code,
            user_id=str(credential.user_id) if credential.user_id else None,
            credential_type=credential.credential_type,
            expires_at=credential.expires_at,
            is_valid=credential.is_valid,
            last_verified_at=credential.last_verified_at,
            created_at=credential.created_at,
            updated_at=credential.updated_at,
            credential_hint=credential_hint
        )
    
    async def set_credential(
        self,
        platform_code: str,
        credential_data: PlatformCredentialCreate,
        user_id: Optional[str] = None
    ) -> PlatformCredentialResponse:
        """设置平台凭证"""
        # 检查平台是否存在
        config_result = await self.db.execute(
            select(PlatformConfig).where(PlatformConfig.platform_code == platform_code)
        )
        if not config_result.scalar_one_or_none():
            raise NotFoundError(f"平台 {platform_code} 不存在")
        
        # 检查是否已存在凭证
        existing = await self.db.execute(
            select(PlatformCredential).where(
                and_(
                    PlatformCredential.platform_code == platform_code,
                    PlatformCredential.user_id == user_id
                )
            )
        )
        
        # 加密凭证
        encrypted_credentials = credential_encryption.encrypt(credential_data.credentials)
        
        credential = existing.scalar_one_or_none()
        if credential:
            # 更新现有凭证
            credential.credentials = encrypted_credentials
            credential.credential_type = credential_data.credential_type.value
            credential.expires_at = credential_data.expires_at
            credential.is_valid = True
            credential.updated_at = datetime.utcnow()
        else:
            # 创建新凭证
            credential = PlatformCredential(
                platform_code=platform_code,
                user_id=user_id,
                credential_type=credential_data.credential_type.value,
                credentials=encrypted_credentials,
                expires_at=credential_data.expires_at,
                is_valid=True
            )
            self.db.add(credential)
        
        await self.db.commit()
        await self.db.refresh(credential)
        
        return PlatformCredentialResponse(
            id=str(credential.id),
            platform_code=credential.platform_code,
            user_id=str(credential.user_id) if credential.user_id else None,
            credential_type=credential.credential_type,
            expires_at=credential.expires_at,
            is_valid=credential.is_valid,
            last_verified_at=credential.last_verified_at,
            created_at=credential.created_at,
            updated_at=credential.updated_at
        )
    
    async def update_credential(
        self,
        platform_code: str,
        update_data: PlatformCredentialUpdate,
        user_id: Optional[str] = None
    ) -> PlatformCredentialResponse:
        """更新平台凭证"""
        query = select(PlatformCredential).where(
            PlatformCredential.platform_code == platform_code
        )
        
        if user_id:
            query = query.where(PlatformCredential.user_id == user_id)
        
        result = await self.db.execute(query)
        credential = result.scalar_one_or_none()
        
        if not credential:
            raise NotFoundError(f"平台凭证 {platform_code} 不存在")
        
        # 更新字段
        if update_data.credentials:
            credential.credentials = credential_encryption.encrypt(update_data.credentials)
        if update_data.expires_at is not None:
            credential.expires_at = update_data.expires_at
        if update_data.is_valid is not None:
            credential.is_valid = update_data.is_valid
        
        credential.updated_at = datetime.utcnow()
        
        await self.db.commit()
        await self.db.refresh(credential)
        
        return PlatformCredentialResponse(
            id=str(credential.id),
            platform_code=credential.platform_code,
            user_id=str(credential.user_id) if credential.user_id else None,
            credential_type=credential.credential_type,
            expires_at=credential.expires_at,
            is_valid=credential.is_valid,
            last_verified_at=credential.last_verified_at,
            created_at=credential.created_at,
            updated_at=credential.updated_at
        )
    
    async def test_connection(
        self,
        platform_code: str,
        user_id: Optional[str] = None
    ) -> TestConnectionResponse:
        """测试平台连接"""
        # 获取平台配置
        config_result = await self.db.execute(
            select(PlatformConfig).where(PlatformConfig.platform_code == platform_code)
        )
        config = config_result.scalar_one_or_none()
        
        if not config:
            raise NotFoundError(f"平台 {platform_code} 不存在")
        
        # 获取凭证
        cred_query = select(PlatformCredential).where(
            PlatformCredential.platform_code == platform_code
        )
        if user_id:
            cred_query = cred_query.where(PlatformCredential.user_id == user_id)
        
        cred_result = await self.db.execute(cred_query)
        credential = cred_result.scalar_one_or_none()
        
        if not credential:
            raise NotFoundError(f"平台凭证 {platform_code} 不存在")
        
        # 解密凭证
        decrypted_credentials = credential_encryption.decrypt(credential.credentials)
        
        # 创建适配器并测试连接
        adapter = PlatformAdapterFactory.create_adapter(
            platform_code,
            {
                'api_base_url': config.api_base_url,
                'auth_type': config.auth_type,
                'features': config.features
            },
            decrypted_credentials
        )
        
        start_time = time.time()
        try:
            result = await adapter.test_connection()
            response_time = int((time.time() - start_time) * 1000)
            
            # 更新凭证验证时间
            credential.last_verified_at = datetime.utcnow()
            credential.is_valid = result.get("success", False)
            await self.db.commit()
            
            return TestConnectionResponse(
                success=result.get("success", False),
                platform_code=platform_code,
                connection_status=result.get("connection_status", "unknown"),
                response_time_ms=response_time,
                details=result.get("details"),
                error=result.get("error"),
                tested_at=datetime.utcnow()
            )
        except Exception as e:
            response_time = int((time.time() - start_time) * 1000)
            
            # 标记凭证无效
            credential.is_valid = False
            await self.db.commit()
            
            return TestConnectionResponse(
                success=False,
                platform_code=platform_code,
                connection_status="failed",
                response_time_ms=response_time,
                error=str(e),
                tested_at=datetime.utcnow()
            )


class PlatformTokenService:
    """平台令牌管理服务"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def get_active_token(self, platform_code: str) -> PlatformTokenResponse:
        """获取当前有效令牌"""
        result = await self.db.execute(
            select(PlatformToken)
            .where(
                and_(
                    PlatformToken.platform_code == platform_code,
                    PlatformToken.is_active == True,
                    PlatformToken.expires_at > datetime.utcnow()
                )
            )
            .order_by(PlatformToken.created_at.desc())
            .limit(1)
        )
        token = result.scalar_one_or_none()
        
        if not token:
            raise NotFoundError(f"平台 {platform_code} 没有有效令牌")
        
        return PlatformTokenResponse(
            id=str(token.id),
            platform_code=token.platform_code,
            credential_id=str(token.credential_id) if token.credential_id else None,
            token_type=token.token_type,
            expires_at=token.expires_at,
            scope=token.scope,
            is_active=token.is_active,
            created_at=token.created_at,
            refreshed_at=token.refreshed_at
        )
    
    async def refresh_token(
        self, 
        platform_code: str,
        force_refresh: bool = False
    ) -> RefreshTokenResponse:
        """刷新平台令牌"""
        # 获取当前令牌
        token_result = await self.db.execute(
            select(PlatformToken)
            .where(
                and_(
                    PlatformToken.platform_code == platform_code,
                    PlatformToken.is_active == True
                )
            )
            .order_by(PlatformToken.created_at.desc())
            .limit(1)
        )
        current_token = token_result.scalar_one_or_none()
        
        # 检查是否需要刷新
        if not force_refresh and current_token:
            # 如果还有5分钟以上有效期，不刷新
            if current_token.expires_at > datetime.utcnow() + timedelta(minutes=5):
                return RefreshTokenResponse(
                    success=True,
                    platform_code=platform_code,
                    expires_at=current_token.expires_at,
                    refreshed_at=current_token.refreshed_at or current_token.created_at
                )
        
        # 获取平台配置和凭证
        config_result = await self.db.execute(
            select(PlatformConfig).where(PlatformConfig.platform_code == platform_code)
        )
        config = config_result.scalar_one_or_none()
        
        if not config:
            raise NotFoundError(f"平台 {platform_code} 不存在")
        
        cred_result = await self.db.execute(
            select(PlatformCredential).where(
                PlatformCredential.platform_code == platform_code
            )
        )
        credential = cred_result.scalar_one_or_none()
        
        if not credential:
            raise NotFoundError(f"平台凭证 {platform_code} 不存在")
        
        # 解密凭证
        decrypted_credentials = credential_encryption.decrypt(credential.credentials)
        
        # 创建适配器
        adapter = PlatformAdapterFactory.create_adapter(
            platform_code,
            {'api_base_url': config.api_base_url},
            decrypted_credentials
        )
        
        try:
            # 刷新令牌
            if current_token and current_token.refresh_token:
                decrypted_refresh = credential_encryption.decrypt_token(current_token.refresh_token)
                new_token_data = await adapter.refresh_token(decrypted_refresh)
            else:
                # 重新认证
                new_token_data = await adapter.authenticate(decrypted_credentials)
            
            # 保存新令牌
            new_token = PlatformToken(
                platform_code=platform_code,
                credential_id=credential.id,
                access_token=credential_encryption.encrypt_token(new_token_data["access_token"]),
                refresh_token=credential_encryption.encrypt_token(new_token_data.get("refresh_token", ""))
                    if new_token_data.get("refresh_token") else None,
                token_type=new_token_data.get("token_type", "Bearer"),
                expires_at=datetime.fromisoformat(new_token_data["expires_at"]),
                scope=new_token_data.get("scope"),
                is_active=True,
                refreshed_at=datetime.utcnow()
            )
            
            # 使旧令牌失效
            if current_token:
                current_token.is_active = False
            
            self.db.add(new_token)
            await self.db.commit()
            
            return RefreshTokenResponse(
                success=True,
                platform_code=platform_code,
                expires_at=new_token.expires_at,
                refreshed_at=new_token.refreshed_at
            )
            
        except Exception as e:
            raise BusinessLogicError(f"刷新令牌失败: {str(e)}")
    
    async def revoke_token(
        self,
        platform_code: str,
        reason: Optional[str] = None
    ) -> RevokeTokenResponse:
        """撤销平台令牌"""
        # 获取所有活动令牌
        result = await self.db.execute(
            select(PlatformToken).where(
                and_(
                    PlatformToken.platform_code == platform_code,
                    PlatformToken.is_active == True
                )
            )
        )
        tokens = result.scalars().all()
        
        if not tokens:
            raise NotFoundError(f"平台 {platform_code} 没有活动令牌")
        
        # 撤销所有活动令牌
        for token in tokens:
            token.is_active = False
        
        await self.db.commit()
        
        return RevokeTokenResponse(
            success=True,
            platform_code=platform_code,
            revoked_at=datetime.utcnow()
        )


class PlatformRouteService:
    """平台路由服务"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def get_routes(
        self,
        platform_code: Optional[str] = None,
        route_type: Optional[str] = None,
        route_status: Optional[str] = None,
        page: int = 1,
        size: int = 20
    ) -> PlatformRouteListResponse:
        """查询路由记录"""
        query = select(PlatformRoute)
        
        # 应用过滤条件
        if platform_code:
            query = query.where(PlatformRoute.platform_code == platform_code)
        if route_type:
            query = query.where(PlatformRoute.route_type == route_type)
        if route_status:
            query = query.where(PlatformRoute.route_status == route_status)
        
        # 排序
        query = query.order_by(PlatformRoute.created_at.desc())
        
        # 计算总数
        count_query = select(func.count()).select_from(query.subquery())
        total_result = await self.db.execute(count_query)
        total = total_result.scalar()
        
        # 分页
        offset = (page - 1) * size
        query = query.offset(offset).limit(size)
        
        # 执行查询
        result = await self.db.execute(query)
        routes = result.scalars().all()
        
        # 转换响应
        items = [
            PlatformRouteResponse(
                id=str(route.id),
                platform_code=route.platform_code,
                order_id=str(route.order_id) if route.order_id else None,
                request_id=str(route.request_id) if route.request_id else None,
                route_type=route.route_type,
                route_status=route.route_status,
                request_data=route.request_data,
                response_data=route.response_data,
                platform_order_id=route.platform_order_id,
                retry_count=route.retry_count,
                last_retry_at=route.last_retry_at,
                error_message=route.error_message,
                created_at=route.created_at,
                completed_at=route.completed_at
            )
            for route in routes
        ]
        
        return PlatformRouteListResponse(
            items=items,
            total=total,
            page=page,
            pages=(total + size - 1) // size
        )
    
    async def get_route_detail(self, route_id: str) -> PlatformRouteDetailResponse:
        """获取路由详情"""
        result = await self.db.execute(
            select(PlatformRoute)
            .options(selectinload(PlatformRoute.platform_config))
            .where(PlatformRoute.id == route_id)
        )
        route = result.scalar_one_or_none()
        
        if not route:
            raise NotFoundError(f"路由记录 {route_id} 不存在")
        
        # 获取平台配置
        config = route.platform_config
        
        return PlatformRouteDetailResponse(
            id=str(route.id),
            platform_code=route.platform_code,
            order_id=str(route.order_id) if route.order_id else None,
            request_id=str(route.request_id) if route.request_id else None,
            route_type=route.route_type,
            route_status=route.route_status,
            request_data=route.request_data,
            response_data=route.response_data,
            platform_order_id=route.platform_order_id,
            retry_count=route.retry_count,
            last_retry_at=route.last_retry_at,
            error_message=route.error_message,
            created_at=route.created_at,
            completed_at=route.completed_at,
            platform_config=PlatformConfigResponse(
                id=str(config.id),
                platform_code=config.platform_code,
                platform_name=config.platform_name,
                platform_type=config.platform_type,
                api_base_url=config.api_base_url,
                auth_type=config.auth_type,
                config_schema=config.config_schema,
                features=config.features,
                rate_limits=config.rate_limits,
                is_active=config.is_active,
                display_order=config.display_order,
                created_at=config.created_at,
                updated_at=config.updated_at
            ) if config else None
        )
    
    async def create_route(
        self,
        route_data: PlatformRouteCreate
    ) -> PlatformRouteResponse:
        """创建路由（发送到平台）"""
        # 获取平台配置
        config_result = await self.db.execute(
            select(PlatformConfig).where(
                PlatformConfig.platform_code == route_data.platform_code
            )
        )
        config = config_result.scalar_one_or_none()
        
        if not config:
            raise NotFoundError(f"平台 {route_data.platform_code} 不存在")
        
        # 创建路由记录
        route = PlatformRoute(
            platform_code=route_data.platform_code,
            order_id=route_data.order_id,
            request_id=route_data.request_id,
            route_type=route_data.route_type.value,
            route_status="pending",
            request_data=route_data.request_data,
            retry_count=0
        )
        
        self.db.add(route)
        await self.db.flush()
        
        try:
            # 获取令牌
            token_service = PlatformTokenService(self.db)
            token = await token_service.get_active_token(route_data.platform_code)
            
            # 获取凭证并解密
            cred_result = await self.db.execute(
                select(PlatformCredential).where(
                    PlatformCredential.id == token.credential_id
                )
            )
            credential = cred_result.scalar_one_or_none()
            
            if credential:
                decrypted_credentials = credential_encryption.decrypt(credential.credentials)
            else:
                decrypted_credentials = {}
            
            # 创建适配器
            adapter = PlatformAdapterFactory.create_adapter(
                route_data.platform_code,
                {'api_base_url': config.api_base_url},
                decrypted_credentials
            )
            
            # 发送请求
            route.route_status = "sent"
            await self.db.flush()
            
            if route_data.route_type == RouteType.ORDER:
                response = await adapter.create_order(route_data.request_data)
            elif route_data.route_type == RouteType.QUERY:
                response = await adapter.query_order(
                    route_data.request_data.get("order_id", "")
                )
            else:
                response = {"message": "Unsupported route type"}
            
            # 更新路由状态
            route.route_status = "success"
            route.response_data = response
            route.platform_order_id = response.get("order_id") or response.get("id")
            route.completed_at = datetime.utcnow()
            
        except Exception as e:
            # 记录错误
            route.route_status = "failed"
            route.error_message = str(e)
        
        await self.db.commit()
        await self.db.refresh(route)
        
        return PlatformRouteResponse(
            id=str(route.id),
            platform_code=route.platform_code,
            order_id=str(route.order_id) if route.order_id else None,
            request_id=str(route.request_id) if route.request_id else None,
            route_type=route.route_type,
            route_status=route.route_status,
            request_data=route.request_data,
            response_data=route.response_data,
            platform_order_id=route.platform_order_id,
            retry_count=route.retry_count,
            last_retry_at=route.last_retry_at,
            error_message=route.error_message,
            created_at=route.created_at,
            completed_at=route.completed_at
        )
    
    async def retry_route(
        self,
        route_id: str,
        max_retries: int = 3,
        retry_delay: int = 5
    ) -> RetryRouteResponse:
        """重试发送"""
        result = await self.db.execute(
            select(PlatformRoute).where(PlatformRoute.id == route_id)
        )
        route = result.scalar_one_or_none()
        
        if not route:
            raise NotFoundError(f"路由记录 {route_id} 不存在")
        
        if route.route_status == "success":
            return RetryRouteResponse(
                success=True,
                route_id=str(route.id),
                retry_count=route.retry_count,
                route_status=route.route_status,
                message="路由已成功，无需重试"
            )
        
        if route.retry_count >= max_retries:
            return RetryRouteResponse(
                success=False,
                route_id=str(route.id),
                retry_count=route.retry_count,
                route_status=route.route_status,
                message=f"已达到最大重试次数 {max_retries}"
            )
        
        # 延迟重试
        await asyncio.sleep(retry_delay)
        
        # 重新创建路由
        new_route_data = PlatformRouteCreate(
            platform_code=route.platform_code,
            order_id=str(route.order_id) if route.order_id else None,
            request_id=str(route.request_id) if route.request_id else None,
            route_type=route.route_type,
            request_data=route.request_data
        )
        
        # 更新重试计数
        route.retry_count += 1
        route.last_retry_at = datetime.utcnow()
        await self.db.flush()
        
        # 执行重试
        try:
            result = await self.create_route(new_route_data)
            
            # 更新原路由状态
            route.route_status = result.route_status
            route.response_data = result.response_data
            route.platform_order_id = result.platform_order_id
            route.error_message = result.error_message
            route.completed_at = result.completed_at
            
            await self.db.commit()
            
            return RetryRouteResponse(
                success=result.route_status == "success",
                route_id=str(route.id),
                retry_count=route.retry_count,
                route_status=route.route_status,
                message="重试成功" if result.route_status == "success" else "重试失败"
            )
            
        except Exception as e:
            route.error_message = str(e)
            await self.db.commit()
            
            return RetryRouteResponse(
                success=False,
                route_id=str(route.id),
                retry_count=route.retry_count,
                route_status=route.route_status,
                message=f"重试失败: {str(e)}"
            )