"""
支付服务层
"""
from typing import Optional, List, Dict, Any
from uuid import UUID, uuid4
from datetime import datetime, timedelta
from decimal import Decimal
import secrets
import hashlib
import json

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, and_, or_, func
from sqlalchemy.orm import selectinload

from app.models.order import Payment, PaymentRefund, Order
from app.schemas.payment import (
    PaymentCreateSchema,
    PaymentUpdateSchema,
    PaymentResponseSchema,
    PaymentConfirmSchema,
    RefundCreateSchema,
    RefundApprovalSchema,
)
from app.config import settings, PAYMENT_METHODS


import logging
logger = logging.getLogger(__name__)
from wechatpayv3 import WeChatPay, WeChatPayType

class PaymentService:
    """支付服务核心类"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_payment(
        self,
        order_id: UUID,
        payment_method: str,
        amount: Decimal
    ) -> Payment:
        """创建支付单"""
        # 验证订单是否存在
        order_query = select(Order).where(Order.id == order_id)
        result = await self.db.execute(order_query)
        order = result.scalar_one_or_none()
        
        if not order:
            raise ValueError(f"订单不存在: {order_id}")
        
        # 检查订单是否已有支付记录
        existing_payment = await self.get_payment_by_order(order_id)
        if existing_payment and existing_payment.payment_status in ['success', 'processing']:
            raise ValueError("订单已有支付记录或正在支付中")
        
        # 验证支付方式
        if payment_method not in PAYMENT_METHODS:
            raise ValueError(f"不支持的支付方式: {payment_method}")
        
        if not PAYMENT_METHODS[payment_method]["is_active"]:
            raise ValueError(f"支付方式未启用: {payment_method}")
        
        # 验证金额范围
        method_config = PAYMENT_METHODS[payment_method]
        if amount < method_config["min_amount"] or amount > method_config["max_amount"]:
            raise ValueError(
                f"支付金额必须在 {method_config['min_amount']} - {method_config['max_amount']} 之间"
            )
        
        # 创建支付记录
        payment = Payment(
            id=uuid4(),
            order_id=order_id,
            payment_method=payment_method,
            payment_status='pending',
            amount=amount,
            created_at=datetime.utcnow()
        )
        
        self.db.add(payment)
        await self.db.commit()
        await self.db.refresh(payment)
        
        return payment
    
    async def get_payment(self, payment_id: UUID) -> Optional[Payment]:
        """获取支付记录"""
        query = select(Payment).where(Payment.id == payment_id)
        result = await self.db.execute(query)
        return result.scalar_one_or_none()
    
    async def get_payment_by_order(self, order_id: UUID) -> Optional[Payment]:
        """根据订单ID获取支付记录"""
        query = select(Payment).where(Payment.order_id == order_id)
        result = await self.db.execute(query)
        return result.scalar_one_or_none()
    
    async def list_payments(
        self,
        user_id: Optional[UUID] = None,
        order_id: Optional[UUID] = None,
        payment_status: Optional[str] = None,
        payment_method: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        page: int = 1,
        limit: int = 20
    ) -> tuple[List[Payment], int]:
        """查询支付记录列表"""
        query = select(Payment)
        count_query = select(func.count(Payment.id))
        
        # 构建查询条件
        conditions = []
        
        if order_id:
            conditions.append(Payment.order_id == order_id)
        
        if payment_status:
            conditions.append(Payment.payment_status == payment_status)
        
        if payment_method:
            conditions.append(Payment.payment_method == payment_method)
        
        if start_date:
            conditions.append(Payment.created_at >= start_date)
        
        if end_date:
            conditions.append(Payment.created_at <= end_date)
        
        # 如果指定了用户ID，需要关联订单表
        if user_id:
            query = query.join(Order, Payment.order_id == Order.id)
            count_query = count_query.join(Order, Payment.order_id == Order.id)
            conditions.append(Order.user_id == user_id)
        
        if conditions:
            query = query.where(and_(*conditions))
            count_query = count_query.where(and_(*conditions))
        
        # 执行计数查询
        total_result = await self.db.execute(count_query)
        total = total_result.scalar()
        
        # 分页
        offset = (page - 1) * limit
        query = query.order_by(Payment.created_at.desc()).offset(offset).limit(limit)
        
        # 执行查询
        result = await self.db.execute(query)
        payments = result.scalars().all()
        
        return payments, total
    
    async def update_payment_status(
        self,
        payment_id: UUID,
        status: str,
        transaction_no: Optional[str] = None,
        callback_data: Optional[Dict[str, Any]] = None
    ) -> Payment:
        """更新支付状态"""
        payment = await self.get_payment(payment_id)
        if not payment:
            raise ValueError(f"支付记录不存在: {payment_id}")
        
        # 更新支付状态
        payment.payment_status = status
        
        if transaction_no:
            payment.transaction_no = transaction_no
        
        if status == 'success':
            payment.paid_at = datetime.utcnow()
            # 同时更新订单状态
            await self._update_order_status(payment.order_id, 'paid')
        elif status == 'failed':
            # 记录失败时间（虽然数据库表中没有此字段，但可以用于日志）
            pass
        elif status in ['refunded', 'partial_refunded']:
            payment.refunded_at = datetime.utcnow()
        
        await self.db.commit()
        await self.db.refresh(payment)
        
        return payment
    
    async def confirm_offline_payment(
        self,
        payment_id: UUID,
        transaction_no: str,
        paid_at: Optional[datetime] = None
    ) -> Payment:
        """确认线下支付"""
        payment = await self.get_payment(payment_id)
        if not payment:
            raise ValueError(f"支付记录不存在: {payment_id}")
        
        if payment.payment_method not in ['offline', 'bank_transfer']:
            raise ValueError("只能确认线下支付或银行转账")
        
        if payment.payment_status == 'success':
            raise ValueError("支付已完成，无需重复确认")
        
        payment.payment_status = 'success'
        payment.transaction_no = transaction_no
        payment.paid_at = paid_at or datetime.utcnow()
        
        # 更新订单状态
        await self._update_order_status(payment.order_id, 'paid')
        
        await self.db.commit()
        await self.db.refresh(payment)
        
        return payment
    
    async def cancel_payment(
        self,
        payment_id: UUID,
        reason: str
    ) -> Payment:
        """取消支付"""
        payment = await self.get_payment(payment_id)
        if not payment:
            raise ValueError(f"支付记录不存在: {payment_id}")
        
        if payment.payment_status in ['success', 'refunded', 'partial_refunded']:
            raise ValueError("已完成或已退款的支付不能取消")
        
        payment.payment_status = 'failed'
        
        # 更新订单状态
        await self._update_order_status(payment.order_id, 'cancelled')
        
        await self.db.commit()
        await self.db.refresh(payment)
        
        return payment
    
    async def process_wechat_payment(self, payment: Payment) -> dict:
        """处理微信支付（模拟）"""
        # 这里应该调用微信支付API
        # 由于没有真实的商户号，这里返回模拟数据
        return {
            "prepay_id": f"wx_prepay_{payment.id}",
            "nonce_str": secrets.token_hex(16),
            "timestamp": str(int(datetime.utcnow().timestamp())),
            "sign": "mock_signature",
            "package": "prepay_id=wx_prepay_" + str(payment.id)
        }
    
    async def process_alipay_payment(self, payment: Payment) -> dict:
        """处理支付宝支付（模拟）"""
        # 这里应该调用支付宝API
        # 返回模拟数据
        return {
            "order_string": f"alipay_order_{payment.id}",
            "qr_code": f"https://qr.alipay.com/{payment.id}",
            "form_html": "<form>...</form>"
        }
    
    async def handle_payment_callback(
        self,
        callback_data: dict,
        channel: str
    ) -> Payment:
        """处理支付回调"""
        # 根据回调数据查找支付记录
        payment_id = callback_data.get('payment_id')
        transaction_no = callback_data.get('transaction_no')
        
        if not payment_id:
            raise ValueError("回调数据缺少payment_id")
        
        payment = await self.get_payment(UUID(payment_id))
        if not payment:
            raise ValueError(f"支付记录不存在: {payment_id}")
        
        # 验证签名（实际环境中需要验证）
        # if not self.verify_payment_signature(callback_data, channel):
        #     raise ValueError("签名验证失败")
        
        # 更新支付状态
        status = callback_data.get('status', 'success')
        await self.update_payment_status(
            payment.id,
            status,
            transaction_no,
            callback_data
        )
        
        return payment
    
    async def verify_payment_signature(
        self,
        data: dict,
        signature: str,
        channel: str
    ) -> bool:
        """验证支付签名（模拟）"""
        # 实际环境中需要根据不同支付渠道验证签名
        # 这里简单返回True
        return True
    
    async def query_payment_status(self, payment_id: UUID) -> dict:
        """查询第三方支付状态（模拟）"""
        payment = await self.get_payment(payment_id)
        if not payment:
            raise ValueError(f"支付记录不存在: {payment_id}")
        
        # 模拟查询第三方支付状态
        return {
            "payment_id": str(payment.id),
            "status": payment.payment_status,
            "amount": float(payment.amount),
            "transaction_no": payment.transaction_no,
            "paid_at": payment.paid_at.isoformat() if payment.paid_at else None
        }
    
    async def get_payment_statistics(
        self,
        user_id: Optional[UUID] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> dict:
        """获取支付统计信息"""
        # 构建基础查询
        base_query = select(Payment)
        
        if user_id:
            base_query = base_query.join(Order, Payment.order_id == Order.id).where(
                Order.user_id == user_id
            )
        
        if start_date:
            base_query = base_query.where(Payment.created_at >= start_date)
        
        if end_date:
            base_query = base_query.where(Payment.created_at <= end_date)
        
        # 执行查询
        result = await self.db.execute(base_query)
        payments = result.scalars().all()
        
        # 统计数据
        total_amount = sum(p.amount for p in payments)
        total_count = len(payments)
        success_count = sum(1 for p in payments if p.payment_status == 'success')
        failed_count = sum(1 for p in payments if p.payment_status == 'failed')
        refund_count = sum(1 for p in payments if 'refunded' in p.payment_status)
        refund_amount = sum(
            p.amount for p in payments 
            if p.payment_status == 'refunded'
        )
        
        return {
            "total_amount": total_amount,
            "total_count": total_count,
            "success_count": success_count,
            "failed_count": failed_count,
            "refund_count": refund_count,
            "refund_amount": refund_amount
        }
    
    async def _update_order_status(self, order_id: UUID, status: str):
        """更新订单状态（内部方法）"""
        query = update(Order).where(Order.id == order_id).values(
            order_status=status
        )
        await self.db.execute(query)

        # 如果订单支付成功且是订阅订单，自动创建订阅
        if status == 'paid':
            await self._handle_paid_order(order_id)

    async def _handle_paid_order(self, order_id: UUID):
        """处理已支付订单的后续业务逻辑"""
        from app.models.order import Order, SubscriptionOrder
        from app.services.subscription_service import SubscriptionService

        # 获取订单信息
        order_result = await self.db.execute(
            select(Order).where(Order.id == order_id)
        )
        order = order_result.scalar_one_or_none()

        if not order:
            return

        # 如果是订阅订单，创建订阅
        if order.order_type == 'subscription':
            # 获取订阅订单详情
            sub_order_result = await self.db.execute(
                select(SubscriptionOrder).where(SubscriptionOrder.order_id == order_id)
            )
            sub_order = sub_order_result.scalar_one_or_none()

            if sub_order:
                # 创建订阅服务实例
                subscription_service = SubscriptionService(self.db)

                # 创建订阅记录
                await subscription_service.create_subscription_from_order(
                    user_id=order.user_id,
                    plan_id=sub_order.plan_id,
                    billing_cycle=sub_order.billing_cycle,
                    start_date=sub_order.start_date,
                    end_date=sub_order.end_date
                )


class RefundService:
    """退款服务类"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.payment_service = PaymentService(db)
    
    async def create_refund_request(
        self,
        payment_id: Optional[UUID] = None,
        order_id: Optional[UUID] = None,
        amount: Decimal = None,
        reason: str = None
    ) -> PaymentRefund:
        """创建退款申请"""
        # 获取支付记录
        if payment_id:
            payment = await self.payment_service.get_payment(payment_id)
        elif order_id:
            payment = await self.payment_service.get_payment_by_order(order_id)
        else:
            raise ValueError("必须提供payment_id或order_id")
        
        if not payment:
            raise ValueError("支付记录不存在")
        
        if payment.payment_status != 'success':
            raise ValueError("只有支付成功的订单才能申请退款")
        
        # 检查是否已有退款记录
        existing_refund = await self.get_refund_by_payment(payment.id)
        if existing_refund and existing_refund.refund_status not in ['failed', 'cancelled']:
            raise ValueError("已有退款申请在处理中")
        
        # 验证退款金额
        if amount > payment.amount:
            raise ValueError("退款金额不能超过支付金额")
        
        # 生成退款单号
        refund_no = self._generate_refund_no()
        
        # 创建退款记录
        refund = PaymentRefund(
            id=uuid4(),
            payment_id=payment.id,
            refund_no=refund_no,
            refund_amount=amount or payment.amount,
            refund_reason=reason,
            refund_status='pending',
            created_at=datetime.utcnow()
        )
        
        self.db.add(refund)
        await self.db.commit()
        await self.db.refresh(refund)
        
        return refund
    
    async def get_refund(self, refund_id: UUID) -> Optional[PaymentRefund]:
        """获取退款记录"""
        query = select(PaymentRefund).where(PaymentRefund.id == refund_id)
        result = await self.db.execute(query)
        return result.scalar_one_or_none()
    
    async def get_refund_by_payment(self, payment_id: UUID) -> Optional[PaymentRefund]:
        """根据支付ID获取退款记录"""
        query = select(PaymentRefund).where(
            PaymentRefund.payment_id == payment_id
        ).order_by(PaymentRefund.created_at.desc())
        result = await self.db.execute(query)
        return result.scalar_one_or_none()
    
    async def list_refunds(
        self,
        user_id: Optional[UUID] = None,
        order_id: Optional[UUID] = None,
        refund_status: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        page: int = 1,
        limit: int = 20
    ) -> tuple[List[PaymentRefund], int]:
        """查询退款记录列表"""
        query = select(PaymentRefund)
        count_query = select(func.count(PaymentRefund.id))
        
        # 构建查询条件
        conditions = []
        
        if refund_status:
            conditions.append(PaymentRefund.refund_status == refund_status)
        
        if start_date:
            conditions.append(PaymentRefund.created_at >= start_date)
        
        if end_date:
            conditions.append(PaymentRefund.created_at <= end_date)
        
        # 如果指定了订单ID或用户ID，需要关联支付表和订单表
        if order_id or user_id:
            query = query.join(Payment, PaymentRefund.payment_id == Payment.id)
            count_query = count_query.join(Payment, PaymentRefund.payment_id == Payment.id)
            
            if order_id:
                conditions.append(Payment.order_id == order_id)
            
            if user_id:
                query = query.join(Order, Payment.order_id == Order.id)
                count_query = count_query.join(Order, Payment.order_id == Order.id)
                conditions.append(Order.user_id == user_id)
        
        if conditions:
            query = query.where(and_(*conditions))
            count_query = count_query.where(and_(*conditions))
        
        # 执行计数查询
        total_result = await self.db.execute(count_query)
        total = total_result.scalar()
        
        # 分页
        offset = (page - 1) * limit
        query = query.order_by(PaymentRefund.created_at.desc()).offset(offset).limit(limit)
        
        # 执行查询
        result = await self.db.execute(query)
        refunds = result.scalars().all()
        
        return refunds, total
    
    async def approve_refund(
        self,
        refund_id: UUID,
        approver_id: UUID,
        note: Optional[str] = None
    ) -> PaymentRefund:
        """批准退款"""
        refund = await self.get_refund(refund_id)
        if not refund:
            raise ValueError(f"退款记录不存在: {refund_id}")
        
        if refund.refund_status != 'pending':
            raise ValueError("只能批准待审核的退款申请")
        
        refund.refund_status = 'approved'
        refund.operator_id = approver_id
        refund.approved_at = datetime.utcnow()
        
        await self.db.commit()
        await self.db.refresh(refund)
        
        return refund
    
    async def reject_refund(
        self,
        refund_id: UUID,
        approver_id: UUID,
        reason: str
    ) -> PaymentRefund:
        """拒绝退款"""
        refund = await self.get_refund(refund_id)
        if not refund:
            raise ValueError(f"退款记录不存在: {refund_id}")
        
        if refund.refund_status != 'pending':
            raise ValueError("只能拒绝待审核的退款申请")
        
        refund.refund_status = 'failed'
        refund.operator_id = approver_id
        refund.refund_reason = f"拒绝原因: {reason}"
        
        await self.db.commit()
        await self.db.refresh(refund)
        
        return refund
    
    async def process_refund(
        self,
        refund_id: UUID
    ) -> PaymentRefund:
        """处理退款（调用第三方）"""
        refund = await self.get_refund(refund_id)
        if not refund:
            raise ValueError(f"退款记录不存在: {refund_id}")
        
        if refund.refund_status != 'approved':
            raise ValueError("只能处理已批准的退款")
        
        # 获取支付记录
        payment = await self.payment_service.get_payment(refund.payment_id)
        
        # 调用第三方退款API（模拟）
        refund_result = await self._process_third_party_refund(payment, refund)
        
        if refund_result.get('success'):
            refund.refund_status = 'success'
            refund.processed_at = datetime.utcnow()
            refund.refund_method = payment.payment_method
            
            # 更新支付状态
            if refund.refund_amount == payment.amount:
                await self.payment_service.update_payment_status(
                    payment.id, 
                    'refunded'
                )
            else:
                await self.payment_service.update_payment_status(
                    payment.id,
                    'partial_refunded'
                )
        else:
            refund.refund_status = 'failed'
            refund.refund_reason = refund_result.get('error', '退款处理失败')
        
        await self.db.commit()
        await self.db.refresh(refund)
        
        return refund
    
    async def cancel_refund(
        self,
        refund_id: UUID,
        user_id: UUID,
        reason: str
    ) -> PaymentRefund:
        """取消退款申请"""
        refund = await self.get_refund(refund_id)
        if not refund:
            raise ValueError(f"退款记录不存在: {refund_id}")
        
        if refund.refund_status not in ['pending', 'approved']:
            raise ValueError("只能取消待处理的退款申请")
        
        refund.refund_status = 'cancelled'
        refund.refund_reason = f"取消原因: {reason}"
        
        await self.db.commit()
        await self.db.refresh(refund)
        
        return refund
    
    async def handle_refund_callback(
        self,
        callback_data: dict,
        channel: str
    ) -> PaymentRefund:
        """处理退款回调"""
        refund_id = callback_data.get('refund_id')
        if not refund_id:
            raise ValueError("回调数据缺少refund_id")
        
        refund = await self.get_refund(UUID(refund_id))
        if not refund:
            raise ValueError(f"退款记录不存在: {refund_id}")
        
        # 更新退款状态
        status = callback_data.get('status')
        if status == 'success':
            refund.refund_status = 'success'
            refund.processed_at = datetime.utcnow()
        else:
            refund.refund_status = 'failed'
        
        await self.db.commit()
        await self.db.refresh(refund)
        
        return refund
    
    def _generate_refund_no(self) -> str:
        """生成退款单号"""
        timestamp = datetime.utcnow().strftime('%Y%m%d%H%M%S')
        random_str = secrets.token_hex(3).upper()
        return f"RFD{timestamp}{random_str}"
    
    async def _process_third_party_refund(
        self,
        payment: Payment,
        refund: PaymentRefund
    ) -> dict:
        """处理第三方退款（模拟）"""
        # 实际环境中需要调用第三方退款API
        # 这里返回模拟结果
        return {
            "success": True,
            "refund_id": str(refund.id),
            "transaction_no": f"refund_{refund.id}",
            "refund_time": datetime.utcnow().isoformat()
        }