from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Dict, Any, Optional, Tuple
import asyncio
import logging
import uuid
import re
from datetime import datetime

from app.repositories.knowledge_repository import KnowledgeRepository
from app.services.rag_knowledge_service import RAGKnowledgeService
from app.models.knowledge import UserKnowledgeBase, KnowledgeDocument, EmbeddingStatus
from app.config import settings
from app.schemas.knowledge import (
    KnowledgeBaseCreateRequest, KnowledgeBaseResponse,
    DocumentCreateRequest, DocumentResponse, DocumentListResponse,
    SearchRequest, SearchResult, SearchResultResponse
)
from app.exceptions import NotFoundError, ValidationError

logger = logging.getLogger(__name__)


class KnowledgeService:
    """知识库管理服务"""

    def __init__(self, db: AsyncSession):
        self.db = db
        self.knowledge_repo = KnowledgeRepository(db)

        # 使用RAG知识库服务
        self.vector_service = RAGKnowledgeService()
        self.use_rag_service = True

        self.max_chunk_size = 1000  # 最大分块大小
        self.chunk_overlap = 100    # 分块重叠大小
    
    async def create_knowledge_base(self, user_id: str, request: KnowledgeBaseCreateRequest) -> KnowledgeBaseResponse:
        """创建知识库"""
        try:
            # 生成集合名称
            collection_name = f"kb_{uuid.uuid4().hex[:16]}"

            # RAG服务会自动创建knowledge_id对应的命名空间，无需显式创建
            logger.info(f"使用RAG服务创建知识库: {collection_name}")

            # 创建知识库记录
            kb_data = {
                "user_id": user_id,
                "name": request.name,
                "description": request.description,
                "viking_collection_name": collection_name,  # 保持字段名不变，但用于存储集合名
                "document_count": 0,
                "vector_count": 0
            }

            knowledge_base = await self.knowledge_repo.create_knowledge_base(kb_data)

            return KnowledgeBaseResponse(
                id=str(knowledge_base.id),
                name=knowledge_base.name,
                description=knowledge_base.description,
                viking_collection_name=knowledge_base.viking_collection_name,
                document_count=knowledge_base.document_count,
                vector_count=knowledge_base.vector_count,
                created_at=knowledge_base.created_at,
                updated_at=knowledge_base.updated_at
            )

        except Exception as e:
            logger.error(f"创建知识库失败: {e}")
            raise ValidationError(f"创建知识库失败: {str(e)}")
    
    async def get_knowledge_base(self, kb_id: str, user_id: str) -> KnowledgeBaseResponse:
        """获取知识库详情"""
        knowledge_base = await self.knowledge_repo.get_knowledge_base_by_id(kb_id, user_id)
        if not knowledge_base:
            raise NotFoundError("知识库不存在或无权限访问")
        
        return KnowledgeBaseResponse(
            id=str(knowledge_base.id),
            name=knowledge_base.name,
            description=knowledge_base.description,
            viking_collection_name=knowledge_base.viking_collection_name,
            document_count=knowledge_base.document_count,
            vector_count=knowledge_base.vector_count,
            created_at=knowledge_base.created_at,
            updated_at=knowledge_base.updated_at
        )
    
    async def get_user_knowledge_bases(self, user_id: str, limit: int = 50, offset: int = 0) -> List[KnowledgeBaseResponse]:
        """获取用户知识库列表"""
        knowledge_bases = await self.knowledge_repo.get_user_knowledge_bases(user_id, limit, offset)

        return [
            KnowledgeBaseResponse(
                id=str(kb.id),
                name=kb.name,
                description=kb.description,
                viking_collection_name=kb.viking_collection_name,
                document_count=kb.document_count,
                vector_count=kb.vector_count,
                created_at=kb.created_at,
                updated_at=kb.updated_at
            )
            for kb in knowledge_bases
        ]

    async def get_user_knowledge_bases_count(self, user_id: str) -> int:
        """获取用户知识库总数"""
        return await self.knowledge_repo.get_user_knowledge_bases_count(user_id)
    
    async def delete_knowledge_base(self, kb_id: str, user_id: str) -> bool:
        """删除知识库"""
        # 验证权限
        knowledge_base = await self.knowledge_repo.get_knowledge_base_by_id(kb_id, user_id)
        if not knowledge_base:
            raise NotFoundError("知识库不存在或无权限访问")
        
        try:
            # ❌ VikingDB集合删除已移除 - RAG服务会自动管理集合
            # await self.viking_service.delete_user_collection(
            #     user_id, knowledge_base.viking_collection_name
            # )

            # 删除数据库记录
            return await self.knowledge_repo.delete_knowledge_base(kb_id, user_id)

        except Exception as e:
            logger.error(f"删除知识库失败: {e}")
            raise ValidationError(f"删除知识库失败: {str(e)}")
    
    async def upload_document(self, kb_id: str, user_id: str, request: DocumentCreateRequest) -> DocumentResponse:
        """上传文档到知识库"""
        # 验证知识库权限
        knowledge_base = await self.knowledge_repo.get_knowledge_base_by_id(kb_id, user_id)
        if not knowledge_base:
            raise NotFoundError("知识库不存在或无权限访问")
        
        try:
            # 创建文档记录
            doc_data = {
                "knowledge_base_id": kb_id,
                "title": request.title,
                "content": request.content,
                "file_type": request.file_type,
                "file_size": len(request.content.encode('utf-8')),
                "chunk_count": 0,
                "embedding_status": EmbeddingStatus.PENDING
            }
            
            document = await self.knowledge_repo.create_document(doc_data)
            
            # 异步处理文档（分块和向量化）
            # 使用新的数据库会话避免冲突
            asyncio.create_task(self._process_document_with_new_session(user_id, str(knowledge_base.id), str(document.id)))
            
            return DocumentResponse(
                id=str(document.id),
                knowledge_base_id=str(document.knowledge_base_id),
                title=document.title,
                file_type=document.file_type,
                file_size=document.file_size,
                chunk_count=document.chunk_count,
                embedding_status=document.embedding_status.value,
                created_at=document.created_at,
                updated_at=document.updated_at
            )
            
        except Exception as e:
            logger.error(f"上传文档失败: {e}")
            raise ValidationError(f"上传文档失败: {str(e)}")

    async def _process_document_with_new_session(self, user_id: str, knowledge_base_id: str, document_id: str):
        """使用新的数据库会话处理文档向量化"""
        from app.database import get_db_session

        async with get_db_session() as db:
            try:
                # 获取知识库和文档信息
                knowledge_repo = KnowledgeRepository(db)
                knowledge_base = await knowledge_repo.get_knowledge_base_by_id(knowledge_base_id, user_id)
                document = await knowledge_repo.get_document_by_id(document_id)

                if not knowledge_base or not document:
                    logger.error(f"知识库或文档不存在: kb={knowledge_base_id}, doc={document_id}")
                    return

                # 处理文档
                await self._process_document_async(user_id, knowledge_base, document, db)

            except Exception as e:
                logger.error(f"异步处理文档失败: {e}")

    async def _process_document_async(self, user_id: str, knowledge_base: UserKnowledgeBase, document: KnowledgeDocument, db: AsyncSession = None):
        """异步处理文档（分块和向量化）"""
        # 使用传入的数据库会话或默认会话
        knowledge_repo = KnowledgeRepository(db) if db else self.knowledge_repo

        try:
            # 更新状态为处理中
            await knowledge_repo.update_document(str(document.id), {
                "embedding_status": EmbeddingStatus.PROCESSING
            })

            # 使用RAG服务处理文档
            await self._process_document_with_rag(user_id, knowledge_base, document, knowledge_repo)

            logger.info(f"文档 {document.id} 处理完成")

        except Exception as e:
            logger.error(f"文档处理失败: {e}")

            # 更新状态为失败
            await knowledge_repo.update_document(str(document.id), {
                "embedding_status": EmbeddingStatus.FAILED,
                "error_message": str(e)
            })

    async def _process_document_with_rag(self, user_id: str, knowledge_base: UserKnowledgeBase,
                                       document: KnowledgeDocument, knowledge_repo: KnowledgeRepository):
        """使用RAG服务处理文档"""
        try:
            # 直接调用RAG服务摄取文档
            result = await self.vector_service.ingest_document(
                user_id=user_id,
                collection_name=knowledge_base.viking_collection_name,
                text=document.content,
                max_chars=self.max_chunk_size,
                overlap=self.chunk_overlap
            )

            chunks_count = result.get('chunks_count', 0)
            logger.info(f"RAG服务处理文档成功: 文档ID={document.id}, 分块数={chunks_count}")

            # 更新文档状态
            await knowledge_repo.update_document(str(document.id), {
                "chunk_count": chunks_count,
                "embedding_status": EmbeddingStatus.COMPLETED
            })

            # 更新知识库统计
            await knowledge_repo.update_knowledge_base(str(knowledge_base.id), {
                "vector_count": knowledge_base.vector_count + chunks_count
            })

        except Exception as e:
            logger.error(f"RAG服务处理文档失败: {e}")
            raise


    
    def _split_document(self, content: str) -> List[str]:
        """智能文档分块"""
        chunks = []
        
        # 按段落分割
        paragraphs = content.split('\n\n')
        
        current_chunk = ""
        for paragraph in paragraphs:
            paragraph = paragraph.strip()
            if not paragraph:
                continue
            
            # 如果当前分块加上新段落超过最大长度
            if len(current_chunk) + len(paragraph) > self.max_chunk_size:
                if current_chunk:
                    chunks.append(current_chunk.strip())
                    current_chunk = ""
                
                # 如果单个段落就超过最大长度，需要进一步分割
                if len(paragraph) > self.max_chunk_size:
                    sub_chunks = self._split_long_paragraph(paragraph)
                    chunks.extend(sub_chunks)
                else:
                    current_chunk = paragraph
            else:
                if current_chunk:
                    current_chunk += "\n\n" + paragraph
                else:
                    current_chunk = paragraph
        
        # 添加最后一个分块
        if current_chunk:
            chunks.append(current_chunk.strip())
        
        return chunks
    
    def _split_long_paragraph(self, paragraph: str) -> List[str]:
        """分割长段落"""
        chunks = []
        
        # 按句子分割
        sentences = re.split(r'[。！？.!?]', paragraph)
        
        current_chunk = ""
        for sentence in sentences:
            sentence = sentence.strip()
            if not sentence:
                continue
            
            if len(current_chunk) + len(sentence) > self.max_chunk_size:
                if current_chunk:
                    chunks.append(current_chunk.strip())
                    current_chunk = sentence
                else:
                    # 如果单个句子就超过最大长度，强制分割
                    if len(sentence) > self.max_chunk_size:
                        for i in range(0, len(sentence), self.max_chunk_size):
                            chunks.append(sentence[i:i + self.max_chunk_size])
                    else:
                        current_chunk = sentence
            else:
                if current_chunk:
                    current_chunk += "。" + sentence
                else:
                    current_chunk = sentence
        
        if current_chunk:
            chunks.append(current_chunk.strip())
        
        return chunks
    
    async def search_knowledge(self, user_id: str, knowledge_base_ids: List[str],
                             query: str, keywords: List[str] = None, limit: int = 5) -> List[Dict[str, Any]]:
        """搜索知识库内容"""
        try:
            return await self._search_with_rag(user_id, knowledge_base_ids, query, limit)

        except Exception as e:
            logger.error(f"知识库搜索失败: {e}")
            return []

    async def _search_with_rag(self, user_id: str, knowledge_base_ids: List[str],
                              query: str, limit: int) -> List[Dict[str, Any]]:
        """使用RAG服务搜索"""
        all_results = []

        for kb_id in knowledge_base_ids:
            # 验证权限
            knowledge_base = await self.knowledge_repo.get_knowledge_base_by_id(kb_id, user_id)
            if not knowledge_base:
                continue

            try:
                # 使用RAG服务搜索
                search_results = await self.vector_service.search_knowledge(
                    user_id=user_id,
                    collection_name=knowledge_base.viking_collection_name,
                    query=query,
                    top_k=limit
                )

                # 为每个结果添加知识库信息
                for result in search_results:
                    result["knowledge_base_id"] = kb_id
                    result["title"] = result.get("metadata", {}).get("title", "")
                    result["document_id"] = result.get("metadata", {}).get("document_id", "")

                all_results.extend(search_results)

            except Exception as e:
                logger.error(f"RAG服务搜索知识库 {kb_id} 失败: {e}")
                continue

        # 按相似度排序并限制结果数量
        all_results.sort(key=lambda x: x.get("similarity_score", 0), reverse=True)
        return all_results[:limit]



    async def get_documents(self, kb_id: str, page: int = 1, size: int = 20):
        """获取知识库的文档列表"""
        try:
            # 获取文档列表
            documents = await self.knowledge_repo.get_documents_by_kb_id(kb_id, page, size)

            # 转换为响应格式
            document_responses = []
            for doc in documents:
                document_responses.append(DocumentResponse(
                    id=str(doc.id),
                    knowledge_base_id=str(doc.knowledge_base_id),
                    title=doc.title,
                    file_path=doc.file_path,
                    file_type=doc.file_type,
                    file_size=doc.file_size,
                    chunk_count=doc.chunk_count,
                    embedding_status=doc.embedding_status.value if doc.embedding_status else "pending",
                    error_message=doc.error_message,
                    created_at=doc.created_at,
                    updated_at=doc.updated_at
                ))

            # 获取总数
            total = await self.knowledge_repo.get_documents_count_by_kb_id(kb_id)

            return DocumentListResponse(
                items=document_responses,
                pagination={
                    "page": page,
                    "size": size,
                    "total": total,
                    "pages": (total + size - 1) // size
                }
            )

        except Exception as e:
            logger.error(f"获取文档列表失败: {e}")
            raise ValidationError(f"获取文档列表失败: {str(e)}")

    async def get_document_by_id(self, doc_id: str):
        """根据ID获取文档"""
        try:
            document = await self.knowledge_repo.get_document_by_id(doc_id)
            return document
        except Exception as e:
            logger.error(f"获取文档失败: {e}")
            raise ValidationError(f"获取文档失败: {str(e)}")
