from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func, desc, asc
from app.models.ai_service import AIRequest, AIQuota, AITemplate, AIUsageStatistics, AIServiceType, AIRequestStatus, AIModelType
from app.models.company import Company
from app.schemas.ai_service import *
from app.exceptions import ValidationError, NotFoundError, PermissionError
from app.config import settings
from typing import Optional, Dict, Any, List, AsyncGenerator
import asyncio
import httpx
import json
import time
from datetime import datetime, timedelta
from decimal import Decimal
import logging

logger = logging.getLogger(__name__)

class QuotaExceededError(Exception):
    """配额超限异常"""
    pass

class AIService:
    def __init__(self, db: AsyncSession):
        self.db = db
        self.ai_clients = self._init_ai_clients()
    
    async def generate_content(self, user_id: str, request: ContentGenerationRequest) -> ContentGenerationResponse:
        """AI内容生成"""
        # 检查配额
        await self._check_quota(user_id, AIServiceType.CONTENT_GENERATION)
        
        # 获取企业信息
        company_id = None
        company_result = await self.db.execute(
            select(Company).where(Company.user_id == user_id)
        )
        company = company_result.scalar_one_or_none()
        if company:
            company_id = company.id
        
        # 创建AI请求记录
        ai_request = await self._create_ai_request(
            user_id=user_id,
            company_id=company_id,
            service_type=AIServiceType.CONTENT_GENERATION,
            ai_model=AIModelType(request.ai_model),
            request_title=f"生成{request.content_type}内容",
            request_prompt=self._build_content_generation_prompt(request),
            request_parameters=request.dict()
        )
        
        try:
            # 调用AI服务
            start_time = time.time()
            ai_response = await self._call_ai_service(
                model=request.ai_model,
                prompt=ai_request.request_prompt,
                parameters=request.dict()
            )
            processing_time = int((time.time() - start_time) * 1000)
            
            # 解析响应
            generated_content = ai_response.get('content', '')
            content_metadata = self._analyze_content_metadata(generated_content)
            suggestions = ai_response.get('suggestions', [])
            quality_score = self._calculate_quality_score(generated_content, request.keywords)
            
            # 更新请求记录
            ai_request.status = AIRequestStatus.COMPLETED
            ai_request.response_content = generated_content
            ai_request.response_data = {
                'content_metadata': content_metadata,
                'suggestions': suggestions
            }
            ai_request.quality_score = quality_score
            ai_request.tokens_used = ai_response.get('tokens_used', 0)
            ai_request.processing_time = processing_time
            ai_request.cost_amount = self._calculate_cost(request.ai_model, ai_response.get('tokens_used', 0))
            ai_request.completed_at = datetime.utcnow()
            
            await self.db.commit()
            
            # 更新配额
            await self._update_quota(user_id, AIServiceType.CONTENT_GENERATION, 1)
            
            # 更新使用统计
            await self._update_usage_statistics(user_id, ai_request)
            
            return ContentGenerationResponse(
                request_id=str(ai_request.id),
                generated_content=generated_content,
                content_metadata=content_metadata,
                suggestions=suggestions,
                quality_score=quality_score,
                tokens_used=ai_request.tokens_used,
                processing_time=processing_time
            )

        except Exception as e:
            # 更新失败状态
            ai_request.status = AIRequestStatus.FAILED
            ai_request.response_content = f"生成失败: {str(e)}"
            await self.db.commit()
            raise

    async def generate_content_stream(self, user_id: str, request: ContentGenerationRequest) -> AsyncGenerator[Dict[str, Any], None]:
        """AI内容生成（流式）"""
        logger.info(f"开始生成内容流 - 用户: {user_id}, 主题: {request.topic}")

        # 检查配额
        await self._check_quota(user_id, AIServiceType.CONTENT_GENERATION)
        logger.info("配额检查通过")

        # 获取企业信息
        company_id = None
        company_result = await self.db.execute(
            select(Company).where(Company.user_id == user_id)
        )
        company = company_result.scalar_one_or_none()
        if company:
            company_id = company.id
        logger.info(f"企业信息获取完成 - 企业ID: {company_id}")

        # 创建AI请求记录（不立即提交事务）
        ai_request = await self._create_ai_request_without_commit(
            user_id=user_id,
            company_id=company_id,
            service_type=AIServiceType.CONTENT_GENERATION,
            ai_model=AIModelType(request.ai_model),
            request_title=f"生成{request.content_type}内容",
            request_prompt=self._build_content_generation_prompt(request),
            request_parameters=request.dict()
        )
        logger.info(f"AI请求记录创建成功 - ID: {ai_request.id}")

        try:
            # 发送进度更新
            logger.info(f"准备发送初始进度消息 - 请求ID: {ai_request.id}")

            # 发送初始进度
            progress_data = {
                'type': 'progress',
                'message': '正在连接AI服务...',
                'progress': 10,
                'request_id': str(ai_request.id)
            }
            logger.info(f"准备yield进度数据: {progress_data}")

            try:
                yield progress_data
                logger.info(f"初始进度消息已发送")
            except Exception as yield_error:
                logger.error(f"yield进度数据时异常: {yield_error}")
                logger.error(f"异常类型: {type(yield_error)}")
                import traceback
                logger.error(f"异常堆栈: {traceback.format_exc()}")
                raise

            # 流式调用AI服务
            start_time = time.time()
            generated_content = ""
            logger.info(f"开始调用AI服务流 - 模型: {request.ai_model}")

            # 发送连接成功进度
            yield {
                'type': 'progress',
                'message': '已连接AI服务，开始生成内容...',
                'progress': 20,
                'request_id': str(ai_request.id)
            }

            has_content = False
            logger.info(f"🚀 准备调用_call_ai_service_stream - 模型: {request.ai_model}")
            logger.info(f"提示词: {ai_request.request_prompt[:100]}...")

            async for chunk in self._call_ai_service_stream(
                model=request.ai_model,
                prompt=ai_request.request_prompt,
                parameters=request.dict()
            ):
                logger.debug(f"收到AI服务流数据块: {chunk}")
                if chunk.get('type') == 'content':
                    has_content = True
                    content_piece = chunk.get('content', '')
                    generated_content += content_piece

                    # 实时发送内容片段
                    yield {
                        'type': 'content',
                        'content': content_piece,
                        'full_content': generated_content,
                        'request_id': str(ai_request.id)
                    }
                elif chunk.get('type') == 'progress':
                    yield chunk
                elif chunk.get('type') == 'error':
                    # 如果AI服务返回错误，直接抛出异常
                    raise Exception(chunk.get('message', 'AI服务调用失败'))

            # 检查是否有实际内容生成
            if not has_content or not generated_content.strip():
                raise Exception("豆包AI未返回任何内容，请检查API配置")

            processing_time = int((time.time() - start_time) * 1000)

            # 分析内容
            content_metadata = self._analyze_content_metadata(generated_content)
            quality_score = self._calculate_quality_score(generated_content, request.keywords)

            # 更新请求记录
            ai_request.status = AIRequestStatus.COMPLETED
            ai_request.response_content = generated_content
            ai_request.response_data = {
                'content_metadata': content_metadata,
                'suggestions': ['内容已生成完成']
            }
            ai_request.quality_score = quality_score
            ai_request.tokens_used = len(generated_content) // 4  # 简单估算
            ai_request.processing_time = processing_time
            ai_request.cost_amount = self._calculate_cost(request.ai_model, ai_request.tokens_used)
            ai_request.completed_at = datetime.utcnow()

            await self.db.commit()

            # 更新配额
            await self._update_quota(user_id, AIServiceType.CONTENT_GENERATION, 1)

            # 只有在真正生成内容时才发送最终结果
            yield {
                'type': 'final',
                'request_id': str(ai_request.id),
                'generated_content': generated_content,
                'content_metadata': content_metadata,
                'quality_score': float(quality_score) if quality_score else None,
                'tokens_used': ai_request.tokens_used,
                'processing_time': processing_time
            }

        except Exception as e:
            logger.error(f"生成内容流失败: {e}")
            import traceback
            logger.error(f"错误堆栈: {traceback.format_exc()}")

            ai_request.status = AIRequestStatus.FAILED
            ai_request.error_message = str(e)
            await self.db.commit()

            yield {
                'type': 'error',
                'message': str(e),
                'request_id': str(ai_request.id)
            }
            # 不要返回假的final结果，直接结束

    async def generate_content_stream_v2(self, user_id: str, request: ContentGenerationRequest) -> AsyncGenerator[Dict[str, Any], None]:
        """AI内容生成V2 - 支持对话上下文和知识库"""
        logger.info(f"🎯 进入generate_content_stream_v2 - 用户: {user_id}")

        # 检查配额
        logger.info("🔍 开始检查配额")
        await self._check_quota(user_id, AIServiceType.CONTENT_GENERATION)
        logger.info("✅ 配额检查通过")

        # 获取企业信息
        logger.info("🏢 开始获取企业信息")
        company_id = None
        company_result = await self.db.execute(
            select(Company).where(Company.user_id == user_id)
        )
        company = company_result.scalar_one_or_none()
        if company:
            company_id = company.id
        logger.info(f"🏢 企业信息获取完成 - 企业ID: {company_id}")

        # 处理对话上下文
        conversation = None
        if request.conversation_id:
            from app.repositories.conversation_repository import ConversationRepository
            conversation_repo = ConversationRepository(self.db)
            conversation = await conversation_repo.get_conversation_by_id(request.conversation_id, user_id)

        # 获取模板（仅首次请求需要）
        template = None
        if request.is_first_request and request.template_id:
            template = await self._get_template(request.template_id)

        # 构建增强提示词
        prompt = await self._build_enhanced_prompt(request, template, conversation, user_id)

        # 创建AI请求记录
        logger.info("📝 开始创建AI请求记录")
        ai_request = await self._create_ai_request(
            user_id=user_id,
            company_id=company_id,
            service_type=AIServiceType.CONTENT_GENERATION,
            ai_model=AIModelType(request.ai_model),
            request_title=f"生成{request.content_type}内容",
            request_prompt=prompt,
            request_parameters=request.dict(),
            conversation_id=conversation.id if conversation else None
        )
        logger.info(f"📝 AI请求记录创建完成 - ID: {ai_request.id}")

        try:
            # 发送进度更新
            logger.info("📤 准备发送第一个进度更新")
            yield {
                'type': 'progress',
                'message': '正在连接AI服务...',
                'progress': 10,
                'request_id': str(ai_request.id),
                'conversation_id': str(conversation.id) if conversation else None
            }
            logger.info("📤 第一个进度更新已发送")

            # 流式调用AI服务
            start_time = time.time()
            generated_content = ""
            has_content = False

            logger.info(f"🔥 准备调用_call_ai_service_stream - 模型: {request.ai_model}")
            logger.info(f"提示词: {prompt[:100]}...")

            async for chunk in self._call_ai_service_stream(
                model=request.ai_model,
                prompt=prompt,
                parameters=request.dict()
            ):
                if chunk.get('type') == 'content':
                    has_content = True
                    generated_content += chunk.get('content', '')
                    yield {
                        'type': 'content',
                        'content': chunk.get('content', ''),
                        'full_content': generated_content,
                        'request_id': str(ai_request.id),
                        'conversation_id': str(conversation.id) if conversation else None
                    }
                elif chunk.get('type') == 'progress':
                    chunk['conversation_id'] = str(conversation.id) if conversation else None
                    yield chunk
                elif chunk.get('type') == 'error':
                    raise Exception(chunk.get('message', 'AI服务调用失败'))

            # 检查是否有实际内容生成
            if not has_content or not generated_content.strip():
                raise Exception("AI未返回任何内容，请检查API配置")

            processing_time = int((time.time() - start_time) * 1000)

            # 分析内容
            content_metadata = self._analyze_content_metadata(generated_content)
            quality_score = self._calculate_quality_score(generated_content, request.keywords)

            # 更新请求记录
            ai_request.status = AIRequestStatus.COMPLETED
            ai_request.response_content = generated_content
            ai_request.response_data = {
                'content_metadata': content_metadata,
                'suggestions': ['内容已生成完成'],
                'conversation_context': {
                    'is_first_request': request.is_first_request,
                    'template_used': str(template.id) if template else None,
                    'knowledge_bases_used': request.knowledge_bases
                }
            }
            ai_request.quality_score = quality_score
            ai_request.tokens_used = len(generated_content) // 4
            ai_request.processing_time = processing_time
            ai_request.cost_amount = self._calculate_cost(request.ai_model, ai_request.tokens_used)
            ai_request.completed_at = datetime.utcnow()

            await self.db.commit()

            # 更新配额
            await self._update_quota(user_id, AIServiceType.CONTENT_GENERATION, 1)

            # 发送最终结果
            yield {
                'type': 'final',
                'request_id': str(ai_request.id),
                'conversation_id': str(conversation.id) if conversation else None,
                'generated_content': generated_content,
                'content_metadata': content_metadata,
                'quality_score': float(quality_score) if quality_score else None,
                'tokens_used': ai_request.tokens_used,
                'processing_time': processing_time
            }

        except Exception as e:
            ai_request.status = AIRequestStatus.FAILED
            ai_request.error_message = str(e)
            await self.db.commit()

            yield {
                'type': 'error',
                'message': str(e),
                'request_id': str(ai_request.id),
                'conversation_id': str(conversation.id) if conversation else None
            }

    async def optimize_content(self, user_id: str, request: ContentOptimizationRequest) -> ContentOptimizationResponse:
        """内容优化建议"""
        # 检查配额
        await self._check_quota(user_id, AIServiceType.CONTENT_OPTIMIZATION)
        
        # 获取企业信息
        company_id = None
        company_result = await self.db.execute(
            select(Company).where(Company.user_id == user_id)
        )
        company = company_result.scalar_one_or_none()
        if company:
            company_id = company.id
        
        # 创建AI请求记录
        ai_request = await self._create_ai_request(
            user_id=user_id,
            company_id=company_id,
            service_type=AIServiceType.CONTENT_OPTIMIZATION,
            ai_model=AIModelType(request.ai_model),
            request_title="内容优化",
            request_prompt=self._build_content_optimization_prompt(request),
            request_parameters=request.dict(),
            input_content=request.original_content
        )
        
        try:
            # 调用AI服务
            start_time = time.time()
            ai_response = await self._call_ai_service(
                model=request.ai_model,
                prompt=ai_request.request_prompt,
                parameters=request.dict()
            )
            processing_time = int((time.time() - start_time) * 1000)
            
            # 解析响应
            optimized_content = ai_response.get('optimized_content', request.original_content)
            optimization_summary = ai_response.get('optimization_summary', {})
            improvements = ai_response.get('improvements', [])
            seo_score = ai_response.get('seo_score')
            readability_score = ai_response.get('readability_score')
            
            # 更新请求记录
            ai_request.status = AIRequestStatus.COMPLETED
            ai_request.response_content = optimized_content
            ai_request.response_data = {
                'optimization_summary': optimization_summary,
                'improvements': improvements,
                'seo_score': float(seo_score) if seo_score is not None else None,
                'readability_score': float(readability_score) if readability_score is not None else None
            }
            ai_request.tokens_used = ai_response.get('tokens_used', 0)
            ai_request.processing_time = processing_time
            ai_request.cost_amount = self._calculate_cost(request.ai_model, ai_response.get('tokens_used', 0))
            ai_request.completed_at = datetime.utcnow()
            
            await self.db.commit()
            
            # 更新配额
            await self._update_quota(user_id, AIServiceType.CONTENT_OPTIMIZATION, 1)
            
            return ContentOptimizationResponse(
                request_id=str(ai_request.id),
                optimized_content=optimized_content,
                optimization_summary=optimization_summary,
                improvements=improvements,
                seo_score=seo_score,
                readability_score=readability_score,
                tokens_used=ai_request.tokens_used,
                processing_time=processing_time
            )
            
        except Exception as e:
            try:
                ai_request.status = AIRequestStatus.FAILED
                ai_request.response_content = f"优化失败: {str(e)}"
                await self.db.commit()
            except Exception as commit_error:
                await self.db.rollback()
                print(f"数据库提交失败: {commit_error}")
            raise ValidationError(f"内容优化失败: {str(e)}")
    
    async def analyze_keywords(self, user_id: str, request: KeywordAnalysisRequest) -> KeywordAnalysisResponse:
        """关键词分析"""
        # 检查配额
        await self._check_quota(user_id, AIServiceType.KEYWORD_ANALYSIS)
        
        # 获取企业信息
        company_id = None
        company_result = await self.db.execute(
            select(Company).where(Company.user_id == user_id)
        )
        company = company_result.scalar_one_or_none()
        if company:
            company_id = company.id
        
        # 创建AI请求记录
        ai_request = await self._create_ai_request(
            user_id=user_id,
            company_id=company_id,
            service_type=AIServiceType.KEYWORD_ANALYSIS,
            ai_model=AIModelType(request.ai_model),
            request_title="关键词分析",
            request_prompt=self._build_keyword_analysis_prompt(request),
            request_parameters=request.dict(),
            input_keywords=request.keywords
        )
        
        try:
            # 调用AI服务
            start_time = time.time()
            ai_response = await self._call_ai_service(
                model=request.ai_model,
                prompt=ai_request.request_prompt,
                parameters=request.dict()
            )
            processing_time = int((time.time() - start_time) * 1000)
            
            # 解析响应
            keyword_insights = ai_response.get('keyword_insights', [])
            trend_analysis = ai_response.get('trend_analysis', {})
            competition_analysis = ai_response.get('competition_analysis') if request.include_competitors else None
            recommendations = ai_response.get('recommendations', [])
            
            # 更新请求记录
            ai_request.status = AIRequestStatus.COMPLETED
            ai_request.response_data = {
                'keyword_insights': keyword_insights,
                'trend_analysis': trend_analysis,
                'competition_analysis': competition_analysis,
                'recommendations': recommendations
            }
            ai_request.tokens_used = ai_response.get('tokens_used', 0)
            ai_request.processing_time = processing_time
            ai_request.cost_amount = self._calculate_cost(request.ai_model, ai_response.get('tokens_used', 0))
            ai_request.completed_at = datetime.utcnow()
            
            await self.db.commit()
            
            # 更新配额
            await self._update_quota(user_id, AIServiceType.KEYWORD_ANALYSIS, 1)
            
            return KeywordAnalysisResponse(
                request_id=str(ai_request.id),
                keyword_insights=keyword_insights,
                trend_analysis=trend_analysis,
                competition_analysis=competition_analysis,
                recommendations=recommendations,
                tokens_used=ai_request.tokens_used,
                processing_time=processing_time
            )
            
        except Exception as e:
            ai_request.status = AIRequestStatus.FAILED
            ai_request.response_content = f"分析失败: {str(e)}"
            await self.db.commit()
            raise ValidationError(f"关键词分析失败: {str(e)}")

    async def get_usage_history(self, query: AIUsageQuery, user_id: str) -> Dict[str, Any]:
        """获取AI使用记录"""
        # 构建查询条件
        conditions = [AIRequest.user_id == user_id]

        if query.service_type:
            conditions.append(AIRequest.service_type == query.service_type)
        if query.status:
            conditions.append(AIRequest.status == query.status)
        if query.start_date:
            conditions.append(AIRequest.created_at >= query.start_date)
        if query.end_date:
            conditions.append(AIRequest.created_at <= query.end_date)

        # 构建查询
        base_query = select(AIRequest).where(and_(*conditions))

        # 排序
        if query.sort_order == "desc":
            base_query = base_query.order_by(desc(getattr(AIRequest, query.sort_by)))
        else:
            base_query = base_query.order_by(asc(getattr(AIRequest, query.sort_by)))

        # 分页
        offset = (query.page - 1) * query.size
        paginated_query = base_query.offset(offset).limit(query.size)

        # 执行查询
        result = await self.db.execute(paginated_query)
        requests = result.scalars().all()

        # 获取总数
        count_query = select(func.count(AIRequest.id)).where(and_(*conditions))
        total_result = await self.db.execute(count_query)
        total = total_result.scalar()

        # 构建响应数据
        items = []
        for req in requests:
            request_response = AIRequestResponse(
                id=str(req.id),
                service_type=req.service_type.value,
                ai_model=req.ai_model.value,
                request_title=req.request_title,
                status=req.status.value,
                response_content=req.response_content,
                response_data=req.response_data,
                quality_score=req.quality_score,
                user_rating=req.user_rating,
                tokens_used=req.tokens_used,
                processing_time=req.processing_time,
                cost_amount=req.cost_amount,
                created_at=req.created_at,
                completed_at=req.completed_at
            )
            items.append(request_response)

        return {
            "items": items,
            "pagination": {
                "total": total,
                "page": query.page,
                "size": query.size,
                "pages": (total + query.size - 1) // query.size
            },
            "statistics": await self._get_usage_statistics(user_id)
        }

    async def get_quota_status(self, user_id: str) -> List[AIQuotaResponse]:
        """获取AI配额状态"""
        result = await self.db.execute(
            select(AIQuota).where(
                and_(AIQuota.user_id == user_id, AIQuota.is_active == True)
            )
        )
        quotas = result.scalars().all()

        # 如果没有配额记录，创建默认配额
        if not quotas:
            await self._create_default_quotas(user_id)
            result = await self.db.execute(
                select(AIQuota).where(
                    and_(AIQuota.user_id == user_id, AIQuota.is_active == True)
                )
            )
            quotas = result.scalars().all()

        quota_responses = []
        for quota in quotas:
            usage_percentage = (quota.used_quota / quota.total_quota * 100) if quota.total_quota > 0 else 0

            quota_response = AIQuotaResponse(
                service_type=quota.service_type.value,
                total_quota=quota.total_quota,
                used_quota=quota.used_quota,
                remaining_quota=quota.remaining_quota,
                quota_period=quota.quota_period,
                period_start=quota.period_start,
                period_end=quota.period_end,
                usage_percentage=round(usage_percentage, 2)
            )
            quota_responses.append(quota_response)

        return quota_responses

    async def _create_ai_request(self, user_id: str, company_id: str, service_type: AIServiceType, ai_model: AIModelType,
                               request_title: str, request_prompt: str, request_parameters: Dict,
                               input_content: str = None, input_keywords: List[str] = None, conversation_id: str = None) -> AIRequest:
        """创建AI请求记录"""
        ai_request = AIRequest(
            user_id=user_id,
            company_id=company_id,
            service_type=service_type,
            ai_model=ai_model,
            request_title=request_title,
            request_prompt=request_prompt,
            request_parameters=request_parameters,
            input_content=input_content,
            input_keywords=input_keywords,
            started_at=datetime.utcnow(),
            conversation_id=conversation_id  # 现在可以使用了
        )

        self.db.add(ai_request)
        await self.db.commit()
        await self.db.refresh(ai_request)

        return ai_request

    async def _create_ai_request_without_commit(self, user_id: str, company_id: str, service_type: AIServiceType, ai_model: AIModelType,
                               request_title: str, request_prompt: str, request_parameters: Dict,
                               input_content: str = None, input_keywords: List[str] = None, conversation_id: str = None) -> AIRequest:
        """创建AI请求记录（不提交事务）"""
        ai_request = AIRequest(
            user_id=user_id,
            company_id=company_id,
            service_type=service_type,
            ai_model=ai_model,
            request_title=request_title,
            request_prompt=request_prompt,
            request_parameters=request_parameters,
            input_content=input_content,
            input_keywords=input_keywords,
            started_at=datetime.utcnow(),
            conversation_id=conversation_id  # 现在可以使用了
        )

        self.db.add(ai_request)
        await self.db.flush()  # 刷新到数据库但不提交事务
        await self.db.refresh(ai_request)

        return ai_request

    async def _check_quota(self, user_id: str, service_type: AIServiceType):
        """检查配额"""
        result = await self.db.execute(
            select(AIQuota).where(
                and_(
                    AIQuota.user_id == user_id,
                    AIQuota.service_type == service_type,
                    AIQuota.is_active == True,
                    AIQuota.period_end > datetime.utcnow()
                )
            )
        )
        quota = result.scalar_one_or_none()

        if not quota:
            # 创建默认配额
            await self._create_default_quotas(user_id)
            # 重新查询
            result = await self.db.execute(
                select(AIQuota).where(
                    and_(
                        AIQuota.user_id == user_id,
                        AIQuota.service_type == service_type,
                        AIQuota.is_active == True
                    )
                )
            )
            quota = result.scalar_one_or_none()

        if not quota or quota.remaining_quota <= 0:
            raise QuotaExceededError(f"{service_type.value}配额不足")

    async def _update_quota(self, user_id: str, service_type: AIServiceType, usage: int):
        """更新配额使用量"""
        result = await self.db.execute(
            select(AIQuota).where(
                and_(
                    AIQuota.user_id == user_id,
                    AIQuota.service_type == service_type,
                    AIQuota.is_active == True
                )
            )
        )
        quota = result.scalar_one_or_none()

        if quota:
            quota.used_quota += usage
            quota.remaining_quota = max(0, quota.total_quota - quota.used_quota)
            quota.updated_at = datetime.utcnow()
            await self.db.commit()

    async def _create_default_quotas(self, user_id: str):
        """创建默认配额"""
        now = datetime.utcnow()
        period_start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        period_end = (period_start + timedelta(days=32)).replace(day=1) - timedelta(seconds=1)

        default_quotas = {
            AIServiceType.CONTENT_GENERATION: 100,
            AIServiceType.CONTENT_OPTIMIZATION: 50,
            AIServiceType.KEYWORD_ANALYSIS: 30
        }

        for service_type, total_quota in default_quotas.items():
            quota = AIQuota(
                user_id=user_id,
                service_type=service_type,
                total_quota=total_quota,
                used_quota=0,
                remaining_quota=total_quota,
                quota_period="monthly",
                period_start=period_start,
                period_end=period_end
            )
            self.db.add(quota)

        await self.db.commit()

    def _build_content_generation_prompt(self, request: ContentGenerationRequest) -> str:
        """构建内容生成提示词"""
        prompt = f"""
        请根据以下要求生成{request.content_type}内容：

        主题：{request.topic}
        关键词：{', '.join(request.keywords)}
        目标受众：{request.target_audience or '通用受众'}
        语调：{request.tone}
        长度：{request.length}
        语言：{request.language}

        额外要求：{request.additional_requirements or '无'}

        请确保内容原创、有价值，并自然地融入关键词。
        """
        return prompt.strip()

    def _build_content_optimization_prompt(self, request: ContentOptimizationRequest) -> str:
        """构建内容优化提示词"""
        prompt = f"""
        请对以下内容进行优化：

        原始内容：
        {request.original_content}

        优化目标：{', '.join(request.optimization_goals)}
        目标关键词：{', '.join(request.target_keywords or [])}
        目标受众：{request.target_audience or '通用受众'}
        发布平台：{request.platform or '通用平台'}

        请提供优化后的内容和具体的改进建议。
        """
        return prompt.strip()

    def _build_keyword_analysis_prompt(self, request: KeywordAnalysisRequest) -> str:
        """构建关键词分析提示词"""
        prompt = f"""
        请分析以下关键词：

        关键词：{', '.join(request.keywords)}
        行业：{request.industry or '通用'}
        目标地区：{request.target_region}
        分析深度：{request.analysis_depth}
        包含竞争分析：{'是' if request.include_competitors else '否'}

        请提供关键词的热度、竞争度、相关性分析和优化建议。
        """
        return prompt.strip()

    async def _call_ai_service(self, model: str, prompt: str, parameters: Dict) -> Dict[str, Any]:
        """调用豆包AI服务"""
        try:
            # 构建请求数据 - 火山引擎豆包API格式（标准OpenAI格式）
            request_data = {
                "model": settings.ai_model or "doubao-seed-1-6-flash-250715",
                "messages": [
                    {
                        "role": "system",
                        "content": "You are a helpful assistant."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "temperature": 0.7,
                "max_tokens": 2000
            }

            headers = {
                "Authorization": f"Bearer {settings.ai_api_key}",
                "Content-Type": "application/json"
            }

            # 调用豆包API
            async with httpx.AsyncClient(timeout=60.0) as client:
                # 使用火山引擎豆包的正确端点格式（根据官方示例）
                api_url = f"{settings.ai_api_url}/chat/completions"
                logger.info(f"调用豆包API: {api_url}")
                logger.info(f"请求数据: {json.dumps(request_data, ensure_ascii=False)}")

                response = await client.post(
                    api_url,
                    headers=headers,
                    json=request_data
                )

                # 记录响应信息用于调试
                logger.info(f"豆包API响应状态: {response.status_code}")
                if response.status_code != 200:
                    error_text = response.text
                    logger.error(f"豆包API错误响应: {error_text}")

                response.raise_for_status()

                result = response.json()

                # 解析豆包响应
                if "choices" in result and len(result["choices"]) > 0:
                    content = result["choices"][0]["message"]["content"]
                    tokens_used = result.get("usage", {}).get("total_tokens", 0)

                    # 根据服务类型解析内容
                    if '生成' in prompt:
                        return {
                            'content': content,
                            'tokens_used': tokens_used,
                            'suggestions': self._extract_suggestions_from_content(content)
                        }
                    elif '优化' in prompt:
                        return self._parse_optimization_response(content, tokens_used)
                    elif '分析' in prompt:
                        return self._parse_analysis_response(content, tokens_used, parameters)
                    else:
                        return {
                            'content': content,
                            'tokens_used': tokens_used
                        }
                else:
                    raise Exception("豆包API返回格式异常")

        except httpx.HTTPError as e:
            logger.error(f"豆包API请求失败: {e}")
            raise Exception(f"AI服务调用失败: {str(e)}")
        except Exception as e:
            logger.error(f"豆包AI服务异常: {e}")
            raise Exception(f"AI服务异常: {str(e)}")

    def _extract_suggestions_from_content(self, content: str) -> List[str]:
        """从生成的内容中提取建议"""
        # 简单的建议提取逻辑
        suggestions = []
        if len(content) < 200:
            suggestions.append("建议增加更多细节和具体信息")
        if "案例" not in content and "例子" not in content:
            suggestions.append("可以添加相关案例或实例")
        if len(content.split('\n')) < 3:
            suggestions.append("建议优化段落结构，增加层次感")
        return suggestions or ["内容质量良好，可以直接使用"]

    def _parse_optimization_response(self, content: str, tokens_used: int) -> Dict[str, Any]:
        """解析优化响应"""
        # 基于豆包AI实际返回的内容进行解析
        word_count = len(content.split())

        return {
            'optimized_content': content,
            'optimization_summary': {
                'changes_made': 1 if content else 0,
                'readability_improved': word_count > 50,
                'seo_optimized': word_count > 100
            },
            'improvements': [
                {'type': 'ai_optimization', 'description': '内容已通过豆包AI进行优化处理'}
            ] if content else [],
            'seo_score': min(Decimal('5.0'), Decimal(str(3.0 + min(2.0, word_count / 200)))),
            'readability_score': min(Decimal('5.0'), Decimal(str(3.0 + min(2.0, word_count / 150)))),
            'tokens_used': tokens_used
        }

    def _parse_analysis_response(self, content: str, tokens_used: int, parameters: Dict) -> Dict[str, Any]:
        """解析分析响应"""
        keywords = parameters.get('keywords', [])

        # 如果豆包没有返回内容，则返回错误信息
        if not content or not content.strip():
            raise Exception("豆包AI未返回关键词分析结果")

        # 基于豆包AI的实际响应内容进行解析
        # 这里应该解析豆包返回的结构化数据，目前先返回基础格式
        return {
            'keyword_insights': [
                {
                    'keyword': kw,
                    'analysis_result': content,  # 豆包的分析结果
                    'ai_generated': True
                }
                for kw in keywords
            ],
            'trend_analysis': {
                'ai_analysis': content,  # 豆包的趋势分析
                'source': 'doubao_ai'
            },
            'competition_analysis': {
                'ai_analysis': content if parameters.get('include_competitors') else None,
                'source': 'doubao_ai'
            } if parameters.get('include_competitors') else None,
            'recommendations': [content],  # 直接使用豆包的建议
            'tokens_used': tokens_used
        }

    async def _call_ai_service_stream(self, model: str, prompt: str, parameters: Dict) -> AsyncGenerator[Dict[str, Any], None]:
        """流式调用豆包AI服务"""
        try:
            logger.info(f"🚀 进入_call_ai_service_stream方法 - 模型: {model}")
            logger.info(f"提示词长度: {len(prompt)}")
            logger.info(f"参数: {parameters}")

            # 构建请求数据 - 火山引擎豆包API格式（标准OpenAI格式）
            request_data = {
                "model": settings.ai_model or "doubao-seed-1-6-flash-250715",
                "messages": [
                    {
                        "role": "system",
                        "content": "You are a helpful assistant."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "temperature": 0.7,
                "max_tokens": 2000,
                "stream": True
            }
            logger.info(f"请求数据构建完成 - 模型: {request_data['model']}")

            headers = {
                "Authorization": f"Bearer {settings.ai_api_key}",
                "Content-Type": "application/json"
            }

            # 发送进度更新
            yield {
                'type': 'progress',
                'message': '正在连接豆包AI...',
                'progress': 10
            }

            # 流式调用豆包API
            async with httpx.AsyncClient(timeout=120.0) as client:
                # 记录请求信息用于调试 - 使用正确的端点
                api_url = f"{settings.ai_api_url}/chat/completions"
                logger.info(f"流式调用豆包API: {api_url}")
                logger.info(f"请求模型: {request_data['model']}")
                logger.info(f"请求头: Authorization=Bearer {settings.ai_api_key[:10]}...")

                try:
                    async with client.stream(
                        "POST",
                        api_url,
                        headers=headers,
                        json=request_data
                    ) as response:
                        logger.info(f"获得流式响应对象，开始处理...")

                        # 记录响应信息用于调试
                        logger.info(f"豆包流式API响应状态: {response.status_code}")
                        if response.status_code != 200:
                            # 读取错误响应内容
                            error_chunks = []
                            async for chunk in response.aiter_bytes():
                                error_chunks.append(chunk)
                            error_text = b''.join(error_chunks).decode('utf-8')
                            logger.error(f"豆包流式API错误响应: {error_text}")
                            raise Exception(f"豆包API返回错误: {response.status_code} - {error_text}")

                        response.raise_for_status()

                        yield {
                            'type': 'progress',
                            'message': '开始生成内容...',
                            'progress': 20
                        }

                        logger.info("开始读取流式响应数据...")
                        line_count = 0
                        async for line in response.aiter_lines():
                            line_count += 1
                            logger.debug(f"收到第{line_count}行数据: {line}")

                            if line.startswith("data: "):
                                data_str = line[6:]  # 去掉 "data: " 前缀
                                logger.debug(f"解析数据: {data_str}")

                                if data_str.strip() == "[DONE]":
                                    logger.info("收到流式响应结束标志")
                                    break

                                try:
                                    data = json.loads(data_str)
                                    logger.debug(f"解析JSON成功: {data}")
                                    if "choices" in data and len(data["choices"]) > 0:
                                        delta = data["choices"][0].get("delta", {})
                                        content_chunk = ""

                                        # 检查content字段
                                        if "content" in delta and delta["content"]:
                                            content_chunk = delta["content"]
                                        # 检查reasoning_content字段（豆包AI特有）
                                        elif "reasoning_content" in delta and delta["reasoning_content"]:
                                            content_chunk = delta["reasoning_content"]

                                        if content_chunk:
                                            logger.debug(f"生成内容片段: {content_chunk}")
                                            yield {
                                                'type': 'content',
                                                'content': content_chunk
                                            }
                                except json.JSONDecodeError as e:
                                    logger.warning(f"JSON解析失败: {e}, 数据: {data_str}")
                                    continue  # 跳过无效的JSON行

                        logger.info(f"流式响应处理完成，共处理{line_count}行数据")
                except Exception as stream_error:
                    logger.error(f"流式处理异常: {stream_error}")
                    raise stream_error

            # 发送完成信号
            yield {
                'type': 'progress',
                'message': '内容生成完成',
                'progress': 100
            }

        except httpx.HTTPError as e:
            error_msg = f"豆包流式API请求失败: {e}"
            logger.error(error_msg)
            logger.error(f"API URL: {settings.ai_api_url}")
            logger.error(f"API Key: {settings.ai_api_key[:10] if settings.ai_api_key else 'None'}...")
            yield {
                'type': 'error',
                'message': f"AI服务调用失败: {str(e)}"
            }
        except Exception as e:
            error_msg = f"豆包流式AI服务异常: {e}"
            logger.error(error_msg)
            yield {
                'type': 'error',
                'message': f"AI服务异常: {str(e)}"
            }

    def _analyze_content_metadata(self, content: str) -> Dict[str, Any]:
        """分析内容元数据"""
        word_count = len(content.split())
        char_count = len(content)

        # 基于内容长度计算可读性评分
        readability_score = 3.0
        if word_count > 100:
            readability_score += 0.5
        if word_count > 300:
            readability_score += 0.5
        if char_count > 500:
            readability_score += 0.5

        return {
            'word_count': word_count,
            'character_count': char_count,
            'estimated_reading_time': max(1, word_count // 200),  # 假设每分钟200字
            'keyword_density': {},  # TODO: 计算关键词密度
            'readability_score': min(5.0, readability_score)
        }

    def _calculate_quality_score(self, content: str, keywords: List[str]) -> Decimal:
        """计算内容质量评分"""
        # TODO: 实现质量评分算法
        # 简化评分：基于内容长度和关键词包含情况
        base_score = 3.0

        # 内容长度评分
        if len(content) > 500:
            base_score += 0.5
        if len(content) > 1000:
            base_score += 0.3

        # 关键词包含评分
        content_lower = content.lower()
        keyword_matches = sum(1 for kw in keywords if kw.lower() in content_lower)
        if keyword_matches > 0:
            base_score += min(1.0, keyword_matches * 0.2)

        return Decimal(str(round(min(5.0, base_score), 2)))

    def _calculate_cost(self, model: str, tokens_used: int) -> Decimal:
        """计算使用成本"""
        # 豆包AI的价格（每1000 tokens）
        price_per_1k = Decimal('0.001')
        return (Decimal(tokens_used) / 1000) * price_per_1k

    def _init_ai_clients(self) -> Dict[str, Any]:
        """初始化AI客户端"""
        # TODO: 初始化各种AI服务的客户端
        return {}

    async def _update_usage_statistics(self, user_id: str, ai_request: AIRequest):
        """更新使用统计"""
        # TODO: 实现使用统计更新逻辑
        pass

    async def _get_usage_statistics(self, user_id: str) -> Dict[str, Any]:
        """获取使用统计"""
        # 获取总请求数
        total_requests_result = await self.db.execute(
            select(func.count(AIRequest.id)).where(AIRequest.user_id == user_id)
        )
        total_requests = total_requests_result.scalar() or 0

        # 获取成功请求数
        successful_requests_result = await self.db.execute(
            select(func.count(AIRequest.id)).where(
                and_(AIRequest.user_id == user_id, AIRequest.status == AIRequestStatus.COMPLETED)
            )
        )
        successful_requests = successful_requests_result.scalar() or 0

        # 获取总token数
        total_tokens_result = await self.db.execute(
            select(func.sum(AIRequest.tokens_used)).where(AIRequest.user_id == user_id)
        )
        total_tokens = total_tokens_result.scalar() or 0

        # 获取总成本
        total_cost_result = await self.db.execute(
            select(func.sum(AIRequest.cost_amount)).where(AIRequest.user_id == user_id)
        )
        total_cost = total_cost_result.scalar() or Decimal('0.0000')

        return {
            "total_requests": total_requests,
            "successful_requests": successful_requests,
            "failed_requests": total_requests - successful_requests,
            "success_rate": round((successful_requests / total_requests * 100) if total_requests > 0 else 0, 2),
            "total_tokens": total_tokens,
            "total_cost": total_cost
        }

    # AI模板管理方法
    async def create_template(self, user_id: str, template_data: AITemplateCreate) -> AITemplateResponse:
        """创建AI模板"""
        try:
            # 检查模板名称是否重复
            existing_template = await self.db.execute(
                select(AITemplate).where(
                    and_(
                        AITemplate.template_name == template_data.template_name,
                        AITemplate.is_active == True
                    )
                )
            )
            if existing_template.scalar_one_or_none():
                raise ValidationError("模板名称已存在")

            # 创建模板
            template = AITemplate(
                template_name=template_data.template_name,
                template_description=template_data.template_description,
                prompt_template=template_data.prompt_template,
                default_parameters=template_data.default_parameters,
                created_by=user_id
            )

            self.db.add(template)
            await self.db.commit()
            await self.db.refresh(template)

            return await self._build_template_response(template)

        except Exception as e:
            await self.db.rollback()
            raise e

    async def get_template_list(self, query: AITemplateListQuery, user_id: str = None) -> Dict[str, Any]:
        """获取模板列表"""
        # 构建查询条件
        conditions = []

        # 处理is_active参数
        if query.is_active is not None:
            conditions.append(AITemplate.is_active == query.is_active)
        # 如果没有指定is_active参数，返回所有状态的模板（不添加is_active条件）

        if query.keyword:
            keyword_condition = or_(
                AITemplate.template_name.ilike(f"%{query.keyword}%"),
                AITemplate.template_description.ilike(f"%{query.keyword}%")
            )
            conditions.append(keyword_condition)

        # 构建排序
        sort_column = getattr(AITemplate, query.sort_by, AITemplate.created_at)
        if query.sort_order.lower() == "desc":
            sort_column = desc(sort_column)
        else:
            sort_column = asc(sort_column)

        # 查询总数
        count_result = await self.db.execute(
            select(func.count(AITemplate.id)).where(and_(*conditions))
        )
        total = count_result.scalar()

        # 分页查询
        offset = (query.page - 1) * query.size
        templates_result = await self.db.execute(
            select(AITemplate)
            .where(and_(*conditions))
            .order_by(sort_column)
            .offset(offset)
            .limit(query.size)
        )
        templates = templates_result.scalars().all()

        # 构建响应
        items = []
        for template in templates:
            items.append(await self._build_template_response(template))

        return {
            "items": items,
            "pagination": {
                "total": total,
                "page": query.page,
                "size": query.size,
                "pages": (total + query.size - 1) // query.size
            },
            "statistics": await self._get_template_statistics(conditions)
        }

    async def get_template_detail(self, template_id: str, user_id: str = None) -> AITemplateResponse:
        """获取模板详情"""
        template_result = await self.db.execute(
            select(AITemplate).where(AITemplate.id == template_id)
        )
        template = template_result.scalar_one_or_none()

        if not template:
            raise NotFoundError("模板不存在")

        return await self._build_template_response(template)

    async def update_template(self, template_id: str, template_data: AITemplateUpdate, user_id: str) -> AITemplateResponse:
        """更新模板"""
        template_result = await self.db.execute(
            select(AITemplate).where(AITemplate.id == template_id)
        )
        template = template_result.scalar_one_or_none()

        if not template:
            raise NotFoundError("模板不存在")

        try:
            # 更新字段
            update_data = template_data.model_dump(exclude_unset=True)
            for field, value in update_data.items():
                if hasattr(template, field):
                    setattr(template, field, value)

            template.updated_at = datetime.utcnow()

            await self.db.commit()
            await self.db.refresh(template)

            return await self._build_template_response(template)

        except Exception as e:
            await self.db.rollback()
            raise e

    async def delete_template(self, template_id: str, user_id: str) -> bool:
        """删除模板（真删除）"""
        template_result = await self.db.execute(
            select(AITemplate).where(AITemplate.id == template_id)
        )
        template = template_result.scalar_one_or_none()

        if not template:
            raise NotFoundError("模板不存在")

        try:
            # 真删除 - 从数据库中彻底删除记录
            await self.db.delete(template)
            await self.db.commit()
            return True

        except Exception as e:
            await self.db.rollback()
            raise e

    async def _build_template_response(self, template: AITemplate) -> AITemplateResponse:
        """构建模板响应对象"""
        return AITemplateResponse(
            id=str(template.id),
            template_name=template.template_name,
            template_description=template.template_description,
            prompt_template=template.prompt_template,
            default_parameters=template.default_parameters,
            usage_count=template.usage_count,
            success_rate=template.success_rate,
            avg_quality_score=template.avg_quality_score,
            is_active=template.is_active,
            created_at=template.created_at,
            updated_at=template.updated_at
        )

    async def _get_template_statistics(self, conditions: List) -> Dict[str, Any]:
        """获取模板统计信息"""
        # 获取总数统计
        total_count = await self.db.execute(
            select(func.count(AITemplate.id))
            .where(and_(*conditions))
        )

        # 按状态统计
        active_count = await self.db.execute(
            select(func.count(AITemplate.id))
            .where(and_(*conditions, AITemplate.is_active == True))
        )

        inactive_count = await self.db.execute(
            select(func.count(AITemplate.id))
            .where(and_(*conditions, AITemplate.is_active == False))
        )

        return {
            "total": total_count.scalar() or 0,
            "active": active_count.scalar() or 0,
            "inactive": inactive_count.scalar() or 0
        }

    # ========== V2版本增强方法 ==========

    async def _build_enhanced_prompt(self, request: ContentGenerationRequest,
                                   template: AITemplate = None,
                                   conversation = None,
                                   user_id: str = None) -> str:
        """构建增强提示词"""

        if request.is_first_request and template:
            # 首次请求：基于模板构建
            base_prompt = self._build_template_based_prompt(request, template)
        elif conversation:
            # 后续请求：基于上下文构建
            base_prompt = await self._build_context_based_prompt(request, conversation)
        else:
            # 降级处理
            base_prompt = self._build_content_generation_prompt(request)

        # 集成知识库内容
        knowledge_context = ""
        knowledge_bases = request.knowledge_bases or (conversation.knowledge_bases if conversation else None)
        if knowledge_bases and user_id:
            knowledge_context = await self._build_knowledge_context(
                user_id, knowledge_bases, request.topic, request.keywords
            )

        # 组合最终提示词
        if knowledge_context:
            return f"{base_prompt}\n\n{knowledge_context}"
        return base_prompt

    def _build_template_based_prompt(self, request: ContentGenerationRequest, template: AITemplate) -> str:
        """基于模板构建提示词"""

        # 获取模板内容
        prompt_template = template.prompt_template

        # 合并参数：模板默认参数 + 用户提供参数
        all_parameters = {}
        if template.default_parameters:
            all_parameters.update(template.default_parameters)
        if request.template_parameters:
            all_parameters.update(request.template_parameters)

        # 参数替换 - 支持多种格式
        for key, value in all_parameters.items():
            if value is not None:
                # 支持 {{key}}, {key}, ${key} 三种格式
                patterns = [
                    f"{{{{{key}}}}}",  # {{key}}
                    f"{{{key}}}",      # {key}
                    f"${{{key}}}",     # ${key}
                ]
                for pattern in patterns:
                    prompt_template = prompt_template.replace(pattern, str(value))

        # 添加基础请求信息
        base_info = f"""
请根据以下具体要求生成内容：

内容类型：{request.content_type}
主题：{request.topic}
关键词：{', '.join(request.keywords)}
目标受众：{request.target_audience or '通用受众'}
语调风格：{request.tone}
内容长度：{request.length}
语言：{request.language}
"""

        # 添加额外要求
        if request.additional_requirements:
            base_info += f"\n额外要求：{request.additional_requirements}"

        # 组合最终提示词
        final_prompt = f"{prompt_template}\n\n{base_info}"
        return final_prompt.strip()

    async def _build_context_based_prompt(self, request: ContentGenerationRequest, conversation) -> str:
        """基于上下文构建提示词"""

        # 获取对话历史（最近10条消息）
        from app.repositories.conversation_repository import ConversationRepository
        conversation_repo = ConversationRepository(self.db)
        recent_messages = await conversation_repo.get_recent_messages(str(conversation.id), limit=10)

        # 构建对话历史文本
        history_text = ""
        for msg in recent_messages:
            role = "用户" if msg.message_type.value == "USER" else "助手"
            history_text += f"{role}：{msg.content}\n\n"

        # 获取对话摘要
        context_summary = conversation.context_summary or "暂无对话摘要"

        # 构建上下文提示词
        context_prompt = f"""
基于以下对话历史和上下文，请对内容进行调整和优化：

对话摘要：
{context_summary}

最近对话历史：
{history_text}

用户最新请求：{request.topic}

请根据用户的反馈和要求，对之前生成的内容进行相应的调整和优化。
保持内容的连贯性和一致性，确保满足用户的具体需求。
"""

        return context_prompt.strip()

    async def _build_knowledge_context(self, user_id: str, knowledge_base_ids: List[str],
                                     query: str, keywords: List[str]) -> str:
        """构建知识库增强上下文"""
        try:
            if not knowledge_base_ids:
                return ""

            # 使用VikingDB进行语义检索
            from app.services.knowledge_service import KnowledgeService
            knowledge_service = KnowledgeService(self.db)
            relevant_docs = await knowledge_service.search_knowledge(
                user_id=user_id,
                knowledge_base_ids=knowledge_base_ids,
                query=query,
                keywords=keywords,
                limit=3  # 限制返回数量，避免提示词过长
            )

            if not relevant_docs:
                return "参考知识库：已关联个人知识库，将基于您的专业知识生成内容。"

            # 构建知识库上下文
            context_parts = ["参考知识库内容："]
            for i, doc in enumerate(relevant_docs, 1):
                context_parts.append(f"""
{i}. 【{doc['title']}】
{doc['content'][:300]}...
相关度: {doc.get('final_score', doc.get('similarity_score', 0)):.2f}
""")

            context = "\n".join(context_parts)

            # 添加使用指导
            context += "\n\n请基于以上知识库内容，结合用户需求生成专业、准确的内容。"

            return context

        except Exception as e:
            logger.error(f"构建知识库上下文失败: {e}")
            return "参考知识库：已关联个人知识库，将基于您的专业知识生成内容。"

    async def generate_simple_completion(self, prompt: str, model: str = "doubao") -> Dict[str, Any]:
        """生成简单的AI完成（用于摘要等）"""
        try:
            response = await self._call_ai_service(
                model=model,
                prompt=prompt,
                parameters={"max_tokens": 500, "temperature": 0.3}
            )
            return response
        except Exception as e:
            logger.error(f"简单完成生成失败: {e}")
            return {"content": ""}

    async def _get_template(self, template_id: str) -> Optional[AITemplate]:
        """获取模板"""
        try:
            result = await self.db.execute(
                select(AITemplate).where(
                    and_(
                        AITemplate.id == template_id,
                        AITemplate.is_active == True
                    )
                )
            )
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"获取模板失败: {e}")
            return None
