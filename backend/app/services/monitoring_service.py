from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func, desc, asc
from app.models.monitoring import MonitoringProject, RankingRecord, KeywordTrend, MonitoringAlert, ProjectStatus
from app.models.company import Company
from app.schemas.monitoring import *
from app.exceptions import ValidationError, NotFoundError, PermissionError
from typing import Optional, Dict, Any, List
from datetime import datetime, timedelta
import random

class MonitoringService:
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_monitoring_project(self, user_id: str, request: MonitoringProjectCreate) -> MonitoringProjectCreateResponse:
        """创建监控项目"""
        # 获取企业信息
        company_id = None
        company_result = await self.db.execute(
            select(Company).where(Company.user_id == user_id)
        )
        company = company_result.scalar_one_or_none()
        if company:
            company_id = company.id
        
        # 检查项目名称是否重复
        existing_project = await self.db.execute(
            select(MonitoringProject).where(
                and_(
                    MonitoringProject.user_id == user_id,
                    MonitoringProject.project_name == request.project_name
                )
            )
        )
        if existing_project.scalar_one_or_none():
            raise ValidationError("项目名称已存在")
        
        # 计算下次监控时间
        next_monitor_at = self._calculate_next_monitor_time(request.monitoring_frequency)
        
        # 创建监控项目
        project = MonitoringProject(
            user_id=user_id,
            company_id=company_id,
            project_name=request.project_name,
            project_description=request.project_description,
            target_website=request.target_website,
            target_brand=request.target_brand,
            keywords=request.keywords,
            search_engines=request.search_engines,
            monitoring_frequency=request.monitoring_frequency,
            competitors=request.competitors,
            total_keywords=len(request.keywords),
            next_monitor_at=next_monitor_at
        )
        
        self.db.add(project)
        await self.db.commit()
        await self.db.refresh(project)
        
        # 启动首次监控任务（模拟）
        await self._schedule_monitoring_task(project)
        
        return MonitoringProjectCreateResponse(
            id=str(project.id),
            project_name=project.project_name,
            target_website=project.target_website,
            target_brand=project.target_brand,
            keywords=project.keywords,
            search_engines=project.search_engines,
            monitoring_frequency=project.monitoring_frequency.value,
            status=project.status.value,
            total_keywords=project.total_keywords,
            created_at=project.created_at,
            next_monitor_at=project.next_monitor_at
        )
    
    async def get_project_list(self, query: ProjectListQuery, user_id: str) -> Dict[str, Any]:
        """获取监控项目列表"""
        # 构建查询条件
        conditions = [MonitoringProject.user_id == user_id]
        
        if query.status:
            conditions.append(MonitoringProject.status == query.status)
        if query.keyword:
            # 搜索项目名称或关键词
            search_condition = or_(
                MonitoringProject.project_name.ilike(f"%{query.keyword}%"),
                MonitoringProject.target_brand.ilike(f"%{query.keyword}%")
            )
            conditions.append(search_condition)
        
        # 构建查询
        base_query = select(MonitoringProject).where(and_(*conditions))
        
        # 排序
        if query.sort_order == "desc":
            base_query = base_query.order_by(desc(getattr(MonitoringProject, query.sort_by)))
        else:
            base_query = base_query.order_by(asc(getattr(MonitoringProject, query.sort_by)))
        
        # 分页
        offset = (query.page - 1) * query.size
        paginated_query = base_query.offset(offset).limit(query.size)
        
        # 执行查询
        result = await self.db.execute(paginated_query)
        projects = result.scalars().all()
        
        # 获取总数
        count_query = select(func.count(MonitoringProject.id)).where(and_(*conditions))
        total_result = await self.db.execute(count_query)
        total = total_result.scalar()
        
        # 构建响应数据
        items = []
        for project in projects:
            project_response = await self._build_project_response(project)
            items.append(project_response)
        
        return {
            "items": items,
            "pagination": {
                "total": total,
                "page": query.page,
                "size": query.size,
                "pages": (total + query.size - 1) // query.size
            },
            "statistics": await self._get_project_statistics(user_id)
        }

    async def get_project_detail(self, user_id: str, project_id: str) -> MonitoringProjectResponse:
        """获取监控项目详情"""
        # 验证项目权限
        project = await self._get_project_with_permission(project_id, user_id)

        # 构建项目响应数据
        return await self._build_project_response(project)

    async def get_project_rankings(self, project_id: str, query: RankingQuery, user_id: str) -> Dict[str, Any]:
        """获取项目排名数据"""
        # 验证项目权限
        project = await self._get_project_with_permission(project_id, user_id)
        
        # 构建查询条件
        conditions = [RankingRecord.project_id == project_id]
        
        if query.start_date:
            conditions.append(RankingRecord.monitored_at >= query.start_date)
        if query.end_date:
            conditions.append(RankingRecord.monitored_at <= query.end_date)
        if query.keyword:
            conditions.append(RankingRecord.keyword == query.keyword)
        if query.search_engine:
            conditions.append(RankingRecord.search_engine == query.search_engine)
        
        # 构建查询
        base_query = select(RankingRecord).where(and_(*conditions))
        base_query = base_query.order_by(desc(RankingRecord.monitored_at))
        
        # 分页
        offset = (query.page - 1) * query.size
        paginated_query = base_query.offset(offset).limit(query.size)
        
        # 执行查询
        result = await self.db.execute(paginated_query)
        rankings = result.scalars().all()
        
        # 获取总数
        count_query = select(func.count(RankingRecord.id)).where(and_(*conditions))
        total_result = await self.db.execute(count_query)
        total = total_result.scalar()
        
        # 构建排名记录响应
        ranking_items = []
        for ranking in rankings:
            ranking_response = RankingRecordResponse(
                id=str(ranking.id),
                keyword=ranking.keyword,
                search_engine=ranking.search_engine.value,
                search_query=ranking.search_query,
                ranking_position=ranking.ranking_position,
                total_results=ranking.total_results,
                result_title=ranking.result_title,
                result_url=ranking.result_url,
                result_snippet=ranking.result_snippet,
                result_score=ranking.result_score,
                competitor_rankings=ranking.competitor_rankings,
                monitored_at=ranking.monitored_at
            )
            ranking_items.append(ranking_response)
        
        # 获取趋势数据
        trends = await self._get_keyword_trends(project_id, query)
        
        # 获取统计数据
        statistics = await self._get_ranking_statistics(project_id, query)
        
        return {
            "items": ranking_items,
            "pagination": {
                "total": total,
                "page": query.page,
                "size": query.size,
                "pages": (total + query.size - 1) // query.size
            },
            "trends": trends,
            "statistics": statistics
        }
    
    async def execute_monitoring_task(self, project_id: str) -> Dict[str, Any]:
        """执行监控任务"""
        # 获取项目信息
        project_result = await self.db.execute(
            select(MonitoringProject).where(MonitoringProject.id == project_id)
        )
        project = project_result.scalar_one_or_none()
        if not project:
            raise NotFoundError("监控项目不存在")
        
        if project.status != ProjectStatus.ACTIVE:
            return {"status": "skipped", "reason": "项目未激活"}
        
        # 执行监控
        monitoring_results = []
        for keyword in project.keywords:
            for search_engine in project.search_engines:
                try:
                    # 执行搜索并获取排名
                    ranking_data = await self._perform_search_ranking(
                        keyword, search_engine, project.target_website, project.competitors
                    )
                    
                    # 保存排名记录
                    ranking_record = RankingRecord(
                        project_id=project.id,
                        keyword=keyword,
                        search_engine=search_engine,
                        search_query=ranking_data.get('search_query'),
                        ranking_position=ranking_data.get('ranking_position'),
                        total_results=ranking_data.get('total_results'),
                        result_title=ranking_data.get('result_title'),
                        result_url=ranking_data.get('result_url'),
                        result_snippet=ranking_data.get('result_snippet'),
                        result_score=ranking_data.get('result_score'),
                        competitor_rankings=ranking_data.get('competitor_rankings'),
                        search_metadata=ranking_data.get('metadata')
                    )
                    
                    self.db.add(ranking_record)
                    monitoring_results.append({
                        "keyword": keyword,
                        "search_engine": search_engine,
                        "ranking_position": ranking_data.get('ranking_position'),
                        "status": "success"
                    })
                    
                except Exception as e:
                    monitoring_results.append({
                        "keyword": keyword,
                        "search_engine": search_engine,
                        "status": "failed",
                        "error": str(e)
                    })
        
        # 更新项目统计信息
        await self._update_project_statistics(project)
        
        # 更新监控时间
        project.last_monitored_at = datetime.utcnow()
        project.next_monitor_at = self._calculate_next_monitor_time(project.monitoring_frequency.value)
        project.updated_at = datetime.utcnow()
        
        await self.db.commit()
        
        # 生成告警
        await self._generate_alerts(project, monitoring_results)
        
        return {
            "status": "completed",
            "project_id": str(project.id),
            "monitored_at": project.last_monitored_at,
            "next_monitor_at": project.next_monitor_at,
            "results": monitoring_results
        }

    async def _build_project_response(self, project: MonitoringProject) -> MonitoringProjectResponse:
        """构建项目响应数据"""
        return MonitoringProjectResponse(
            id=str(project.id),
            project_name=project.project_name,
            project_description=project.project_description,
            target_website=project.target_website,
            target_brand=project.target_brand,
            keywords=project.keywords,
            search_engines=project.search_engines,
            monitoring_frequency=project.monitoring_frequency.value,
            competitors=project.competitors,
            status=project.status.value,
            total_keywords=project.total_keywords,
            total_rankings=project.total_rankings,
            avg_ranking=project.avg_ranking,
            best_ranking=project.best_ranking,
            created_at=project.created_at,
            updated_at=project.updated_at,
            last_monitored_at=project.last_monitored_at,
            next_monitor_at=project.next_monitor_at
        )

    async def update_monitoring_project(self, user_id: str, project_id: str, request: MonitoringProjectCreate) -> MonitoringProjectResponse:
        """更新监控项目"""
        # 获取项目并验证权限
        project = await self._get_project_with_permission(project_id, user_id)

        # 检查项目名称是否重复（排除当前项目）
        if request.project_name != project.project_name:
            existing_project = await self.db.execute(
                select(MonitoringProject).where(
                    and_(
                        MonitoringProject.user_id == user_id,
                        MonitoringProject.project_name == request.project_name,
                        MonitoringProject.id != project_id
                    )
                )
            )
            if existing_project.scalar_one_or_none():
                raise ValidationError("项目名称已存在")

        # 更新项目信息
        project.project_name = request.project_name
        project.project_description = request.project_description
        project.target_website = request.target_website
        project.target_brand = request.target_brand
        project.keywords = request.keywords
        project.search_engines = request.search_engines

        # 转换监控频率为枚举类型
        from app.models.monitoring import MonitoringFrequency
        old_frequency = project.monitoring_frequency.value
        project.monitoring_frequency = MonitoringFrequency(request.monitoring_frequency)

        project.competitors = request.competitors
        project.total_keywords = len(request.keywords)
        project.updated_at = datetime.utcnow()

        # 如果监控频率改变，重新计算下次监控时间
        if old_frequency != request.monitoring_frequency:
            project.next_monitor_at = self._calculate_next_monitor_time(request.monitoring_frequency)

        await self.db.commit()
        await self.db.refresh(project)

        return await self._build_project_response(project)

    async def _get_project_with_permission(self, project_id: str, user_id: str) -> MonitoringProject:
        """获取项目并验证权限"""
        result = await self.db.execute(
            select(MonitoringProject).where(
                and_(MonitoringProject.id == project_id, MonitoringProject.user_id == user_id)
            )
        )
        project = result.scalar_one_or_none()
        if not project:
            raise NotFoundError("监控项目不存在或无权限访问")
        return project

    def _calculate_next_monitor_time(self, frequency: str) -> datetime:
        """计算下次监控时间"""
        now = datetime.utcnow()
        if frequency == "daily":
            return now + timedelta(days=1)
        elif frequency == "weekly":
            return now + timedelta(weeks=1)
        elif frequency == "monthly":
            return now + timedelta(days=30)
        else:
            return now + timedelta(days=1)

    async def _schedule_monitoring_task(self, project: MonitoringProject):
        """安排监控任务"""
        # TODO: 集成任务队列（Celery）来安排定时监控任务
        # 这里模拟创建一些初始排名数据
        await self._create_sample_ranking_data(project)

    async def _create_sample_ranking_data(self, project: MonitoringProject):
        """创建示例排名数据"""
        # 为每个关键词和搜索引擎创建示例数据
        for keyword in project.keywords[:3]:  # 限制前3个关键词
            for search_engine in project.search_engines[:2]:  # 限制前2个搜索引擎
                ranking_data = await self._perform_search_ranking(
                    keyword, search_engine, project.target_website, project.competitors
                )

                ranking_record = RankingRecord(
                    project_id=project.id,
                    keyword=keyword,
                    search_engine=search_engine,
                    search_query=ranking_data.get('search_query'),
                    ranking_position=ranking_data.get('ranking_position'),
                    total_results=ranking_data.get('total_results'),
                    result_title=ranking_data.get('result_title'),
                    result_url=ranking_data.get('result_url'),
                    result_snippet=ranking_data.get('result_snippet'),
                    result_score=ranking_data.get('result_score'),
                    competitor_rankings=ranking_data.get('competitor_rankings'),
                    search_metadata=ranking_data.get('metadata')
                )

                self.db.add(ranking_record)

        await self.db.commit()

        # 更新项目统计
        await self._update_project_statistics(project)
        await self.db.commit()

    async def _perform_search_ranking(self, keyword: str, search_engine: str, target_website: str, competitors: List[Dict]) -> Dict[str, Any]:
        """执行搜索排名检测"""
        # TODO: 集成AI搜索引擎API来获取实际排名数据
        # 这里返回模拟数据
        ranking_position = random.randint(1, 20) if random.random() > 0.3 else None

        return {
            "search_query": f"{keyword} {search_engine}搜索",
            "ranking_position": ranking_position,
            "total_results": random.randint(1000, 10000),
            "result_title": f"{keyword}相关结果标题",
            "result_url": f"https://{target_website}/page",
            "result_snippet": f"关于{keyword}的搜索结果摘要...",
            "result_score": round(random.uniform(0.7, 0.95), 4),
            "competitor_rankings": {
                comp.get('name', 'competitor'): random.randint(1, 20)
                for comp in (competitors or [])
            },
            "metadata": {
                "search_time": datetime.utcnow().isoformat(),
                "search_engine": search_engine
            }
        }

    async def _update_project_statistics(self, project: MonitoringProject):
        """更新项目统计信息"""
        # 计算平均排名
        avg_ranking_result = await self.db.execute(
            select(func.avg(RankingRecord.ranking_position))
            .where(
                and_(
                    RankingRecord.project_id == project.id,
                    RankingRecord.ranking_position.isnot(None)
                )
            )
        )
        avg_ranking = avg_ranking_result.scalar()

        # 计算最佳排名
        best_ranking_result = await self.db.execute(
            select(func.min(RankingRecord.ranking_position))
            .where(
                and_(
                    RankingRecord.project_id == project.id,
                    RankingRecord.ranking_position.isnot(None)
                )
            )
        )
        best_ranking = best_ranking_result.scalar()

        # 计算总排名记录数
        total_rankings_result = await self.db.execute(
            select(func.count(RankingRecord.id))
            .where(RankingRecord.project_id == project.id)
        )
        total_rankings = total_rankings_result.scalar()

        # 更新项目统计
        project.avg_ranking = avg_ranking
        project.best_ranking = best_ranking
        project.total_rankings = total_rankings

    async def _generate_alerts(self, project: MonitoringProject, monitoring_results: List[Dict]):
        """生成监控告警"""
        # TODO: 实现告警逻辑
        # 比如排名下降、新竞争对手出现等
        pass

    async def _get_keyword_trends(self, project_id: str, query: RankingQuery) -> List[KeywordTrendResponse]:
        """获取关键词趋势数据"""
        # TODO: 实现趋势数据查询
        return []

    async def _get_ranking_statistics(self, project_id: str, query: RankingQuery) -> ProjectStatisticsResponse:
        """获取排名统计数据"""
        # 获取基本统计
        total_rankings_result = await self.db.execute(
            select(func.count(RankingRecord.id))
            .where(RankingRecord.project_id == project_id)
        )
        total_rankings = total_rankings_result.scalar() or 0

        # 获取关键词数量
        keywords_result = await self.db.execute(
            select(func.count(func.distinct(RankingRecord.keyword)))
            .where(RankingRecord.project_id == project_id)
        )
        total_keywords = keywords_result.scalar() or 0

        # 获取平均排名
        avg_ranking_result = await self.db.execute(
            select(func.avg(RankingRecord.ranking_position))
            .where(
                and_(
                    RankingRecord.project_id == project_id,
                    RankingRecord.ranking_position.isnot(None)
                )
            )
        )
        avg_ranking = avg_ranking_result.scalar()

        # 获取最佳排名
        best_ranking_result = await self.db.execute(
            select(func.min(RankingRecord.ranking_position))
            .where(
                and_(
                    RankingRecord.project_id == project_id,
                    RankingRecord.ranking_position.isnot(None)
                )
            )
        )
        best_ranking = best_ranking_result.scalar()

        return ProjectStatisticsResponse(
            total_keywords=total_keywords,
            total_rankings=total_rankings,
            avg_ranking=avg_ranking,
            best_ranking=best_ranking,
            ranking_distribution={},
            engine_performance={},
            trend_summary={},
            recent_changes=[]
        )

    async def _get_project_statistics(self, user_id: str) -> Dict[str, Any]:
        """获取用户项目统计"""
        total_projects_result = await self.db.execute(
            select(func.count(MonitoringProject.id)).where(MonitoringProject.user_id == user_id)
        )
        total_projects = total_projects_result.scalar() or 0

        active_projects_result = await self.db.execute(
            select(func.count(MonitoringProject.id)).where(
                and_(MonitoringProject.user_id == user_id, MonitoringProject.status == "active")
            )
        )
        active_projects = active_projects_result.scalar() or 0

        # 获取总关键词数
        total_keywords_result = await self.db.execute(
            select(func.sum(MonitoringProject.total_keywords))
            .where(MonitoringProject.user_id == user_id)
        )
        total_keywords = total_keywords_result.scalar() or 0

        return {
            "total_projects": total_projects,
            "active_projects": active_projects,
            "paused_projects": total_projects - active_projects,
            "total_keywords": total_keywords
        }
