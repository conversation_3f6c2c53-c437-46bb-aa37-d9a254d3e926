"""
基础AI适配器
"""
import time
import asyncio
from abc import ABC, abstractmethod
from typing import Optional, Dict, Any
from dataclasses import dataclass
from enum import Enum

from ....core.geo.config import AIModelType, AIModelConfig
from ....core.geo.exceptions import AIAPIEx<PERSON>, TimeoutException

@dataclass
class AIResponse:
    """AI响应数据类"""
    content: str
    model: str
    usage: Optional[Dict[str, Any]] = None
    response_time: Optional[float] = None
    success: bool = True
    error: Optional[str] = None

class BaseAIAdapter(ABC):
    """基础AI适配器抽象类"""
    
    def __init__(self, config: AIModelConfig):
        self.config = config
        self.model_type = None  # 子类需要设置
    
    @abstractmethod
    async def chat_completion(
        self,
        messages: list,
        temperature: float = None,
        max_tokens: int = None,
        **kwargs
    ) -> AIResponse:
        """聊天完成接口"""
        pass
    
    async def analyze_with_prompt(
        self,
        system_prompt: str,
        user_prompt: str,
        temperature: float = None,
        max_tokens: int = None
    ) -> AIResponse:
        """使用提示词进行分析"""
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]
        
        start_time = time.time()
        try:
            response = await self.chat_completion(
                messages=messages,
                temperature=temperature or self.config.temperature,
                max_tokens=max_tokens or self.config.max_tokens
            )
            response.response_time = time.time() - start_time
            return response
            
        except asyncio.TimeoutError:
            raise TimeoutException(
                operation=f"{self.model_type}_chat_completion",
                timeout_seconds=self.config.timeout
            )
        except Exception as e:
            raise AIAPIException(
                model_type=str(self.model_type),
                api_error=str(e)
            )
    
    def _build_headers(self) -> Dict[str, str]:
        """构建请求头"""
        return {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.config.api_key}"
        }
    
    def _build_request_data(
        self,
        messages: list,
        temperature: float,
        max_tokens: int,
        **kwargs
    ) -> Dict[str, Any]:
        """构建请求数据"""
        return {
            "model": self.config.model_id,
            "messages": messages,
            "temperature": temperature,
            "max_tokens": max_tokens,
            **kwargs
        }
    
    def _parse_response(self, response_data: Dict[str, Any]) -> AIResponse:
        """解析响应数据"""
        try:
            content = response_data["choices"][0]["message"]["content"]
            usage = response_data.get("usage", {})
            
            return AIResponse(
                content=content,
                model=self.config.model_id,
                usage=usage,
                success=True
            )
        except (KeyError, IndexError) as e:
            raise AIAPIException(
                model_type=str(self.model_type),
                api_error=f"响应解析失败: {str(e)}"
            )
    
    async def test_connection(self) -> bool:
        """测试连接"""
        try:
            test_response = await self.analyze_with_prompt(
                system_prompt="你是一个测试助手。",
                user_prompt="请回复'连接正常'",
                max_tokens=10
            )
            return test_response.success
        except Exception:
            return False
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            "model_type": str(self.model_type),
            "model_id": self.config.model_id,
            "model_name": self.config.model_name,
            "enabled": self.config.enabled,
            "max_tokens": self.config.max_tokens,
            "temperature": self.config.temperature
        }

class AIAdapterFactory:
    """AI适配器工厂"""

    _adapters = {}

    @classmethod
    def register_adapter(cls, model_type: AIModelType, adapter_class):
        """注册适配器"""
        cls._adapters[model_type] = adapter_class

    @classmethod
    def create_adapter(cls, config: AIModelConfig) -> BaseAIAdapter:
        """创建适配器实例"""
        # 从配置中获取模型类型 - 优先使用model_id来推断类型
        model_id_lower = config.model_id.lower()

        # 根据model_id推断模型类型
        for model_type in AIModelType:
            # 检查model_id是否包含模型类型标识
            if (model_type.value in model_id_lower or
                model_type.value == config.model_name.lower() or
                str(model_type).split('.')[-1].lower() in config.model_name.lower()):
                if model_type in cls._adapters:
                    adapter_class = cls._adapters[model_type]
                    return adapter_class(config)

        raise ValueError(f"不支持的AI模型类型: {config.model_name} (model_id: {config.model_id})")

    @classmethod
    def get_supported_models(cls) -> list:
        """获取支持的模型列表"""
        return list(cls._adapters.keys())

# 全局AI适配器工厂实例
ai_adapter_factory = AIAdapterFactory()
