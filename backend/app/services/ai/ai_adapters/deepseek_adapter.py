"""
DeepSeek AI适配器
"""
import aiohttp
import asyncio
from typing import Dict, Any
from ....core.geo.config import AIModelType
from .base_adapter import BaseAIAdapter, AIResponse

class DeepSeekAdapter(BaseAIAdapter):
    """DeepSeek AI适配器"""
    
    def __init__(self, config):
        super().__init__(config)
        self.model_type = AIModelType.DEEPSEEK
    
    async def chat_completion(
        self,
        messages: list,
        temperature: float = None,
        max_tokens: int = None,
        **kwargs
    ) -> AIResponse:
        """DeepSeek聊天完成接口"""
        
        # 构建请求数据
        request_data = self._build_request_data(
            messages=messages,
            temperature=temperature or self.config.temperature,
            max_tokens=max_tokens or self.config.max_tokens,
            **kwargs
        )
        
        # 构建请求头
        headers = self._build_headers()
        
        try:
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.config.timeout)) as session:
                async with session.post(
                    f"{self.config.base_url}/chat/completions",
                    json=request_data,
                    headers=headers
                ) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        return AIResponse(
                            content="",
                            model=self.config.model_id,
                            success=False,
                            error=f"DeepSeek API错误 {response.status}: {error_text}"
                        )
                    
                    response_data = await response.json()
                    return self._parse_response(response_data)
                    
        except asyncio.TimeoutError:
            return AIResponse(
                content="",
                model=self.config.model_id,
                success=False,
                error="DeepSeek API请求超时"
            )
        except Exception as e:
            return AIResponse(
                content="",
                model=self.config.model_id,
                success=False,
                error=f"DeepSeek API请求失败: {str(e)}"
            )
    
    def _build_request_data(
        self,
        messages: list,
        temperature: float,
        max_tokens: int,
        **kwargs
    ) -> Dict[str, Any]:
        """构建DeepSeek请求数据"""
        return {
            "model": self.config.model_id,
            "messages": messages,
            "temperature": temperature,
            "max_tokens": max_tokens,
            "stream": False
        }
    
    def _parse_response(self, response_data: Dict[str, Any]) -> AIResponse:
        """解析DeepSeek响应"""
        try:
            choice = response_data["choices"][0]
            content = choice["message"]["content"]
            usage = response_data.get("usage", {})
            
            return AIResponse(
                content=content,
                model=self.config.model_id,
                usage=usage,
                success=True
            )
        except (KeyError, IndexError) as e:
            return AIResponse(
                content="",
                model=self.config.model_id,
                success=False,
                error=f"DeepSeek响应解析失败: {str(e)}"
            )
