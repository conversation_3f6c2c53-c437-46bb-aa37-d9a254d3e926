"""
AI适配器包
"""

from ....core.geo.config import AIModelType
from .base_adapter import Base<PERSON><PERSON>dapter, AIResponse, ai_adapter_factory
from .doubao_adapter import DoubaoAdapter
from .kimi_adapter import KimiAdapter
from .deepseek_adapter import DeepSeekAdapter
from .gpt_adapter import GPTAdapter
from .grok_adapter import GrokAdapter
from .perplexity_adapter import PerplexityAdapter

# 注册所有适配器
ai_adapter_factory.register_adapter(AIModelType.DOUBAO, DoubaoAdapter)
ai_adapter_factory.register_adapter(AIModelType.KIMI, KimiAdapter)
ai_adapter_factory.register_adapter(AIModelType.DEEPSEEK, DeepSeekAdapter)
ai_adapter_factory.register_adapter(AIModelType.GPT, GPTAdapter)
ai_adapter_factory.register_adapter(AIModelType.GROK, GrokAdapter)
ai_adapter_factory.register_adapter(AIModelType.PERPLEXITY, PerplexityAdapter)

__all__ = [
    "BaseAIAdapter",
    "AIResponse",
    "AIModelType",
    "ai_adapter_factory",
    "DoubaoAdapter",
    "KimiAdapter",
    "DeepSeekAdapter",
    "GPTAdapter",
    "GrokAdapter",
    "PerplexityAdapter"
]
