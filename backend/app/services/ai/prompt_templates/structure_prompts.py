"""
数据结构化提示词模板
"""
from .base_prompts import prompt_manager, PromptType

# 平台匹配提示词
PLATFORM_MATCH_PROMPT = """
以下是我方平台资源信息：
{platform_resources}

客户关键词搜索内容：{keyword}

你是一个专业的AI搜索引擎优化大师，请帮我分析哪个平台比较合适。
请将这些平台按照适合程度排序，并输出以下几个维度的分值：

请按照以下JSON格式输出：
{{
    "platform_recommendations": [
        {{
            "platform_name": "平台名称",
            "rank": 1,
            "scores": {{
                "relevance": 85,
                "cost_effectiveness": 90,
                "inclusion_rate": 80,
                "competition": 70,
                "traffic_potential": 85
            }},
            "total_score": 82,
            "reason": "推荐理由",
            "posting_restrictions": "发帖限制",
            "price_range": "价格范围",
            "inclusion_rate": "收录率"
        }}
    ]
}}
"""

# 账号匹配提示词
ACCOUNT_MATCH_PROMPT = """
以下是该平台的相关账号信息：
{account_info}

客户关键词搜索内容：{keyword}

请根据该客户的关键词搜索内容进行匹配和排序，并输出结构化的多维度评价体系：

请按照以下JSON格式输出：
{{
    "account_recommendations": [
        {{
            "account_name": "账号名称",
            "platform": "所属平台",
            "rank": 1,
            "scores": {{
                "relevance": 90,
                "authority": 85,
                "engagement": 80,
                "cost_performance": 75,
                "audience_match": 88
            }},
            "total_score": 84,
            "followers": "粉丝数量",
            "industry": "所属行业",
            "engagement_rate": "互动率",
            "price": "合作价格",
            "reason": "推荐理由"
        }}
    ]
}}
"""

# 注册提示词模板
prompt_manager.register_template(PromptType.PLATFORM_MATCH, PLATFORM_MATCH_PROMPT)
prompt_manager.register_template(PromptType.ACCOUNT_MATCH, ACCOUNT_MATCH_PROMPT)
