"""
分析类提示词模板
"""
from .base_prompts import prompt_manager, PromptType

# 基础搜索提示词（仅附加来源要求，不做分析）
BASIC_SEARCH_PROMPT = """
{keyword}
"""

# 平台识别提示词（从信源中提取结构化信息）
PLATFORM_IDENTIFY_PROMPT = """
请根据以下信源内容，识别其中的网站平台信息，去除无用的结构，返回重点名称和网址的结构化信息：

信源内容：
{source_content}

请按照以下JSON格式输出：
{{
    "platforms": [
        {{
            "name": "平台名称",
            "url": "网站地址",
            "type": "平台类型",
            "description": "平台描述"
        }}
    ]
}}
"""

# 关键词排名分析提示词
KEYWORD_RANKING_PROMPT = """
请根据以上的内容，找寻以下关键词在其中的排名情况：

目标关键词：{keywords}
分析内容：{content}

请输出以下结构化格式：
{{
    "keyword_rankings": [
        {{
            "keyword": "关键词",
            "rank": "排名位置"
        }}
    ]
}}
"""

# 注册提示词模板
prompt_manager.register_template(PromptType.BASIC_SEARCH, BASIC_SEARCH_PROMPT)
prompt_manager.register_template(PromptType.PLATFORM_IDENTIFY, PLATFORM_IDENTIFY_PROMPT)
prompt_manager.register_template(PromptType.KEYWORD_RANKING, KEYWORD_RANKING_PROMPT)
