"""
基础提示词模板系统
"""
from typing import Dict, Any, Optional
from enum import Enum

class PromptType(Enum):
    """提示词类型枚举"""
    BASIC_SEARCH = "basic_search"            # 基础搜索（仅附加来源要求）
    PLATFORM_IDENTIFY = "platform_identify"  # 平台识别（从信源中提取）
    KEYWORD_RANKING = "keyword_ranking"      # 关键词排名分析
    PLATFORM_MATCH = "platform_match"       # 平台匹配推荐
    ACCOUNT_MATCH = "account_match"          # 账号匹配推荐

class BasePromptTemplate:
    """基础提示词模板类"""

    def __init__(self):
        self.source_request_suffix = "请附带信息来源地址"

    def build_prompt(self, template: str, add_source_request: bool = False, **kwargs) -> str:
        """构建完整提示词"""
        # 仅在需要时附加信息来源请求（主要用于AI关键词分析）
        if add_source_request and self.source_request_suffix not in template:
            template += f"\n\n{self.source_request_suffix}"

        # 参数替换
        return template.format(**kwargs)

    def get_template(self, prompt_type: PromptType) -> str:
        """获取指定类型的提示词模板"""
        raise NotImplementedError

class GeoPromptManager:
    """Geo提示词管理器"""

    def __init__(self):
        self.templates = {}
        self.base_template = BasePromptTemplate()

    def register_template(self, prompt_type: PromptType, template: str):
        """注册提示词模板"""
        self.templates[prompt_type] = template

    def get_prompt(self, prompt_type: PromptType, add_source_request: bool = False, **kwargs) -> str:
        """获取构建好的提示词"""
        if prompt_type not in self.templates:
            raise ValueError(f"未找到提示词模板: {prompt_type}")

        template = self.templates[prompt_type]
        return self.base_template.build_prompt(template, add_source_request=add_source_request, **kwargs)

    def get_ai_analysis_prompt(self, prompt_type: PromptType, **kwargs) -> str:
        """获取AI关键词分析提示词（自动附加信息来源请求）"""
        return self.get_prompt(prompt_type, add_source_request=True, **kwargs)

# 全局提示词管理器实例
prompt_manager = GeoPromptManager()
