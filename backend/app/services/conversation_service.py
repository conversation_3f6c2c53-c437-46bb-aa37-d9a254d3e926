from sqlalchemy.ext.asyncio import AsyncSession
from typing import Dict, List, Optional, AsyncGenerator, Any
from datetime import datetime
import uuid
import logging
import asyncio

from app.repositories.conversation_repository import ConversationRepository
from app.models.knowledge import AIConversation, MessageType
from app.schemas.ai_service import ContentGenerationRequest
from app.schemas.conversation import (
    ConversationCreateRequest, ConversationResponse, 
    MessageSendRequest, ConversationDetailResponse,
    MessageResponse, ConversationStatistics
)
from app.exceptions import NotFoundError, PermissionError, ValidationError

logger = logging.getLogger(__name__)


class ConversationService:
    """对话管理服务"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.conversation_repo = ConversationRepository(db)
        self.ai_service = None  # 延迟初始化避免循环依赖
    
    async def create_conversation(self, user_id: str, request: ConversationCreateRequest) -> ConversationResponse:
        """创建新对话"""
        try:
            conversation_data = {
                "user_id": user_id,
                "title": request.title,
                "template_id": request.template_id,
                "template_parameters": request.template_parameters,
                "knowledge_bases": request.knowledge_bases,
                "message_count": 0
            }
            
            conversation = await self.conversation_repo.create_conversation(conversation_data)
            
            return ConversationResponse(
                id=str(conversation.id),
                title=conversation.title,
                template_id=str(conversation.template_id) if conversation.template_id else None,
                message_count=conversation.message_count,
                created_at=conversation.created_at,
                updated_at=conversation.updated_at
            )
            
        except Exception as e:
            logger.error(f"创建对话失败: {e}")
            raise ValidationError(f"创建对话失败: {str(e)}")
    
    async def get_conversation(self, conversation_id: str, user_id: str) -> ConversationDetailResponse:
        """获取对话详情"""
        conversation = await self.conversation_repo.get_conversation_by_id(conversation_id, user_id)
        if not conversation:
            raise NotFoundError("对话不存在或无权限访问")
        
        # 获取消息历史
        messages = await self.conversation_repo.get_conversation_messages(conversation_id)
        
        message_responses = [
            MessageResponse(
                id=str(msg.id),
                conversation_id=str(msg.conversation_id),
                message_type=msg.message_type.value,
                content=msg.content,
                request_id=str(msg.request_id) if msg.request_id else None,
                created_at=msg.created_at
            )
            for msg in messages
        ]
        
        return ConversationDetailResponse(
            id=str(conversation.id),
            title=conversation.title,
            template_id=str(conversation.template_id) if conversation.template_id else None,
            template_parameters=conversation.template_parameters,
            knowledge_bases=conversation.knowledge_bases,
            context_summary=conversation.context_summary,
            message_count=conversation.message_count,
            messages=message_responses,
            created_at=conversation.created_at,
            updated_at=conversation.updated_at
        )
    
    async def get_user_conversations(self, user_id: str, business_type: Optional[str] = None, limit: int = 20, offset: int = 0) -> List[ConversationResponse]:
        """获取用户对话列表"""
        conversations = await self.conversation_repo.get_user_conversations(user_id, business_type, limit, offset)
        
        return [
            ConversationResponse(
                id=str(conv.id),
                title=conv.title,
                template_id=str(conv.template_id) if conv.template_id else None,
                message_count=conv.message_count,
                created_at=conv.created_at,
                updated_at=conv.updated_at
            )
            for conv in conversations
        ]
    
    async def delete_conversation(self, conversation_id: str, user_id: str) -> bool:
        """删除对话"""
        # 验证权限
        conversation = await self.conversation_repo.get_conversation_by_id(conversation_id, user_id)
        if not conversation:
            raise NotFoundError("对话不存在或无权限访问")
        
        return await self.conversation_repo.delete_conversation(conversation_id, user_id)
    
    async def add_message_and_generate_response(self, conversation_id: str, 
                                              request: MessageSendRequest,
                                              user_id: str) -> AsyncGenerator[Dict[str, Any], None]:
        """添加用户消息并生成AI回复"""
        # 1. 验证对话权限
        conversation = await self.conversation_repo.get_conversation_by_id(conversation_id, user_id)
        if not conversation:
            raise NotFoundError("对话不存在或无权限访问")
        
        try:
            # 2. 添加用户消息
            user_message = await self.conversation_repo.add_message({
                "conversation_id": conversation_id,
                "message_type": MessageType.USER,
                "content": request.content
            })
            
            # 3. 构建AI请求
            ai_request = self._build_ai_request_from_conversation(conversation, request.content)
            
            # 4. 延迟初始化AI服务
            if not self.ai_service:
                from app.services.ai_service import AIService
                self.ai_service = AIService(self.db)
            
            # 5. 流式生成AI回复
            generated_content = ""
            ai_request_id = None
            
            async for chunk in self.ai_service.generate_content_stream_v2(user_id, ai_request):
                if chunk.get('type') == 'content':
                    generated_content += chunk.get('content', '')
                elif chunk.get('type') == 'final':
                    ai_request_id = chunk.get('request_id')
                
                # 添加对话ID到响应
                chunk['conversation_id'] = conversation_id
                yield chunk
            
            # 6. 保存AI回复
            if generated_content:
                await self.conversation_repo.add_message({
                    "conversation_id": conversation_id,
                    "message_type": MessageType.ASSISTANT,
                    "content": generated_content,
                    "request_id": ai_request_id
                })
                
                # 7. 更新上下文摘要（异步执行，不阻塞响应）
                asyncio.create_task(self.update_context_summary(conversation_id))
                
        except Exception as e:
            logger.error(f"生成回复失败: {e}")
            yield {
                'type': 'error',
                'error': str(e),
                'conversation_id': conversation_id
            }
    
    def _build_ai_request_from_conversation(self, conversation: AIConversation, user_message: str) -> ContentGenerationRequest:
        """从对话构建AI请求"""
        return ContentGenerationRequest(
            content_type="article",  # 默认类型，可以根据对话配置调整
            topic=user_message,
            keywords=[],  # 可以从消息中提取关键词
            conversation_id=str(conversation.id),
            template_id=str(conversation.template_id) if conversation.template_id else None,
            template_parameters=conversation.template_parameters,
            knowledge_bases=conversation.knowledge_bases,
            is_first_request=conversation.message_count <= 1,  # 判断是否首次请求
            ai_model="doubao"
        )
    
    async def update_context_summary(self, conversation_id: str):
        """更新对话上下文摘要"""
        try:
            messages = await self.conversation_repo.get_conversation_messages(conversation_id, limit=20)
            
            if len(messages) < 5:  # 消息太少，不需要摘要
                return
            
            # 构建摘要提示词
            messages_text = "\n".join([
                f"{'用户' if msg.message_type == MessageType.USER else '助手'}：{msg.content[:200]}..."
                for msg in messages[-10:]  # 只用最近10条消息生成摘要
            ])
            
            summary_prompt = f"""
请为以下对话生成简洁摘要，重点关注：
1. 用户的主要需求和目标
2. 已生成内容的特点和方向
3. 用户的反馈和调整要求
4. 当前讨论的重点话题

对话内容：
{messages_text}

请生成150字以内的结构化摘要：
"""
            
            # 延迟初始化AI服务
            if not self.ai_service:
                from app.services.ai_service import AIService
                self.ai_service = AIService(self.db)
            
            # 调用AI生成摘要
            summary_response = await self.ai_service.generate_simple_completion(summary_prompt)
            summary = summary_response.get('content', '').strip()
            
            # 更新对话摘要
            await self.conversation_repo.update_conversation_summary(conversation_id, summary)
            
        except Exception as e:
            logger.error(f"更新对话摘要失败: {e}")
    
    async def get_conversation_statistics(self, conversation_id: str, user_id: str) -> ConversationStatistics:
        """获取对话统计信息"""
        # 验证权限
        conversation = await self.conversation_repo.get_conversation_by_id(conversation_id, user_id)
        if not conversation:
            raise NotFoundError("对话不存在或无权限访问")
        
        stats = await self.conversation_repo.get_conversation_statistics(conversation_id)
        
        return ConversationStatistics(
            total_messages=stats['total_messages'],
            user_messages=stats['user_messages'],
            assistant_messages=stats['assistant_messages'],
            last_activity=conversation.updated_at
        )
    
    async def search_conversations(self, user_id: str, query: str, business_type: Optional[str] = None, limit: int = 10) -> List[ConversationResponse]:
        """搜索用户对话"""
        # 这里可以实现更复杂的搜索逻辑
        # 暂时使用简单的标题匹配
        conversations = await self.conversation_repo.get_user_conversations(user_id, business_type, limit=100)
        
        # 简单的文本匹配
        filtered_conversations = [
            conv for conv in conversations 
            if query.lower() in conv.title.lower()
        ][:limit]
        
        return [
            ConversationResponse(
                id=str(conv.id),
                title=conv.title,
                template_id=str(conv.template_id) if conv.template_id else None,
                message_count=conv.message_count,
                created_at=conv.created_at,
                updated_at=conv.updated_at
            )
            for conv in filtered_conversations
        ]
