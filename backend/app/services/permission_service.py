"""
权限管理服务（简化版本）
"""
from typing import List, Optional, Dict, Set
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import and_, or_, select
from fastapi import HTTPException, status

from app.models.permission import Permission, Role, RolePermission, UserRole
from app.models.user import User


class PermissionService:
    """权限管理服务（简化版本，用于快速部署）"""

    def __init__(self, db: AsyncSession):
        self.db = db

    def has_permission(self, user_id: str, permission_code: str) -> bool:
        """
        检查用户是否有指定权限

        Args:
            user_id: 用户ID
            permission_code: 权限代码，如 'users.list.view'

        Returns:
            bool: 是否有权限
        """
        try:
            # 使用同步查询（因为依赖项需要同步函数）
            from sqlalchemy import create_engine, text
            from app.config import settings

            # 创建同步引擎
            sync_engine = create_engine(settings.database_url.replace("+asyncpg", ""))

            with sync_engine.connect() as conn:
                # 查询用户的所有激活角色
                user_roles_query = text("""
                    SELECT ur.role_id
                    FROM user_roles ur
                    WHERE ur.user_id = :user_id AND ur.role_status = 'active'
                """)
                user_roles_result = conn.execute(user_roles_query, {"user_id": user_id})
                role_ids = [row[0] for row in user_roles_result]

                if not role_ids:
                    return False

                # 查询这些角色是否有指定权限
                # 使用 ANY 操作符来处理角色ID列表，更安全和高效
                permission_query = text("""
                    SELECT COUNT(*)
                    FROM role_permissions rp
                    JOIN permissions p ON rp.permission_id = p.id
                    WHERE rp.role_id = ANY(:role_ids) AND p.permission_code = :permission_code
                """)
                permission_result = conn.execute(permission_query, {
                    "role_ids": role_ids,
                    "permission_code": permission_code
                })
                count = permission_result.scalar()

                return count > 0

        except Exception as e:
            print(f"检查权限时出错: {e}")
            # 出错时默认拒绝访问
            return False

    async def has_permission_async(self, user_id: str, permission_code: str) -> bool:
        """异步检查用户是否有指定权限"""
        try:
            # 获取用户的活跃角色
            user_roles_query = select(UserRole.role_id).where(
                and_(
                    UserRole.user_id == user_id,
                    UserRole.role_status == "active"
                )
            )
            user_roles_result = await self.db.execute(user_roles_query)
            role_ids = [str(row[0]) for row in user_roles_result.fetchall()]

            if not role_ids:
                return False

            # 查询这些角色是否有指定权限
            from sqlalchemy import text
            # 使用 ANY 操作符来处理角色ID列表，更安全和高效
            permission_query = text("""
                SELECT COUNT(*)
                FROM role_permissions rp
                JOIN permissions p ON rp.permission_id = p.id
                WHERE rp.role_id = ANY(:role_ids) AND p.permission_code = :permission_code
            """)
            permission_result = await self.db.execute(permission_query, {
                "role_ids": role_ids,
                "permission_code": permission_code
            })

            count = permission_result.scalar()
            return count > 0

        except Exception as e:
            print(f"检查权限时出错: {e}")
            return False

    def get_user_permissions(self, user_id: str) -> List[str]:
        """
        获取用户所有权限代码列表

        Args:
            user_id: 用户ID

        Returns:
            List[str]: 权限代码列表
        """
        try:
            from sqlalchemy import create_engine, text
            from app.config import settings

            # 创建同步引擎
            sync_engine = create_engine(settings.database_url.replace("+asyncpg", ""))

            with sync_engine.connect() as conn:
                # 查询用户的所有权限
                permissions_query = text("""
                    SELECT DISTINCT p.permission_code
                    FROM user_roles ur
                    JOIN role_permissions rp ON ur.role_id = rp.role_id
                    JOIN permissions p ON rp.permission_id = p.id
                    WHERE ur.user_id = :user_id AND ur.role_status = 'active'
                """)
                permissions_result = conn.execute(permissions_query, {"user_id": user_id})
                permissions = [row[0] for row in permissions_result]

                return permissions

        except Exception as e:
            print(f"获取用户权限时出错: {e}")
            return []

    def get_user_roles(self, user_id: str) -> List[Dict]:
        """
        获取用户角色信息

        Args:
            user_id: 用户ID

        Returns:
            List[Dict]: 角色信息列表
        """
        try:
            from sqlalchemy import create_engine, text
            from app.config import settings

            # 创建同步引擎
            sync_engine = create_engine(settings.database_url.replace("+asyncpg", ""))

            with sync_engine.connect() as conn:
                # 查询用户角色信息
                roles_query = text("""
                    SELECT ur.role_id, r.role_code, r.role_name, ur.role_status, ur.assigned_at
                    FROM user_roles ur
                    JOIN roles r ON ur.role_id = r.id
                    WHERE ur.user_id = :user_id
                """)
                roles_result = conn.execute(roles_query, {"user_id": user_id})

                roles = []
                for row in roles_result:
                    roles.append({
                        "role_id": str(row[0]),
                        "role_code": row[1],
                        "role_name": row[2],
                        "role_status": row[3],
                        "assigned_at": row[4].isoformat() if row[4] else None
                    })

                return roles

        except Exception as e:
            print(f"获取用户角色时出错: {e}")
            return []

    async def assign_role_to_user(self, user_id: str, role_code: str, assigned_by: Optional[str] = None) -> bool:
        """
        为用户分配角色

        Args:
            user_id: 用户ID
            role_code: 角色代码
            assigned_by: 分配人ID

        Returns:
            bool: 是否分配成功
        """
        try:
            # 查找角色
            role_result = await self.db.execute(
                select(Role).where(Role.role_code == role_code)
            )
            role = role_result.scalar_one_or_none()

            if not role:
                print(f"角色不存在: {role_code}")
                return False

            # 检查用户是否已有此角色
            existing_result = await self.db.execute(
                select(UserRole).where(
                    and_(
                        UserRole.user_id == user_id,
                        UserRole.role_id == role.id,
                        UserRole.role_status == "active"
                    )
                )
            )

            if existing_result.scalar_one_or_none():
                print(f"用户已有角色: {role_code}")
                return True

            # 创建用户角色关联
            user_role = UserRole(
                user_id=user_id,
                role_id=role.id,
                role_status="active",
                assigned_by=assigned_by
            )

            self.db.add(user_role)
            await self.db.commit()

            print(f"成功为用户 {user_id} 分配角色: {role_code}")
            return True

        except Exception as e:
            await self.db.rollback()
            print(f"分配角色时出错: {e}")
            return False

    def remove_role_from_user(self, user_id: str, role_code: str) -> bool:
        """
        移除用户角色

        Args:
            user_id: 用户ID
            role_code: 角色代码

        Returns:
            bool: 是否移除成功
        """
        try:
            # 查找角色
            role = self.db.query(Role).filter(Role.role_code == role_code).first()
            if not role:
                return False

            # 查找用户角色关联
            user_role = self.db.query(UserRole).filter(
                and_(
                    UserRole.user_id == user_id,
                    UserRole.role_id == role.id
                )
            ).first()

            if user_role:
                self.db.delete(user_role)
                self.db.commit()
                return True

            return False

        except Exception as e:
            print(f"移除角色时出错: {e}")
            self.db.rollback()
            return False

    def get_role_permissions(self, role_code: str) -> List[str]:
        """
        获取角色的所有权限

        Args:
            role_code: 角色代码

        Returns:
            List[str]: 权限代码列表
        """
        try:
            role = self.db.query(Role).filter(Role.role_code == role_code).first()
            if not role:
                return []

            permissions = self.db.query(Permission).join(RolePermission).filter(
                RolePermission.role_id == role.id
            ).all()

            return [p.permission_code for p in permissions]

        except Exception as e:
            print(f"获取角色权限时出错: {e}")
            return []

    def check_user_has_any_role(self, user_id: str, role_codes: List[str]) -> bool:
        """
        检查用户是否拥有指定角色中的任意一个

        Args:
            user_id: 用户ID
            role_codes: 角色代码列表

        Returns:
            bool: 是否拥有任意一个角色
        """
        try:
            from sqlalchemy import create_engine, text
            from app.config import settings

            # 创建同步引擎
            sync_engine = create_engine(settings.database_url.replace("+asyncpg", ""))

            with sync_engine.connect() as conn:
                # 查询用户是否有指定角色
                role_query = text("""
                    SELECT COUNT(*)
                    FROM user_roles ur
                    JOIN roles r ON ur.role_id = r.id
                    WHERE ur.user_id = :user_id
                    AND ur.role_status = 'active'
                    AND r.role_code = ANY(:role_codes)
                """)
                result = conn.execute(role_query, {
                    "user_id": user_id,
                    "role_codes": role_codes
                })
                count = result.scalar()

                return count > 0

        except Exception as e:
            print(f"检查用户角色时出错: {e}")
            return False

    def is_admin_user(self, user_id: str) -> bool:
        """
        检查用户是否是管理员

        Args:
            user_id: 用户ID

        Returns:
            bool: 是否是管理员
        """
        return self.check_user_has_any_role(user_id, ["super_admin", "admin"])

    def is_enterprise_user(self, user_id: str) -> bool:
        """
        检查用户是否是企业用户

        Args:
            user_id: 用户ID

        Returns:
            bool: 是否是企业用户
        """
        return self.check_user_has_any_role(user_id, ["enterprise_user"])

    def is_channel_user(self, user_id: str) -> bool:
        """
        检查用户是否是渠道商用户

        Args:
            user_id: 用户ID

        Returns:
            bool: 是否是渠道商用户
        """
        return self.check_user_has_any_role(user_id, ["channel_user"])

    def is_agent_user(self, user_id: str) -> bool:
        """
        检查用户是否是代理商用户

        Args:
            user_id: 用户ID

        Returns:
            bool: 是否是代理商用户
        """
        return self.check_user_has_any_role(user_id, ["agent_user"])

    def is_admin_user(self, user_id: str) -> bool:
        """检查是否为管理员用户"""
        return self.check_user_has_any_role(user_id, ["super_admin", "admin"])

    def is_enterprise_user(self, user_id: str) -> bool:
        """检查是否为企业用户"""
        return self.check_user_has_any_role(user_id, ["enterprise_user"])

    def is_channel_user(self, user_id: str) -> bool:
        """检查是否为渠道商用户"""
        return self.check_user_has_any_role(user_id, ["channel_user"])

    def is_agent_user(self, user_id: str) -> bool:
        """检查是否为代理商用户"""
        return self.check_user_has_any_role(user_id, ["agent_user"])

    async def check_user_has_any_role_async(self, user_id: str, role_codes: List[str]) -> bool:
        """
        异步检查用户是否拥有指定角色中的任意一个

        Args:
            user_id: 用户ID
            role_codes: 角色代码列表

        Returns:
            bool: 是否拥有任意一个角色
        """
        try:
            # 查询用户是否有指定角色
            from sqlalchemy import text
            role_codes_str = ','.join([f"'{code}'" for code in role_codes])
            role_query = text(f"""
                SELECT COUNT(*)
                FROM user_roles ur
                JOIN roles r ON ur.role_id = r.id
                WHERE ur.user_id = :user_id
                AND ur.role_status = 'active'
                AND r.role_code IN ({role_codes_str})
            """)
            result = await self.db.execute(role_query, {"user_id": user_id})
            count = result.scalar()

            return count > 0

        except Exception as e:
            print(f"检查用户角色时出错: {e}")
            return False

    async def is_admin_user_async(self, user_id: str) -> bool:
        """异步检查用户是否是管理员"""
        return await self.check_user_has_any_role_async(user_id, ["super_admin", "admin"])

    async def is_enterprise_user_async(self, user_id: str) -> bool:
        """异步检查用户是否是企业用户"""
        return await self.check_user_has_any_role_async(user_id, ["enterprise_user"])

    async def is_channel_user_async(self, user_id: str) -> bool:
        """异步检查用户是否是渠道商用户"""
        return await self.check_user_has_any_role_async(user_id, ["channel_user"])

    async def is_agent_user_async(self, user_id: str) -> bool:
        """异步检查用户是否是代理商用户"""
        return await self.check_user_has_any_role_async(user_id, ["agent_user"])
