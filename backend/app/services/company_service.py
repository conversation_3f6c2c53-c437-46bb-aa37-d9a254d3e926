from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func, desc, asc
from app.models.company import Company, CompanySize, VerificationStatus
from app.models.user import User
from app.schemas.company import *
from app.exceptions import ValidationError, NotFoundError, PermissionError
from typing import Optional, Dict, Any
import secrets
import string
from datetime import datetime

class CompanyService:
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_company_profile(self, user_id: str, request: CompanyProfileRequest) -> CompanyResponse:
        """创建企业信息"""
        # 检查用户是否已有企业信息
        existing_company = await self.db.execute(
            select(Company).where(Company.user_id == user_id)
        )
        if existing_company.scalar_one_or_none():
            raise ValidationError("企业信息已存在，请使用更新接口")
        
        # 检查企业名称是否重复
        name_check = await self.db.execute(
            select(Company).where(Company.company_name == request.company_name)
        )
        if name_check.scalar_one_or_none():
            raise ValidationError("企业名称已存在")
        
        # 使用提供的企业代码
        company_code = request.company_code
        
        # 创建企业信息
        company = Company(
            user_id=user_id,
            company_name=request.company_name,
            company_name_en=request.company_name_en,
            company_code=company_code,
            legal_person=request.legal_person,
            contact_phone=request.contact_phone,
            contact_email=request.contact_email,
            headquarters_location=request.headquarters_location,
            industry=request.industry,
            company_size=request.company_size,
            founded_year=request.founded_year if request.founded_year and request.founded_year > 0 else None,
            business_scope=request.business_scope,
            company_description=request.company_description,
            official_website=str(request.official_website) if request.official_website else None,
            business_license_url=request.business_license_url,
            other_files=request.other_files,
            social_media_links=request.social_media_links,
            submitted_at=datetime.utcnow()
        )
        
        self.db.add(company)
        await self.db.commit()
        await self.db.refresh(company)
        
        return await self._build_company_response(company)
    
    async def get_company_info(self, user_id: str) -> CompanyResponse:
        """获取企业信息"""
        result = await self.db.execute(
            select(Company).where(Company.user_id == user_id)
        )
        company = result.scalar_one_or_none()
        if not company:
            raise NotFoundError("企业信息不存在")
        
        return await self._build_company_response(company)
    
    async def update_company_info(self, user_id: str, request: CompanyUpdateRequest) -> CompanyResponse:
        """更新企业信息"""
        result = await self.db.execute(
            select(Company).where(Company.user_id == user_id)
        )
        company = result.scalar_one_or_none()
        if not company:
            raise NotFoundError("企业信息不存在")
        
        # 记录变更前的数据
        old_data = {}
        new_data = {}
        
        # 更新允许修改的字段
        if request.contact_phone is not None:
            old_data["contact_phone"] = company.contact_phone
            company.contact_phone = request.contact_phone
            new_data["contact_phone"] = request.contact_phone
            
        if request.contact_email is not None:
            old_data["contact_email"] = company.contact_email
            company.contact_email = request.contact_email
            new_data["contact_email"] = request.contact_email
            
        if request.headquarters_location is not None:
            old_data["headquarters_location"] = company.headquarters_location
            company.headquarters_location = request.headquarters_location
            new_data["headquarters_location"] = request.headquarters_location
            
        if request.official_website is not None:
            old_data["official_website"] = company.official_website
            company.official_website = str(request.official_website)
            new_data["official_website"] = str(request.official_website)
            
        if request.business_scope is not None:
            old_data["business_scope"] = company.business_scope
            company.business_scope = request.business_scope
            new_data["business_scope"] = request.business_scope
            
        if request.company_description is not None:
            old_data["company_description"] = company.company_description
            company.company_description = request.company_description
            new_data["company_description"] = request.company_description
        
        company.updated_at = datetime.utcnow()
        
        await self.db.commit()
        
        return await self._build_company_response(company)
    
    async def get_company_list(self, query: CompanyListQuery, admin_id: str) -> Dict[str, Any]:
        """获取企业列表（管理员）"""
        # 构建查询条件
        conditions = []
        
        if query.verification_status:
            conditions.append(Company.verification_status == query.verification_status)
        if query.industry:
            conditions.append(Company.industry == query.industry)
        if query.company_size:
            conditions.append(Company.company_size == query.company_size)
        if query.search:
            search_condition = or_(
                Company.company_name.ilike(f"%{query.search}%"),
                Company.company_name_en.ilike(f"%{query.search}%"),
                Company.legal_person.ilike(f"%{query.search}%"),
                Company.company_code.ilike(f"%{query.search}%")
            )
            conditions.append(search_condition)
        
        # 构建查询
        base_query = select(Company)
        if conditions:
            base_query = base_query.where(and_(*conditions))
        
        # 排序
        if query.sort_order == "desc":
            base_query = base_query.order_by(desc(getattr(Company, query.sort_by)))
        else:
            base_query = base_query.order_by(asc(getattr(Company, query.sort_by)))
        
        # 分页
        offset = (query.page - 1) * query.size
        paginated_query = base_query.offset(offset).limit(query.size)
        
        # 执行查询
        result = await self.db.execute(paginated_query)
        companies = result.scalars().all()
        
        # 获取总数
        count_query = select(func.count(Company.id))
        if conditions:
            count_query = count_query.where(and_(*conditions))
        total_result = await self.db.execute(count_query)
        total = total_result.scalar()
        
        # 构建响应数据
        items = []
        for company in companies:
            company_response = await self._build_company_response(company)
            items.append(company_response)
        
        return {
            "items": items,
            "pagination": {
                "total": total,
                "page": query.page,
                "size": query.size,
                "pages": (total + query.size - 1) // query.size
            }
        }
    
    async def verify_company(self, company_id: str, request: CompanyVerificationRequest, verifier_id: str) -> Dict[str, Any]:
        """企业审核（管理员）"""
        result = await self.db.execute(
            select(Company).where(Company.id == company_id)
        )
        company = result.scalar_one_or_none()
        if not company:
            raise NotFoundError("企业信息不存在")
        
        old_status = company.verification_status
        company.verification_status = request.status
        company.verification_time = datetime.utcnow()
        company.verification_note = request.note
        company.verifier_id = verifier_id
        company.updated_at = datetime.utcnow()
        
        await self.db.commit()

        # 如果审核通过，自动为用户分配企业用户角色
        if request.status == "verified":
            try:
                from app.services.permission_service import PermissionService
                permission_service = PermissionService(self.db)
                await permission_service.assign_role_to_user(
                    user_id=str(company.user_id),
                    role_code="enterprise_user",
                    assigned_by=verifier_id
                )
                print(f"审核通过，已为用户 {company.user_id} 分配企业用户角色")
            except Exception as e:
                print(f"分配企业用户角色失败: {e}")
                # 不影响审核流程，继续执行

        # TODO: 发送通知
        if request.notify_user:
            pass  # await self.notification_service.send_verification_result(company, request)
        
        return {
            "company_id": str(company_id),
            "company_name": company.company_name,
            "old_status": old_status,
            "new_status": request.status,
            "verification_note": request.note,
            "verifier": str(verifier_id),
            "verification_time": company.verification_time
        }
    
    async def _build_company_response(self, company: Company) -> CompanyResponse:
        """构建企业响应数据"""
        return CompanyResponse(
            id=str(company.id),
            company_name=company.company_name,
            company_name_en=company.company_name_en,
            company_code=company.company_code,
            legal_person=company.legal_person,
            contact_phone=company.contact_phone,
            contact_email=company.contact_email,
            headquarters_location=company.headquarters_location,
            industry=company.industry,
            company_size=company.company_size,
            founded_year=company.founded_year,
            business_scope=company.business_scope,
            company_description=company.company_description,
            official_website=company.official_website,
            business_license_url=company.business_license_url,
            other_files=company.other_files,
            social_media_links=company.social_media_links,
            verification_status=company.verification_status,
            verification_time=company.verification_time,
            verification_note=company.verification_note,
            created_at=company.created_at,
            updated_at=company.updated_at,
            submitted_at=company.submitted_at
        )
    
    async def _generate_company_code(self, company_name: str) -> str:
        """生成企业代码"""
        # 提取企业名称的首字母
        import re
        
        # 移除常见的企业后缀
        name_clean = re.sub(r'(有限公司|股份有限公司|集团|公司|企业|科技|技术|信息|网络|Ltd|Co|Inc|Corp|Group|Tech|Technology|Information|Network)$', '', company_name, flags=re.IGNORECASE)
        
        # 提取中文字符
        chinese_chars = re.findall(r'[\u4e00-\u9fff]', name_clean)
        if chinese_chars:
            # 中文企业名称，取前3个字
            code_base = "".join(chinese_chars[:3])
        else:
            # 英文企业名称，取前3个单词的首字母
            words = name_clean.upper().split()
            code_base = "".join(word[0] for word in words[:3] if word)
        
        # 如果代码太短，补充随机字母
        if len(code_base) < 2:
            code_base += ''.join(secrets.choice(string.ascii_uppercase) for _ in range(2 - len(code_base)))
        
        # 添加随机数字
        random_suffix = ''.join(secrets.choice(string.digits) for _ in range(3))
        candidate_code = f"{code_base}{random_suffix}"
        
        # 检查代码是否已存在
        existing_code = await self.db.execute(
            select(Company).where(Company.company_code == candidate_code)
        )
        if existing_code.scalar_one_or_none():
            # 如果存在，重新生成
            return await self._generate_company_code(company_name)
        
        return candidate_code
