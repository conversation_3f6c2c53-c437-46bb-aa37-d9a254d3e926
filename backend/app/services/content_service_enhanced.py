"""
Enhanced Content Service Module
包含内容请求管理、内容服务订单、内容交付管理的增强服务
"""

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func, desc, asc, update, delete
from sqlalchemy.orm import selectinload, joinedload
from app.models.content import (
    ContentRequest, ContentDelivery, ContentRevision, ContentServiceOrder,
    RequestStatus, ReviewStatus
)
from app.models.order import Order, OrderStatus, OrderType
from app.models.company import Company
from app.models.user import User
from app.schemas.content import *
from app.exceptions import ValidationError, NotFoundError, PermissionError
from typing import Optional, Dict, Any, List, Tuple
from datetime import datetime, timedelta
from decimal import Decimal
import uuid
import json


class ContentRequestServiceEnhanced:
    """增强的内容请求服务"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def update_content_request(
        self, 
        request_id: str, 
        update_data: ContentRequestUpdate, 
        user_id: str
    ) -> ContentRequestResponse:
        """更新内容请求"""
        # 查找请求
        result = await self.db.execute(
            select(ContentRequest).where(ContentRequest.id == request_id)
        )
        content_request = result.scalar_one_or_none()
        if not content_request:
            raise NotFoundError(f"内容请求 {request_id} 不存在")
        
        # 权限检查：只有创建者或管理员可以更新
        user_result = await self.db.execute(
            select(User).where(User.id == user_id)
        )
        user = user_result.scalar_one_or_none()
        if not user:
            raise NotFoundError("用户不存在")
        
        # 检查是否是请求创建者
        company_result = await self.db.execute(
            select(Company).where(Company.id == content_request.company_id)
        )
        company = company_result.scalar_one_or_none()
        if company and str(company.user_id) != user_id and 'admin' not in user.roles:
            raise PermissionError("无权更新此内容请求")
        
        # 状态检查：只有待处理和已接受状态可以更新
        if content_request.status not in [RequestStatus.PENDING, RequestStatus.ACCEPTED]:
            raise ValidationError(f"状态为 {content_request.status} 的请求不能更新")
        
        # 更新字段
        update_dict = update_data.dict(exclude_unset=True)
        for field, value in update_dict.items():
            if value is not None:
                setattr(content_request, field, value)
        
        content_request.updated_at = datetime.utcnow()
        
        await self.db.commit()
        await self.db.refresh(content_request)
        
        return self._convert_to_response(content_request)
    
    async def cancel_content_request(
        self, 
        request_id: str, 
        cancel_request: CancelRequest, 
        user_id: str
    ) -> OperationResponse:
        """取消内容请求"""
        # 查找请求
        result = await self.db.execute(
            select(ContentRequest).where(ContentRequest.id == request_id)
        )
        content_request = result.scalar_one_or_none()
        if not content_request:
            raise NotFoundError(f"内容请求 {request_id} 不存在")
        
        # 权限检查
        user_result = await self.db.execute(
            select(User).where(User.id == user_id)
        )
        user = user_result.scalar_one_or_none()
        if not user:
            raise NotFoundError("用户不存在")
        
        # 检查是否是请求创建者
        company_result = await self.db.execute(
            select(Company).where(Company.id == content_request.company_id)
        )
        company = company_result.scalar_one_or_none()
        if company and str(company.user_id) != user_id and 'admin' not in user.roles:
            raise PermissionError("无权取消此内容请求")
        
        # 状态检查：已完成或已取消的不能再取消
        if content_request.status in [RequestStatus.COMPLETED, RequestStatus.CANCELLED]:
            raise ValidationError(f"状态为 {content_request.status} 的请求不能取消")
        
        # 更新请求状态
        content_request.status = RequestStatus.CANCELLED
        content_request.cancel_reason = cancel_request.reason
        content_request.cancelled_at = datetime.utcnow()
        content_request.updated_at = datetime.utcnow()
        
        # 如果需要取消相关订单
        if cancel_request.cancel_related_orders:
            # 查找相关的内容服务订单
            order_result = await self.db.execute(
                select(ContentServiceOrder).where(
                    ContentServiceOrder.request_id == request_id
                )
            )
            service_orders = order_result.scalars().all()
            
            for service_order in service_orders:
                if service_order.work_status not in ['completed', 'cancelled']:
                    service_order.work_status = 'cancelled'
                    
                    # 更新主订单状态
                    main_order_result = await self.db.execute(
                        select(Order).where(Order.id == service_order.order_id)
                    )
                    main_order = main_order_result.scalar_one_or_none()
                    if main_order and main_order.order_status not in [OrderStatus.COMPLETED, OrderStatus.CANCELLED]:
                        main_order.order_status = OrderStatus.CANCELLED
                        main_order.cancelled_at = datetime.utcnow()
        
        await self.db.commit()
        
        return OperationResponse(
            success=True,
            message=f"内容请求已取消",
            data={"request_id": request_id, "status": "cancelled"}
        )
    
    async def assign_service(
        self, 
        request_id: str, 
        assignment: ServiceAssignment, 
        user_id: str
    ) -> OperationResponse:
        """分配渠道服务"""
        # 查找请求
        result = await self.db.execute(
            select(ContentRequest).where(ContentRequest.id == request_id)
        )
        content_request = result.scalar_one_or_none()
        if not content_request:
            raise NotFoundError(f"内容请求 {request_id} 不存在")
        
        # 权限检查
        user_result = await self.db.execute(
            select(User).where(User.id == user_id)
        )
        user = user_result.scalar_one_or_none()
        if not user:
            raise NotFoundError("用户不存在")
        
        # 检查是否是请求创建者或管理员
        company_result = await self.db.execute(
            select(Company).where(Company.id == content_request.company_id)
        )
        company = company_result.scalar_one_or_none()
        if company and str(company.user_id) != user_id and 'admin' not in user.roles:
            raise PermissionError("无权分配此内容请求的服务")
        
        # 状态检查
        if content_request.status != RequestStatus.PENDING:
            raise ValidationError(f"只有待处理状态的请求可以分配服务")
        
        # 更新分配信息
        content_request.assigned_service_id = assignment.service_id
        content_request.assigned_at = datetime.utcnow()
        if assignment.provider_id:
            content_request.provider_id = assignment.provider_id
        content_request.updated_at = datetime.utcnow()
        
        await self.db.commit()
        
        return OperationResponse(
            success=True,
            message="服务分配成功",
            data={
                "request_id": request_id,
                "service_id": assignment.service_id,
                "assigned_at": content_request.assigned_at.isoformat()
            }
        )
    
    async def search_requests(
        self, 
        filters: Dict[str, Any], 
        pagination: Dict[str, int]
    ) -> ContentRequestListResponse:
        """搜索内容请求"""
        query = select(ContentRequest)
        
        # 应用过滤条件
        if filters.get('status'):
            query = query.where(ContentRequest.status == filters['status'])
        if filters.get('request_type'):
            query = query.where(ContentRequest.request_type == filters['request_type'])
        if filters.get('provider_id'):
            query = query.where(ContentRequest.provider_id == filters['provider_id'])
        if filters.get('company_id'):
            query = query.where(ContentRequest.company_id == filters['company_id'])
        if filters.get('group_id'):
            query = query.where(ContentRequest.group_id == filters['group_id'])
        if filters.get('tags'):
            # 搜索包含任意指定标签的请求
            tag_conditions = []
            for tag in filters['tags']:
                tag_conditions.append(func.jsonb_contains(ContentRequest.tags, json.dumps([tag])))
            query = query.where(or_(*tag_conditions))
        if filters.get('date_from'):
            query = query.where(ContentRequest.created_at >= filters['date_from'])
        if filters.get('date_to'):
            query = query.where(ContentRequest.created_at <= filters['date_to'])
        
        # 排序
        sort_by = filters.get('sort_by', 'created_at')
        sort_order = filters.get('sort_order', 'desc')
        if sort_order == 'desc':
            query = query.order_by(desc(getattr(ContentRequest, sort_by)))
        else:
            query = query.order_by(asc(getattr(ContentRequest, sort_by)))
        
        # 计算总数
        count_query = select(func.count()).select_from(query.subquery())
        total_result = await self.db.execute(count_query)
        total = total_result.scalar()
        
        # 分页
        page = pagination.get('page', 1)
        size = pagination.get('size', 20)
        offset = (page - 1) * size
        query = query.offset(offset).limit(size)
        
        # 执行查询
        result = await self.db.execute(query)
        requests = result.scalars().all()
        
        # 转换响应
        items = [self._convert_to_response(req) for req in requests]
        
        return ContentRequestListResponse(
            items=items,
            total=total,
            page=page,
            size=size,
            pages=(total + size - 1) // size
        )
    
    def _convert_to_response(self, request: ContentRequest) -> ContentRequestResponse:
        """转换为响应模型"""
        return ContentRequestResponse(
            id=str(request.id),
            group_id=str(request.group_id),
            company_id=str(request.company_id),
            provider_id=str(request.provider_id),
            service_id=str(request.service_id),
            request_type=request.request_type.value if hasattr(request.request_type, 'value') else request.request_type,
            request_title=request.request_title,
            request_description=request.request_description,
            provided_content_title=request.provided_content_title,
            provided_content_text=request.provided_content_text,
            provided_content_files=request.provided_content_files,
            creation_requirements=request.creation_requirements,
            tags=request.tags,
            deadline=request.deadline,
            status=request.status.value if hasattr(request.status, 'value') else request.status,
            estimated_delivery_days=request.estimated_delivery_days,
            accept_message=request.accept_message,
            fixed_price=request.fixed_price,
            created_at=request.created_at,
            updated_at=request.updated_at,
            accepted_at=request.accepted_at
        )


class ContentServiceOrderService:
    """内容服务订单服务"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_service_order(
        self, 
        order_data: ServiceOrderCreate, 
        user_id: str
    ) -> ServiceOrderResponse:
        """创建内容服务订单"""
        # 验证请求存在
        request_result = await self.db.execute(
            select(ContentRequest).where(ContentRequest.id == order_data.request_id)
        )
        content_request = request_result.scalar_one_or_none()
        if not content_request:
            raise NotFoundError(f"内容请求 {order_data.request_id} 不存在")
        
        # 创建主订单
        order_no = f"CSO{datetime.now().strftime('%Y%m%d%H%M%S')}{uuid.uuid4().hex[:6].upper()}"
        main_order = Order(
            order_no=order_no,
            user_id=user_id,
            company_id=content_request.company_id,
            order_type=OrderType.CONTENT_SERVICE,
            order_status=OrderStatus.PENDING,
            product_id=content_request.service_id,
            product_name=content_request.request_title,
            product_description=content_request.request_description,
            original_amount=order_data.price,
            final_amount=order_data.price,
            currency="CNY"
        )
        self.db.add(main_order)
        await self.db.flush()
        
        # 创建内容服务订单
        service_order = ContentServiceOrder(
            order_id=main_order.id,
            request_id=order_data.request_id,
            route_type=order_data.route_type,
            provider_id=order_data.provider_id,
            provider_type=order_data.provider_type,
            work_status='pending',
            assigned_at=datetime.utcnow(),
            estimated_delivery_days=order_data.estimated_delivery_days,
            price=order_data.price,
            notes=order_data.notes
        )
        self.db.add(service_order)
        
        # 更新内容请求状态
        content_request.status = RequestStatus.ACCEPTED
        content_request.accepted_at = datetime.utcnow()
        
        await self.db.commit()
        await self.db.refresh(service_order)
        
        return self._convert_to_response(service_order)
    
    async def get_service_order(
        self, 
        order_id: str, 
        user_id: str
    ) -> ServiceOrderDetailResponse:
        """获取服务订单详情"""
        result = await self.db.execute(
            select(ContentServiceOrder).where(ContentServiceOrder.id == order_id)
        )
        service_order = result.scalar_one_or_none()
        if not service_order:
            raise NotFoundError(f"服务订单 {order_id} 不存在")
        
        # TODO: 权限检查
        
        # 获取关联信息
        request_result = await self.db.execute(
            select(ContentRequest).where(ContentRequest.id == service_order.request_id)
        )
        request = request_result.scalar_one_or_none()
        
        order_result = await self.db.execute(
            select(Order).where(Order.id == service_order.order_id)
        )
        main_order = order_result.scalar_one_or_none()
        
        response = ServiceOrderDetailResponse(
            **self._convert_to_response(service_order).dict(),
            request_info=self._request_to_dict(request) if request else None,
            order_info=self._order_to_dict(main_order) if main_order else None
        )
        
        return response
    
    async def list_service_orders(
        self, 
        filters: Dict[str, Any], 
        user_id: str, 
        pagination: Dict[str, int]
    ) -> ServiceOrderListResponse:
        """查询服务订单列表"""
        query = select(ContentServiceOrder)
        
        # 应用过滤条件
        if filters.get('work_status'):
            query = query.where(ContentServiceOrder.work_status == filters['work_status'])
        if filters.get('provider_id'):
            query = query.where(ContentServiceOrder.provider_id == filters['provider_id'])
        if filters.get('request_id'):
            query = query.where(ContentServiceOrder.request_id == filters['request_id'])
        if filters.get('route_type'):
            query = query.where(ContentServiceOrder.route_type == filters['route_type'])
        if filters.get('date_from'):
            query = query.where(ContentServiceOrder.created_at >= filters['date_from'])
        if filters.get('date_to'):
            query = query.where(ContentServiceOrder.created_at <= filters['date_to'])
        
        # 排序
        query = query.order_by(desc(ContentServiceOrder.created_at))
        
        # 计算总数
        count_query = select(func.count()).select_from(query.subquery())
        total_result = await self.db.execute(count_query)
        total = total_result.scalar()
        
        # 分页
        page = pagination.get('page', 1)
        size = pagination.get('size', 20)
        offset = (page - 1) * size
        query = query.offset(offset).limit(size)
        
        # 执行查询
        result = await self.db.execute(query)
        orders = result.scalars().all()
        
        # 转换响应
        items = [self._convert_to_response(order) for order in orders]
        
        return ServiceOrderListResponse(
            items=items,
            total=total,
            page=page,
            size=size,
            pages=(total + size - 1) // size
        )
    
    async def update_work_status(
        self, 
        order_id: str, 
        status_update: WorkStatusUpdate, 
        user_id: str
    ) -> OperationResponse:
        """更新工作状态"""
        result = await self.db.execute(
            select(ContentServiceOrder).where(ContentServiceOrder.id == order_id)
        )
        service_order = result.scalar_one_or_none()
        if not service_order:
            raise NotFoundError(f"服务订单 {order_id} 不存在")
        
        # TODO: 权限检查
        
        # 更新状态
        service_order.work_status = status_update.work_status
        service_order.updated_at = datetime.utcnow()
        
        # 如果是交付状态，记录交付时间
        if status_update.work_status == 'delivered':
            service_order.delivered_at = datetime.utcnow()
        
        await self.db.commit()
        
        return OperationResponse(
            success=True,
            message=f"工作状态已更新为 {status_update.work_status}",
            data={"order_id": order_id, "work_status": status_update.work_status}
        )
    
    async def deliver_content(
        self, 
        order_id: str, 
        delivery_data: ContentDeliverySubmit, 
        user_id: str
    ) -> DeliveryResponse:
        """提交交付内容"""
        # 查找服务订单
        result = await self.db.execute(
            select(ContentServiceOrder).where(ContentServiceOrder.id == order_id)
        )
        service_order = result.scalar_one_or_none()
        if not service_order:
            raise NotFoundError(f"服务订单 {order_id} 不存在")
        
        # TODO: 权限检查 - 只有服务提供方可以交付
        
        # 创建交付记录
        delivery = ContentDelivery(
            request_id=service_order.request_id,
            content_title=delivery_data.delivered_title,
            content_text=delivery_data.delivered_content,
            content_attachments=delivery_data.delivered_files,
            delivery_note=delivery_data.delivery_note or "",
            review_status=ReviewStatus.PENDING,
            version=1,
            acceptance_status='pending'
        )
        self.db.add(delivery)
        
        # 更新服务订单状态
        service_order.work_status = 'delivered'
        service_order.delivered_at = datetime.utcnow()
        
        await self.db.commit()
        await self.db.refresh(delivery)
        
        return self._delivery_to_response(delivery)
    
    async def review_delivery(
        self, 
        order_id: str, 
        review_data: DeliveryReview, 
        user_id: str
    ) -> ReviewResponse:
        """审核交付内容"""
        # 查找服务订单
        result = await self.db.execute(
            select(ContentServiceOrder).where(ContentServiceOrder.id == order_id)
        )
        service_order = result.scalar_one_or_none()
        if not service_order:
            raise NotFoundError(f"服务订单 {order_id} 不存在")
        
        # 查找最新的交付记录
        delivery_result = await self.db.execute(
            select(ContentDelivery)
            .where(ContentDelivery.request_id == service_order.request_id)
            .order_by(desc(ContentDelivery.delivered_at))
            .limit(1)
        )
        delivery = delivery_result.scalar_one_or_none()
        if not delivery:
            raise NotFoundError("未找到交付记录")
        
        # 更新交付记录
        if review_data.review_status == 'approved':
            delivery.review_status = ReviewStatus.APPROVED
            delivery.acceptance_status = 'accepted'
            delivery.accepted_at = datetime.utcnow()
            service_order.work_status = 'completed'
            
            # 更新内容请求状态
            request_result = await self.db.execute(
                select(ContentRequest).where(ContentRequest.id == service_order.request_id)
            )
            request = request_result.scalar_one_or_none()
            if request:
                request.status = RequestStatus.COMPLETED
                
        elif review_data.review_status == 'rejected':
            delivery.review_status = ReviewStatus.REJECTED
            delivery.acceptance_status = 'rejected'
            delivery.rejected_at = datetime.utcnow()
            delivery.acceptance_reason = review_data.review_comment
            service_order.work_status = 'rejected'
            
        elif review_data.review_status == 'revision_required':
            delivery.review_status = ReviewStatus.REVISION_REQUIRED
            service_order.work_status = 'reviewing'
            
            # 创建修改记录
            if review_data.revision_requirements:
                revision = ContentRevision(
                    delivery_id=delivery.id,
                    revision_requirements=review_data.revision_requirements,
                    revision_deadline=datetime.utcnow() + timedelta(days=3)
                )
                self.db.add(revision)
        
        delivery.review_note = review_data.review_comment
        delivery.review_score = review_data.quality_score
        delivery.reviewed_at = datetime.utcnow()
        delivery.reviewer_id = user_id
        
        await self.db.commit()
        
        return ReviewResponse(
            success=True,
            message=f"审核完成: {review_data.review_status}",
            order_id=str(order_id),
            delivery_id=str(delivery.id),
            review_status=review_data.review_status,
            next_action='complete' if review_data.review_status == 'approved' else 'revise'
        )
    
    def _convert_to_response(self, order: ContentServiceOrder) -> ServiceOrderResponse:
        """转换为响应模型"""
        return ServiceOrderResponse(
            id=str(order.id),
            order_id=str(order.order_id),
            request_id=str(order.request_id),
            route_type=order.route_type,
            provider_id=str(order.provider_id) if order.provider_id else None,
            provider_type=order.provider_type,
            work_status=order.work_status,
            assigned_at=order.assigned_at,
            delivered_at=order.delivered_at,
            estimated_delivery_days=order.estimated_delivery_days,
            price=order.price,
            notes=order.notes,
            created_at=order.created_at,
            updated_at=order.updated_at
        )
    
    def _delivery_to_response(self, delivery: ContentDelivery) -> DeliveryResponse:
        """转换交付记录为响应"""
        return DeliveryResponse(
            id=str(delivery.id),
            request_id=str(delivery.request_id),
            content_title=delivery.content_title,
            content_text=delivery.content_text,
            content_summary=delivery.content_summary,
            delivery_note=delivery.delivery_note,
            review_status=delivery.review_status.value if hasattr(delivery.review_status, 'value') else delivery.review_status,
            version=delivery.version or 1,
            parent_delivery_id=str(delivery.parent_delivery_id) if delivery.parent_delivery_id else None,
            acceptance_status=delivery.acceptance_status,
            acceptance_reason=delivery.acceptance_reason,
            accepted_at=delivery.accepted_at,
            rejected_at=delivery.rejected_at,
            delivered_at=delivery.delivered_at,
            updated_at=delivery.updated_at
        )
    
    def _request_to_dict(self, request: ContentRequest) -> Dict[str, Any]:
        """转换请求为字典"""
        if not request:
            return None
        return {
            "id": str(request.id),
            "title": request.request_title,
            "type": request.request_type.value if hasattr(request.request_type, 'value') else request.request_type,
            "status": request.status.value if hasattr(request.status, 'value') else request.status,
            "deadline": request.deadline.isoformat() if request.deadline else None
        }
    
    def _order_to_dict(self, order: Order) -> Dict[str, Any]:
        """转换订单为字典"""
        if not order:
            return None
        return {
            "id": str(order.id),
            "order_no": order.order_no,
            "status": order.order_status.value if hasattr(order.order_status, 'value') else order.order_status,
            "amount": float(order.final_amount) if order.final_amount else 0
        }


class ContentDeliveryService:
    """内容交付服务"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_delivery(
        self, 
        delivery_data: DeliveryCreate, 
        provider_id: str
    ) -> DeliveryResponse:
        """创建交付记录"""
        # 验证订单和请求
        order_result = await self.db.execute(
            select(ContentServiceOrder).where(ContentServiceOrder.id == delivery_data.order_id)
        )
        service_order = order_result.scalar_one_or_none()
        if not service_order:
            raise NotFoundError(f"服务订单 {delivery_data.order_id} 不存在")
        
        # 创建交付记录
        delivery = ContentDelivery(
            request_id=delivery_data.request_id,
            content_title=delivery_data.delivered_title,
            content_text=delivery_data.delivered_content,
            content_attachments=delivery_data.delivered_files,
            delivery_note="",
            review_status=ReviewStatus.PENDING,
            version=delivery_data.version,
            parent_delivery_id=delivery_data.parent_delivery_id,
            acceptance_status='pending'
        )
        self.db.add(delivery)
        
        await self.db.commit()
        await self.db.refresh(delivery)
        
        return self._convert_to_response(delivery)
    
    async def get_delivery(
        self, 
        delivery_id: str, 
        user_id: str
    ) -> DeliveryDetailResponse:
        """获取交付详情"""
        result = await self.db.execute(
            select(ContentDelivery).where(ContentDelivery.id == delivery_id)
        )
        delivery = result.scalar_one_or_none()
        if not delivery:
            raise NotFoundError(f"交付记录 {delivery_id} 不存在")
        
        # TODO: 权限检查
        
        # 获取历史版本
        history = []
        if delivery.parent_delivery_id:
            history_result = await self.db.execute(
                select(ContentDelivery)
                .where(ContentDelivery.parent_delivery_id == delivery.parent_delivery_id)
                .order_by(ContentDelivery.version)
            )
            history_deliveries = history_result.scalars().all()
            history = [self._delivery_to_dict(d) for d in history_deliveries]
        
        response = DeliveryDetailResponse(
            **self._convert_to_response(delivery).dict(),
            content_images=delivery.content_images,
            content_videos=delivery.content_videos,
            content_attachments=delivery.content_attachments,
            content_metadata=delivery.content_metadata,
            history=history
        )
        
        return response
    
    async def list_deliveries(
        self, 
        filters: Dict[str, Any], 
        user_id: str, 
        pagination: Dict[str, int]
    ) -> DeliveryListResponse:
        """查询交付记录列表"""
        query = select(ContentDelivery)
        
        # 应用过滤条件
        if filters.get('order_id'):
            # 需要通过服务订单关联
            order_result = await self.db.execute(
                select(ContentServiceOrder).where(ContentServiceOrder.id == filters['order_id'])
            )
            service_order = order_result.scalar_one_or_none()
            if service_order:
                query = query.where(ContentDelivery.request_id == service_order.request_id)
        
        if filters.get('request_id'):
            query = query.where(ContentDelivery.request_id == filters['request_id'])
        if filters.get('acceptance_status'):
            query = query.where(ContentDelivery.acceptance_status == filters['acceptance_status'])
        if filters.get('version'):
            query = query.where(ContentDelivery.version == filters['version'])
        
        # 排序
        query = query.order_by(desc(ContentDelivery.delivered_at))
        
        # 计算总数
        count_query = select(func.count()).select_from(query.subquery())
        total_result = await self.db.execute(count_query)
        total = total_result.scalar()
        
        # 分页
        page = pagination.get('page', 1)
        size = pagination.get('size', 20)
        offset = (page - 1) * size
        query = query.offset(offset).limit(size)
        
        # 执行查询
        result = await self.db.execute(query)
        deliveries = result.scalars().all()
        
        # 转换响应
        items = [self._convert_to_response(d) for d in deliveries]
        
        return DeliveryListResponse(
            items=items,
            total=total,
            page=page,
            size=size,
            pages=(total + size - 1) // size
        )
    
    async def accept_delivery(
        self, 
        delivery_id: str, 
        acceptance: DeliveryAcceptance, 
        user_id: str
    ) -> OperationResponse:
        """接受交付"""
        result = await self.db.execute(
            select(ContentDelivery).where(ContentDelivery.id == delivery_id)
        )
        delivery = result.scalar_one_or_none()
        if not delivery:
            raise NotFoundError(f"交付记录 {delivery_id} 不存在")
        
        # TODO: 权限检查 - 只有订单所有者可以接受
        
        # 更新交付状态
        delivery.acceptance_status = 'accepted'
        delivery.acceptance_reason = acceptance.acceptance_note
        delivery.accepted_at = datetime.utcnow()
        delivery.review_status = ReviewStatus.APPROVED
        delivery.review_score = acceptance.quality_score
        
        # 更新相关服务订单
        order_result = await self.db.execute(
            select(ContentServiceOrder).where(ContentServiceOrder.request_id == delivery.request_id)
        )
        service_order = order_result.scalar_one_or_none()
        if service_order:
            service_order.work_status = 'completed'
        
        # 更新内容请求状态
        request_result = await self.db.execute(
            select(ContentRequest).where(ContentRequest.id == delivery.request_id)
        )
        request = request_result.scalar_one_or_none()
        if request:
            request.status = RequestStatus.COMPLETED
        
        await self.db.commit()
        
        return OperationResponse(
            success=True,
            message="交付已接受",
            data={
                "delivery_id": delivery_id,
                "status": "accepted",
                "score": acceptance.quality_score
            }
        )
    
    async def reject_delivery(
        self, 
        delivery_id: str, 
        rejection: DeliveryRejection, 
        user_id: str
    ) -> OperationResponse:
        """拒绝交付"""
        result = await self.db.execute(
            select(ContentDelivery).where(ContentDelivery.id == delivery_id)
        )
        delivery = result.scalar_one_or_none()
        if not delivery:
            raise NotFoundError(f"交付记录 {delivery_id} 不存在")
        
        # TODO: 权限检查 - 只有订单所有者可以拒绝
        
        # 更新交付状态
        delivery.acceptance_status = 'rejected'
        delivery.acceptance_reason = rejection.rejection_reason
        delivery.rejected_at = datetime.utcnow()
        delivery.review_status = ReviewStatus.REVISION_REQUIRED
        
        # 创建修改要求记录
        revision = ContentRevision(
            delivery_id=delivery.id,
            revision_requirements=rejection.revision_requirements,
            revision_deadline=datetime.utcnow() + timedelta(days=rejection.max_revision_days)
        )
        self.db.add(revision)
        
        # 更新交付记录的修改次数
        delivery.revision_count = (delivery.revision_count or 0) + 1
        
        # 更新服务订单状态
        order_result = await self.db.execute(
            select(ContentServiceOrder).where(ContentServiceOrder.request_id == delivery.request_id)
        )
        service_order = order_result.scalar_one_or_none()
        if service_order:
            service_order.work_status = 'reviewing'
        
        await self.db.commit()
        
        return OperationResponse(
            success=True,
            message="交付已拒绝，需要修改",
            data={
                "delivery_id": delivery_id,
                "status": "rejected",
                "revision_deadline": (datetime.utcnow() + timedelta(days=rejection.max_revision_days)).isoformat()
            }
        )
    
    async def create_revision(
        self, 
        original_delivery_id: str, 
        revised_content: ContentDeliverySubmit, 
        provider_id: str
    ) -> DeliveryResponse:
        """创建修订版本"""
        # 查找原交付记录
        result = await self.db.execute(
            select(ContentDelivery).where(ContentDelivery.id == original_delivery_id)
        )
        original = result.scalar_one_or_none()
        if not original:
            raise NotFoundError(f"原交付记录 {original_delivery_id} 不存在")
        
        # 创建新版本
        new_version = ContentDelivery(
            request_id=original.request_id,
            content_title=revised_content.delivered_title,
            content_text=revised_content.delivered_content,
            content_attachments=revised_content.delivered_files,
            delivery_note=revised_content.delivery_note or "",
            review_status=ReviewStatus.PENDING,
            version=(original.version or 1) + 1,
            parent_delivery_id=original.parent_delivery_id or original.id,
            acceptance_status='pending'
        )
        self.db.add(new_version)
        
        await self.db.commit()
        await self.db.refresh(new_version)
        
        return self._convert_to_response(new_version)
    
    def _convert_to_response(self, delivery: ContentDelivery) -> DeliveryResponse:
        """转换为响应模型"""
        return DeliveryResponse(
            id=str(delivery.id),
            request_id=str(delivery.request_id),
            content_title=delivery.content_title,
            content_text=delivery.content_text,
            content_summary=delivery.content_summary,
            delivery_note=delivery.delivery_note,
            review_status=delivery.review_status.value if hasattr(delivery.review_status, 'value') else delivery.review_status,
            version=delivery.version or 1,
            parent_delivery_id=str(delivery.parent_delivery_id) if delivery.parent_delivery_id else None,
            acceptance_status=delivery.acceptance_status,
            acceptance_reason=delivery.acceptance_reason,
            accepted_at=delivery.accepted_at,
            rejected_at=delivery.rejected_at,
            delivered_at=delivery.delivered_at,
            updated_at=delivery.updated_at
        )
    
    def _delivery_to_dict(self, delivery: ContentDelivery) -> Dict[str, Any]:
        """转换交付记录为字典"""
        return {
            "id": str(delivery.id),
            "version": delivery.version or 1,
            "title": delivery.content_title,
            "status": delivery.review_status.value if hasattr(delivery.review_status, 'value') else delivery.review_status,
            "delivered_at": delivery.delivered_at.isoformat() if delivery.delivered_at else None
        }