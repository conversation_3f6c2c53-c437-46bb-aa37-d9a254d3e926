"""
Platform Adapters - 平台适配器实现
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
import aiohttp
import json
from datetime import datetime, timedelta


class PlatformAdapter(ABC):
    """平台适配器基类"""
    
    def __init__(self, config: Dict[str, Any], credentials: Optional[Dict[str, Any]] = None):
        self.config = config
        self.credentials = credentials
        self.base_url = config.get('api_base_url', '')
    
    @abstractmethod
    async def authenticate(self, credentials: Dict[str, Any]) -> Dict[str, Any]:
        """认证并获取访问令牌"""
        pass
    
    @abstractmethod
    async def create_order(self, order_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建订单"""
        pass
    
    @abstractmethod
    async def query_order(self, order_id: str) -> Dict[str, Any]:
        """查询订单状态"""
        pass
    
    @abstractmethod
    async def test_connection(self) -> Dict[str, Any]:
        """测试连接"""
        pass
    
    @abstractmethod
    async def refresh_token(self, refresh_token: str) -> Dict[str, Any]:
        """刷新令牌"""
        pass
    
    async def _make_request(
        self, 
        method: str, 
        endpoint: str, 
        headers: Optional[Dict] = None,
        data: Optional[Dict] = None,
        params: Optional[Dict] = None
    ) -> Dict[str, Any]:
        """统一的HTTP请求方法"""
        url = f"{self.base_url}{endpoint}"
        
        async with aiohttp.ClientSession() as session:
            async with session.request(
                method=method,
                url=url,
                headers=headers,
                json=data,
                params=params
            ) as response:
                response_data = await response.json()
                
                if response.status >= 400:
                    raise Exception(f"Platform API error: {response.status} - {response_data}")
                
                return response_data


class RuanwenjieAdapter(PlatformAdapter):
    """软文街平台适配器"""
    
    async def authenticate(self, credentials: Dict[str, Any]) -> Dict[str, Any]:
        """软文街认证"""
        # 软文街使用API Key认证，不需要获取令牌
        return {
            "access_token": credentials.get("api_key"),
            "token_type": "api_key",
            "expires_at": (datetime.utcnow() + timedelta(days=365)).isoformat()
        }
    
    async def create_order(self, order_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建软文街订单"""
        headers = {
            "Authorization": f"Bearer {self.credentials.get('api_key')}",
            "Content-Type": "application/json"
        }
        
        return await self._make_request(
            method="POST",
            endpoint="/api/v2/orders",
            headers=headers,
            data=order_data
        )
    
    async def query_order(self, order_id: str) -> Dict[str, Any]:
        """查询软文街订单"""
        headers = {
            "Authorization": f"Bearer {self.credentials.get('api_key')}"
        }
        
        return await self._make_request(
            method="GET",
            endpoint=f"/api/v2/orders/{order_id}",
            headers=headers
        )
    
    async def test_connection(self) -> Dict[str, Any]:
        """测试软文街连接"""
        try:
            headers = {
                "Authorization": f"Bearer {self.credentials.get('api_key')}"
            }
            
            # 调用一个简单的API来测试连接
            result = await self._make_request(
                method="GET",
                endpoint="/api/v2/account/info",
                headers=headers
            )
            
            return {
                "success": True,
                "connection_status": "connected",
                "details": result
            }
        except Exception as e:
            return {
                "success": False,
                "connection_status": "failed",
                "error": str(e)
            }
    
    async def refresh_token(self, refresh_token: str) -> Dict[str, Any]:
        """软文街不需要刷新令牌"""
        return {
            "access_token": self.credentials.get("api_key"),
            "token_type": "api_key",
            "expires_at": (datetime.utcnow() + timedelta(days=365)).isoformat()
        }


class KimiAdapter(PlatformAdapter):
    """Kimi AI平台适配器"""
    
    async def authenticate(self, credentials: Dict[str, Any]) -> Dict[str, Any]:
        """Kimi OAuth2认证"""
        data = {
            "client_id": credentials.get("client_id"),
            "client_secret": credentials.get("client_secret"),
            "grant_type": "client_credentials",
            "scope": credentials.get("scope", "chat completion")
        }
        
        result = await self._make_request(
            method="POST",
            endpoint="/oauth/token",
            data=data
        )
        
        return {
            "access_token": result.get("access_token"),
            "refresh_token": result.get("refresh_token"),
            "token_type": result.get("token_type", "Bearer"),
            "expires_at": (
                datetime.utcnow() + timedelta(seconds=result.get("expires_in", 3600))
            ).isoformat()
        }
    
    async def create_order(self, order_data: Dict[str, Any]) -> Dict[str, Any]:
        """Kimi不支持订单，这里可以是创建会话"""
        headers = {
            "Authorization": f"Bearer {self.credentials.get('access_token')}",
            "Content-Type": "application/json"
        }
        
        return await self._make_request(
            method="POST",
            endpoint="/v1/chat/completions",
            headers=headers,
            data=order_data
        )
    
    async def query_order(self, order_id: str) -> Dict[str, Any]:
        """查询Kimi会话状态"""
        headers = {
            "Authorization": f"Bearer {self.credentials.get('access_token')}"
        }
        
        return await self._make_request(
            method="GET",
            endpoint=f"/v1/chat/sessions/{order_id}",
            headers=headers
        )
    
    async def test_connection(self) -> Dict[str, Any]:
        """测试Kimi连接"""
        try:
            headers = {
                "Authorization": f"Bearer {self.credentials.get('access_token')}"
            }
            
            # 测试模型列表API
            result = await self._make_request(
                method="GET",
                endpoint="/v1/models",
                headers=headers
            )
            
            return {
                "success": True,
                "connection_status": "connected",
                "details": {
                    "models": result.get("data", [])
                }
            }
        except Exception as e:
            return {
                "success": False,
                "connection_status": "failed",
                "error": str(e)
            }
    
    async def refresh_token(self, refresh_token: str) -> Dict[str, Any]:
        """刷新Kimi令牌"""
        data = {
            "grant_type": "refresh_token",
            "refresh_token": refresh_token,
            "client_id": self.credentials.get("client_id"),
            "client_secret": self.credentials.get("client_secret")
        }
        
        result = await self._make_request(
            method="POST",
            endpoint="/oauth/token",
            data=data
        )
        
        return {
            "access_token": result.get("access_token"),
            "refresh_token": result.get("refresh_token", refresh_token),
            "token_type": result.get("token_type", "Bearer"),
            "expires_at": (
                datetime.utcnow() + timedelta(seconds=result.get("expires_in", 3600))
            ).isoformat()
        }


class DoubaoAdapter(PlatformAdapter):
    """抖音/豆包平台适配器"""
    
    async def authenticate(self, credentials: Dict[str, Any]) -> Dict[str, Any]:
        """豆包认证"""
        # 豆包使用API Key + Secret签名认证
        return {
            "access_token": credentials.get("api_key"),
            "token_type": "api_key",
            "expires_at": (datetime.utcnow() + timedelta(days=30)).isoformat()
        }
    
    async def create_order(self, order_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建豆包内容订单"""
        headers = {
            "X-API-Key": self.credentials.get("api_key"),
            "Content-Type": "application/json"
        }
        
        return await self._make_request(
            method="POST",
            endpoint="/api/v1/content/create",
            headers=headers,
            data=order_data
        )
    
    async def query_order(self, order_id: str) -> Dict[str, Any]:
        """查询豆包订单"""
        headers = {
            "X-API-Key": self.credentials.get("api_key")
        }
        
        return await self._make_request(
            method="GET",
            endpoint=f"/api/v1/content/{order_id}",
            headers=headers
        )
    
    async def test_connection(self) -> Dict[str, Any]:
        """测试豆包连接"""
        try:
            headers = {
                "X-API-Key": self.credentials.get("api_key")
            }
            
            result = await self._make_request(
                method="GET",
                endpoint="/api/v1/account/status",
                headers=headers
            )
            
            return {
                "success": True,
                "connection_status": "connected",
                "details": result
            }
        except Exception as e:
            return {
                "success": False,
                "connection_status": "failed",
                "error": str(e)
            }
    
    async def refresh_token(self, refresh_token: str) -> Dict[str, Any]:
        """豆包不需要刷新令牌"""
        return {
            "access_token": self.credentials.get("api_key"),
            "token_type": "api_key",
            "expires_at": (datetime.utcnow() + timedelta(days=30)).isoformat()
        }


class PlatformAdapterFactory:
    """平台适配器工厂"""
    
    _adapters = {
        "ruanwenjie": RuanwenjieAdapter,
        "kimi": KimiAdapter,
        "doubao": DoubaoAdapter
    }
    
    @classmethod
    def create_adapter(
        cls, 
        platform_code: str, 
        config: Dict[str, Any], 
        credentials: Optional[Dict[str, Any]] = None
    ) -> PlatformAdapter:
        """
        创建平台适配器实例
        
        Args:
            platform_code: 平台代码
            config: 平台配置
            credentials: 平台凭证
            
        Returns:
            平台适配器实例
        """
        adapter_class = cls._adapters.get(platform_code)
        if not adapter_class:
            raise ValueError(f"Unsupported platform: {platform_code}")
        
        return adapter_class(config, credentials)
    
    @classmethod
    def register_adapter(cls, platform_code: str, adapter_class: type):
        """注册新的平台适配器"""
        cls._adapters[platform_code] = adapter_class
    
    @classmethod
    def get_supported_platforms(cls) -> list:
        """获取支持的平台列表"""
        return list(cls._adapters.keys())