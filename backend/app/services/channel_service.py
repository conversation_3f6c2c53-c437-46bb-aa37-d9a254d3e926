from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func, desc, asc, text, String
from app.models.channel import ContentProvider, ChannelEarnings, ChannelWithdraw, ChannelCategory, ChannelService as ChannelServiceModel, ChannelCategoryMapping
from app.models.user import User
from app.schemas.channel import *
from app.schemas.channel_category import *
from app.exceptions import ValidationError, NotFoundError, PermissionError
from typing import Optional, Dict, Any, List
from datetime import datetime, timedelta
from decimal import Decimal
import uuid

class ChannelService:
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_channel_profile(self, user_id: str, request: ChannelProfileRequest) -> ChannelResponse:
        """创建渠道商信息"""
        # 检查用户是否已有渠道商信息
        existing_provider = await self.db.execute(
            select(ContentProvider).where(ContentProvider.user_id == user_id)
        )
        if existing_provider.scalar_one_or_none():
            raise ValidationError("渠道商信息已存在，请使用更新接口")
        
        # 检查提供商名称是否重复
        name_check = await self.db.execute(
            select(ContentProvider).where(ContentProvider.provider_name == request.provider_name)
        )
        if name_check.scalar_one_or_none():
            raise ValidationError("提供商名称已存在")
        
        # 根据类型验证必填字段
        self._validate_provider_fields(request)
        
        # 创建渠道商信息
        provider = ContentProvider(
            user_id=user_id,
            provider_name=request.provider_name,
            provider_type=request.provider_type,
            real_name=request.real_name,
            id_card_number=request.id_card_number,
            company_name=request.company_name,
            business_license=request.business_license,
            contact_phone=request.contact_phone,
            contact_email=request.contact_email,
            contact_address=request.contact_address,
            business_description=request.business_description,
            service_categories=request.service_categories,
            platform_accounts=request.platform_accounts,
            portfolio_urls=request.portfolio_urls,
            qualification_files=request.qualification_files
        )
        
        self.db.add(provider)
        await self.db.commit()
        await self.db.refresh(provider)
        
        return await self._build_channel_response(provider)
    
    async def get_channel_info(self, user_id: str) -> ChannelResponse:
        """获取渠道商信息"""
        result = await self.db.execute(
            select(ContentProvider).where(ContentProvider.user_id == user_id)
        )
        provider = result.scalar_one_or_none()
        if not provider:
            raise NotFoundError("渠道商信息不存在")
        
        return await self._build_channel_response(provider)
    
    async def update_channel_info(self, user_id: str, request: ChannelUpdateRequest) -> ChannelResponse:
        """更新渠道商信息"""
        result = await self.db.execute(
            select(ContentProvider).where(ContentProvider.user_id == user_id)
        )
        provider = result.scalar_one_or_none()
        if not provider:
            raise NotFoundError("渠道商信息不存在")
        
        # 记录变更前的数据
        old_data = {}
        new_data = {}
        
        # 更新允许修改的字段
        if request.contact_phone is not None:
            old_data["contact_phone"] = provider.contact_phone
            provider.contact_phone = request.contact_phone
            new_data["contact_phone"] = request.contact_phone
            
        if request.contact_email is not None:
            old_data["contact_email"] = provider.contact_email
            provider.contact_email = request.contact_email
            new_data["contact_email"] = request.contact_email
            
        if request.contact_address is not None:
            old_data["contact_address"] = provider.contact_address
            provider.contact_address = request.contact_address
            new_data["contact_address"] = request.contact_address
            
        if request.business_description is not None:
            old_data["business_description"] = provider.business_description
            provider.business_description = request.business_description
            new_data["business_description"] = request.business_description
            
        if request.service_categories is not None:
            old_data["service_categories"] = provider.service_categories
            provider.service_categories = request.service_categories
            new_data["service_categories"] = request.service_categories
            
        if request.platform_accounts is not None:
            old_data["platform_accounts"] = provider.platform_accounts
            provider.platform_accounts = request.platform_accounts
            new_data["platform_accounts"] = request.platform_accounts
            
        if request.portfolio_urls is not None:
            old_data["portfolio_urls"] = provider.portfolio_urls
            provider.portfolio_urls = request.portfolio_urls
            new_data["portfolio_urls"] = request.portfolio_urls
            
        if request.qualification_files is not None:
            old_data["qualification_files"] = provider.qualification_files
            provider.qualification_files = request.qualification_files
            new_data["qualification_files"] = request.qualification_files
        
        provider.updated_at = datetime.utcnow()
        
        # 记录活动日志（暂时注释掉，因为ChannelActivity表可能不存在）
        # if old_data:  # 只有在有变更时才记录
        #     activity = ChannelActivity(
        #         provider_id=provider.id,
        #         user_id=user_id,
        #         activity_type="updated",
        #         description="渠道商信息更新",
        #         old_data=old_data,
        #         new_data=new_data
        #     )
        #     self.db.add(activity)
        
        await self.db.commit()
        
        return await self._build_channel_response(provider)



    async def get_channel_list(self, query: ChannelListQuery, admin_id: str) -> Dict[str, Any]:
        """获取渠道商列表（管理员）"""
        # 构建查询条件
        conditions = []
        
        if query.provider_type:
            conditions.append(ContentProvider.provider_type == query.provider_type)
        if query.verification_status:
            conditions.append(ContentProvider.verification_status == query.verification_status)
        if query.search:
            search_condition = or_(
                ContentProvider.provider_name.ilike(f"%{query.search}%"),
                ContentProvider.company_name.ilike(f"%{query.search}%"),
                ContentProvider.real_name.ilike(f"%{query.search}%"),
                ContentProvider.contact_email.ilike(f"%{query.search}%")
            )
            conditions.append(search_condition)
        
        # 构建查询
        base_query = select(ContentProvider)
        if conditions:
            base_query = base_query.where(and_(*conditions))
        
        # 排序
        if query.sort_order == "desc":
            base_query = base_query.order_by(desc(getattr(ContentProvider, query.sort_by)))
        else:
            base_query = base_query.order_by(asc(getattr(ContentProvider, query.sort_by)))
        
        # 分页
        offset = (query.page - 1) * query.size
        paginated_query = base_query.offset(offset).limit(query.size)
        
        # 执行查询
        result = await self.db.execute(paginated_query)
        providers = result.scalars().all()
        
        # 获取总数
        count_query = select(func.count(ContentProvider.id))
        if conditions:
            count_query = count_query.where(and_(*conditions))
        total_result = await self.db.execute(count_query)
        total = total_result.scalar()
        
        # 构建响应数据
        items = []
        for provider in providers:
            provider_response = await self._build_channel_response(provider)
            items.append(provider_response)
        
        return {
            "items": items,
            "pagination": {
                "total": total,
                "page": query.page,
                "size": query.size,
                "pages": (total + query.size - 1) // query.size
            }
        }
    
    async def verify_channel(self, provider_id: str, request: ChannelVerificationRequest, verifier_id: str) -> Dict[str, Any]:
        """渠道商审核（管理员）"""
        result = await self.db.execute(
            select(ContentProvider).where(ContentProvider.id == provider_id)
        )
        provider = result.scalar_one_or_none()
        if not provider:
            raise NotFoundError("渠道商信息不存在")
        
        old_status = provider.verification_status
        provider.verification_status = request.status
        provider.verification_time = datetime.utcnow()
        provider.verification_note = request.note
        provider.verifier_id = verifier_id
        provider.updated_at = datetime.utcnow()
        
        # 记录活动日志（暂时注释掉，因为ChannelActivity表可能不存在）
        # activity = ChannelActivity(
        #     provider_id=provider.id,
        #     user_id=verifier_id,
        #     activity_type="verified",
        #     description=f"渠道商审核: {old_status} -> {request.status}",
        #     old_data={"verification_status": old_status},
        #     new_data={
        #         "verification_status": request.status,
        #         "verification_note": request.note
        #     }
        # )
        # self.db.add(activity)
        
        await self.db.commit()

        # 如果审核通过，自动为用户分配渠道商用户角色
        if request.status == "verified":
            try:
                from app.services.permission_service import PermissionService
                permission_service = PermissionService(self.db)
                await permission_service.assign_role_to_user(
                    user_id=str(provider.user_id),
                    role_code="channel_user",
                    assigned_by=verifier_id
                )
                print(f"审核通过，已为用户 {provider.user_id} 分配渠道商用户角色")
            except Exception as e:
                print(f"分配渠道商用户角色失败: {e}")
                # 不影响审核流程，继续执行

        # TODO: 发送通知
        if request.notify_user:
            pass  # await self.notification_service.send_verification_result(provider, request)
        
        return {
            "provider_id": str(provider_id),
            "provider_name": provider.provider_name,
            "old_status": old_status,
            "new_status": request.status,
            "verification_note": request.note,
            "verifier": str(verifier_id),
            "verification_time": provider.verification_time
        }
    
    async def _build_channel_response(self, provider: ContentProvider) -> ChannelResponse:
        """构建渠道商响应数据"""
        return ChannelResponse(
            id=str(provider.id),
            provider_name=provider.provider_name,
            provider_type=provider.provider_type,  # 已经是字符串
            real_name=provider.real_name,
            company_name=provider.company_name,
            contact_phone=provider.contact_phone,
            contact_email=provider.contact_email,
            contact_address=provider.contact_address,
            business_description=provider.business_description,
            service_categories=provider.service_categories,
            platform_accounts=provider.platform_accounts,
            portfolio_urls=provider.portfolio_urls,
            qualification_files=provider.qualification_files,  # 添加资质文件
            verification_status=provider.verification_status,  # 已经是字符串
            verification_time=provider.verification_time,
            verification_note=provider.verification_note,
            is_active=provider.is_active,
            service_rating=provider.service_rating,
            completed_orders=provider.completed_orders,
            created_at=provider.created_at,
            updated_at=provider.updated_at
        )
    
    def _validate_provider_fields(self, request: ChannelProfileRequest):
        """验证提供商字段"""
        if request.provider_type == "individual":
            if not request.real_name:
                raise ValidationError("个人提供商必须填写真实姓名")
        else:
            if not request.company_name:
                raise ValidationError("机构提供商必须填写公司名称")





    async def apply_withdraw(self, user_id: str, request: WithdrawRequest) -> Dict[str, Any]:
        """申请提现"""
        # 获取渠道商信息
        provider = await self._get_provider_by_user_id(user_id)

        # 检查可提现余额
        available_balance = await self._get_available_balance(provider.id)
        if request.amount > available_balance:
            raise ValidationError(f"提现金额超过可用余额，当前可提现金额：{available_balance}元")

        # 检查是否有待处理的提现申请
        pending_withdraw = await self.db.execute(
            select(ChannelWithdraw).where(
                and_(
                    ChannelWithdraw.provider_id == provider.id,
                    ChannelWithdraw.status.in_(["pending", "processing"])
                )
            )
        )
        if pending_withdraw.scalar_one_or_none():
            raise ValidationError("您有待处理的提现申请，请等待处理完成后再申请")

        # 创建提现记录
        withdraw = ChannelWithdraw(
            provider_id=provider.id,
            amount=request.amount,
            bank_account=request.bank_account,
            bank_name=request.bank_name,
            account_holder=request.account_holder,
            withdraw_reason=request.withdraw_reason,
            estimated_arrival=datetime.utcnow() + timedelta(days=2)  # 预计2个工作日到账
        )

        self.db.add(withdraw)
        await self.db.commit()
        await self.db.refresh(withdraw)

        return {
            "withdraw_id": str(withdraw.id),
            "amount": float(withdraw.amount),
            "status": withdraw.status,
            "applied_at": withdraw.applied_at.isoformat(),
            "estimated_arrival": withdraw.estimated_arrival.isoformat() if withdraw.estimated_arrival else None
        }

    async def get_withdraw_records(self, user_id: str, query: WithdrawQuery) -> Dict[str, Any]:
        """获取提现记录"""
        # 获取渠道商信息
        provider = await self._get_provider_by_user_id(user_id)

        # 构建查询条件
        conditions = [ChannelWithdraw.provider_id == provider.id]

        if query.status:
            conditions.append(ChannelWithdraw.status == query.status)

        # 获取提现记录
        stmt = select(ChannelWithdraw).where(and_(*conditions)).order_by(desc(ChannelWithdraw.applied_at))
        offset = (query.page - 1) * query.size
        stmt = stmt.offset(offset).limit(query.size)

        result = await self.db.execute(stmt)
        withdraws = result.scalars().all()

        # 获取总数
        count_stmt = select(func.count(ChannelWithdraw.id)).where(and_(*conditions))
        total_result = await self.db.execute(count_stmt)
        total = total_result.scalar()

        # 构建响应
        withdrawals = []
        for withdraw in withdraws:
            withdrawals.append({
                "withdraw_id": str(withdraw.id),
                "amount": float(withdraw.amount),
                "bank_account": withdraw.bank_account,
                "bank_name": withdraw.bank_name,
                "status": withdraw.status,
                "applied_at": withdraw.applied_at.isoformat(),
                "processed_at": withdraw.processed_at.isoformat() if withdraw.processed_at else None,
                "completed_at": withdraw.completed_at.isoformat() if withdraw.completed_at else None
            })

        return {
            "withdrawals": withdrawals,
            "pagination": {
                "page": query.page,
                "size": query.size,
                "total": total,
                "total_pages": (total + query.size - 1) // query.size
            }
        }

    async def _get_provider_by_user_id(self, user_id: str) -> ContentProvider:
        """根据用户ID获取渠道商信息"""
        result = await self.db.execute(
            select(ContentProvider).where(ContentProvider.user_id == user_id)
        )
        provider = result.scalar_one_or_none()
        if not provider:
            raise NotFoundError("渠道商信息不存在")
        return provider

    async def _get_available_balance(self, provider_id: str) -> Decimal:
        """获取可提现余额"""
        # 获取已结算但未提现的收益
        settled_stmt = select(func.sum(ChannelEarnings.net_amount)).where(
            and_(
                ChannelEarnings.provider_id == provider_id,
                ChannelEarnings.settlement_status == "settled"
            )
        )
        settled_result = await self.db.execute(settled_stmt)
        settled_amount = settled_result.scalar() or Decimal('0.00')

        # 获取已提现金额
        withdrawn_stmt = select(func.sum(ChannelWithdraw.amount)).where(
            and_(
                ChannelWithdraw.provider_id == provider_id,
                ChannelWithdraw.status.in_(["completed", "processing"])
            )
        )
        withdrawn_result = await self.db.execute(withdrawn_stmt)
        withdrawn_amount = withdrawn_result.scalar() or Decimal('0.00')

        return settled_amount - withdrawn_amount

    def _get_platform_name(self, platform_accounts: dict) -> str:
        """从平台账号信息获取平台名称"""
        if not platform_accounts:
            return "未知平台"

        platform_map = {
            "wechat": "微信公众号",
            "weibo": "微博",
            "douyin": "抖音",
            "xiaohongshu": "小红书"
        }

        for platform_type in platform_accounts.keys():
            if platform_type in platform_map:
                return platform_map[platform_type]

        return "其他平台"

    def _get_platform_type(self, platform_accounts: dict) -> str:
        """从平台账号信息获取平台类型"""
        if not platform_accounts:
            return "other"

        for platform_type in ["wechat", "weibo", "douyin", "xiaohongshu"]:
            if platform_type in platform_accounts:
                return platform_type

        return "other"

    def _get_content_types(self, service_categories: list) -> List[str]:
        """从服务类别获取内容类型"""
        if not service_categories:
            return ["article"]

        # 简化映射，实际应该有更复杂的逻辑
        content_types = []
        if any("文章" in cat or "图文" in cat for cat in service_categories):
            content_types.append("article")
        if any("视频" in cat for cat in service_categories):
            content_types.append("video")
        if any("图片" in cat or "海报" in cat for cat in service_categories):
            content_types.append("image")

        return content_types if content_types else ["article"]

    def _get_followers_count(self, platform_accounts: dict) -> Optional[int]:
        """从平台账号信息获取粉丝数量"""
        if not platform_accounts:
            return None

        # 简化处理，实际应该从平台账号详细信息获取
        return 50000  # 模拟数据

    # ==================== 渠道分类管理方法 ====================

    async def get_category_list(self, query: ChannelCategoryListQuery) -> Dict[str, Any]:
        """获取渠道分类列表"""
        # 构建查询条件
        conditions = []

        if query.is_active is not None:
            conditions.append(ChannelCategory.is_active == query.is_active)

        if query.parent_id is not None:
            conditions.append(ChannelCategory.parent_id == query.parent_id)

        if query.level is not None:
            conditions.append(ChannelCategory.category_level == query.level)

        if query.search:
            search_condition = or_(
                ChannelCategory.category_name.ilike(f"%{query.search}%"),
                ChannelCategory.category_description.ilike(f"%{query.search}%")
            )
            conditions.append(search_condition)

        # 构建排序
        valid_sort_fields = {
            'category_name': ChannelCategory.category_name,
            'category_code': ChannelCategory.category_code,
            'category_level': ChannelCategory.category_level,
            'sort_order': ChannelCategory.sort_order,
            'created_at': ChannelCategory.created_at,
            'updated_at': ChannelCategory.updated_at,
            'is_active': ChannelCategory.is_active
        }
        order_column = valid_sort_fields.get(query.sort_by, ChannelCategory.created_at)
        if query.sort_order == "desc":
            order_clause = desc(order_column)
        else:
            order_clause = asc(order_column)

        # 执行查询
        stmt = select(ChannelCategory).where(and_(*conditions)).order_by(order_clause)
        offset = (query.page - 1) * query.size
        stmt = stmt.offset(offset).limit(query.size)

        result = await self.db.execute(stmt)
        categories = result.scalars().all()

        # 获取总数
        count_stmt = select(func.count(ChannelCategory.id)).where(and_(*conditions))
        total_result = await self.db.execute(count_stmt)
        total = total_result.scalar()

        # 构建响应
        category_list = []
        for category in categories:
            category_data = ChannelCategoryResponse(
                id=str(category.id),
                category_name=category.category_name,
                category_code=category.category_code,
                category_description=category.category_description,
                parent_id=str(category.parent_id) if category.parent_id else None,
                category_level=getattr(category, 'category_level', 1),
                sort_order=getattr(category, 'sort_order', 0),
                is_active=category.is_active,
                created_at=category.created_at,
                updated_at=category.updated_at
            )
            category_list.append(category_data)

        return {
            "categories": category_list,
            "pagination": {
                "page": query.page,
                "size": query.size,
                "total": total,
                "total_pages": (total + query.size - 1) // query.size
            }
        }



    async def create_category(self, request: ChannelCategoryCreate) -> ChannelCategoryResponse:
        """创建渠道分类"""
        # 检查分类代码是否已存在
        existing_result = await self.db.execute(
            select(ChannelCategory).where(ChannelCategory.category_code == request.category_code)
        )
        if existing_result.scalar_one_or_none():
            raise ValidationError(f"分类代码 '{request.category_code}' 已存在")

        # 验证父分类（如果指定了parent_id）
        if hasattr(request, 'parent_id') and request.parent_id:
            parent_result = await self.db.execute(
                select(ChannelCategory).where(ChannelCategory.id == request.parent_id)
            )
            parent_category = parent_result.scalar_one_or_none()
            if not parent_category:
                raise ValidationError("指定的父分类不存在")
            if parent_category.category_level >= 2:
                raise ValidationError("不能在二级分类下创建子分类")

        # 创建分类
        category = ChannelCategory(
            category_name=request.category_name,
            category_code=request.category_code,
            category_description=getattr(request, 'category_description', None),
            parent_id=getattr(request, 'parent_id', None),
            category_level=getattr(request, 'category_level', 1),
            sort_order=getattr(request, 'sort_order', 0),
            is_active=getattr(request, 'is_active', True)
        )

        self.db.add(category)
        await self.db.commit()
        await self.db.refresh(category)

        return ChannelCategoryResponse(
            id=str(category.id),
            category_name=category.category_name,
            category_code=category.category_code,
            category_description=category.category_description,
            parent_id=str(category.parent_id) if category.parent_id else None,
            category_level=category.category_level,
            sort_order=category.sort_order,
            is_active=category.is_active,
            created_at=category.created_at,
            updated_at=category.updated_at
        )

    async def update_category(self, category_id: str, request: ChannelCategoryUpdate) -> ChannelCategoryResponse:
        """更新渠道分类"""
        # 获取分类
        result = await self.db.execute(
            select(ChannelCategory).where(ChannelCategory.id == category_id)
        )
        category = result.scalar_one_or_none()
        if not category:
            raise NotFoundError("分类不存在")

        # 更新字段
        if request.category_name is not None:
            category.category_name = request.category_name
        if request.category_description is not None:
            category.category_description = request.category_description
        if hasattr(request, 'parent_id') and request.parent_id is not None:
            # 验证父分类
            if request.parent_id:
                parent_result = await self.db.execute(
                    select(ChannelCategory).where(ChannelCategory.id == request.parent_id)
                )
                parent_category = parent_result.scalar_one_or_none()
                if not parent_category:
                    raise ValidationError("指定的父分类不存在")
                if parent_category.category_level >= 2:
                    raise ValidationError("不能在二级分类下创建子分类")
            category.parent_id = request.parent_id
        if hasattr(request, 'category_level') and request.category_level is not None:
            category.category_level = request.category_level
        if hasattr(request, 'sort_order') and request.sort_order is not None:
            category.sort_order = request.sort_order
        if request.is_active is not None:
            category.is_active = request.is_active

        category.updated_at = datetime.utcnow()

        await self.db.commit()
        await self.db.refresh(category)

        # 获取渠道商数量
        # 需要通过 ChannelService 表连接
        provider_count_stmt = select(
            func.count(func.distinct(ChannelCategoryMapping.provider_id))
        ).select_from(
            ChannelCategoryMapping.__table__.join(ChannelServiceModel.__table__, ChannelCategoryMapping.service_id == ChannelServiceModel.id)
        ).where(
            and_(
                ChannelServiceModel.category_id == category.id,
                ChannelCategoryMapping.is_active == True,
                ChannelServiceModel.is_active == True
            )
        )
        provider_count_result = await self.db.execute(provider_count_stmt)
        provider_count = provider_count_result.scalar() or 0

        return ChannelCategoryResponse(
            id=str(category.id),
            category_name=category.category_name,
            category_code=category.category_code,
            category_description=category.category_description,
            parent_id=str(category.parent_id) if category.parent_id else None,
            category_level=category.category_level,
            sort_order=category.sort_order,
            is_active=category.is_active,
            created_at=category.created_at,
            updated_at=category.updated_at
        )

    async def get_category_detail(self, category_id: str) -> ChannelCategoryResponse:
        """获取渠道分类详情"""
        # 获取分类基本信息
        result = await self.db.execute(
            select(ChannelCategory).where(ChannelCategory.id == category_id)
        )
        category = result.scalar_one_or_none()
        if not category:
            raise NotFoundError("分类不存在")

        return ChannelCategoryResponse(
            id=str(category.id),
            category_name=category.category_name,
            category_code=category.category_code,
            category_description=category.category_description,
            is_active=category.is_active,
            created_at=category.created_at,
            updated_at=category.updated_at
        )

    async def delete_category(self, category_id: str) -> bool:
        """删除渠道分类"""
        # 获取分类
        result = await self.db.execute(
            select(ChannelCategory).where(ChannelCategory.id == category_id)
        )
        category = result.scalar_one_or_none()
        if not category:
            raise NotFoundError("分类不存在")

        # 由于已删除层级结构，不再需要检查子分类

        # 检查是否有渠道商使用该分类
        # 需要通过 ChannelService 表连接检查
        mapping_result = await self.db.execute(
            select(ChannelCategoryMapping).select_from(
                ChannelCategoryMapping.__table__.join(ChannelServiceModel.__table__, ChannelCategoryMapping.service_id == ChannelServiceModel.id)
            ).where(ChannelServiceModel.category_id == category_id)
        )
        if mapping_result.scalars().first():
            raise ValidationError("该分类下还有渠道商，无法删除")

        # 删除分类
        await self.db.delete(category)
        await self.db.commit()

        return True

    # ==================== 渠道商分类关联管理方法 ====================
    # 所有渠道商分类关联管理方法已删除

    # ==================== 渠道服务管理方法 ====================

    async def create_service(self, user_id: str, request: ChannelServiceCreate) -> ChannelServiceResponse:
        """创建服务"""
        # 验证分类是否存在
        category_result = await self.db.execute(
            select(ChannelCategory).where(ChannelCategory.id == request.category_id)
        )
        category = category_result.scalar_one_or_none()
        if not category:
            raise NotFoundError("分类不存在")

        if not category.is_active:
            raise ValidationError("该分类已被禁用")

        # 检查服务代码是否重复
        existing_result = await self.db.execute(
            select(ChannelServiceModel).where(
                ChannelServiceModel.service_code == request.service_code
            )
        )
        if existing_result.scalar_one_or_none():
            raise ValidationError(f"服务代码 '{request.service_code}' 已存在")

        # 创建平台服务（管理员专用）
        provider_id = None
        approval_status = 'approved'  # 平台服务直接通过审核
        service_source = 'platform'

        # 创建服务
        service = ChannelServiceModel(
            category_id=request.category_id,
            service_source=service_source,
            provider_id=provider_id,
            approval_status=approval_status,
            service_name=request.service_name,
            service_code=request.service_code,
            service_description=request.service_description,
            service_features=request.service_features,
            base_price=request.base_price,
            price_unit=request.price_unit,
            channel_type=request.channel_type.value if request.channel_type else None,
            is_active=request.is_active
        )

        self.db.add(service)
        await self.db.commit()
        await self.db.refresh(service)

        return ChannelServiceResponse(
            id=str(service.id),
            category_id=str(service.category_id),
            service_source=service.service_source,
            provider_id=str(service.provider_id) if service.provider_id else None,
            approval_status=service.approval_status,
            approval_note=service.approval_note,
            approval_time=service.approval_time,
            approver_id=str(service.approver_id) if service.approver_id else None,
            service_name=service.service_name,
            service_code=service.service_code,
            service_description=service.service_description,
            service_features=service.service_features,
            base_price=service.base_price,
            price_unit=service.price_unit,
            channel_type=service.channel_type,
            is_active=service.is_active,
            created_at=service.created_at,
            updated_at=service.updated_at,
            category=ChannelCategoryResponse(
                id=str(category.id),
                category_name=category.category_name,
                category_code=category.category_code,
                category_description=category.category_description,
                is_active=category.is_active,
                created_at=category.created_at,
                updated_at=category.updated_at
            )
        )

    async def create_provider_service(self, user_id: str, request: ChannelServiceCreate) -> ChannelServiceResponse:
        """渠道商创建服务"""
        # 验证分类是否存在
        category_result = await self.db.execute(
            select(ChannelCategory).where(ChannelCategory.id == request.category_id)
        )
        category = category_result.scalar_one_or_none()
        if not category:
            raise NotFoundError("分类不存在")

        if not category.is_active:
            raise ValidationError("该分类已被禁用")

        # 检查服务代码是否重复
        existing_result = await self.db.execute(
            select(ChannelServiceModel).where(
                ChannelServiceModel.service_code == request.service_code
            )
        )
        if existing_result.scalar_one_or_none():
            raise ValidationError(f"服务代码 '{request.service_code}' 已存在")

        # 获取渠道商信息
        provider = await self._get_provider_by_user_id(user_id)

        # 创建渠道商服务
        service = ChannelServiceModel(
            category_id=request.category_id,
            service_source='provider',  # 渠道商服务
            provider_id=provider.id,
            approval_status='pending',  # 渠道商创建的服务需要审核
            service_name=request.service_name,
            service_code=request.service_code,
            service_description=request.service_description,
            service_features=request.service_features,
            base_price=request.base_price,
            price_unit=request.price_unit,
            channel_type=request.channel_type.value if request.channel_type else None,
            is_active=False  # 渠道商创建的服务在审批通过前默认禁用
        )

        self.db.add(service)
        await self.db.commit()
        await self.db.refresh(service)

        return ChannelServiceResponse(
            id=str(service.id),
            category_id=str(service.category_id),
            service_source=service.service_source,
            provider_id=str(service.provider_id) if service.provider_id else None,
            approval_status=service.approval_status,
            approval_note=service.approval_note,
            approval_time=service.approval_time,
            approver_id=str(service.approver_id) if service.approver_id else None,
            service_name=service.service_name,
            service_code=service.service_code,
            service_description=service.service_description,
            service_features=service.service_features,
            base_price=service.base_price,
            price_unit=service.price_unit,
            channel_type=service.channel_type,
            is_active=service.is_active,
            created_at=service.created_at,
            updated_at=service.updated_at,
            category=ChannelCategoryResponse(
                id=str(category.id),
                category_name=category.category_name,
                category_code=category.category_code,
                category_description=category.category_description,
                is_active=category.is_active,
                created_at=category.created_at,
                updated_at=category.updated_at
            )
        )

    async def get_pending_services_list(self, query: ChannelServiceListQuery) -> Dict[str, Any]:
        """获取待审批服务列表（管理员）"""
        # 构建查询条件 - 只查询渠道商创建的待审批服务
        conditions = [
            ChannelServiceModel.service_source == 'provider',
            ChannelServiceModel.approval_status == 'pending'
        ]

        if query.category_id:
            try:
                category_uuid = uuid.UUID(query.category_id)
                conditions.append(ChannelServiceModel.category_id == category_uuid)
            except ValueError:
                pass

        if query.search:
            search_condition = or_(
                ChannelServiceModel.service_name.ilike(f"%{query.search}%"),
                ChannelServiceModel.service_description.ilike(f"%{query.search}%"),
                ChannelServiceModel.service_code.ilike(f"%{query.search}%")
            )
            conditions.append(search_condition)

        # 构建查询
        base_query = select(
            ChannelServiceModel, ChannelCategory, ContentProvider
        ).select_from(
            ChannelServiceModel.__table__.join(
                ChannelCategory, ChannelServiceModel.category_id == ChannelCategory.id
            ).join(
                ContentProvider, ChannelServiceModel.provider_id == ContentProvider.id
            )
        ).where(and_(*conditions))

        # 排序
        sort_column = getattr(ChannelServiceModel, query.sort_by, ChannelServiceModel.created_at)
        if query.sort_order == "desc":
            order_func = desc
        else:
            order_func = asc
        base_query = base_query.order_by(order_func(sort_column))

        # 获取总数
        count_stmt = select(func.count(ChannelServiceModel.id)).where(and_(*conditions))
        total_result = await self.db.execute(count_stmt)
        total = total_result.scalar()

        # 分页
        offset = (query.page - 1) * query.size
        base_query = base_query.offset(offset).limit(query.size)

        # 执行查询
        result = await self.db.execute(base_query)
        rows = result.fetchall()

        # 构建响应
        items = []
        for row in rows:
            service = row[0]  # ChannelServiceModel
            category = row[1]  # ChannelCategory
            provider = row[2]  # ContentProvider

            service_response = ChannelServiceResponse(
                id=str(service.id),
                category_id=str(service.category_id),
                service_source=service.service_source,
                provider_id=str(service.provider_id) if service.provider_id else None,
                approval_status=service.approval_status,
                approval_note=service.approval_note,
                approval_time=service.approval_time,
                approver_id=str(service.approver_id) if service.approver_id else None,
                service_name=service.service_name,
                service_code=service.service_code,
                service_description=service.service_description,
                service_features=service.service_features,
                base_price=service.base_price,
                discount_price=service.discount_price,
                price_unit=service.price_unit,
                service_specs=service.service_specs,
                delivery_time=service.delivery_time,
                revision_count=service.revision_count,
                channel_type=service.channel_type,
                portal_type=service.portal_type,
                platform_specs=service.platform_specs,
                coverage_area=service.coverage_area,
                is_active=service.is_active,
                created_at=service.created_at,
                updated_at=service.updated_at,
                category=ChannelCategoryResponse(
                    id=str(category.id),
                    category_name=category.category_name,
                    category_code=category.category_code,
                    category_description=category.category_description,
                    is_active=category.is_active,
                    created_at=category.created_at,
                    updated_at=category.updated_at
                ),
                creator_info={
                    "provider_id": str(provider.id),
                    "provider_name": provider.provider_name,
                    "provider_type": provider.provider_type,
                    "company_name": provider.company_name,
                    "contact_email": provider.contact_email
                }
            )
            items.append(service_response)

        return {
            "items": items,
            "pagination": {
                "page": query.page,
                "size": query.size,
                "total": total,
                "pages": (total + query.size - 1) // query.size
            }
        }

    async def approve_service(self, service_id: str, approver_id: str, request: ChannelServiceApprovalRequest) -> ChannelServiceResponse:
        """审核服务"""
        # 获取服务
        result = await self.db.execute(
            select(ChannelServiceModel).where(ChannelServiceModel.id == service_id)
        )
        service = result.scalar_one_or_none()
        if not service:
            raise NotFoundError("服务不存在")

        # 只有渠道商创建的服务才需要审核
        if service.service_source != 'provider':
            raise ValidationError("只有渠道商创建的服务才需要审核")

        # 只有待审核状态的服务才能审核
        if service.approval_status != 'pending':
            raise ValidationError("该服务已经审核过了")

        # 更新审核信息
        service.approval_status = request.status
        service.approval_note = request.note
        service.approval_time = datetime.utcnow()
        service.approver_id = approver_id
        service.updated_at = datetime.utcnow()

        # 如果审核通过，自动启用服务并为创建该服务的渠道商绑定该服务
        if request.status == 'approved':
            service.is_active = True  # 审批通过后自动启用服务

            if service.provider_id:
                # 检查是否已经存在绑定关系
                existing_mapping = await self.db.execute(
                    select(ChannelCategoryMapping).where(
                        and_(
                            ChannelCategoryMapping.provider_id == service.provider_id,
                            ChannelCategoryMapping.service_id == service.id
                        )
                    )
                )
                if not existing_mapping.scalar_one_or_none():
                    # 创建绑定关系
                    mapping = ChannelCategoryMapping(
                        provider_id=service.provider_id,
                        service_id=service.id,
                        is_active=True
                    )
                    self.db.add(mapping)

        await self.db.commit()
        await self.db.refresh(service)

        return ChannelServiceResponse(
            id=str(service.id),
            category_id=str(service.category_id),
            service_source=service.service_source,
            provider_id=str(service.provider_id) if service.provider_id else None,
            approval_status=service.approval_status,
            approval_note=service.approval_note,
            approval_time=service.approval_time,
            approver_id=str(service.approver_id) if service.approver_id else None,
            service_name=service.service_name,
            service_code=service.service_code,
            service_description=service.service_description,
            service_features=service.service_features,
            base_price=service.base_price,
            discount_price=service.discount_price,
            price_unit=service.price_unit,
            service_specs=service.service_specs,
            delivery_time=service.delivery_time,
            revision_count=service.revision_count,
            channel_type=service.channel_type,
            portal_type=service.portal_type,
            platform_specs=service.platform_specs,
            coverage_area=service.coverage_area,
            is_active=service.is_active,
            created_at=service.created_at,
            updated_at=service.updated_at
        )

    async def update_service(self, user_id: str, service_id: str, request: ChannelServiceUpdate) -> ChannelServiceResponse:
        """更新渠道服务（管理员版本）"""
        # 直接获取服务信息，不需要验证渠道商
        service_result = await self.db.execute(
            select(ChannelServiceModel, ChannelCategory).join(
                ChannelCategory, ChannelServiceModel.category_id == ChannelCategory.id
            ).where(ChannelServiceModel.id == service_id)
        )
        service_data = service_result.first()
        if not service_data:
            raise NotFoundError("服务不存在")

        service, category = service_data

        # 检查服务代码是否重复（如果要更新服务代码）
        update_data = request.model_dump(exclude_unset=True)
        if 'service_code' in update_data and update_data['service_code'] != service.service_code:
            existing_result = await self.db.execute(
                select(ChannelServiceModel).where(
                    and_(
                        ChannelServiceModel.service_code == update_data['service_code'],
                        ChannelServiceModel.id != service_id
                    )
                )
            )
            if existing_result.scalar_one_or_none():
                raise ValidationError(f"服务代码 '{update_data['service_code']}' 已存在")

        # 验证分类是否存在（如果要更新分类）
        if 'category_id' in update_data and update_data['category_id']:
            category_result = await self.db.execute(
                select(ChannelCategory).where(ChannelCategory.id == update_data['category_id'])
            )
            category = category_result.scalar_one_or_none()
            if not category:
                raise NotFoundError("分类不存在")
            if not category.is_active:
                raise ValidationError("该分类已被禁用")
            # 由于已删除层级结构，不再需要检查is_leaf

        # 更新字段
        for field, value in update_data.items():
            if hasattr(service, field):
                setattr(service, field, value)

        service.updated_at = datetime.utcnow()

        await self.db.commit()
        await self.db.refresh(service)

        return ChannelServiceResponse(
            id=str(service.id),
            category_id=str(service.category_id),
            service_source=service.service_source,
            provider_id=str(service.provider_id) if service.provider_id else None,
            approval_status=service.approval_status,
            approval_note=service.approval_note,
            approval_time=service.approval_time,
            approver_id=str(service.approver_id) if service.approver_id else None,
            service_name=service.service_name,
            service_code=service.service_code,
            service_description=service.service_description,
            service_features=service.service_features,
            base_price=service.base_price,
            discount_price=service.discount_price,
            price_unit=service.price_unit,
            service_specs=service.service_specs,
            delivery_time=service.delivery_time,
            revision_count=service.revision_count,
            channel_type=service.channel_type,
            portal_type=service.portal_type,
            platform_specs=service.platform_specs,
            coverage_area=service.coverage_area,
            is_active=service.is_active,
            created_at=service.created_at,
            updated_at=service.updated_at,
            category=ChannelCategoryResponse(
                id=str(category.id),
                category_name=category.category_name,
                category_code=category.category_code,
                category_description=category.category_description,
                is_active=category.is_active,
                created_at=category.created_at,
                updated_at=category.updated_at
            )
        )

    # ==================== 渠道商专用服务管理方法 ====================

    async def get_my_created_services(self, user_id: str, query: ChannelServiceListQuery, approval_status: Optional[str] = None) -> Dict[str, Any]:
        """渠道商获取自己创建的服务列表"""
        # 获取渠道商信息
        provider = await self._get_provider_by_user_id(user_id)

        # 构建查询条件
        conditions = [
            ChannelServiceModel.service_source == 'provider',
            ChannelServiceModel.provider_id == provider.id
        ]

        if query.category_id:
            try:
                category_uuid = uuid.UUID(query.category_id)
                conditions.append(ChannelServiceModel.category_id == category_uuid)
            except ValueError:
                pass

        if approval_status:
            conditions.append(ChannelServiceModel.approval_status == approval_status)

        if query.is_active is not None:
            conditions.append(ChannelServiceModel.is_active == query.is_active)

        if query.search:
            search_condition = or_(
                ChannelServiceModel.service_name.ilike(f"%{query.search}%"),
                ChannelServiceModel.service_description.ilike(f"%{query.search}%"),
                ChannelServiceModel.service_code.ilike(f"%{query.search}%")
            )
            conditions.append(search_condition)

        # 构建查询
        base_query = select(
            ChannelServiceModel, ChannelCategory
        ).select_from(
            ChannelServiceModel.__table__.join(
                ChannelCategory, ChannelServiceModel.category_id == ChannelCategory.id
            )
        ).where(and_(*conditions))

        # 排序
        sort_column = getattr(ChannelServiceModel, query.sort_by, ChannelServiceModel.created_at)
        if query.sort_order == "desc":
            order_func = desc
        else:
            order_func = asc
        base_query = base_query.order_by(order_func(sort_column))

        # 获取总数
        count_stmt = select(func.count(ChannelServiceModel.id)).where(and_(*conditions))
        total_result = await self.db.execute(count_stmt)
        total = total_result.scalar()

        # 分页
        offset = (query.page - 1) * query.size
        base_query = base_query.offset(offset).limit(query.size)

        # 执行查询
        result = await self.db.execute(base_query)
        rows = result.fetchall()

        # 构建响应
        items = []
        for row in rows:
            service = row[0]  # ChannelServiceModel
            category = row[1]  # ChannelCategory

            service_response = ChannelServiceResponse(
                id=str(service.id),
                category_id=str(service.category_id),
                service_source=service.service_source,
                provider_id=str(service.provider_id) if service.provider_id else None,
                approval_status=service.approval_status,
                approval_note=service.approval_note,
                approval_time=service.approval_time,
                approver_id=str(service.approver_id) if service.approver_id else None,
                service_name=service.service_name,
                service_code=service.service_code,
                service_description=service.service_description,
                service_features=service.service_features,
                base_price=service.base_price,
                discount_price=service.discount_price,
                price_unit=service.price_unit,
                service_specs=service.service_specs,
                delivery_time=service.delivery_time,
                revision_count=service.revision_count,
                channel_type=service.channel_type,
                portal_type=service.portal_type,
                platform_specs=service.platform_specs,
                coverage_area=service.coverage_area,
                is_active=service.is_active,
                created_at=service.created_at,
                updated_at=service.updated_at,
                category=ChannelCategoryResponse(
                    id=str(category.id),
                    category_name=category.category_name,
                    category_code=category.category_code,
                    category_description=category.category_description,
                    is_active=category.is_active,
                    created_at=category.created_at,
                    updated_at=category.updated_at
                )
            )
            items.append(service_response)

        return {
            "items": items,
            "pagination": {
                "page": query.page,
                "size": query.size,
                "total": total,
                "pages": (total + query.size - 1) // query.size
            }
        }

    async def update_my_service(self, user_id: str, service_id: str, request: ChannelServiceUpdate) -> ChannelServiceResponse:
        """渠道商更新自己创建的服务"""
        # 获取渠道商信息
        provider = await self._get_provider_by_user_id(user_id)

        # 获取服务
        result = await self.db.execute(
            select(ChannelServiceModel).where(
                and_(
                    ChannelServiceModel.id == service_id,
                    ChannelServiceModel.provider_id == provider.id,
                    ChannelServiceModel.service_source == 'provider'
                )
            )
        )
        service = result.scalar_one_or_none()
        if not service:
            raise NotFoundError("服务不存在或您没有权限修改")

        # 如果服务已审核通过，限制某些字段的修改
        if service.approval_status == 'approved':
            # 审核通过的服务不能修改核心字段
            restricted_fields = ['service_code', 'category_id']
            for field in restricted_fields:
                if hasattr(request, field) and getattr(request, field) is not None:
                    raise ValidationError(f"已审核通过的服务不能修改{field}字段")

        # 更新字段
        update_data = request.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            if hasattr(service, field) and value is not None:
                setattr(service, field, value)

        service.updated_at = datetime.utcnow()

        await self.db.commit()
        await self.db.refresh(service)

        # 获取分类信息
        category_result = await self.db.execute(
            select(ChannelCategory).where(ChannelCategory.id == service.category_id)
        )
        category = category_result.scalar_one()

        return await self._build_channel_service_response(service, category)

    async def _build_channel_service_response(self, service: ChannelServiceModel, category: ChannelCategory) -> ChannelServiceResponse:
        """构建渠道服务响应数据"""
        return ChannelServiceResponse(
            id=str(service.id),
            provider_id=str(service.provider_id) if service.provider_id else None,
            category_id=str(service.category_id),
            service_source=service.service_source,
            approval_status=service.approval_status,
            approval_note=service.approval_note,
            approval_time=service.approval_time,
            approver_id=str(service.approver_id) if service.approver_id else None,
            service_name=service.service_name,
            service_code=service.service_code,
            service_description=service.service_description,
            service_features=service.service_features,
            base_price=service.base_price,
            discount_price=service.discount_price,
            price_unit=service.price_unit,
            service_specs=service.service_specs,
            delivery_time=service.delivery_time,
            revision_count=service.revision_count,
            channel_type=service.channel_type,
            portal_type=service.portal_type,
            platform_specs=service.platform_specs,
            coverage_area=service.coverage_area,
            is_active=service.is_active,
            created_at=service.created_at,
            updated_at=service.updated_at,
            category=ChannelCategoryResponse(
                id=str(category.id),
                category_name=category.category_name,
                category_code=category.category_code,
                category_description=category.category_description,
                is_active=category.is_active,
                created_at=category.created_at,
                updated_at=category.updated_at
            )
        )

    async def get_my_service_detail(self, user_id: str, service_id: str) -> ChannelServiceResponse:
        """渠道商获取自己创建的服务详情"""
        # 获取渠道商信息
        provider = await self._get_provider_by_user_id(user_id)

        # 获取服务
        result = await self.db.execute(
            select(ChannelServiceModel, ChannelCategory).select_from(
                ChannelServiceModel.__table__.join(
                    ChannelCategory, ChannelServiceModel.category_id == ChannelCategory.id
                )
            ).where(
                and_(
                    ChannelServiceModel.id == service_id,
                    ChannelServiceModel.provider_id == provider.id,
                    ChannelServiceModel.service_source == 'provider'
                )
            )
        )
        row = result.first()
        if not row:
            raise NotFoundError("服务不存在或您没有权限查看")

        service, category = row
        return await self._build_channel_service_response(service, category)

    async def delete_my_service(self, user_id: str, service_id: str) -> None:
        """渠道商删除自己创建的服务"""
        # 获取渠道商信息
        provider = await self._get_provider_by_user_id(user_id)

        # 获取服务
        result = await self.db.execute(
            select(ChannelServiceModel).where(
                and_(
                    ChannelServiceModel.id == service_id,
                    ChannelServiceModel.provider_id == provider.id,
                    ChannelServiceModel.service_source == 'provider'
                )
            )
        )
        service = result.scalar_one_or_none()
        if not service:
            raise NotFoundError("服务不存在或您没有权限删除")

        # 检查是否可以删除
        if service.approval_status == 'approved':
            # 检查是否有订单使用该服务
            # 这里可以添加订单检查逻辑
            pass

        # 删除服务
        await self.db.delete(service)
        await self.db.commit()

    async def delete_service(self, user_id: str, service_id: str) -> bool:
        """删除渠道服务（管理员版本）"""
        # 直接获取服务信息，不需要验证渠道商
        service_result = await self.db.execute(
            select(ChannelServiceModel).where(ChannelServiceModel.id == service_id)
        )
        service = service_result.scalar_one_or_none()
        if not service:
            raise NotFoundError("服务不存在")

        # 检查是否有关联的分类映射
        mapping_result = await self.db.execute(
            select(ChannelCategoryMapping).where(ChannelCategoryMapping.service_id == service_id)
        )
        if mapping_result.scalar_one_or_none():
            raise ValidationError("该服务已被分类关联使用，无法删除")

        # 删除服务
        await self.db.delete(service)
        await self.db.commit()

        return True

    async def get_service_detail(self, user_id: str, service_id: str) -> ChannelServiceResponse:
        """获取服务详情（渠道商版本）"""
        # 获取渠道商信息
        provider = await self._get_provider_by_user_id(user_id)

        # 获取服务详情（包含分类信息）
        service_result = await self.db.execute(
            select(ChannelServiceModel, ChannelCategory).join(
                ChannelCategory, ChannelServiceModel.category_id == ChannelCategory.id
            ).where(
                and_(
                    ChannelServiceModel.id == service_id,
                    ChannelServiceModel.provider_id == provider.id
                )
            )
        )
        service_data = service_result.first()
        if not service_data:
            raise NotFoundError("服务不存在")

        service, category = service_data

        return await self._build_channel_service_response(service, category)

    async def get_admin_service_detail(self, service_id: str) -> ChannelServiceResponse:
        """获取服务详情（管理员版本 - 不限制provider_id）"""
        # 获取服务详情（包含分类信息）
        service_result = await self.db.execute(
            select(ChannelServiceModel, ChannelCategory).join(
                ChannelCategory, ChannelServiceModel.category_id == ChannelCategory.id
            ).where(ChannelServiceModel.id == service_id)
        )
        service_data = service_result.first()
        if not service_data:
            raise NotFoundError("服务不存在")

        service, category = service_data

        return ChannelServiceResponse(
            id=str(service.id),
            provider_id=str(service.provider_id) if service.provider_id else None,
            category_id=str(service.category_id),
            service_source=service.service_source,
            approval_status=service.approval_status,
            approval_note=service.approval_note,
            approval_time=service.approval_time,
            approver_id=str(service.approver_id) if service.approver_id else None,
            service_name=service.service_name,
            service_code=service.service_code,
            service_description=service.service_description,
            service_features=service.service_features,
            base_price=service.base_price,
            discount_price=service.discount_price,
            price_unit=service.price_unit,
            service_specs=service.service_specs,
            delivery_time=service.delivery_time,
            revision_count=service.revision_count,
            channel_type=service.channel_type,
            portal_type=service.portal_type,
            platform_specs=service.platform_specs,
            coverage_area=service.coverage_area,
            is_active=service.is_active,
            created_at=service.created_at,
            updated_at=service.updated_at,
            category=ChannelCategoryResponse(
                id=str(category.id),
                category_name=category.category_name,
                category_code=category.category_code,
                category_description=category.category_description,
                is_active=category.is_active,
                created_at=category.created_at,
                updated_at=category.updated_at
            )
        )

    async def get_service_list(self, user_id: str, query: ChannelServiceListQuery) -> Dict[str, Any]:
        """获取渠道商的服务列表"""
        # 获取渠道商信息
        provider = await self._get_provider_by_user_id(user_id)

        # 构建查询条件
        conditions = [ChannelServiceModel.provider_id == provider.id]

        if query.category_id:
            try:
                category_uuid = uuid.UUID(query.category_id)
                conditions.append(ChannelServiceModel.category_id == category_uuid)
            except ValueError:
                # 如果category_id不是有效的UUID，跳过此条件
                pass

        if query.portal_type:
            conditions.append(ChannelServiceModel.portal_type == query.portal_type)

        # 处理覆盖区域筛选
        if query.tags:  # 保持查询参数名不变，但实际筛选覆盖区域
            tag_list = [tag.strip() for tag in query.tags.split(',') if tag.strip()]
            if tag_list:
                # 使用JSON_CONTAINS函数检查是否包含指定区域
                tag_conditions = []
                for tag in tag_list:
                    # 使用多种匹配方式确保能找到区域
                    tag_conditions.extend([
                        # 精确匹配带引号的区域
                        ChannelServiceModel.coverage_area.cast(String).like(f'%"{tag}"%'),
                        # 模糊匹配不带引号的区域（防止JSON格式问题）
                        ChannelServiceModel.coverage_area.cast(String).like(f'%{tag}%')
                    ])
                if tag_conditions:
                    conditions.append(or_(*tag_conditions))

        if query.is_active is not None:
            conditions.append(ChannelServiceModel.is_active == query.is_active)

        if query.min_price is not None:
            conditions.append(ChannelServiceModel.base_price >= query.min_price)

        if query.max_price is not None:
            conditions.append(ChannelServiceModel.base_price <= query.max_price)

        if query.search:
            search_condition = or_(
                ChannelServiceModel.service_name.ilike(f"%{query.search}%"),
                ChannelServiceModel.service_description.ilike(f"%{query.search}%"),
                ChannelServiceModel.service_code.ilike(f"%{query.search}%")
            )
            conditions.append(search_condition)

        # 构建基础查询
        base_query = select(ChannelServiceModel, ChannelCategory).join(
            ChannelCategory, ChannelServiceModel.category_id == ChannelCategory.id
        ).where(and_(*conditions))

        # 排序
        sort_column = getattr(ChannelServiceModel, query.sort_by, ChannelServiceModel.created_at)
        if query.sort_order == "desc":
            order_func = desc
        else:
            order_func = asc
        base_query = base_query.order_by(order_func(sort_column))

        # 分页
        offset = (query.page - 1) * query.size
        paginated_query = base_query.offset(offset).limit(query.size)

        # 执行查询
        result = await self.db.execute(paginated_query)
        services_data = result.all()

        # 获取总数
        count_query = select(func.count(ChannelServiceModel.id)).where(and_(*conditions))
        total_result = await self.db.execute(count_query)
        total = total_result.scalar()

        # 构建响应数据
        items = []
        for service, category in services_data:
            service_response = ChannelServiceResponse(
                id=str(service.id),
                provider_id=str(service.provider_id),
                category_id=str(service.category_id),
                service_name=service.service_name,
                service_code=service.service_code,
                service_description=service.service_description,
                service_features=service.service_features,
                base_price=service.base_price,
                discount_price=service.discount_price,
                price_unit=service.price_unit,
                service_specs=service.service_specs,
                delivery_time=service.delivery_time,
                revision_count=service.revision_count,
                portal_type=service.portal_type,
                platform_specs=service.platform_specs,
                tags=service.tags,
                is_active=service.is_active,
                created_at=service.created_at,
                updated_at=service.updated_at,
                category=ChannelCategoryResponse(
                    id=str(category.id),
                    category_name=category.category_name,
                    category_code=category.category_code,
                    category_description=category.category_description,
                    is_active=category.is_active,
                    created_at=category.created_at,
                    updated_at=category.updated_at
                )
            )
            items.append(service_response)

        return {
            "items": items,
            "pagination": {
                "page": query.page,
                "size": query.size,
                "total": total,
                "pages": (total + query.size - 1) // query.size
            }
        }

    async def get_all_services_list(self, query: ChannelServiceListQuery) -> Dict[str, Any]:
        """获取所有渠道服务列表（管理员视图）"""
        try:
            # 先检查数据库中是否有数据
            count_result = await self.db.execute(select(func.count(ChannelServiceModel.id)))
            total_count = count_result.scalar() or 0
            print(f"数据库中总共有 {total_count} 条服务记录")

            # 如果没有数据，直接返回空结果
            if total_count == 0:
                return {
                    "items": [],
                    "pagination": {
                        "page": query.page,
                        "size": query.size,
                        "total": 0,
                        "total_pages": 0
                    }
                }

            # 构建查询条件
            conditions = []

            # 只显示已审批通过的服务
            conditions.append(ChannelServiceModel.approval_status == 'approved')

            if query.category_id:
                try:
                    category_uuid = uuid.UUID(query.category_id)
                    conditions.append(ChannelServiceModel.category_id == category_uuid)
                except ValueError:
                    # 如果category_id不是有效的UUID，跳过此条件
                    pass

            if query.portal_type:
                conditions.append(ChannelServiceModel.portal_type == query.portal_type)

            if query.channel_type:
                conditions.append(ChannelServiceModel.channel_type == query.channel_type)

            # 处理coverage_area筛选
            if query.coverage_area:
                area_list = [area.strip() for area in query.coverage_area.split(',') if area.strip()]
                if area_list:
                    # 使用JSON_CONTAINS函数检查是否包含指定区域
                    area_conditions = []
                    for area in area_list:
                        # 使用多种匹配方式确保能找到区域
                        area_conditions.extend([
                            # 精确匹配带引号的区域
                            ChannelServiceModel.coverage_area.cast(String).like(f'%"{area}"%'),
                            # 模糊匹配不带引号的区域（防止JSON格式问题）
                            ChannelServiceModel.coverage_area.cast(String).like(f'%{area}%')
                        ])
                    if area_conditions:
                        conditions.append(or_(*area_conditions))

            if query.is_active is not None:
                conditions.append(ChannelServiceModel.is_active == query.is_active)

            if query.min_price is not None:
                conditions.append(ChannelServiceModel.base_price >= query.min_price)

            if query.max_price is not None:
                conditions.append(ChannelServiceModel.base_price <= query.max_price)

            if query.max_delivery_time is not None:
                conditions.append(ChannelServiceModel.delivery_time <= query.max_delivery_time)

            # 搜索条件
            if query.search:
                search_condition = or_(
                    ChannelServiceModel.service_name.ilike(f"%{query.search}%"),
                    ChannelServiceModel.service_description.ilike(f"%{query.search}%"),
                    ChannelServiceModel.service_code.ilike(f"%{query.search}%")
                )
                conditions.append(search_condition)

            # 构建基础查询，使用LEFT JOIN以包含关联数据
            base_query = select(
                ChannelServiceModel,
                ChannelCategory.category_name
            ).outerjoin(
                ChannelCategory, ChannelServiceModel.category_id == ChannelCategory.id
            )

            if conditions:
                base_query = base_query.where(and_(*conditions))

            # 排序
            if query.sort_by == "created_at":
                order_column = ChannelServiceModel.created_at
            elif query.sort_by == "updated_at":
                order_column = ChannelServiceModel.updated_at
            elif query.sort_by == "service_name":
                order_column = ChannelServiceModel.service_name
            elif query.sort_by == "base_price":
                order_column = ChannelServiceModel.base_price
            else:
                order_column = ChannelServiceModel.created_at

            if query.sort_order == "desc":
                base_query = base_query.order_by(desc(order_column))
            else:
                base_query = base_query.order_by(asc(order_column))

            # 获取总数
            count_query = select(func.count(ChannelServiceModel.id))

            if conditions:
                count_query = count_query.where(and_(*conditions))

            total_result = await self.db.execute(count_query)
            total = total_result.scalar() or 0

            # 分页查询
            offset = (query.page - 1) * query.size
            paginated_query = base_query.offset(offset).limit(query.size)

            result = await self.db.execute(paginated_query)
            rows = result.all()

            # 转换为响应格式
            services = []
            for row in rows:
                service = row[0]  # ChannelServiceModel
                category_name = row[1] if len(row) > 1 else "未知分类"  # category_name

                # 查询该服务绑定的内容提供商
                bound_providers = await self._get_service_bound_providers(service.id)

                services.append(
                    ChannelServiceResponse(
                        id=str(service.id),
                        category_id=str(service.category_id),
                        service_source=service.service_source,
                        provider_id=str(service.provider_id) if service.provider_id else None,
                        approval_status=service.approval_status,
                        approval_note=service.approval_note,
                        approval_time=service.approval_time,
                        approver_id=str(service.approver_id) if service.approver_id else None,
                        service_name=service.service_name,
                        service_code=service.service_code,
                        service_description=service.service_description,
                        service_features=service.service_features,
                        base_price=service.base_price,
                        discount_price=service.discount_price,
                        price_unit=service.price_unit,
                        service_specs=service.service_specs,
                        delivery_time=service.delivery_time,
                        revision_count=service.revision_count,
                        channel_type=service.channel_type,
                        portal_type=service.portal_type,
                        platform_specs=service.platform_specs,
                        coverage_area=service.coverage_area,
                        is_active=service.is_active,
                        created_at=service.created_at,
                        updated_at=service.updated_at,
                        bound_providers=bound_providers
                    )
                )

            return {
                "items": services,
                "pagination": {
                    "page": query.page,
                    "size": query.size,
                    "total": total,
                    "pages": (total + query.size - 1) // query.size if total > 0 else 0
                }
            }
        except Exception as e:
            print(f"获取所有服务列表时发生错误: {str(e)}")
            raise

    async def _get_service_bound_providers(self, service_id: uuid.UUID) -> List[Dict[str, Any]]:
        """获取服务绑定的内容提供商列表"""
        try:
            # 查询绑定的内容提供商
            result = await self.db.execute(
                select(
                    ChannelCategoryMapping,
                    ContentProvider
                ).select_from(
                    ChannelCategoryMapping.__table__.join(
                        ContentProvider, ChannelCategoryMapping.provider_id == ContentProvider.id
                    )
                ).where(
                    ChannelCategoryMapping.service_id == service_id
                )
            )

            mappings = result.all()

            # 构建返回数据（简化版，只返回必要信息）
            providers_data = []
            for mapping, provider in mappings:
                provider_data = {
                    "id": str(mapping.id),  # 绑定关系ID
                    "provider_id": str(provider.id),  # 内容提供商ID
                    "provider_name": provider.provider_name  # 内容提供商名称
                }
                providers_data.append(provider_data)

            return providers_data

        except Exception as e:
            print(f"❌ 获取服务绑定的内容提供商失败: {e}")
            import traceback
            print(f"错误堆栈: {traceback.format_exc()}")
            return []

    async def get_service_list_by_provider(self, provider_id: str, query: ChannelServiceListQuery) -> Dict[str, Any]:
        """管理员获取指定渠道商的服务列表"""
        # 验证渠道商是否存在
        provider_result = await self.db.execute(
            select(ContentProvider).where(ContentProvider.id == provider_id)
        )
        provider = provider_result.scalar_one_or_none()
        if not provider:
            raise NotFoundError("渠道商不存在")

        # 构建查询条件
        conditions = [ChannelServiceModel.provider_id == provider_id]

        if query.category_id:
            try:
                category_uuid = uuid.UUID(query.category_id)
                conditions.append(ChannelServiceModel.category_id == category_uuid)
            except ValueError:
                # 如果category_id不是有效的UUID，跳过此条件
                pass

        if query.portal_type:
            conditions.append(ChannelServiceModel.portal_type == query.portal_type)

        # 处理tags筛选
        if query.tags:
            tag_list = [tag.strip() for tag in query.tags.split(',') if tag.strip()]
            if tag_list:
                # 使用JSON_CONTAINS函数检查是否包含指定标签
                tag_conditions = []
                for tag in tag_list:
                    # 使用多种匹配方式确保能找到标签
                    tag_conditions.extend([
                        # 精确匹配带引号的标签
                        ChannelServiceModel.tags.cast(String).like(f'%"{tag}"%'),
                        # 模糊匹配不带引号的标签（防止JSON格式问题）
                        ChannelServiceModel.tags.cast(String).like(f'%{tag}%')
                    ])
                if tag_conditions:
                    conditions.append(or_(*tag_conditions))

        if query.is_active is not None:
            conditions.append(ChannelServiceModel.is_active == query.is_active)

        if query.min_price is not None:
            conditions.append(ChannelServiceModel.base_price >= query.min_price)

        if query.max_price is not None:
            conditions.append(ChannelServiceModel.base_price <= query.max_price)

        if query.search:
            search_condition = or_(
                ChannelServiceModel.service_name.ilike(f"%{query.search}%"),
                ChannelServiceModel.service_description.ilike(f"%{query.search}%"),
                ChannelServiceModel.service_code.ilike(f"%{query.search}%")
            )
            conditions.append(search_condition)

        # 构建基础查询
        base_query = select(ChannelServiceModel, ChannelCategory).join(
            ChannelCategory, ChannelServiceModel.category_id == ChannelCategory.id
        ).where(and_(*conditions))

        # 排序
        sort_column = getattr(ChannelServiceModel, query.sort_by, ChannelServiceModel.created_at)
        if query.sort_order == "desc":
            order_func = desc
        else:
            order_func = asc
        base_query = base_query.order_by(order_func(sort_column))

        # 分页
        offset = (query.page - 1) * query.size
        paginated_query = base_query.offset(offset).limit(query.size)

        # 执行查询
        result = await self.db.execute(paginated_query)
        services_data = result.all()

        # 获取总数
        count_query = select(func.count(ChannelServiceModel.id)).where(and_(*conditions))
        total_result = await self.db.execute(count_query)
        total = total_result.scalar()

        # 构建响应数据
        items = []
        for service, category in services_data:
            service_response = ChannelServiceResponse(
                id=str(service.id),
                provider_id=str(service.provider_id),
                category_id=str(service.category_id),
                service_name=service.service_name,
                service_code=service.service_code,
                service_description=service.service_description,
                service_features=service.service_features,
                base_price=service.base_price,
                discount_price=service.discount_price,
                price_unit=service.price_unit,
                service_specs=service.service_specs,
                delivery_time=service.delivery_time,
                revision_count=service.revision_count,
                channel_type=service.channel_type,
                portal_type=service.portal_type,
                platform_specs=service.platform_specs,
                coverage_area=service.coverage_area,
                is_active=service.is_active,
                created_at=service.created_at,
                updated_at=service.updated_at,
                category=ChannelCategoryResponse(
                    id=str(category.id),
                    category_name=category.category_name,
                    category_code=category.category_code,
                    category_description=category.category_description,
                    is_active=category.is_active,
                    created_at=category.created_at,
                    updated_at=category.updated_at
                )
            )
            items.append(service_response)

        return {
            "items": items,
            "pagination": {
                "page": query.page,
                "size": query.size,
                "total": total,
                "pages": (total + query.size - 1) // query.size
            },
            "provider_info": {
                "id": str(provider.id),
                "provider_name": provider.provider_name,
                "provider_type": provider.provider_type,
                "verification_status": provider.verification_status
            }
        }

    async def get_category_services(self, query: ChannelServiceListQuery) -> Dict[str, Any]:
        """管理员获取某个分类下所有渠道商的服务列表"""
        # 验证分类是否存在
        if query.category_id:
            category_result = await self.db.execute(
                select(ChannelCategory).where(ChannelCategory.id == query.category_id)
            )
            category = category_result.scalar_one_or_none()
            if not category:
                raise NotFoundError("分类不存在")

        # 构建查询条件
        conditions = []

        if query.category_id:
            try:
                category_uuid = uuid.UUID(query.category_id)
                conditions.append(ChannelServiceModel.category_id == category_uuid)
            except ValueError:
                # 如果category_id不是有效的UUID，跳过此条件
                pass

        if query.portal_type:
            conditions.append(ChannelServiceModel.portal_type == query.portal_type)

        # 处理tags筛选
        if query.tags:
            tag_list = [tag.strip() for tag in query.tags.split(',') if tag.strip()]
            if tag_list:
                # 使用JSON_CONTAINS函数检查是否包含指定标签
                tag_conditions = []
                for tag in tag_list:
                    # 使用多种匹配方式确保能找到标签
                    tag_conditions.extend([
                        # 精确匹配带引号的标签
                        ChannelServiceModel.tags.cast(String).like(f'%"{tag}"%'),
                        # 模糊匹配不带引号的标签（防止JSON格式问题）
                        ChannelServiceModel.tags.cast(String).like(f'%{tag}%')
                    ])
                if tag_conditions:
                    conditions.append(or_(*tag_conditions))

        if query.is_active is not None:
            conditions.append(ChannelServiceModel.is_active == query.is_active)

        if query.min_price is not None:
            conditions.append(ChannelServiceModel.base_price >= query.min_price)

        if query.max_price is not None:
            conditions.append(ChannelServiceModel.base_price <= query.max_price)

        if query.search:
            search_condition = or_(
                ChannelServiceModel.service_name.ilike(f"%{query.search}%"),
                ChannelServiceModel.service_description.ilike(f"%{query.search}%"),
                ChannelServiceModel.service_code.ilike(f"%{query.search}%")
            )
            conditions.append(search_condition)

        # 构建基础查询，包含渠道商信息
        base_query = select(ChannelServiceModel, ChannelCategory, ContentProvider).join(
            ChannelCategory, ChannelServiceModel.category_id == ChannelCategory.id
        ).join(
            ContentProvider, ChannelServiceModel.provider_id == ContentProvider.id
        )

        if conditions:
            base_query = base_query.where(and_(*conditions))

        # 排序
        sort_column = getattr(ChannelServiceModel, query.sort_by, ChannelServiceModel.created_at)
        if query.sort_order == "desc":
            order_func = desc
        else:
            order_func = asc
        base_query = base_query.order_by(order_func(sort_column))

        # 分页
        offset = (query.page - 1) * query.size
        paginated_query = base_query.offset(offset).limit(query.size)

        # 执行查询
        result = await self.db.execute(paginated_query)
        services_data = result.all()

        # 获取总数
        count_conditions = conditions.copy() if conditions else []
        count_query = select(func.count(ChannelServiceModel.id))
        if count_conditions:
            count_query = count_query.where(and_(*count_conditions))
        total_result = await self.db.execute(count_query)
        total = total_result.scalar()

        # 构建响应数据
        items = []
        for service, category, provider in services_data:
            service_response = ChannelServiceResponse(
                id=str(service.id),
                provider_id=str(service.provider_id),
                category_id=str(service.category_id),
                service_name=service.service_name,
                service_code=service.service_code,
                service_description=service.service_description,
                service_features=service.service_features,
                base_price=service.base_price,
                discount_price=service.discount_price,
                price_unit=service.price_unit,
                service_specs=service.service_specs,
                delivery_time=service.delivery_time,
                revision_count=service.revision_count,
                channel_type=service.channel_type,
                portal_type=service.portal_type,
                platform_specs=service.platform_specs,
                coverage_area=service.coverage_area,
                is_active=service.is_active,
                created_at=service.created_at,
                updated_at=service.updated_at,
                category=ChannelCategoryResponse(
                    id=str(category.id),
                    category_name=category.category_name,
                    category_code=category.category_code,
                    category_description=category.category_description,
                    is_active=category.is_active,
                    created_at=category.created_at,
                    updated_at=category.updated_at
                )
            )
            # 添加渠道商信息到服务响应中
            service_response.provider_info = {
                "id": str(provider.id),
                "provider_name": provider.provider_name,
                "provider_type": provider.provider_type,
                "verification_status": provider.verification_status
            }
            items.append(service_response)

        return {
            "items": items,
            "pagination": {
                "page": query.page,
                "size": query.size,
                "total": total,
                "pages": (total + query.size - 1) // query.size
            }
        }

    # ==================== 管理员分类关联管理方法 ====================

    async def get_category_mapping_list(self, query: ChannelCategoryMappingListQuery) -> Dict[str, Any]:
        """管理员获取分类关联列表"""
        # 构建查询条件
        conditions = []

        if query.provider_id:
            conditions.append(ChannelCategoryMapping.provider_id == query.provider_id)

        if query.category_id:
            # 需要通过 ChannelService 表连接到分类
            try:
                category_uuid = uuid.UUID(query.category_id)
                conditions.append(ChannelServiceModel.category_id == category_uuid)
            except ValueError:
                # 如果category_id不是有效的UUID，跳过此条件
                pass

        if query.service_id:
            conditions.append(ChannelCategoryMapping.service_id == query.service_id)

        # 注意：is_primary 字段已移至 ChannelServiceEvaluation 表

        if query.is_active is not None:
            conditions.append(ChannelCategoryMapping.is_active == query.is_active)

        # 构建排序
        valid_sort_fields = {
            'created_at': ChannelCategoryMapping.created_at,
            'updated_at': ChannelCategoryMapping.updated_at,
            'is_active': ChannelCategoryMapping.is_active
        }
        sort_column = valid_sort_fields.get(query.sort_by, ChannelCategoryMapping.created_at)
        order_func = desc if query.sort_order == "desc" else asc

        # 获取总数
        count_stmt = select(func.count(ChannelCategoryMapping.id))
        if conditions:
            count_stmt = count_stmt.where(and_(*conditions))
        total_result = await self.db.execute(count_stmt)
        total = total_result.scalar()

        # 获取分页数据
        offset = (query.page - 1) * query.size

        # 先尝试简单查询，避免复杂JOIN导致的问题
        stmt = select(ChannelCategoryMapping)

        if conditions:
            stmt = stmt.where(and_(*conditions))

        stmt = stmt.order_by(order_func(sort_column)).offset(offset).limit(query.size)

        result = await self.db.execute(stmt)
        mappings = result.scalars().all()

        # 分别查询关联数据并构建响应
        mapping_list = []
        for mapping in mappings:
            # 查询服务信息
            service_stmt = select(ChannelServiceModel).where(ChannelServiceModel.id == mapping.service_id)
            service_result = await self.db.execute(service_stmt)
            service = service_result.scalar_one_or_none()

            if not service:
                continue

            # 查询分类信息
            category_stmt = select(ChannelCategory).where(ChannelCategory.id == service.category_id)
            category_result = await self.db.execute(category_stmt)
            category = category_result.scalar_one_or_none()

            # 查询渠道商信息
            provider_stmt = select(ContentProvider).where(ContentProvider.id == mapping.provider_id)
            provider_result = await self.db.execute(provider_stmt)
            provider = provider_result.scalar_one_or_none()

            # 构建响应数据
            mapping_data = ChannelCategoryMappingResponse(
                id=str(mapping.id),
                provider_id=str(mapping.provider_id),
                service_id=str(mapping.service_id),
                is_active=mapping.is_active,
                created_at=mapping.created_at,
                updated_at=mapping.updated_at,
                service=ChannelServiceResponse(
                    id=str(service.id),
                    category_id=str(service.category_id),
                    service_source=service.service_source,
                    provider_id=str(service.provider_id) if service.provider_id else None,
                    approval_status=service.approval_status,
                    approval_note=service.approval_note,
                    approval_time=service.approval_time,
                    approver_id=str(service.approver_id) if service.approver_id else None,
                    service_name=service.service_name,
                    service_code=service.service_code,
                    service_description=service.service_description,
                    service_features=service.service_features,
                    base_price=service.base_price,
                    discount_price=service.discount_price,
                    price_unit=service.price_unit,
                    service_specs=service.service_specs,
                    delivery_time=service.delivery_time,
                    revision_count=service.revision_count,
                    channel_type=service.channel_type,
                    portal_type=service.portal_type,
                    platform_specs=service.platform_specs,
                    coverage_area=service.coverage_area,
                    is_active=service.is_active,
                    created_at=service.created_at,
                    updated_at=service.updated_at,
                    category=ChannelCategoryResponse(
                        id=str(category.id),
                        category_name=category.category_name,
                        category_code=category.category_code,
                        category_description=category.category_description,
                        is_active=category.is_active,
                        created_at=category.created_at,
                        updated_at=category.updated_at
                    )
                )
            )
            mapping_list.append(mapping_data)

        return {
            "items": mapping_list,
            "pagination": {
                "page": query.page,
                "size": query.size,
                "total": total,
                "total_pages": (total + query.size - 1) // query.size
            }
        }

    async def get_category_mapping_detail(self, mapping_id: str) -> ChannelCategoryMappingResponse:
        """管理员获取分类关联详情"""
        stmt = select(
            ChannelCategoryMapping,
            ChannelCategory,
            ChannelServiceModel,
            ContentProvider
        ).join(
            ChannelServiceModel, ChannelCategoryMapping.service_id == ChannelServiceModel.id
        ).join(
            ChannelCategory, ChannelServiceModel.category_id == ChannelCategory.id
        ).outerjoin(
            ContentProvider, ChannelServiceModel.provider_id == ContentProvider.id
        ).where(ChannelCategoryMapping.id == mapping_id)

        result = await self.db.execute(stmt)
        mapping_data = result.first()
        if not mapping_data:
            raise NotFoundError("分类关联不存在")

        mapping, category, service, creator = mapping_data

        # 构建创建者信息
        creator_info = None
        if creator:
            creator_info = {
                "id": str(creator.id),
                "name": creator.name,
                "email": creator.email,
                "real_name": creator.real_name,
                "company_name": creator.company_name,
                "provider_type": creator.provider_type
            }

        return ChannelCategoryMappingResponse(
            id=str(mapping.id),
            provider_id=str(mapping.provider_id),
            service_id=str(mapping.service_id),
            is_active=mapping.is_active,
            created_at=mapping.created_at,
            updated_at=mapping.updated_at,
            service=ChannelServiceResponse(
                id=str(service.id),
                category_id=str(service.category_id),
                service_source=service.service_source,
                provider_id=str(service.provider_id) if service.provider_id else None,
                approval_status=service.approval_status,
                approval_note=service.approval_note,
                approval_time=service.approval_time,
                approver_id=str(service.approver_id) if service.approver_id else None,
                service_name=service.service_name,
                service_code=service.service_code,
                service_description=service.service_description,
                service_features=service.service_features,
                base_price=service.base_price,
                discount_price=service.discount_price,
                price_unit=service.price_unit,
                service_specs=service.service_specs,
                delivery_time=service.delivery_time,
                revision_count=service.revision_count,
                channel_type=service.channel_type,
                portal_type=service.portal_type,
                platform_specs=service.platform_specs,
                coverage_area=service.coverage_area,
                is_active=service.is_active,
                created_at=service.created_at,
                updated_at=service.updated_at,
                category=ChannelCategoryResponse(
                    id=str(category.id),
                    category_name=category.category_name,
                    category_code=category.category_code,
                    category_description=category.category_description,
                    is_active=category.is_active,
                    created_at=category.created_at,
                    updated_at=category.updated_at
                ),
                creator_info=creator_info
            ),
            category=ChannelCategoryResponse(
                id=str(category.id),
                category_name=category.category_name,
                category_code=category.category_code,
                category_description=category.category_description,
                is_active=category.is_active,
                created_at=category.created_at,
                updated_at=category.updated_at
            )
        )

    async def admin_update_category_mapping(self, mapping_id: str, request: ChannelCategoryMappingUpdate) -> ChannelCategoryMappingResponse:
        """管理员更新分类关联"""
        # 获取关联记录
        result = await self.db.execute(
            select(ChannelCategoryMapping).where(ChannelCategoryMapping.id == mapping_id)
        )
        mapping = result.scalar_one_or_none()
        if not mapping:
            raise NotFoundError("分类关联不存在")

        # 注意：is_primary 字段已移至 ChannelServiceEvaluation 表

        # 更新字段
        update_data = request.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            if hasattr(mapping, field):
                setattr(mapping, field, value)

        mapping.updated_at = datetime.utcnow()

        await self.db.commit()
        await self.db.refresh(mapping)

        # 获取关联的分类和服务信息
        detail_result = await self.db.execute(
            select(ChannelCategoryMapping, ChannelCategory, ChannelServiceModel).join(
                ChannelServiceModel, ChannelCategoryMapping.service_id == ChannelServiceModel.id
            ).join(
                ChannelCategory, ChannelServiceModel.category_id == ChannelCategory.id
            ).where(ChannelCategoryMapping.id == mapping_id)
        )
        mapping_data = detail_result.first()
        mapping, category, service = mapping_data

        return ChannelCategoryMappingResponse(
            id=str(mapping.id),
            provider_id=str(mapping.provider_id),
            service_id=str(mapping.service_id),
            is_active=mapping.is_active,
            created_at=mapping.created_at,
            updated_at=mapping.updated_at,
            category=ChannelCategoryResponse(
                id=str(category.id),
                category_name=category.category_name,
                category_code=category.category_code,
                category_description=category.category_description,
                is_active=category.is_active,
                created_at=category.created_at,
                updated_at=category.updated_at
            ),
            service=ChannelServiceResponse(
                id=str(service.id),
                provider_id=str(service.provider_id),
                category_id=str(service.category_id),
                service_name=service.service_name,
                service_code=service.service_code,
                service_description=service.service_description,
                service_features=service.service_features,
                base_price=service.base_price,
                discount_price=service.discount_price,
                price_unit=service.price_unit,
                service_specs=service.service_specs,
                delivery_time=service.delivery_time,
                revision_count=service.revision_count,
                portal_type=service.portal_type,
                platform_specs=service.platform_specs,
                tags=service.tags,
                is_active=service.is_active,
                created_at=service.created_at,
                updated_at=service.updated_at
            )
        )

    async def admin_delete_category_mapping(self, mapping_id: str) -> bool:
        """管理员删除分类关联"""
        # 获取关联记录
        result = await self.db.execute(
            select(ChannelCategoryMapping).where(ChannelCategoryMapping.id == mapping_id)
        )
        mapping = result.scalar_one_or_none()
        if not mapping:
            raise NotFoundError("分类关联不存在")

        # 删除关联
        await self.db.delete(mapping)
        await self.db.commit()

        return True

    async def get_provider_service_mapping(self, service_id: str, provider_id: str) -> ChannelCategoryMappingResponse:
        """查询渠道商和服务的关联关系"""
        # 查询关联记录
        mapping_result = await self.db.execute(
            select(ChannelCategoryMapping).where(
                and_(
                    ChannelCategoryMapping.provider_id == provider_id,
                    ChannelCategoryMapping.service_id == service_id
                )
            )
        )
        mapping = mapping_result.scalar_one_or_none()
        if not mapping:
            raise NotFoundError("未找到关联关系")

        # 返回详情
        return await self.get_category_mapping_detail(str(mapping.id))

    async def admin_get_service_providers(self, service_id: str) -> List[Dict[str, Any]]:
        """管理员获取特定服务绑定的所有渠道商列表"""
        print(f"🔍 获取服务绑定的渠道商列表: service_id={service_id}")

        try:
            # 验证服务是否存在
            service_uuid = uuid.UUID(service_id)
            service_result = await self.db.execute(
                select(ChannelServiceModel).where(ChannelServiceModel.id == service_uuid)
            )
            service = service_result.scalar_one_or_none()
            if not service:
                print(f"❌ 服务不存在: {service_id}")
                raise NotFoundError("服务不存在")

            print(f"✅ 服务存在: {service.service_name}")

            # 查询绑定的渠道商
            result = await self.db.execute(
                select(
                    ChannelCategoryMapping,
                    ContentProvider
                ).select_from(
                    ChannelCategoryMapping.__table__.join(
                        ContentProvider, ChannelCategoryMapping.provider_id == ContentProvider.id
                    )
                ).where(
                    ChannelCategoryMapping.service_id == service_uuid
                )
            )

            mappings = result.all()
            print(f"✅ 找到 {len(mappings)} 个绑定的渠道商")

            # 构建返回数据
            providers_data = []
            for mapping, provider in mappings:
                provider_data = {
                    "id": str(mapping.id),
                    "provider_id": str(provider.id),
                    "provider_name": provider.provider_name,
                    "provider_type": provider.provider_type,
                    "real_name": provider.real_name,
                    "contact_email": provider.contact_email,
                    "is_active": mapping.is_active,
                    "created_at": mapping.created_at.isoformat() if mapping.created_at else None,
                    "updated_at": mapping.updated_at.isoformat() if mapping.updated_at else None
                }
                providers_data.append(provider_data)

            return providers_data

        except Exception as e:
            print(f"❌ 获取服务绑定的渠道商列表失败: {e}")
            import traceback
            print(f"错误堆栈: {traceback.format_exc()}")
            raise e

    async def admin_link_provider_to_service(self, service_id: str, provider_id: str) -> ChannelCategoryMappingResponse:
        """管理员为服务关联渠道商"""
        print(f"🔍 开始关联服务: service_id={service_id}, provider_id={provider_id}")

        # 验证UUID格式
        try:
            service_uuid = uuid.UUID(service_id)
            provider_uuid = uuid.UUID(provider_id)
            print(f"✅ UUID格式验证通过: service_uuid={service_uuid}, provider_uuid={provider_uuid}")
        except ValueError as e:
            print(f"❌ UUID格式错误: {e}")
            raise ValidationError(f"ID格式无效: {e}")

        # 验证服务是否存在
        print(f"🔍 查询服务: {service_id}")
        service_result = await self.db.execute(
            select(ChannelServiceModel).where(ChannelServiceModel.id == service_id)
        )
        service = service_result.scalar_one_or_none()
        if not service:
            print(f"❌ 服务不存在: {service_id}")
            raise NotFoundError("服务不存在")
        print(f"✅ 找到服务: {service.service_name}")

        # 验证渠道商是否存在
        print(f"🔍 查询渠道商: {provider_id}")
        provider_result = await self.db.execute(
            select(ContentProvider).where(ContentProvider.id == provider_id)
        )
        provider = provider_result.scalar_one_or_none()
        if not provider:
            print(f"❌ 渠道商不存在: {provider_id}")
            raise NotFoundError("渠道商不存在")
        print(f"✅ 找到渠道商: {provider.provider_name}")

        # 检查是否已经关联（同一渠道商和服务）
        print(f"🔍 检查是否已关联")
        existing_result = await self.db.execute(
            select(ChannelCategoryMapping).where(
                and_(
                    ChannelCategoryMapping.provider_id == provider_id,
                    ChannelCategoryMapping.service_id == service_id
                )
            )
        )
        existing_mapping = existing_result.scalar_one_or_none()
        if existing_mapping:
            print(f"❌ 已经关联: mapping_id={existing_mapping.id}")
            raise ValidationError("该渠道商已经关联到此服务")
        print(f"✅ 未关联，可以创建新关联")

        # 检查该服务是否已经关联了其他内容提供商（多对一关系限制）
        print(f"🔍 检查服务是否已关联其他内容提供商")
        service_existing_result = await self.db.execute(
            select(ChannelCategoryMapping).where(
                ChannelCategoryMapping.service_id == service_id
            )
        )
        service_existing_mapping = service_existing_result.scalar_one_or_none()
        if service_existing_mapping:
            # 获取已关联的内容提供商信息
            existing_provider_result = await self.db.execute(
                select(ContentProvider).where(ContentProvider.id == service_existing_mapping.provider_id)
            )
            existing_provider = existing_provider_result.scalar_one_or_none()
            provider_name = existing_provider.provider_name if existing_provider else "未知提供商"
            print(f"❌ 服务已关联其他内容提供商: {provider_name}")
            raise ValidationError(f"该服务已经关联到内容提供商：{provider_name}。每个服务最多只能关联一个内容提供商。")
        print(f"✅ 服务未关联其他内容提供商，可以创建新关联")

        # 创建关联记录
        print(f"🔍 创建关联记录")
        try:
            from datetime import datetime
            current_time = datetime.utcnow()

            mapping = ChannelCategoryMapping(
                provider_id=provider_uuid,
                service_id=service_uuid,
                is_active=True,
                created_at=current_time,
                updated_at=current_time
            )
            print(f"✅ 关联对象创建成功")

            self.db.add(mapping)
            print(f"✅ 添加到数据库会话")

            await self.db.commit()
            print(f"✅ 提交到数据库成功")

            await self.db.refresh(mapping)
            print(f"✅ 刷新对象成功: mapping_id={mapping.id}")
            print(f"✅ 时间戳: created_at={mapping.created_at}, updated_at={mapping.updated_at}")

            # 返回详情
            result = await self.get_category_mapping_detail(str(mapping.id))
            print(f"✅ 关联成功完成")
            return result

        except Exception as e:
            print(f"❌ 创建关联记录失败: {e}")
            import traceback
            print(f"错误堆栈: {traceback.format_exc()}")
            await self.db.rollback()
            raise ValidationError(f"创建关联记录失败: {e}")

    # ==================== 可用渠道商查询方法 ====================

    async def get_available_providers(self, query_params: Dict[str, Any]) -> Dict[str, Any]:
        """企业用户获取提供指定服务的可用渠道商列表"""
        try:
            # 验证service_id是否为有效的UUID
            try:
                service_uuid = uuid.UUID(query_params['service_id'])
            except (ValueError, TypeError):
                raise ValidationError("无效的服务ID格式")

            # 构建基础查询 - 重点返回渠道商信息
            base_query = select(
                ContentProvider.id.label('provider_id'),
                ContentProvider.provider_name,
                ContentProvider.provider_type,
                ContentProvider.contact_phone,
                ContentProvider.contact_email,
                ContentProvider.contact_address,
                ContentProvider.business_description,
                ContentProvider.service_rating,
                ContentProvider.completed_orders,
                ContentProvider.verification_status,
                ContentProvider.created_at,
                # 服务相关信息作为补充
                ChannelServiceModel.service_name.label('service_provided'),
                ChannelServiceModel.platform_type,
                ChannelServiceModel.base_price,
                ChannelServiceModel.delivery_time
            ).select_from(
                ContentProvider
            ).join(
                ChannelCategoryMapping, ContentProvider.id == ChannelCategoryMapping.provider_id
            ).join(
                ChannelServiceModel, ChannelCategoryMapping.service_id == ChannelServiceModel.id
            ).where(
                and_(
                    ChannelServiceModel.id == service_uuid,
                    # 暂时放宽条件用于调试
                    # ContentProvider.verification_status == 'verified',
                    ContentProvider.is_active == True,
                    ChannelServiceModel.is_active == True,
                    ChannelCategoryMapping.is_active == True
                )
            )

            # 添加筛选条件
            conditions = []

            # 价格筛选
            if query_params.get('min_price') is not None:
                conditions.append(ChannelServiceModel.base_price >= query_params['min_price'])
            if query_params.get('max_price') is not None:
                conditions.append(ChannelServiceModel.base_price <= query_params['max_price'])

            # 评分筛选
            if query_params.get('min_rating') is not None:
                conditions.append(ContentProvider.service_rating >= query_params['min_rating'])

            # 平台类型筛选
            if query_params.get('platform_type'):
                conditions.append(ChannelServiceModel.platform_type == query_params['platform_type'])

            # 交付时间筛选
            if query_params.get('delivery_time') is not None:
                conditions.append(ChannelServiceModel.delivery_time <= query_params['delivery_time'])

            # 标签筛选（通过业务描述进行模糊匹配）
            if query_params.get('tags'):
                tag_conditions = []
                for tag in query_params['tags']:
                    tag_conditions.append(ContentProvider.business_description.ilike(f"%{tag}%"))
                    tag_conditions.append(ChannelServiceModel.service_description.ilike(f"%{tag}%"))
                if tag_conditions:
                    conditions.append(or_(*tag_conditions))

            if conditions:
                base_query = base_query.where(and_(*conditions))

            # 排序
            sort_field = query_params.get('sort_by', 'service_rating')
            sort_order = query_params.get('sort_order', 'desc')

            if sort_field == 'price':
                order_column = ChannelServiceModel.base_price
            elif sort_field == 'service_rating':
                order_column = ContentProvider.service_rating
            elif sort_field == 'delivery_time':
                order_column = ChannelServiceModel.delivery_time
            else:
                order_column = ContentProvider.service_rating

            if sort_order == 'asc':
                base_query = base_query.order_by(asc(order_column))
            else:
                base_query = base_query.order_by(desc(order_column))

            # 分页
            page = query_params.get('page', 1)
            size = query_params.get('size', 20)
            offset = (page - 1) * size

            # 获取总数
            count_query = select(func.count()).select_from(base_query.subquery())
            count_result = await self.db.execute(count_query)
            total = count_result.scalar()

            # 获取分页数据
            paginated_query = base_query.offset(offset).limit(size)
            result = await self.db.execute(paginated_query)
            providers = result.fetchall()

            # 调试日志
            print(f"DEBUG: service_id={service_uuid}, total={total}, providers_count={len(providers)}")

            # 构建响应数据 - 重点展示渠道商信息
            items = []
            for provider in providers:
                # 根据渠道商类型设置图标
                icon_map = {
                    'individual': '👤',
                    'company': '🏢',
                    'studio': '🎨',
                    'mcn': '📺'
                }

                items.append({
                    'provider_id': str(provider.provider_id),
                    'provider_name': provider.provider_name,
                    'provider_type': provider.provider_type,
                    'contact_phone': provider.contact_phone,
                    'contact_email': provider.contact_email,
                    'contact_address': provider.contact_address,
                    'business_description': provider.business_description,
                    'verification_status': provider.verification_status,
                    'service_icon': icon_map.get(provider.provider_type, '🏢'),
                    'service_rating': float(provider.service_rating) if provider.service_rating else 0,
                    'completed_orders': provider.completed_orders or 0,
                    'created_at': provider.created_at.isoformat() if provider.created_at else None,
                    # 服务相关信息作为补充
                    'service_provided': provider.service_provided,
                    'platform_type': provider.platform_type,
                    'base_price': float(provider.base_price) if provider.base_price else 0,
                    'delivery_time': provider.delivery_time or 24,
                                        'response_time': '1-2小时'  # 可以根据实际数据调整
                })

            # 计算筛选器数据
            filters = await self._get_provider_filters(str(service_uuid))

            return {
                'items': items,
                'pagination': {
                    'page': page,
                    'size': size,
                    'total': total,
                    'pages': (total + size - 1) // size
                },
                'filters': filters
            }

        except ValidationError:
            raise
        except Exception as e:
            raise Exception(f"获取可用渠道商列表失败: {str(e)}")

    async def _get_provider_filters(self, service_id: str) -> Dict[str, Any]:
        """获取筛选器选项数据"""
        try:
            # 验证service_id是否为有效的UUID
            try:
                service_uuid = uuid.UUID(service_id)
            except (ValueError, TypeError):
                return {
                    'price_range': {'min': 0, 'max': 0},
                    'rating_range': {'min': 0, 'max': 5},
                    'delivery_time_range': {'min': 1, 'max': 168},
                    'available_tags': [],
                    'platform_types': []
                }

            # 获取价格范围
            price_query = select(
                func.min(ChannelServiceModel.base_price).label('min_price'),
                func.max(ChannelServiceModel.base_price).label('max_price')
            ).select_from(
                ChannelServiceModel
            ).join(
                ChannelCategoryMapping, ChannelServiceModel.id == ChannelCategoryMapping.service_id
            ).where(
                and_(
                    ChannelServiceModel.id == service_uuid,
                    ChannelServiceModel.is_active == True,
                    ChannelCategoryMapping.is_active == True
                )
            )

            price_result = await self.db.execute(price_query)
            price_data = price_result.first()

            # 获取评分范围
            rating_query = select(
                func.min(ContentProvider.service_rating).label('min_rating'),
                func.max(ContentProvider.service_rating).label('max_rating')
            ).select_from(
                ContentProvider
            ).join(
                ChannelCategoryMapping, ContentProvider.id == ChannelCategoryMapping.provider_id
            ).join(
                ChannelServiceModel, ChannelCategoryMapping.service_id == ChannelServiceModel.id
            ).where(
                and_(
                    ChannelServiceModel.id == service_uuid,
                    ContentProvider.verification_status == 'verified',
                    ContentProvider.is_active == True
                )
            )

            rating_result = await self.db.execute(rating_query)
            rating_data = rating_result.first()

            # 获取交付时间范围
            delivery_query = select(
                func.min(ChannelServiceModel.delivery_time).label('min_delivery'),
                func.max(ChannelServiceModel.delivery_time).label('max_delivery')
            ).select_from(
                ChannelServiceModel
            ).join(
                ChannelCategoryMapping, ChannelServiceModel.id == ChannelCategoryMapping.service_id
            ).where(
                and_(
                    ChannelServiceModel.id == service_uuid,
                    ChannelServiceModel.is_active == True
                )
            )

            delivery_result = await self.db.execute(delivery_query)
            delivery_data = delivery_result.first()

            # 获取平台类型
            platform_query = select(
                func.distinct(ChannelServiceModel.platform_type)
            ).select_from(
                ChannelServiceModel
            ).join(
                ChannelCategoryMapping, ChannelServiceModel.id == ChannelCategoryMapping.service_id
            ).where(
                and_(
                    ChannelServiceModel.id == service_uuid,
                    ChannelServiceModel.is_active == True,
                    ChannelServiceModel.platform_type.isnot(None)
                )
            )

            platform_result = await self.db.execute(platform_query)
            platform_types = [row[0] for row in platform_result.fetchall()]

            return {
                'price_range': {
                    'min': float(price_data.min_price) if price_data and price_data.min_price else 0,
                    'max': float(price_data.max_price) if price_data and price_data.max_price else 0
                },
                'rating_range': {
                    'min': float(rating_data.min_rating) if rating_data and rating_data.min_rating else 0,
                    'max': float(rating_data.max_rating) if rating_data and rating_data.max_rating else 5
                },
                'delivery_time_range': {
                    'min': delivery_data.min_delivery if delivery_data and delivery_data.min_delivery else 1,
                    'max': delivery_data.max_delivery if delivery_data and delivery_data.max_delivery else 168
                },
                'available_tags': [],  # 可以根据需要实现标签提取逻辑
                'platform_types': platform_types
            }

        except Exception as e:
            # 如果查询失败，返回默认值
            return {
                'price_range': {'min': 0, 'max': 0},
                'rating_range': {'min': 0, 'max': 5},
                'delivery_time_range': {'min': 1, 'max': 168},
                'available_tags': [],
                'platform_types': []
            }

    # ==================== 渠道商分类映射管理方法 ====================

    async def get_my_bound_services(self, user_id: str, query: ChannelCategoryMappingListQuery) -> Dict[str, Any]:
        """渠道商获取自己绑定的系统服务列表"""
        # 获取渠道商信息
        provider = await self._get_provider_by_user_id(user_id)

        # 设置查询条件中的渠道商ID
        query.provider_id = str(provider.id)

        # 调用管理员方法获取数据
        return await self.get_category_mapping_list(query)



    async def bind_provider_to_service(self, user_id: str, service_id: str, request: ChannelCategoryMappingCreate) -> ChannelCategoryMappingResponse:
        """渠道商绑定系统服务"""
        # 获取渠道商信息
        provider = await self._get_provider_by_user_id(user_id)

        # 验证服务是否存在且可用
        service_result = await self.db.execute(
            select(ChannelServiceModel).where(
                and_(
                    ChannelServiceModel.id == service_id,
                    ChannelServiceModel.is_active == True
                )
            )
        )
        service = service_result.scalar_one_or_none()
        if not service:
            raise NotFoundError("服务不存在或已被禁用")

        # 检查是否已经绑定
        existing_result = await self.db.execute(
            select(ChannelCategoryMapping).where(
                and_(
                    ChannelCategoryMapping.provider_id == provider.id,
                    ChannelCategoryMapping.service_id == service_id
                )
            )
        )
        if existing_result.scalar_one_or_none():
            raise ValidationError("该服务已经绑定")

        # 创建绑定记录
        from datetime import datetime
        current_time = datetime.utcnow()

        mapping = ChannelCategoryMapping(
            provider_id=provider.id,
            service_id=uuid.UUID(service_id),
            is_active=request.is_active,
            created_at=current_time,
            updated_at=current_time
        )

        self.db.add(mapping)
        await self.db.commit()
        await self.db.refresh(mapping)

        # 返回详情
        return await self.get_category_mapping_detail(str(mapping.id))

    async def unbind_provider_from_service(self, user_id: str, service_id: str) -> bool:
        """渠道商取消绑定系统服务"""
        # 获取渠道商信息
        provider = await self._get_provider_by_user_id(user_id)

        # 查找绑定记录
        result = await self.db.execute(
            select(ChannelCategoryMapping).where(
                and_(
                    ChannelCategoryMapping.provider_id == provider.id,
                    ChannelCategoryMapping.service_id == service_id
                )
            )
        )
        mapping = result.scalar_one_or_none()
        if not mapping:
            raise NotFoundError("未找到绑定的服务")

        # 删除绑定记录
        await self.db.delete(mapping)
        await self.db.commit()

        return True

    async def update_provider_service_binding(self, user_id: str, service_id: str, request: ChannelCategoryMappingUpdate) -> ChannelCategoryMappingResponse:
        """渠道商更新服务绑定状态"""
        # 获取渠道商信息
        provider = await self._get_provider_by_user_id(user_id)

        # 查找绑定记录
        result = await self.db.execute(
            select(ChannelCategoryMapping).where(
                and_(
                    ChannelCategoryMapping.provider_id == provider.id,
                    ChannelCategoryMapping.service_id == service_id
                )
            )
        )
        mapping = result.scalar_one_or_none()
        if not mapping:
            raise NotFoundError("未找到绑定的服务")

        # 更新字段 - 只更新实际存在的字段
        update_fields = ['is_active']  # 只有 is_active 字段在数据库模型中存在
        for field in update_fields:
            if hasattr(request, field):
                value = getattr(request, field)
                if value is not None:
                    setattr(mapping, field, value)

        mapping.updated_at = datetime.utcnow()

        await self.db.commit()
        await self.db.refresh(mapping)

        # 返回详情
        return await self.get_category_mapping_detail(str(mapping.id))


 