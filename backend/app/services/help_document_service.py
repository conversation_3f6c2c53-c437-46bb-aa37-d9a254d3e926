"""
帮助文档服务层
"""
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_, desc, asc
from sqlalchemy.orm import selectinload, joinedload
from typing import List, Optional, Dict, Any
from datetime import datetime
from uuid import UUID

from app.models.help_document import HelpCategory, HelpDocument, HelpDocumentMedia
from app.schemas.help_document import (
    HelpCategoryCreate, HelpCategoryUpdate, HelpCategoryResponse,
    HelpDocumentCreate, HelpDocumentUpdate, HelpDocumentResponse, HelpDocumentDetailResponse,
    HelpDocumentSearchRequest, HelpDocumentStatistics,
    BatchSortUpdate, BatchOperationResponse
)
from app.exceptions import NotFoundError, ValidationError, PermissionError
import os
import uuid
import secrets
import string
import re

# TOS服务导入
from app.services.tos_service import tos_service


class HelpCategoryService:
    """帮助文档分类服务"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_category(self, data: HelpCategoryCreate) -> HelpCategoryResponse:
        """创建分类"""
        # 检查名称是否重复
        existing = await self.db.execute(
            select(HelpCategory).where(HelpCategory.name == data.name)
        )
        if existing.scalar_one_or_none():
            raise ValidationError(f"分类名称 '{data.name}' 已存在")
        
        # 创建分类
        category = HelpCategory(**data.dict())
        self.db.add(category)
        await self.db.commit()
        await self.db.refresh(category)
        
        return HelpCategoryResponse.from_orm(category)
    
    async def get_categories(
        self, 
        is_active: Optional[bool] = None,
        include_document_count: bool = False
    ) -> List[HelpCategoryResponse]:
        """获取分类列表"""
        query = select(HelpCategory)
        
        # 过滤条件
        if is_active is not None:
            query = query.where(HelpCategory.is_active == is_active)
        
        # 排序
        query = query.order_by(HelpCategory.sort_order, HelpCategory.id)
        
        # 是否包含文档关联
        if include_document_count:
            query = query.options(selectinload(HelpCategory.documents))
        
        result = await self.db.execute(query)
        categories = result.scalars().all()
        
        # 转换响应
        response_list = []
        for category in categories:
            cat_dict = category.to_dict(include_document_count=include_document_count)
            response_list.append(HelpCategoryResponse(**cat_dict))
        
        return response_list
    
    async def get_category_by_id(self, category_id: int) -> HelpCategoryResponse:
        """获取分类详情"""
        result = await self.db.execute(
            select(HelpCategory)
            .where(HelpCategory.id == category_id)
            .options(selectinload(HelpCategory.documents))
        )
        category = result.scalar_one_or_none()
        
        if not category:
            raise NotFoundError(f"分类 {category_id} 不存在")
        
        cat_dict = category.to_dict(include_document_count=True)
        return HelpCategoryResponse(**cat_dict)
    
    async def update_category(
        self, 
        category_id: int, 
        data: HelpCategoryUpdate
    ) -> HelpCategoryResponse:
        """更新分类"""
        # 获取分类
        result = await self.db.execute(
            select(HelpCategory).where(HelpCategory.id == category_id)
        )
        category = result.scalar_one_or_none()
        
        if not category:
            raise NotFoundError(f"分类 {category_id} 不存在")
        
        # 如果更新名称，检查是否重复
        if data.name and data.name != category.name:
            existing = await self.db.execute(
                select(HelpCategory).where(
                    and_(
                        HelpCategory.name == data.name,
                        HelpCategory.id != category_id
                    )
                )
            )
            if existing.scalar_one_or_none():
                raise ValidationError(f"分类名称 '{data.name}' 已存在")
        
        # 更新字段
        update_data = data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(category, field, value)
        
        category.updated_at = datetime.utcnow()
        await self.db.commit()
        await self.db.refresh(category)
        
        return HelpCategoryResponse.from_orm(category)
    
    async def delete_category(self, category_id: int) -> BatchOperationResponse:
        """删除分类"""
        # 获取分类
        result = await self.db.execute(
            select(HelpCategory)
            .where(HelpCategory.id == category_id)
            .options(selectinload(HelpCategory.documents))
        )
        category = result.scalar_one_or_none()
        
        if not category:
            raise NotFoundError(f"分类 {category_id} 不存在")
        
        # 检查是否有文档
        if category.documents:
            raise ValidationError(f"分类下有 {len(category.documents)} 篇文档，无法删除")
        
        # 删除分类
        await self.db.delete(category)
        await self.db.commit()
        
        return BatchOperationResponse(
            success=True,
            affected_count=1,
            message=f"分类 '{category.name}' 已删除"
        )
    
    async def batch_update_sort(self, data: BatchSortUpdate) -> BatchOperationResponse:
        """批量更新排序"""
        updated_count = 0
        
        for item in data.items:
            result = await self.db.execute(
                select(HelpCategory).where(HelpCategory.id == item['id'])
            )
            category = result.scalar_one_or_none()
            
            if category:
                category.sort_order = item['sort_order']
                category.updated_at = datetime.utcnow()
                updated_count += 1
        
        await self.db.commit()
        
        return BatchOperationResponse(
            success=True,
            affected_count=updated_count,
            message=f"已更新 {updated_count} 个分类的排序"
        )


class HelpDocumentService:
    """帮助文档服务"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_document(
        self, 
        data: HelpDocumentCreate,
        created_by: UUID
    ) -> HelpDocumentResponse:
        """创建文档"""
        # 验证分类存在
        category_result = await self.db.execute(
            select(HelpCategory).where(HelpCategory.id == data.category_id)
        )
        if not category_result.scalar_one_or_none():
            raise NotFoundError(f"分类 {data.category_id} 不存在")
        
        # 创建文档
        document = HelpDocument(
            **data.dict(),
            created_by=created_by
        )
        self.db.add(document)
        await self.db.commit()
        await self.db.refresh(document)
        
        # 加载关联数据
        result = await self.db.execute(
            select(HelpDocument)
            .where(HelpDocument.id == document.id)
            .options(joinedload(HelpDocument.category), joinedload(HelpDocument.creator))
        )
        document = result.scalar_one()
        
        return HelpDocumentResponse(**document.to_dict())
    
    async def get_documents(
        self, 
        request: HelpDocumentSearchRequest,
        is_admin: bool = False
    ) -> Dict[str, Any]:
        """获取文档列表"""
        query = select(HelpDocument).options(
            joinedload(HelpDocument.category),
            joinedload(HelpDocument.creator)
        )
        
        # 过滤条件
        conditions = []
        
        # 非管理员只能看到已发布的文档
        if not is_admin:
            conditions.append(HelpDocument.is_published == True)
        elif request.is_published is not None:
            conditions.append(HelpDocument.is_published == request.is_published)
        
        # 分类过滤
        if request.category_id:
            conditions.append(HelpDocument.category_id == request.category_id)
        
        # 关键词搜索
        if request.keyword:
            keyword_pattern = f"%{request.keyword}%"
            conditions.append(
                or_(
                    HelpDocument.title.ilike(keyword_pattern),
                    HelpDocument.content.ilike(keyword_pattern)
                )
            )
        
        if conditions:
            query = query.where(and_(*conditions))
        
        # 排序
        sort_field = getattr(HelpDocument, request.sort_by)
        if request.sort_order == "desc":
            query = query.order_by(desc(sort_field))
        else:
            query = query.order_by(asc(sort_field))
        
        # 获取总数
        count_query = select(func.count()).select_from(HelpDocument)
        if conditions:
            count_query = count_query.where(and_(*conditions))
        total_result = await self.db.execute(count_query)
        total = total_result.scalar() or 0
        
        # 分页
        offset = (request.page - 1) * request.page_size
        query = query.offset(offset).limit(request.page_size)
        
        # 执行查询
        result = await self.db.execute(query)
        documents = result.scalars().all()
        
        # 转换响应
        items = [HelpDocumentResponse(**doc.to_dict()) for doc in documents]
        
        return {
            "items": items,
            "total": total,
            "page": request.page,
            "page_size": request.page_size
        }
    
    async def get_document_by_id(
        self, 
        document_id: int,
        increment_view: bool = False,
        is_admin: bool = False
    ) -> HelpDocumentDetailResponse:
        """获取文档详情"""
        query = select(HelpDocument).where(HelpDocument.id == document_id)
        
        # 加载关联数据
        query = query.options(
            joinedload(HelpDocument.category),
            joinedload(HelpDocument.creator),
            selectinload(HelpDocument.media_files)
        )
        
        result = await self.db.execute(query)
        document = result.scalar_one_or_none()
        
        if not document:
            raise NotFoundError(f"文档 {document_id} 不存在")
        
        # 非管理员检查发布状态
        if not is_admin and not document.is_published:
            raise PermissionError("文档未发布")
        
        # 增加浏览次数
        if increment_view and document.is_published:
            document.view_count += 1
            await self.db.commit()
            # 刷新对象以确保所有属性都已加载
            await self.db.refresh(document)

        # 转换响应
        doc_dict = document.to_dict(include_content=True, include_media=True)
        return HelpDocumentDetailResponse(**doc_dict)
    
    async def update_document(
        self,
        document_id: int,
        data: HelpDocumentUpdate
    ) -> HelpDocumentResponse:
        """更新文档"""
        # 获取文档及其媒体文件
        result = await self.db.execute(
            select(HelpDocument)
            .where(HelpDocument.id == document_id)
            .options(selectinload(HelpDocument.media_files))
        )
        document = result.scalar_one_or_none()

        if not document:
            raise NotFoundError(f"文档 {document_id} 不存在")

        # 如果更新分类，验证分类存在
        if data.category_id:
            category_result = await self.db.execute(
                select(HelpCategory).where(HelpCategory.id == data.category_id)
            )
            if not category_result.scalar_one_or_none():
                raise NotFoundError(f"分类 {data.category_id} 不存在")

        # 如果更新了内容，需要清理未使用的媒体文件
        if data.content is not None:
            await self._cleanup_unused_media_files(document, data.content)

        # 更新字段
        update_data = data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(document, field, value)
        
        document.updated_at = datetime.utcnow()
        await self.db.commit()
        
        # 重新加载关联数据
        result = await self.db.execute(
            select(HelpDocument)
            .where(HelpDocument.id == document_id)
            .options(joinedload(HelpDocument.category), joinedload(HelpDocument.creator))
        )
        document = result.scalar_one()
        
        return HelpDocumentResponse(**document.to_dict())
    
    async def delete_document(self, document_id: int) -> BatchOperationResponse:
        """删除文档及其所有媒体文件"""
        # 获取文档及其媒体文件
        result = await self.db.execute(
            select(HelpDocument)
            .where(HelpDocument.id == document_id)
            .options(selectinload(HelpDocument.media_files))
        )
        document = result.scalar_one_or_none()

        if not document:
            raise NotFoundError(f"文档 {document_id} 不存在")

        title = document.title
        media_count = len(document.media_files)

        # 先删除所有关联的媒体文件（从TOS和数据库）
        deleted_media_count = 0
        failed_media_files = []

        for media in document.media_files:
            try:
                # 删除TOS中的文件
                await tos_service.delete_file(media.file_path)
                deleted_media_count += 1
            except Exception as e:
                # 记录删除失败的文件，但继续删除其他文件
                failed_media_files.append(f"{media.file_name}: {str(e)}")

        # 删除文档（级联删除媒体文件记录）
        await self.db.delete(document)
        await self.db.commit()

        # 构建结果消息
        message = f"文档 '{title}' 已删除"
        if media_count > 0:
            message += f"，共删除 {deleted_media_count}/{media_count} 个媒体文件"
            if failed_media_files:
                message += f"，{len(failed_media_files)} 个文件删除失败"

        return BatchOperationResponse(
            success=True,
            affected_count=1,
            message=message,
            details={"failed_media_files": failed_media_files} if failed_media_files else None
        )
    
    async def batch_update_sort(self, data: BatchSortUpdate) -> BatchOperationResponse:
        """批量更新排序"""
        updated_count = 0
        
        for item in data.items:
            result = await self.db.execute(
                select(HelpDocument).where(HelpDocument.id == item['id'])
            )
            document = result.scalar_one_or_none()
            
            if document:
                document.sort_order = item['sort_order']
                document.updated_at = datetime.utcnow()
                updated_count += 1
        
        await self.db.commit()
        
        return BatchOperationResponse(
            success=True,
            affected_count=updated_count,
            message=f"已更新 {updated_count} 篇文档的排序"
        )
    
    async def get_statistics(self) -> HelpDocumentStatistics:
        """获取统计信息"""
        # 分类统计
        total_categories = await self.db.execute(
            select(func.count(HelpCategory.id))
        )
        active_categories = await self.db.execute(
            select(func.count(HelpCategory.id)).where(HelpCategory.is_active == True)
        )
        
        # 文档统计
        total_documents = await self.db.execute(
            select(func.count(HelpDocument.id))
        )
        published_documents = await self.db.execute(
            select(func.count(HelpDocument.id)).where(HelpDocument.is_published == True)
        )
        
        # 浏览量统计
        total_views = await self.db.execute(
            select(func.sum(HelpDocument.view_count))
        )
        
        # 媒体文件统计
        total_media = await self.db.execute(
            select(func.count(HelpDocumentMedia.id))
        )
        total_images = await self.db.execute(
            select(func.count(HelpDocumentMedia.id)).where(HelpDocumentMedia.file_type == 'image')
        )
        total_videos = await self.db.execute(
            select(func.count(HelpDocumentMedia.id)).where(HelpDocumentMedia.file_type == 'video')
        )
        
        return HelpDocumentStatistics(
            total_categories=total_categories.scalar() or 0,
            active_categories=active_categories.scalar() or 0,
            total_documents=total_documents.scalar() or 0,
            published_documents=published_documents.scalar() or 0,
            total_views=total_views.scalar() or 0,
            total_media_files=total_media.scalar() or 0,
            total_images=total_images.scalar() or 0,
            total_videos=total_videos.scalar() or 0
        )

    # ========== 媒体文件管理 ==========

    async def _cleanup_unused_media_files(self, document, new_content: str):
        """清理文档中未使用的媒体文件"""
        if not document.media_files:
            return

        # 提取新内容中使用的所有媒体文件URL
        used_urls = set()

        # 检查图片 ![alt](url)
        import re
        image_pattern = r'!\[.*?\]\((.*?)\)'
        image_matches = re.findall(image_pattern, new_content)
        used_urls.update(image_matches)

        # 检查视频 <video><source src="url">
        video_pattern = r'<source\s+src="([^"]+)"'
        video_matches = re.findall(video_pattern, new_content)
        used_urls.update(video_matches)

        # 检查HTML video标签的src属性
        video_src_pattern = r'<video[^>]*src="([^"]+)"'
        video_src_matches = re.findall(video_src_pattern, new_content)
        used_urls.update(video_src_matches)

        # 找出未使用的媒体文件
        unused_media = []
        for media in document.media_files:
            media_url = media.get_file_url()
            # 检查媒体文件的URL是否在内容中被使用
            is_used = False
            for used_url in used_urls:
                # 比较URL，考虑可能的域名差异
                if media.file_path in used_url or used_url.endswith(media.file_path):
                    is_used = True
                    break

            if not is_used:
                unused_media.append(media)

        # 删除未使用的媒体文件
        for media in unused_media:
            try:
                # 删除TOS中的文件
                await tos_service.delete_file(media.file_path)
                # 删除数据库记录
                await self.db.delete(media)
                print(f"已删除未使用的媒体文件: {media.file_name}")
            except Exception as e:
                print(f"删除媒体文件失败 {media.file_name}: {e}")

        if unused_media:
            await self.db.commit()

    def _generate_help_storage_key(self, document_id: int, file_type: str, file_name: str) -> str:
        """生成帮助文档专用的存储键"""
        timestamp = datetime.utcnow().strftime("%Y%m%d/%H%M%S")
        random_suffix = ''.join(secrets.choice(string.ascii_lowercase + string.digits) for _ in range(8))
        safe_filename = "".join(c for c in file_name if c.isalnum() or c in ".-_") if file_name else "file"

        # 帮助文档专用路径：help-docs/{file_type}/{document_id}/{timestamp}/{random_suffix}_{filename}
        return f"help-docs/{file_type}/{document_id}/{timestamp}/{random_suffix}_{safe_filename}"

    async def upload_media(
        self,
        document_id: int,
        file_type: str,
        file_data: bytes,
        file_name: str,
        mime_type: str
    ) -> HelpDocumentMedia:
        """上传媒体文件到TOS"""
        # 验证文档存在
        document_result = await self.db.execute(
            select(HelpDocument).where(HelpDocument.id == document_id)
        )
        if not document_result.scalar_one_or_none():
            raise NotFoundError(f"文档 {document_id} 不存在")

        # 生成TOS存储键
        storage_key = self._generate_help_storage_key(document_id, file_type, file_name)

        try:
            # 上传到TOS
            upload_result = await tos_service.upload_file(storage_key, file_data, mime_type)

            # 保存到数据库
            media = HelpDocumentMedia(
                document_id=document_id,
                file_type=file_type,
                file_name=file_name,
                file_path=storage_key,  # 存储TOS的storage_key
                file_size=len(file_data),
                mime_type=mime_type
            )

            self.db.add(media)
            await self.db.commit()
            await self.db.refresh(media)

            return media

        except Exception as e:
            raise ValidationError(f"文件上传失败: {str(e)}")

    async def get_media_by_id(self, media_id: int) -> HelpDocumentMedia:
        """获取媒体文件信息"""
        result = await self.db.execute(
            select(HelpDocumentMedia).where(HelpDocumentMedia.id == media_id)
        )
        media = result.scalar_one_or_none()

        if not media:
            raise NotFoundError(f"媒体文件 {media_id} 不存在")

        return media

    async def delete_media(self, media_id: int) -> bool:
        """删除媒体文件（仅从TOS删除）"""
        result = await self.db.execute(
            select(HelpDocumentMedia).where(HelpDocumentMedia.id == media_id)
        )
        media = result.scalar_one_or_none()

        if not media:
            raise NotFoundError(f"媒体文件 {media_id} 不存在")

        # 先删除TOS中的文件，失败则抛出异常
        try:
            await tos_service.delete_file(media.file_path)  # file_path存储的是storage_key
        except Exception as e:
            # TOS删除失败，不删除数据库记录
            raise ValidationError(f"删除TOS文件失败: {str(e)}")

        # TOS删除成功后，删除数据库记录
        await self.db.delete(media)
        await self.db.commit()

        return True

    # ========== 批量操作 ==========

    async def batch_sort_categories(self, data: BatchSortUpdate) -> BatchOperationResponse:
        """批量更新分类排序"""
        affected_count = 0

        for item in data.items:
            result = await self.db.execute(
                select(HelpCategory).where(HelpCategory.id == item.id)
            )
            category = result.scalar_one_or_none()

            if category:
                category.sort_order = item.sort_order
                affected_count += 1

        await self.db.commit()

        return BatchOperationResponse(
            success=True,
            affected_count=affected_count,
            message=f"成功更新 {affected_count} 个分类的排序"
        )

    async def batch_sort_documents(self, data: BatchSortUpdate) -> BatchOperationResponse:
        """批量更新文档排序"""
        affected_count = 0

        for item in data.items:
            result = await self.db.execute(
                select(HelpDocument).where(HelpDocument.id == item.id)
            )
            document = result.scalar_one_or_none()

            if document:
                document.sort_order = item.sort_order
                affected_count += 1

        await self.db.commit()

        return BatchOperationResponse(
            success=True,
            affected_count=affected_count,
            message=f"成功更新 {affected_count} 个文档的排序"
        )


class HelpDocumentPublicService:
    """帮助文档公开服务（前台使用）"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.document_service = HelpDocumentService(db)
    
    async def get_public_categories(self) -> List[Dict[str, Any]]:
        """获取公开分类列表（仅显示有文档的活跃分类）"""
        # 查询活跃分类及其文档数量
        query = (
            select(
                HelpCategory,
                func.count(HelpDocument.id).label('doc_count')
            )
            .outerjoin(
                HelpDocument,
                and_(
                    HelpDocument.category_id == HelpCategory.id,
                    HelpDocument.is_published == True
                )
            )
            .where(HelpCategory.is_active == True)
            .group_by(HelpCategory.id)
            .having(func.count(HelpDocument.id) > 0)
            .order_by(HelpCategory.sort_order, HelpCategory.id)
        )
        
        result = await self.db.execute(query)
        categories = result.all()
        
        # 转换响应
        response = []
        for category, doc_count in categories:
            response.append({
                'id': category.id,
                'name': category.name,
                'description': category.description,
                'icon': category.icon,
                'document_count': doc_count
            })
        
        return response
    
    async def get_public_documents(
        self, 
        category_id: Optional[int] = None,
        keyword: Optional[str] = None,
        page: int = 1,
        page_size: int = 20
    ) -> Dict[str, Any]:
        """获取公开文档列表"""
        request = HelpDocumentSearchRequest(
            category_id=category_id,
            keyword=keyword,
            is_published=True,
            page=page,
            page_size=page_size,
            sort_by="sort_order",
            sort_order="asc"
        )
        
        return await self.document_service.get_documents(request, is_admin=False)
    
    async def get_public_document_detail(self, document_id: int) -> HelpDocumentDetailResponse:
        """获取公开文档详情"""
        return await self.document_service.get_document_by_id(
            document_id, 
            increment_view=True,
            is_admin=False
        )