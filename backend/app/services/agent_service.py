from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func, desc, asc, case
from app.models.agent import Agent, Commission, CommissionSettlement
from app.models.user import User
from app.schemas.agent import *
from app.exceptions import ValidationError, NotFoundError, PermissionError
from typing import Optional, Dict, Any, List
import secrets
import string
from datetime import datetime, timedelta
from decimal import Decimal

class AgentService:
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_agent_profile(self, user_id: str, request: AgentProfileRequest) -> AgentResponse:
        """创建代理商信息"""
        # 检查用户是否已有代理商信息
        existing_agent = await self.db.execute(
            select(Agent).where(Agent.user_id == user_id)
        )
        if existing_agent.scalar_one_or_none():
            raise ValidationError("代理商信息已存在，请使用更新接口")
        
        # 生成代理商代码
        agent_code = await self._generate_agent_code()
        
        # 创建代理商信息
        agent = Agent(
            user_id=user_id,
            agent_name=request.agent_name,
            agent_code=agent_code,
            contact_phone=request.contact_phone,
            contact_email=request.contact_email,
            office_address=request.office_address,
            agent_description=request.agent_description,
            service_regions=request.service_regions,
            specialties=request.specialties
        )
        
        self.db.add(agent)
        await self.db.commit()
        await self.db.refresh(agent)
        
        return await self._build_agent_response(agent)
    
    async def get_agent_info(self, user_id: str) -> AgentResponse:
        """获取代理商信息"""
        agent = await self._get_agent_by_user_id(user_id)
        return await self._build_agent_response(agent)

    async def get_agent_list(self, query: 'AgentListQuery') -> Dict[str, Any]:
        """获取代理商列表（管理员）"""
        from app.schemas.agent import AgentListQuery

        # 构建查询条件
        conditions = []

        if query.verification_status:
            conditions.append(Agent.verification_status == query.verification_status)
        if query.agent_level:
            conditions.append(Agent.agent_level == query.agent_level)
        if query.search:
            conditions.append(
                or_(
                    Agent.agent_name.ilike(f"%{query.search}%"),
                    Agent.agent_code.ilike(f"%{query.search}%"),
                    Agent.contact_email.ilike(f"%{query.search}%")
                )
            )

        # 构建基础查询
        base_query = select(Agent).where(Agent.is_active == True)
        if conditions:
            base_query = base_query.where(and_(*conditions))

        # 排序
        if query.sort_order == "desc":
            base_query = base_query.order_by(desc(getattr(Agent, query.sort_by)))
        else:
            base_query = base_query.order_by(asc(getattr(Agent, query.sort_by)))

        # 分页
        offset = (query.page - 1) * query.size
        paginated_query = base_query.offset(offset).limit(query.size)

        # 执行查询
        result = await self.db.execute(paginated_query)
        agents = result.scalars().all()

        # 获取总数
        count_query = select(func.count(Agent.id)).where(Agent.is_active == True)
        if conditions:
            count_query = count_query.where(and_(*conditions))
        total_result = await self.db.execute(count_query)
        total = total_result.scalar()

        # 构建响应数据
        items = []
        for agent in agents:
            agent_response = await self._build_agent_response(agent)
            items.append(agent_response)

        return {
            "agents": items,
            "pagination": {
                "total": total,
                "page": query.page,
                "size": query.size,
                "pages": (total + query.size - 1) // query.size
            }
        }
    
    async def update_agent_info(self, user_id: str, request: AgentUpdateRequest) -> AgentResponse:
        """更新代理商信息"""
        # 获取代理商信息
        agent = await self._get_agent_by_user_id(user_id)
        
        # 记录变更前的数据
        old_data = {}
        new_data = {}
        
        # 更新允许修改的字段
        if request.contact_phone is not None:
            old_data["contact_phone"] = agent.contact_phone
            agent.contact_phone = request.contact_phone
            new_data["contact_phone"] = request.contact_phone
            
        if request.contact_email is not None:
            old_data["contact_email"] = agent.contact_email
            agent.contact_email = request.contact_email
            new_data["contact_email"] = request.contact_email
            
        if request.office_address is not None:
            old_data["office_address"] = agent.office_address
            agent.office_address = request.office_address
            new_data["office_address"] = request.office_address
            
        if request.agent_description is not None:
            old_data["agent_description"] = agent.agent_description
            agent.agent_description = request.agent_description
            new_data["agent_description"] = request.agent_description
            
        if request.service_regions is not None:
            old_data["service_regions"] = agent.service_regions
            agent.service_regions = request.service_regions
            new_data["service_regions"] = request.service_regions
            
        if request.specialties is not None:
            old_data["specialties"] = agent.specialties
            agent.specialties = request.specialties
            new_data["specialties"] = request.specialties
        
        agent.updated_at = datetime.utcnow()
        
        # 记录活动日志（如果有活动日志表的话）
        # 这里暂时注释掉，因为可能没有 AgentActivity 表
        # if old_data:
        #     activity = AgentActivity(
        #         agent_id=agent.id,
        #         user_id=user_id,
        #         activity_type="updated",
        #         description="代理商信息更新",
        #         old_data=old_data,
        #         new_data=new_data
        #     )
        #     self.db.add(activity)
        
        await self.db.commit()
        
        return await self._build_agent_response(agent)
    
    async def get_referral_list(self, user_id: str, query: ReferralListQuery) -> Dict[str, Any]:
        """获取推荐用户列表"""
        # 获取代理商信息
        agent = await self._get_agent_by_user_id(user_id)
        
        # 构建查询条件（模拟推荐关系，实际应该有referral表）
        conditions = []
        
        if query.status:
            conditions.append(User.status == query.status)
        if query.start_date:
            conditions.append(User.created_at >= query.start_date)
        if query.end_date:
            conditions.append(User.created_at <= query.end_date)
        
        # 构建查询（模拟获取推荐用户）
        from app.models.user import UserStatus
        base_query = select(User).where(User.status == UserStatus.ACTIVE)  # 简化处理
        if conditions:
            base_query = base_query.where(and_(*conditions))
        
        # 排序
        if query.sort_order == "desc":
            base_query = base_query.order_by(desc(getattr(User, query.sort_by)))
        else:
            base_query = base_query.order_by(asc(getattr(User, query.sort_by)))
        
        # 分页
        offset = (query.page - 1) * query.size
        paginated_query = base_query.offset(offset).limit(query.size)
        
        # 执行查询
        result = await self.db.execute(paginated_query)
        users = result.scalars().all()
        
        # 获取总数
        count_query = select(func.count(User.id)).where(User.status == 'active')
        if conditions:
            count_query = count_query.where(and_(*conditions))
        total_result = await self.db.execute(count_query)
        total = total_result.scalar()
        
        # 构建响应数据
        items = []
        for user in users:
            try:
                # 获取用户统计信息（模拟）
                user_stats = await self._get_user_commission_stats(str(user.id), str(agent.id))

                referral_response = ReferralResponse(
                    id=str(user.id),
                    full_name=user.full_name or "",  # 确保不为None
                    phone=user.phone,
                    email=user.email,
                    user_type="enterprise_user",  # 简化处理
                    company_name=None,  # TODO: 获取企业名称
                    status=user.status.value,  # 转换枚举为字符串
                    total_orders=user_stats.get('total_orders', 0),
                    total_spent=user_stats.get('total_spent', Decimal('0.00')),
                    generated_commission=user_stats.get('generated_commission', Decimal('0.00')),
                    last_order_time=user_stats.get('last_order_time'),
                    registered_at=user.created_at
                )
                items.append(referral_response)
            except Exception as e:
                # 记录错误但继续处理其他用户
                print(f"处理用户 {user.id} 时出错: {e}")
                continue
        
        return {
            "items": items,
            "pagination": {
                "total": total,
                "page": query.page,
                "size": query.size,
                "pages": (total + query.size - 1) // query.size
            },
            "summary": {
                "total_referrals": total,
                "active_users": len([u for u in users if u.status == UserStatus.ACTIVE]),
                "total_commission": sum(item.generated_commission for item in items)
            }
        }
    
    async def get_commission_list(self, user_id: str, query: CommissionListQuery) -> Dict[str, Any]:
        """获取佣金记录列表"""
        # 获取代理商信息
        agent = await self._get_agent_by_user_id(user_id)
        
        # 构建查询条件
        conditions = [Commission.agent_id == agent.id]
        
        if query.status:
            conditions.append(Commission.status == query.status)
        if query.order_type:
            conditions.append(Commission.order_type == query.order_type)
        if query.start_date:
            conditions.append(Commission.commission_date >= query.start_date)
        if query.end_date:
            conditions.append(Commission.commission_date <= query.end_date)
        
        # 构建查询
        base_query = select(Commission).where(and_(*conditions))
        base_query = base_query.order_by(desc(Commission.commission_date))
        
        # 分页
        offset = (query.page - 1) * query.size
        paginated_query = base_query.offset(offset).limit(query.size)
        
        # 执行查询
        result = await self.db.execute(paginated_query)
        commissions = result.scalars().all()
        
        # 获取总数
        count_query = select(func.count(Commission.id)).where(and_(*conditions))
        total_result = await self.db.execute(count_query)
        total = total_result.scalar()
        
        # 构建响应数据
        items = []
        for commission in commissions:
            # 获取客户信息
            customer_result = await self.db.execute(
                select(User).where(User.id == commission.customer_id)
            )
            customer = customer_result.scalar_one_or_none()
            
            commission_response = CommissionResponse(
                id=str(commission.id),
                order_no=commission.order_no,
                order_type=commission.order_type,
                product_name=commission.product_name,
                customer_name=customer.full_name if customer else "未知用户",
                customer_company=None,  # TODO: 获取企业名称
                order_amount=commission.order_amount,
                commission_rate=commission.commission_rate,
                commission_amount=commission.commission_amount,
                status=commission.status,
                commission_date=commission.commission_date,
                settlement_id=str(commission.settlement_id) if commission.settlement_id else None,
                settled_at=commission.settled_at
            )
            items.append(commission_response)
        
        # 计算汇总信息
        summary_query = select(
            func.sum(Commission.commission_amount).label('total_commission'),
            func.sum(case((Commission.status == 'pending', Commission.commission_amount), else_=0)).label('pending_commission'),
            func.sum(case((Commission.status == 'settled', Commission.commission_amount), else_=0)).label('settled_commission')
        ).where(and_(*conditions))
        
        summary_result = await self.db.execute(summary_query)
        summary = summary_result.first()
        
        return {
            "items": items,
            "pagination": {
                "total": total,
                "page": query.page,
                "size": query.size,
                "pages": (total + query.size - 1) // query.size
            },
            "summary": {
                "total_commission": summary.total_commission or Decimal('0.00'),
                "pending_commission": summary.pending_commission or Decimal('0.00'),
                "settled_commission": summary.settled_commission or Decimal('0.00'),
                "this_period_commission": sum(item.commission_amount for item in items)
            }
        }
    
    async def apply_commission_settlement(self, user_id: str, request: CommissionSettlementRequest) -> CommissionSettlementResponse:
        """申请佣金结算"""
        # 获取代理商信息
        agent = await self._get_agent_by_user_id(user_id)
        
        # 检查待结算佣金是否足够
        pending_commission_result = await self.db.execute(
            select(func.sum(Commission.commission_amount))
            .where(and_(Commission.agent_id == agent.id, Commission.status == 'pending'))
        )
        pending_commission = pending_commission_result.scalar() or Decimal('0.00')
        
        if pending_commission < request.settlement_amount:
            raise ValidationError(f"待结算佣金不足，当前可结算金额: {pending_commission}")
        
        # 计算涉及的佣金记录数
        commission_count_result = await self.db.execute(
            select(func.count(Commission.id))
            .where(and_(Commission.agent_id == agent.id, Commission.status == 'pending'))
        )
        commission_count = commission_count_result.scalar() or 0
        
        # 创建结算申请
        settlement = CommissionSettlement(
            agent_id=agent.id,
            settlement_amount=request.settlement_amount,
            commission_records_count=commission_count,
            bank_account=request.bank_account,
            note=request.note,
            expected_settlement_date=datetime.utcnow() + timedelta(days=5)
        )
        
        self.db.add(settlement)
        await self.db.commit()
        await self.db.refresh(settlement)
        
        return CommissionSettlementResponse(
            settlement_id=str(settlement.id),
            agent_id=str(agent.id),
            settlement_amount=settlement.settlement_amount,
            commission_records_count=settlement.commission_records_count,
            status=settlement.status,
            bank_account={
                "account_name": settlement.bank_account.get("account_name"),
                "bank_name": settlement.bank_account.get("bank_name"),
                "account_number": settlement.bank_account.get("account_number", "")[-4:]  # 只显示后4位
            },
            applied_at=settlement.applied_at,
            expected_settlement_date=settlement.expected_settlement_date
        )
    
    
    async def _build_agent_response(self, agent: Agent) -> AgentResponse:
        """构建代理商响应数据"""
        return AgentResponse(
            id=str(agent.id),
            agent_name=agent.agent_name,
            agent_code=agent.agent_code,
            agent_level=agent.agent_level,  # 已经是字符串
            contact_phone=agent.contact_phone,
            contact_email=agent.contact_email,
            office_address=agent.office_address,
            agent_description=agent.agent_description,
            service_regions=agent.service_regions,
            specialties=agent.specialties,
            verification_status=agent.verification_status,  # 已经是字符串
            verification_time=agent.verification_time,
            commission_config={
                "subscription_rate": agent.subscription_rate,
                "content_service_rate": agent.content_service_rate,
                "settlement_cycle": agent.settlement_cycle
            },
            created_at=agent.created_at,
            updated_at=agent.updated_at
        )
    
    async def _get_agent_by_user_id(self, user_id: str) -> Agent:
        """根据用户ID获取代理商信息"""
        result = await self.db.execute(
            select(Agent).where(Agent.user_id == user_id)
        )
        agent = result.scalar_one_or_none()
        if not agent:
            raise NotFoundError("代理商信息不存在")
        return agent
    
    async def _generate_agent_code(self, length: int = 8) -> str:
        """生成代理商代码"""
        while True:
            prefix = "AGENT"
            suffix = ''.join(secrets.choice(string.digits) for _ in range(4))
            candidate_code = f"{prefix}{suffix}"
            
            # 检查代码是否已存在
            existing_code = await self.db.execute(
                select(Agent).where(Agent.agent_code == candidate_code)
            )
            if not existing_code.scalar_one_or_none():
                return candidate_code
    
    async def _get_user_commission_stats(self, user_id: str, agent_id: str) -> Dict[str, Any]:
        """获取用户佣金统计"""
        # TODO: 实现用户佣金统计逻辑
        return {
            "total_orders": 0,
            "total_spent": Decimal('0.00'),
            "generated_commission": Decimal('0.00'),
            "last_order_time": None
        }

    async def verify_agent(self, agent_id: str, request: 'AgentVerificationRequest', verifier_id: str) -> Dict[str, Any]:
        """代理商审核（管理员）"""
        from app.schemas.agent import AgentVerificationRequest

        result = await self.db.execute(
            select(Agent).where(Agent.id == agent_id)
        )
        agent = result.scalar_one_or_none()
        if not agent:
            raise NotFoundError("代理商信息不存在")

        old_status = agent.verification_status
        agent.verification_status = request.status
        agent.verification_time = datetime.utcnow()
        agent.verification_note = request.note
        agent.verifier_id = verifier_id
        agent.updated_at = datetime.utcnow()

        await self.db.commit()

        # 如果审核通过，自动为用户分配代理商用户角色
        if request.status == "verified":
            try:
                from app.services.permission_service import PermissionService
                permission_service = PermissionService(self.db)
                await permission_service.assign_role_to_user(
                    user_id=str(agent.user_id),
                    role_code="agent_user",
                    assigned_by=verifier_id
                )
                print(f"审核通过，已为用户 {agent.user_id} 分配代理商用户角色")
            except Exception as e:
                print(f"分配代理商用户角色失败: {e}")
                # 不影响审核流程，继续执行

        # TODO: 发送通知
        if hasattr(request, 'notify_user') and request.notify_user:
            pass  # await self.notification_service.send_verification_result(agent, request)

        return await self._build_agent_response(agent)
