from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func, desc, asc
from app.models.content import ContentRequest, ContentDelivery, ContentRevision, RequestStatus, ReviewStatus
from app.models.company import Company
from app.models.channel import ContentProvider
from app.schemas.content import *
from app.exceptions import ValidationError, NotFoundError, PermissionError
from typing import Optional, Dict, Any, List
from datetime import datetime
from decimal import Decimal
import uuid

class ContentService:
    def __init__(self, db: AsyncSession):
        self.db = db

    def _normalize_file_data(self, files_data):
        """标准化文件数据格式"""
        if not files_data:
            return None

        if files_data == "null" or files_data == "" or files_data == []:
            return None

        # 如果是字符串列表（旧格式），直接返回
        if isinstance(files_data, list) and len(files_data) > 0:
            if isinstance(files_data[0], str):
                return files_data
            # 如果是对象列表（新格式），提取需要的信息
            elif isinstance(files_data[0], dict):
                return files_data  # 保持完整的文件信息

        return None

    def _clean_file_data(self, files_data):
        """清理文件数据，移除空对象"""
        if not files_data:
            return None

        if isinstance(files_data, list):
            # 过滤掉空对象和无效数据
            cleaned_files = []
            for file_item in files_data:
                if isinstance(file_item, str) and file_item.strip():
                    cleaned_files.append(file_item)
                elif isinstance(file_item, dict) and file_item and any(file_item.values()):
                    # 只保留有实际内容的对象
                    cleaned_files.append(file_item)

            return cleaned_files if cleaned_files else None

        return files_data

    def _normalize_tags(self, tags):
        """标准化标签数据，确保中文不被转义"""
        if not tags:
            return []

        if isinstance(tags, list):
            # 确保每个标签都是字符串，并且去除空白
            normalized_tags = []
            for tag in tags:
                if isinstance(tag, str) and tag.strip():
                    # 确保中文字符正确编码
                    clean_tag = tag.strip()
                    # 如果标签包含Unicode转义，尝试解码
                    try:
                        if '\\u' in clean_tag:
                            clean_tag = clean_tag.encode().decode('unicode_escape')
                    except:
                        pass  # 如果解码失败，使用原始标签
                    normalized_tags.append(clean_tag)
            return normalized_tags

        return []

    async def create_content_request(self, company_id: str, request: ContentRequestCreate) -> ContentRequestResponse:
        """发布内容需求"""

        # TODO: 渠道验证需要重新设计，因为channel_info表已被移除
        # 暂时注释掉渠道验证
        # channel_result = await self.db.execute(
        #     select(ChannelInfo).where(ChannelInfo.id == request.channel_id)
        # )
        # channel = channel_result.scalar_one_or_none()
        # if not channel:
        #     raise NotFoundError("指定的渠道不存在")
        
        # 创建内容需求
        try:
            # 根据需求类型处理字段
            if request.request_type == 'PUBLISH_CONTENT':
                # 稿件直发模式：企业提供稿件，渠道商负责发布
                # 主要字段是 provided_content_* 系列
                provided_content_title = request.provided_content_title
                provided_content_text = request.provided_content_text
                provided_content_files = request.provided_content_files
                creation_requirements = None  # 稿件直发模式不需要创作要求

                # 处理 "null" 字符串问题
                if provided_content_title == "null" or provided_content_title == "":
                    provided_content_title = None
                if provided_content_text == "null" or provided_content_text == "":
                    provided_content_text = None
                provided_content_files = self._normalize_file_data(provided_content_files)

            elif request.request_type == 'CREATE_CONTENT':
                # 创作需求模式：渠道商负责创作和发布
                # 主要字段是 request_title, request_description, creation_requirements
                # 也可以包含参考附件
                provided_content_title = None  # 创作模式不提供稿件
                provided_content_text = None
                provided_content_files = request.provided_content_files  # 创作需求可以有参考附件
                creation_requirements = request.creation_requirements

                # 处理 "null" 字符串问题
                if creation_requirements == "null" or creation_requirements == "":
                    creation_requirements = None
                provided_content_files = self._normalize_file_data(provided_content_files)
            else:
                raise ValidationError(f"不支持的需求类型: {request.request_type}")

            # 处理group_id：如果前端提供了group_id就使用，否则生成新的
            group_id = uuid.UUID(request.group_id) if request.group_id else uuid.uuid4()

            content_request = ContentRequest(
                group_id=group_id,
                company_id=uuid.UUID(company_id),
                provider_id=uuid.UUID(request.provider_id),
                service_id=uuid.UUID(request.service_id),
                request_type=request.request_type,
                request_title=request.request_title,
                request_description=request.request_description,
                provided_content_title=provided_content_title,
                provided_content_text=provided_content_text,
                provided_content_files=provided_content_files,
                creation_requirements=creation_requirements,
                tags=self._normalize_tags(request.tags),
                deadline=request.deadline,
                fixed_price=request.fixed_price or Decimal('0.00')  # 使用传入的价格，默认为0
            )
        except ValueError as e:
            raise ValidationError(f"无效的UUID格式: {str(e)}")
        
        self.db.add(content_request)
        await self.db.commit()
        await self.db.refresh(content_request)
        
        return await self._build_content_request_response(content_request)
    
    async def get_content_request_list(self, query: ContentRequestQuery, user_id: str = None, user_role: str = None) -> Dict[str, Any]:
        """获取内容需求列表"""
        # 构建查询条件
        conditions = []

        # 根据用户角色过滤
        if user_role == "enterprise_user":
            # 企业用户只能看自己的需求
            company_result = await self.db.execute(
                select(Company.id).where(Company.user_id == user_id)
            )
            company_id = company_result.scalar_one_or_none()
            if company_id:
                conditions.append(ContentRequest.company_id == company_id)
            else:
                # 如果企业用户没有关联企业，返回空结果
                conditions.append(ContentRequest.id.is_(None))
        elif user_role == "channel_user":
            # 渠道商只能看分配给自己的需求
            provider_result = await self.db.execute(
                select(ContentProvider.id).where(ContentProvider.user_id == user_id)
            )
            provider_id = provider_result.scalar_one_or_none()
            if provider_id:
                # 只返回分配给当前渠道商的需求
                conditions.append(ContentRequest.provider_id == provider_id)
            else:
                # 如果渠道商用户没有关联渠道商，返回空结果
                conditions.append(ContentRequest.id.is_(None))
        
        # 其他筛选条件
        if query.status:
            # 支持多个状态值，用逗号分隔
            if ',' in query.status:
                status_list = [status.strip() for status in query.status.split(',') if status.strip()]
                conditions.append(ContentRequest.status.in_(status_list))
            else:
                conditions.append(ContentRequest.status == query.status)
        if query.request_type:
            conditions.append(ContentRequest.request_type == query.request_type)
        if query.provider_id:
            conditions.append(ContentRequest.provider_id == query.provider_id)

        if query.service_id:
            conditions.append(ContentRequest.service_id == query.service_id)
        if query.keyword:
            search_condition = or_(
                ContentRequest.request_title.ilike(f"%{query.keyword}%"),
                ContentRequest.request_description.ilike(f"%{query.keyword}%")
            )
            conditions.append(search_condition)
        if query.tag:
            conditions.append(ContentRequest.tags.contains([query.tag]))
        if query.deadline_before:
            conditions.append(ContentRequest.deadline <= query.deadline_before)
        
        # 构建查询
        base_query = select(ContentRequest)
        if conditions:
            base_query = base_query.where(and_(*conditions))
        
        # 排序
        base_query = base_query.order_by(desc(ContentRequest.created_at))
        
        # 分页
        offset = (query.page - 1) * query.size
        paginated_query = base_query.offset(offset).limit(query.size)
        
        # 执行查询
        result = await self.db.execute(paginated_query)
        requests = result.scalars().all()
        
        # 获取总数
        count_query = select(func.count(ContentRequest.id))
        if conditions:
            count_query = count_query.where(and_(*conditions))
        total_result = await self.db.execute(count_query)
        total = total_result.scalar()
        
        # 构建响应数据
        items = []
        for req in requests:
            req_response = await self._build_content_request_response(req)
            items.append(req_response)
        
        return {
            "items": items,
            "pagination": {
                "total": total,
                "page": query.page,
                "size": query.size,
                "pages": (total + query.size - 1) // query.size
            },
            "statistics": await self._get_content_statistics(conditions)
        }
    
    async def apply_content_request(self, request_id: str, apply_request: ApplyRequestRequest, channel_user_id: str) -> ApplyRequestResponse:
        """申请接单"""
        # 获取内容需求
        content_request = await self._get_content_request(request_id)
        
        # 验证渠道商权限
        await self._validate_provider_permission(content_request.provider_id, channel_user_id)
        
        # 检查需求状态
        if content_request.status != RequestStatus.PENDING:
            raise ValidationError("该需求已被处理，无法接单")
        
        # 更新需求状态
        content_request.status = RequestStatus.ACCEPTED
        content_request.estimated_delivery_days = apply_request.estimated_delivery_days
        content_request.accept_message = apply_request.accept_message
        content_request.accepted_at = datetime.utcnow()
        content_request.updated_at = datetime.utcnow()
        
        await self.db.commit()
        
        return ApplyRequestResponse(
            id=str(content_request.id),
            company_id=str(content_request.company_id),
            provider_id=str(content_request.provider_id),
            service_id=str(content_request.service_id),
            request_title=content_request.request_title,
            status=content_request.status.value,
            estimated_delivery_days=content_request.estimated_delivery_days,
            fixed_price=content_request.fixed_price,
            accepted_at=content_request.accepted_at,
            updated_at=content_request.updated_at
        )
    
    async def get_content_request_detail(self, request_id: str) -> ContentRequestResponse:
        """获取内容需求详情"""
        content_request = await self._get_content_request(request_id)
        return await self._build_content_request_response(content_request)
    
    async def deliver_content(self, request_id: str, delivery_request: ContentDeliveryRequest, channel_user_id: str) -> ContentDeliveryCreateResponse:
        """内容交付"""
        # 获取内容需求
        content_request = await self._get_content_request(request_id)

        # 验证渠道商权限
        await self._validate_provider_permission(content_request.provider_id, channel_user_id)

        # 检查需求状态
        if content_request.status != RequestStatus.ACCEPTED:
            raise ValidationError(f"该需求状态为{content_request.status}，无法交付")

        # 创建内容交付记录
        delivery = ContentDelivery(
            request_id=request_id,
            content_title=delivery_request.content_title,
            content_text=delivery_request.content_text,
            content_summary=delivery_request.content_summary,
            content_images=delivery_request.content_images,
            content_videos=delivery_request.content_videos,
            content_attachments=delivery_request.content_attachments,
            content_metadata=delivery_request.content_metadata,
            delivery_note=delivery_request.delivery_note
        )

        self.db.add(delivery)

        # 更新需求状态
        content_request.status = RequestStatus.DELIVERED
        content_request.updated_at = datetime.utcnow()

        await self.db.commit()
        await self.db.refresh(delivery)
        
        return ContentDeliveryCreateResponse(
            request_id=request_id,
            delivery_id=str(delivery.id),
            content_title=delivery.content_title,
            delivery_note=delivery.delivery_note,
            delivered_at=delivery.delivered_at,
            review_status=delivery.review_status.value
        )
    
    async def get_delivered_content(self, request_id: str) -> ContentDeliveryResponse:
        """查看交付内容详情"""
        # 获取交付记录
        result = await self.db.execute(
            select(ContentDelivery).where(ContentDelivery.request_id == request_id)
        )
        delivery = result.scalar_one_or_none()
        if not delivery:
            raise NotFoundError("未找到交付内容")
        
        return ContentDeliveryResponse(
            id=str(delivery.id),
            request_id=str(delivery.request_id),
            content_title=delivery.content_title,
            content_text=delivery.content_text,
            content_summary=delivery.content_summary,
            content_images=delivery.content_images,
            content_videos=delivery.content_videos,
            content_attachments=delivery.content_attachments,
            content_metadata=delivery.content_metadata,
            delivery_note=delivery.delivery_note,
            review_status=delivery.review_status.value,
            review_note=delivery.review_note,
            review_score=delivery.review_score,
            reviewed_at=delivery.reviewed_at,
            revision_count=delivery.revision_count,
            delivered_at=delivery.delivered_at,
            updated_at=delivery.updated_at
        )
    
    async def review_content(self, request_id: str, review_request: ContentReviewRequest, company_user_id: str) -> ContentReviewResponse:
        """内容验收"""
        # 获取内容需求
        content_request = await self._get_content_request(request_id)
        
        # 验证企业用户权限
        await self._validate_company_permission(content_request.company_id, company_user_id)
        
        # 获取交付记录
        delivery_result = await self.db.execute(
            select(ContentDelivery).where(ContentDelivery.request_id == request_id)
        )
        delivery = delivery_result.scalar_one_or_none()
        if not delivery:
            raise NotFoundError("未找到交付内容")
        
        # 更新验收信息 - 将字符串转换为枚举值
        if review_request.review_status == "approved":
            delivery.review_status = ReviewStatus.APPROVED
        elif review_request.review_status == "rejected":
            delivery.review_status = ReviewStatus.REJECTED
        elif review_request.review_status == "revision_required":
            delivery.review_status = ReviewStatus.REVISION_REQUIRED
        else:
            raise ValidationError(f"无效的审核状态: {review_request.review_status}")

        delivery.review_note = review_request.review_note
        delivery.review_score = review_request.review_score
        delivery.reviewed_at = datetime.utcnow()
        delivery.reviewer_id = company_user_id
        delivery.updated_at = datetime.utcnow()

        # 根据验收结果更新需求状态
        if review_request.review_status == "approved":
            content_request.status = RequestStatus.COMPLETED
        elif review_request.review_status == "rejected":
            content_request.status = RequestStatus.CANCELLED  # 拒绝后标记为已取消
        elif review_request.review_status == "revision_required":
            content_request.status = RequestStatus.IN_PROGRESS
            # 创建修改要求记录
            revision = ContentRevision(
                delivery_id=delivery.id,
                revision_requirements=review_request.revision_requirements,
                revision_deadline=review_request.revision_deadline
            )
            self.db.add(revision)
            delivery.revision_count += 1
        
        content_request.updated_at = datetime.utcnow()
        
        await self.db.commit()
        
        return ContentReviewResponse(
            request_id=request_id,
            delivery_id=str(delivery.id),
            review_status=delivery.review_status.value if hasattr(delivery.review_status, 'value') else str(delivery.review_status),
            review_note=delivery.review_note,
            review_score=delivery.review_score,
            reviewed_at=delivery.reviewed_at,
            request_status=content_request.status.value if hasattr(content_request.status, 'value') else str(content_request.status)
        )

    async def _build_content_request_response(self, content_request: ContentRequest) -> ContentRequestResponse:
        """构建内容需求响应数据"""
        # 获取企业信息
        company_result = await self.db.execute(
            select(Company).where(Company.id == content_request.company_id)
        )
        company = company_result.scalar_one_or_none()

        # 获取渠道商信息
        provider_result = await self.db.execute(
            select(ContentProvider).where(ContentProvider.id == content_request.provider_id)
        )
        provider = provider_result.scalar_one_or_none()

        return ContentRequestResponse(
            id=str(content_request.id),
            group_id=str(content_request.group_id),
            company_id=str(content_request.company_id),
            provider_id=str(content_request.provider_id),
            service_id=str(content_request.service_id),
            request_type=content_request.request_type.value if hasattr(content_request.request_type, 'value') else str(content_request.request_type),
            request_title=content_request.request_title,
            request_description=content_request.request_description,
            provided_content_title=content_request.provided_content_title,
            provided_content_text=content_request.provided_content_text,
            provided_content_files=self._clean_file_data(content_request.provided_content_files),
            creation_requirements=content_request.creation_requirements,
            tags=content_request.tags,
            deadline=content_request.deadline,
            status=content_request.status.value if hasattr(content_request.status, 'value') else str(content_request.status),
            estimated_delivery_days=content_request.estimated_delivery_days,
            accept_message=content_request.accept_message,
            fixed_price=content_request.fixed_price,
            created_at=content_request.created_at,
            updated_at=content_request.updated_at,
            accepted_at=content_request.accepted_at,
            company_info={
                "company_name": company.company_name if company else "未知企业",
                "company_code": company.company_code if company else None,
                "industry": company.industry if company else None
            } if company else None,
            provider_info={
                "provider_name": provider.provider_name if provider else "未知渠道商",
                "provider_type": provider.provider_type if provider else None,
                "contact_phone": provider.contact_phone if provider else None,
                "contact_email": provider.contact_email if provider else None,
                "contact_address": provider.contact_address if provider else None,
                "business_description": provider.business_description if provider else None,
                "verification_status": provider.verification_status if provider else None,
                "service_rating": provider.service_rating if provider else None,
                "completed_orders": provider.completed_orders if provider else None
            } if provider else None
        )

    async def _get_content_request(self, request_id: str) -> ContentRequest:
        """获取内容需求"""
        try:
            # 验证UUID格式
            uuid.UUID(request_id)
        except ValueError:
            raise NotFoundError("内容需求不存在")

        result = await self.db.execute(
            select(ContentRequest).where(ContentRequest.id == request_id)
        )
        content_request = result.scalar_one_or_none()
        if not content_request:
            raise NotFoundError("内容需求不存在")
        return content_request

    async def _validate_provider_permission(self, provider_id: str, user_id: str):
        """验证渠道商权限"""
        # TODO: 渠道权限验证需要重新设计
        # 暂时注释掉权限验证
        pass

    async def _validate_company_permission(self, company_id: str, user_id: str):
        """验证企业用户权限"""
        company_result = await self.db.execute(
            select(Company).where(
                and_(Company.id == company_id, Company.user_id == user_id)
            )
        )
        company = company_result.scalar_one_or_none()
        if not company:
            raise PermissionError("无权限操作该企业")

    async def _get_content_statistics(self, conditions: List) -> Dict[str, Any]:
        """获取内容统计信息"""
        base_query = select(func.count(ContentRequest.id))
        if conditions:
            base_query = base_query.where(and_(*conditions))

        total_requests = await self.db.execute(base_query)

        # 各状态统计
        status_stats = {}
        for status in RequestStatus:
            status_conditions = conditions + [ContentRequest.status == status]
            status_query = select(func.count(ContentRequest.id)).where(and_(*status_conditions))
            status_result = await self.db.execute(status_query)
            status_stats[f"{status.value}_requests"] = status_result.scalar()

        return {
            "total_requests": total_requests.scalar(),
            **status_stats
        }
