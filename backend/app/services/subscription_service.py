from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func, desc, update
from app.models.order import (
    Subscription, SubscriptionPlan,
    QuotaUsage, SubscriptionChange
)

from app.models.company import Company
from app.schemas.subscription import *
from app.exceptions import ValidationError, NotFoundError, PermissionError
from typing import Optional, Dict, Any, List
from datetime import datetime, timedelta, timezone
from dateutil.relativedelta import relativedelta
from decimal import Decimal
from uuid import UUID
import uuid

import logging

logger = logging.getLogger(__name__)

class SubscriptionService:
    def __init__(self, db: AsyncSession):
        self.db = db
    
    # ============= Plan Management =============
    
    async def create_plan(self, request: SubscriptionPlanCreate) -> SubscriptionPlanResponse:
        """Create a new subscription plan"""
        # Check if plan code already exists
        existing = await self.db.execute(
            select(SubscriptionPlan).where(SubscriptionPlan.plan_code == request.plan_code)
        )
        if existing.scalar_one_or_none():
            raise ValidationError(f"Plan code {request.plan_code} already exists")
        
        plan = SubscriptionPlan(
            id=uuid.uuid4(),
            **request.model_dump()
        )
        
        self.db.add(plan)
        await self.db.commit()
        await self.db.refresh(plan)
        
        # Convert UUID to string for response
        plan_dict = {
            "id": str(plan.id),
            "plan_code": plan.plan_code,
            "plan_name": plan.plan_name,
            "plan_type": plan.plan_type,
            "target_user_type": plan.target_user_type,
            "monthly_price": plan.monthly_price,
            "quarterly_price": plan.quarterly_price,
            "semi_annual_price": plan.semi_annual_price,
            "yearly_price": plan.yearly_price,
            "max_content_requests": plan.max_content_requests,
            "max_monitoring_projects": plan.max_monitoring_projects,
            "max_api_calls": plan.max_api_calls,
            "max_team_members": plan.max_team_members,
            "max_service_orders": plan.max_service_orders,
            "max_channels": plan.max_channels,
            "commission_rate": plan.commission_rate,
            "features": plan.features,
            "is_active": plan.is_active,
            "display_order": plan.display_order,
            "created_at": plan.created_at,
            "updated_at": plan.updated_at
        }
        
        return SubscriptionPlanResponse(**plan_dict)
    
    def _plan_to_response(self, plan: SubscriptionPlan) -> SubscriptionPlanResponse:
        """Convert SubscriptionPlan model to response"""
        return SubscriptionPlanResponse(
            id=str(plan.id),
            plan_code=plan.plan_code,
            plan_name=plan.plan_name,
            plan_type=plan.plan_type,
            target_user_type=plan.target_user_type,
            monthly_price=plan.monthly_price,
            quarterly_price=plan.quarterly_price,
            semi_annual_price=plan.semi_annual_price,
            yearly_price=plan.yearly_price,
            max_content_requests=plan.max_content_requests,
            max_monitoring_projects=plan.max_monitoring_projects,
            max_api_calls=plan.max_api_calls,
            max_team_members=plan.max_team_members,
            max_service_orders=plan.max_service_orders,
            max_channels=plan.max_channels,
            commission_rate=plan.commission_rate,
            features=plan.features,
            is_active=plan.is_active,
            display_order=plan.display_order,
            created_at=plan.created_at,
            updated_at=plan.updated_at
        )
    
    async def get_plans(self, target_user_type: Optional[str] = None, active_only: bool = True) -> List[SubscriptionPlanResponse]:
        """Get available subscription plans"""
        query = select(SubscriptionPlan)
        
        conditions = []
        if active_only:
            conditions.append(SubscriptionPlan.is_active == True)
        if target_user_type:
            conditions.append(or_(
                SubscriptionPlan.target_user_type == target_user_type,
                SubscriptionPlan.target_user_type == "both"
            ))
        
        if conditions:
            query = query.where(and_(*conditions))
        
        query = query.order_by(SubscriptionPlan.display_order, SubscriptionPlan.monthly_price)
        
        result = await self.db.execute(query)
        plans = result.scalars().all()
        
        return [self._plan_to_response(plan) for plan in plans]

    async def get_plans_with_purchase_status(self, target_user_type: Optional[str] = None, active_only: bool = True, user_id: Optional[str] = None, billing_cycle: Optional[str] = None) -> List[SubscriptionPlanResponse]:
        """Get available subscription plans with purchase status for user - 只有最高级套餐显示已购买"""
        # Get all plans first
        plans_data = await self.get_plans(target_user_type, active_only)

        # If no user provided, return plans without purchase status
        if not user_id:
            return plans_data

        # Get user's current active subscription (highest-tier)
        current_subscription = await self.get_active_subscription(user_id)

        # Debug logging
        if current_subscription:
            print(f"DEBUG: User {user_id} current subscription - Plan: {current_subscription.plan_id}, Billing: {current_subscription.billing_cycle}")
        else:
            print(f"DEBUG: User {user_id} has no active subscription")

        # Convert to list of dictionaries for easier manipulation
        plans_with_status = []

        for plan in plans_data:
            plan_dict = plan.model_dump()

            # Only mark as purchased if this is the user's current highest-tier subscription
            is_current_plan = False
            current_subscription_id = None

            if current_subscription:
                # Check if this plan matches the user's current subscription
                plan_matches = (str(plan.id) == str(current_subscription.plan_id))

                # For is_purchased status, consider both plan_id and billing_cycle
                # This allows users to upgrade from same plan with different billing cycle
                if billing_cycle:
                    # If billing_cycle is specified, check both plan and billing cycle
                    billing_cycle_matches = (current_subscription.billing_cycle == billing_cycle)
                    is_current_plan = plan_matches and billing_cycle_matches
                else:
                    # If no billing_cycle filter, consider plan as current if plan_id matches
                    is_current_plan = plan_matches



                if plan_matches:  # Use plan_matches for subscription_id, not is_current_plan
                    current_subscription_id = current_subscription.id

            plan_dict['is_purchased'] = is_current_plan
            plan_dict['current_subscription_id'] = current_subscription_id

            plans_with_status.append(SubscriptionPlanResponse(**plan_dict))

        return plans_with_status

    async def create_subscription_from_order(
        self,
        order_id: UUID,  # 修复：添加入参
        user_id: UUID,
        plan_id: UUID,
        billing_cycle: str,
        start_date: datetime,
        end_date: datetime
    ) -> Subscription:
        """从订单创建订阅记录 - 应用最高级套餐优先规则"""
        # 暂时禁用 autoflush 避免在查询时触发 autoflush
        original_autoflush = self.db.autoflush
        self.db.autoflush = False
        
        try:
            print(f"DEBUG: 开始创建订阅，用户ID: {user_id}, 套餐ID: {plan_id}")

            # 获取新套餐信息
            new_plan = await self.get_plan_by_id(plan_id)
            print(f"DEBUG: 获取套餐信息成功，套餐名称: {new_plan.plan_name}")

            # 获取用户所有活跃订阅
            print(f"DEBUG: 查询用户现有活跃订阅")
            result = await self.db.execute(
                select(Subscription)
                .join(SubscriptionPlan, Subscription.plan_id == SubscriptionPlan.id)
                .where(
                    and_(
                        Subscription.user_id == user_id,
                        Subscription.status == 'active',
                        Subscription.end_date >= datetime.now(timezone.utc)
                    )
                )
            )
            existing_subscriptions = result.scalars().all()
            print(f"DEBUG: 找到 {len(existing_subscriptions)} 个现有活跃订阅")

            # 创建新订阅
            print(f"DEBUG: 创建新订阅记录")
            new_subscription = Subscription(
                user_id=user_id,
                plan_id=plan_id,
                status='active',
                start_date=start_date,
                end_date=end_date,
                billing_cycle=billing_cycle,
                created_at=datetime.now(timezone.utc),
                updated_at=datetime.now(timezone.utc)
            )

            self.db.add(new_subscription)
            print(f"DEBUG: 新订阅记录已添加到数据库会话")
            
            # 刷新以获取生成的ID
            await self.db.flush()
            print(f"DEBUG: 订阅记录已刷新，ID: {new_subscription.id}")

            # 检查是否需要取消现有订阅
            for existing in existing_subscriptions:
                existing_plan = await self.get_plan_by_id(existing.plan_id)
                # 如果新套餐价格更高或相等，取消旧订阅
                if new_plan.monthly_price >= existing_plan.monthly_price:
                    existing.status = 'cancelled'
                    existing.updated_at = datetime.now(timezone.utc)

            # 创建配额使用记录
            print(f"DEBUG: 开始创建配额使用记录")
            await self._create_quota_usage(new_subscription, new_plan)
            print(f"DEBUG: 配额使用记录创建完成")

            # 解决任何剩余的冲突（确保只有最高级套餐生效）
            print(f"DEBUG: 开始解决订阅冲突")
            await self.resolve_subscription_conflicts(str(user_id))
            print(f"DEBUG: 订阅冲突解决完成")
            
        except Exception as e:
            print(f"ERROR: 创建订阅过程中出错: {e}")
            raise
        finally:
            # 恢复 autoflush 设置
            self.db.autoflush = original_autoflush

        # 提交所有更改
        print(f"DEBUG: 最终提交所有更改")
        await self.db.commit()
        await self.db.refresh(new_subscription)
        print(f"DEBUG: 订阅创建流程完全完成，最终订阅ID: {new_subscription.id}")

        return new_subscription

    async def resolve_subscription_conflicts(self, user_id: str) -> Dict[str, Any]:
        """解决用户的订阅冲突 - 按角色分组，每个角色保留最高级套餐"""
        print(f"DEBUG resolve_subscription_conflicts: 开始处理用户 {user_id} 的订阅冲突")
        
        # 获取用户所有活跃订阅，包括套餐信息
        result = await self.db.execute(
            select(Subscription, SubscriptionPlan)
            .join(SubscriptionPlan, Subscription.plan_id == SubscriptionPlan.id)
            .where(
                and_(
                    Subscription.user_id == user_id,
                    Subscription.status == 'active',
                    Subscription.end_date >= datetime.now(timezone.utc)
                )
            )
            .order_by(
                SubscriptionPlan.target_user_type,      # 按角色分组
                SubscriptionPlan.monthly_price.desc(),  # 每个角色内按价格排序
                Subscription.end_date.desc(),
                Subscription.updated_at.desc()
            )
        )
        subscription_plan_pairs = result.all()
        
        print(f"DEBUG resolve_subscription_conflicts: 找到 {len(subscription_plan_pairs)} 个活跃订阅")
        for i, (sub, plan) in enumerate(subscription_plan_pairs):
            print(f"  {i+1}. {plan.plan_name} (角色: {plan.target_user_type}, 价格: {plan.monthly_price})")

        if len(subscription_plan_pairs) <= 1:
            print(f"DEBUG resolve_subscription_conflicts: 无需处理冲突")
            return {
                "conflicts_found": False,
                "active_subscriptions": len(subscription_plan_pairs),
                "cancelled_subscriptions": 0,
                "message": "No conflicts found"
            }

        # 按角色分组处理冲突
        role_groups = {}
        for subscription, plan in subscription_plan_pairs:
            role = plan.target_user_type
            if role not in role_groups:
                role_groups[role] = []
            role_groups[role].append((subscription, plan))

        print(f"DEBUG resolve_subscription_conflicts: 按角色分组结果:")
        for role, subs in role_groups.items():
            print(f"  角色 {role}: {len(subs)} 个订阅")

        total_cancelled = 0
        kept_subscriptions = []

        # 每个角色内部解决冲突，保留最高级的订阅
        for role, subscriptions in role_groups.items():
            print(f"DEBUG resolve_subscription_conflicts: 处理角色 {role}")
            print(f"  该角色有 {len(subscriptions)} 个订阅")
            if len(subscriptions) > 1:
                # 保留第一个（最高价格的），取消其他的
                kept_subscription = subscriptions[0][0]
                kept_subscriptions.append(kept_subscription)
                print(f"  保留: {subscriptions[0][1].plan_name}")
                
                for subscription, plan in subscriptions[1:]:
                    print(f"  取消: {plan.plan_name}")
                    subscription.status = 'cancelled'
                    subscription.updated_at = datetime.now(timezone.utc)
                    total_cancelled += 1
            else:
                # 只有一个订阅，直接保留
                print(f"  保留: {subscriptions[0][1].plan_name} (该角色只有1个订阅)")
                kept_subscriptions.append(subscriptions[0][0])

        await self.db.commit()

        return {
            "conflicts_found": total_cancelled > 0,
            "active_subscriptions": len(kept_subscriptions),
            "cancelled_subscriptions": total_cancelled,
            "roles_processed": list(role_groups.keys()),
            "message": f"按角色解决冲突: 保留 {len(kept_subscriptions)} 个订阅, 取消 {total_cancelled} 个订阅"
        }
    
    async def get_plan_by_id(self, plan_id: str) -> SubscriptionPlan:
        """Get plan by ID"""
        result = await self.db.execute(
            select(SubscriptionPlan).where(SubscriptionPlan.id == plan_id)
        )
        plan = result.scalar_one_or_none()
        if not plan:
            raise NotFoundError(f"Plan {plan_id} not found")
        return plan

    async def update_plan(self, plan_id: str, request: SubscriptionPlanUpdate) -> SubscriptionPlanResponse:
        """Update a subscription plan"""
        # Get existing plan
        plan = await self.get_plan_by_id(plan_id)

        # Update only provided fields
        update_data = request.model_dump(exclude_unset=True)
        if update_data:
            for field, value in update_data.items():
                setattr(plan, field, value)

            plan.updated_at = datetime.now(timezone.utc)

            await self.db.commit()
            await self.db.refresh(plan)

        return self._plan_to_response(plan)

    async def delete_plan(self, plan_id: str) -> Dict[str, Any]:
        """Delete a subscription plan (hard delete - permanently remove from database)"""
        plan = await self.get_plan_by_id(plan_id)

        # Check if plan is being used by any subscriptions (active or inactive)
        result = await self.db.execute(
            select(func.count(Subscription.id)).where(
                Subscription.plan_id == plan_id
            )
        )
        subscriptions_count = result.scalar()

        if subscriptions_count > 0:
            raise ValidationError(f"Cannot delete plan. {subscriptions_count} subscriptions are using this plan. Please handle these subscriptions first.")

        # Hard delete - permanently remove from database
        await self.db.delete(plan)
        await self.db.commit()

        return {
            "plan_id": plan_id,
            "status": "deleted",
            "message": "Plan has been permanently deleted from the database"
        }

    async def toggle_plan_status(self, plan_id: str, request: SubscriptionPlanStatusUpdate) -> Dict[str, Any]:
        """Toggle plan active status"""
        plan = await self.get_plan_by_id(plan_id)

        old_status = plan.is_active
        plan.is_active = request.is_active
        plan.updated_at = datetime.now(timezone.utc)

        await self.db.commit()

        status_text = "activated" if request.is_active else "deactivated"

        return {
            "plan_id": plan_id,
            "old_status": old_status,
            "new_status": request.is_active,
            "status": "updated",
            "message": f"Plan has been {status_text} successfully",
            "reason": request.reason
        }

    # ============= Subscription Management =============
    
    async def get_active_subscription(self, user_id: str) -> Optional[SubscriptionResponse]:
        """Get user's active subscription - 最高级套餐优先"""
        result = await self.db.execute(
            select(Subscription)
            .join(SubscriptionPlan, Subscription.plan_id == SubscriptionPlan.id)
            .where(
                and_(
                    Subscription.user_id == user_id,
                    Subscription.status == 'active',
                    Subscription.end_date >= datetime.now(timezone.utc).replace(tzinfo=None)
                )
            )
            .order_by(
                SubscriptionPlan.monthly_price.desc(),  # 最高价格优先
                Subscription.end_date.desc(),           # 最长有效期优先
                Subscription.updated_at.desc()          # 最新更新优先
            )
        )
        subscription = result.scalars().first()  # 使用 scalars().first() 获取 Subscription 对象

        if not subscription:
            return None

        return await self._build_subscription_response(subscription)
    
    async def get_active_subscription_by_role(self, user_id: str, role_context: Optional[str] = None) -> Optional[SubscriptionResponse]:
        """Get user's active subscription based on role context - 根据角色上下文获取对应订阅"""
        
        if not role_context:
            # 如果没有指定角色上下文，使用原来的逻辑（最高价格优先）
            return await self.get_active_subscription(user_id)
        
        if role_context == 'enterprise':
            # 企业用户：只查询企业套餐和通用套餐
            result = await self.db.execute(
                select(Subscription)
                .join(SubscriptionPlan, Subscription.plan_id == SubscriptionPlan.id)
                .where(
                    and_(
                        Subscription.user_id == user_id,
                        Subscription.status == 'active',
                        Subscription.end_date >= datetime.now(timezone.utc).replace(tzinfo=None),
                        SubscriptionPlan.target_user_type.in_(['enterprise', 'both'])
                    )
                )
                .order_by(
                    SubscriptionPlan.monthly_price.desc(),  # 最高价格优先
                    Subscription.end_date.desc(),           # 最长有效期优先
                    Subscription.updated_at.desc()          # 最新更新优先
                )
            )
        elif role_context == 'provider':
            # 渠道商：严格只查询渠道商套餐和通用套餐
            result = await self.db.execute(
                select(Subscription)
                .join(SubscriptionPlan, Subscription.plan_id == SubscriptionPlan.id)
                .where(
                    and_(
                        Subscription.user_id == user_id,
                        Subscription.status == 'active',
                        Subscription.end_date >= datetime.now(timezone.utc).replace(tzinfo=None),
                        SubscriptionPlan.target_user_type.in_(['provider', 'both'])
                    )
                )
                .order_by(
                    SubscriptionPlan.monthly_price.desc(),
                    Subscription.end_date.desc(),
                    Subscription.updated_at.desc()
                )
            )
        else:
            # 未知角色上下文，回退到默认逻辑
            return await self.get_active_subscription(user_id)
        subscription = result.scalars().first()

        if not subscription:
            return None

        return await self._build_subscription_response(subscription)
    
    async def get_subscription_detail(self, subscription_id: str, user_id: str) -> SubscriptionDetailResponse:
        """Get detailed subscription information"""
        subscription = await self._get_subscription(subscription_id)
        
        # Verify ownership
        if str(subscription.user_id) != user_id:
            raise PermissionError("No permission to access this subscription")
        
        # Get plan details
        plan = await self.get_plan_by_id(subscription.plan_id)
        
        # Get quota usage
        quota_details = await self._get_quota_usage_details(subscription_id)
        
        # Get billing history
        billing_history = await self._get_billing_history(subscription_id)
        
        # Get change history
        change_history = await self._get_change_history(subscription_id)
        
        # Check available actions
        can_upgrade, can_downgrade = await self._check_available_actions(subscription, plan)
        
        # Get available plans for upgrade/downgrade
        # 通过用户ID查询是否有公司信息来判断用户类型
        company_result = await self.db.execute(
            select(Company).where(Company.user_id == subscription.user_id)
        )
        company = company_result.scalar_one_or_none()
        user_type = "enterprise" if company else "provider"

        available_plans = await self.get_plans(
            target_user_type=user_type
        )
        
        base_response = await self._build_subscription_response(subscription)
        
        return SubscriptionDetailResponse(
            **base_response.model_dump(),
            plan=self._plan_to_response(plan),
            quota_details=quota_details,
            billing_history=billing_history,
            change_history=change_history,
            next_billing_date=subscription.end_date if subscription.auto_renewal else None,
            can_upgrade=can_upgrade,
            can_downgrade=can_downgrade,
            available_plans=available_plans
        )
    
    async def create_subscription(self, user_id: str, request: SubscriptionCreate) -> SubscriptionResponse:
        """Create a new subscription"""
        # Check if user already has active subscription
        existing = await self.get_active_subscription(user_id)
        if existing:
            raise ValidationError("User already has an active subscription")
        
        # Get plan details
        plan = await self.get_plan_by_id(request.plan_id)
        

        
        # Calculate subscription period
        start_date = datetime.now(timezone.utc)
        end_date = self._calculate_end_date(start_date, request.billing_cycle)
        
        # Create subscription
        subscription = Subscription(
            id=uuid.uuid4(),
            user_id=user_id,
            plan_id=plan.id,
            start_date=start_date,
            end_date=end_date,
            status='active',
            billing_cycle=request.billing_cycle.value,
            created_at=datetime.now(timezone.utc)
        )
        
        self.db.add(subscription)

        # Create initial quota usage record
        await self._create_quota_usage(subscription, plan)

        await self.db.commit()
        await self.db.refresh(subscription)
        
        return await self._build_subscription_response(subscription)
    
    async def upgrade_subscription(self, subscription_id: str, user_id: str, request: SubscriptionUpgrade) -> SubscriptionChangeResponse:
        """Upgrade subscription to a higher plan"""
        subscription = await self._get_subscription(subscription_id)

        # Verify ownership
        if str(subscription.user_id) != user_id:
            raise PermissionError("No permission to modify this subscription")

        # Get current and target plans
        current_plan = await self.get_plan_by_id(subscription.plan_id)
        target_plan = await self.get_plan_by_id(request.target_plan_id)

        # Debug logging
        print(f"DEBUG: Current plan ID: {current_plan.id}, name: {current_plan.plan_name}, monthly_price: {current_plan.monthly_price}")
        print(f"DEBUG: Target plan ID: {target_plan.id}, name: {target_plan.plan_name}, monthly_price: {target_plan.monthly_price}")
        print(f"DEBUG: Billing cycle: {request.billing_cycle}")

        # Verify it's an upgrade - improved logic to handle different billing cycles
        current_price = self._get_plan_price(current_plan, subscription.billing_cycle or "monthly")
        target_price = self._get_plan_price(target_plan, request.billing_cycle or subscription.billing_cycle or "monthly")

        print(f"DEBUG: Current price: {current_price}, Target price: {target_price}")

        # Allow same plan with different billing cycle (e.g., monthly to quarterly)
        # Also allow genuine upgrades to higher-tier plans
        is_same_plan_different_cycle = (current_plan.id == target_plan.id and
                                       subscription.billing_cycle != request.billing_cycle)
        is_higher_tier_plan = (current_plan.id != target_plan.id and target_price > current_price)

        if not (is_same_plan_different_cycle or is_higher_tier_plan):
            if current_plan.id == target_plan.id:
                raise ValidationError("Cannot upgrade to the same plan with the same billing cycle")
            else:
                raise ValidationError(f"Target plan is not an upgrade. Current: {current_price}, Target: {target_price}")
        
        # Calculate prorated amount if needed
        amount = Decimal('0.00')
        if request.prorate:
            # Ensure end_date has timezone info
            end_date_with_tz = subscription.end_date
            if hasattr(end_date_with_tz, 'tzinfo') and end_date_with_tz.tzinfo is None:
                end_date_with_tz = end_date_with_tz.replace(tzinfo=timezone.utc)
            elif not hasattr(end_date_with_tz, 'tzinfo'):
                # It's a date object, convert to datetime with timezone
                from datetime import time
                end_date_with_tz = datetime.combine(end_date_with_tz, time.max, tzinfo=timezone.utc)
            
            days_remaining = (end_date_with_tz - datetime.now(timezone.utc)).days
            months_remaining = days_remaining / 30
            amount = (target_plan.monthly_price - current_plan.monthly_price) * Decimal(str(months_remaining))
        
        # Create change record
        change = SubscriptionChange(
            id=uuid.uuid4(),
            subscription_id=subscription_id,
            user_id=user_id,
            change_type="upgrade",
            from_plan_id=current_plan.id,
            to_plan_id=target_plan.id,
            from_billing_cycle=subscription.billing_cycle or "monthly",
            to_billing_cycle=request.billing_cycle or subscription.billing_cycle or "monthly",
            amount=amount,
            effective_date=datetime.now(timezone.utc) if request.effective_immediately else subscription.end_date,
            reason="User requested upgrade",
            created_at=datetime.now(timezone.utc)
        )
        
        self.db.add(change)
        
        # Update subscription if effective immediately
        if request.effective_immediately:
            old_billing_cycle = subscription.billing_cycle
            subscription.plan_id = target_plan.id
            # 重要：更新付费周期
            if request.billing_cycle:
                subscription.billing_cycle = request.billing_cycle
                print(f"DEBUG: 升级订阅 - 付费周期从 {old_billing_cycle} 更新为 {request.billing_cycle}")
            subscription.updated_at = datetime.now(timezone.utc)

            # Update quota usage
            await self._update_quota_limits(subscription_id, target_plan)
        
        await self.db.commit()
        
        return SubscriptionChangeResponse(
            id=str(change.id),
            subscription_id=subscription_id,
            change_type="upgrade",
            from_plan={"id": str(current_plan.id), "name": current_plan.plan_name},
            to_plan={"id": str(target_plan.id), "name": target_plan.plan_name},
            amount=amount,
            effective_date=change.effective_date,
            status="success",
            message="Subscription upgraded successfully"
        )
    
    async def downgrade_subscription(self, subscription_id: str, user_id: str, request: SubscriptionUpgrade) -> SubscriptionChangeResponse:
        """Downgrade subscription to a lower plan"""
        subscription = await self._get_subscription(subscription_id)
        
        # Verify ownership
        if str(subscription.user_id) != user_id:
            raise PermissionError("No permission to modify this subscription")
        
        # Get current and target plans
        current_plan = await self.get_plan_by_id(subscription.plan_id)
        target_plan = await self.get_plan_by_id(request.target_plan_id)
        
        # Verify it's a downgrade
        if target_plan.monthly_price >= current_plan.monthly_price:
            raise ValidationError("Target plan is not a downgrade")
        
        # Downgrades always take effect at the end of current period
        effective_date = subscription.end_date
        
        # Calculate refund if applicable
        refund_amount = Decimal('0.00')
        if request.prorate:
            days_remaining = (subscription.end_date - datetime.now(timezone.utc)).days
            months_remaining = days_remaining / 30
            refund_amount = (current_plan.monthly_price - target_plan.monthly_price) * Decimal(str(months_remaining)) * Decimal('0.8')
        
        # Create change record
        change = SubscriptionChange(
            id=uuid.uuid4(),
            subscription_id=subscription_id,
            user_id=user_id,
            change_type="downgrade",
            from_plan_id=current_plan.id,
            to_plan_id=target_plan.id,
            from_billing_cycle=request.billing_cycle or "monthly",
            to_billing_cycle=request.billing_cycle or "monthly",
            amount=-refund_amount,  # Negative for refund
            effective_date=effective_date,
            reason="User requested downgrade",
            created_at=datetime.now(timezone.utc)
        )
        
        self.db.add(change)
        await self.db.commit()
        
        return SubscriptionChangeResponse(
            id=str(change.id),
            subscription_id=subscription_id,
            change_type="downgrade",
            from_plan={"id": str(current_plan.id), "name": current_plan.plan_name},
            to_plan={"id": str(target_plan.id), "name": target_plan.plan_name},
            amount=refund_amount,
            effective_date=effective_date,
            status="scheduled",
            message=f"Downgrade scheduled for {effective_date.date()}"
        )
    
    async def renew_subscription(self, subscription_id: str, user_id: str, request: SubscriptionRenewal) -> SubscriptionChangeResponse:
        """Renew subscription"""
        subscription = await self._get_subscription(subscription_id)
        
        # Verify ownership
        if str(subscription.user_id) != user_id:
            raise PermissionError("No permission to renew this subscription")
        
        # Get plan
        plan = await self.get_plan_by_id(subscription.plan_id)
        
        # Calculate renewal price
        price = self._calculate_plan_price(plan, request.billing_cycle)
        if request.apply_discount:
            price = self._apply_renewal_discount(price, request.billing_cycle)
        
        # Calculate new end date
        new_end_date = self._calculate_end_date(subscription.end_date, request.billing_cycle)
        
        # Create change record
        change = SubscriptionChange(
            id=uuid.uuid4(),
            subscription_id=subscription_id,
            user_id=user_id,
            change_type="renewal",
            from_plan_id=plan.id,
            to_plan_id=plan.id,
            from_billing_cycle=request.billing_cycle,
            to_billing_cycle=request.billing_cycle,
            amount=price,
            effective_date=subscription.end_date,
            reason="User renewed subscription",
            created_at=datetime.now(timezone.utc)
        )
        
        self.db.add(change)
        
        # Update subscription
        subscription.end_date = new_end_date
        subscription.updated_at = datetime.now(timezone.utc)
        
        await self.db.commit()
        
        return SubscriptionChangeResponse(
            id=str(change.id),
            subscription_id=subscription_id,
            change_type="renewal",
            from_plan={"id": str(plan.id), "name": plan.plan_name},
            to_plan={"id": str(plan.id), "name": plan.plan_name},
            amount=price,
            effective_date=change.effective_date,
            status="success",
            message=f"Subscription renewed until {new_end_date.date()}"
        )
    
    async def cancel_subscription(self, subscription_id: str, user_id: str, request: SubscriptionCancel) -> SubscriptionChangeResponse:
        """Cancel subscription"""
        subscription = await self._get_subscription(subscription_id)
        
        # Verify ownership
        if str(subscription.user_id) != user_id:
            raise PermissionError("No permission to cancel this subscription")
        
        # Determine cancellation date
        effective_date = datetime.now(timezone.utc) if request.cancel_immediately else subscription.end_date
        
        # Create change record
        change = SubscriptionChange(
            id=uuid.uuid4(),
            subscription_id=subscription_id,
            user_id=user_id,
            change_type="cancellation",
            from_plan_id=subscription.plan_id,
            to_plan_id=None,
            effective_date=effective_date,
            reason=request.reason,
            created_at=datetime.now(timezone.utc)
        )
        
        self.db.add(change)
        
        # Update subscription
        if request.cancel_immediately:
            subscription.status = 'cancelled'
            subscription.end_date = datetime.now(timezone.utc)
        # Note: auto_renewal would need to be stored in a separate table or settings
        subscription.updated_at = datetime.now(timezone.utc)
        
        await self.db.commit()
        
        return SubscriptionChangeResponse(
            id=str(change.id),
            subscription_id=subscription_id,
            change_type="cancellation",
            from_plan={"id": str(subscription.plan_id), "name": subscription.plan_name},
            to_plan=None,
            amount=None,
            effective_date=effective_date,
            status="success" if request.cancel_immediately else "scheduled",
            message=f"Subscription {'cancelled' if request.cancel_immediately else 'will be cancelled on ' + str(effective_date.date())}"
        )
    
    async def get_subscription_history(self, user_id: str) -> List[SubscriptionResponse]:
        """Get user's subscription history"""
        result = await self.db.execute(
            select(Subscription).where(
                Subscription.user_id == user_id
            ).order_by(desc(Subscription.created_at))
        )
        subscriptions = result.scalars().all()

        return [await self._build_subscription_response(sub) for sub in subscriptions]

    async def get_user_subscriptions_paginated(self, user_id: str, is_active: Optional[bool] = None,
                                             page: int = 1, size: int = 20) -> List[SubscriptionResponse]:
        """Get user's subscriptions with database-level pagination and filtering"""
        query = select(Subscription).where(Subscription.user_id == user_id)

        # Apply active filter if specified
        if is_active is not None:
            if is_active:
                query = query.where(Subscription.status == 'active')
            else:
                query = query.where(Subscription.status != 'active')

        # Apply pagination and ordering
        query = query.order_by(desc(Subscription.created_at)).offset((page - 1) * size).limit(size)

        result = await self.db.execute(query)
        subscriptions = result.scalars().all()

        return [await self._build_subscription_response(sub) for sub in subscriptions]

    async def get_all_subscriptions_for_admin(self, page: int = 1, size: int = 20, is_active: Optional[bool] = None) -> List[SubscriptionResponse]:
        """Get all subscriptions for admin management"""
        query = select(Subscription).order_by(desc(Subscription.created_at))

        # Apply active filter if specified
        if is_active is not None:
            query = query.where(Subscription.is_active == is_active)

        # Apply pagination
        query = query.offset((page - 1) * size).limit(size)

        result = await self.db.execute(query)
        subscriptions = result.scalars().all()

        # Build response list
        responses = []
        for sub in subscriptions:
            response = await self._build_subscription_response(sub)
            responses.append(response)

        return responses

    # ============= Helper Methods =============
    
    async def _get_subscription(self, subscription_id: str) -> Subscription:
        """Get subscription by ID"""
        result = await self.db.execute(
            select(Subscription).where(Subscription.id == subscription_id)
        )
        subscription = result.scalar_one_or_none()
        if not subscription:
            raise NotFoundError(f"Subscription {subscription_id} not found")
        return subscription
    
    async def _build_subscription_response(self, subscription: Subscription) -> SubscriptionResponse:
        """Build subscription response"""
        # Get plan information
        plan_result = await self.db.execute(
            select(SubscriptionPlan).where(SubscriptionPlan.id == subscription.plan_id)
        )
        plan = plan_result.scalar_one_or_none()

        # Calculate days remaining
        days_remaining = 0
        if subscription.end_date:
            # Convert end_date to datetime if it's a date object
            if hasattr(subscription.end_date, 'date'):
                # It's already a datetime
                end_datetime = subscription.end_date
            else:
                # It's a date, convert to datetime with timezone
                from datetime import time
                end_datetime = datetime.combine(subscription.end_date, time.max, tzinfo=timezone.utc)

            # Ensure end_datetime has timezone info
            if end_datetime.tzinfo is None:
                end_datetime = end_datetime.replace(tzinfo=timezone.utc)
                
            delta = end_datetime - datetime.now(timezone.utc)
            days_remaining = max(0, delta.days)

        return SubscriptionResponse(
            id=str(subscription.id),
            user_id=str(subscription.user_id),
            company_id=None,  # TODO: Get from user relationship
            order_id=str(subscription.id),  # Use subscription ID as order ID for now
            plan_id=str(subscription.plan_id),
            plan_name=plan.plan_name if plan else "Unknown Plan",
            plan_type=plan.plan_type if plan else "unknown",
            content_quota=plan.max_content_requests if plan else 0,
            content_used=subscription.content_used,
            monitoring_quota=plan.max_monitoring_projects if plan else 0,
            monitoring_used=subscription.monitoring_used,
            ai_quota=plan.max_api_calls if plan else 0,
            ai_used=subscription.ai_used,
            start_date=subscription.start_date,
            end_date=subscription.end_date,
            is_active=subscription.is_active,
            days_remaining=days_remaining,
            billing_cycle=subscription.billing_cycle or "monthly",
            auto_renewal=False,  # 默认值，可以后续从配置中获取
            renewal_price=plan.monthly_price if plan else None,  # 使用套餐价格作为续费价格
            created_at=subscription.created_at,
            updated_at=subscription.updated_at
        )
    
    def _calculate_plan_price(self, plan: SubscriptionPlan, billing_cycle: BillingCycle) -> Decimal:
        """Calculate plan price based on billing cycle"""
        if billing_cycle == BillingCycle.MONTHLY:
            return plan.monthly_price
        elif billing_cycle == BillingCycle.QUARTERLY:
            return plan.quarterly_price or (plan.monthly_price * 3)
        elif billing_cycle == BillingCycle.SEMI_ANNUAL:
            return plan.semi_annual_price or (plan.monthly_price * 6)
        elif billing_cycle == BillingCycle.YEARLY:
            return plan.yearly_price or (plan.monthly_price * 12)


    def _get_plan_price(self, plan: SubscriptionPlan, billing_cycle: str) -> Decimal:
        """Get plan price for comparison purposes (normalized to monthly equivalent)"""
        # Convert string to BillingCycle enum if needed
        if isinstance(billing_cycle, str):
            billing_cycle_map = {
                "monthly": BillingCycle.MONTHLY,
                "quarterly": BillingCycle.QUARTERLY,
                "semi_annual": BillingCycle.SEMI_ANNUAL,
                "yearly": BillingCycle.YEARLY
            }
            billing_cycle = billing_cycle_map.get(billing_cycle, BillingCycle.MONTHLY)

        total_price = self._calculate_plan_price(plan, billing_cycle)

        # Normalize to monthly equivalent for comparison
        if billing_cycle == BillingCycle.MONTHLY:
            return total_price
        elif billing_cycle == BillingCycle.QUARTERLY:
            return total_price / 3
        elif billing_cycle == BillingCycle.SEMI_ANNUAL:
            return total_price / 6
        elif billing_cycle == BillingCycle.YEARLY:
            return total_price / 12

    
    def _calculate_end_date(self, start_date: datetime, billing_cycle: BillingCycle) -> datetime:
        """Calculate subscription end date based on billing cycle"""
        if billing_cycle == BillingCycle.MONTHLY:
            months = 1
        elif billing_cycle == BillingCycle.QUARTERLY:
            months = 3
        elif billing_cycle == BillingCycle.SEMI_ANNUAL:
            months = 6
        elif billing_cycle == BillingCycle.YEARLY:
            months = 12
        
        return start_date + relativedelta(months=months)
    
    def _apply_renewal_discount(self, price: Decimal, billing_cycle: BillingCycle) -> Decimal:
        """Apply renewal discount based on billing cycle"""
        if billing_cycle == BillingCycle.YEARLY:
            return price * Decimal('0.85')  # 15% discount
        elif billing_cycle == BillingCycle.SEMI_ANNUAL:
            return price * Decimal('0.95')  # 5% discount
        return price
    
    async def _create_quota_usage(self, subscription: Subscription, plan: SubscriptionPlan):
        """Create initial quota usage record"""
        usage_month = datetime.now(timezone.utc).strftime("%Y-%m")

        # 通过用户ID查询是否有公司信息来判断用户类型
        from app.models.company import Company
        company_result = await self.db.execute(
            select(Company).where(Company.user_id == subscription.user_id)
        )
        company = company_result.scalar_one_or_none()
        user_type = "enterprise" if company else "provider"

        quota_usage = QuotaUsage(
            id=uuid.uuid4(),
            user_id=subscription.user_id,
            subscription_id=subscription.id,
            usage_month=usage_month,
            user_type=user_type,
            max_content_requests=plan.max_content_requests,
            max_monitoring_projects=plan.max_monitoring_projects,
            max_api_calls=plan.max_api_calls,
            max_team_members=plan.max_team_members,
            max_service_orders=plan.max_service_orders,
            max_channels=plan.max_channels,
            used_content_requests=0,
            used_monitoring_projects=0,
            used_api_calls=0,
            used_team_members=1,
            used_service_orders=0,
            used_channels=0
        )

        self.db.add(quota_usage)
    
    async def _update_quota_limits(self, subscription_id: str, plan: SubscriptionPlan):
        """Update quota limits for current month"""
        usage_month = datetime.now(timezone.utc).strftime("%Y-%m")

        result = await self.db.execute(
            update(QuotaUsage).where(
                and_(
                    QuotaUsage.subscription_id == subscription_id,
                    QuotaUsage.usage_month == usage_month
                )
            ).values(
                max_content_requests=plan.max_content_requests,
                max_monitoring_projects=plan.max_monitoring_projects,
                max_api_calls=plan.max_api_calls,
                max_team_members=plan.max_team_members,
                max_service_orders=plan.max_service_orders,
                max_channels=plan.max_channels,
                updated_at=datetime.now(timezone.utc)
            )
        )

        # 立即刷新配额更新到数据库
        await self.db.flush()
    
    async def _get_quota_usage_details(self, subscription_id: str) -> List[QuotaUsageDetail]:
        """Get detailed quota usage information"""
        usage_month = datetime.now(timezone.utc).strftime("%Y-%m")
        
        result = await self.db.execute(
            select(QuotaUsage).where(
                and_(
                    QuotaUsage.subscription_id == subscription_id,
                    QuotaUsage.usage_month == usage_month
                )
            )
        )
        usage = result.scalar_one_or_none()
        
        if not usage:
            return []
        
        details = []
        
        # Enterprise quotas
        if usage.user_type == "enterprise":
            details.extend([
                QuotaUsageDetail(
                    quota_type="content",
                    max_value=usage.max_content_requests,
                    used_value=usage.used_content_requests,
                    remaining=usage.max_content_requests - usage.used_content_requests,
                    percentage_used=(usage.used_content_requests / usage.max_content_requests * 100) if usage.max_content_requests > 0 else 0,
                    reset_date=self._get_next_month_start()
                ),
                QuotaUsageDetail(
                    quota_type="monitoring",
                    max_value=usage.max_monitoring_projects,
                    used_value=usage.used_monitoring_projects,
                    remaining=usage.max_monitoring_projects - usage.used_monitoring_projects,
                    percentage_used=(usage.used_monitoring_projects / usage.max_monitoring_projects * 100) if usage.max_monitoring_projects > 0 else 0,
                    reset_date=self._get_next_month_start()
                ),
                QuotaUsageDetail(
                    quota_type="api",
                    max_value=usage.max_api_calls,
                    used_value=usage.used_api_calls,
                    remaining=usage.max_api_calls - usage.used_api_calls,
                    percentage_used=(usage.used_api_calls / usage.max_api_calls * 100) if usage.max_api_calls > 0 else 0,
                    reset_date=self._get_next_month_start()
                ),
                QuotaUsageDetail(
                    quota_type="team",
                    max_value=usage.max_team_members,
                    used_value=usage.used_team_members,
                    remaining=usage.max_team_members - usage.used_team_members,
                    percentage_used=(usage.used_team_members / usage.max_team_members * 100) if usage.max_team_members > 0 else 0,
                    reset_date=None
                )
            ])
        else:  # Provider quotas
            details.extend([
                QuotaUsageDetail(
                    quota_type="service",
                    max_value=usage.max_service_orders,
                    used_value=usage.used_service_orders,
                    remaining=usage.max_service_orders - usage.used_service_orders,
                    percentage_used=(usage.used_service_orders / usage.max_service_orders * 100) if usage.max_service_orders > 0 else 0,
                    reset_date=self._get_next_month_start()
                ),
                QuotaUsageDetail(
                    quota_type="channel",
                    max_value=usage.max_channels,
                    used_value=usage.used_channels,
                    remaining=usage.max_channels - usage.used_channels,
                    percentage_used=(usage.used_channels / usage.max_channels * 100) if usage.max_channels > 0 else 0,
                    reset_date=None
                )
            ])
        
        return details
    
    async def _get_billing_history(self) -> List[Dict[str, Any]]:
        """Get billing history for subscription"""
        # TODO: Implement actual billing history from payments table
        return []
    
    async def _get_change_history(self, subscription_id: str) -> List[Dict[str, Any]]:
        """Get subscription change history"""
        result = await self.db.execute(
            select(SubscriptionChange).where(
                SubscriptionChange.subscription_id == subscription_id
            ).order_by(desc(SubscriptionChange.created_at))
        )
        changes = result.scalars().all()
        
        history = []
        for change in changes:
            history.append({
                "id": str(change.id),
                "type": change.change_type,
                "amount": float(change.amount) if change.amount else None,
                "effective_date": change.effective_date,
                "reason": change.reason,
                "created_at": change.created_at
            })
        
        return history
    
    async def _check_available_actions(self, subscription: Subscription, plan: SubscriptionPlan) -> tuple[bool, bool]:
        """Check if upgrade/downgrade is available"""
        # Get all active plans
        all_plans = await self.get_plans(active_only=True)
        
        can_upgrade = any(p.monthly_price > plan.monthly_price for p in all_plans)
        can_downgrade = any(p.monthly_price < plan.monthly_price for p in all_plans)
        
        return can_upgrade, can_downgrade
    
    def _get_next_month_start(self) -> datetime:
        """Get the start of next month"""
        today = datetime.now(timezone.utc)
        if today.month == 12:
            return datetime(today.year + 1, 1, 1)
        else:
            return datetime(today.year, today.month + 1, 1)

    async def admin_update_subscription(self, subscription_id: str, request: AdminSubscriptionUpdate) -> SubscriptionResponse:
        """管理员更新订阅信息 - 统一的订阅编辑方法"""
        # 获取订阅
        subscription = await self._get_subscription(subscription_id)

        # 记录修改前的状态（用于日志）
        original_status = subscription.status

        # 更新字段
        if request.status is not None:
            subscription.status = request.status

        if request.start_date is not None:
            subscription.start_date = request.start_date

        if request.end_date is not None:
            subscription.end_date = request.end_date

        if request.plan_id is not None:
            # 验证新套餐是否存在
            new_plan = await self.get_plan_by_id(request.plan_id)
            subscription.plan_id = request.plan_id

        # 更新配额使用量
        if request.content_used is not None:
            subscription.used_content_requests = request.content_used

        if request.monitoring_used is not None:
            subscription.used_monitoring_projects = request.monitoring_used

        if request.ai_used is not None:
            subscription.used_api_calls = request.ai_used

        # 更新时间戳
        subscription.updated_at = datetime.now(timezone.utc)

        # 提交更改
        await self.db.commit()
        await self.db.refresh(subscription)

        # 记录管理员操作日志（可选）
        if request.reason:
            # 这里可以添加操作日志记录
            pass

        # 返回更新后的订阅信息
        return await self._build_subscription_response(subscription)

    # ============= 定时任务相关方法 =============

    async def get_auto_renewal_subscriptions(self) -> List[Subscription]:
        """获取需要自动续费的订阅（明天到期且启用自动续费）"""
        tomorrow = datetime.now(timezone.utc) + timedelta(days=1)
        tomorrow_start = tomorrow.replace(hour=0, minute=0, second=0, microsecond=0)
        tomorrow_end = tomorrow.replace(hour=23, minute=59, second=59, microsecond=999999)

        query = select(Subscription).where(
            and_(
                Subscription.status == 'active',
                Subscription.auto_renewal == True,
                Subscription.end_date >= tomorrow_start,
                Subscription.end_date <= tomorrow_end
            )
        )

        result = await self.db.execute(query)
        return result.scalars().all()

    async def get_expiring_subscriptions(self, days: int = 3) -> List[Subscription]:
        """获取即将到期的订阅"""
        now = datetime.now(timezone.utc)
        expiry_threshold = now + timedelta(days=days)

        query = select(Subscription).where(
            and_(
                Subscription.status == 'active',
                Subscription.end_date <= expiry_threshold,
                Subscription.end_date > now
            )
        )

        result = await self.db.execute(query)
        return result.scalars().all()

    async def get_renewal_reminder_subscriptions(self, days: int = 7) -> List[Subscription]:
        """获取需要续费提醒的订阅（未启用自动续费）"""
        now = datetime.now(timezone.utc)
        reminder_threshold = now + timedelta(days=days)

        query = select(Subscription).where(
            and_(
                Subscription.status == 'active',
                Subscription.auto_renewal == False,  # 只给未启用自动续费的发送提醒
                Subscription.end_date <= reminder_threshold,
                Subscription.end_date > now
            )
        )

        result = await self.db.execute(query)
        return result.scalars().all()

    async def process_expired_subscriptions(self) -> int:
        """处理已到期的订阅"""
        now = datetime.now(timezone.utc)

        # 获取已到期但状态仍为active的订阅
        result = await self.db.execute(
            select(Subscription).where(
                and_(
                    Subscription.status == 'active',
                    Subscription.end_date <= now
                )
            )
        )
        expired_subscriptions = result.scalars().all()

        # 更新状态为expired
        for subscription in expired_subscriptions:
            subscription.status = 'expired'
            subscription.updated_at = now

        await self.db.commit()
        return len(expired_subscriptions)

    async def process_auto_renewal(self, subscription_id: str) -> Dict[str, Any]:
        """处理自动续费"""
        try:
            subscription = await self._get_subscription(subscription_id)

            if not subscription.auto_renewal:
                return {"success": False, "error": "Auto renewal not enabled"}

            # 获取套餐信息
            plan = await self.get_plan_by_id(subscription.plan_id)

            # 计算续费价格
            billing_cycle = BillingCycle(subscription.billing_cycle or "monthly")
            price = self._calculate_plan_price(plan, billing_cycle)

            # 获取用户的默认支付方式
            user_payment_method = await self._get_user_default_payment_method(subscription.user_id)
            if not user_payment_method:
                return {"success": False, "error": "No default payment method"}

            # 处理自动支付
            payment_result = await self._process_auto_payment(subscription, price, user_payment_method)

            if payment_result.get("success"):
                # 延长订阅期限
                new_end_date = self._calculate_end_date(subscription.end_date, billing_cycle)
                subscription.end_date = new_end_date
                subscription.updated_at = datetime.now(timezone.utc)

                # 创建续费记录
                change = SubscriptionChange(
                    id=uuid.uuid4(),
                    subscription_id=subscription_id,
                    user_id=subscription.user_id,
                    change_type="renewal",
                    from_plan_id=plan.id,
                    to_plan_id=plan.id,
                    from_billing_cycle=billing_cycle.value,
                    to_billing_cycle=billing_cycle.value,
                    amount=price,
                    effective_date=datetime.now(timezone.utc),
                    reason="Auto renewal",
                    created_at=datetime.now(timezone.utc)
                )

                self.db.add(change)
                await self.db.commit()

                return {
                    "success": True,
                    "new_end_date": new_end_date,
                    "payment_amount": price
                }
            else:
                # 自动续费失败，禁用自动续费
                subscription.auto_renewal = False
                subscription.updated_at = datetime.now(timezone.utc)
                await self.db.commit()

                return {
                    "success": False,
                    "error": f"Payment failed: {payment_result.get('error')}",
                    "auto_renewal_disabled": True
                }

        except Exception as e:
            logger.error(f"自动续费处理失败: {e}")
            return {"success": False, "error": str(e)}

    async def record_renewal_failure(self, subscription_id: str, error_message: str):
        """记录续费失败信息"""
        # 这里可以记录到专门的失败日志表
        logger.error(f"订阅 {subscription_id} 自动续费失败: {error_message}")

    async def send_renewal_failure_notification(self, subscription: Subscription, error_message: str):
        """发送续费失败通知"""
        try:
            from app.services.notification_service import NotificationService
            notification_service = NotificationService(self.db)
            await notification_service.send_auto_renewal_failed_notification(subscription, error_message)
        except Exception as e:
            logger.error(f"发送续费失败通知失败: {e}")

    async def _get_user_default_payment_method(self, user_id: str) -> Optional[Dict[str, Any]]:
        """获取用户默认支付方式"""
        # 简化实现，返回余额支付
        return {"method": "balance", "account": "default"}

    async def _process_auto_payment(self, subscription: Subscription, amount: Decimal, payment_method: Dict[str, Any]) -> Dict[str, Any]:
        """处理自动支付"""
        try:
            if payment_method["method"] == "balance":
                # 模拟余额支付
                balance_sufficient = await self._check_user_balance(subscription.user_id, amount)
                if balance_sufficient:
                    await self._deduct_user_balance(subscription.user_id, amount)
                    return {"success": True, "payment_method": "balance"}
                else:
                    return {"success": False, "error": "Insufficient balance"}
            else:
                # 其他支付方式
                return {"success": False, "error": "Unsupported payment method"}

        except Exception as e:
            logger.error(f"自动支付处理失败: {e}")
            return {"success": False, "error": str(e)}

    async def _check_user_balance(self, user_id: str, amount: Decimal) -> bool:
        """检查用户余额是否充足"""
        # 简化实现，假设余额充足
        return True

    async def _deduct_user_balance(self, user_id: str, amount: Decimal):
        """扣除用户余额"""
        # 简化实现
        pass