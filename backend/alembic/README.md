# Alembic 数据库迁移

这个目录包含了项目的数据库迁移脚本。

## 目录结构

```
alembic/
├── env.py              # Alembic 环境配置
├── script.py.mako      # 迁移脚本模板
├── versions/           # 迁移版本文件目录
│   └── 001_initial_database_schema.py  # 初始数据库架构
├── .gitignore          # Git 忽略文件
└── README.md           # 本文件
```

## 使用说明

### 创建新的迁移
```bash
cd backend
alembic revision --autogenerate -m "描述你的更改"
```

### 执行迁移
```bash
cd backend
alembic upgrade head
```

### 查看当前版本
```bash
cd backend
alembic current
```

### 查看迁移历史
```bash
cd backend
alembic history
```

## 注意事项

- 所有迁移文件都应该放在 `versions/` 目录下
- 迁移文件名格式：`{revision_id}_{description}.py`
- 当前基线版本：`001` (初始数据库架构)
- 数据库已经包含完整的表结构和数据
