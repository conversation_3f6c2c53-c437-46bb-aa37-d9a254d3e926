# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Logs
*.log
logs/

# Environment variables
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Testing
coverage/
.nyc_output
.coverage
.pytest_cache/

# Temporary files
temp/
tmp/
*.tmp
*.temp

# Database
*.db
*.sqlite
*.sqlite3

# Project specific temporary files
run_migration_direct.py
simple_migration.py
test_db_connection.py
test_migration.html
**/test_*.py
**/debug_*.py
**/check_*.py
**/run_*_migration.py
**/fix_*.py
