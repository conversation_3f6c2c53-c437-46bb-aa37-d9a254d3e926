{"name": "ai-seo-platform-frontend", "version": "1.0.0", "private": true, "description": "AI搜索引擎优化平台前端", "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@mui/icons-material": "^7.2.0", "@mui/lab": "^7.0.0-beta.16", "@mui/material": "^7.2.0", "@mui/x-date-pickers": "^8.10.0", "@testing-library/dom": "^10.4.1", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "@uiw/react-md-editor": "^4.0.8", "axios": "^1.11.0", "chart.js": "^4.5.0", "date-fns": "^4.1.0", "http-proxy-middleware": "^3.0.5", "is-root": "^3.0.0", "lucide-react": "^0.537.0", "mermaid": "^11.10.0", "notistack": "^3.0.2", "react": "^19.1.1", "react-chartjs-2": "^5.3.0", "react-dom": "^19.1.1", "react-markdown": "^10.1.0", "react-router-dom": "^6.30.1", "react-scripts": "5.0.1", "recharts": "^3.1.2", "remark-gfm": "^4.0.1", "web-vitals": "^2.1.4"}, "devDependencies": {"@playwright/test": "^1.55.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:workspace": "playwright test tests/playwright/enterprise-workspace.spec.js", "test:workspace:debug": "playwright test tests/playwright/enterprise-workspace.spec.js --debug", "test:install": "playwright install", "test:report": "playwright show-report"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"], "rules": {"no-unused-vars": "warn"}}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}