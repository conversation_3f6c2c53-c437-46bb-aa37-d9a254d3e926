import { lazy } from 'react';
import { Navigate } from 'react-router-dom';
// import { lazy } from '../utils/lazy';

// Lazy load components for code splitting
const Home = lazy(() => import('../pages/Home'));
const About = lazy(() => import('../pages/About'));
const Product = lazy(() => import('../pages/Product'));
const Pricing = lazy(() => import('../pages/Pricing'));

const HelpCenter = lazy(() => import('../pages/HelpCenter'));
const HelpDocumentDetail = lazy(() => import('../pages/HelpDocumentDetail'));
const Terms = lazy(() => import('../pages/Terms'));
const Privacy = lazy(() => import('../pages/Privacy'));

// Auth pages
const Login = lazy(() => import('../pages/auth/Login'));
const AdminLogin = lazy(() => import('../pages/auth/AdminLogin'));
const Register = lazy(() => import('../pages/auth/Register'));
const ForgotPassword = lazy(() => import('../pages/auth/ForgotPassword'));
const ResetPassword = lazy(() => import('../pages/auth/ResetPassword'));

// Dashboard pages
const Dashboard = lazy(() => import('../pages/dashboard/Dashboard'));

// Super Admin pages
const SuperAdminDashboard = lazy(() => import('../pages/super-admin/SuperAdminDashboard'));
const SuperAdminUserManagement = lazy(() => import('../pages/super-admin/SuperAdminUserManagement'));
const RoleManagement = lazy(() => import('../pages/super-admin/RoleManagement'));
const RolePermissionManagement = lazy(() => import('../pages/super-admin/RolePermissionManagement'));
const PermissionManagement = lazy(() => import('../pages/super-admin/PermissionManagement'));
const CommissionSettings = lazy(() => import('../pages/super-admin/CommissionSettings'));
const SystemConfig = lazy(() => import('../pages/super-admin/SystemConfig'));

// Admin pages
const AdminDashboard = lazy(() => import('../pages/admin/AdminDashboard'));
const UserManagement = lazy(() => import('../pages/admin/UserManagement'));
const ContentReview = lazy(() => import('../pages/admin/ContentReview'));
const RoleApproval = lazy(() => import('../pages/admin/RoleApproval'));
const ChannelManagement = lazy(() => import('../pages/admin/ChannelManagement'));
const ChannelCategoryManagement = lazy(() => import('../pages/admin/ChannelCategoryManagement'));
const ServiceManagement = lazy(() => import('../pages/admin/ServiceManagement'));
// ❌ ServiceManagementDemo已删除
const ServiceApproval = lazy(() => import('../pages/admin/ServiceApproval'));

const DocumentationManagement = lazy(() => import('../pages/admin/DocumentationManagement'));
const AnnouncementManagement = lazy(() => import('../pages/admin/AnnouncementManagement'));
const ProductManagement = lazy(() => import('../pages/admin/ProductManagement'));
const SubscriptionList = lazy(() => import('../pages/admin/SubscriptionList'));
const OrderCenter = lazy(() => import('../pages/admin/OrderCenter'));
const PaymentCenter = lazy(() => import('../pages/admin/PaymentCenter'));
// New admin pages
const PlatformIntegration = lazy(() => import('../pages/admin/PlatformIntegrationNew'));
const StatusMonitor = lazy(() => import('../pages/admin/StatusMonitor'));
const ContentServiceManagement = lazy(() => import('../pages/admin/ContentServiceManagement'));
const AnalyticsCenter = lazy(() => import('../pages/admin/AnalyticsCenter'));
const TaskManagement = lazy(() => import('../pages/admin/TaskManagement'));

// User pages
const UserDashboard = lazy(() => import('../pages/user/UserDashboard'));
const UserProfile = lazy(() => import('../pages/user/UserProfile'));
const RoleApplication = lazy(() => import('../pages/user/RoleApplication'));
const ApplicationRecords = lazy(() => import('../pages/user/ApplicationRecords'));
const UserSubscriptions = lazy(() => import('../pages/user/UserSubscriptions'));

// Role-specific dashboard pages
const EnterpriseDashboard = lazy(() => import('../pages/enterprise/EnterpriseDashboard'));
const ChannelDashboard = lazy(() => import('../pages/channel/ChannelDashboard'));
const AgentDashboard = lazy(() => import('../pages/agent/AgentDashboard'));

// Standalone control center pages (without layout)
const EnterpriseControlCenter = lazy(() => import('../pages/enterprise/EnterpriseControlCenter'));
const ChannelControlCenter = lazy(() => import('../pages/channel/ChannelControlCenter'));
const AgentControlCenter = lazy(() => import('../pages/agent/AgentControlCenter'));

// Error pages
const NotFound = lazy(() => import('../pages/error/NotFound'));
const Forbidden = lazy(() => import('../pages/error/Forbidden'));
const ServerError = lazy(() => import('../pages/error/ServerError'));

// ❌ Demo和Test组件已删除
// const AttachmentDemo = lazy(() => import('../components/AttachmentDemo'));
// const SubscriptionPlanTest = lazy(() => import('../components/SubscriptionPlanTest'));

// Route configuration
export const routes = [
  // Public routes
  {
    path: '/',
    component: Home,
    public: true,
  },
  {
    path: '/about',
    component: About,
    public: true,
  },
  {
    path: '/product',
    component: Product,
    public: true,
  },
  {
    path: '/pricing',
    component: Pricing,
    public: true,
  },

  {
    path: '/help',
    component: HelpCenter,
    public: true,
  },
  {
    path: '/help/documents/:documentId',
    component: HelpDocumentDetail,
    public: true,
  },
  {
    path: '/terms',
    component: Terms,
    public: true,
  },
  {
    path: '/privacy',
    component: Privacy,
    public: true,
  },

  // ❌ Demo和Test路由已删除
  // {
  //   path: '/demo/attachment',
  //   component: AttachmentDemo,
  //   public: true,
  // },
  // {
  //   path: '/test/subscription-plans',
  //   component: SubscriptionPlanTest,
  //   public: true,
  // },

  // Auth routes
  {
    path: '/auth/login',
    component: Login,
    public: true,
  },
  {
    path: '/auth/admin-login',
    component: AdminLogin,
    public: true,
  },
  {
    path: '/auth/register',
    component: Register,
    public: true,
  },
  {
    path: '/auth/forgot-password',
    component: ForgotPassword,
    public: true,
  },
  {
    path: '/auth/reset-password',
    component: ResetPassword,
    public: true,
  },

  // Dashboard redirect
  {
    path: '/dashboard',
    component: Dashboard,
    protected: true,
  },

  // Super Admin routes
  {
    path: '/super-admin/dashboard',
    component: SuperAdminDashboard,
    protected: true,
    roles: ['super_admin'],
  },
  {
    path: '/super-admin/users',
    component: SuperAdminUserManagement,
    protected: true,
    roles: ['super_admin'],
  },
  {
    path: '/super-admin/roles',
    component: RoleManagement,
    protected: true,
    roles: ['super_admin'],
  },
  {
    path: '/super-admin/role-permissions',
    component: RolePermissionManagement,
    protected: true,
    roles: ['super_admin'],
  },
  {
    path: '/super-admin/permissions',
    component: PermissionManagement,
    protected: true,
    roles: ['super_admin'],
  },
  {
    path: '/super-admin/commission',
    component: CommissionSettings,
    protected: true,
    roles: ['super_admin'],
  },
  {
    path: '/super-admin/system-config',
    component: SystemConfig,
    protected: true,
    roles: ['super_admin'],
  },

  // Admin routes
  {
    path: '/admin/dashboard',
    component: AdminDashboard,
    protected: true,
    roles: ['admin'],
  },
  {
    path: '/admin/users',
    component: UserManagement,
    protected: true,
    roles: ['admin'],
  },
  {
    path: '/admin/content',
    component: ContentReview,
    protected: true,
    roles: ['admin'],
  },
  {
    path: '/admin/roles',
    component: RoleApproval,
    protected: true,
    roles: ['admin'],
  },
  {
    path: '/admin/orders',
    component: OrderCenter,
    protected: true,
    roles: ['admin'],
  },
  {
    path: '/admin/products',
    component: ProductManagement,
    protected: true,
    roles: ['admin'],
  },
  {
    path: '/admin/subscriptions',
    component: SubscriptionList,
    protected: true,
    roles: ['admin'],
  },
  {
    path: '/admin/payments',
    component: PaymentCenter,
    protected: true,
    roles: ['admin'],
  },
  {
    path: '/admin/channels',
    component: ChannelManagement,
    protected: true,
    roles: ['admin'],
  },
  {
    path: '/admin/channel-categories',
    component: ChannelCategoryManagement,
    protected: true,
    roles: ['admin'],
  },
  {
    path: '/admin/services',
    component: ServiceManagement,
    protected: true,
    roles: ['admin'],
  },
  // ❌ ServiceManagementDemo路由已删除
  // {
  //   path: '/admin/service-demo',
  //   component: ServiceManagementDemo,
  //   protected: true,
  //   roles: ['admin'],
  // },
  {
    path: '/admin/service-approval',
    component: ServiceApproval,
    protected: true,
    roles: ['admin'],
  },
  {
    path: '/admin/documentation',
    component: DocumentationManagement,
    protected: true,
    roles: ['admin'],
  },
  {
    path: '/admin/announcements',
    component: AnnouncementManagement,
    protected: true,
    roles: ['admin'],
  },
  {
    path: '/admin/platform-integration',
    component: PlatformIntegration,
    protected: true,
    roles: ['admin'],
  },
  {
    path: '/admin/status-monitor',
    component: StatusMonitor,
    protected: true,
    roles: ['admin'],
  },
  {
    path: '/admin/content-service',
    component: ContentServiceManagement,
    protected: true,
    roles: ['admin'],
  },
  {
    path: '/admin/analytics',
    component: AnalyticsCenter,
    protected: true,
    roles: ['admin'],
  },
  {
    path: '/admin/tasks',
    component: TaskManagement,
    protected: true,
    roles: ['admin'],
  },

  // User routes
  {
    path: '/user/dashboard',
    component: UserDashboard,
    protected: true,
    roles: ['regular_user'],
  },
  {
    path: '/user/profile',
    component: UserProfile,
    protected: true,
    roles: ['regular_user'],
  },
  {
    path: '/user/role-application',
    component: RoleApplication,
    protected: true,
    roles: ['regular_user', 'enterprise_user', 'channel_user', 'agent_user'],
  },
  {
    path: '/user/application-records',
    component: ApplicationRecords,
    protected: true,
    roles: ['regular_user', 'enterprise_user', 'channel_user', 'agent_user'],
  },
  {
    path: '/user/subscriptions',
    component: UserSubscriptions,
    protected: true,
    roles: ['regular_user', 'enterprise_user', 'channel_user', 'agent_user'],
  },

  // Enterprise user routes
  {
    path: '/enterprise/dashboard',
    component: EnterpriseDashboard,
    protected: true,
    roles: ['enterprise_user'],
  },

  // Channel user routes
  {
    path: '/channel/dashboard',
    component: ChannelDashboard,
    protected: true,
    roles: ['channel_user'],
  },

  // Agent user routes
  {
    path: '/agent/dashboard',
    component: AgentDashboard,
    protected: true,
    roles: ['agent_user'],
  },

  // Standalone control center routes (without layout)
  {
    path: '/control-center/enterprise',
    component: EnterpriseControlCenter,
    protected: true,
    roles: ['enterprise_user'],
    standalone: true,
  },
  {
    path: '/control-center/channel',
    component: ChannelControlCenter,
    protected: true,
    roles: ['channel_user'],
    standalone: true,
  },
  {
    path: '/control-center/agent',
    component: AgentControlCenter,
    protected: true,
    roles: ['agent_user'],
    standalone: true,
  },

  // Error routes
  {
    path: '/error/403',
    component: Forbidden,
    public: true,
  },
  {
    path: '/error/500',
    component: ServerError,
    public: true,
  },
  {
    path: '/404',
    component: NotFound,
    public: true,
  },
  {
    path: '*',
    element: <Navigate to="/404" replace />,
    public: true,
  },
];

export default routes;
