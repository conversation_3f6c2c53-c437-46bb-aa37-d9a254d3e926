import React from 'react';
import { render, act, waitFor } from '@testing-library/react';
import { AuthProvider, useAuth } from '../AuthContext';
import { AppConfig } from '../../config/app-config';
import apiService from '../../services/api';

// Mock the API service
jest.mock('../../services/api', () => ({
  post: jest.fn(),
}));

// Test component to access auth context
const TestComponent = ({ onAuthChange }) => {
  const auth = useAuth();
  
  React.useEffect(() => {
    onAuthChange(auth);
  }, [auth, onAuthChange]);

  return <div>Test Component</div>;
};

describe('AuthContext Logout Functionality', () => {
  let authContext;
  const mockOnAuthChange = jest.fn((auth) => {
    authContext = auth;
  });

  beforeEach(() => {
    // Clear all localStorage and sessionStorage before each test
    localStorage.clear();
    sessionStorage.clear();
    
    // Reset API mock
    apiService.post.mockClear();
    
    // Reset the callback
    mockOnAuthChange.mockClear();
    authContext = null;
  });

  afterEach(() => {
    // Clean up after each test
    localStorage.clear();
    sessionStorage.clear();
  });

  const setupTestData = () => {
    // Set up test data in localStorage
    const testData = {
      [`${AppConfig.storage.prefix}${AppConfig.storage.tokenKey}`]: 'test-token',
      [`${AppConfig.storage.prefix}${AppConfig.storage.userKey}`]: JSON.stringify({
        id: '1',
        email: '<EMAIL>',
        name: 'Test User'
      }),
      [`${AppConfig.storage.prefix}refresh_token`]: 'test-refresh-token',
      [`${AppConfig.storage.prefix}${AppConfig.storage.settingsKey}`]: JSON.stringify({
        theme: 'dark',
        language: 'zh-CN'
      }),
      [`${AppConfig.storage.prefix}${AppConfig.storage.cacheKey}`]: JSON.stringify({
        someData: 'cached-data'
      }),
      [`${AppConfig.storage.prefix}theme`]: 'dark',
      'applicationRecords': JSON.stringify([{ id: 1, name: 'Test Record' }]),
      'applicationRecordsTime': Date.now().toString(),
      'someOtherData': 'should-not-be-removed'
    };

    // Set up test data in sessionStorage
    const sessionTestData = {
      [`${AppConfig.storage.prefix}sessionData`]: 'test-session-data',
      'otherSessionData': 'should-not-be-removed'
    };

    Object.entries(testData).forEach(([key, value]) => {
      localStorage.setItem(key, value);
    });

    Object.entries(sessionTestData).forEach(([key, value]) => {
      sessionStorage.setItem(key, value);
    });
  };

  test('should clear all user-related data from localStorage on logout', async () => {
    // Setup test data
    setupTestData();

    // Mock successful API call
    apiService.post.mockResolvedValue({ data: { success: true } });

    // Render the provider and test component
    render(
      <AuthProvider>
        <TestComponent onAuthChange={mockOnAuthChange} />
      </AuthProvider>
    );

    // Wait for the auth context to be available
    await waitFor(() => {
      expect(authContext).toBeTruthy();
    });

    // Verify initial data exists
    expect(localStorage.getItem(`${AppConfig.storage.prefix}${AppConfig.storage.tokenKey}`)).toBe('test-token');
    expect(localStorage.getItem('applicationRecords')).toBeTruthy();
    expect(localStorage.getItem('someOtherData')).toBe('should-not-be-removed');

    // Call logout
    await act(async () => {
      await authContext.logout();
    });

    // Verify API was called
    expect(apiService.post).toHaveBeenCalledWith('/auth/logout');

    // Verify user-related data is cleared
    expect(localStorage.getItem(`${AppConfig.storage.prefix}${AppConfig.storage.tokenKey}`)).toBeNull();
    expect(localStorage.getItem(`${AppConfig.storage.prefix}${AppConfig.storage.userKey}`)).toBeNull();
    expect(localStorage.getItem(`${AppConfig.storage.prefix}refresh_token`)).toBeNull();
    expect(localStorage.getItem(`${AppConfig.storage.prefix}${AppConfig.storage.cacheKey}`)).toBeNull();
    expect(localStorage.getItem('applicationRecords')).toBeNull();
    expect(localStorage.getItem('applicationRecordsTime')).toBeNull();

    // Verify theme is preserved (as per current implementation)
    expect(localStorage.getItem(`${AppConfig.storage.prefix}theme`)).toBe('dark');

    // Verify non-app data is preserved
    expect(localStorage.getItem('someOtherData')).toBe('should-not-be-removed');

    // Verify sessionStorage is cleared
    expect(sessionStorage.getItem(`${AppConfig.storage.prefix}sessionData`)).toBeNull();
    expect(sessionStorage.getItem('otherSessionData')).toBe('should-not-be-removed');

    // Verify auth state is updated
    expect(authContext.isAuthenticated).toBe(false);
    expect(authContext.user).toBeNull();
    expect(authContext.token).toBeNull();
  });

  test('should clear data even if API call fails', async () => {
    // Setup test data
    setupTestData();

    // Mock failed API call
    apiService.post.mockRejectedValue(new Error('Network error'));

    // Render the provider and test component
    render(
      <AuthProvider>
        <TestComponent onAuthChange={mockOnAuthChange} />
      </AuthProvider>
    );

    // Wait for the auth context to be available
    await waitFor(() => {
      expect(authContext).toBeTruthy();
    });

    // Call logout
    await act(async () => {
      await authContext.logout();
    });

    // Verify API was called
    expect(apiService.post).toHaveBeenCalledWith('/auth/logout');

    // Verify data is still cleared despite API failure
    expect(localStorage.getItem(`${AppConfig.storage.prefix}${AppConfig.storage.tokenKey}`)).toBeNull();
    expect(localStorage.getItem(`${AppConfig.storage.prefix}${AppConfig.storage.userKey}`)).toBeNull();
    expect(localStorage.getItem(`${AppConfig.storage.prefix}refresh_token`)).toBeNull();

    // Verify auth state is updated
    expect(authContext.isAuthenticated).toBe(false);
    expect(authContext.user).toBeNull();
    expect(authContext.token).toBeNull();
  });

  test('should handle localStorage errors gracefully', async () => {
    // Setup test data
    setupTestData();

    // Mock localStorage.removeItem to throw an error
    const originalRemoveItem = localStorage.removeItem;
    localStorage.removeItem = jest.fn(() => {
      throw new Error('Storage error');
    });

    // Mock successful API call
    apiService.post.mockResolvedValue({ data: { success: true } });

    // Render the provider and test component
    render(
      <AuthProvider>
        <TestComponent onAuthChange={mockOnAuthChange} />
      </AuthProvider>
    );

    // Wait for the auth context to be available
    await waitFor(() => {
      expect(authContext).toBeTruthy();
    });

    // Call logout - should not throw error
    await act(async () => {
      await expect(authContext.logout()).resolves.not.toThrow();
    });

    // Verify auth state is still updated
    expect(authContext.isAuthenticated).toBe(false);
    expect(authContext.user).toBeNull();
    expect(authContext.token).toBeNull();

    // Restore original localStorage
    localStorage.removeItem = originalRemoveItem;
  });
});
