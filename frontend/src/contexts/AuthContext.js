import { createContext, useContext, useReducer, useEffect } from 'react';
import { AppConfig } from '../config/app-config';
import apiService from '../services/api';

// Initial state
const initialState = {
  user: null,
  token: null,
  isAuthenticated: false,
  isLoading: true,
  error: null,
};

// Action types
const AUTH_ACTIONS = {
  LOGIN_START: 'LOGIN_START',
  LOGIN_SUCCESS: 'LOGIN_SUCCESS',
  LOGIN_FAILURE: 'LOGIN_FAILURE',
  LOGOUT: 'LOGOUT',
  REGISTER_START: 'REGISTER_START',
  REGISTER_SUCCESS: 'REGISTER_SUCCESS',
  REGISTER_FAILURE: 'REGISTER_FAILURE',
  LOAD_USER: 'LOAD_USER',
  CLEAR_ERROR: 'CLEAR_ERROR',
  SET_LOADING: 'SET_LOADING',
};

// Reducer
function authReducer(state, action) {
  switch (action.type) {
    case AUTH_ACTIONS.LOGIN_START:
    case AUTH_ACTIONS.REGISTER_START:
      return {
        ...state,
        isLoading: true,
        error: null,
      };

    case AUTH_ACTIONS.LOGIN_SUCCESS:
    case AUTH_ACTIONS.REGISTER_SUCCESS:
      return {
        ...state,
        user: action.payload.user,
        token: action.payload.token,
        isAuthenticated: true,
        isLoading: false,
        error: null,
      };

    case AUTH_ACTIONS.LOGIN_FAILURE:
    case AUTH_ACTIONS.REGISTER_FAILURE:
      return {
        ...state,
        user: null,
        token: null,
        isAuthenticated: false,
        isLoading: false,
        error: action.payload,
      };

    case AUTH_ACTIONS.LOGOUT:
      return {
        ...state,
        user: null,
        token: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
      };

    case AUTH_ACTIONS.LOAD_USER:
      return {
        ...state,
        user: action.payload.user,
        token: action.payload.token,
        isAuthenticated: true,
        isLoading: false,
      };

    case AUTH_ACTIONS.CLEAR_ERROR:
      return {
        ...state,
        error: null,
      };

    case AUTH_ACTIONS.SET_LOADING:
      return {
        ...state,
        isLoading: action.payload,
      };

    default:
      return state;
  }
}

// Create context
const AuthContext = createContext();

// Auth provider component
export function AuthProvider({ children }) {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Load user from localStorage on mount
  useEffect(() => {
    const loadUser = () => {
      try {
        const token = localStorage.getItem(`${AppConfig.storage.prefix}${AppConfig.storage.tokenKey}`);
        const userData = localStorage.getItem(`${AppConfig.storage.prefix}${AppConfig.storage.userKey}`);

        if (token && userData) {
          const user = JSON.parse(userData);
          dispatch({
            type: AUTH_ACTIONS.LOAD_USER,
            payload: { user, token },
          });
        } else {
          dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: false });
        }
      } catch (error) {

        dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: false });
      }
    };

    loadUser();
  }, []);

  // Login function
  const login = async (credentials) => {
    dispatch({ type: AUTH_ACTIONS.LOGIN_START });

    try {
      // Call backend login API
      const { default: ApiService } = await import('../services/api');

      const response = await ApiService.login({
        email: credentials.email,
        password: credentials.password,
      });

      if (!response.success) {
        throw new Error(response.message || '登录失败');
      }

      const { data } = response;


      // Determine user role based on roles array
      // Priority: regular_user first, then others by hierarchy
      let userRole = 'regular_user'; // default role
      if (data.user.roles && data.user.roles.length > 0) {
        if (data.user.roles.includes('regular_user')) {
          userRole = 'regular_user';
        } else if (data.user.roles.includes('super_admin')) {
          userRole = 'super_admin';
        } else if (data.user.roles.includes('admin')) {
          userRole = 'admin';
        } else if (data.user.roles.includes('enterprise_user')) {
          userRole = 'enterprise_user';
        } else if (data.user.roles.includes('channel_user')) {
          userRole = 'channel_user';
        } else if (data.user.roles.includes('agent_user')) {
          userRole = 'agent_user';
        } else {
          userRole = 'regular_user';
        }
      }

      // Transform backend response to frontend format
      const loginResponse = {
        user: {
          id: data.user.id,
          username: data.user.full_name,
          email: data.user.email,
          role: userRole,
          name: data.user.full_name,
          phone: data.user.phone,
          avatar: null,
          roles: data.user.roles,
          profile_completed: data.user.profile_completed,
          last_login_at: data.user.last_login_at,
        },
        token: data.access_token,
        refresh_token: data.refresh_token,
        expires_in: data.expires_in,
      };

      // Store in localStorage
      localStorage.setItem(`${AppConfig.storage.prefix}${AppConfig.storage.tokenKey}`, loginResponse.token);
      localStorage.setItem(`${AppConfig.storage.prefix}${AppConfig.storage.userKey}`, JSON.stringify(loginResponse.user));

      // Store refresh token if available
      if (loginResponse.refresh_token) {
        localStorage.setItem(`${AppConfig.storage.prefix}refresh_token`, loginResponse.refresh_token);
      }

      dispatch({
        type: AUTH_ACTIONS.LOGIN_SUCCESS,
        payload: loginResponse,
      });

      return loginResponse;
    } catch (error) {


      let errorMessage = '登录失败';

      if (error.response) {
        // HTTP error response
        if (error.response.status === 401) {
          errorMessage = '邮箱或密码错误';
        } else if (error.response.status === 500) {
          errorMessage = '服务器错误，请稍后重试';
        } else {
          errorMessage = error.response.data?.detail || error.response.data?.message || '登录失败';
        }
      } else if (error.message) {
        errorMessage = error.message;
      }

      dispatch({
        type: AUTH_ACTIONS.LOGIN_FAILURE,
        payload: errorMessage,
      });
      throw error;
    }
  };

  // Register function
  const register = async (userData) => {
    dispatch({ type: AUTH_ACTIONS.REGISTER_START });

    try {
      // 调用注册 API
      const response = await apiService.register(userData);

      if (response.success) {
        dispatch({
          type: AUTH_ACTIONS.REGISTER_SUCCESS,
          payload: { user: null, token: null },
        });

        return response;
      } else {
        throw new Error(response.message || '注册失败');
      }
    } catch (error) {
      const errorMessage = error.response?.data?.detail || error.message || '注册失败';
      dispatch({
        type: AUTH_ACTIONS.REGISTER_FAILURE,
        payload: errorMessage,
      });
      throw error;
    }
  };

  // Logout function
  const logout = async () => {
    try {
      // Call backend logout API to invalidate token on server side
      await apiService.post('/auth/logout');
    } catch (error) {

      // Continue with logout even if API call fails
    } finally {
      // Clear all user-related data from localStorage
      const keysToRemove = [
        // Authentication tokens
        `${AppConfig.storage.prefix}${AppConfig.storage.tokenKey}`,
        `${AppConfig.storage.prefix}${AppConfig.storage.userKey}`,
        `${AppConfig.storage.prefix}refresh_token`,

        // User settings (optional - uncomment if you want to clear theme settings too)
        // `${AppConfig.storage.prefix}${AppConfig.storage.settingsKey}`,
        // `${AppConfig.storage.prefix}theme`,

        // Application-specific cache data
        'applicationRecords',
        'applicationRecordsTime',

        // API cache (if using the configured cache key)
        `${AppConfig.storage.prefix}${AppConfig.storage.cacheKey}`,
      ];

      // Remove all specified keys
      keysToRemove.forEach(key => {
        try {
          localStorage.removeItem(key);
        } catch (error) {

        }
      });

      // Clear any other user-specific cache data that might exist
      // This will remove any keys that start with our app prefix (except theme if we want to keep it)
      try {
        Object.keys(localStorage).forEach(key => {
          if (key.startsWith(AppConfig.storage.prefix)) {
            // Keep theme setting if user wants to preserve it across sessions
            if (key === `${AppConfig.storage.prefix}theme`) {
              return; // Skip removing theme
            }
            // Remove other app-specific data
            localStorage.removeItem(key);
          }
        });
      } catch (error) {

      }

      // Clear sessionStorage as well (in case any data is stored there)
      try {
        // Clear all sessionStorage data with our prefix
        Object.keys(sessionStorage).forEach(key => {
          if (key.startsWith(AppConfig.storage.prefix)) {
            sessionStorage.removeItem(key);
          }
        });
      } catch (error) {

      }

      // Update authentication state
      dispatch({ type: AUTH_ACTIONS.LOGOUT });

      // Optional: Force a small delay to ensure all cleanup is complete
      // This can help with any race conditions
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  };

  // Clear error function
  const clearError = () => {
    dispatch({ type: AUTH_ACTIONS.CLEAR_ERROR });
  };

  // Check if user has permission
  const hasPermission = (permission) => {
    if (!state.user) return false;
    return AppConfig.hasPermission(state.user.role, permission);
  };

  // Get navigation for current user
  const getNavigation = () => {
    if (!state.user) return [];
    return AppConfig.getNavigationForRole(state.user.role);
  };

  const value = {
    ...state,
    login,
    register,
    logout,
    clearError,
    hasPermission,
    getNavigation,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

// Custom hook to use auth context
export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

export default AuthContext;
