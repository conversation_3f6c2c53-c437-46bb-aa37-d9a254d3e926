import { createContext, useContext, useState } from 'react';

const CustomerServiceContext = createContext();

export const useCustomerService = () => {
  const context = useContext(CustomerServiceContext);
  if (!context) {
    throw new Error('useCustomerService must be used within a CustomerServiceProvider');
  }
  return context;
};

export const CustomerServiceProvider = ({ children }) => {
  const [isChatOpen, setIsChatOpen] = useState(false);
  const [hasNewMessage, setHasNewMessage] = useState(false);

  const openChat = () => {
    setIsChatOpen(true);
    setHasNewMessage(false);
  };

  const closeChat = () => {
    setIsChatOpen(false);
  };

  const showNewMessageIndicator = () => {
    if (!isChatOpen) {
      setHasNewMessage(true);
    }
  };

  const value = {
    isChatOpen,
    hasNewMessage,
    openChat,
    closeChat,
    showNewMessageIndicator,
  };

  return (
    <CustomerServiceContext.Provider value={value}>
      {children}
    </CustomerServiceContext.Provider>
  );
};

export default CustomerServiceContext;
