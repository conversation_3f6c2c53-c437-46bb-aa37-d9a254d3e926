import axios from 'axios';
import { ApiConfig } from '../config/api-config';
import { AppConfig } from '../config/app-config';

// Create axios instance
const api = axios.create({
  baseURL: `${ApiConfig.baseURL}/api/${ApiConfig.version}`,
  timeout: ApiConfig.timeout,
  headers: ApiConfig.defaultHeaders,
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    // Try multiple token storage keys for compatibility
    let token = localStorage.getItem(`${AppConfig.storage.prefix}${AppConfig.storage.tokenKey}`);

    // Fallback to other possible token keys
    if (!token) {
      token = localStorage.getItem('ai_seo_auth_token');
    }
    if (!token) {
      token = localStorage.getItem('token');
    }

    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response) {
      // Handle specific error codes
      switch (error.response.status) {
        case 401:
          // Unauthorized - only redirect if not already on login page
          if (!window.location.hash.includes('/auth/login')) {
            localStorage.removeItem(`${AppConfig.storage.prefix}${AppConfig.storage.tokenKey}`);
            localStorage.removeItem(`${AppConfig.storage.prefix}${AppConfig.storage.userKey}`);
            window.location.href = '/#/auth/login';
          }
          break;
        case 403:
          // Forbidden - show error message
          break;
        case 404:
          // Not found
          break;
        case 500:
          // Server error
          break;
        default:
          // Other API errors
          break;
      }
    } else if (error.request) {
      // Network error
    } else {
      // Other error
    }
    return Promise.reject(error);
  }
);

// API service class
class ApiService {
  // Authentication methods
  async login(credentials) {
    const response = await api.post(ApiConfig.endpoints.auth.login, credentials);
    return response.data;
  }

  async register(userData) {
    const response = await api.post(ApiConfig.endpoints.auth.register, userData);
    return response.data;
  }

  async logout() {
    const response = await api.post(ApiConfig.endpoints.auth.logout);
    return response.data;
  }

  async refreshToken() {
    const response = await api.post(ApiConfig.endpoints.auth.refresh);
    return response.data;
  }

  async resetPassword(resetData) {
    const response = await api.post(ApiConfig.endpoints.auth.resetPassword, resetData);
    return response.data;
  }

  async sendVerificationCode(email, codeType = 'register') {
    const response = await api.post(ApiConfig.endpoints.auth.sendVerificationCode, {
      email,
      code_type: codeType
    });
    return response.data;
  }

  async verifyCode(email, code, codeType = 'register') {
    const response = await api.post(ApiConfig.endpoints.auth.verifyCode, {
      email,
      code,
      code_type: codeType
    });
    return response.data;
  }

  async changePassword(passwordData) {
    const response = await api.post(ApiConfig.endpoints.auth.changePassword, passwordData);
    return response.data;
  }

  // User methods
  async getUserProfile() {
    const response = await api.get(ApiConfig.endpoints.users.profile);
    return response.data;
  }

  async updateUserProfile(userData) {
    const response = await api.put(ApiConfig.endpoints.users.update, userData);
    return response.data;
  }

  async getUsers(params = {}) {
    const response = await api.get(ApiConfig.endpoints.users.list, { params });
    return response.data;
  }

  // Enterprise methods
  async getEnterprises(params = {}) {
    const response = await api.get(ApiConfig.endpoints.enterprises.list, { params });
    return response.data;
  }

  async createEnterprise(enterpriseData) {
    const response = await api.post(ApiConfig.endpoints.enterprises.create, enterpriseData);
    return response.data;
  }

  async updateEnterprise(id, enterpriseData) {
    const response = await api.put(`${ApiConfig.endpoints.enterprises.update}/${id}`, enterpriseData);
    return response.data;
  }

  async deleteEnterprise(id) {
    const response = await api.delete(`${ApiConfig.endpoints.enterprises.delete}/${id}`);
    return response.data;
  }

  // Company methods
  async getCompanies(params = {}) {
    const response = await api.get(ApiConfig.endpoints.companies.list, { params });
    return response.data;
  }

  async getPendingCompanies(params = {}) {
    const response = await api.get(ApiConfig.endpoints.companies.pending, { params });
    return response.data;
  }

  async verifyCompany(id, verificationData) {
    const response = await api.post(`${ApiConfig.endpoints.companies.verify}/${id}`, verificationData);
    return response.data;
  }

  // Agent methods
  async getAgents(params = {}) {
    const response = await api.get(ApiConfig.endpoints.agents.list, { params });
    return response.data;
  }

  async getPendingAgents(params = {}) {
    const response = await api.get(ApiConfig.endpoints.agents.pending, { params });
    return response.data;
  }

  async verifyAgent(id, verificationData) {
    const response = await api.post(`${ApiConfig.endpoints.agents.verify}/${id}`, verificationData);
    return response.data;
  }

  // Agent referral link methods
  async createReferralLink(linkData) {
    const response = await api.post(ApiConfig.endpoints.agents.referralLinks, linkData);
    return response.data;
  }

  async getReferralLinks(params = {}) {
    const response = await api.get(ApiConfig.endpoints.agents.referralLinks, { params });
    return response.data;
  }

  async updateReferralLink(linkId, linkData) {
    const response = await api.put(`${ApiConfig.endpoints.agents.updateReferralLink}/${linkId}`, linkData);
    return response.data;
  }

  async deleteReferralLink(linkId) {
    const response = await api.delete(`${ApiConfig.endpoints.agents.deleteReferralLink}/${linkId}`);
    return response.data;
  }

  async toggleReferralLinkStatus(linkId) {
    const response = await api.patch(`${ApiConfig.endpoints.agents.toggleReferralLinkStatus}/${linkId}/toggle-status`);
    return response.data;
  }

  // Agent customer management methods
  async getAgentCustomers(params = {}) {
    const response = await api.get('/agents/customers', { params });
    return response.data;
  }

  async getCustomerDetail(customerId) {
    const response = await api.get(`/agents/customers/${customerId}`);
    return response.data;
  }

  async getCustomersStatistics() {
    const response = await api.get('/agents/customers/statistics');
    return response.data;
  }

  async getAgentStatistics() {
    const response = await api.get('/agents/statistics');
    return response.data;
  }

  // Channel management methods
  async getChannels(params = {}) {
    const response = await api.get(ApiConfig.endpoints.channels.list, { params });
    return response.data;
  }



  async createChannel(channelData) {
    const response = await api.post(ApiConfig.endpoints.channels.create, channelData);
    return response.data;
  }

  async updateChannel(id, channelData) {
    const response = await api.put(`${ApiConfig.endpoints.channels.update}/${id}`, channelData);
    return response.data;
  }

  async deleteChannel(id) {
    const response = await api.delete(`${ApiConfig.endpoints.channels.delete}/${id}`);
    return response.data;
  }

  async getChannelDetails(id) {
    const response = await api.get(`${ApiConfig.endpoints.channels.details}/${id}`);
    return response.data;
  }

  async getChannelStats(params = {}) {
    const response = await api.get(ApiConfig.endpoints.channels.stats, { params });
    return response.data;
  }

  // Channel profile methods
  async createChannelProfile(profileData) {
    const response = await api.post('/channels/profile', profileData);
    return response.data;
  }

  async getChannelProfile() {
    const response = await api.get('/channels/profile');
    return response.data;
  }

  async updateChannelProfile(profileData) {
    const response = await api.put('/channels/profile', profileData);
    return response.data;
  }

  async getMyChannelInfo() {
    const response = await api.get('/channels/me');
    return response.data;
  }

  async updateMyChannelInfo(channelData) {
    const response = await api.put('/channels/me', channelData);
    return response.data;
  }

  // Channel withdraw management methods

  async applyWithdraw(withdrawData) {
    const response = await api.post('/channels/withdraw', withdrawData);
    return response.data;
  }

  async getWithdrawRecords(params = {}) {
    const response = await api.get('/channels/withdraw', { params });
    return response.data;
  }

  // Channel requirements and tasks methods
  async getChannelRequirements(params = {}) {
    // 使用渠道商专用接口获取需求数据
    const response = await api.get('/content/provider/requests', { params });
    return response.data;
  }

  async applyForRequirement(requirementId, applicationData) {
    // 使用订单接口申请需求
    const response = await api.post('/orders', {
      content_id: requirementId,
      ...applicationData
    });
    return response.data;
  }

  // 获取内容需求详情
  async getContentRequestDetail(requestId) {
    const response = await api.get(`/content/requests/${requestId}`);
    return response.data;
  }

  // 内容交付
  async deliverContent(requestId, deliveryData) {
    const response = await api.post(`/content/requests/${requestId}/delivery`, deliveryData);
    return response.data;
  }

  // 查看交付内容详情
  async getDeliveredContent(requestId) {
    const response = await api.get(`/content/requests/${requestId}/content`);
    return response.data;
  }

  // 内容验收
  async reviewContent(requestId, reviewData) {
    const response = await api.post(`/content/requests/${requestId}/review`, reviewData);
    return response.data;
  }

  async getChannelTasks(params = {}) {
    // 使用我的订单接口获取任务数据
    const response = await api.get('/orders/my', { params });
    return response.data;
  }

  async updateTaskProgress(taskId, progressData) {
    const response = await api.put(`/channels/tasks/${taskId}/progress`, progressData);
    return response.data;
  }

  async submitTask(taskId, submissionData) {
    const response = await api.post(`/channels/tasks/${taskId}/submit`, submissionData);
    return response.data;
  }

  // Agent commission settlement methods
  async applyCommissionSettlement(settlementData) {
    const response = await api.post('/agents/settlements', settlementData);
    return response.data;
  }

  async getCommissions(params = {}) {
    const response = await api.get('/agents/commissions', { params });
    return response.data;
  }

  // Note: Agent customer management methods are already defined above (lines 211-223)

  // Channel methods (additional to existing)
  async getPendingChannels(params = {}) {
    const response = await api.get(ApiConfig.endpoints.channels.pending, { params });
    return response.data;
  }

  async verifyChannel(id, verificationData) {
    const response = await api.post(`${ApiConfig.endpoints.channels.verify}/${id}`, verificationData);
    return response.data;
  }

  // Channel services approval methods
  async getPendingServices(params = {}) {
    const response = await api.get(ApiConfig.endpoints.channelServices.pending, { params });
    return response.data;
  }

  async approveService(serviceId, approvalData) {
    const response = await api.put(`${ApiConfig.endpoints.channelServices.approval}/${serviceId}/approval`, approvalData);
    return response.data;
  }

  // Channel categories management methods
  async getChannelCategories(params = {}) {
    const response = await api.get(ApiConfig.endpoints.channelCategories.list, { params });
    return response.data;
  }

  async createChannelCategory(categoryData) {
    const response = await api.post(ApiConfig.endpoints.channelCategories.create, categoryData);
    return response.data;
  }

  async updateChannelCategory(categoryId, categoryData) {
    const response = await api.put(`${ApiConfig.endpoints.channelCategories.update}/${categoryId}`, categoryData);
    return response.data;
  }

  async deleteChannelCategory(categoryId) {
    const response = await api.delete(`${ApiConfig.endpoints.channelCategories.delete}/${categoryId}`);
    return response.data;
  }

  // Channel category mappings methods (用户渠道服务管理)
  async getChannelCategoryMappings(params = {}) {
    const response = await api.get(ApiConfig.endpoints.channelCategoryMappings.list, { params });
    return response.data;
  }

  async createChannelCategoryMapping(mappingData) {
    const response = await api.post(ApiConfig.endpoints.channelCategoryMappings.create, mappingData);
    return response.data;
  }

  async updateChannelCategoryMapping(serviceId, mappingData) {
    const response = await api.put(`${ApiConfig.endpoints.channelCategoryMappings.update}/${serviceId}`, mappingData);
    return response.data;
  }

  async patchChannelCategoryMapping(serviceId, mappingData) {
    if (process.env.NODE_ENV === 'development') {
      console.log('API调用 - PATCH请求:', {
        url: `${ApiConfig.endpoints.channelCategoryMappings.update}/${serviceId}`,
        data: mappingData,
        apiObject: api,
        patchMethod: typeof api.patch
      });
    }

    try {
      const response = await api.patch(`${ApiConfig.endpoints.channelCategoryMappings.update}/${serviceId}`, mappingData);
      if (process.env.NODE_ENV === 'development') {
        console.log('API响应:', response.data);
      }
      return response.data;
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('PATCH请求失败:', error);
      }
      throw error;
    }
  }

  async deleteChannelCategoryMapping(serviceId) {
    const response = await api.delete(`${ApiConfig.endpoints.channelCategoryMappings.delete}/${serviceId}`);
    return response.data;
  }

  // Content methods
  async getContent(params = {}) {
    const response = await api.get(ApiConfig.endpoints.content.list, { params });
    return response.data;
  }

  async createContent(contentData) {
    const response = await api.post(ApiConfig.endpoints.content.create, contentData);
    return response.data;
  }

  async updateContent(id, contentData) {
    const response = await api.put(`${ApiConfig.endpoints.content.update}/${id}`, contentData);
    return response.data;
  }

  async deleteContent(id) {
    const response = await api.delete(`${ApiConfig.endpoints.content.delete}/${id}`);
    return response.data;
  }

  async publishContent(id) {
    const response = await api.post(`${ApiConfig.endpoints.content.publish}/${id}`);
    return response.data;
  }

  // AI service methods
  async generateContent(prompt) {
    const response = await api.post(ApiConfig.endpoints.ai.generate, { prompt });
    return response.data;
  }

  async optimizeContent(contentData) {
    const response = await api.post(ApiConfig.endpoints.ai.optimize, contentData);
    return response.data;
  }

  async analyzeContent(contentData) {
    const response = await api.post(ApiConfig.endpoints.ai.analyze, contentData);
    return response.data;
  }

  async getSuggestions(query) {
    const response = await api.get(ApiConfig.endpoints.ai.suggestions, { params: { query } });
    return response.data;
  }

  // Order methods
  async getOrders(params = {}) {
    const response = await api.get(ApiConfig.endpoints.orders.list, { params });
    return response.data;
  }

  async createOrder(orderData) {
    const response = await api.post(ApiConfig.endpoints.orders.create, orderData);
    return response.data;
  }

  async getOrderDetails(id) {
    const response = await api.get(`${ApiConfig.endpoints.orders.details}/${id}`);
    return response.data;
  }

  // Analytics methods
  async getDashboardData() {
    const response = await api.get(ApiConfig.endpoints.analytics.dashboard);
    return response.data;
  }

  async getTrafficData(params = {}) {
    const response = await api.get(ApiConfig.endpoints.analytics.traffic, { params });
    return response.data;
  }

  async getPerformanceData(params = {}) {
    const response = await api.get(ApiConfig.endpoints.analytics.performance, { params });
    return response.data;
  }

  // File upload method
  async uploadFile(file, onProgress = null) {
    const formData = new FormData();
    formData.append('file', file);

    const config = {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    };

    if (onProgress) {
      config.onUploadProgress = (progressEvent) => {
        const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
        onProgress(percentCompleted);
      };
    }

    const response = await api.post(ApiConfig.upload.endpoint, formData, config);
    return response.data;
  }

  // Generic methods
  async get(endpoint, params = {}) {
    const response = await api.get(endpoint, { params });
    return response.data;
  }

  async post(endpoint, data = {}) {
    const response = await api.post(endpoint, data);
    return response.data;
  }

  async put(endpoint, data = {}) {
    const response = await api.put(endpoint, data);
    return response.data;
  }

  async patch(endpoint, data = {}) {
    const response = await api.patch(endpoint, data);
    return response.data;
  }

  async delete(endpoint) {
    const response = await api.delete(endpoint);
    return response.data;
  }


}

// Create and export API service instance
const apiService = new ApiService();
export default apiService;
export { ApiService, api };
