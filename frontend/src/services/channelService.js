import apiService, { api } from './api';
import axios from 'axios';

/**
 * 渠道商服务类
 * 处理渠道商控制平台相关的API调用
 */
class ChannelService {
  
  // ==================== 渠道商基础信息管理 ====================

  /**
   * 获取当前用户的渠道商信息
   */
  async getMyChannelInfo() {
    try {
      const response = await apiService.getMyChannelInfo();
      return response;
    } catch (error) {
      console.error('获取渠道商信息失败:', error);
      throw error;
    }
  }

  /**
   * 获取渠道商账户信息（用于账户设置页面）
   * @returns {Promise<Object>} 渠道商账户信息
   */
  async getChannelAccountInfo() {
    try {
      console.log('获取渠道商账户信息...');
      const response = await api.get('/channels/me');
      console.log('渠道商账户信息API响应:', response);
      return response.data;
    } catch (error) {
      console.error('获取渠道商账户信息失败:', error);
      throw error;
    }
  }

  /**
   * 创建渠道商资料
   */
  async createChannelProfile(profileData) {
    try {
      const response = await apiService.createChannelProfile(profileData);
      return response;
    } catch (error) {
      console.error('创建渠道商资料失败:', error);
      throw error;
    }
  }

  /**
   * 更新渠道商信息
   */
  async updateChannelInfo(channelData) {
    try {
      const response = await apiService.updateMyChannelInfo(channelData);
      return response;
    } catch (error) {
      console.error('更新渠道商信息失败:', error);
      throw error;
    }
  }

  // ==================== 渠道统计数据 ====================
  
  /**
   * 获取渠道统计数据
   */
  async getChannelStats() {
    try {
      // 由于收益接口已删除，只获取渠道基本信息
      const channelInfo = await apiService.getMyChannelInfo();

      let totalRevenue = 0;
      let activeChannels = 0;
      let completedOrders = 0;

      if (channelInfo.success) {
        activeChannels = 1; // 如果有渠道信息，说明至少有1个渠道
        // 可以从渠道信息中获取一些基本统计数据
        const data = channelInfo.data || {};
        completedOrders = data.completed_orders || 0;
      }

      return {
        success: true,
        data: {
          channelPartners: activeChannels,
          totalRevenue: totalRevenue,
          activeChannels: activeChannels,
          conversionRate: completedOrders > 0 ? (completedOrders / 10 * 100).toFixed(1) : 0, // 简单的转化率计算
          monthlyGrowth: 0, // 暂时没有增长率数据
          partnerSatisfaction: activeChannels > 0 ? 85 : 0, // 如果有活跃渠道，给个默认满意度
        }
      };
    } catch (error) {

      // 返回默认统计数据
      return {
        success: true,
        data: {
          channelPartners: 0,
          totalRevenue: 0,
          activeChannels: 0,
          conversionRate: 0,
          monthlyGrowth: 0,
          partnerSatisfaction: 0,
        }
      };
    }
  }

  // ==================== 收益管理 ====================
  
  /**
   * 获取收益统计
   */
  async getEarnings(params = {}) {
    // 收益接口已删除，直接返回默认数据
    console.warn('收益接口已删除，返回默认数据');
    return {
      success: true,
      data: {
        totalRevenue: 0,
        totalRevenueGrowth: 0,
        commission: 0,
        commissionGrowth: 0,
        balance: 0,
        balanceStatus: 'processing',
        withdrawable: 0,
        withdrawableGrowth: 0,
        earnings: []
      }
    };
  }

  // ==================== 提现管理 ====================
  
  /**
   * 申请提现
   */
  async applyWithdraw(withdrawData) {
    try {
      const response = await apiService.applyWithdraw(withdrawData);
      return response;
    } catch (error) {
      console.error('申请提现失败:', error);
      throw error;
    }
  }

  /**
   * 获取提现记录
   */
  async getWithdrawRecords(params = {}) {
    try {
      const response = await apiService.getWithdrawRecords(params);
      return response;
    } catch (error) {
      console.error('获取提现记录失败，返回默认数据:', error);
      // 返回默认提现记录
      return {
        success: true,
        data: {
          records: [],
          pagination: {
            page: 1,
            size: 20,
            total: 0,
            pages: 1
          }
        }
      };
    }
  }

  // ==================== 需求和任务管理 ====================
  
  /**
   * 获取可申请的需求列表
   */
  async getRequirements(params = {}) {
    try {
      const response = await apiService.getChannelRequirements(params);
      return response;
    } catch (error) {
      console.error('获取需求列表失败，返回默认数据:', error);
      // 返回默认需求数据
      return {
        success: true,
        data: {
          requirements: [],
          pagination: {
            page: 1,
            size: 20,
            total: 0,
            pages: 1
          }
        }
      };
    }
  }

  /**
   * 申请需求
   */
  async applyForRequirement(requirementId, applicationData) {
    try {
      const response = await apiService.applyForRequirement(requirementId, applicationData);
      return response;
    } catch (error) {
      console.error('申请需求失败:', error);
      throw error;
    }
  }

  /**
   * 获取任务列表
   */
  async getTasks(params = {}) {
    try {
      const response = await apiService.getChannelTasks(params);
      return response;
    } catch (error) {
      console.error('获取任务列表失败，返回默认数据:', error);
      // 返回默认任务数据
      return {
        success: true,
        data: {
          tasks: [],
          statistics: {
            pendingTasks: 0,
            inProgressTasks: 0,
            completedTasks: 0
          },
          pagination: {
            page: 1,
            size: 20,
            total: 0,
            pages: 1
          }
        }
      };
    }
  }

  /**
   * 更新任务进度
   */
  async updateTaskProgress(taskId, progressData) {
    try {
      const response = await apiService.updateTaskProgress(taskId, progressData);
      return response;
    } catch (error) {
      console.error('更新任务进度失败:', error);
      throw error;
    }
  }

  /**
   * 提交任务
   */
  async submitTask(taskId, submissionData) {
    try {
      const response = await apiService.submitTask(taskId, submissionData);
      return response;
    } catch (error) {
      console.error('提交任务失败:', error);
      throw error;
    }
  }

  /**
   * 更新任务状态
   * @param {string} taskId - 任务ID
   * @param {string} status - 新状态 (pending, in_progress, completed)
   * @param {string} statusNote - 状态更新说明
   */
  async updateTaskStatus(taskId, status, statusNote = '') {
    try {
      const response = await apiService.put(`/tasks/${taskId}/status`, {
        status,
        status_note: statusNote,
        updated_at: new Date().toISOString()
      });
      return response;
    } catch (error) {
      console.error('更新任务状态失败:', error);
      throw error;
    }
  }

  // ==================== 渠道服务管理 ====================

  /**
   * 创建渠道商服务
   */
  async createProviderService(serviceData) {
    try {
      const response = await apiService.post('/provider-services/create-service', serviceData);
      return {
        success: true,
        data: response.data,
        message: '服务创建成功，等待审核'
      };
    } catch (error) {
      console.error('创建渠道服务失败:', error);
      return {
        success: false,
        message: error.response?.data?.detail || '创建服务失败'
      };
    }
  }

  /**
   * 获取我创建的服务列表
   */
  async getMyCreatedServices(params = {}) {
    try {
      const response = await apiService.get('/provider-services/my-services', { params });
      return {
        success: true,
        data: response.data || { items: [], pagination: { total: 0, page: 1, size: 20, pages: 0 } },
        message: '获取服务列表成功'
      };
    } catch (error) {
      console.error('获取我创建的服务列表失败:', error);
      return {
        success: false,
        data: { items: [], pagination: { total: 0, page: 1, size: 20, pages: 0 } },
        message: error.response?.data?.detail || '获取服务列表失败'
      };
    }
  }

  /**
   * 获取我创建的服务详情
   */
  async getMyServiceDetail(serviceId) {
    try {
      const response = await apiService.get(`/provider-services/${serviceId}`);
      return {
        success: true,
        data: response.data,
        message: '获取服务详情成功'
      };
    } catch (error) {
      console.error('获取服务详情失败:', error);
      return {
        success: false,
        message: error.response?.data?.detail || '获取服务详情失败'
      };
    }
  }

  /**
   * 更新我创建的服务
   */
  async updateMyService(serviceId, serviceData) {
    try {
      const response = await apiService.put(`/provider-services/${serviceId}`, serviceData);
      return {
        success: true,
        data: response.data,
        message: '更新服务成功'
      };
    } catch (error) {
      console.error('更新服务失败:', error);
      return {
        success: false,
        message: error.response?.data?.detail || '更新服务失败'
      };
    }
  }

  /**
   * 删除我创建的服务
   */
  async deleteMyService(serviceId) {
    try {
      const response = await apiService.delete(`/provider-services/${serviceId}`);
      return {
        success: true,
        data: response.data,
        message: '删除服务成功'
      };
    } catch (error) {
      console.error('删除服务失败:', error);
      return {
        success: false,
        message: error.response?.data?.detail || '删除服务失败'
      };
    }
  }

  // ==================== 渠道管理 ====================
  
  /**
   * 创建新渠道 (自媒体渠道绑定)
   */
  async createChannel(channelData) {
    try {
      const response = await apiService.post('/channels', channelData);
      return {
        success: true,
        data: response.data,
        message: '渠道申请提交成功'
      };
    } catch (error) {
      console.error('创建渠道失败:', error);
      return {
        success: false,
        message: error.response?.data?.message || '创建渠道失败'
      };
    }
  }

  /**
   * 获取我的渠道列表
   */
  async getMyChannels(params = {}) {
    try {
      const response = await apiService.get('/channels/my', { params });
      return {
        success: true,
        data: response.data || []
      };
    } catch (error) {
      console.error('获取渠道列表失败:', error);
      return {
        success: false,
        data: [],
        message: error.message
      };
    }
  }

  /**
   * 更新渠道状态 (用于渠道验证)
   */
  async updateChannelStatus(channelId, status, note = '') {
    try {
      const response = await apiService.put(`/channels/${channelId}/status`, {
        status,
        note,
        updated_at: new Date().toISOString()
      });
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('更新渠道状态失败:', error);
      return {
        success: false,
        message: error.response?.data?.message || '更新渠道状态失败'
      };
    }
  }

  /**
   * 验证渠道所有权
   */
  async verifyChannelOwnership(channelId, verificationData) {
    try {
      const response = await apiService.post(`/channels/${channelId}/verify`, verificationData);
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('验证渠道失败:', error);
      return {
        success: false,
        message: error.response?.data?.message || '验证渠道失败'
      };
    }
  }

  /**
   * 删除渠道
   */
  async deleteMyChannel(channelId) {
    try {
      const response = await apiService.delete(`/channels/${channelId}`);
      return {
        success: true,
        message: '渠道删除成功'
      };
    } catch (error) {
      console.error('删除渠道失败:', error);
      return {
        success: false,
        message: error.response?.data?.message || '删除渠道失败'
      };
    }
  }

  /**
   * 更新渠道信息
   */
  async updateChannel(channelId, channelData) {
    try {
      const response = await apiService.updateChannel(channelId, channelData);
      return response;
    } catch (error) {
      console.error('更新渠道信息失败:', error);
      throw error;
    }
  }

  /**
   * 删除渠道
   */
  async deleteChannel(channelId) {
    try {
      const response = await apiService.deleteChannel(channelId);
      return response;
    } catch (error) {
      console.error('删除渠道失败:', error);
      throw error;
    }
  }

  // ==================== 渠道分类管理 ====================

  /**
   * 获取我的服务分类映射
   * 对应接口: GET /api/v1/channel-category-mappings
   */
  async getMyCategories() {
    try {
      const response = await apiService.getChannelCategoryMappings();

      // 检查响应结构
      if (response && response.success && response.data) {
        return {
          success: true,
          data: response.data
        };
      } else {
        return {
          success: false,
          data: { items: [], pagination: { total: 0, page: 1, size: 20, total_pages: 0 } },
          message: '数据格式错误'
        };
      }
    } catch (error) {
      console.error('获取我的服务分类失败:', error);
      return {
        success: false,
        data: { items: [], pagination: { total: 0, page: 1, size: 20, total_pages: 0 } },
        message: error.message
      };
    }
  }

  /**
   * 添加服务分类映射
   * 对应接口: POST /api/v1/channel-category-mappings
   */
  async addCategory(categoryData) {
    try {
      const response = await apiService.createChannelCategoryMapping(categoryData);
      return {
        success: true,
        data: response
      };
    } catch (error) {
      console.error('添加服务分类失败:', error);
      throw error;
    }
  }

  /**
   * 绑定系统服务
   * 对应接口: PUT /api/v1/channel-category-mappings/{service_id}
   */
  async bindSystemService(serviceId, bindingData) {
    try {
      const response = await apiService.updateChannelCategoryMapping(serviceId, bindingData);
      return {
        success: true,
        data: response
      };
    } catch (error) {
      console.error('绑定系统服务失败:', error);
      throw error;
    }
  }

  /**
   * 更新服务绑定状态
   * 对应接口: PATCH /api/v1/channel-category-mappings/{service_id}
   */
  async updateServiceStatus(serviceId, statusData) {
    try {
      // 使用专门的 PATCH 方法更新服务状态
      const response = await apiService.patchChannelCategoryMapping(serviceId, statusData);
      return {
        success: true,
        data: response
      };
    } catch (error) {
      console.error('更新服务状态失败:', error);
      throw error;
    }
  }

  /**
   * 取消绑定系统服务
   * 对应接口: DELETE /api/v1/channel-category-mappings/{service_id}
   */
  async unbindSystemService(serviceId) {
    try {
      const response = await apiService.deleteChannelCategoryMapping(serviceId);
      return {
        success: true,
        data: response
      };
    } catch (error) {
      console.error('取消绑定系统服务失败:', error);
      throw error;
    }
  }

  /**
   * 更新服务分类映射
   * 对应接口: PUT /api/v1/channel-category-mappings/{service_id}
   */
  async updateCategory(serviceId, categoryData) {
    try {
      const response = await apiService.updateChannelCategoryMapping(serviceId, categoryData);
      return {
        success: true,
        data: response
      };
    } catch (error) {
      console.error('更新服务分类失败:', error);
      throw error;
    }
  }

  /**
   * 移除服务分类映射
   * 对应接口: DELETE /api/v1/channel-category-mappings/{service_id}
   */
  async removeCategory(serviceId) {
    try {
      const response = await apiService.deleteChannelCategoryMapping(serviceId);
      return {
        success: true,
        data: response
      };
    } catch (error) {
      console.error('移除服务分类失败:', error);
      throw error;
    }
  }



  // ==================== 渠道服务管理 ====================

  /**
   * 获取我的服务列表
   */
  async getMyServices(params = {}) {
    try {
      const response = await apiService.get('/channel-services', params);
      return response;
    } catch (error) {
      console.error('获取我的服务列表失败:', error);
      return {
        success: false,
        data: { items: [], pagination: { total: 0, page: 1, size: 20, pages: 0 } },
        message: error.message
      };
    }
  }

  /**
   * 获取渠道分类列表
   */
  async getChannelCategories(params = {}) {
    try {
      const response = await apiService.get('/channel-categories/list', { params });

      if (response && response.success && response.data) {
        return {
          success: true,
          data: response.data
        };
      } else {
        return {
          success: false,
          data: { items: [], pagination: { total: 0, page: 1, size: 20, pages: 0 } },
          message: '数据格式错误'
        };
      }
    } catch (error) {
      console.error('获取渠道分类列表失败:', error);
      return {
        success: false,
        data: { items: [], pagination: { total: 0, page: 1, size: 20, pages: 0 } },
        message: error.message
      };
    }
  }

  /**
   * 获取系统已有服务列表（用于添加服务时选择）
   */
  async getSystemServices(params = {}) {
    try {
      const response = await api.get('/channel-services', { params });

      // 检查响应结构
      if (response.data && response.data.success && response.data.data) {
        return {
          success: true,
          data: response.data.data
        };
      } else {
        return {
          success: false,
          data: { items: [], pagination: { total: 0, page: 1, size: 20, pages: 0 } },
          message: '数据格式错误'
        };
      }
    } catch (error) {
      console.error('获取系统服务列表失败:', error);
      return {
        success: false,
        data: { items: [], pagination: { total: 0, page: 1, size: 20, pages: 0 } },
        message: error.message
      };
    }
  }

  /**
   * 创建渠道服务
   */
  async createService(serviceData) {
    try {
      const response = await apiService.post('/channel-services', serviceData);
      return response;
    } catch (error) {
      console.error('创建渠道服务失败:', error);
      throw error;
    }
  }

  /**
   * 获取服务详情
   */
  async getServiceDetail(serviceId) {
    try {
      const response = await apiService.get(`/channel-services/${serviceId}`);
      // apiService.get 已经返回了 response.data，所以这里的 response 就是后端返回的数据
      return response;
    } catch (error) {
      console.error('获取服务详情失败:', error);
      // 返回错误格式，保持与其他方法一致
      return {
        success: false,
        message: error.response?.data?.detail || error.message || '获取服务详情失败'
      };
    }
  }

  /**
   * 更新服务信息
   */
  async updateService(serviceId, serviceData) {
    try {
      const response = await apiService.put(`/channel-services/${serviceId}`, serviceData);
      return response.data;
    } catch (error) {
      console.error('更新服务信息失败:', error);
      throw error;
    }
  }

  /**
   * 删除服务
   */
  async deleteService(serviceId) {
    try {
      const response = await apiService.delete(`/channel-services/${serviceId}`);
      return response.data;
    } catch (error) {
      console.error('删除服务失败:', error);
      throw error;
    }
  }

  /**
   * 获取可用分类列表
   */
  async getAvailableCategories() {
    try {
      const response = await apiService.get('/channel-categories/list?is_active=true&size=100');
      return response;
    } catch (error) {
      return {
        success: false,
        data: [],
        message: error.message
      };
    }
  }
}

// 创建并导出服务实例
const channelService = new ChannelService();
export default channelService;
