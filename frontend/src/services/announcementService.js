import api from './api';

class AnnouncementService {
  constructor() {
    this.baseUrl = '/announcements';
  }

  /**
   * 获取公告列表
   * @param {Object} params - 查询参数
   * @param {number} params.page - 页码，从1开始
   * @param {number} params.page_size - 每页数量
   * @param {string} params.target_audience - 目标受众 (enterprise, all)
   * @returns {Promise} API响应
   */
  async getAnnouncements(params = {}) {
    try {
      const defaultParams = {
        page: 1,
        page_size: 5,
        target_audience: 'enterprise'
      };
      
      const queryParams = { ...defaultParams, ...params };
      
      // 构建查询字符串
      const searchParams = new URLSearchParams();
      Object.keys(queryParams).forEach(key => {
        if (queryParams[key] !== undefined && queryParams[key] !== null) {
          searchParams.append(key, queryParams[key]);
        }
      });
      
      const response = await api.get(`${this.baseUrl}?${searchParams.toString()}`);
      return response.data;
    } catch (error) {
      console.error('获取公告列表失败:', error);
      throw error;
    }
  }

  /**
   * 获取单个公告详情
   * @param {string} id - 公告ID
   * @returns {Promise} API响应
   */
  async getAnnouncementById(id) {
    try {
      if (!id) {
        throw new Error('公告ID不能为空');
      }
      
      const response = await api.get(`${this.baseUrl}/${id}`);
      return response.data;
    } catch (error) {
      console.error('获取公告详情失败:', error);
      throw error;
    }
  }

  /**
   * 标记公告为已读
   * @param {string} id - 公告ID
   * @returns {Promise} API响应
   */
  async markAsRead(id) {
    try {
      if (!id) {
        throw new Error('公告ID不能为空');
      }
      
      const response = await api.post(`${this.baseUrl}/${id}/read`);
      return response.data;
    } catch (error) {
      console.error('标记公告已读失败:', error);
      throw error;
    }
  }

  /**
   * 获取公告统计信息
   * @param {string} target_audience - 目标受众
   * @returns {Promise} API响应
   */
  async getAnnouncementStats(target_audience = 'enterprise') {
    try {
      const response = await api.get(`${this.baseUrl}/stats?target_audience=${target_audience}`);
      return response.data;
    } catch (error) {
      console.error('获取公告统计失败:', error);
      throw error;
    }
  }
}

// 创建单例实例
const announcementService = new AnnouncementService();

export default announcementService;
