import { api } from './api';

class PaymentService {
  // Base endpoints
  baseUrl = '/payments';
  refundUrl = '/refunds';

  // ========== Payment Records Management (7 APIs) ==========
  
  // Get all payment records
  async getPayments(params = {}) {
    const response = await api.get(this.baseUrl, { params });
    return response.data;
  }

  // Get payment by ID
  async getPaymentById(id) {
    const response = await api.get(`${this.baseUrl}/${id}`);
    return response.data;
  }

  // Create payment record
  async createPayment(paymentData) {
    const response = await api.post(this.baseUrl, paymentData);
    return response.data;
  }

  // Update payment status
  async updatePaymentStatus(id, status) {
    const response = await api.patch(`${this.baseUrl}/${id}/status`, { status });
    return response.data;
  }

  // Get payments by order
  async getPaymentsByOrder(orderId) {
    const response = await api.get(`${this.baseUrl}/order/${orderId}`);
    return response.data;
  }

  // Get payments by user
  async getPaymentsByUser(userId, params = {}) {
    const response = await api.get(`${this.baseUrl}/user/${userId}`, { params });
    return response.data;
  }

  // Get payment methods
  async getPaymentMethods() {
    const response = await api.get(`${this.baseUrl}/methods`);
    return response.data;
  }

  // ========== Payment Callback Processing (3 APIs) ==========
  
  // Process payment callback
  async processCallback(provider, callbackData) {
    const response = await api.post(`${this.baseUrl}/callback/${provider}`, callbackData);
    return response.data;
  }

  // Verify payment signature
  async verifyPayment(paymentId, verificationData) {
    const response = await api.post(`${this.baseUrl}/${paymentId}/verify`, verificationData);
    return response.data;
  }

  // Retry failed payment
  async retryPayment(paymentId) {
    const response = await api.post(`${this.baseUrl}/${paymentId}/retry`);
    return response.data;
  }

  // Mock payment success (for development/testing)
  async mockPaymentSuccess(paymentId) {
    const response = await api.post(`${this.baseUrl}/${paymentId}/mock-success`);
    return response.data;
  }

  // ========== Offline Payment Management ==========
  
  // Get offline payments pending confirmation
  async getOfflinePayments(params = {}) {
    const response = await api.get(`${this.baseUrl}/offline`, { params });
    return response.data;
  }

  // Confirm offline payment
  async confirmOfflinePayment(paymentId, confirmationData) {
    const response = await api.post(`${this.baseUrl}/${paymentId}/confirm`, confirmationData);
    return response.data;
  }

  // Reject offline payment
  async rejectOfflinePayment(paymentId, reason) {
    const response = await api.post(`${this.baseUrl}/${paymentId}/reject`, { reason });
    return response.data;
  }

  // ========== Refund Management (7 APIs) ==========
  
  // Get all refunds
  async getRefunds(params = {}) {
    const response = await api.get(this.refundUrl, { params });
    return response.data;
  }

  // Create refund request
  async createRefund(refundData) {
    const response = await api.post(this.refundUrl, refundData);
    return response.data;
  }

  // Get refund by ID
  async getRefundById(id) {
    const response = await api.get(`${this.refundUrl}/${id}`);
    return response.data;
  }

  // Approve refund
  async approveRefund(id, approvalData = {}) {
    const response = await api.post(`${this.refundUrl}/${id}/approve`, approvalData);
    return response.data;
  }

  // Reject refund
  async rejectRefund(id, reason) {
    const response = await api.post(`${this.refundUrl}/${id}/reject`, { reason });
    return response.data;
  }

  // Process refund (execute the refund)
  async processRefund(id) {
    const response = await api.post(`${this.refundUrl}/${id}/process`);
    return response.data;
  }

  // Get refund status
  async getRefundStatus(id) {
    const response = await api.get(`${this.refundUrl}/${id}/status`);
    return response.data;
  }

  // Cancel refund
  async cancelRefund(id, reason) {
    const response = await api.post(`${this.refundUrl}/${id}/cancel`, { reason });
    return response.data;
  }

  // ========== Statistics and Analytics ==========
  
  // Get payment statistics
  async getPaymentStatistics(params = {}) {
    const response = await api.get(`${this.baseUrl}/statistics`, { params });
    return response.data;
  }

  // Get payment trends
  async getPaymentTrends(dateRange) {
    const response = await api.get(`${this.baseUrl}/trends`, { params: dateRange });
    return response.data;
  }

  // Get channel distribution
  async getChannelDistribution(dateRange) {
    const response = await api.get(`${this.baseUrl}/channel-distribution`, { params: dateRange });
    return response.data;
  }

  // Get success rate analytics
  async getSuccessRateAnalytics(dateRange) {
    const response = await api.get(`${this.baseUrl}/success-rate`, { params: dateRange });
    return response.data;
  }

  // ========== Batch Operations ==========
  
  // Batch approve refunds
  async batchApproveRefunds(refundIds) {
    const response = await api.post(`${this.refundUrl}/batch/approve`, { refund_ids: refundIds });
    return response.data;
  }

  // Batch reject refunds
  async batchRejectRefunds(refundIds, reason) {
    const response = await api.post(`${this.refundUrl}/batch/reject`, { 
      refund_ids: refundIds,
      reason 
    });
    return response.data;
  }

  // ========== Export and Reconciliation ==========
  
  // Export payment records
  async exportPayments(filters = {}) {
    const response = await api.post(`${this.baseUrl}/export`, filters, {
      responseType: 'blob'
    });
    return response.data;
  }

  // Export refund records
  async exportRefunds(filters = {}) {
    const response = await api.post(`${this.refundUrl}/export`, filters, {
      responseType: 'blob'
    });
    return response.data;
  }

  // Reconcile payments
  async reconcilePayments(dateRange) {
    const response = await api.post(`${this.baseUrl}/reconcile`, dateRange);
    return response.data;
  }
}

export default new PaymentService();