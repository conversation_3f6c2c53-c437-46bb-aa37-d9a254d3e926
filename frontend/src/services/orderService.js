import { api } from './api';

class OrderService {
  // Base endpoints
  baseUrl = '/orders';

  // Order CRUD Operations
  async getOrders(params = {}) {
    const response = await api.get(this.baseUrl, { params });
    return response.data;
  }

  async createOrder(orderData) {
    const response = await api.post(this.baseUrl, orderData);
    return response.data;
  }

  // Create subscription order
  async createSubscriptionOrder(orderData) {
    const response = await api.post(`${this.baseUrl}/subscription`, orderData);
    return response.data;
  }

  async getOrderById(id) {
    const response = await api.get(`${this.baseUrl}/${id}`);
    return response.data;
  }

  async updateOrder(id, orderData) {
    const response = await api.put(`${this.baseUrl}/${id}`, orderData);
    return response.data;
  }

  async deleteOrder(id) {
    const response = await api.delete(`${this.baseUrl}/${id}`);
    return response.data;
  }

  // Order Actions
  async cancelOrder(id) {
    const response = await api.post(`${this.baseUrl}/${id}/cancel`);
    return response.data;
  }

  async processPayment(id, paymentData) {
    const response = await api.post(`${this.baseUrl}/${id}/payment`, paymentData);
    return response.data;
  }

  async refundOrder(id, refundData) {
    const response = await api.post(`${this.baseUrl}/${id}/refund`, refundData);
    return response.data;
  }

  // Order Queries
  async searchOrders(searchParams) {
    const response = await api.post(`${this.baseUrl}/search`, searchParams);
    return response.data;
  }

  async getOrdersByType(type, params = {}) {
    const response = await api.get(`${this.baseUrl}/by-type/${type}`, { params });
    return response.data;
  }

  async getOrdersByStatus(status, params = {}) {
    const response = await api.get(`${this.baseUrl}/by-status/${status}`, { params });
    return response.data;
  }

  async getRecentOrders(limit = 10) {
    const response = await api.get(`${this.baseUrl}/recent`, { params: { limit } });
    return response.data;
  }

  async getPendingPaymentOrders(params = {}) {
    const response = await api.get(`${this.baseUrl}/pending-payment`, { params });
    return response.data;
  }

  async getMyOrders(params = {}) {
    const response = await api.get(`${this.baseUrl}/my`, { params });
    return response.data;
  }

  // Order Statistics
  async getOrderStatistics(dateRange = {}) {
    const response = await api.get(`${this.baseUrl}/statistics`, { params: dateRange });
    return response.data;
  }

  // Batch Operations
  async batchUpdateStatus(orderIds, status) {
    const response = await api.post(`${this.baseUrl}/batch/status`, {
      order_ids: orderIds,
      status
    });
    return response.data;
  }

  async batchCancelOrders(orderIds, reason) {
    const response = await api.post(`${this.baseUrl}/batch/cancel`, {
      order_ids: orderIds,
      reason
    });
    return response.data;
  }

  // Export
  async exportOrders(filters = {}) {
    const response = await api.post(`${this.baseUrl}/export`, filters, {
      responseType: 'blob'
    });
    return response.data;
  }
}

export default new OrderService();