import { api } from './api';

class UserService {
  // Base endpoints
  baseUrl = '/users';

  // Get user by ID (for admin)
  async getUserById(userId) {
    try {
      const response = await api.get(`${this.baseUrl}/${userId}`);
      return response.data;
    } catch (error) {
      console.error('获取用户信息失败:', error);
      throw error;
    }
  }

  // Get multiple users by IDs
  async getUsersByIds(userIds) {
    try {
      const response = await api.post(`${this.baseUrl}/batch`, { user_ids: userIds });
      return response.data;
    } catch (error) {
      console.error('批量获取用户信息失败:', error);
      // 如果批量接口不存在，逐个获取
      const users = {};
      for (const userId of userIds) {
        try {
          const user = await this.getUserById(userId);
          users[userId] = user.data || user;
        } catch (err) {
          console.warn(`获取用户 ${userId} 信息失败:`, err);
          users[userId] = {
            id: userId,
            email: `user_${userId.substring(0, 8)}@unknown.com`,
            full_name: `用户_${userId.substring(0, 8)}`,
            status: 'unknown'
          };
        }
      }
      return { data: users };
    }
  }

  // Get user list (for admin)
  async getUsers(params = {}) {
    try {
      const response = await api.get(this.baseUrl, { params });
      return response.data;
    } catch (error) {
      console.error('获取用户列表失败:', error);
      throw error;
    }
  }

  // Get current user profile
  async getCurrentUser() {
    try {
      const response = await api.get(`${this.baseUrl}/me`);
      return response.data;
    } catch (error) {
      console.error('获取当前用户信息失败:', error);
      throw error;
    }
  }

  // Update user profile
  async updateUser(userId, userData) {
    try {
      const response = await api.put(`${this.baseUrl}/${userId}`, userData);
      return response.data;
    } catch (error) {
      console.error('更新用户信息失败:', error);
      throw error;
    }
  }

  // Create user (for admin)
  async createUser(userData) {
    try {
      const response = await api.post(this.baseUrl, userData);
      return response.data;
    } catch (error) {
      console.error('创建用户失败:', error);
      throw error;
    }
  }

  // Delete user (for admin)
  async deleteUser(userId) {
    try {
      const response = await api.delete(`${this.baseUrl}/${userId}`);
      return response.data;
    } catch (error) {
      console.error('删除用户失败:', error);
      throw error;
    }
  }
}

export default new UserService();
