import axios from 'axios';

// 创建axios实例
const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:8000',
  timeout: 30000, // 30秒超时
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 可以在这里添加认证token
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    console.error('API请求错误:', error);
    return Promise.reject(error);
  }
);

/**
 * AI聊天服务
 */
class AIService {
  /**
   * 发送聊天消息（流式响应）
   * @param {string} message - 用户消息
   * @param {string} mode - 模式：'chat' 或 'agent'
   * @param {Array} conversationHistory - 对话历史
   * @param {Object} context - 上下文信息
   * @param {Function} onChunk - 接收流式数据的回调函数
   * @param {Function} onComplete - 完成时的回调函数
   * @param {Function} onError - 错误时的回调函数
   * @returns {Promise<void>}
   */
  async sendMessageStream(message, mode = 'chat', conversationHistory = [], context = {}, onChunk, onComplete, onError) {
    try {
      // ❌ 原AI Chat接口已删除，建议使用conversations接口
      if (onError) {
        onError(new Error('AI Chat接口已废弃，请使用conversations接口'));
      }
      return;

      // ❌ 原代码已注释 - 后端ai_chat.py已删除
      // const response = await fetch(`${api.defaults.baseURL}/api/v1/ai/chat`, {
      //   method: 'POST',
      //   headers: {
      //     'Content-Type': 'application/json',
      //     'Authorization': `Bearer ${localStorage.getItem('token') || ''}`,
      //   },
      //   body: JSON.stringify({
      //     message,
      //     mode,
      //     conversation_history: conversationHistory,
      //     context,
      //     stream: true
      //   })
      // });

      // if (!response.ok) {
      //   throw new Error(`HTTP error! status: ${response.status}`);
      // }

      // ❌ 原流式处理代码已注释 - AI Chat接口已删除
      // const reader = response.body.getReader();
      // const decoder = new TextDecoder();
      // let buffer = '';

      // try {
      //   while (true) {
      //     const { done, value } = await reader.read();
      //     if (done) break;
      //     buffer += decoder.decode(value, { stream: true });
      //     const lines = buffer.split('\n');
      //     buffer = lines.pop();
      //     for (const line of lines) {
      //       if (line.startsWith('data: ')) {
      //         const dataStr = line.slice(6);
      //         if (dataStr.trim() === '') continue;
      //         try {
      //           const data = JSON.parse(dataStr);
      //           if (data.type === 'message') {
      //             onChunk && onChunk(data.content, 'message');
      //           } else if (data.type === 'html') {
      //             onChunk && onChunk(data.content, 'html');
      //           } else if (data.type === 'error') {
      //             onError && onError(data.content);
      //             return;
      //           } else if (data.type === 'done') {
      //             onComplete && onComplete();
      //             return;
      //           }
      //         } catch (parseError) {
      //           console.warn('解析流式数据失败:', parseError);
      //         }
      //       }
      //     }
      //   }
      // } finally {
      //   reader.releaseLock();
      // }

    } catch (error) {
      console.error('流式AI聊天请求失败:', error);

      // 如果后端不可用，使用模拟流式响应
      if (error.message.includes('fetch') || error.message.includes('network')) {
        await this.getMockStreamResponse(message, mode, onChunk, onComplete, onError);
        return;
      }

      onError && onError(error.message || '请求失败');
    }
  }

  /**
   * 发送聊天消息（非流式响应，兼容旧版本）
   * @param {string} message - 用户消息
   * @param {string} mode - 模式：'chat' 或 'agent'
   * @param {Array} conversationHistory - 对话历史
   * @param {Object} context - 上下文信息
   * @returns {Promise<Object>} AI回复
   */
  async sendMessage(message, mode = 'chat', conversationHistory = [], context = {}) {
    try {
      // ❌ 原AI Chat接口已删除，建议使用conversations接口
      throw new Error('AI Chat接口已废弃，请使用conversations接口');

      // ❌ 原代码已注释 - 后端ai_chat.py已删除
      // const response = await api.post('/api/v1/ai/chat', {
      //   message,
      //   mode,
      //   conversation_history: conversationHistory,
      //   context,
      //   stream: false
      // });

      // ❌ AI Chat接口已废弃，返回错误信息
      throw new Error('AI Chat接口已废弃，请使用conversations接口');
    } catch (error) {
      console.error('AI聊天请求失败:', error);

      // 如果后端不可用，返回模拟响应
      if (error.code === 'ECONNREFUSED' || error.response?.status >= 500) {
        return this.getMockResponse(message, mode);
      }

      return {
        success: false,
        error: error.response?.data?.detail || error.message || '请求失败'
      };
    }
  }

  /**
   * 模拟流式响应（当后端不可用时）
   * @param {string} message - 用户消息
   * @param {string} mode - 模式
   * @param {Function} onChunk - 接收流式数据的回调函数
   * @param {Function} onComplete - 完成时的回调函数
   * @param {Function} onError - 错误时的回调函数
   */
  async getMockStreamResponse(message, mode, onChunk, onComplete, onError) {
    try {
      if (mode === 'chat') {
        const mockMessage = `基于您的查询"${message}"，我已经为您分析了相关数据：\n\n📊 **关键指标分析**\n- 当前排名：第3位（上升2位）\n- 搜索量：12,500次/月\n- 竞争难度：中等\n- 点击率：4.8%\n\n💡 **优化建议**\n1. 增加相关长尾关键词密度\n2. 优化页面加载速度\n3. 更新内容保持新鲜度\n\n需要我为您生成详细的优化方案吗？`;

        // 模拟逐字输出
        for (let i = 0; i < mockMessage.length; i++) {
          onChunk && onChunk(mockMessage[i], 'message');

          // 模拟打字延迟
          if (i % 5 === 0) {
            await new Promise(resolve => setTimeout(resolve, 50));
          }
        }

      } else if (mode === 'agent') {
        const mockMessage = `我已经为您生成了关于"${message}"的HTML页面。页面包含了现代化的设计、响应式布局和相关的数据可视化元素。您可以在预览区域查看效果。`;

        // 模拟逐字输出消息
        for (let i = 0; i < mockMessage.length; i++) {
          onChunk && onChunk(mockMessage[i], 'message');
          await new Promise(resolve => setTimeout(resolve, 30));
        }

        // 发送HTML内容
        const htmlContent = this.generateMockHTML(message);
        onChunk && onChunk(htmlContent, 'html');
      }

      onComplete && onComplete();
    } catch (error) {
      onError && onError(error.message);
    }
  }

  /**
   * 获取模拟响应（当后端不可用时）
   * @param {string} message - 用户消息
   * @param {string} mode - 模式
   * @returns {Object} 模拟响应
   */
  getMockResponse(message, mode) {
    if (mode === 'chat') {
      return {
        success: true,
        data: {
          message: `基于您的查询"${message}"，我已经为您分析了相关数据：\n\n📊 **关键指标分析**\n- 当前排名：第3位（上升2位）\n- 搜索量：12,500次/月\n- 竞争难度：中等\n- 点击率：4.8%\n\n💡 **优化建议**\n1. 增加相关长尾关键词密度\n2. 优化页面加载速度\n3. 更新内容保持新鲜度\n\n需要我为您生成详细的优化方案吗？`,
          mode: 'chat',
          timestamp: new Date().toISOString(),
          success: true
        }
      };
    } else if (mode === 'agent') {
      return {
        success: true,
        data: {
          message: `我已经为您生成了关于"${message}"的HTML页面。页面包含了现代化的设计、响应式布局和相关的数据可视化元素。您可以在预览区域查看效果。`,
          mode: 'agent',
          timestamp: new Date().toISOString(),
          html_content: this.generateMockHTML(message),
          success: true
        }
      };
    }
  }

  /**
   * 生成模拟HTML内容
   * @param {string} topic - 主题
   * @returns {string} HTML内容
   */
  generateMockHTML(topic) {
    // 根据主题生成不同的HTML模板
    if (topic.includes('电商') || topic.includes('商城')) {
      return this.getEcommerceTemplate(topic);
    } else if (topic.includes('SEO') || topic.includes('优化')) {
      return this.getSEOTemplate(topic);
    } else if (topic.includes('分析') || topic.includes('报告')) {
      return this.getAnalysisTemplate(topic);
    } else {
      return this.getDefaultTemplate(topic);
    }
  }

  /**
   * 电商网站HTML模板
   */
  getEcommerceTemplate(topic) {
    return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${topic} - SEO分析报告</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 60px 0;
        }
        .metric-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .progress-custom {
            height: 8px;
            border-radius: 4px;
        }
        .chart-container {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="hero-section">
        <div class="container">
            <div class="row">
                <div class="col-lg-8">
                    <h1><i class="fas fa-shopping-cart"></i> ${topic}</h1>
                    <p class="lead">全面的电商网站SEO分析与优化建议</p>
                </div>
            </div>
        </div>
    </div>

    <div class="container my-5">
        <div class="row">
            <div class="col-md-3">
                <div class="metric-card text-center">
                    <h3 class="text-primary">85</h3>
                    <p class="mb-0">SEO得分</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card text-center">
                    <h3 class="text-success">1,234</h3>
                    <p class="mb-0">关键词排名</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card text-center">
                    <h3 class="text-warning">45.6%</h3>
                    <p class="mb-0">有机流量增长</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card text-center">
                    <h3 class="text-info">2.3s</h3>
                    <p class="mb-0">页面加载时间</p>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-lg-8">
                <div class="chart-container">
                    <h4><i class="fas fa-chart-line"></i> 流量趋势分析</h4>
                    <div class="mt-3">
                        <div class="d-flex justify-content-between mb-2">
                            <span>有机搜索流量</span>
                            <span>78%</span>
                        </div>
                        <div class="progress progress-custom mb-3">
                            <div class="progress-bar bg-success" style="width: 78%"></div>
                        </div>
                        
                        <div class="d-flex justify-content-between mb-2">
                            <span>直接访问</span>
                            <span>15%</span>
                        </div>
                        <div class="progress progress-custom mb-3">
                            <div class="progress-bar bg-primary" style="width: 15%"></div>
                        </div>
                        
                        <div class="d-flex justify-content-between mb-2">
                            <span>社交媒体</span>
                            <span>7%</span>
                        </div>
                        <div class="progress progress-custom mb-3">
                            <div class="progress-bar bg-warning" style="width: 7%"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="chart-container">
                    <h4><i class="fas fa-bullseye"></i> 优化建议</h4>
                    <ul class="list-unstyled mt-3">
                        <li class="mb-2"><i class="fas fa-check-circle text-success"></i> 优化产品页面标题</li>
                        <li class="mb-2"><i class="fas fa-check-circle text-success"></i> 改善图片ALT标签</li>
                        <li class="mb-2"><i class="fas fa-exclamation-circle text-warning"></i> 提升页面加载速度</li>
                        <li class="mb-2"><i class="fas fa-exclamation-circle text-warning"></i> 增加内部链接</li>
                        <li class="mb-2"><i class="fas fa-times-circle text-danger"></i> 修复重复内容问题</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>`;
  }

  /**
   * SEO分析HTML模板
   */
  getSEOTemplate(topic) {
    return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${topic} - SEO优化报告</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .hero-section {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 60px 0;
        }
        .metric-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            transition: transform 0.3s ease;
        }
        .metric-card:hover {
            transform: translateY(-5px);
        }
        .progress-custom {
            height: 10px;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="hero-section">
        <div class="container">
            <h1><i class="fas fa-search"></i> ${topic}</h1>
            <p class="lead">专业的SEO分析与优化策略报告</p>
        </div>
    </div>

    <div class="container my-5">
        <div class="row">
            <div class="col-md-4">
                <div class="metric-card text-center">
                    <i class="fas fa-chart-line fa-3x text-primary mb-3"></i>
                    <h3 class="text-primary">92</h3>
                    <p class="mb-0">SEO健康度</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="metric-card text-center">
                    <i class="fas fa-key fa-3x text-success mb-3"></i>
                    <h3 class="text-success">567</h3>
                    <p class="mb-0">关键词数量</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="metric-card text-center">
                    <i class="fas fa-tachometer-alt fa-3x text-warning mb-3"></i>
                    <h3 class="text-warning">1.8s</h3>
                    <p class="mb-0">加载速度</p>
                </div>
            </div>
        </div>

        <div class="row mt-5">
            <div class="col-12">
                <div class="metric-card">
                    <h4><i class="fas fa-tasks"></i> 优化进度</h4>
                    <div class="mt-4">
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>页面标题优化</span>
                                <span>90%</span>
                            </div>
                            <div class="progress progress-custom">
                                <div class="progress-bar bg-success" style="width: 90%"></div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>Meta描述优化</span>
                                <span>75%</span>
                            </div>
                            <div class="progress progress-custom">
                                <div class="progress-bar bg-info" style="width: 75%"></div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>内容质量提升</span>
                                <span>60%</span>
                            </div>
                            <div class="progress progress-custom">
                                <div class="progress-bar bg-warning" style="width: 60%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>`;
  }

  /**
   * 分析报告HTML模板
   */
  getAnalysisTemplate(topic) {
    return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${topic} - 数据分析报告</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 60px 0;
        }
        .analysis-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .data-table {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="hero-section">
        <div class="container">
            <h1><i class="fas fa-chart-bar"></i> ${topic}</h1>
            <p class="lead">深度数据分析与洞察报告</p>
        </div>
    </div>

    <div class="container my-5">
        <div class="row">
            <div class="col-lg-6">
                <div class="analysis-card">
                    <h4><i class="fas fa-chart-pie"></i> 数据概览</h4>
                    <div class="row mt-3">
                        <div class="col-6 text-center">
                            <h3 class="text-primary">15,234</h3>
                            <p class="mb-0">总访问量</p>
                        </div>
                        <div class="col-6 text-center">
                            <h3 class="text-success">+23.5%</h3>
                            <p class="mb-0">增长率</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="analysis-card">
                    <h4><i class="fas fa-users"></i> 用户行为</h4>
                    <div class="row mt-3">
                        <div class="col-6 text-center">
                            <h3 class="text-warning">3.2</h3>
                            <p class="mb-0">平均会话时长</p>
                        </div>
                        <div class="col-6 text-center">
                            <h3 class="text-info">68%</h3>
                            <p class="mb-0">跳出率</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12">
                <div class="data-table">
                    <table class="table table-striped mb-0">
                        <thead class="table-dark">
                            <tr>
                                <th>关键词</th>
                                <th>排名</th>
                                <th>搜索量</th>
                                <th>点击率</th>
                                <th>趋势</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>SEO优化</td>
                                <td><span class="badge bg-success">3</span></td>
                                <td>12,500</td>
                                <td>4.8%</td>
                                <td><i class="fas fa-arrow-up text-success"></i></td>
                            </tr>
                            <tr>
                                <td>关键词分析</td>
                                <td><span class="badge bg-warning">7</span></td>
                                <td>8,900</td>
                                <td>3.2%</td>
                                <td><i class="fas fa-arrow-down text-danger"></i></td>
                            </tr>
                            <tr>
                                <td>网站优化</td>
                                <td><span class="badge bg-primary">5</span></td>
                                <td>15,600</td>
                                <td>5.1%</td>
                                <td><i class="fas fa-arrow-up text-success"></i></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>`;
  }

  /**
   * 默认HTML模板
   */
  getDefaultTemplate(topic) {
    return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${topic} - 专业报告</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 60px 0;
        }
        .content-card {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="hero-section">
        <div class="container">
            <h1><i class="fas fa-file-alt"></i> ${topic}</h1>
            <p class="lead">专业的分析报告与建议</p>
        </div>
    </div>

    <div class="container my-5">
        <div class="content-card">
            <h3>报告概述</h3>
            <p>这是一个基于您的需求"${topic}"生成的专业报告页面。</p>
            
            <h4 class="mt-4">主要内容</h4>
            <ul>
                <li>详细的数据分析</li>
                <li>专业的建议和策略</li>
                <li>可视化的数据展示</li>
                <li>实用的操作指南</li>
            </ul>
            
            <div class="alert alert-info mt-4">
                <i class="fas fa-info-circle"></i>
                <strong>提示：</strong>这个页面是根据您的需求自动生成的，您可以根据实际情况进行调整和优化。
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>`;
  }
}

// 创建并导出服务实例
const aiService = new AIService();
export default aiService;
