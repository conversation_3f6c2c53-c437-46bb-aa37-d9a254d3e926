import { api } from './api';

class SubscriptionService {
  // Base endpoints
  baseUrl = '/subscriptions';
  packagesUrl = '/subscriptions/packages';  // 修正：packages接口实际不存在，使用available-plans
  quotasUrl = '/subscriptions';             // 修正：配额接口在subscriptions下
  billsUrl = '/subscriptions';              // 修正：账单接口在subscriptions下
  autoRenewalUrl = '/subscriptions/auto-renewal';

  // ========== Subscription Management (5 APIs) ==========

  // Get all subscriptions (admin) or my subscriptions (user)
  async getSubscriptions(params = {}) {
    const { endpoint, ...queryParams } = params;
    const url = endpoint || this.baseUrl;
    const response = await api.get(url, { params: queryParams });
    return response.data;
  }

  // Get my subscriptions
  async getMySubscriptions(params = {}) {
    const response = await api.get(`${this.baseUrl}/my`, { params });
    return response.data;
  }

  // Get current active subscription
  async getCurrentSubscription(roleContext = null) {
    const params = {};
    if (roleContext) {
      params.role_context = roleContext;
    }
    const response = await api.get(`${this.baseUrl}/current`, { params });
    return response.data;
  }

  // Get subscription history
  async getSubscriptionHistory() {
    const response = await api.get(`${this.baseUrl}/my-history`);
    return response.data;
  }

  // Get subscription by ID
  async getSubscriptionById(id) {
    const response = await api.get(`${this.baseUrl}/${id}`);
    return response.data;
  }

  // Create new subscription
  async createSubscription(subscriptionData) {
    const response = await api.post(this.baseUrl, subscriptionData);
    return response.data;
  }

  // Update subscription
  async updateSubscription(id, updateData) {
    const response = await api.put(`${this.baseUrl}/${id}`, updateData);
    return response.data;
  }

  // Cancel subscription
  async cancelSubscription(id, reason) {
    const response = await api.post(`${this.baseUrl}/${id}/cancel`, { reason });
    return response.data;
  }

  // Get subscriptions by user
  async getSubscriptionsByUser(userId, params = {}) {
    const response = await api.get(`${this.baseUrl}/user/${userId}`, { params });
    return response.data;
  }

  // ========== Package Management (2 APIs) ==========

  // Get all packages
  async getPackages(params = {}) {
    const response = await api.get(this.packagesUrl, { params });
    return response.data;
  }

  // Get available subscription plans
  async getAvailablePlans(params = {}) {
    const response = await api.get(`${this.baseUrl}/available-plans`, { params });
    return response.data;
  }

  // Get plan by ID
  async getPlanById(planId) {
    try {
      const response = await api.get(`${this.baseUrl}/plans/${planId}`);
      return response.data;
    } catch (error) {
      console.error('Failed to get plan by ID:', error);
      return null;
    }
  }

  // Get package by ID
  async getPackageById(id) {
    const response = await api.get(`${this.packagesUrl}/${id}`);
    return response.data;
  }

  // Create package
  async createPackage(packageData) {
    const response = await api.post(this.packagesUrl, packageData);
    return response.data;
  }

  // Update package
  async updatePackage(id, packageData) {
    const response = await api.put(`${this.packagesUrl}/${id}`, packageData);
    return response.data;
  }

  // Delete package
  async deletePackage(id) {
    const response = await api.delete(`${this.packagesUrl}/${id}`);
    return response.data;
  }

  // ========== Subscription Changes (4 APIs) ==========

  // Upgrade subscription
  async upgradeSubscription(upgradeData) {
    const response = await api.post(`${this.baseUrl}/upgrade`, upgradeData);
    return response.data;
  }

  // Downgrade subscription
  async downgradeSubscription(downgradeData) {
    const response = await api.post(`${this.baseUrl}/downgrade`, downgradeData);
    return response.data;
  }

  // ❌ 续费功能已删除 - 后端subscription_renewals.py已删除
  // async renewSubscription(renewalData) {
  //   const response = await api.post(`${this.baseUrl}/renew`, renewalData);
  //   return response.data;
  // }

  // ❌ 重复的cancelSubscription方法已删除 - 使用第一个版本

  // Pause subscription
  async pauseSubscription(id, pauseData) {
    const response = await api.post(`${this.baseUrl}/${id}/pause`, pauseData);
    return response.data;
  }

  // Resume subscription
  async resumeSubscription(id) {
    const response = await api.post(`${this.baseUrl}/${id}/resume`);
    return response.data;
  }

  // ❌ ========== Quota Management APIs已删除 ==========
  // 后端subscription_quotas.py已删除，以下方法不再可用

  // ❌ Get quotas - 已删除
  // async getQuotas(subscriptionId) {
  //   const response = await api.get(`${this.quotasUrl}/${subscriptionId}/quota`);
  //   return response.data;
  // }

  // ❌ Update quota - 已删除
  // async updateQuota(subscriptionId, quotaType, newLimit) {
  //   const response = await api.put(`${this.quotasUrl}/${subscriptionId}/${quotaType}`, {
  //     limit: newLimit
  //   });
  //   return response.data;
  // }

  // ❌ Get quota usage - 已删除
  // async getQuotaUsage(subscriptionId, quotaType) {
  //   const response = await api.get(`${this.quotasUrl}/${subscriptionId}/${quotaType}/usage`);
  //   return response.data;
  // }

  // ❌ Reset quota - 已删除
  // async resetQuota(subscriptionId, quotaType) {
  //   const response = await api.post(`${this.quotasUrl}/${subscriptionId}/${quotaType}/reset`);
  //   return response.data;
  // }

  // ❌ Get quota alerts - 已删除
  // async getQuotaAlerts(subscriptionId) {
  //   const response = await api.get(`${this.quotasUrl}/${subscriptionId}/alerts`);
  //   return response.data;
  // }

  // ❌ Set quota alert - 已删除
  // async setQuotaAlert(subscriptionId, alertConfig) {
  //   const response = await api.post(`${this.quotasUrl}/${subscriptionId}/alerts`, alertConfig);
  //   return response.data;
  // }

  // ❌ Get quota history - 已删除
  // async getQuotaHistory(subscriptionId, params = {}) {
  //   const response = await api.get(`${this.quotasUrl}/${subscriptionId}/history`, { params });
  //   return response.data;
  // }

  // ❌ ========== Auto-Renewal Management APIs已删除 ==========
  // 后端subscription_renewals.py已删除，以下方法不再可用

  // ❌ Get auto-renewal settings - 已删除
  // async getAutoRenewalSettings(subscriptionId) {
  //   const response = await api.get(`${this.autoRenewalUrl}/${subscriptionId}`);
  //   return response.data;
  // }

  // ❌ Enable auto-renewal - 已删除
  // async enableAutoRenewal(subscriptionId, settings) {
  //   const response = await api.post(`${this.autoRenewalUrl}/${subscriptionId}/enable`, settings);
  //   return response.data;
  // }

  // ❌ Disable auto-renewal - 已删除
  // async disableAutoRenewal(subscriptionId) {
  //   const response = await api.post(`${this.autoRenewalUrl}/${subscriptionId}/disable`);
  //   return response.data;
  // }

  // ❌ Update auto-renewal settings - 已删除
  // async updateAutoRenewalSettings(subscriptionId, settings) {
  //   const response = await api.put(`${this.autoRenewalUrl}/${subscriptionId}`, settings);
  //   return response.data;
  // }

  // ❌ Get all auto-renewals - 已删除
  // async getAllAutoRenewals(params = {}) {
  //   const response = await api.get(this.autoRenewalUrl, { params });
  //   return response.data;
  // }

  // ❌ ========== Billing Management APIs已删除 ==========
  // 后端subscription_bills.py已删除，以下方法不再可用

  // ❌ Get bills - 已删除
  // async getBills(params = {}) {
  //   const response = await api.get(`${this.billsUrl}/bills`, { params });
  //   return response.data;
  // }

  // ❌ Get bill by ID - 已删除
  // async getBillById(id) {
  //   const response = await api.get(`${this.billsUrl}/bills/${id}`);
  //   return response.data;
  // }

  // ❌ Get bills by subscription - 已删除
  // async getBillsBySubscription(subscriptionId, params = {}) {
  //   const response = await api.get(`${this.billsUrl}/${subscriptionId}/bills`, { params });
  //   return response.data;
  // }

  // ❌ Download bill - 已删除
  // async downloadBill(id) {
  //   const response = await api.get(`${this.billsUrl}/${id}/download`, {
  //     responseType: 'blob'
  //   });
  //   return response.data;
  // }

  // ❌ Send bill email - 已删除
  // async sendBillEmail(id, email) {
  //   const response = await api.post(`${this.billsUrl}/${id}/send`, { email });
  //   return response.data;
  // }

  // ❌ Generate bill - 已删除
  // async generateBill(subscriptionId, billData) {
  //   const response = await api.post(`${this.billsUrl}/generate`, {
  //     subscription_id: subscriptionId,
  //     ...billData
  //   });
  //   return response.data;
  // }

  // ========== Statistics and Analytics ==========
  
  // Get subscription statistics
  async getSubscriptionStatistics(params = {}) {
    const response = await api.get(`${this.baseUrl}/statistics`, { params });
    return response.data;
  }

  // Get expiring subscriptions
  async getExpiringSubscriptions(days = 30) {
    const response = await api.get(`${this.baseUrl}/expiring`, { 
      params: { days } 
    });
    return response.data;
  }

  // Get renewal rate
  async getRenewalRate(dateRange) {
    const response = await api.get(`${this.baseUrl}/renewal-rate`, { 
      params: dateRange 
    });
    return response.data;
  }

  // Get revenue analytics
  async getRevenueAnalytics(dateRange) {
    const response = await api.get(`${this.baseUrl}/revenue`, { 
      params: dateRange 
    });
    return response.data;
  }

  // ========== Admin Operations ==========

  // Admin edit subscription - 管理员编辑订阅
  async adminEditSubscription(subscriptionId, updateData) {
    const response = await api.put(`${this.baseUrl}/${subscriptionId}/admin-edit`, updateData);
    return response.data;
  }

  // Admin activate subscription - 管理员激活订阅
  async adminActivateSubscription(subscriptionId, reason = '') {
    const response = await api.put(`${this.baseUrl}/${subscriptionId}/admin-edit`, {
      status: 'active',
      reason: reason || '管理员激活订阅'
    });
    return response.data;
  }

  // Admin deactivate subscription - 管理员停用订阅
  async adminDeactivateSubscription(subscriptionId, reason = '') {
    const response = await api.put(`${this.baseUrl}/${subscriptionId}/admin-edit`, {
      status: 'expired',
      reason: reason || '管理员停用订阅'
    });
    return response.data;
  }

  // Admin suspend subscription - 管理员暂停订阅
  async adminSuspendSubscription(subscriptionId, reason = '') {
    const response = await api.put(`${this.baseUrl}/${subscriptionId}/admin-edit`, {
      status: 'suspended',
      reason: reason || '管理员暂停订阅'
    });
    return response.data;
  }

  // Admin reset quota usage - 管理员重置配额使用量
  async adminResetQuotaUsage(subscriptionId, reason = '') {
    const response = await api.put(`${this.baseUrl}/${subscriptionId}/admin-edit`, {
      content_used: 0,
      monitoring_used: 0,
      ai_used: 0,
      reason: reason || '管理员重置配额使用量'
    });
    return response.data;
  }

  // ========== Batch Operations ==========

  // ❌ Batch renew subscriptions - 已删除
  // async batchRenewSubscriptions(subscriptionIds, renewalData) {
  //   const response = await api.post(`${this.baseUrl}/batch/renew`, {
  //     subscription_ids: subscriptionIds,
  //     ...renewalData
  //   });
  //   return response.data;
  // }

  // Batch cancel subscriptions
  async batchCancelSubscriptions(subscriptionIds, reason) {
    const response = await api.post(`${this.baseUrl}/batch/cancel`, {
      subscription_ids: subscriptionIds,
      reason
    });
    return response.data;
  }

  // ❌ Batch update auto-renewal - 已删除
  // async batchUpdateAutoRenewal(subscriptionIds, enabled) {
  //   const response = await api.post(`${this.autoRenewalUrl}/batch/update`, {
  //     subscription_ids: subscriptionIds,
  //     enabled
  //   });
  //   return response.data;
  // }

  // ========== Export Operations ==========
  
  // Export subscriptions
  async exportSubscriptions(filters = {}) {
    const response = await api.post(`${this.baseUrl}/export`, filters, {
      responseType: 'blob'
    });
    return response.data;
  }

  // ❌ Export bills - 已删除
  // async exportBills(filters = {}) {
  //   const response = await api.post(`${this.billsUrl}/export`, filters, {
  //     responseType: 'blob'
  //   });
  //   return response.data;
  // }

  // ❌ Send renewal reminder - 已删除
  // async sendRenewalReminder(subscriptionId) {
  //   const response = await api.post(`${this.baseUrl}/${subscriptionId}/renewal-reminder`);
  //   return response.data;
  // }
}

// 创建实例并导出
const subscriptionService = new SubscriptionService();
export default subscriptionService;