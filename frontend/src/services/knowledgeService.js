import axios from 'axios';

// 创建axios实例
const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:8000',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器 - 添加认证token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('ai_seo_auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器 - 处理错误
api.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Token过期，清除本地存储并跳转到登录页
      localStorage.removeItem('ai_seo_auth_token');
      window.location.href = '/#/auth/login';
    }
    return Promise.reject(error);
  }
);

class KnowledgeService {
  /**
   * 创建知识库
   * @param {Object} data - 知识库数据
   * @param {string} data.name - 知识库名称
   * @param {string} [data.description] - 知识库描述
   */
  async createKnowledgeBase(data) {
    try {
      const response = await api.post('/api/v1/knowledge-bases', data);
      return response;
    } catch (error) {
      console.error('创建知识库失败:', error);
      throw error;
    }
  }

  /**
   * 获取用户知识库列表
   * @param {Object} params - 查询参数
   * @param {number} [params.page=1] - 页码
   * @param {number} [params.size=20] - 每页数量
   */
  async getKnowledgeBases(params = {}) {
    try {
      const response = await api.get('/api/v1/knowledge-bases', { params });
      return response;
    } catch (error) {
      console.error('获取知识库列表失败:', error);
      throw error;
    }
  }

  /**
   * 获取知识库详情
   * @param {string} kbId - 知识库ID
   */
  async getKnowledgeBase(kbId) {
    try {
      const response = await api.get(`/api/v1/knowledge-bases/${kbId}`);
      return response;
    } catch (error) {
      console.error('获取知识库详情失败:', error);
      throw error;
    }
  }

  /**
   * 删除知识库
   * @param {string} kbId - 知识库ID
   */
  async deleteKnowledgeBase(kbId) {
    try {
      const response = await api.delete(`/api/v1/knowledge-bases/${kbId}`);
      return response;
    } catch (error) {
      console.error('删除知识库失败:', error);
      throw error;
    }
  }

  /**
   * 上传文档到知识库
   * @param {string} kbId - 知识库ID
   * @param {Object} data - 文档数据
   * @param {string} data.title - 文档标题
   * @param {string} data.content - 文档内容
   * @param {string} [data.file_type='text'] - 文件类型
   */
  async uploadDocument(kbId, data) {
    try {
      const response = await api.post(`/api/v1/knowledge-bases/${kbId}/documents`, data);
      return response;
    } catch (error) {
      console.error('上传文档失败:', error);
      throw error;
    }
  }

  /**
   * 上传文件到知识库
   * @param {string} kbId - 知识库ID
   * @param {File} file - 文件对象
   * @param {Function} [onProgress] - 上传进度回调
   */
  async uploadFile(kbId, file, onProgress) {
    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await api.post(`/api/v1/knowledge-bases/${kbId}/documents/file`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        onUploadProgress: (progressEvent) => {
          if (onProgress) {
            const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
            onProgress(percentCompleted);
          }
        }
      });

      return response;
    } catch (error) {
      console.error('上传文件失败:', error);
      throw error;
    }
  }

  /**
   * 获取知识库文档列表
   * @param {string} kbId - 知识库ID
   * @param {Object} params - 查询参数
   * @param {number} [params.page=1] - 页码
   * @param {number} [params.size=20] - 每页数量
   */
  async getDocuments(kbId, params = {}) {
    try {
      const response = await api.get(`/api/v1/knowledge-bases/${kbId}/documents`, { params });
      return response;
    } catch (error) {
      console.error('获取文档列表失败:', error);
      throw error;
    }
  }

  /**
   * 删除文档
   * @param {string} docId - 文档ID
   */
  async deleteDocument(docId) {
    try {
      const response = await api.delete(`/api/v1/documents/${docId}`);
      return response;
    } catch (error) {
      console.error('删除文档失败:', error);
      throw error;
    }
  }

  /**
   * 获取文档处理状态
   * @param {string} docId - 文档ID
   */
  async getDocumentStatus(docId) {
    try {
      const response = await api.get(`/api/v1/documents/${docId}/status`);
      return response;
    } catch (error) {
      console.error('获取文档状态失败:', error);
      throw error;
    }
  }

  /**
   * 搜索知识库内容
   * @param {string} kbId - 知识库ID
   * @param {Object} data - 搜索数据
   * @param {string} data.query - 搜索查询
   * @param {Array} [data.keywords] - 关键词列表
   * @param {number} [data.limit=5] - 结果数量限制
   */
  async searchKnowledgeBase(kbId, data) {
    try {
      const response = await api.post(`/api/v1/knowledge-bases/${kbId}/search`, data);
      return response;
    } catch (error) {
      console.error('搜索知识库失败:', error);
      throw error;
    }
  }

  /**
   * 批量搜索多个知识库
   * @param {Array} kbIds - 知识库ID列表
   * @param {Object} data - 搜索数据
   * @param {string} data.query - 搜索查询
   * @param {Array} [data.keywords] - 关键词列表
   * @param {number} [data.limit=5] - 结果数量限制
   */
  async batchSearchKnowledgeBases(kbIds, data) {
    try {
      const params = new URLSearchParams();
      kbIds.forEach(id => params.append('knowledge_base_ids', id));
      
      const response = await api.post(`/api/v1/knowledge-bases/batch-search?${params.toString()}`, data);
      return response;
    } catch (error) {
      console.error('批量搜索知识库失败:', error);
      throw error;
    }
  }

  /**
   * 获取知识库统计信息
   * @param {string} kbId - 知识库ID
   */
  async getKnowledgeBaseStatistics(kbId) {
    try {
      const response = await api.get(`/api/v1/knowledge-bases/${kbId}/statistics`);
      return response;
    } catch (error) {
      console.error('获取知识库统计失败:', error);
      throw error;
    }
  }

  /**
   * 更新知识库信息
   * @param {string} kbId - 知识库ID
   * @param {Object} data - 更新数据
   * @param {string} [data.name] - 知识库名称
   * @param {string} [data.description] - 知识库描述
   */
  async updateKnowledgeBase(kbId, data) {
    try {
      const response = await api.put(`/api/v1/knowledge-bases/${kbId}`, data);
      return response;
    } catch (error) {
      console.error('更新知识库失败:', error);
      throw error;
    }
  }

  /**
   * 导出知识库
   * @param {string} kbId - 知识库ID
   * @param {Object} options - 导出选项
   * @param {string} [options.format='json'] - 导出格式
   * @param {boolean} [options.include_vectors=false] - 是否包含向量数据
   */
  async exportKnowledgeBase(kbId, options = {}) {
    try {
      const response = await api.post(`/api/v1/knowledge-bases/${kbId}/export`, options);
      return response;
    } catch (error) {
      console.error('导出知识库失败:', error);
      throw error;
    }
  }

  /**
   * 重新处理文档
   * @param {string} docId - 文档ID
   */
  async reprocessDocument(docId) {
    try {
      const response = await api.post(`/api/v1/documents/${docId}/reprocess`);
      return response;
    } catch (error) {
      console.error('重新处理文档失败:', error);
      throw error;
    }
  }
}

// 创建单例实例
const knowledgeService = new KnowledgeService();

export default knowledgeService;
