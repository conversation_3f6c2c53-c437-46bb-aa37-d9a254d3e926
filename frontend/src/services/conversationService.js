import ApiService from './api';

class ConversationService {
  /**
   * 创建新对话
   * @param {Object} data - 对话数据
   * @param {string} data.title - 对话标题
   * @param {string} [data.template_id] - 模板ID
   * @param {Object} [data.template_parameters] - 模板参数
   * @param {Array} [data.knowledge_bases] - 知识库ID列表
   */
  async createConversation(data) {
    try {
      const response = await ApiService.post('/conversations', data);
      return response.data;
    } catch (error) {
      console.error('创建对话失败:', error);
      throw error;
    }
  }

  /**
   * 获取用户对话列表
   * @param {Object} params - 查询参数
   * @param {number} [params.page=1] - 页码
   * @param {number} [params.size=20] - 每页数量
   * @param {string} [params.keyword] - 搜索关键词
   */
  async getConversations(params = {}) {
    try {
      const response = await ApiService.get('/conversations', { params });
      return response.data;
    } catch (error) {
      console.error('获取对话列表失败:', error);
      throw error;
    }
  }

  /**
   * 获取对话详情
   * @param {string} conversationId - 对话ID
   */
  async getConversation(conversationId) {
    try {
      const response = await ApiService.get(`/conversations/${conversationId}`);
      return response.data;
    } catch (error) {
      console.error('获取对话详情失败:', error);
      throw error;
    }
  }

  /**
   * 删除对话
   * @param {string} conversationId - 对话ID
   */
  async deleteConversation(conversationId) {
    try {
      const response = await ApiService.delete(`/conversations/${conversationId}`);
      return response.data;
    } catch (error) {
      console.error('删除对话失败:', error);
      throw error;
    }
  }

  /**
   * 发送消息到对话（流式响应）
   * @param {string} conversationId - 对话ID
   * @param {Object} data - 消息数据
   * @param {string} data.content - 消息内容
   * @param {Function} onChunk - 处理流式数据的回调函数
   */
  async sendMessage(conversationId, data, onChunk) {
    try {
      const token = localStorage.getItem('ai_seo_auth_token');
      const response = await fetch(`/api/v1/conversations/${conversationId}/messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(data)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = '';

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const chunk = JSON.parse(line.slice(6));
              if (onChunk) {
                onChunk(chunk);
              }
            } catch (e) {
              console.error('解析SSE数据失败:', e);
            }
          }
        }
      }
    } catch (error) {
      console.error('发送消息失败:', error);
      throw error;
    }
  }

  /**
   * 搜索对话
   * @param {Object} data - 搜索数据
   * @param {string} data.query - 搜索查询
   * @param {number} [data.limit=10] - 结果数量限制
   */
  async searchConversations(data) {
    try {
      const response = await ApiService.post('/conversations/search', data);
      return response.data;
    } catch (error) {
      console.error('搜索对话失败:', error);
      throw error;
    }
  }

  /**
   * 获取对话统计信息
   * @param {string} conversationId - 对话ID
   */
  async getConversationStatistics(conversationId) {
    try {
      const response = await ApiService.get(`/conversations/${conversationId}/statistics`);
      return response.data;
    } catch (error) {
      console.error('获取对话统计失败:', error);
      throw error;
    }
  }

  /**
   * 更新对话标题
   * @param {string} conversationId - 对话ID
   * @param {string} title - 新标题
   */
  async updateConversationTitle(conversationId, title) {
    try {
      const response = await ApiService.put(`/conversations/${conversationId}/title`, { title });
      return response.data;
    } catch (error) {
      console.error('更新对话标题失败:', error);
      throw error;
    }
  }

  /**
   * 导出对话
   * @param {string} conversationId - 对话ID
   * @param {Object} options - 导出选项
   * @param {string} [options.format='json'] - 导出格式
   * @param {boolean} [options.include_metadata=true] - 是否包含元数据
   */
  async exportConversation(conversationId, options = {}) {
    try {
      const response = await ApiService.post(`/conversations/${conversationId}/export`, options);
      return response.data;
    } catch (error) {
      console.error('导出对话失败:', error);
      throw error;
    }
  }
}

// 创建单例实例
const conversationService = new ConversationService();

export default conversationService;
