import axios from 'axios';
import { ApiConfig } from '../config/api-config';
import { AppConfig } from '../config/app-config';

// Create axios instance for upload service
const uploadApi = axios.create({
  baseURL: `${ApiConfig.baseURL}/api/${ApiConfig.version}`,
  timeout: ApiConfig.timeout,
});

// Request interceptor to add auth token
uploadApi.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem(`${AppConfig.storage.prefix}${AppConfig.storage.tokenKey}`);
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

class UploadService {
  /**
   * 上传单个文件
   * @param {File} file - 要上传的文件
   * @param {string} businessType - 业务类型 (content, attachment, document等)
   * @param {string} businessId - 业务ID (可选)
   * @param {boolean} isPublic - 是否公开访问 (默认false)
   * @param {number} expiresHours - 过期时间(小时) (可选)
   * @returns {Promise<Object>} 上传结果
   */
  async uploadFile(file, businessType = 'attachment', businessId = null, isPublic = false, expiresHours = null) {
    try {
      // 验证文件对象
      if (!file || !(file instanceof File)) {
        throw new Error('无效的文件对象');
      }

      const formData = new FormData();
      formData.append('file', file);
      formData.append('business_type', businessType);
      formData.append('is_public', isPublic.toString());

      if (businessId) {
        formData.append('business_id', businessId);
      }

      if (expiresHours) {
        formData.append('expires_hours', expiresHours.toString());
      }

      const response = await uploadApi.post('/upload/file', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        }
      });

      if (response.data.success) {
        return response.data.data;
      } else {
        throw new Error(response.data.message || '文件上传失败');
      }
    } catch (error) {
      throw new Error(error.response?.data?.detail || error.message || '文件上传失败');
    }
  }

  /**
   * 批量上传文件
   * @param {File[]} files - 要上传的文件数组
   * @param {string} businessType - 业务类型
   * @param {string} businessId - 业务ID (可选)
   * @param {boolean} isPublic - 是否公开访问
   * @param {number} expiresHours - 过期时间(小时) (可选)
   * @param {Function} onProgress - 进度回调函数
   * @returns {Promise<Object[]>} 上传结果数组
   */
  async uploadFiles(files, businessType = 'attachment', businessId = null, isPublic = false, expiresHours = null, onProgress = null) {
    const uploadPromises = files.map(async (file, index) => {
      try {
        const result = await this.uploadFile(file, businessType, businessId, isPublic, expiresHours);
        
        if (onProgress) {
          onProgress(index + 1, files.length, file.name, 'success');
        }
        
        return {
          file: file,
          success: true,
          result: result,
          error: null
        };
      } catch (error) {
        if (onProgress) {
          onProgress(index + 1, files.length, file.name, 'error', error.message);
        }
        
        return {
          file: file,
          success: false,
          result: null,
          error: error.message
        };
      }
    });

    return await Promise.all(uploadPromises);
  }

  /**
   * 获取文件预签名上传URL (批量上传)
   * @param {Object[]} filesInfo - 文件信息数组 [{filename, size, mime_type}]
   * @param {string} businessType - 业务类型
   * @param {string} businessId - 业务ID (可选)
   * @param {boolean} isPublic - 是否公开访问
   * @param {number} expiresHours - 过期时间(小时) (可选)
   * @returns {Promise<Object>} 批量上传响应
   */
  async getBatchUploadUrls(filesInfo, businessType = 'attachment', businessId = null, isPublic = false, expiresHours = null) {
    try {
      const requestData = {
        files_info: filesInfo,
        business_type: businessType,
        is_public: isPublic
      };

      if (businessId) {
        requestData.business_id = businessId;
      }

      if (expiresHours) {
        requestData.expires_hours = expiresHours;
      }

      const response = await uploadApi.post('/upload/batch', requestData);

      if (response.data.success) {
        return response.data.data;
      } else {
        throw new Error(response.data.message || '获取上传URL失败');
      }
    } catch (error) {
      throw new Error(error.response?.data?.detail || error.message || '获取上传URL失败');
    }
  }

  /**
   * 确认批量上传完成
   * @param {string} sessionId - 上传会话ID
   * @param {string[]} fileIds - 已上传的文件ID数组
   * @returns {Promise<Object>} 确认结果
   */
  async confirmBatchUpload(sessionId, fileIds) {
    try {
      const response = await uploadApi.post('/upload/batch/confirm', {
        session_id: sessionId,
        file_ids: fileIds
      });

      if (response.data.success) {
        return response.data.data;
      } else {
        throw new Error(response.data.message || '确认上传失败');
      }
    } catch (error) {
      throw new Error(error.response?.data?.detail || error.message || '确认上传失败');
    }
  }

  /**
   * 验证文件类型和大小
   * @param {File} file - 要验证的文件
   * @param {string[]} allowedTypes - 允许的文件类型数组
   * @param {number} maxSize - 最大文件大小(字节)
   * @returns {Object} 验证结果 {valid: boolean, error: string}
   */
  validateFile(file, allowedTypes = [], maxSize = 100 * 1024 * 1024) { // 默认100MB
    if (!file) {
      return { valid: false, error: '请选择文件' };
    }

    // 检查文件大小
    if (file.size > maxSize) {
      const maxSizeMB = Math.round(maxSize / 1024 / 1024);
      return { valid: false, error: `文件大小不能超过 ${maxSizeMB}MB` };
    }

    // 检查文件类型
    if (allowedTypes.length > 0) {
      const fileExtension = file.name.split('.').pop().toLowerCase();
      const mimeType = file.type.toLowerCase();

      const isValidExtension = allowedTypes.some(type => {
        const typeStr = type.toLowerCase().trim();

        // 处理通配符类型，如 image/*
        if (typeStr.includes('*')) {
          const baseType = typeStr.split('/')[0];
          return mimeType.startsWith(baseType + '/');
        }

        // 处理扩展名，如 .pdf
        if (typeStr.startsWith('.')) {
          return typeStr === '.' + fileExtension;
        }

        // 处理完整的MIME类型，如 image/jpeg
        if (typeStr.includes('/')) {
          return typeStr === mimeType;
        }

        // 处理纯扩展名，如 jpg
        return typeStr === fileExtension;
      });

      if (!isValidExtension) {
        return { valid: false, error: `不支持的文件类型，支持的类型：${allowedTypes.join(', ')}` };
      }
    }

    return { valid: true, error: null };
  }

  /**
   * 获取文件下载链接
   * @param {string} fileId - 文件ID
   * @param {number} expiresHours - 链接有效期(小时)，默认1小时
   * @returns {Promise<Object>} 下载信息
   */
  async getFileDownloadUrl(fileId, expiresHours = 1) {
    try {
      const response = await uploadApi.get(`/upload/files/${fileId}/url`, {
        params: { expires_hours: expiresHours }
      });

      if (response.data.success) {
        return response.data.data;
      } else {
        throw new Error(response.data.message || '获取下载链接失败');
      }
    } catch (error) {
      throw new Error(error.response?.data?.detail || error.message || '获取下载链接失败');
    }
  }

  /**
   * 下载文件
   * @param {string} fileId - 文件ID
   * @param {string} filename - 文件名（可选）
   * @returns {Promise<void>}
   */
  async downloadFile(fileId, filename = null) {
    try {
      // 获取下载链接（通过后端API）
      const downloadInfo = await this.getFileDownloadUrl(fileId, 1); // 1小时有效期
      const downloadUrl = downloadInfo.download_url;
      const finalFilename = filename || downloadInfo.filename || 'download';

      if (!downloadUrl) {
        throw new Error('无法获取有效的下载链接');
      }

      // 使用fetch下载文件内容
      const response = await fetch(downloadUrl);

      if (!response.ok) {
        throw new Error(`下载请求失败: ${response.status} ${response.statusText}`);
      }

      // 获取文件内容
      const blob = await response.blob();

      // 创建下载链接
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = finalFilename;

      // 触发下载
      document.body.appendChild(link);
      link.click();

      // 清理
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      return downloadInfo;
    } catch (error) {
      throw new Error(error.message || '文件下载失败');
    }
  }

  /**
   * 格式化文件大小
   * @param {number} bytes - 字节数
   * @returns {string} 格式化后的文件大小
   */
  formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}

export default new UploadService();
