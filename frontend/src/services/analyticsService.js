import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000/api/v1';

const analyticsService = {
  // ============= Business Overview =============
  
  // Get core business metrics
  async getCoreMetrics(dateRange) {
    try {
      const params = {
        start_date: dateRange?.startDate,
        end_date: dateRange?.endDate
      };
      const response = await axios.get(`${API_BASE_URL}/analytics/core-metrics`, { params });
      return response.data;
    } catch (error) {
      // Return mock data for development
      return {
        revenue: {
          total: 285600,
          growth: 15.3,
          trend: 'up'
        },
        orders: {
          total: 1256,
          growth: 8.7,
          trend: 'up'
        },
        users: {
          total: 3420,
          growth: 12.1,
          trend: 'up'
        },
        conversion: {
          rate: 3.8,
          growth: -2.1,
          trend: 'down'
        }
      };
    }
  },

  // Get trend comparison data
  async getTrendComparison(metric, period = '7d') {
    try {
      const response = await axios.get(`${API_BASE_URL}/analytics/trends/${metric}`, {
        params: { period }
      });
      return response.data;
    } catch (error) {
      // Return mock trend data with subscription and content data
      const generateTrendData = () => {
        const data = [];
        const days = period === '7d' ? 7 : period === '30d' ? 30 : 90;
        const today = new Date();

        // 基础趋势参数
        let subscriptionRevenueTrend = 6000;
        let contentRevenueTrend = 1800;
        let subscriptionCountTrend = 8;
        let contentOrdersTrend = 25;

        for (let i = days - 1; i >= 0; i--) {
          const date = new Date(today);
          date.setDate(date.getDate() - i);

          // 添加平滑的趋势变化（每天小幅增长）
          const growthFactor = 1 + (0.002 * (days - i)); // 轻微上升趋势

          // 基础数据 + 趋势 + 小幅随机波动
          const subscriptionRevenue = Math.floor(subscriptionRevenueTrend * growthFactor + (Math.random() - 0.5) * 400);
          const contentRevenue = Math.floor(contentRevenueTrend * growthFactor + (Math.random() - 0.5) * 200);
          const subscriptionCount = Math.floor(subscriptionCountTrend * growthFactor + (Math.random() - 0.5) * 2);
          const contentOrders = Math.floor(contentOrdersTrend * growthFactor + (Math.random() - 0.5) * 6);

          data.push({
            date: date.toISOString().split('T')[0],
            // 订阅相关数据
            subscription_revenue: Math.max(0, subscriptionRevenue),
            subscription_count: Math.max(0, subscriptionCount),
            // 内容交易相关数据
            content_revenue: Math.max(0, contentRevenue),
            content_orders: Math.max(0, contentOrders),
            // 兼容原有数据格式
            current: Math.max(0, subscriptionRevenue + contentRevenue),
            previous: Math.max(0, (subscriptionRevenue + contentRevenue) * 0.85)
          });
        }
        return data;
      };
      
      return {
        metric,
        period,
        data: generateTrendData()
      };
    }
  },

  // Get real-time activity feed
  async getActivityFeed(limit = 10) {
    try {
      const response = await axios.get(`${API_BASE_URL}/analytics/activity-feed`, {
        params: { limit }
      });
      return response.data;
    } catch (error) {
      // Return mock activity data
      return [
        { id: 1, type: 'order', message: '新软文发布订单 #SW12345', time: '2分钟前', icon: 'shopping_cart' },
        { id: 2, type: 'user', message: '企业用户注册成功', time: '5分钟前', icon: 'person_add' },
        { id: 3, type: 'payment', message: 'AI写作套餐支付 ¥2,880', time: '8分钟前', icon: 'payment' },
        { id: 4, type: 'content', message: '软文发布至新浪网完成', time: '12分钟前', icon: 'publish' },
        { id: 5, type: 'subscription', message: '用户升级至企业版', time: '18分钟前', icon: 'upgrade' },
        { id: 6, type: 'order', message: 'SEO优化服务订单完成', time: '25分钟前', icon: 'shopping_cart' },
        { id: 7, type: 'content', message: 'AI生成内容质量检测通过', time: '32分钟前', icon: 'publish' },
        { id: 8, type: 'user', message: '代理商推荐用户注册', time: '38分钟前', icon: 'person_add' }
      ];
    }
  },

  // ============= Order Analysis =============
  
  // Get order analytics
  async getOrderAnalytics(dateRange) {
    try {
      const params = {
        start_date: dateRange?.startDate,
        end_date: dateRange?.endDate
      };
      const response = await axios.get(`${API_BASE_URL}/analytics/orders`, { params });
      return response.data;
    } catch (error) {
      // Return mock order analytics
      return {
        trend: this.generateOrderTrendData(),
        distribution: this.generateOrderDistribution(),
        funnel: this.generateConversionFunnel(),
        topProducts: this.generateTopProducts()
      };
    }
  },

  // Generate mock order trend data
  generateOrderTrendData() {
    const data = [];
    let ordersTrend = 40;
    let revenueTrend = 8000;

    for (let i = 29; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);

      // 添加平滑趋势
      const growthFactor = 1 + (0.001 * (30 - i));
      const orders = Math.floor(ordersTrend * growthFactor + (Math.random() - 0.5) * 10);
      const revenue = Math.floor(revenueTrend * growthFactor + (Math.random() - 0.5) * 2000);

      data.push({
        date: date.toISOString().split('T')[0],
        orders: Math.max(0, orders),
        revenue: Math.max(0, revenue),
        avg_value: Math.max(0, Math.floor(revenue / Math.max(1, orders)))
      });
    }
    return data;
  },

  // Generate order distribution
  generateOrderDistribution() {
    return [
      { type: 'AI内容生成', value: 40, count: 567 },
      { type: '软文发布', value: 35, count: 495 },
      { type: 'SEO优化', value: 15, count: 212 },
      { type: '数据监控', value: 10, count: 142 }
    ];
  },

  // Generate conversion funnel
  generateConversionFunnel() {
    return [
      { stage: '平台访问', value: 12500, rate: 100 },
      { stage: '用户注册', value: 4200, rate: 33.6 },
      { stage: '浏览服务', value: 2800, rate: 22.4 },
      { stage: '创建订单', value: 1350, rate: 10.8 },
      { stage: '完成支付', value: 1180, rate: 9.4 },
      { stage: '服务交付', value: 1120, rate: 9.0 }
    ];
  },

  // Generate top products
  generateTopProducts() {
    return [
      { name: 'AI智能写作套餐', orders: 284, revenue: 85200, growth: 18.5 },
      { name: '软文发布服务', orders: 245, revenue: 73500, growth: 12.3 },
      { name: 'SEO关键词优化', orders: 189, revenue: 56700, growth: 8.7 },
      { name: '品牌监控服务', orders: 156, revenue: 46800, growth: 15.2 },
      { name: '竞品分析报告', orders: 98, revenue: 29400, growth: -3.1 }
    ];
  },

  // ============= Financial Analysis =============
  
  // Get financial analytics
  async getFinancialAnalytics(dateRange) {
    try {
      const params = {
        start_date: dateRange?.startDate,
        end_date: dateRange?.endDate
      };
      const response = await axios.get(`${API_BASE_URL}/analytics/financial`, { params });
      return response.data;
    } catch (error) {
      // Return mock financial data
      return {
        revenue: this.generateRevenueData(),
        payments: this.generatePaymentChannels(),
        refunds: this.generateRefundAnalysis(),
        cashflow: this.generateCashflowData()
      };
    }
  },

  // Generate revenue data
  generateRevenueData() {
    const data = [];
    let revenueTrend = 180000;
    let costTrend = 95000;

    for (let i = 11; i >= 0; i--) {
      const date = new Date();
      date.setMonth(date.getMonth() - i);

      // 添加月度增长趋势
      const growthFactor = 1 + (0.02 * (12 - i));
      const revenue = Math.floor(revenueTrend * growthFactor + (Math.random() - 0.5) * 20000);
      const cost = Math.floor(costTrend * growthFactor + (Math.random() - 0.5) * 15000);

      data.push({
        month: date.toLocaleDateString('zh-CN', { month: 'short' }),
        revenue: Math.max(0, revenue),
        cost: Math.max(0, cost),
        profit: Math.max(0, revenue - cost)
      });
    }
    return data;
  },

  // Generate payment channels data
  generatePaymentChannels() {
    return [
      { channel: '支付宝', amount: 125000, percentage: 43.8, transactions: 523 },
      { channel: '微信支付', amount: 98000, percentage: 34.3, transactions: 412 },
      { channel: '银行转账', amount: 45000, percentage: 15.8, transactions: 89 },
      { channel: '信用卡', amount: 17600, percentage: 6.1, transactions: 67 }
    ];
  },

  // Generate refund analysis
  generateRefundAnalysis() {
    return {
      total: 15600,
      count: 23,
      rate: 1.8,
      reasons: [
        { reason: '服务质量', count: 8, amount: 5400 },
        { reason: '延期交付', count: 6, amount: 4200 },
        { reason: '需求变更', count: 5, amount: 3500 },
        { reason: '其他', count: 4, amount: 2500 }
      ],
      trend: this.generateRefundTrend()
    };
  },

  // Generate refund trend
  generateRefundTrend() {
    const data = [];
    for (let i = 29; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      data.push({
        date: date.toISOString().split('T')[0],
        refunds: Math.floor(Math.random() * 5),
        amount: Math.floor(Math.random() * 2000)
      });
    }
    return data;
  },

  // Generate cashflow data
  generateCashflowData() {
    const data = [];
    for (let i = 6; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      data.push({
        date: date.toISOString().split('T')[0],
        inflow: Math.floor(Math.random() * 50000) + 30000,
        outflow: Math.floor(Math.random() * 30000) + 20000,
        balance: Math.floor(Math.random() * 100000) + 200000
      });
    }
    return data;
  },

  // ============= User Analysis =============
  
  // Get user analytics
  async getUserAnalytics(dateRange) {
    try {
      const params = {
        start_date: dateRange?.startDate,
        end_date: dateRange?.endDate
      };
      const response = await axios.get(`${API_BASE_URL}/analytics/users`, { params });
      return response.data;
    } catch (error) {
      // Return mock user analytics
      return {
        growth: this.generateUserGrowth(),
        subscriptions: this.generateSubscriptionData(),
        activity: this.generateActivityData(),
        retention: this.generateRetentionData()
      };
    }
  },

  // Generate user growth data
  generateUserGrowth() {
    const data = [];
    let newUsersTrend = 35;
    let activeUsersTrend = 400;
    let totalUsers = 3200;

    for (let i = 29; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);

      // 添加用户增长趋势
      const growthFactor = 1 + (0.001 * (30 - i));
      const newUsers = Math.floor(newUsersTrend * growthFactor + (Math.random() - 0.5) * 10);
      const activeUsers = Math.floor(activeUsersTrend * growthFactor + (Math.random() - 0.5) * 50);
      totalUsers += Math.max(0, newUsers); // 累积总用户数

      data.push({
        date: date.toISOString().split('T')[0],
        new_users: Math.max(0, newUsers),
        active_users: Math.max(0, activeUsers),
        total_users: totalUsers
      });
    }
    return data;
  },

  // Generate subscription data
  generateSubscriptionData() {
    return {
      distribution: [
        { plan: '免费版', users: 1850, percentage: 54.1 },
        { plan: '基础版', users: 890, percentage: 26.0 },
        { plan: '专业版', users: 520, percentage: 15.2 },
        { plan: '企业版', users: 160, percentage: 4.7 }
      ],
      mrr: 186500, // Monthly Recurring Revenue
      arr: 2238000, // Annual Recurring Revenue
      churn_rate: 2.3,
      ltv: 8500 // Lifetime Value
    };
  },

  // Generate activity data
  generateActivityData() {
    return {
      dau: 892, // Daily Active Users
      wau: 2145, // Weekly Active Users
      mau: 2980, // Monthly Active Users
      engagement_rate: 26.1,
      avg_session_duration: '8m 32s',
      pages_per_session: 5.3,
      heatmap: this.generateActivityHeatmap()
    };
  },

  // Generate activity heatmap
  generateActivityHeatmap() {
    const data = [];
    const days = ['日', '一', '二', '三', '四', '五', '六'];
    const hours = Array.from({ length: 24 }, (_, i) => i);
    
    days.forEach((day, dayIndex) => {
      hours.forEach(hour => {
        data.push({
          day,
          hour,
          value: Math.floor(Math.random() * 100)
        });
      });
    });
    
    return data;
  },

  // Generate retention data
  generateRetentionData() {
    return {
      day1: 85.2,
      day7: 62.5,
      day30: 45.3,
      cohorts: [
        { cohort: '1月', retention: [100, 85, 72, 65, 58, 52, 48] },
        { cohort: '2月', retention: [100, 87, 75, 68, 61, 55] },
        { cohort: '3月', retention: [100, 83, 70, 63, 57] },
        { cohort: '4月', retention: [100, 86, 73, 66] }
      ]
    };
  },

  // ============= Custom Reports =============
  
  // Get saved reports
  async getSavedReports() {
    try {
      const response = await axios.get(`${API_BASE_URL}/analytics/reports`);
      return response.data;
    } catch (error) {
      // Return mock saved reports
      return [
        {
          id: '1',
          name: '月度经营报告',
          type: 'monthly',
          schedule: '每月1日',
          last_run: '2024-04-01',
          status: 'active'
        },
        {
          id: '2',
          name: '周销售分析',
          type: 'weekly',
          schedule: '每周一',
          last_run: '2024-04-08',
          status: 'active'
        },
        {
          id: '3',
          name: '用户行为报告',
          type: 'custom',
          schedule: '手动',
          last_run: '2024-04-05',
          status: 'paused'
        }
      ];
    }
  },

  // Create custom report
  async createReport(reportConfig) {
    try {
      const response = await axios.post(`${API_BASE_URL}/analytics/reports`, reportConfig);
      return response.data;
    } catch (error) {
      console.error('Error creating report:', error);
      throw error;
    }
  },

  // Export report
  async exportReport(reportId, format = 'pdf') {
    try {
      const response = await axios.get(`${API_BASE_URL}/analytics/reports/${reportId}/export`, {
        params: { format },
        responseType: 'blob'
      });
      return response.data;
    } catch (error) {
      console.error('Error exporting report:', error);
      throw error;
    }
  },

  // ============= Utility Functions =============
  
  // Calculate growth rate
  calculateGrowthRate(current, previous) {
    if (previous === 0) return 0;
    return ((current - previous) / previous * 100).toFixed(1);
  },

  // Format currency
  formatCurrency(amount) {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY'
    }).format(amount);
  },

  // Format percentage
  formatPercentage(value) {
    return `${value.toFixed(1)}%`;
  },

  // Get date range presets
  getDateRangePresets() {
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    
    return [
      {
        label: '今天',
        value: 'today',
        startDate: today,
        endDate: today
      },
      {
        label: '昨天',
        value: 'yesterday',
        startDate: yesterday,
        endDate: yesterday
      },
      {
        label: '最近7天',
        value: '7days',
        startDate: new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000),
        endDate: today
      },
      {
        label: '最近30天',
        value: '30days',
        startDate: new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000),
        endDate: today
      },
      {
        label: '本月',
        value: 'this_month',
        startDate: new Date(today.getFullYear(), today.getMonth(), 1),
        endDate: today
      },
      {
        label: '上月',
        value: 'last_month',
        startDate: new Date(today.getFullYear(), today.getMonth() - 1, 1),
        endDate: new Date(today.getFullYear(), today.getMonth(), 0)
      }
    ];
  }
};

export default analyticsService;