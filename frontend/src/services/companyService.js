import ApiService from './api';

class CompanyService {
  /**
   * 获取企业信息
   * @returns {Promise<Object>} 企业信息
   */
  async getCompanyInfo() {
    try {
      const response = await ApiService.get('/companies/me');
      return response;
    } catch (error) {
      console.error('获取企业信息失败:', error);
      throw error;
    }
  }

  /**
   * 更新企业信息
   * @param {Object} companyData 企业信息数据
   * @returns {Promise<Object>} 更新结果
   */
  async updateCompanyInfo(companyData) {
    try {
      const response = await ApiService.put('/companies/me', companyData);
      return response;
    } catch (error) {
      console.error('更新企业信息失败:', error);
      throw error;
    }
  }
}

export default new CompanyService();
