import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000/api/v1';

const statusService = {
  // ============= Status Logs Management =============
  
  // Get status change logs with filters
  async getStatusLogs(params = {}) {
    try {
      const response = await axios.get(`${API_BASE_URL}/status/order-status-logs`, { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching status logs:', error);
      throw error;
    }
  },

  // Get order status history
  async getOrderStatusHistory(orderId) {
    try {
      const response = await axios.get(`${API_BASE_URL}/status/order-status-logs/${orderId}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching order status history:', error);
      throw error;
    }
  },

  // Create status log entry
  async createStatusLog(logData) {
    try {
      const response = await axios.post(`${API_BASE_URL}/status/order-status-logs`, logData);
      return response.data;
    } catch (error) {
      console.error('Error creating status log:', error);
      throw error;
    }
  },

  // ============= Status Transitions =============
  
  // Get status transition rules
  async getStatusTransitions() {
    try {
      const response = await axios.get(`${API_BASE_URL}/status/order-status/transitions`);
      return response.data;
    } catch (error) {
      console.error('Error fetching status transitions:', error);
      throw error;
    }
  },

  // Validate status transition
  async validateStatusTransition(orderId, toStatus) {
    try {
      const response = await axios.post(`${API_BASE_URL}/status/order-status/validate`, {
        order_id: orderId,
        to_status: toStatus
      });
      return response.data;
    } catch (error) {
      console.error('Error validating status transition:', error);
      throw error;
    }
  },

  // ============= Statistics & Analytics =============
  
  // Get status statistics
  async getStatusStatistics(startDate, endDate) {
    try {
      const params = {};
      if (startDate) params.start_date = startDate;
      if (endDate) params.end_date = endDate;
      
      const response = await axios.get(`${API_BASE_URL}/status/order-status/statistics`, { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching status statistics:', error);
      throw error;
    }
  },

  // ============= Batch Operations =============
  
  // Batch update order statuses
  async batchUpdateStatus(orderIds, toStatus, changeReason) {
    try {
      const response = await axios.post(`${API_BASE_URL}/status/order-status/batch-update`, {
        order_ids: orderIds,
        to_status: toStatus,
        change_reason: changeReason
      });
      return response.data;
    } catch (error) {
      console.error('Error batch updating statuses:', error);
      throw error;
    }
  },

  // ============= Real-time Updates (WebSocket Mock) =============
  
  // Subscribe to status updates (simulated with polling)
  subscribeToStatusUpdates(callback, interval = 5000) {
    const pollInterval = setInterval(async () => {
      try {
        const logs = await this.getStatusLogs({ limit: 10 });
        callback(logs.items);
      } catch (error) {
        console.error('Error polling status updates:', error);
      }
    }, interval);

    // Return unsubscribe function
    return () => clearInterval(pollInterval);
  },

  // ============= Utility Functions =============
  
  // Get status color mapping
  getStatusColor(status) {
    const colorMap = {
      pending: '#FFA726',
      processing: '#42A5F5',
      completed: '#66BB6A',
      cancelled: '#EF5350',
      failed: '#F44336',
      refunded: '#AB47BC',
      shipped: '#26C6DA',
      delivered: '#9CCC65',
      paid: '#4CAF50',
      unpaid: '#FF9800'
    };
    return colorMap[status] || '#9E9E9E';
  },

  // Get status icon
  getStatusIcon(status) {
    const iconMap = {
      pending: 'PendingActions',
      processing: 'Loop',
      completed: 'CheckCircle',
      cancelled: 'Cancel',
      failed: 'Error',
      refunded: 'MoneyOff',
      shipped: 'LocalShipping',
      delivered: 'Done',
      paid: 'Paid',
      unpaid: 'Payment'
    };
    return iconMap[status] || 'Help';
  },

  // Format status for display
  formatStatus(status) {
    const nameMap = {
      pending: '待处理',
      processing: '处理中',
      completed: '已完成',
      cancelled: '已取消',
      failed: '失败',
      refunded: '已退款',
      shipped: '已发货',
      delivered: '已送达',
      paid: '已支付',
      unpaid: '未支付'
    };
    return nameMap[status] || status;
  },

  // Get transition reason options
  getTransitionReasons(fromStatus, toStatus) {
    const reasons = {
      'pending_to_processing': ['开始处理', '自动分配', '手动接单'],
      'processing_to_completed': ['任务完成', '客户确认', '自动完成'],
      'any_to_cancelled': ['客户取消', '系统取消', '超时取消', '管理员取消'],
      'completed_to_refunded': ['客户申请退款', '质量问题', '服务问题'],
      'processing_to_failed': ['处理失败', '资源不足', '技术问题']
    };
    
    const key = `${fromStatus}_to_${toStatus}`;
    return reasons[key] || reasons[`any_to_${toStatus}`] || ['状态变更'];
  },

  // Calculate status duration
  calculateStatusDuration(startTime, endTime) {
    const start = new Date(startTime);
    const end = endTime ? new Date(endTime) : new Date();
    const diff = end - start;
    
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    
    if (hours > 24) {
      const days = Math.floor(hours / 24);
      return `${days}天${hours % 24}小时`;
    } else if (hours > 0) {
      return `${hours}小时${minutes}分钟`;
    } else {
      return `${minutes}分钟`;
    }
  },

  // Generate mock data for charts
  generateMockChartData() {
    return {
      statusDistribution: [
        { name: '待处理', value: 45, color: '#FFA726' },
        { name: '处理中', value: 30, color: '#42A5F5' },
        { name: '已完成', value: 120, color: '#66BB6A' },
        { name: '已取消', value: 15, color: '#EF5350' },
        { name: '失败', value: 5, color: '#F44336' }
      ],
      trendData: [
        { date: '2024-01-01', pending: 20, processing: 15, completed: 45 },
        { date: '2024-01-02', pending: 25, processing: 20, completed: 50 },
        { date: '2024-01-03', pending: 18, processing: 25, completed: 55 },
        { date: '2024-01-04', pending: 22, processing: 18, completed: 60 },
        { date: '2024-01-05', pending: 30, processing: 22, completed: 48 },
        { date: '2024-01-06', pending: 28, processing: 25, completed: 52 },
        { date: '2024-01-07', pending: 15, processing: 30, completed: 65 }
      ],
      avgTransitionTime: {
        'pending_to_processing': '2小时15分',
        'processing_to_completed': '5小时30分',
        'completed_to_delivered': '24小时',
        'any_to_cancelled': '1小时45分'
      }
    };
  }
};

export default statusService;