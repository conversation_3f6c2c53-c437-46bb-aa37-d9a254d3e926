import axios from 'axios';

// 创建axios实例
const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:8000',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器 - 添加认证token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('ai_seo_auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器 - 处理错误
api.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Token过期，清除本地存储并跳转到登录页
      localStorage.removeItem('ai_seo_auth_token');
      window.location.href = '/#/auth/login';
    }
    return Promise.reject(error);
  }
);

// 内容需求相关API
export const contentService = {
  // 创建内容需求
  createContentRequest: async (requestData) => {
    try {
      const response = await api.post('/api/v1/content-requests', requestData);
      return response;
    } catch (error) {
      throw new Error(error.response?.data?.detail || '创建需求失败');
    }
  },



  // 企业用户获取内容需求列表
  getEnterpriseContentRequests: async (params = {}) => {
    try {
      const response = await api.get('/api/v1/content-requests', { params });
      return response;
    } catch (error) {
      throw new Error(error.response?.data?.detail || '获取企业需求列表失败');
    }
  },

  // 渠道商获取内容需求列表
  getProviderContentRequests: async (params = {}) => {
    try {
      const response = await api.get('/api/v1/content/provider/requests', { params });
      return response;
    } catch (error) {
      throw new Error(error.response?.data?.detail || '获取渠道商需求列表失败');
    }
  },

  // Enhanced content request APIs
  listContentRequests: async (params = {}) => {
    try {
      const response = await api.get('/api/v1/content-requests', { params });
      return response;
    } catch (error) {
      throw new Error(error.response?.data?.detail || '获取内容请求列表失败');
    }
  },

  // Assign service provider
  assignService: async (requestId, providerId, notes = '') => {
    try {
      const response = await api.post(`/api/v1/content-requests/${requestId}/assign`, {
        provider_id: providerId,
        notes
      });
      return response;
    } catch (error) {
      throw new Error(error.response?.data?.detail || '分配服务失败');
    }
  },

  // Service order management
  listServiceOrders: async (params = {}) => {
    try {
      const response = await api.get('/api/v1/service-orders', { params });
      return response;
    } catch (error) {
      throw new Error(error.response?.data?.detail || '获取服务订单列表失败');
    }
  },

  getServiceOrder: async (orderId) => {
    try {
      const response = await api.get(`/api/v1/service-orders/${orderId}`);
      return response;
    } catch (error) {
      throw new Error(error.response?.data?.detail || '获取服务订单详情失败');
    }
  },

  updateWorkStatus: async (orderId, status, progress, notes) => {
    try {
      const response = await api.put(`/api/v1/service-orders/${orderId}/status`, {
        status,
        progress,
        notes
      });
      return response;
    } catch (error) {
      throw new Error(error.response?.data?.detail || '更新工作状态失败');
    }
  },

  // Delivery management
  listDeliveries: async (params = {}) => {
    try {
      const response = await api.get('/api/v1/deliveries', { params });
      return response;
    } catch (error) {
      throw new Error(error.response?.data?.detail || '获取交付列表失败');
    }
  },

  acceptDelivery: async (deliveryId, feedback = '') => {
    try {
      const response = await api.post(`/api/v1/deliveries/${deliveryId}/accept`, {
        feedback
      });
      return response;
    } catch (error) {
      throw new Error(error.response?.data?.detail || '接受交付失败');
    }
  },

  rejectDelivery: async (deliveryId, reason, requireRevision = false) => {
    try {
      const response = await api.post(`/api/v1/deliveries/${deliveryId}/reject`, {
        reason,
        require_revision: requireRevision
      });
      return response;
    } catch (error) {
      throw new Error(error.response?.data?.detail || '拒绝交付失败');
    }
  },

  // Statistics
  getContentStatistics: async (dateRange) => {
    try {
      const params = {
        start_date: dateRange?.startDate,
        end_date: dateRange?.endDate
      };
      const response = await api.get('/api/v1/content/statistics', { params });
      return response;
    } catch (error) {
      // Return mock data for development
      return {
        pending_reviews: 12,
        in_progress: 25,
        completed: 156,
        monthly_total: 193,
        acceptance_rate: 0.92,
        avg_delivery_time: '3.5天',
        provider_performance: [
          { provider_id: '1', name: '优质内容服务商', completed: 45, rating: 4.8 },
          { provider_id: '2', name: '快速交付团队', completed: 38, rating: 4.6 }
        ]
      };
    }
  },

  // 获取内容需求详情
  getContentRequestDetail: async (requestId) => {
    try {
      const response = await api.get(`/api/v1/content-requests/${requestId}`);
      return response;
    } catch (error) {
      throw new Error(error.response?.data?.detail || '获取需求详情失败');
    }
  },

  // 更新内容需求
  updateContentRequest: async (requestId, requestData) => {
    try {
      const response = await api.put(`/api/v1/content-requests/${requestId}`, requestData);
      return response;
    } catch (error) {
      throw new Error(error.response?.data?.detail || '更新需求失败');
    }
  },

  // 删除内容需求
  deleteContentRequest: async (requestId) => {
    try {
      const response = await api.delete(`/api/v1/content-requests/${requestId}`);
      return response;
    } catch (error) {
      throw new Error(error.response?.data?.detail || '删除需求失败');
    }
  },

  // 申请接单
  applyContentRequest: async (requestId, applyData) => {
    try {
      const response = await api.post(`/api/v1/content-requests/${requestId}/apply`, applyData);
      return response;
    } catch (error) {
      throw new Error(error.response?.data?.detail || '申请接单失败');
    }
  },

  // 内容交付
  deliverContent: async (requestId, deliveryData) => {
    try {
      const response = await api.post(`/api/v1/content-requests/${requestId}/delivery`, deliveryData);
      return response;
    } catch (error) {
      throw new Error(error.response?.data?.detail || '内容交付失败');
    }
  },

  // 查看交付内容详情
  getDeliveredContent: async (requestId) => {
    try {
      const response = await api.get(`/api/v1/content-requests/${requestId}/content`);
      return response;
    } catch (error) {
      throw new Error(error.response?.data?.detail || '获取交付内容失败');
    }
  },

  // 内容审核
  reviewContent: async (requestId, reviewData) => {
    try {
      const response = await api.post(`/api/v1/content-requests/${requestId}/review`, reviewData);
      return response;
    } catch (error) {
      throw new Error(error.response?.data?.detail || '内容审核失败');
    }
  },

  // 获取渠道列表（用于选择目标渠道）
  getChannels: async () => {
    try {
      const response = await api.get('/api/v1/channels');
      return response;
    } catch (error) {
      throw new Error(error.response?.data?.detail || '获取渠道列表失败');
    }
  },

  // 获取渠道分类列表
  getChannelCategories: async (params = {}) => {
    try {
      const response = await api.get('/api/v1/channel-categories/list', { params });
      return response;
    } catch (error) {
      throw new Error(error.response?.data?.detail || '获取渠道分类列表失败');
    }
  },

  // 获取指定父分类下的子分类
  getSubCategories: async (parentId, level = null) => {
    try {
      const params = { parent_id: parentId };
      if (level) {
        params.level = level;
      }
      const response = await api.get('/api/v1/channel-categories/list', { params });
      return response;
    } catch (error) {
      throw new Error(error.response?.data?.detail || '获取子分类列表失败');
    }
  },

  // 获取渠道分类（一级分类）
  getTopLevelCategories: async () => {
    try {
      const response = await api.get('/api/v1/channel-categories/list', {
        params: { category_level: 1, is_active: true }
      });
      return response;
    } catch (error) {
      throw new Error(error.response?.data?.detail || '获取渠道分类失败');
    }
  },

  // 获取服务分类（二级分类，指定渠道下的服务）
  getSecondaryCategories: async (parentId) => {
    try {
      const response = await api.get('/api/v1/channel-categories/list', {
        params: { parent_id: parentId, category_level: 2, is_active: true }
      });
      return response;
    } catch (error) {
      throw new Error(error.response?.data?.detail || '获取服务分类失败');
    }
  },

  // 获取渠道服务列表
  getChannelServices: async (params = {}) => {
    try {
      const response = await api.get('/api/v1/channel-services', { params });
      return response;
    } catch (error) {
      throw new Error(error.response?.data?.detail || '获取渠道服务列表失败');
    }
  },

  // 基于标签获取服务列表
  getServicesByTags: async (categoryId, tags) => {
    try {
      const params = {
        category_id: categoryId,
        tags: tags
      };
      const response = await api.get('/api/v1/channel-services', { params });
      return response;
    } catch (error) {
      throw new Error(error.response?.data?.detail || '获取服务列表失败');
    }
  },

  // 文件上传
  uploadFile: async (file) => {
    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await api.post('/api/v1/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response;
    } catch (error) {
      throw new Error(error.response?.data?.detail || '文件上传失败');
    }
  },

  // 获取可用渠道商列表（新的专门接口）
  getAvailableProviders: async (serviceId, filters = {}) => {
    try {
      const params = {
        service_id: serviceId,
        ...filters
      };

      // 处理标签数组
      if (filters.tags && Array.isArray(filters.tags)) {
        params.tags = filters.tags.join(',');
      }

      const response = await api.get('/api/v1/channels/available-providers', { params });
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('获取可用渠道商列表失败:', error);
      return {
        success: false,
        data: { items: [], pagination: {}, filters: {} },
        message: error.response?.data?.detail || '获取可用渠道商列表失败'
      };
    }
  },

  // 根据服务ID获取提供该服务的渠道商列表（保留旧方法作为备选）
  getChannelsByService: async (serviceId, params = {}) => {
    try {
      // 使用新的专门接口
      const filters = {};
      if (params.tags) {
        filters.tags = Array.isArray(params.tags) ? params.tags : [params.tags];
      }
      if (params.verification_status) {
        // 新接口默认只返回已验证的渠道商，无需额外筛选
      }
      if (params.is_active) {
        // 新接口默认只返回活跃的渠道商，无需额外筛选
      }

      return await contentService.getAvailableProviders(serviceId, filters);
    } catch (error) {
      console.error('获取渠道商列表失败:', error);
      return {
        success: false,
        data: { items: [] },
        message: error.response?.data?.detail || '获取渠道商列表失败'
      };
    }
  },
};

// 数据转换工具函数
export const dataTransformers = {
  // 将前端表单数据转换为后端API格式
  transformFormDataToApiRequest: (formData, requirementType, channelId) => {
    const baseData = {
      channel_id: channelId,
      request_type: requirementType === 'creation' ? 'create_content' : 'publish_content',
      request_title: formData.title,
      request_description: formData.requirements,
      tags: formData.keywords ? formData.keywords.split(',').map(tag => tag.trim()).filter(tag => tag) : [],
      deadline: new Date(formData.deadline).toISOString(),
    };

    // 根据需求类型添加特定字段
    if (requirementType === 'creation') {
      baseData.creation_requirements = formData.requirements;
    } else {
      // 投稿需求模式
      baseData.provided_content_title = formData.title;
      baseData.provided_content_text = formData.submissionGuidelines;
      baseData.provided_content_files = formData.attachments || [];
    }

    return baseData;
  },

  // 将后端API响应转换为前端显示格式
  transformApiResponseToDisplayData: (apiData) => {
    return {
      id: apiData.id,
      title: apiData.request_title,
      type: apiData.request_type === 'create_content' ? 'creation' : 'submission',
      category: apiData.category || '未分类',
      platform: apiData.platform || [],
      status: apiData.status,
      statusLabel: getStatusLabel(apiData.status),
      author: apiData.author || '',
      submittedCount: apiData.submitted_count || 0,
      deadline: apiData.deadline ? new Date(apiData.deadline).toISOString().split('T')[0] : '',
      price: apiData.price || 0,
      wordCount: apiData.word_count || '',
      createDate: apiData.created_at ? new Date(apiData.created_at).toISOString().split('T')[0] : '',
      progress: apiData.progress || 0,
      priority: apiData.priority || 'medium',
      tags: apiData.tags || [],
      requirements: apiData.request_description || '',
    };
  },
};

// 状态标签映射
const getStatusLabel = (status) => {
  const statusMap = {
    pending: '待接单',
    accepted: '已接单',
    in_progress: '进行中',
    reviewing: '审核中',
    completed: '已完成',
    cancelled: '已取消',
    rejected: '已拒绝',
  };
  return statusMap[status] || status;
};

export default { contentService, dataTransformers };
