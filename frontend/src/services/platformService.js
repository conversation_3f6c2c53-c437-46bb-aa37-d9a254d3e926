import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000/api/v1';

// Platform Configuration APIs
const platformService = {
  // ============= Platform Config Management =============
  
  // Get platform configurations list
  async getPlatformConfigs(params = {}) {
    try {
      const response = await axios.get(`${API_BASE_URL}/platforms/configs`, { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching platform configs:', error);
      throw error;
    }
  },

  // Get single platform config details
  async getPlatformConfig(platformCode) {
    try {
      const response = await axios.get(`${API_BASE_URL}/platforms/configs/${platformCode}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching platform config:', error);
      throw error;
    }
  },

  // Create new platform configuration
  async createPlatformConfig(configData) {
    try {
      const response = await axios.post(`${API_BASE_URL}/platforms/configs`, configData);
      return response.data;
    } catch (error) {
      console.error('Error creating platform config:', error);
      throw error;
    }
  },

  // Update platform configuration
  async updatePlatformConfig(platformCode, updateData) {
    try {
      const response = await axios.put(`${API_BASE_URL}/platforms/configs/${platformCode}`, updateData);
      return response.data;
    } catch (error) {
      console.error('Error updating platform config:', error);
      throw error;
    }
  },

  // ============= Credential Management =============
  
  // Get credentials list
  async getCredentialsList() {
    try {
      const response = await axios.get(`${API_BASE_URL}/platforms/credentials`);
      return response.data;
    } catch (error) {
      console.error('Error fetching credentials list:', error);
      throw error;
    }
  },

  // Get credential details
  async getCredential(platformCode) {
    try {
      const response = await axios.get(`${API_BASE_URL}/platforms/credentials/${platformCode}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching credential:', error);
      throw error;
    }
  },

  // Set platform credential
  async setCredential(platformCode, credentialData) {
    try {
      const response = await axios.post(`${API_BASE_URL}/platforms/credentials/${platformCode}`, credentialData);
      return response.data;
    } catch (error) {
      console.error('Error setting credential:', error);
      throw error;
    }
  },

  // Update platform credential
  async updateCredential(platformCode, updateData) {
    try {
      const response = await axios.put(`${API_BASE_URL}/platforms/credentials/${platformCode}`, updateData);
      return response.data;
    } catch (error) {
      console.error('Error updating credential:', error);
      throw error;
    }
  },

  // Test platform connection
  async testConnection(platformCode) {
    try {
      const response = await axios.post(`${API_BASE_URL}/platforms/credentials/${platformCode}/test`);
      return response.data;
    } catch (error) {
      console.error('Error testing connection:', error);
      throw error;
    }
  },

  // ============= Token Management =============
  
  // Get active token
  async getActiveToken(platformCode) {
    try {
      const response = await axios.get(`${API_BASE_URL}/platforms/tokens/${platformCode}/active`);
      return response.data;
    } catch (error) {
      console.error('Error fetching active token:', error);
      throw error;
    }
  },

  // Refresh platform token
  async refreshToken(platformCode, forceRefresh = false) {
    try {
      const response = await axios.post(`${API_BASE_URL}/platforms/tokens/${platformCode}/refresh`, {
        force_refresh: forceRefresh
      });
      return response.data;
    } catch (error) {
      console.error('Error refreshing token:', error);
      throw error;
    }
  },

  // Revoke platform token
  async revokeToken(platformCode, reason = '') {
    try {
      const response = await axios.post(`${API_BASE_URL}/platforms/tokens/${platformCode}/revoke`, {
        reason
      });
      return response.data;
    } catch (error) {
      console.error('Error revoking token:', error);
      throw error;
    }
  },

  // ============= Route Management =============
  
  // Get routes list
  async getRoutes(params = {}) {
    try {
      const response = await axios.get(`${API_BASE_URL}/platforms/routes`, { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching routes:', error);
      throw error;
    }
  },

  // Get route details
  async getRouteDetail(routeId) {
    try {
      const response = await axios.get(`${API_BASE_URL}/platforms/routes/${routeId}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching route detail:', error);
      throw error;
    }
  },

  // Create new route
  async createRoute(routeData) {
    try {
      const response = await axios.post(`${API_BASE_URL}/platforms/routes`, routeData);
      return response.data;
    } catch (error) {
      console.error('Error creating route:', error);
      throw error;
    }
  },

  // Retry failed route
  async retryRoute(routeId, maxRetries = 3, retryDelay = 1000) {
    try {
      const response = await axios.post(`${API_BASE_URL}/platforms/routes/${routeId}/retry`, {
        max_retries: maxRetries,
        retry_delay: retryDelay
      });
      return response.data;
    } catch (error) {
      console.error('Error retrying route:', error);
      throw error;
    }
  },

  // ============= Ruanwenjie (软文街) Special APIs =============
  
  // Get media resources from Ruanwenjie
  async getRuanwenjieMediaResources(params = {}) {
    try {
      const response = await axios.get(`${API_BASE_URL}/platforms/ruanwenjie/media`, { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching Ruanwenjie media resources:', error);
      throw error;
    }
  },

  // Sync Ruanwenjie orders
  async syncRuanwenjieOrders() {
    try {
      const response = await axios.post(`${API_BASE_URL}/platforms/ruanwenjie/sync-orders`);
      return response.data;
    } catch (error) {
      console.error('Error syncing Ruanwenjie orders:', error);
      throw error;
    }
  },

  // Get Ruanwenjie order status
  async getRuanwenjieOrderStatus(orderId) {
    try {
      const response = await axios.get(`${API_BASE_URL}/platforms/ruanwenjie/orders/${orderId}/status`);
      return response.data;
    } catch (error) {
      console.error('Error fetching Ruanwenjie order status:', error);
      throw error;
    }
  },

  // Compare Ruanwenjie prices
  async compareRuanwenjiePrices(mediaIds = []) {
    try {
      const response = await axios.post(`${API_BASE_URL}/platforms/ruanwenjie/compare-prices`, {
        media_ids: mediaIds
      });
      return response.data;
    } catch (error) {
      console.error('Error comparing Ruanwenjie prices:', error);
      throw error;
    }
  },

  // ============= Utility Functions =============
  
  // Get platform types enum
  getPlatformTypes() {
    return [
      { value: 'ai_search', label: 'AI搜索引擎' },
      { value: 'content_publish', label: '内容发布平台' },
      { value: 'media_resource', label: '媒体资源平台' },
      { value: 'analytics', label: '数据分析平台' },
      { value: 'payment', label: '支付平台' }
    ];
  },

  // Get connection statuses
  getConnectionStatuses() {
    return [
      { value: 'connected', label: '已连接', color: 'success' },
      { value: 'disconnected', label: '未连接', color: 'error' },
      { value: 'connecting', label: '连接中', color: 'warning' },
      { value: 'error', label: '连接错误', color: 'error' }
    ];
  },

  // Get route statuses
  getRouteStatuses() {
    return [
      { value: 'pending', label: '待处理', color: 'default' },
      { value: 'processing', label: '处理中', color: 'info' },
      { value: 'success', label: '成功', color: 'success' },
      { value: 'failed', label: '失败', color: 'error' },
      { value: 'retrying', label: '重试中', color: 'warning' }
    ];
  }
};

export default platformService;