import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000/api/v1';

const systemConfigService = {
  // ============= Payment Configuration =============
  
  // Get payment configuration
  async getPaymentConfig() {
    try {
      const response = await axios.get(`${API_BASE_URL}/system/config/payment`);
      return response.data;
    } catch (error) {
      // Return mock data for development
      return {
        payment_methods: [
          {
            id: 'alipay',
            name: '支付宝',
            enabled: true,
            icon: 'alipay',
            fee_rate: 0.6,
            fee_type: 'percentage',
            min_amount: 0.01,
            max_amount: 50000,
            daily_limit: 500000,
            config: {
              app_id: '202********00000',
              merchant_id: 'MERCHANT123',
              callback_url: 'https://api.example.com/callback/alipay'
            }
          },
          {
            id: 'wechat',
            name: '微信支付',
            enabled: true,
            icon: 'wechat',
            fee_rate: 0.6,
            fee_type: 'percentage',
            min_amount: 0.01,
            max_amount: 50000,
            daily_limit: 500000,
            config: {
              app_id: 'wx**********',
              mch_id: '**********',
              callback_url: 'https://api.example.com/callback/wechat'
            }
          },
          {
            id: 'bank_transfer',
            name: '银行转账',
            enabled: true,
            icon: 'bank',
            fee_rate: 5,
            fee_type: 'fixed',
            min_amount: 100,
            max_amount: 1000000,
            daily_limit: ********,
            config: {
              bank_name: '中国工商银行',
              account_number: '****************',
              account_name: '科技有限公司'
            }
          },
          {
            id: 'credit_card',
            name: '信用卡',
            enabled: false,
            icon: 'credit_card',
            fee_rate: 2.5,
            fee_type: 'percentage',
            min_amount: 1,
            max_amount: 100000,
            daily_limit: 1000000,
            config: {}
          }
        ],
        global_settings: {
          auto_confirm_threshold: 10000,
          manual_review_required: true,
          refund_enabled: true,
          refund_time_limit: 7,
          partial_refund_enabled: true,
          invoice_enabled: true,
          receipt_template: 'default'
        }
      };
    }
  },

  // Update payment configuration
  async updatePaymentConfig(config) {
    try {
      const response = await axios.put(`${API_BASE_URL}/system/config/payment`, config);
      return response.data;
    } catch (error) {
      console.error('Error updating payment config:', error);
      throw error;
    }
  },

  // ============= Subscription Configuration =============
  
  // Get subscription configuration
  async getSubscriptionConfig() {
    try {
      const response = await axios.get(`${API_BASE_URL}/system/config/subscription`);
      return response.data;
    } catch (error) {
      // Return mock data
      return {
        plans: [
          {
            id: 'free',
            name: '免费版',
            price: 0,
            billing_cycle: 'forever',
            features: ['基础功能', '5个项目', '社区支持'],
            limits: {
              projects: 5,
              users: 1,
              storage: 1024,
              api_calls: 1000
            },
            is_default: true,
            is_active: true,
            trial_days: 0
          },
          {
            id: 'basic',
            name: '基础版',
            price: 99,
            billing_cycle: 'monthly',
            features: ['所有免费功能', '20个项目', '邮件支持', '基础分析'],
            limits: {
              projects: 20,
              users: 5,
              storage: 10240,
              api_calls: 10000
            },
            is_default: false,
            is_active: true,
            trial_days: 7
          },
          {
            id: 'pro',
            name: '专业版',
            price: 299,
            billing_cycle: 'monthly',
            features: ['所有基础功能', '无限项目', '优先支持', '高级分析', 'API访问'],
            limits: {
              projects: -1,
              users: 20,
              storage: 102400,
              api_calls: 100000
            },
            is_default: false,
            is_active: true,
            trial_days: 14
          },
          {
            id: 'enterprise',
            name: '企业版',
            price: 999,
            billing_cycle: 'monthly',
            features: ['所有功能', '定制开发', '专属支持', 'SLA保障', '培训服务'],
            limits: {
              projects: -1,
              users: -1,
              storage: -1,
              api_calls: -1
            },
            is_default: false,
            is_active: true,
            trial_days: 30
          }
        ],
        settings: {
          trial_enabled: true,
          default_trial_days: 14,
          auto_renew_enabled: true,
          grace_period_days: 3,
          downgrade_enabled: true,
          upgrade_proration: true,
          cancel_at_period_end: true,
          require_payment_method: false
        },
        discounts: [
          {
            id: 'annual',
            name: '年付优惠',
            type: 'percentage',
            value: 20,
            applicable_plans: ['basic', 'pro', 'enterprise'],
            conditions: {
              min_billing_cycle: 'yearly'
            }
          },
          {
            id: 'early_bird',
            name: '早鸟优惠',
            type: 'percentage',
            value: 30,
            applicable_plans: ['pro'],
            conditions: {
              valid_until: '2024-12-31'
            }
          }
        ]
      };
    }
  },

  // Update subscription configuration
  async updateSubscriptionConfig(config) {
    try {
      const response = await axios.put(`${API_BASE_URL}/system/config/subscription`, config);
      return response.data;
    } catch (error) {
      console.error('Error updating subscription config:', error);
      throw error;
    }
  },

  // ============= Notification Configuration =============
  
  // Get notification configuration
  async getNotificationConfig() {
    try {
      const response = await axios.get(`${API_BASE_URL}/system/config/notification`);
      return response.data;
    } catch (error) {
      // Return mock data
      return {
        email: {
          enabled: true,
          provider: 'smtp',
          config: {
            host: 'smtp.example.com',
            port: 587,
            secure: true,
            auth_user: '<EMAIL>',
            from_name: 'AI SEO Platform',
            from_email: '<EMAIL>'
          },
          templates: [
            {
              id: 'welcome',
              name: '欢迎邮件',
              subject: '欢迎加入 {{company_name}}',
              body: '<h1>欢迎 {{user_name}}</h1><p>感谢您的注册...</p>',
              variables: ['user_name', 'company_name', 'activation_link'],
              enabled: true
            },
            {
              id: 'password_reset',
              name: '密码重置',
              subject: '重置您的密码',
              body: '<p>点击链接重置密码: {{reset_link}}</p>',
              variables: ['user_name', 'reset_link', 'expire_time'],
              enabled: true
            },
            {
              id: 'order_confirmation',
              name: '订单确认',
              subject: '订单确认 #{{order_id}}',
              body: '<p>您的订单已确认...</p>',
              variables: ['order_id', 'amount', 'items'],
              enabled: true
            }
          ]
        },
        sms: {
          enabled: true,
          provider: 'aliyun',
          config: {
            access_key: 'LTAI5t*********',
            region: 'cn-shanghai',
            sign_name: 'AI平台',
            daily_limit: 1000
          },
          templates: [
            {
              id: 'verification',
              name: '验证码',
              template_code: 'SMS_123456',
              content: '您的验证码是{{code}}，5分钟内有效',
              variables: ['code'],
              enabled: true
            },
            {
              id: 'order_status',
              name: '订单状态',
              template_code: 'SMS_123457',
              content: '您的订单{{order_id}}状态已更新为{{status}}',
              variables: ['order_id', 'status'],
              enabled: true
            }
          ]
        },
        push: {
          enabled: false,
          provider: 'firebase',
          config: {
            server_key: '',
            sender_id: ''
          }
        },
        webhook: {
          enabled: true,
          endpoints: [
            {
              id: 'slack',
              name: 'Slack通知',
              url: 'https://hooks.slack.com/services/xxx',
              events: ['order.created', 'order.completed', 'user.registered'],
              enabled: true
            },
            {
              id: 'dingtalk',
              name: '钉钉通知',
              url: 'https://oapi.dingtalk.com/robot/send',
              events: ['system.alert', 'payment.failed'],
              enabled: false
            }
          ]
        }
      };
    }
  },

  // Update notification configuration
  async updateNotificationConfig(config) {
    try {
      const response = await axios.put(`${API_BASE_URL}/system/config/notification`, config);
      return response.data;
    } catch (error) {
      console.error('Error updating notification config:', error);
      throw error;
    }
  },

  // ============= Business Rules Configuration =============
  
  // Get business rules
  async getBusinessRules() {
    try {
      const response = await axios.get(`${API_BASE_URL}/system/config/business-rules`);
      return response.data;
    } catch (error) {
      // Return mock data
      return {
        refund: {
          enabled: true,
          auto_approve_threshold: 100,
          time_limit_days: 7,
          reasons: [
            { id: 'quality', name: '质量问题', auto_approve: false },
            { id: 'not_delivered', name: '未交付', auto_approve: true },
            { id: 'duplicate', name: '重复购买', auto_approve: true },
            { id: 'other', name: '其他', auto_approve: false }
          ],
          deduction_rules: [
            { days: 1, rate: 0 },
            { days: 3, rate: 10 },
            { days: 7, rate: 20 },
            { days: 30, rate: 50 }
          ]
        },
        upgrade: {
          enabled: true,
          immediate_upgrade: true,
          proration_enabled: true,
          downgrade_at_period_end: true,
          rules: [
            {
              from_plan: 'free',
              to_plan: 'basic',
              allowed: true,
              requirements: []
            },
            {
              from_plan: 'basic',
              to_plan: 'pro',
              allowed: true,
              requirements: ['verified_email', 'payment_method']
            },
            {
              from_plan: 'pro',
              to_plan: 'enterprise',
              allowed: true,
              requirements: ['verified_company', 'contract_signed']
            }
          ]
        },
        commission: {
          enabled: true,
          tiers: [
            { min: 0, max: 10000, rate: 10 },
            { min: 10001, max: 50000, rate: 15 },
            { min: 50001, max: 100000, rate: 20 },
            { min: 100001, max: null, rate: 25 }
          ],
          special_rates: [
            { partner_type: 'gold', rate: 30 },
            { partner_type: 'silver', rate: 25 },
            { partner_type: 'bronze', rate: 20 }
          ]
        },
        content: {
          auto_review_enabled: false,
          quality_threshold: 80,
          plagiarism_check: true,
          max_revision_count: 3,
          delivery_time_buffer_hours: 24,
          late_penalty_rate: 5
        },
        user: {
          registration_approval: false,
          email_verification_required: true,
          phone_verification_required: false,
          password_policy: {
            min_length: 8,
            require_uppercase: true,
            require_lowercase: true,
            require_number: true,
            require_special: false,
            expire_days: 90
          },
          session_timeout_minutes: 30,
          max_login_attempts: 5,
          lockout_duration_minutes: 30
        }
      };
    }
  },

  // Update business rules
  async updateBusinessRules(rules) {
    try {
      const response = await axios.put(`${API_BASE_URL}/system/config/business-rules`, rules);
      return response.data;
    } catch (error) {
      console.error('Error updating business rules:', error);
      throw error;
    }
  },

  // ============= Webhook Configuration =============
  
  // Get webhook configuration
  async getWebhookConfig() {
    try {
      const response = await axios.get(`${API_BASE_URL}/system/config/webhooks`);
      return response.data;
    } catch (error) {
      // Return mock data
      return {
        endpoints: [
          {
            id: '1',
            name: '订单事件',
            url: 'https://api.partner.com/webhooks/orders',
            secret: 'whsec_***************',
            events: ['order.created', 'order.updated', 'order.completed', 'order.cancelled'],
            active: true,
            retry_enabled: true,
            max_retries: 3,
            last_triggered: '2024-04-10 15:30:00',
            success_rate: 98.5
          },
          {
            id: '2',
            name: '用户事件',
            url: 'https://api.partner.com/webhooks/users',
            secret: 'whsec_***************',
            events: ['user.created', 'user.updated', 'user.deleted'],
            active: true,
            retry_enabled: true,
            max_retries: 5,
            last_triggered: '2024-04-10 14:20:00',
            success_rate: 99.2
          },
          {
            id: '3',
            name: '支付事件',
            url: 'https://api.accounting.com/webhooks',
            secret: 'whsec_***************',
            events: ['payment.succeeded', 'payment.failed', 'refund.created'],
            active: false,
            retry_enabled: false,
            max_retries: 3,
            last_triggered: '2024-04-09 10:15:00',
            success_rate: 95.0
          }
        ],
        available_events: [
          { category: '订单', events: ['order.created', 'order.updated', 'order.completed', 'order.cancelled'] },
          { category: '用户', events: ['user.created', 'user.updated', 'user.deleted', 'user.verified'] },
          { category: '支付', events: ['payment.succeeded', 'payment.failed', 'payment.pending'] },
          { category: '退款', events: ['refund.created', 'refund.approved', 'refund.rejected'] },
          { category: '内容', events: ['content.created', 'content.delivered', 'content.approved'] },
          { category: '系统', events: ['system.maintenance', 'system.alert', 'system.update'] }
        ],
        global_settings: {
          signing_secret: 'whsec_global_***************',
          timeout_seconds: 30,
          retry_intervals: [1, 5, 15, 60],
          max_payload_size: 5242880,
          include_headers: ['X-Event-Type', 'X-Event-ID', 'X-Timestamp', 'X-Signature']
        }
      };
    }
  },

  // Update webhook configuration
  async updateWebhookConfig(config) {
    try {
      const response = await axios.put(`${API_BASE_URL}/system/config/webhooks`, config);
      return response.data;
    } catch (error) {
      console.error('Error updating webhook config:', error);
      throw error;
    }
  },

  // Test webhook endpoint
  async testWebhook(webhookId) {
    try {
      const response = await axios.post(`${API_BASE_URL}/system/config/webhooks/${webhookId}/test`);
      return response.data;
    } catch (error) {
      console.error('Error testing webhook:', error);
      throw error;
    }
  },

  // ============= System Backup & Restore =============
  
  // Export all configurations
  async exportConfig() {
    try {
      const response = await axios.get(`${API_BASE_URL}/system/config/export`, {
        responseType: 'blob'
      });
      return response.data;
    } catch (error) {
      console.error('Error exporting config:', error);
      throw error;
    }
  },

  // Import configurations
  async importConfig(configFile) {
    try {
      const formData = new FormData();
      formData.append('config', configFile);
      const response = await axios.post(`${API_BASE_URL}/system/config/import`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error importing config:', error);
      throw error;
    }
  },

  // Get configuration history
  async getConfigHistory() {
    try {
      const response = await axios.get(`${API_BASE_URL}/system/config/history`);
      return response.data;
    } catch (error) {
      // Return mock data
      return [
        {
          id: '1',
          timestamp: '2024-04-10 15:00:00',
          user: '<EMAIL>',
          changes: ['Payment configuration updated', 'Alipay fee rate changed from 0.5% to 0.6%'],
          version: '1.2.3'
        },
        {
          id: '2',
          timestamp: '2024-04-09 10:30:00',
          user: '<EMAIL>',
          changes: ['Subscription plans updated', 'Enterprise plan price changed'],
          version: '1.2.2'
        },
        {
          id: '3',
          timestamp: '2024-04-08 14:20:00',
          user: '<EMAIL>',
          changes: ['Email templates updated', 'Welcome email template modified'],
          version: '1.2.1'
        }
      ];
    }
  }
};

export default systemConfigService;