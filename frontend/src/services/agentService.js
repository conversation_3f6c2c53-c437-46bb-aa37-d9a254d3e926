import ApiService from './api';

class AgentService {
  /**
   * 获取代理商信息
   * @returns {Promise<Object>} 代理商信息
   */
  async getAgentInfo() {
    try {
      console.log('获取代理商信息...');
      const response = await ApiService.get('/agents/me');
      console.log('代理商信息API响应:', response);
      return response;
    } catch (error) {
      console.error('获取代理商信息失败:', error);
      throw error;
    }
  }

  /**
   * 更新代理商信息
   * @param {Object} agentData 代理商信息数据
   * @returns {Promise<Object>} 更新结果
   */
  async updateAgentInfo(agentData) {
    try {
      console.log('更新代理商信息...', agentData);
      const response = await ApiService.put('/agents/me', agentData);
      console.log('更新代理商信息API响应:', response);
      return response;
    } catch (error) {
      console.error('更新代理商信息失败:', error);
      throw error;
    }
  }

  /**
   * 获取代理商统计数据
   * @returns {Promise<Object>} 统计数据
   */
  async getAgentStats() {
    try {
      console.log('获取代理商统计数据...');
      const response = await ApiService.get('/agents/stats');
      console.log('代理商统计数据API响应:', response);
      return response;
    } catch (error) {
      console.error('获取代理商统计数据失败:', error);
      // 返回默认统计数据
      return {
        success: true,
        data: {
          totalCommission: 0,
          monthlyCommission: 0,
          totalReferrals: 0,
          activeReferrals: 0,
          conversionRate: 0,
          monthlyGrowth: 0
        }
      };
    }
  }

  /**
   * 获取推广链接
   * @returns {Promise<Object>} 推广链接信息
   */
  async getPromotionLinks() {
    try {
      console.log('获取推广链接...');
      const response = await ApiService.get('/agents/promotion-links');
      console.log('推广链接API响应:', response);
      return response;
    } catch (error) {
      console.error('获取推广链接失败:', error);
      throw error;
    }
  }

  /**
   * 创建推广链接
   * @param {Object} linkData 推广链接数据
   * @returns {Promise<Object>} 创建结果
   */
  async createPromotionLink(linkData) {
    try {
      console.log('创建推广链接...', linkData);
      const response = await ApiService.post('/agents/promotion-links', linkData);
      console.log('创建推广链接API响应:', response);
      return response;
    } catch (error) {
      console.error('创建推广链接失败:', error);
      throw error;
    }
  }

  /**
   * 获取佣金记录
   * @param {Object} params 查询参数
   * @returns {Promise<Object>} 佣金记录
   */
  async getCommissionRecords(params = {}) {
    try {
      console.log('获取佣金记录...', params);
      const response = await ApiService.get('/agents/commissions', { params });
      console.log('佣金记录API响应:', response);
      return response;
    } catch (error) {
      console.error('获取佣金记录失败:', error);
      // 返回默认数据
      return {
        success: true,
        data: {
          records: [],
          pagination: {
            page: 1,
            size: 20,
            total: 0,
            pages: 1
          }
        }
      };
    }
  }
}

export default new AgentService();
