import ApiService from './api';

class TemplateService {
  /**
   * 获取模板列表
   * @param {Object} params - 查询参数
   * @param {number} params.page - 页码
   * @param {number} params.size - 每页数量
   * @param {string} params.service_type - 服务类型筛选
   * @param {string} params.category - 分类筛选
   * @param {boolean} params.is_active - 状态筛选
   * @param {boolean} params.is_public - 公开状态筛选
   * @param {string} params.keyword - 关键词搜索
   * @param {string} params.sort_by - 排序字段
   * @param {string} params.sort_order - 排序方式
   * @returns {Promise<Object>} 模板列表响应
   */
  async getTemplateList(params = {}) {
    try {
      const queryParams = new URLSearchParams();
      
      // 添加查询参数
      Object.entries(params).forEach(([key, value]) => {
        if (value !== null && value !== undefined && value !== '') {
          queryParams.append(key, value);
        }
      });

      const endpoint = `/ai/templates${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
      const response = await ApiService.get(endpoint);
      
      return response;
    } catch (error) {
      console.error('获取模板列表失败:', error);
      throw error;
    }
  }

  /**
   * 获取模板详情
   * @param {string} templateId - 模板ID
   * @returns {Promise<Object>} 模板详情响应
   */
  async getTemplateDetail(templateId) {
    try {
      const response = await ApiService.get(`/ai/templates/${templateId}`);
      return response;
    } catch (error) {
      console.error('获取模板详情失败:', error);
      throw error;
    }
  }

  /**
   * 创建模板
   * @param {Object} templateData - 模板数据
   * @returns {Promise<Object>} 创建响应
   */
  async createTemplate(templateData) {
    try {
      const response = await ApiService.post('/ai/templates', templateData);
      return response;
    } catch (error) {
      console.error('创建模板失败:', error);
      throw error;
    }
  }

  /**
   * 更新模板
   * @param {string} templateId - 模板ID
   * @param {Object} templateData - 更新数据
   * @returns {Promise<Object>} 更新响应
   */
  async updateTemplate(templateId, templateData) {
    try {
      const response = await ApiService.put(`/ai/templates/${templateId}`, templateData);
      return response;
    } catch (error) {
      console.error('更新模板失败:', error);
      throw error;
    }
  }

  /**
   * 删除模板
   * @param {string} templateId - 模板ID
   * @returns {Promise<Object>} 删除响应
   */
  async deleteTemplate(templateId) {
    try {
      const response = await ApiService.delete(`/ai/templates/${templateId}`);
      return response;
    } catch (error) {
      console.error('删除模板失败:', error);
      throw error;
    }
  }

  /**
   * 获取模板分类统计
   * @returns {Promise<Object>} 分类统计响应
   */
  async getTemplateCategories() {
    try {
      // 获取所有模板并统计分类
      const response = await this.getTemplateList({ size: 1000 });
      
      if (response.success && response.data) {
        const templates = response.data.items || [];
        const categoryStats = {};
        
        // 统计各分类的数量
        templates.forEach(template => {
          const category = template.category || 'uncategorized';
          categoryStats[category] = (categoryStats[category] || 0) + 1;
        });

        // 构建分类列表
        const categories = [
          { id: 'all', name: '全部模板', count: templates.length },
          { id: 'marketing', name: '营销文案', count: categoryStats.marketing || 0 },
          { id: 'content', name: '内容创作', count: categoryStats.content || 0 },
          { id: 'business', name: '商务文档', count: categoryStats.business || 0 },
          { id: 'technical', name: '技术文档', count: categoryStats.technical || 0 },
          { id: 'seo', name: 'SEO优化', count: categoryStats.seo || 0 },
          { id: 'social', name: '社交媒体', count: categoryStats.social || 0 },
          { id: 'email', name: '邮件模板', count: categoryStats.email || 0 },
        ];

        return {
          success: true,
          data: categories
        };
      }
      
      return {
        success: false,
        data: []
      };
    } catch (error) {
      console.error('获取模板分类失败:', error);
      return {
        success: false,
        data: []
      };
    }
  }

  /**
   * 搜索模板
   * @param {string} keyword - 搜索关键词
   * @param {Object} filters - 其他筛选条件
   * @returns {Promise<Object>} 搜索结果
   */
  async searchTemplates(keyword, filters = {}) {
    try {
      const params = {
        keyword,
        ...filters,
        page: 1,
        size: 50
      };
      
      return await this.getTemplateList(params);
    } catch (error) {
      console.error('搜索模板失败:', error);
      throw error;
    }
  }

  /**
   * 获取用户的模板使用统计
   * @returns {Promise<Object>} 使用统计响应
   */
  async getTemplateUsageStats() {
    try {
      // 这里可以调用专门的统计接口，目前使用模板列表来模拟
      const response = await this.getTemplateList({ size: 1000 });
      
      if (response.success && response.data) {
        const templates = response.data.items || [];
        const stats = {
          totalTemplates: templates.length,
          activeTemplates: templates.filter(t => t.is_active).length,
          publicTemplates: templates.filter(t => t.is_public).length,
          totalUsage: templates.reduce((sum, t) => sum + (t.usage_count || 0), 0),
          avgSuccessRate: templates.length > 0 
            ? templates.reduce((sum, t) => sum + (parseFloat(t.success_rate) || 0), 0) / templates.length 
            : 0
        };

        return {
          success: true,
          data: stats
        };
      }
      
      return {
        success: false,
        data: null
      };
    } catch (error) {
      console.error('获取模板使用统计失败:', error);
      throw error;
    }
  }

  /**
   * 复制模板
   * @param {string} templateId - 要复制的模板ID
   * @param {string} newName - 新模板名称
   * @returns {Promise<Object>} 复制响应
   */
  async duplicateTemplate(templateId, newName) {
    try {
      // 先获取原模板详情
      const originalResponse = await this.getTemplateDetail(templateId);
      
      if (originalResponse.success && originalResponse.data) {
        const original = originalResponse.data;
        
        // 创建新模板
        const newTemplateData = {
          template_name: newName || `${original.template_name} (副本)`,
          template_description: original.template_description,
          service_type: original.service_type,
          category: original.category,
          prompt_template: original.prompt_template,
          default_parameters: original.default_parameters,
          is_public: false // 复制的模板默认为私有
        };
        
        return await this.createTemplate(newTemplateData);
      }
      
      throw new Error('无法获取原模板信息');
    } catch (error) {
      console.error('复制模板失败:', error);
      throw error;
    }
  }
}

// 创建并导出服务实例
const templateService = new TemplateService();
export default templateService;
