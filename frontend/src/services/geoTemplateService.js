/**
 * GEO监控AI模板系统 - 前端模板服务
 * 提供标准模板和用户自定义模板的API调用封装
 */

// API基础配置
const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:8000';
const TEMPLATE_API_PREFIX = '/api/v1/geo/templates';

/**
 * 获取认证token
 */
function getAuthToken() {
    // 从localStorage或其他地方获取token
    return localStorage.getItem('auth_token') || '';
}

/**
 * 创建带认证头的fetch配置
 */
function createAuthHeaders(additionalHeaders = {}) {
    const headers = {
        'Content-Type': 'application/json',
        ...additionalHeaders
    };
    
    const token = getAuthToken();
    if (token) {
        headers['Authorization'] = `Bearer ${token}`;
    }
    
    return headers;
}

/**
 * 处理API响应
 */
async function handleApiResponse(response) {
    if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || `HTTP ${response.status}: ${response.statusText}`);
    }
    
    const contentType = response.headers.get('content-type');
    if (contentType && contentType.includes('application/json')) {
        return response.json();
    } else {
        return response.text();
    }
}

/**
 * GEO模板服务类
 */
class GeoTemplateService {
    
    // ==================== 标准模板相关方法 ====================
    
    /**
     * 获取标准模板列表
     * @param {string} category - 模板分类过滤
     * @returns {Promise<Array>} 标准模板列表
     */
    async getStandardTemplates(category = null) {
        try {
            const url = new URL(`${API_BASE_URL}${TEMPLATE_API_PREFIX}/standard`);
            if (category) {
                url.searchParams.append('category', category);
            }
            
            const response = await fetch(url.toString(), {
                method: 'GET',
                headers: createAuthHeaders()
            });
            
            const result = await handleApiResponse(response);
            return result.data || [];
        } catch (error) {
            console.error('获取标准模板列表失败:', error);
            throw error;
        }
    }
    
    /**
     * 获取标准模板详情
     * @param {string} templateKey - 模板唯一标识
     * @returns {Promise<Object>} 模板详情
     */
    async getStandardTemplateDetail(templateKey) {
        try {
            const response = await fetch(`${API_BASE_URL}${TEMPLATE_API_PREFIX}/standard/${templateKey}`, {
                method: 'GET',
                headers: createAuthHeaders()
            });
            
            const result = await handleApiResponse(response);
            return result.data;
        } catch (error) {
            console.error('获取标准模板详情失败:', error);
            throw error;
        }
    }
    
    /**
     * 渲染标准模板 - 返回完整HTML页面
     * @param {string} templateKey - 模板唯一标识
     * @param {Object} variables - 渲染变量
     * @returns {Promise<string>} 渲染后的HTML内容
     */
    async renderStandardTemplate(templateKey, variables = {}) {
        try {
            const url = new URL(`${API_BASE_URL}${TEMPLATE_API_PREFIX}/standard/${templateKey}/render`);
            if (Object.keys(variables).length > 0) {
                url.searchParams.append('variables', JSON.stringify(variables));
            }
            
            const response = await fetch(url.toString(), {
                method: 'GET',
                headers: createAuthHeaders()
            });
            
            return await handleApiResponse(response); // 返回HTML字符串
        } catch (error) {
            console.error('渲染标准模板失败:', error);
            throw error;
        }
    }
    
    // ==================== 用户自定义模板相关方法 ====================
    
    /**
     * 获取用户自定义模板列表
     * @param {boolean} includePublic - 是否包含公开模板
     * @returns {Promise<Array>} 用户模板列表
     */
    async getUserTemplates(includePublic = false) {
        try {
            const url = new URL(`${API_BASE_URL}${TEMPLATE_API_PREFIX}/custom`);
            if (includePublic) {
                url.searchParams.append('include_public', 'true');
            }
            
            const response = await fetch(url.toString(), {
                method: 'GET',
                headers: createAuthHeaders()
            });
            
            const result = await handleApiResponse(response);
            return result.data || [];
        } catch (error) {
            console.error('获取用户模板列表失败:', error);
            throw error;
        }
    }
    
    /**
     * 获取用户模板详情
     * @param {number} templateId - 模板ID
     * @returns {Promise<Object>} 模板详情
     */
    async getUserTemplateDetail(templateId) {
        try {
            const response = await fetch(`${API_BASE_URL}${TEMPLATE_API_PREFIX}/custom/${templateId}`, {
                method: 'GET',
                headers: createAuthHeaders()
            });
            
            const result = await handleApiResponse(response);
            return result.data;
        } catch (error) {
            console.error('获取用户模板详情失败:', error);
            throw error;
        }
    }
    
    /**
     * 渲染用户自定义模板 - 返回完整HTML页面
     * @param {number} templateId - 模板ID
     * @param {Object} variables - 渲染变量
     * @returns {Promise<string>} 渲染后的HTML内容
     */
    async renderUserTemplate(templateId, variables = {}) {
        try {
            const url = new URL(`${API_BASE_URL}${TEMPLATE_API_PREFIX}/custom/${templateId}/render`);
            if (Object.keys(variables).length > 0) {
                url.searchParams.append('variables', JSON.stringify(variables));
            }
            
            const response = await fetch(url.toString(), {
                method: 'GET',
                headers: createAuthHeaders()
            });
            
            return await handleApiResponse(response); // 返回HTML字符串
        } catch (error) {
            console.error('渲染用户模板失败:', error);
            throw error;
        }
    }
    
    /**
     * 创建自定义模板
     * @param {Object} templateData - 模板数据
     * @returns {Promise<Object>} 创建结果
     */
    async createCustomTemplate(templateData) {
        try {
            const response = await fetch(`${API_BASE_URL}${TEMPLATE_API_PREFIX}/custom`, {
                method: 'POST',
                headers: createAuthHeaders(),
                body: JSON.stringify(templateData)
            });
            
            const result = await handleApiResponse(response);
            return result;
        } catch (error) {
            console.error('创建自定义模板失败:', error);
            throw error;
        }
    }
    
    /**
     * 更新自定义模板
     * @param {number} templateId - 模板ID
     * @param {Object} templateData - 更新数据
     * @returns {Promise<Object>} 更新结果
     */
    async updateCustomTemplate(templateId, templateData) {
        try {
            const response = await fetch(`${API_BASE_URL}${TEMPLATE_API_PREFIX}/custom/${templateId}`, {
                method: 'PUT',
                headers: createAuthHeaders(),
                body: JSON.stringify(templateData)
            });
            
            const result = await handleApiResponse(response);
            return result;
        } catch (error) {
            console.error('更新自定义模板失败:', error);
            throw error;
        }
    }
    
    /**
     * 删除自定义模板
     * @param {number} templateId - 模板ID
     * @returns {Promise<Object>} 删除结果
     */
    async deleteCustomTemplate(templateId) {
        try {
            const response = await fetch(`${API_BASE_URL}${TEMPLATE_API_PREFIX}/custom/${templateId}`, {
                method: 'DELETE',
                headers: createAuthHeaders()
            });
            
            const result = await handleApiResponse(response);
            return result;
        } catch (error) {
            console.error('删除自定义模板失败:', error);
            throw error;
        }
    }
    
    /**
     * 收藏标准模板为自定义模板
     * @param {string} templateKey - 标准模板key
     * @param {string} customName - 自定义名称
     * @param {string} description - 描述
     * @returns {Promise<Object>} 收藏结果
     */
    async favoriteTemplate(templateKey, customName, description = '') {
        try {
            const response = await fetch(`${API_BASE_URL}${TEMPLATE_API_PREFIX}/custom/favorite`, {
                method: 'POST',
                headers: createAuthHeaders(),
                body: JSON.stringify({
                    source_template_key: templateKey,
                    template_name: customName,
                    description: description
                })
            });
            
            const result = await handleApiResponse(response);
            return result;
        } catch (error) {
            console.error('收藏模板失败:', error);
            throw error;
        }
    }
    
    // ==================== 工具方法 ====================
    
    /**
     * 验证HTML内容
     * @param {string} htmlContent - HTML内容
     * @returns {Object} 验证结果 {isValid: boolean, message: string}
     */
    validateHtmlContent(htmlContent) {
        if (!htmlContent || !htmlContent.trim()) {
            return { isValid: false, message: 'HTML内容不能为空' };
        }
        
        // 检查基本HTML结构
        if (!htmlContent.includes('<!DOCTYPE html>')) {
            return { isValid: false, message: 'HTML内容必须包含DOCTYPE声明' };
        }
        
        if (!htmlContent.includes('<html')) {
            return { isValid: false, message: 'HTML内容必须包含html标签' };
        }
        
        // 检查文件大小 (5MB限制)
        const sizeInBytes = new Blob([htmlContent]).size;
        if (sizeInBytes > 5 * 1024 * 1024) {
            return { isValid: false, message: 'HTML文件大小不能超过5MB' };
        }
        
        return { isValid: true, message: '验证通过' };
    }
    
    /**
     * 格式化文件大小
     * @param {number} bytes - 字节数
     * @returns {string} 格式化后的大小
     */
    formatFileSize(bytes) {
        if (!bytes) return '0 B';
        
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    /**
     * 生成默认变量
     * @param {string} templateTitle - 模板标题
     * @returns {Object} 默认变量对象
     */
    generateDefaultVariables(templateTitle = '') {
        return {
            title: templateTitle || 'GEO监控报告',
            timestamp: new Date().toLocaleString('zh-CN'),
            keyword: '示例关键词',
            ranking: '3',
            searchVolume: '12,500',
            difficulty: '中等',
            ctr: '4.8'
        };
    }
}

// 创建单例实例
const geoTemplateService = new GeoTemplateService();

// 导出服务实例和类
export default geoTemplateService;
export { GeoTemplateService };
