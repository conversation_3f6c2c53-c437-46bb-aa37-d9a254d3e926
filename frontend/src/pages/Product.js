import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Button,
  Paper,
  Chip,
  Avatar,
  Stack,
  IconButton,
  LinearProgress,
  Fade,
  Zoom,
  Slide,
  useScrollTrigger,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Collapse,
  Tab,
  Tabs,
} from '@mui/material';
import {
  TrendingUp,
  AutoFixHigh,
  Psychology,
  Analytics,
  CheckCircle,
  ArrowForward,
  Dashboard,
  MonetizationOn,
  Timeline,
  Hub,
  Language,
  Rocket,
  EmojiObjects,
  Architecture,
  Speed,
  Security,
  CloudSync,
  Api,
  Insights,
  Public,
  Storage,
  Memory,
  NetworkCheck,
  Biotech,
  SmartToy,
  AutoAwesome,
  ElectricBolt,
  NavigateNext,
  KeyboardArrowDown,
  Groups,
  TipsAndUpdates,
  WorkspacePremium,
  VerifiedUser,
  QuestionAnswer,
  Search,
  Psychology as PsychologyIcon,
  DataArray,
  Loop,
  ThumbUp,
  AccessTime,
  AccountTree,
  BusinessCenter,
  Create,
  Share,
  Gavel,
  Shield,
  CreditCard,
  AccountBalance,
  AttachMoney,
  ExpandMore,
  School,
  Lightbulb,
  CompareArrows,
  FilterAlt,
  VerifiedUserOutlined,
  TrendingUpOutlined,
  AccountBalanceWallet,
  SyncAlt,
  Handshake,
  Warning,
  CheckCircleOutline,
  RadioButtonUnchecked,
  RadioButtonChecked,
  ArrowRightAlt,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useCustomerService } from '../contexts/CustomerServiceContext';
import Footer from '../components/layout/Footer';

function Product() {
  const navigate = useNavigate();
  const { openChat } = useCustomerService();
  const [activeTab, setActiveTab] = useState(0);
  const [expandedSection, setExpandedSection] = useState(null);
  const [scrollProgress, setScrollProgress] = useState(0);
  
  const trigger = useScrollTrigger({
    disableHysteresis: true,
    threshold: 100,
  });

  useEffect(() => {
    const handleScroll = () => {
      const scrolled = window.scrollY;
      const maxScroll = document.documentElement.scrollHeight - window.innerHeight;
      const progress = (scrolled / maxScroll) * 100;
      setScrollProgress(progress);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const handleGetStarted = () => {
    navigate('/auth/register');
  };

  const handleContactSales = () => {
    openChat();
  };

  const handleExpandSection = (section) => {
    setExpandedSection(expandedSection === section ? null : section);
  };

  // AI搜索工作原理步骤
  const aiSearchSteps = [
    { icon: <QuestionAnswer />, title: '用户提问', desc: '以自然语言形式提出问题' },
    { icon: <Psychology />, title: '语义理解', desc: 'AI理解问题的真实意图和上下文' },
    { icon: <Search />, title: '知识检索', desc: '从训练数据和实时内容中检索信息' },
    { icon: <AutoAwesome />, title: '答案生成', desc: '综合多源信息生成针对性回答' },
    { icon: <Loop />, title: '对话延续', desc: '支持多轮对话深入探讨' },
  ];

  // 内容依赖性维度
  const contentDimensions = [
    { 
      icon: <VerifiedUser />, 
      title: '内容来源权重', 
      desc: '知名平台、认证账户的内容权重更高',
      value: 95 
    },
    { 
      icon: <ThumbUp />, 
      title: '内容互动数据', 
      desc: '点赞、评论、分享等社交信号',
      value: 85 
    },
    { 
      icon: <AccessTime />, 
      title: '内容时效性', 
      desc: '最新发布的内容优先展示',
      value: 90 
    },
    { 
      icon: <Analytics />, 
      title: '内容相关度', 
      desc: '与查询意图的语义匹配程度',
      value: 88 
    },
  ];

  return (
    <Box sx={{ bgcolor: '#fafafa', minHeight: '100vh' }}>
      {/* 进度条 */}
      <Box
        sx={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          height: 3,
          bgcolor: 'background.paper',
          zIndex: 1201,
        }}
      >
        <LinearProgress
          variant="determinate"
          value={scrollProgress}
          sx={{
            height: '100%',
            '& .MuiLinearProgress-bar': {
              background: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 50%, #10b981 100%)',
            },
          }}
        />
      </Box>

      {/* Hero Section - 与主页一致的渐变背景 */}
      <Box
        sx={{
          pt: 12,
          pb: 8,
          background: 'linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #334155 50%, #475569 75%, #64748b 100%)',
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        {/* 装饰性背景 - 与主页一致 */}
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: `
              radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.15) 0%, transparent 50%),
              radial-gradient(circle at 80% 20%, rgba(16, 185, 129, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 40% 40%, rgba(200, 200, 200, 0.1) 0%, transparent 50%)
            `,
            pointerEvents: 'none',
          }}
        />

        <Container maxWidth="lg">
          <Box sx={{ textAlign: 'center' }}>
            <Fade in timeout={500}>
              <Chip
                icon={<Architecture />}
                label="AI SEARCH INFRASTRUCTURE"
                sx={{
                  mb: 3,
                  px: 3,
                  py: 2.5,
                  background: 'linear-gradient(135deg, #3b82f6 0%, #2563eb 100%)',
                  color: 'white',
                  fontWeight: 600,
                  fontSize: '0.9rem',
                  letterSpacing: 1,
                }}
              />
            </Fade>

            <Fade in timeout={700}>
              <Typography
                variant="h1"
                sx={{
                  fontSize: { xs: '2.5rem', md: '4rem' },
                  fontWeight: 800,
                  mb: 3,
                  color: 'white',
                  lineHeight: 1.2,
                }}
              >
                AI搜索时代的
                <br />
                内容基础设施
              </Typography>
            </Fade>

            <Fade in timeout={900}>
              <Typography
                variant="h5"
                sx={{
                  color: 'rgba(255, 255, 255, 0.9)',
                  mb: 5,
                  maxWidth: 800,
                  mx: 'auto',
                  fontWeight: 400,
                  lineHeight: 1.6,
                }}
              >
                当AI成为搜索主流，优质内容成为稀缺资源。
                我们构建连接企业与内容创作者的智能生态系统。
              </Typography>
            </Fade>

            <Fade in timeout={1100}>
              <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} sx={{ justifyContent: 'center' }}>
                <Button
                  variant="contained"
                  size="large"
                  onClick={handleGetStarted}
                  startIcon={<Rocket />}
                  sx={{
                    px: 4,
                    py: 1.5,
                    background: 'linear-gradient(135deg, #3b82f6 0%, #2563eb 100%)',
                    fontSize: '1.1rem',
                    fontWeight: 600,
                    borderRadius: 50,
                    textTransform: 'none',
                    boxShadow: '0 10px 30px rgba(59, 130, 246, 0.2)',
                    '&:hover': {
                      transform: 'translateY(-2px)',
                      boxShadow: '0 15px 40px rgba(59, 130, 246, 0.3)',
                    },
                  }}
                >
                  立即开始
                </Button>
                <Button
                  variant="outlined"
                  size="large"
                  onClick={() => document.getElementById('ai-principle').scrollIntoView({ behavior: 'smooth' })}
                  sx={{
                    px: 4,
                    py: 1.5,
                    fontSize: '1.1rem',
                    fontWeight: 600,
                    borderRadius: 50,
                    textTransform: 'none',
                    borderColor: 'rgba(255, 255, 255, 0.5)',
                    color: 'white',
                    backgroundColor: 'transparent',
                    '&:hover': {
                      borderColor: 'white',
                      backgroundColor: 'rgba(255, 255, 255, 0.1)',
                    },
                  }}
                >
                  了解原理
                </Button>
              </Stack>
            </Fade>
          </Box>
        </Container>
      </Box>

      {/* Section 1: AI搜索原理与内容生态的必然性 */}
      <Box id="ai-principle" sx={{ py: 10, bgcolor: 'white' }}>
        <Container maxWidth="lg">
          <Box sx={{ textAlign: 'center', mb: 8 }}>
            <Chip
              label="PRINCIPLE"
              sx={{
                mb: 3,
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                color: '#3b82f6',
                fontWeight: 600,
                letterSpacing: 1,
              }}
            />
            <Typography
              variant="h2"
              sx={{
                fontWeight: 700,
                mb: 2,
                fontSize: { xs: '2rem', md: '3rem' },
                color: '#1e293b',
              }}
            >
              AI搜索原理与内容生态的必然性
            </Typography>
            <Typography
              variant="h6"
              sx={{
                color: '#64748b',
                maxWidth: 700,
                mx: 'auto',
              }}
            >
              理解AI搜索的工作原理，把握内容营销的新机遇
            </Typography>
          </Box>

          {/* AI搜索工作原理 */}
          <Paper
            elevation={0}
            sx={{
              p: 5,
              mb: 6,
              borderRadius: 3,
              border: '1px solid #e2e8f0',
              background: 'linear-gradient(135deg, #f8fafc 0%, #ffffff 100%)',
            }}
          >
            <Typography variant="h4" sx={{ fontWeight: 600, mb: 4, color: '#1e293b' }}>
              AI搜索的工作原理
            </Typography>
            
            {/* 流程步骤 */}
            <Box sx={{ position: 'relative', mb: 4 }}>
              <Box
                sx={{
                  position: 'absolute',
                  top: 35,
                  left: 60,
                  right: 60,
                  height: 2,
                  background: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)',
                  opacity: 0.2,
                  display: { xs: 'none', md: 'block' },
                }}
              />
              <Grid container spacing={3}>
                {aiSearchSteps.map((step, index) => (
                  <Grid item xs={12} sm={6} md={2.4} key={index}>
                    <Box sx={{ textAlign: 'center', position: 'relative' }}>
                      <Avatar
                        sx={{
                          width: 70,
                          height: 70,
                          mx: 'auto',
                          mb: 2,
                          background: 'linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%)',
                          boxShadow: '0 10px 30px rgba(59, 130, 246, 0.2)',
                        }}
                      >
                        {React.cloneElement(step.icon, { sx: { fontSize: 35, color: 'white' } })}
                      </Avatar>
                      <Typography variant="subtitle1" sx={{ fontWeight: 600, color: '#1e293b', mb: 1 }}>
                        {index + 1}. {step.title}
                      </Typography>
                      <Typography variant="body2" sx={{ color: '#64748b' }}>
                        {step.desc}
                      </Typography>
                    </Box>
                  </Grid>
                ))}
              </Grid>
            </Box>

            <Box
              sx={{
                p: 3,
                borderRadius: 2,
                background: 'linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(139, 92, 246, 0.05) 100%)',
                border: '1px solid rgba(59, 130, 246, 0.1)',
              }}
            >
              <Typography variant="body1" sx={{ color: '#475569', lineHeight: 1.8 }}>
                <strong>关键洞察：</strong>与传统搜索引擎不同，AI搜索的核心是基于大语言模型（LLM）的理解和生成能力。
                它不仅匹配关键词，更理解用户意图，生成个性化答案。
              </Typography>
            </Box>
          </Paper>

          {/* AI搜索的内容依赖性 - 居中对齐 */}
          <Grid container spacing={4} sx={{ justifyContent: 'center' }}>
            <Grid size={{ xs: 12, md: 6 }}>
              <Paper
                elevation={0}
                sx={{
                  p: 4,
                  height: '100%',
                  borderRadius: 3,
                  border: '1px solid #e2e8f0',
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                  <Avatar
                    sx={{
                      width: 48,
                      height: 48,
                      background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
                      mr: 2,
                    }}
                  >
                    <DataArray />
                  </Avatar>
                  <Typography variant="h5" sx={{ fontWeight: 600, color: '#1e293b' }}>
                    内容依赖性
                  </Typography>
                </Box>

                <Typography variant="body1" sx={{ color: '#475569', mb: 3, lineHeight: 1.8 }}>
                  <strong>AI搜索的质量完全依赖于其能够获取的内容质量。</strong>
                </Typography>

                <List>
                  <ListItem sx={{ px: 0 }}>
                    <ListItemIcon>
                      <CheckCircle sx={{ color: '#10b981' }} />
                    </ListItemIcon>
                    <ListItemText
                      primary="预训练数据"
                      secondary="历史互联网数据，存在时效性问题"
                      primaryTypographyProps={{ fontWeight: 600, color: '#1e293b' }}
                      secondaryTypographyProps={{ color: '#64748b' }}
                    />
                  </ListItem>
                  <ListItem sx={{ px: 0 }}>
                    <ListItemIcon>
                      <CheckCircle sx={{ color: '#10b981' }} />
                    </ListItemIcon>
                    <ListItemText
                      primary="实时抓取"
                      secondary="从各大平台抓取最新内容"
                      primaryTypographyProps={{ fontWeight: 600, color: '#1e293b' }}
                      secondaryTypographyProps={{ color: '#64748b' }}
                    />
                  </ListItem>
                  <ListItem sx={{ px: 0 }}>
                    <ListItemIcon>
                      <CheckCircle sx={{ color: '#10b981' }} />
                    </ListItemIcon>
                    <ListItemText
                      primary="用户反馈"
                      secondary="通过用户互动不断优化答案"
                      primaryTypographyProps={{ fontWeight: 600, color: '#1e293b' }}
                      secondaryTypographyProps={{ color: '#64748b' }}
                    />
                  </ListItem>
                </List>
              </Paper>
            </Grid>

            <Grid item xs={12} md={6}>
              <Paper
                elevation={0}
                sx={{
                  p: 4,
                  height: '100%',
                  borderRadius: 3,
                  border: '1px solid #e2e8f0',
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                  <Avatar
                    sx={{
                      width: 48,
                      height: 48,
                      background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',
                      mr: 2,
                    }}
                  >
                    <FilterAlt />
                  </Avatar>
                  <Typography variant="h5" sx={{ fontWeight: 600, color: '#1e293b' }}>
                    AI如何判断内容质量？
                  </Typography>
                </Box>

                {contentDimensions.map((dimension, index) => (
                  <Box key={index} sx={{ mb: 3 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        {React.cloneElement(dimension.icon, { sx: { fontSize: 20, color: '#3b82f6', mr: 1 } })}
                        <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                          {dimension.title}
                        </Typography>
                      </Box>
                      <Typography variant="caption" sx={{ color: '#3b82f6', fontWeight: 600 }}>
                        {dimension.value}%
                      </Typography>
                    </Box>
                    <LinearProgress
                      variant="determinate"
                      value={dimension.value}
                      sx={{
                        height: 6,
                        borderRadius: 3,
                        backgroundColor: '#e2e8f0',
                        '& .MuiLinearProgress-bar': {
                          borderRadius: 3,
                          background: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)',
                        },
                      }}
                    />
                    <Typography variant="caption" sx={{ color: '#64748b', mt: 0.5, display: 'block' }}>
                      {dimension.desc}
                    </Typography>
                  </Box>
                ))}
              </Paper>
            </Grid>
          </Grid>
        </Container>
      </Box>

      {/* Section 2: 为什么企业需要内容提供商 */}
      <Box sx={{ py: 10, bgcolor: '#f8fafc' }}>
        <Container maxWidth="lg">
          <Box sx={{ textAlign: 'center', mb: 8 }}>
            <Chip
              label="NECESSITY"
              sx={{
                mb: 3,
                backgroundColor: 'rgba(139, 92, 246, 0.1)',
                color: '#8b5cf6',
                fontWeight: 600,
                letterSpacing: 1,
              }}
            />
            <Typography
              variant="h2"
              sx={{
                fontWeight: 700,
                mb: 2,
                fontSize: { xs: '2rem', md: '3rem' },
                color: '#1e293b',
              }}
            >
              为什么企业需要内容提供商
            </Typography>
            <Typography
              variant="h6"
              sx={{
                color: '#64748b',
                maxWidth: 700,
                mx: 'auto',
              }}
            >
              传统SEO失效，专业内容创作成为新的竞争力
            </Typography>
          </Box>

          {/* 传统SEO失效原因 - 改为大标题卡片展示 */}
          <Paper
            elevation={0}
            sx={{
              p: 5,
              mb: 6,
              borderRadius: 3,
              background: 'linear-gradient(135deg, #fff4f4 0%, #fff9f4 100%)',
              border: '1px solid #fed7d7',
            }}
          >
            <Box sx={{ textAlign: 'center', mb: 4 }}>
              <Typography variant="h4" sx={{ fontWeight: 600, color: '#1e293b', mb: 2 }}>
                传统SEO已经失效
              </Typography>
              <Typography variant="body1" sx={{ color: '#64748b', maxWidth: 600, mx: 'auto' }}>
                AI搜索时代的来临彻底改变了内容营销的游戏规则
              </Typography>
            </Box>
          </Paper>

          {/* 三个失效原因卡片 - 横向排列并居中 */}
          <Grid container spacing={4} sx={{ mb: 6, justifyContent: 'center' }}>
            <Grid size={{ xs: 12, md: 4 }}>
              <Card
                sx={{
                  height: '100%',
                  borderRadius: 3,
                  border: '1px solid #e2e8f0',
                  boxShadow: 'none',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    borderColor: '#cbd5e1',
                    transform: 'translateY(-4px)',
                    boxShadow: '0 10px 30px rgba(0,0,0,0.05)',
                  },
                }}
              >
                <CardContent sx={{ p: 4 }}>
                  <Box
                    sx={{
                      width: 60,
                      height: 60,
                      borderRadius: 2,
                      background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      mb: 3,
                    }}
                  >
                    <Warning sx={{ fontSize: 30, color: 'white' }} />
                  </Box>
                  <Typography variant="h6" sx={{ fontWeight: 600, mb: 2, color: '#1e293b' }}>
                    技术壁垒改变
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#64748b', lineHeight: 1.8 }}>
                    从关键词堆砌到语义理解，技术门槛大幅提升。AI搜索更注重内容的语义连贯性和价值密度。
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            <Grid size={{ xs: 12, md: 4 }}>
              <Card
                sx={{
                  height: '100%',
                  borderRadius: 3,
                  border: '1px solid #e2e8f0',
                  boxShadow: 'none',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    borderColor: '#cbd5e1',
                    transform: 'translateY(-4px)',
                    boxShadow: '0 10px 30px rgba(0,0,0,0.05)',
                  },
                }}
              >
                <CardContent sx={{ p: 4 }}>
                  <Box
                    sx={{
                      width: 60,
                      height: 60,
                      borderRadius: 2,
                      background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      mb: 3,
                    }}
                  >
                    <SyncAlt sx={{ fontSize: 30, color: 'white' }} />
                  </Box>
                  <Typography variant="h6" sx={{ fontWeight: 600, mb: 2, color: '#1e293b' }}>
                    内容形式改变
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#64748b', lineHeight: 1.8 }}>
                    从网页优化到全平台内容分发。需要在知乎、小红书、公众号等多平台同步发布高质量内容。
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            <Grid size={{ xs: 12, md: 4 }}>
              <Card
                sx={{
                  height: '100%',
                  borderRadius: 3,
                  border: '1px solid #e2e8f0',
                  boxShadow: 'none',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    borderColor: '#cbd5e1',
                    transform: 'translateY(-4px)',
                    boxShadow: '0 10px 30px rgba(0,0,0,0.05)',
                  },
                }}
              >
                <CardContent sx={{ p: 4 }}>
                  <Box
                    sx={{
                      width: 60,
                      height: 60,
                      borderRadius: 2,
                      background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      mb: 3,
                    }}
                  >
                    <CompareArrows sx={{ fontSize: 30, color: 'white' }} />
                  </Box>
                  <Typography variant="h6" sx={{ fontWeight: 600, mb: 2, color: '#1e293b' }}>
                    权重算法改变
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#64748b', lineHeight: 1.8 }}>
                    从外链数量到内容质量和账户权重。高权重账户发布的内容在AI搜索中具有天然优势。
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* 内容提供商的价值 - 改进布局 */}
          <Box sx={{ mt: 8 }}>
            <Paper
              elevation={0}
              sx={{
                p: 5,
                borderRadius: 3,
                background: 'linear-gradient(135deg, #f0fdf4 0%, #f0f9ff 100%)',
                border: '1px solid #bbf7d0',
                mb: 6,
              }}
            >
              <Box sx={{ textAlign: 'center', mb: 4 }}>
                <Typography variant="h4" sx={{ fontWeight: 600, color: '#1e293b', mb: 2 }}>
                  内容提供商的不可替代价值
                </Typography>
                <Typography variant="body1" sx={{ color: '#64748b', maxWidth: 700, mx: 'auto' }}>
                  专业的内容提供商拥有企业难以复制的核心竞争力
                </Typography>
              </Box>
            </Paper>

            <Grid container spacing={4} sx={{ justifyContent: 'center' }}>
              {[
                {
                  title: '高权重账户资源',
                  icon: <VerifiedUserOutlined />,
                  color: '#3b82f6',
                  points: [
                    '长期经营的真实账户在AI算法中权重极高',
                    '通过持续输出高质量内容建立信任度',
                    'AI搜索引擎优先抓取和展示这些账户的内容',
                  ],
                },
                {
                  title: '内容创作专业性',
                  icon: <Create />,
                  color: '#10b981',
                  points: [
                    '了解AI搜索的语义理解特点',
                    '创作既满足AI算法又吸引真实用户的内容',
                    '自然植入商业信息而不影响内容质量',
                  ],
                },
                {
                  title: '多平台分发能力',
                  icon: <Share />,
                  color: '#f59e0b',
                  points: [
                    '不同AI搜索引擎有不同的内容偏好',
                    '针对性地在不同平台发布和优化内容',
                    '掌握各平台的最佳发布时机和内容形式',
                  ],
                },
              ].map((item, index) => (
                <Grid item xs={12} md={4} key={index}>
                  <Card
                    sx={{
                      height: '100%',
                      borderRadius: 3,
                      border: '1px solid #e2e8f0',
                      boxShadow: 'none',
                      transition: 'all 0.3s ease',
                      background: 'white',
                      '&:hover': {
                        borderColor: item.color,
                        transform: 'translateY(-8px)',
                        boxShadow: `0 20px 40px ${item.color}20`,
                      },
                    }}
                  >
                    <CardContent sx={{ p: 4 }}>
                      <Box sx={{ textAlign: 'center', mb: 3 }}>
                        <Avatar
                          sx={{
                            width: 64,
                            height: 64,
                            background: `linear-gradient(135deg, ${item.color} 0%, ${item.color}dd 100%)`,
                            mx: 'auto',
                            mb: 2,
                            boxShadow: `0 8px 20px ${item.color}30`,
                          }}
                        >
                          {React.cloneElement(item.icon, { sx: { fontSize: 32 } })}
                        </Avatar>
                        <Typography variant="h6" sx={{ fontWeight: 600, color: '#1e293b' }}>
                          {item.title}
                        </Typography>
                      </Box>
                      <List>
                      {item.points.map((point, idx) => (
                        <ListItem key={idx} sx={{ px: 0, py: 1 }}>
                          <ListItemIcon sx={{ minWidth: 32 }}>
                            <CheckCircleOutline sx={{ fontSize: 20, color: item.color }} />
                          </ListItemIcon>
                          <ListItemText
                            primary={point}
                            primaryTypographyProps={{
                              variant: 'body2',
                              color: '#475569',
                              lineHeight: 1.6,
                            }}
                          />
                        </ListItem>
                      ))}
                      </List>
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </Box>
        </Container>
      </Box>

      {/* Section 3: 内容生态的商业逻辑 */}
      <Box sx={{ py: 10, bgcolor: 'white' }}>
        <Container maxWidth="lg">
          <Box sx={{ textAlign: 'center', mb: 8 }}>
            <Chip
              label="BUSINESS LOGIC"
              sx={{
                mb: 3,
                backgroundColor: 'rgba(16, 185, 129, 0.1)',
                color: '#10b981',
                fontWeight: 600,
                letterSpacing: 1,
              }}
            />
            <Typography
              variant="h2"
              sx={{
                fontWeight: 700,
                mb: 2,
                fontSize: { xs: '2rem', md: '3rem' },
                color: '#1e293b',
              }}
            >
              我们的产品介绍
            </Typography>
            <Typography
              variant="h6"
              sx={{
                color: '#64748b',
                maxWidth: 700,
                mx: 'auto',
              }}
            >
              为企业提供全方位的AI搜索内容营销解决方案
            </Typography>
          </Box>

          {/* 核心产品功能 - 居中对齐 */}
          <Grid container spacing={4} sx={{ mb: 6, justifyContent: 'center' }}>
            {[
              {
                title: '企业内容管理系统',
                icon: <BusinessCenter />,
                color: '#3b82f6',
                gradient: 'linear-gradient(135deg, #3b82f6 0%, #2563eb 100%)',
                points: [
                  { icon: <RadioButtonChecked />, text: '一站式内容创作与发布管理平台' },
                  { icon: <RadioButtonChecked />, text: 'AI辅助内容生成，提升创作效率' },
                  { icon: <RadioButtonChecked />, text: '多渠道同步发布，扩大影响力' },
                ],
              },
              {
                title: '智能匹配引擎',
                icon: <Create />,
                color: '#10b981',
                gradient: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
                points: [
                  { icon: <RadioButtonChecked />, text: '基于AI算法的精准供需匹配' },
                  { icon: <RadioButtonChecked />, text: '实时分析内容质量与渠道效果' },
                  { icon: <RadioButtonChecked />, text: '智能推荐最优投放策略' },
                ],
              },
              {
                title: '数据分析中心',
                icon: <Hub />,
                color: '#8b5cf6',
                gradient: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',
                points: [
                  { icon: <RadioButtonChecked />, text: '实时监测AI搜索排名变化' },
                  { icon: <RadioButtonChecked />, text: '内容效果数据可视化分析' },
                  { icon: <RadioButtonChecked />, text: '竞品动态跟踪与预警' },
                ],
              },
            ].map((item, index) => (
              <Grid size={{ xs: 12, md: 4 }} key={index}>
                <Paper
                  elevation={0}
                  sx={{
                    height: '100%',
                    borderRadius: 3,
                    overflow: 'hidden',
                    border: '1px solid #e2e8f0',
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      boxShadow: '0 20px 40px rgba(0,0,0,0.08)',
                    },
                  }}
                >
                  <Box
                    sx={{
                      p: 3,
                      background: item.gradient,
                      color: 'white',
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      {React.cloneElement(item.icon, { sx: { fontSize: 32, mr: 2 } })}
                      <Typography variant="h5" sx={{ fontWeight: 600 }}>
                        {item.title}
                      </Typography>
                    </Box>
                  </Box>
                  <CardContent sx={{ p: 4 }}>
                    <List>
                      {item.points.map((point, idx) => (
                        <ListItem key={idx} sx={{ px: 0, py: 1.5 }}>
                          <ListItemIcon sx={{ minWidth: 32 }}>
                            {React.cloneElement(point.icon, { 
                              sx: { fontSize: 20, color: item.color } 
                            })}
                          </ListItemIcon>
                          <ListItemText
                            primary={point.text}
                            primaryTypographyProps={{
                              variant: 'body2',
                              color: '#475569',
                              lineHeight: 1.6,
                            }}
                          />
                        </ListItem>
                      ))}
                    </List>
                  </CardContent>
                </Paper>
              </Grid>
            ))}
          </Grid>

          {/* 产品优势说明 */}
          <Box
            sx={{
              p: 5,
              borderRadius: 3,
              background: 'linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(16, 185, 129, 0.05) 100%)',
              border: '2px solid',
              borderColor: 'transparent',
              borderImage: 'linear-gradient(135deg, #3b82f6 0%, #10b981 100%) 1',
            }}
          >
            <Typography variant="h5" sx={{ fontWeight: 600, mb: 3, color: '#1e293b', textAlign: 'center' }}>
              为什么选择我们的产品？
            </Typography>
            <Typography variant="body1" sx={{ color: '#475569', lineHeight: 1.8, textAlign: 'center' }}>
              我们提供的不仅是工具，更是一套完整的内容营销生态系统。
              <br />
              通过技术创新和平台优势，帮助企业在AI搜索时代获得<strong>持续竞争力</strong>。
            </Typography>
          </Box>
        </Container>
      </Box>

      {/* Section 4: 我们的解决方案 */}
      <Box sx={{ py: 10, bgcolor: '#f8fafc' }}>
        <Container maxWidth="lg">
          <Box sx={{ textAlign: 'center', mb: 8 }}>
            <Chip
              label="SOLUTION"
              sx={{
                mb: 3,
                background: 'linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%)',
                color: 'white',
                fontWeight: 600,
                letterSpacing: 1,
                px: 3,
                py: 2.5,
              }}
            />
            <Typography
              variant="h2"
              sx={{
                fontWeight: 700,
                mb: 2,
                fontSize: { xs: '2rem', md: '3rem' },
                background: 'linear-gradient(135deg, #1e293b 0%, #475569 100%)',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
              }}
            >
              我们的解决方案
            </Typography>
            <Typography
              variant="h6"
              sx={{
                color: '#64748b',
                maxWidth: 700,
                mx: 'auto',
              }}
            >
              构建AI时代的内容生态基础设施
            </Typography>
          </Box>

          {/* 解决方案tabs */}
          <Paper
            elevation={0}
            sx={{
              borderRadius: 3,
              border: '1px solid #e2e8f0',
              overflow: 'hidden',
            }}
          >
            <Tabs
              value={activeTab}
              onChange={(e, newValue) => setActiveTab(newValue)}
              variant="fullWidth"
              sx={{
                borderBottom: '1px solid #e2e8f0',
                '& .MuiTab-root': {
                  textTransform: 'none',
                  fontWeight: 600,
                  fontSize: '1rem',
                },
              }}
            >
              <Tab label="双边生态平台" />
              <Tab label="智能匹配系统" />
              <Tab label="创作者赋能" />
              <Tab label="风控质量保障" />
              <Tab label="价值分配机制" />
            </Tabs>

            <Box sx={{ p: { xs: 3, md: 5 }, mt: 3 }}>
              {activeTab === 0 && (
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 600, mb: 3, color: '#1e293b', textAlign: 'center' }}>
                    双边生态平台
                  </Typography>
                  <Typography variant="body1" sx={{ color: '#475569', mb: 5, lineHeight: 1.8, textAlign: 'center', maxWidth: 800, mx: 'auto' }}>
                    我们构建了连接企业和内容提供商的智能化双边平台，让供需双方高效匹配。
                  </Typography>
                  <Grid container spacing={4} alignItems="center" justifyContent="center">
                    <Grid item xs={12} md={6}>
                      <Grid container spacing={3}>
                      <Grid item xs={12}>
                        <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
                          <Avatar
                            sx={{
                              width: 40,
                              height: 40,
                              background: 'linear-gradient(135deg, #3b82f6 0%, #2563eb 100%)',
                              mr: 2,
                            }}
                          >
                            <BusinessCenter sx={{ fontSize: 20 }} />
                          </Avatar>
                          <Box>
                            <Typography variant="subtitle1" sx={{ fontWeight: 600, color: '#1e293b', mb: 1 }}>
                              企业端
                            </Typography>
                            <List dense>
                              <ListItem sx={{ px: 0 }}>
                                <ListItemIcon sx={{ minWidth: 28 }}>
                                  <ArrowRightAlt sx={{ fontSize: 18, color: '#3b82f6' }} />
                                </ListItemIcon>
                                <ListItemText primary="发布优化需求，明确目标关键词" />
                              </ListItem>
                              <ListItem sx={{ px: 0 }}>
                                <ListItemIcon sx={{ minWidth: 28 }}>
                                  <ArrowRightAlt sx={{ fontSize: 18, color: '#3b82f6' }} />
                                </ListItemIcon>
                                <ListItemText primary="按效果付费，透明可控" />
                              </ListItem>
                              <ListItem sx={{ px: 0 }}>
                                <ListItemIcon sx={{ minWidth: 28 }}>
                                  <ArrowRightAlt sx={{ fontSize: 18, color: '#3b82f6' }} />
                                </ListItemIcon>
                                <ListItemText primary="实时监测数据，掌握投放效果" />
                              </ListItem>
                            </List>
                          </Box>
                        </Box>
                      </Grid>
                      <Grid item xs={12}>
                        <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
                          <Avatar
                            sx={{
                              width: 40,
                              height: 40,
                              background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
                              mr: 2,
                            }}
                          >
                            <Create sx={{ fontSize: 20 }} />
                          </Avatar>
                          <Box>
                            <Typography variant="subtitle1" sx={{ fontWeight: 600, color: '#1e293b', mb: 1 }}>
                              创作者端（渠道商）
                            </Typography>
                            <List dense>
                              <ListItem sx={{ px: 0 }}>
                                <ListItemIcon sx={{ minWidth: 28 }}>
                                  <ArrowRightAlt sx={{ fontSize: 18, color: '#10b981' }} />
                                </ListItemIcon>
                                <ListItemText primary="AI辅助创作，提升效率" />
                              </ListItem>
                              <ListItem sx={{ px: 0 }}>
                                <ListItemIcon sx={{ minWidth: 28 }}>
                                  <ArrowRightAlt sx={{ fontSize: 18, color: '#10b981' }} />
                                </ListItemIcon>
                                <ListItemText primary="管理渠道发布，多平台同步" />
                              </ListItem>
                              <ListItem sx={{ px: 0 }}>
                                <ListItemIcon sx={{ minWidth: 28 }}>
                                  <ArrowRightAlt sx={{ fontSize: 18, color: '#10b981' }} />
                                </ListItemIcon>
                                <ListItemText primary="保障排名效果，获得持续收益" />
                              </ListItem>
                            </List>
                          </Box>
                        </Box>
                      </Grid>
                    </Grid>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Box
                      sx={{
                        position: 'relative',
                        height: 400,
                        background: 'linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%)',
                        borderRadius: 3,
                        p: 4,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                    >
                      <Box sx={{ textAlign: 'center' }}>
                        <Handshake sx={{ fontSize: 100, color: '#3b82f6', mb: 2 }} />
                        <Typography variant="h5" sx={{ fontWeight: 600, color: '#1e293b', mb: 1 }}>
                          双边网络效应
                        </Typography>
                        <Typography variant="body2" sx={{ color: '#64748b' }}>
                          更多企业带来更多需求
                          <br />
                          更多创作者提供更好内容
                          <br />
                          形成正向循环生态
                        </Typography>
                      </Box>
                    </Box>
                  </Grid>
                </Grid>
                </Box>
              )}

              {activeTab === 1 && (
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 600, mb: 3, color: '#1e293b', textAlign: 'center' }}>
                    智能匹配系统
                  </Typography>
                  <Typography variant="body1" sx={{ color: '#475569', mb: 5, lineHeight: 1.8, textAlign: 'center', maxWidth: 800, mx: 'auto' }}>
                    我们的AI算法确保企业需求与创作者能力的精准匹配，匹配度远超行业平均水平。
                  </Typography>
                  <Grid container spacing={4} alignItems="center" justifyContent="center">
                    <Grid item xs={12} md={6}>
                      <List>
                      <ListItem sx={{ px: 0, py: 2 }}>
                        <ListItemIcon>
                          <Avatar sx={{ bgcolor: 'rgba(59, 130, 246, 0.1)' }}>
                            <VerifiedUser sx={{ color: '#3b82f6' }} />
                          </Avatar>
                        </ListItemIcon>
                        <ListItemText
                          primary="真实账户认证"
                          secondary="严格认证机制，剔除虚假账户，只合作真实高权重渠道"
                          primaryTypographyProps={{ fontWeight: 600, color: '#1e293b' }}
                          secondaryTypographyProps={{ color: '#64748b' }}
                        />
                      </ListItem>
                      <ListItem sx={{ px: 0, py: 2 }}>
                        <ListItemIcon>
                          <Avatar sx={{ bgcolor: 'rgba(139, 92, 246, 0.1)' }}>
                            <Psychology sx={{ color: '#8b5cf6' }} />
                          </Avatar>
                        </ListItemIcon>
                        <ListItemText
                          primary="AI智能匹配"
                          secondary="基于内容风格、行业专长、历史表现等多维度智能匹配"
                          primaryTypographyProps={{ fontWeight: 600, color: '#1e293b' }}
                          secondaryTypographyProps={{ color: '#64748b' }}
                        />
                      </ListItem>
                      <ListItem sx={{ px: 0, py: 2 }}>
                        <ListItemIcon>
                          <Avatar sx={{ bgcolor: 'rgba(16, 185, 129, 0.1)' }}>
                            <TrendingUp sx={{ color: '#10b981' }} />
                          </Avatar>
                        </ListItemIcon>
                        <ListItemText
                          primary="效果数据优化"
                          secondary="基于实际投放效果持续优化匹配算法，越用越精准"
                          primaryTypographyProps={{ fontWeight: 600, color: '#1e293b' }}
                          secondaryTypographyProps={{ color: '#64748b' }}
                        />
                      </ListItem>
                    </List>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Paper
                      elevation={0}
                      sx={{
                        p: 4,
                        background: 'linear-gradient(135deg, #f8fafc 0%, #ffffff 100%)',
                        border: '1px solid #e2e8f0',
                        borderRadius: 3,
                      }}
                    >
                      <Typography variant="h6" sx={{ fontWeight: 600, mb: 3, color: '#1e293b' }}>
                        匹配成功率对比
                      </Typography>
                      <Box sx={{ mb: 3 }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                          <Typography variant="body2" sx={{ fontWeight: 600 }}>我们的平台</Typography>
                          <Typography variant="body2" sx={{ color: '#3b82f6', fontWeight: 600 }}>95%</Typography>
                        </Box>
                        <LinearProgress
                          variant="determinate"
                          value={95}
                          sx={{
                            height: 8,
                            borderRadius: 4,
                            backgroundColor: '#e2e8f0',
                            '& .MuiLinearProgress-bar': {
                              borderRadius: 4,
                              background: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)',
                            },
                          }}
                        />
                      </Box>
                      <Box sx={{ mb: 3 }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                          <Typography variant="body2" sx={{ fontWeight: 600 }}>传统中介</Typography>
                          <Typography variant="body2" sx={{ color: '#64748b', fontWeight: 600 }}>45%</Typography>
                        </Box>
                        <LinearProgress
                          variant="determinate"
                          value={45}
                          sx={{
                            height: 8,
                            borderRadius: 4,
                            backgroundColor: '#e2e8f0',
                            '& .MuiLinearProgress-bar': {
                              borderRadius: 4,
                              backgroundColor: '#94a3b8',
                            },
                          }}
                        />
                      </Box>
                      <Box>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                          <Typography variant="body2" sx={{ fontWeight: 600 }}>行业平均</Typography>
                          <Typography variant="body2" sx={{ color: '#64748b', fontWeight: 600 }}>60%</Typography>
                        </Box>
                        <LinearProgress
                          variant="determinate"
                          value={60}
                          sx={{
                            height: 8,
                            borderRadius: 4,
                            backgroundColor: '#e2e8f0',
                            '& .MuiLinearProgress-bar': {
                              borderRadius: 4,
                              backgroundColor: '#94a3b8',
                            },
                          }}
                        />
                      </Box>
                    </Paper>
                  </Grid>
                </Grid>
                </Box>
              )}

              {activeTab === 2 && (
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 600, mb: 3, color: '#1e293b', textAlign: 'center' }}>
                    创作者赋能
                  </Typography>
                  <Typography variant="body1" sx={{ color: '#475569', mb: 5, lineHeight: 1.8, textAlign: 'center', maxWidth: 800, mx: 'auto' }}>
                    为创作者提供全方位支持，让优质内容创造更大价值
                  </Typography>
                  <Grid container spacing={3} justifyContent="center">
                    {[
                      {
                        icon: <AccountBalanceWallet />,
                        title: '内容价值变现',
                        desc: '让被AI免费抓取的内容产生商业价值',
                        color: '#3b82f6',
                      },
                      {
                        icon: <AutoAwesome />,
                        title: 'AI创作辅助',
                        desc: '提供AI工具和内容框架，降低创作难度',
                        color: '#8b5cf6',
                      },
                      {
                        icon: <Shield />,
                        title: '账户安全保障',
                        desc: '软广形式确保账户权重不受影响',
                        color: '#10b981',
                      },
                      {
                        icon: <TrendingUpOutlined />,
                        title: '稳定收入来源',
                        desc: '持续的订单供应，收入有保障',
                        color: '#f59e0b',
                      },
                      {
                        icon: <School />,
                        title: '行业洞察支持',
                        desc: '提供行业趋势和热点分析',
                        color: '#ec4899',
                      },
                      {
                        icon: <Groups />,
                        title: '创作者社区',
                        desc: '经验分享，共同成长',
                        color: '#06b6d4',
                      },
                    ].map((item, index) => (
                      <Grid size={{ xs: 12, sm: 6, md: 4 }} key={index}>
                        <Card
                          sx={{
                            height: '100%',
                            borderRadius: 2,
                            border: '1px solid #e2e8f0',
                            boxShadow: 'none',
                            transition: 'all 0.3s ease',
                            '&:hover': {
                              borderColor: item.color,
                              transform: 'translateY(-4px)',
                              boxShadow: `0 10px 30px ${item.color}20`,
                            },
                          }}
                        >
                          <CardContent sx={{ p: 3, textAlign: 'center' }}>
                            <Avatar
                              sx={{
                                width: 60,
                                height: 60,
                                mx: 'auto',
                                mb: 2,
                                background: `linear-gradient(135deg, ${item.color} 0%, ${item.color}dd 100%)`,
                              }}
                            >
                              {React.cloneElement(item.icon, { sx: { fontSize: 30, color: 'white' } })}
                            </Avatar>
                            <Typography variant="h6" sx={{ fontWeight: 600, mb: 1, color: '#1e293b' }}>
                              {item.title}
                            </Typography>
                            <Typography variant="body2" sx={{ color: '#64748b' }}>
                              {item.desc}
                            </Typography>
                          </CardContent>
                        </Card>
                      </Grid>
                    ))}
                  </Grid>
                  <Box
                    sx={{
                      mt: 4,
                      p: 3,
                      borderRadius: 2,
                      background: 'linear-gradient(135deg, rgba(16, 185, 129, 0.05) 0%, rgba(59, 130, 246, 0.05) 100%)',
                      border: '1px solid rgba(16, 185, 129, 0.2)',
                      textAlign: 'center',
                    }}
                  >
                    <Typography variant="body1" sx={{ color: '#1e293b', fontWeight: 600 }}>
                      创作者的内容被AI引用越多，获得的收益越高
                    </Typography>
                  </Box>
                </Box>
              )}

              {activeTab === 3 && (
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 600, mb: 3, color: '#1e293b', textAlign: 'center' }}>
                    风控与质量保障体系
                  </Typography>
                  <Typography variant="body1" sx={{ color: '#475569', mb: 5, lineHeight: 1.8, textAlign: 'center', maxWidth: 800, mx: 'auto' }}>
                    多重保障机制确保内容质量和投放效果
                  </Typography>
                  <Grid container spacing={4} sx={{ justifyContent: 'center' }}>
                    <Grid item xs={12} md={6}>
                      <Paper
                        elevation={0}
                        sx={{
                          p: 4,
                          height: '100%',
                          borderRadius: 3,
                          border: '1px solid #e2e8f0',
                        }}
                      >
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                          <Avatar
                            sx={{
                              width: 48,
                              height: 48,
                              background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',
                              mr: 2,
                            }}
                          >
                            <Gavel />
                          </Avatar>
                          <Typography variant="h5" sx={{ fontWeight: 600, color: '#1e293b' }}>
                            三级审核机制
                          </Typography>
                        </Box>
                        <List>
                          <ListItem sx={{ px: 0 }}>
                            <ListItemIcon>
                              <RadioButtonChecked sx={{ color: '#ef4444' }} />
                            </ListItemIcon>
                            <ListItemText
                              primary="AI预审"
                              secondary="检测原创度和敏感词"
                              primaryTypographyProps={{ fontWeight: 600 }}
                            />
                          </ListItem>
                          <ListItem sx={{ px: 0 }}>
                            <ListItemIcon>
                              <RadioButtonChecked sx={{ color: '#ef4444' }} />
                            </ListItemIcon>
                            <ListItemText
                              primary="人工复核"
                              secondary="确保内容专业性"
                              primaryTypographyProps={{ fontWeight: 600 }}
                            />
                          </ListItem>
                          <ListItem sx={{ px: 0 }}>
                            <ListItemIcon>
                              <RadioButtonChecked sx={{ color: '#ef4444' }} />
                            </ListItemIcon>
                            <ListItemText
                              primary="客户确认"
                              secondary="最终满意度把关"
                              primaryTypographyProps={{ fontWeight: 600 }}
                            />
                          </ListItem>
                        </List>
                      </Paper>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Paper
                        elevation={0}
                        sx={{
                          p: 4,
                          height: '100%',
                          borderRadius: 3,
                          border: '1px solid #e2e8f0',
                        }}
                      >
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                          <Avatar
                            sx={{
                              width: 48,
                              height: 48,
                              background: 'linear-gradient(135deg, #3b82f6 0%, #2563eb 100%)',
                              mr: 2,
                            }}
                          >
                            <VerifiedUser />
                          </Avatar>
                          <Typography variant="h5" sx={{ fontWeight: 600, color: '#1e293b' }}>
                            信用评级系统
                          </Typography>
                        </Box>
                        <List>
                          <ListItem sx={{ px: 0 }}>
                            <ListItemIcon>
                              <CheckCircle sx={{ color: '#3b82f6' }} />
                            </ListItemIcon>
                            <ListItemText
                              primary="账户质量评估"
                              secondary="实时监测权重和互动率"
                              primaryTypographyProps={{ fontWeight: 600 }}
                            />
                          </ListItem>
                          <ListItem sx={{ px: 0 }}>
                            <ListItemIcon>
                              <CheckCircle sx={{ color: '#3b82f6' }} />
                            </ListItemIcon>
                            <ListItemText
                              primary="内容质量把控"
                              secondary="识别并剔除劣质内容"
                              primaryTypographyProps={{ fontWeight: 600 }}
                            />
                          </ListItem>
                          <ListItem sx={{ px: 0 }}>
                            <ListItemIcon>
                              <CheckCircle sx={{ color: '#3b82f6' }} />
                            </ListItemIcon>
                            <ListItemText
                              primary="长期信任建立"
                              secondary="分级管理和激励机制"
                              primaryTypographyProps={{ fontWeight: 600 }}
                            />
                          </ListItem>
                        </List>
                      </Paper>
                    </Grid>
                  </Grid>
                </Box>
              )}

              {activeTab === 4 && (
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 600, mb: 3, color: '#1e293b', textAlign: 'center' }}>
                    透明的价值分配机制
                  </Typography>
                  <Typography variant="body1" sx={{ color: '#475569', mb: 5, lineHeight: 1.8, textAlign: 'center', maxWidth: 800, mx: 'auto' }}>
                    公平透明的利益分配，让每个参与者都能获得应有回报
                  </Typography>
                  <Grid container spacing={4} sx={{ justifyContent: 'center' }}>
                    {[
                      {
                        title: '企业端',
                        icon: <BusinessCenter />,
                        color: '#3b82f6',
                        items: [
                          '明码标价的服务费用',
                          '内容创作费 + 渠道发布费',
                          '可选的会员订阅费',
                          '发布即付费，无虚假渠道损失',
                        ],
                      },
                      {
                        title: '创作者端',
                        icon: <Create />,
                        color: '#10b981',
                        items: [
                          '获得交易金额的80-85%',
                          '平台仅收取15-20%佣金',
                          'T+7快速结算',
                          '收入稳定有保障',
                        ],
                      },
                      {
                        title: '平台端',
                        icon: <Hub />,
                        color: '#8b5cf6',
                        items: [
                          '撮合佣金维持运营',
                          '会员订阅费用',
                          '持续投入技术研发',
                          '不断优化服务体验',
                        ],
                      },
                    ].map((item, index) => (
                      <Grid item xs={12} md={4} key={index}>
                        <Card
                          sx={{
                            height: '100%',
                            borderRadius: 3,
                            overflow: 'hidden',
                            border: '1px solid #e2e8f0',
                            boxShadow: 'none',
                          }}
                        >
                          <Box
                            sx={{
                              p: 3,
                              background: `linear-gradient(135deg, ${item.color} 0%, ${item.color}dd 100%)`,
                              color: 'white',
                              textAlign: 'center',
                            }}
                          >
                            {React.cloneElement(item.icon, { sx: { fontSize: 40, mb: 1 } })}
                            <Typography variant="h5" sx={{ fontWeight: 600 }}>
                              {item.title}
                            </Typography>
                          </Box>
                          <CardContent sx={{ p: 3 }}>
                            <List>
                              {item.items.map((text, idx) => (
                                <ListItem key={idx} sx={{ px: 0, py: 1 }}>
                                  <ListItemIcon sx={{ minWidth: 32 }}>
                                    <CheckCircleOutline sx={{ fontSize: 20, color: item.color }} />
                                  </ListItemIcon>
                                  <ListItemText
                                    primary={text}
                                    primaryTypographyProps={{
                                      variant: 'body2',
                                      color: '#475569',
                                    }}
                                  />
                                </ListItem>
                              ))}
                            </List>
                          </CardContent>
                        </Card>
                      </Grid>
                    ))}
                  </Grid>
                </Box>
              )}
            </Box>
          </Paper>
        </Container>
      </Box>

      <Footer />
    </Box>
  );
}

export default Product;