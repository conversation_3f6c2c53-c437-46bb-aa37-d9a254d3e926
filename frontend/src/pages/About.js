import React, { useEffect, useState } from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Avatar,
  Chip,
  Stack,
  Paper,
  Fade,
  Grow,
  Button,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
} from '@mui/material';
import {
  AutoAwesome,
  Rocket,
  Language,
  Gavel,
  CleaningServices,
  Hub,
  School,
  Shield,
  AllInclusive,
  TrendingUp,
  DiamondOutlined,
  StarOutline,
  ArrowForward,
  CheckCircle,
  PlayArrow,
  Insights,
  PublicOutlined,
  SecurityOutlined,
  GroupsOutlined,
} from '@mui/icons-material';
import Footer from '../components/layout/Footer';

function About() {
  const [activeValue, setActiveValue] = useState(0);
  const [visibleMissions, setVisibleMissions] = useState([]);
  const [stats, setStats] = useState({
    clients: 0,
    projects: 0,
    satisfaction: 0,
    countries: 0,
  });

  // Animate stats counters
  useEffect(() => {
    const targets = { clients: 1000, projects: 5000, satisfaction: 98, countries: 50 };
    const duration = 2000;
    const steps = 60;
    const stepTime = duration / steps;

    const intervals = Object.keys(targets).map(key => {
      const target = targets[key];
      const step = target / steps;
      let current = 0;

      return setInterval(() => {
        current += step;
        if (current >= target) {
          current = target;
          clearInterval(intervals.find(i => i === intervals[key]));
        }
        setStats(prev => ({ ...prev, [key]: Math.floor(current) }));
      }, stepTime);
    });

    return () => intervals.forEach(clearInterval);
  }, []);

  // Auto-rotate core values
  useEffect(() => {
    const interval = setInterval(() => {
      setActiveValue(prev => (prev + 1) % 4);
    }, 4000);
    return () => clearInterval(interval);
  }, []);

  // Animate mission cards
  useEffect(() => {
    const timer = setInterval(() => {
      setVisibleMissions(prev => {
        if (prev.length < 6) {
          return [...prev, prev.length];
        }
        clearInterval(timer);
        return prev;
      });
    }, 200);

    return () => clearInterval(timer);
  }, []);

  // Core values with tech styling
  const coreValues = [
    {
      title: '创新驱动',
      icon: <Rocket />,
      description: '永不停止探索的脚步',
      color: '#3b82f6',
      features: ['前沿AI技术研发', '持续产品迭代', '创新解决方案']
    },
    {
      title: '用户至上',
      icon: <AutoAwesome />,
      description: '一切以用户价值为核心',
      color: '#10b981',
      features: ['用户需求导向', '极致产品体验', '7×24服务保障']
    },
    {
      title: '安全可靠',
      icon: <Shield />,
      description: '构建值得信赖的生态',
      color: '#f59e0b',
      features: ['企业级安全防护', '数据隐私保护', '稳定服务保障']
    },
    {
      title: '合作共赢',
      icon: <AllInclusive />,
      description: '携手共创美好未来',
      color: '#8b5cf6',
      features: ['开放生态平台', '合作伙伴赋能', '价值共创共享']
    },
  ];

  // Company missions with enhanced content
  const missions = [
    {
      icon: <Gavel />,
      title: '建立行业标准',
      subtitle: '确保信息可控',
      description: '参与制定AI搜索优化行业标准，建立内容审核和溯源机制，让AI搜索生态更加健康有序',
      color: '#3b82f6',
      impact: '已制定3项行业标准',
      keyPoints: ['标准化内容审核流程', '建立溯源追踪机制', '推动行业规范化发展']
    },
    {
      icon: <Language />,
      title: '推动国际化',
      subtitle: '助力全球发展',
      description: '帮助企业在ChatGPT、Claude、Grok等国际AI平台获得更好展示，成为连接中外市场的桥梁',
      color: '#10b981',
      impact: '服务覆盖50+国家',
      keyPoints: ['多语言AI平台支持', '跨境内容优化', '国际市场拓展']
    },
    {
      icon: <Shield />,
      title: '强化AI安全',
      subtitle: '保护企业品牌',
      description: '建立AI内容风险监测体系，开发AI生成内容检测技术，提供全方位品牌保护方案',
      color: '#f59e0b',
      impact: '99.9%安全防护率',
      keyPoints: ['AI风险监测系统', '品牌保护方案', '内容安全检测']
    },
    {
      icon: <CleaningServices />,
      title: '净化内容生态',
      subtitle: '提升AI质量',
      description: '严格内容审核质量控制，激励优质内容创作，为提升整体AI回答质量贡献力量',
      color: '#8b5cf6',
      impact: '处理10万+内容样本',
      keyPoints: ['三级内容审核机制', '优质内容激励', 'AI回答质量提升']
    },
    {
      icon: <Hub />,
      title: '构建开放平台',
      subtitle: '赋能行业发展',
      description: '开放技术能力和数据接口，让更多企业和开发者参与生态建设',
      color: '#06b6d4',
      impact: '1000+开发者接入',
      keyPoints: ['开放API接口', '技术能力共享', '开发者生态建设']
    },
    {
      icon: <School />,
      title: '培养专业人才',
      subtitle: '推动职业发展',
      description: '建立AI搜索专业人才培养体系，推动整个行业的职业化和规范化发展',
      color: '#ec4899',
      impact: '培养5000+专业人才',
      keyPoints: ['专业认证体系', '人才培训计划', '职业发展通道']
    },
  ];

  const companyStats = [
    { label: '服务客户', value: stats.clients, suffix: '+', icon: <GroupsOutlined /> },
    { label: '完成项目', value: stats.projects, suffix: '+', icon: <Insights /> },
    { label: '客户满意度', value: stats.satisfaction, suffix: '%', icon: <StarOutline /> },
    { label: '覆盖国家', value: stats.countries, suffix: '+', icon: <PublicOutlined /> },
  ];

  return (
    <Box>
      {/* Hero Section - 匹配Product页面的大小 */}
      <Box
        sx={{
          background: 'linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #334155 50%, #475569 75%, #64748b 100%)',
          pt: 12,
          pb: 8,
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        {/* 动态背景装饰 */}
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: `
              radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.15) 0%, transparent 50%),
              radial-gradient(circle at 80% 20%, rgba(16, 185, 129, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 40% 40%, rgba(139, 92, 246, 0.1) 0%, transparent 50%)
            `,
            pointerEvents: 'none',
          }}
        />

        {/* 网格背景 */}
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundImage: `
              linear-gradient(rgba(255,255,255,0.02) 1px, transparent 1px),
              linear-gradient(90deg, rgba(255,255,255,0.02) 1px, transparent 1px)
            `,
            backgroundSize: '50px 50px',
            pointerEvents: 'none',
          }}
        />

        <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 2 }}>
          <Grid container spacing={6} alignItems="center">
            <Grid size={{ xs: 12, md: 6 }}>
              <Box sx={{ color: 'white' }}>
                <Chip
                  label="关于极优云创"
                  sx={{
                    mb: 3,
                    px: 3,
                    py: 1,
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    color: '#60a5fa',
                    border: '1px solid rgba(59, 130, 246, 0.2)',
                    fontSize: '0.9rem',
                    fontWeight: 600,
                  }}
                />
                
                <Typography
                  variant="h1"
                  sx={{
                    fontSize: { xs: '2.5rem', md: '4rem' },
                    fontWeight: 800,
                    mb: 3,
                    lineHeight: 1.1,
                    background: 'linear-gradient(135deg, #ffffff 0%, #e2e8f0 50%, #cbd5e1 100%)',
                    backgroundClip: 'text',
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                  }}
                >
                  重新定义
                  <br />
                  <Box component="span" sx={{ color: '#3b82f6' }}>AI搜索优化</Box>
                </Typography>
                
                <Typography
                  variant="h5"
                  sx={{
                    fontSize: { xs: '1.1rem', md: '1.3rem' },
                    fontWeight: 400,
                    mb: 4,
                    color: '#cbd5e1',
                    lineHeight: 1.6,
                    maxWidth: 500,
                  }}
                >
                  我们致力于构建智能化的内容生态平台，让每个企业都能在AI搜索时代获得应有的展示机会。
                </Typography>

                <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} sx={{ mb: 6 }}>
                  <Button
                    variant="contained"
                    size="large"
                    startIcon={<PlayArrow />}
                    sx={{
                      px: 4,
                      py: 1.5,
                      fontSize: '1.1rem',
                      fontWeight: 600,
                      borderRadius: 2,
                      background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
                      boxShadow: '0 10px 40px rgba(59, 130, 246, 0.3)',
                      '&:hover': {
                        background: 'linear-gradient(135deg, #2563eb 0%, #1e40af 100%)',
                        transform: 'translateY(-2px)',
                        boxShadow: '0 15px 50px rgba(59, 130, 246, 0.4)',
                      },
                      transition: 'all 0.3s ease',
                    }}
                  >
                    观看介绍视频
                  </Button>
                  <Button
                    variant="outlined"
                    size="large"
                    endIcon={<ArrowForward />}
                    sx={{
                      px: 4,
                      py: 1.5,
                      fontSize: '1.1rem',
                      fontWeight: 600,
                      borderRadius: 2,
                      borderColor: 'rgba(255, 255, 255, 0.3)',
                      color: 'white',
                      '&:hover': {
                        borderColor: 'white',
                        backgroundColor: 'rgba(255, 255, 255, 0.1)',
                        transform: 'translateY(-2px)',
                      },
                      transition: 'all 0.3s ease',
                    }}
                  >
                    了解更多
                  </Button>
                </Stack>

                {/* 公司数据统计 */}
                <Grid container spacing={3} sx={{ mt: 2 }}>
                  {companyStats.map((stat, index) => (
                    <Grid size={{ xs: 6, sm: 3 }} key={index}>
                      <Box sx={{ textAlign: 'center' }}>
                        <Box sx={{ color: '#3b82f6', mb: 1 }}>
                          {stat.icon}
                        </Box>
                        <Typography
                          variant="h4"
                          sx={{
                            fontWeight: 700,
                            color: 'white',
                            fontSize: { xs: '1.5rem', md: '2rem' },
                          }}
                        >
                          {stat.value}{stat.suffix}
                        </Typography>
                        <Typography
                          variant="body2"
                          sx={{
                            color: '#94a3b8',
                            fontSize: '0.9rem',
                          }}
                        >
                          {stat.label}
                        </Typography>
                      </Box>
                    </Grid>
                  ))}
                </Grid>
              </Box>
            </Grid>

            <Grid size={{ xs: 12, md: 6 }}>
              {/* 核心价值观动态展示 */}
              <Box
                sx={{
                  position: 'relative',
                  height: 400,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                {coreValues.map((value, index) => (
                  <Fade in={activeValue === index} key={index} timeout={800}>
                    <Box
                      sx={{
                        position: activeValue === index ? 'static' : 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        opacity: activeValue === index ? 1 : 0,
                        width: '100%',
                      }}
                    >
                      <Card
                        elevation={0}
                        sx={{
                          p: 4,
                          bgcolor: 'rgba(255, 255, 255, 0.05)',
                          backdropFilter: 'blur(10px)',
                          border: `1px solid rgba(${value.color === '#3b82f6' ? '59, 130, 246' : value.color === '#10b981' ? '16, 185, 129' : value.color === '#f59e0b' ? '245, 158, 11' : '139, 92, 246'}, 0.2)`,
                          borderRadius: 3,
                          color: 'white',
                        }}
                      >
                        <Box
                          sx={{
                            width: 80,
                            height: 80,
                            borderRadius: '50%',
                            bgcolor: value.color,
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            mx: 'auto',
                            mb: 3,
                            boxShadow: `0 20px 40px ${value.color}40`,
                          }}
                        >
                          {React.cloneElement(value.icon, {
                            sx: { fontSize: 36, color: 'white' }
                          })}
                        </Box>
                        
                        <Typography variant="h4" sx={{ fontWeight: 700, mb: 2, textAlign: 'center' }}>
                          {value.title}
                        </Typography>
                        
                        <Typography variant="h6" sx={{ color: '#cbd5e1', mb: 3, textAlign: 'center' }}>
                          {value.description}
                        </Typography>

                        <List>
                          {value.features.map((feature, idx) => (
                            <ListItem key={idx} sx={{ px: 0, py: 0.5 }}>
                              <ListItemIcon>
                                <CheckCircle sx={{ color: value.color, fontSize: 20 }} />
                              </ListItemIcon>
                              <ListItemText
                                primary={feature}
                                primaryTypographyProps={{
                                  color: '#e2e8f0',
                                  fontSize: '0.9rem',
                                }}
                              />
                            </ListItem>
                          ))}
                        </List>
                      </Card>
                    </Box>
                  </Fade>
                ))}

                {/* 价值观指示器 */}
                <Stack
                  direction="row"
                  spacing={1}
                  sx={{
                    position: 'absolute',
                    bottom: -30,
                    left: '50%',
                    transform: 'translateX(-50%)',
                  }}
                >
                  {coreValues.map((_, index) => (
                    <Box
                      key={index}
                      sx={{
                        width: 12,
                        height: 12,
                        borderRadius: '50%',
                        bgcolor: activeValue === index ? '#3b82f6' : 'rgba(255, 255, 255, 0.3)',
                        transition: 'all 0.3s ease',
                        cursor: 'pointer',
                      }}
                      onClick={() => setActiveValue(index)}
                    />
                  ))}
                </Stack>
              </Box>
            </Grid>
          </Grid>
        </Container>
      </Box>

      {/* Mission & Vision Section - 突出内容展示 */}
      <Box sx={{ py: 12, bgcolor: '#fafafa', position: 'relative' }}>
        {/* 背景装饰 */}
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundImage: `
              linear-gradient(rgba(59, 130, 246, 0.05) 1px, transparent 1px),
              linear-gradient(90deg, rgba(59, 130, 246, 0.05) 1px, transparent 1px)
            `,
            backgroundSize: '40px 40px',
          }}
        />

        <Container maxWidth="xl" sx={{ position: 'relative', zIndex: 2 }}>
          <Box sx={{ textAlign: 'center', mb: 8 }}>
            <Chip
              label="使命愿景"
              sx={{
                mb: 3,
                px: 3,
                py: 1,
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                color: '#60a5fa',
                border: '1px solid rgba(59, 130, 246, 0.2)',
                fontSize: '0.9rem',
                fontWeight: 600,
              }}
            />
            <Typography
              variant="h2"
              sx={{
                fontSize: { xs: '2rem', md: '3.5rem' },
                fontWeight: 800,
                color: '#1e293b',
                mb: 3,
                lineHeight: 1.2,
              }}
            >
              我们致力于
            </Typography>
            <Typography
              variant="h5"
              sx={{
                color: '#64748b',
                maxWidth: 800,
                mx: 'auto',
                fontWeight: 400,
                lineHeight: 1.6,
              }}
            >
              构建更智能、更开放、更安全的AI搜索生态系统
            </Typography>
          </Box>

          <Grid container spacing={4}>
            {missions.map((mission, index) => (
              <Grid size={{ xs: 12, md: 6, lg: 4 }} key={index}>
                <Grow in={visibleMissions.includes(index)} timeout={600}>
                  <Box
                    sx={{
                      height: '100%',
                      p: 3,
                      transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                      position: 'relative',
                      overflow: 'hidden',
                      borderLeft: `4px solid ${mission.color}`,
                      bgcolor: 'transparent',
                      '&:hover': {
                        transform: 'translateX(8px)',
                        bgcolor: `${mission.color}08`,
                        '& .mission-icon': {
                          bgcolor: mission.color,
                          transform: 'scale(1.1)',
                        },
                        '& .mission-title': {
                          color: mission.color,
                        },
                      },
                    }}
                  >
                    {/* 背景光效 */}
                    <Box
                      className="mission-bg"
                      sx={{
                        position: 'absolute',
                        top: -50,
                        right: -50,
                        width: 200,
                        height: 200,
                        borderRadius: '50%',
                        background: `radial-gradient(circle, ${mission.color}20 0%, transparent 70%)`,
                        opacity: 0.3,
                        transition: 'all 0.6s ease',
                        filter: 'blur(40px)',
                      }}
                    />

                    <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 3 }}>
                      <Box
                        className="mission-icon"
                        sx={{
                          width: 56,
                          height: 56,
                          borderRadius: '50%',
                          bgcolor: `${mission.color}15`,
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          flexShrink: 0,
                          transition: 'all 0.3s ease',
                        }}
                      >
                        {React.cloneElement(mission.icon, {
                          sx: { fontSize: 28, color: mission.color }
                        })}
                      </Box>

                      <Box sx={{ flex: 1 }}>
                        <Typography
                          variant="h5"
                          className="mission-title"
                          sx={{
                            fontWeight: 700,
                            mb: 0.5,
                            color: '#1e293b',
                            transition: 'color 0.3s ease',
                          }}
                        >
                          {mission.title}
                        </Typography>
                        
                        <Typography
                          variant="subtitle2"
                          sx={{
                            color: '#64748b',
                            fontWeight: 500,
                            mb: 2,
                          }}
                        >
                          {mission.subtitle}
                        </Typography>

                        <Typography
                          variant="body2"
                          sx={{
                            color: '#475569',
                            lineHeight: 1.8,
                            mb: 2,
                          }}
                        >
                          {mission.description}
                        </Typography>

                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                          <Chip
                            label={mission.impact}
                            size="small"
                            variant="outlined"
                            sx={{
                              borderColor: mission.color,
                              color: mission.color,
                              fontWeight: 600,
                              fontSize: '0.75rem',
                            }}
                          />
                        </Box>

                        <Stack spacing={1}>
                          {mission.keyPoints.map((point, idx) => (
                            <Box key={idx} sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <CheckCircle sx={{ fontSize: 16, color: mission.color }} />
                              <Typography
                                variant="body2"
                                sx={{
                                  color: '#64748b',
                                  fontSize: '0.85rem',
                                }}
                              >
                                {point}
                              </Typography>
                            </Box>
                          ))}
                        </Stack>
                      </Box>
                    </Box>
                  </Box>
                </Grow>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>

      {/* Summary Section - 强调公司愿景 */}
      <Box
        sx={{
          py: 12,
          bgcolor: 'white',
          position: 'relative',
        }}
      >
        {/* 动态背景 - 移除动画以避免无限滚动 */}
        <Box
          sx={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            width: '150%',
            height: '150%',
            transform: 'translate(-50%, -50%)',
            background: 'conic-gradient(from 180deg at 50% 50%, #3b82f6 0deg, #1d4ed8 60deg, #10b981 120deg, #f59e0b 180deg, #8b5cf6 240deg, #3b82f6 360deg)',
            opacity: 0.05,
            filter: 'blur(100px)',
            pointerEvents: 'none',
          }}
        />

        <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 2 }}>
          <Card
            elevation={0}
            sx={{
              p: { xs: 6, md: 10 },
              textAlign: 'center',
              bgcolor: '#f8fafc',
              border: '1px solid rgba(226, 232, 240, 0.8)',
              borderRadius: 4,
              color: '#1e293b',
              position: 'relative',
              overflow: 'hidden',
              boxShadow: '0 4px 6px rgba(0, 0, 0, 0.05)',
            }}
          >
            {/* 装饰元素 */}
            <Box
              sx={{
                position: 'absolute',
                top: -100,
                right: -100,
                width: 200,
                height: 200,
                borderRadius: '50%',
                background: 'radial-gradient(circle, rgba(59, 130, 246, 0.3) 0%, transparent 70%)',
                filter: 'blur(60px)',
              }}
            />
            <Box
              sx={{
                position: 'absolute',
                bottom: -80,
                left: -80,
                width: 160,
                height: 160,
                borderRadius: '50%',
                background: 'radial-gradient(circle, rgba(16, 185, 129, 0.2) 0%, transparent 70%)',
                filter: 'blur(50px)',
              }}
            />

            <Typography
              variant="h2"
              sx={{
                fontWeight: 800,
                mb: 4,
                fontSize: { xs: '2rem', md: '3rem' },
                position: 'relative',
                zIndex: 1,
                background: 'linear-gradient(135deg, #1e293b 0%, #3b82f6 50%, #10b981 100%)',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
              }}
            >
              引领AI搜索新时代
            </Typography>
            
            <Typography
              variant="h5"
              sx={{
                color: '#64748b',
                maxWidth: 800,
                mx: 'auto',
                lineHeight: 1.8,
                mb: 6,
                fontWeight: 400,
                position: 'relative',
                zIndex: 1,
              }}
            >
              我们的使命相互支撑、彼此成就，共同构建完整的价值体系。
              极优云创将以创新为驱动，以用户为中心，推动AI搜索生态的健康发展，
              为企业和社会创造更大价值。
            </Typography>

            <Stack
              direction="row"
              spacing={3}
              justifyContent="center"
              sx={{ position: 'relative', zIndex: 1 }}
            >
              {[
                { icon: <DiamondOutlined />, label: '技术创新' },
                { icon: <StarOutline />, label: '用户至上' },
                { icon: <AutoAwesome />, label: '生态共建' },
              ].map((item, i) => (
                <Box
                  key={i}
                  sx={{
                    textAlign: 'center',
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      transform: 'scale(1.1)',
                    },
                  }}
                >
                  <Box
                    sx={{
                      width: 60,
                      height: 60,
                      borderRadius: '50%',
                      bgcolor: '#3b82f6',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      color: 'white',
                      mx: 'auto',
                      mb: 1,
                      boxShadow: '0 10px 30px rgba(59, 130, 246, 0.3)',
                    }}
                  >
                    {item.icon}
                  </Box>
                  <Typography variant="body2" sx={{ color: '#64748b', fontWeight: 500 }}>
                    {item.label}
                  </Typography>
                </Box>
              ))}
            </Stack>
          </Card>
        </Container>
      </Box>

      <Footer />
    </Box>
  );
}

export default About;