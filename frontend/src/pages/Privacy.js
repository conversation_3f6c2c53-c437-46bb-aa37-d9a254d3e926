import React from 'react';
import {
  Box,
  Container,
  Typography,
  Paper,
  Divider,
  List,
  ListItem,
  ListItemText,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from '@mui/material';
import Footer from '../components/layout/Footer';

function Privacy() {
  const sections = [
    {
      title: '1. 信息收集',
      subsections: [
        {
          subtitle: '1.1 我们收集的信息类型',
          content: [
            '账户信息：包括您的姓名、邮箱地址、手机号码、公司名称等。',
            '使用信息：包括您的登录记录、浏览历史、搜索查询、点击行为等。',
            '设备信息：包括IP地址、浏览器类型、操作系统、设备标识符等。',
            '内容信息：您在平台上创建、上传或分享的内容。',
            '支付信息：订购服务时的支付相关信息（支付处理由第三方完成）。'
          ]
        },
        {
          subtitle: '1.2 信息收集方式',
          content: [
            '直接收集：您注册账户或使用服务时主动提供的信息。',
            '自动收集：通过Cookies和类似技术自动收集的信息。',
            '第三方来源：从合作伙伴或公开渠道获取的信息。'
          ]
        }
      ]
    },
    {
      title: '2. 信息使用',
      subsections: [
        {
          subtitle: '2.1 我们如何使用您的信息',
          content: [
            '提供、维护和改进我们的服务。',
            '处理交易和发送相关通知。',
            '回应您的咨询和提供客户支持。',
            '发送服务更新、营销信息（您可选择退订）。',
            '检测、预防欺诈和其他非法活动。',
            '遵守法律义务和保护我们的合法权益。'
          ]
        },
        {
          subtitle: '2.2 数据分析',
          content: [
            '我们使用汇总和匿名化的数据进行统计分析。',
            '分析结果用于改善服务质量和用户体验。',
            '不会将分析数据与特定个人关联。'
          ]
        }
      ]
    },
    {
      title: '3. 信息共享',
      subsections: [
        {
          subtitle: '3.1 我们不会出售您的个人信息',
          content: [
            '我们承诺永远不会出售、出租或以其他方式商业化您的个人信息。'
          ]
        },
        {
          subtitle: '3.2 可能共享信息的情况',
          content: [
            '征得您的同意：在获得您明确同意的情况下共享。',
            '服务提供商：与帮助我们提供服务的第三方服务商共享必要信息。',
            '法律要求：根据法律法规、法院命令或政府要求披露。',
            '保护权益：为保护平台、用户或公众的权益、财产或安全。',
            '业务转让：在合并、收购或资产出售时转移相关信息。'
          ]
        }
      ]
    },
    {
      title: '4. 信息安全',
      subsections: [
        {
          subtitle: '4.1 安全措施',
          content: [
            '采用业界标准的加密技术保护数据传输。',
            '实施访问控制，限制员工访问个人信息。',
            '定期进行安全审计和漏洞测试。',
            '建立数据泄露应急响应机制。'
          ]
        },
        {
          subtitle: '4.2 用户责任',
          content: [
            '请妥善保管您的账户密码。',
            '定期更新密码，使用强密码。',
            '不要与他人分享账户信息。',
            '发现异常情况及时通知我们。'
          ]
        }
      ]
    },
    {
      title: '5. Cookie政策',
      subsections: [
        {
          subtitle: '5.1 Cookie的使用',
          content: [
            '必要Cookie：确保网站正常运行的基本功能。',
            '功能Cookie：记住您的偏好设置。',
            '分析Cookie：帮助我们了解用户如何使用网站。',
            '营销Cookie：用于提供相关的广告内容。'
          ]
        },
        {
          subtitle: '5.2 管理Cookie',
          content: [
            '您可以通过浏览器设置管理或禁用Cookie。',
            '禁用某些Cookie可能影响网站功能的正常使用。'
          ]
        }
      ]
    },
    {
      title: '6. 用户权利',
      subsections: [
        {
          subtitle: '6.1 您的权利',
          content: [
            '访问权：查看我们收集的关于您的信息。',
            '更正权：更正不准确或不完整的信息。',
            '删除权：要求删除您的个人信息。',
            '限制处理权：限制我们处理您的信息。',
            '数据可携带权：获取您的数据副本。',
            '反对权：反对某些数据处理活动。'
          ]
        },
        {
          subtitle: '6.2 行使权利',
          content: [
            '您可以通过账户设置或联系我们行使上述权利。',
            '我们将在30天内回应您的请求。',
            '某些情况下，法律要求可能限制这些权利。'
          ]
        }
      ]
    },
    {
      title: '7. 儿童隐私',
      content: [
        '本平台不面向13岁以下的儿童。',
        '我们不会故意收集儿童的个人信息。',
        '如发现误收集儿童信息，我们将立即删除。',
        '家长如发现孩子未经同意提供了个人信息，请联系我们。'
      ]
    },
    {
      title: '8. 国际数据传输',
      content: [
        '您的信息可能被传输到其他国家或地区。',
        '我们确保采取适当的保护措施。',
        '跨境传输遵守相关法律法规要求。'
      ]
    },
    {
      title: '9. 隐私政策更新',
      content: [
        '我们可能不时更新本隐私政策。',
        '重大更改将通过邮件或网站通知您。',
        '继续使用服务即表示接受更新后的政策。',
        '您可以随时查看最新版本的隐私政策。'
      ]
    }
  ];

  const dataRetentionTable = [
    { type: '账户信息', period: '账户存续期间 + 3年', purpose: '提供服务、法律合规' },
    { type: '交易记录', period: '7年', purpose: '财务记录、法律要求' },
    { type: '使用日志', period: '1年', purpose: '服务改进、安全分析' },
    { type: 'Cookie数据', period: '最长1年', purpose: '用户体验优化' },
    { type: '营销数据', period: '直到用户退订', purpose: '营销推广' },
  ];

  return (
    <Box sx={{ minHeight: '100vh', bgcolor: '#f5f7fa', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Box
        sx={{
          background: 'linear-gradient(135deg, #1e293b 0%, #334155 100%)',
          py: 8,
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        <Container maxWidth="lg">
          <Typography
            variant="h2"
            sx={{
              fontWeight: 700,
              color: 'white',
              textAlign: 'center',
              mb: 2,
              fontSize: { xs: '2rem', md: '3rem' },
            }}
          >
            隐私政策
          </Typography>
          <Typography
            variant="h6"
            sx={{
              color: 'rgba(255, 255, 255, 0.8)',
              textAlign: 'center',
              maxWidth: 600,
              mx: 'auto',
            }}
          >
            我们重视并保护您的隐私
          </Typography>
        </Container>
      </Box>

      {/* Content */}
      <Container maxWidth="lg" sx={{ py: 6, flex: 1 }}>
        <Paper
          elevation={0}
          sx={{
            p: { xs: 3, md: 6 },
            borderRadius: 3,
            border: '1px solid #e2e8f0',
          }}
        >
          <Box sx={{ mb: 4 }}>
            <Typography variant="body1" sx={{ color: '#64748b', mb: 3 }}>
              最后更新日期：2024年12月
            </Typography>
            <Typography variant="body1" sx={{ color: '#475569', lineHeight: 1.8 }}>
              AI搜索优化平台（以下简称"我们"或"本平台"）非常重视用户的隐私保护。
              本隐私政策详细说明了我们如何收集、使用、共享和保护您的个人信息。
              请您仔细阅读本政策，了解我们的隐私保护措施。
            </Typography>
          </Box>

          <Divider sx={{ my: 4 }} />

          {sections.map((section, index) => (
            <Box key={index} sx={{ mb: 4 }}>
              <Typography
                variant="h5"
                sx={{
                  fontWeight: 600,
                  color: '#1e293b',
                  mb: 2,
                }}
              >
                {section.title}
              </Typography>
              
              {section.subsections ? (
                section.subsections.map((subsection, subIdx) => (
                  <Box key={subIdx} sx={{ mb: 3 }}>
                    <Typography
                      variant="h6"
                      sx={{
                        fontWeight: 500,
                        color: '#334155',
                        mb: 1,
                      }}
                    >
                      {subsection.subtitle}
                    </Typography>
                    <List>
                      {subsection.content.map((item, idx) => (
                        <ListItem key={idx} sx={{ px: 0, py: 1 }}>
                          <ListItemText
                            primary={item}
                            primaryTypographyProps={{
                              sx: { color: '#475569', lineHeight: 1.8 }
                            }}
                          />
                        </ListItem>
                      ))}
                    </List>
                  </Box>
                ))
              ) : (
                <List>
                  {section.content.map((item, idx) => (
                    <ListItem key={idx} sx={{ px: 0, py: 1 }}>
                      <ListItemText
                        primary={item}
                        primaryTypographyProps={{
                          sx: { color: '#475569', lineHeight: 1.8 }
                        }}
                      />
                    </ListItem>
                  ))}
                </List>
              )}
            </Box>
          ))}

          <Divider sx={{ my: 4 }} />

          {/* Data Retention Table */}
          <Box sx={{ mb: 4 }}>
            <Typography
              variant="h5"
              sx={{
                fontWeight: 600,
                color: '#1e293b',
                mb: 3,
              }}
            >
              10. 数据保留期限
            </Typography>
            <TableContainer component={Paper} variant="outlined">
              <Table>
                <TableHead>
                  <TableRow sx={{ bgcolor: '#f8fafc' }}>
                    <TableCell sx={{ fontWeight: 600 }}>数据类型</TableCell>
                    <TableCell sx={{ fontWeight: 600 }}>保留期限</TableCell>
                    <TableCell sx={{ fontWeight: 600 }}>保留目的</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {dataRetentionTable.map((row, index) => (
                    <TableRow key={index}>
                      <TableCell>{row.type}</TableCell>
                      <TableCell>{row.period}</TableCell>
                      <TableCell>{row.purpose}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Box>

          <Divider sx={{ my: 4 }} />

          <Box sx={{ mt: 4 }}>
            <Typography variant="h5" sx={{ fontWeight: 600, color: '#1e293b', mb: 2 }}>
              11. 联系我们
            </Typography>
            <Typography variant="body1" sx={{ color: '#475569', lineHeight: 1.8, mb: 2 }}>
              如果您对本隐私政策有任何疑问、意见或请求，请通过以下方式联系我们的隐私保护团队：
            </Typography>
            <List>
              <ListItem sx={{ px: 0, py: 1 }}>
                <ListItemText
                  primary="隐私保护邮箱：<EMAIL>"
                  primaryTypographyProps={{
                    sx: { color: '#475569' }
                  }}
                />
              </ListItem>
              <ListItem sx={{ px: 0, py: 1 }}>
                <ListItemText
                  primary="客服邮箱：<EMAIL>"
                  primaryTypographyProps={{
                    sx: { color: '#475569' }
                  }}
                />
              </ListItem>
              <ListItem sx={{ px: 0, py: 1 }}>
                <ListItemText
                  primary="客服电话：400-888-8888"
                  primaryTypographyProps={{
                    sx: { color: '#475569' }
                  }}
                />
              </ListItem>
            </List>
            <Typography variant="body1" sx={{ color: '#475569', lineHeight: 1.8, mt: 2 }}>
              我们将在收到您的请求后30天内回复。为了保护您的隐私和安全，我们可能需要验证您的身份后才能处理某些请求。
            </Typography>
          </Box>
        </Paper>
      </Container>

      {/* Footer */}
      <Footer />
    </Box>
  );
}

export default Privacy;