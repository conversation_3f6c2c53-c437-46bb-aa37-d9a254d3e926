import React from 'react';
import {
  Box,
  Container,
  Typography,
  Button,
  Paper,
} from '@mui/material';
import {
  Home,
  Refresh,
  ErrorOutline,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';

function ServerError() {
  const navigate = useNavigate();

  const handleGoHome = () => {
    navigate('/');
  };

  const handleRefresh = () => {
    window.location.reload();
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        bgcolor: 'grey.50',
        py: 4,
      }}
    >
      <Container maxWidth="md">
        <Paper
          elevation={0}
          sx={{
            p: 6,
            textAlign: 'center',
            borderRadius: 3,
            border: 1,
            borderColor: 'divider',
          }}
        >
          <ErrorOutline
            sx={{
              fontSize: 120,
              color: 'warning.main',
              mb: 3,
            }}
          />
          
          <Typography
            variant="h1"
            sx={{
              fontSize: '6rem',
              fontWeight: 700,
              color: 'warning.main',
              mb: 2,
            }}
          >
            500
          </Typography>
          
          <Typography variant="h4" sx={{ fontWeight: 600, mb: 2 }}>
            服务器错误
          </Typography>
          
          <Typography variant="body1" color="text.secondary" sx={{ mb: 4, maxWidth: 500, mx: 'auto' }}>
            抱歉，服务器遇到了一些问题。我们正在努力修复，请稍后再试。
          </Typography>

          <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
            <Button
              variant="contained"
              startIcon={<Refresh />}
              onClick={handleRefresh}
              size="large"
            >
              刷新页面
            </Button>
            
            <Button
              variant="outlined"
              startIcon={<Home />}
              onClick={handleGoHome}
              size="large"
            >
              返回首页
            </Button>
          </Box>
        </Paper>
      </Container>
    </Box>
  );
}

export default ServerError;
