import React from 'react';
import {
  Box,
  Container,
  Typography,
  Button,
  Paper,
} from '@mui/material';
import {
  Home,
  ArrowBack,
  Block,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';

function Forbidden() {
  const navigate = useNavigate();

  const handleGoHome = () => {
    navigate('/');
  };

  const handleGoBack = () => {
    navigate(-1);
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        bgcolor: 'grey.50',
        py: 4,
      }}
    >
      <Container maxWidth="md">
        <Paper
          elevation={0}
          sx={{
            p: 6,
            textAlign: 'center',
            borderRadius: 3,
            border: 1,
            borderColor: 'divider',
          }}
        >
          <Block
            sx={{
              fontSize: 120,
              color: 'error.main',
              mb: 3,
            }}
          />
          
          <Typography
            variant="h1"
            sx={{
              fontSize: '6rem',
              fontWeight: 700,
              color: 'error.main',
              mb: 2,
            }}
          >
            403
          </Typography>
          
          <Typography variant="h4" sx={{ fontWeight: 600, mb: 2 }}>
            访问被拒绝
          </Typography>
          
          <Typography variant="body1" color="text.secondary" sx={{ mb: 4, maxWidth: 500, mx: 'auto' }}>
            抱歉，您没有权限访问此页面。请联系管理员获取相应权限，或使用有权限的账户登录。
          </Typography>

          <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
            <Button
              variant="contained"
              startIcon={<Home />}
              onClick={handleGoHome}
              size="large"
            >
              返回首页
            </Button>
            
            <Button
              variant="outlined"
              startIcon={<ArrowBack />}
              onClick={handleGoBack}
              size="large"
            >
              返回上页
            </Button>
          </Box>
        </Paper>
      </Container>
    </Box>
  );
}

export default Forbidden;
