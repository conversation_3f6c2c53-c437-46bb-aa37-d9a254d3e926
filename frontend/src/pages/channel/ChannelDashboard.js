import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Button,
  Avatar,
  Chip,
  LinearProgress,
  IconButton,
  Tooltip,
  Paper,
  Divider,
} from '@mui/material';
import {
  Store,
  TrendingUp,
  Analytics,
  CloudUpload,
  Payment,
  Assessment,
  Dashboard,
  Refresh,
  MoreVert,
  CheckCircle,
  Schedule,
  AttachMoney,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';

function ChannelDashboard() {
  const navigate = useNavigate();
  const [stats, setStats] = useState({
    totalEarnings: 15680,
    contentUploaded: 245,
    activeContent: 189,
    monthlyViews: 45200
  });

  const quickActions = [
    {
      id: 1,
      title: '内容上传',
      description: '上传优质内容获得收益',
      icon: <CloudUpload />,
      color: 'primary',
      action: () => navigate('/channel/upload'),
      gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
    },
    {
      id: 2,
      title: '收益管理',
      description: '查看收益详情和提现',
      icon: <Payment />,
      color: 'success',
      action: () => navigate('/channel/earnings'),
      gradient: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)'
    },
    {
      id: 3,
      title: '数据分析',
      description: '分析内容表现和用户反馈',
      icon: <Analytics />,
      color: 'info',
      action: () => navigate('/channel/analytics'),
      gradient: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)'
    },
    {
      id: 4,
      title: '内容管理',
      description: '管理已上传的内容',
      icon: <Assessment />,
      color: 'warning',
      action: () => navigate('/channel/content-management'),
      gradient: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)'
    },
  ];

  const statCards = [
    {
      title: '总收益',
      value: `¥${stats.totalEarnings.toLocaleString()}`,
      icon: <AttachMoney />,
      color: 'success',
      change: '+15%'
    },
    {
      title: '上传内容',
      value: stats.contentUploaded,
      icon: <CloudUpload />,
      color: 'primary',
      change: '+12'
    },
    {
      title: '活跃内容',
      value: stats.activeContent,
      icon: <Assessment />,
      color: 'info',
      change: '+8'
    },
    {
      title: '月度浏览',
      value: stats.monthlyViews.toLocaleString(),
      icon: <TrendingUp />,
      color: 'warning',
      change: '+23%'
    },
  ];

  const recentContent = [
    { title: '企业SEO优化指南', status: '已审核', earnings: '¥280', views: 1250 },
    { title: '内容营销策略分析', status: '审核中', earnings: '¥0', views: 0 },
    { title: '数字化转型案例', status: '已审核', earnings: '¥450', views: 2100 },
  ];

  return (
    <Box sx={{ minHeight: '100vh', backgroundColor: '#ffffff' }}>
      {/* 页面标题 */}
      <Box sx={{ p: 3, pb: 2, borderBottom: '1px solid #e5e7eb' }}>
        <Typography variant="h4" sx={{ fontWeight: 600, mb: 0.5, display: 'flex', alignItems: 'center' }}>
          <Store sx={{ mr: 2, color: '#6b7280', fontSize: 28 }} />
          渠道商控制中心
        </Typography>
        <Typography variant="body2" color="text.secondary">
          管理您的内容和收益
        </Typography>
      </Box>

      {/* 主内容区 - 扁平化设计 */}
      <Box sx={{ p: 3 }}>
        <Box sx={{
          border: '1px solid #e5e7eb',
          borderRadius: 0,
          backgroundColor: '#ffffff',
          maxHeight: 'calc(100vh - 150px)',
          overflow: 'hidden',
        }}>
          <Box sx={{
            p: 3,
            maxHeight: 'calc(100vh - 150px)',
            overflowY: 'auto',
            '&::-webkit-scrollbar': {
              width: '6px',
            },
            '&::-webkit-scrollbar-track': {
              backgroundColor: '#f3f4f6',
            },
            '&::-webkit-scrollbar-thumb': {
              backgroundColor: '#d1d5db',
              '&:hover': {
                backgroundColor: '#9ca3af',
              },
            },
          }}>

        {/* 统计卡片 */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          {statCards.map((stat) => (
            <Grid item xs={12} sm={6} md={3} key={stat.title}>
              <Paper sx={{ 
                border: '1px solid #e5e7eb',
                borderRadius: 0,
                p: 2.5,
                height: '100%',
                transition: 'border-color 0.2s',
                '&:hover': {
                  borderColor: '#d1d5db',
                }
              }}>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                  <Box sx={{ 
                    width: 40,
                    height: 40,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    backgroundColor: '#f9fafb',
                    border: '1px solid #e5e7eb'
                  }}>
                    {React.cloneElement(stat.icon, { sx: { color: '#6b7280', fontSize: 20 } })}
                  </Box>
                  <Typography variant="caption" sx={{ 
                    color: stat.change.startsWith('+') ? '#10b981' : '#ef4444',
                    fontWeight: 500
                  }}>
                    {stat.change}
                  </Typography>
                </Box>
                <Typography variant="h5" sx={{ fontWeight: 600, mb: 0.5, color: '#111827' }}>
                  {stat.value}
                </Typography>
                <Typography variant="caption" sx={{ color: '#6b7280', textTransform: 'uppercase', letterSpacing: 0.5 }}>
                  {stat.title}
                </Typography>
              </Paper>
            </Grid>
          ))}
        </Grid>

        {/* 快速操作 */}
        <Box sx={{ 
          border: '1px solid #e5e7eb',
          borderRadius: 0,
          mb: 3,
          p: 3,
          backgroundColor: '#ffffff'
        }}>
          <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2.5, color: '#374151' }}>
            快速操作
          </Typography>
            <Grid container spacing={2}>
              {quickActions.map((action) => (
                <Grid item xs={12} sm={6} md={3} key={action.id}>
                  <Paper
                    sx={{
                      border: '1px solid #e5e7eb',
                      borderRadius: 0,
                      p: 2.5,
                      textAlign: 'center',
                      cursor: 'pointer',
                      transition: 'all 0.15s ease',
                      backgroundColor: '#ffffff',
                      '&:hover': {
                        borderColor: '#9ca3af',
                        backgroundColor: '#f9fafb',
                      }
                    }}
                    onClick={action.action}
                  >
                    <Box sx={{
                      width: 48,
                      height: 48,
                      mx: 'auto',
                      mb: 2,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      backgroundColor: '#f3f4f6',
                      border: '1px solid #e5e7eb'
                    }}>
                      {React.cloneElement(action.icon, { sx: { color: '#6b7280', fontSize: 24 } })}
                    </Box>
                    <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 0.5, color: '#111827' }}>
                      {action.title}
                    </Typography>
                    <Typography variant="caption" sx={{ color: '#6b7280', display: 'block' }}>
                      {action.description}
                    </Typography>
                  </Paper>
                </Grid>
              ))}
            </Grid>
        </Box>

        {/* 最近内容 */}
        <Box sx={{ 
          border: '1px solid #e5e7eb',
          borderRadius: 0,
          p: 3,
          backgroundColor: '#ffffff'
        }}>
          <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2.5, color: '#374151' }}>
            最近上传内容
          </Typography>
            <Grid container spacing={2}>
              {recentContent.map((content, index) => (
                <Grid item xs={12} key={index}>
                  <Box sx={{ 
                    p: 2,
                    border: '1px solid #e5e7eb',
                    borderRadius: 0,
                    backgroundColor: index % 2 === 0 ? '#ffffff' : '#f9fafb',
                    '&:hover': {
                      backgroundColor: '#f3f4f6',
                    }
                  }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                      <Box>
                        <Typography variant="body1" sx={{ fontWeight: 500, mb: 0.5, color: '#111827' }}>
                          {content.title}
                        </Typography>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                          <Typography 
                            variant="caption" 
                            sx={{ 
                              px: 1,
                              py: 0.25,
                              border: '1px solid',
                              borderColor: content.status === '已审核' ? '#10b981' : '#f59e0b',
                              color: content.status === '已审核' ? '#10b981' : '#f59e0b'
                            }}
                          >
                            {content.status}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            浏览量: {content.views.toLocaleString()}
                          </Typography>
                        </Box>
                      </Box>
                      <Box sx={{ textAlign: 'right' }}>
                        <Typography variant="body1" sx={{ fontWeight: 600, color: '#111827' }}>
                          {content.earnings}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          收益
                        </Typography>
                      </Box>
                    </Box>
                  </Box>
                </Grid>
              ))}
            </Grid>
        </Box>
          </Box>
        </Box>
      </Box>
    </Box>
  );
}

export default ChannelDashboard;
