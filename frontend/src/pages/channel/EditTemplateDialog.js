import React, { useState, useEffect } from 'react';
import {
  <PERSON>alog,
  <PERSON>alogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Switch,
  Box,
  Typography,
  Grid,
  Alert,
  Chip,
  IconButton,
  Tabs,
  Tab,
} from '@mui/material';
import {
  Close,
  Add,
  Delete,
  Settings,
  Tune,
} from '@mui/icons-material';
import templateService from '../../services/templateService';

const EditTemplateDialog = ({ open, onClose, template, onSuccess }) => {
  const [formData, setFormData] = useState({
    template_name: '',
    template_description: '',
    prompt_template: '',
    default_parameters: {},
    is_active: true,
  });

  const [parameters, setParameters] = useState([]);
  const [newParameter, setNewParameter] = useState({
    key: '',
    label: '',
    required: false,
    placeholder: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [activeTab, setActiveTab] = useState(0);

  // 参数插入功能状态
  const [selectedParameter, setSelectedParameter] = useState('');
  const [promptTextFieldRef, setPromptTextFieldRef] = useState(null);

  // 当模板数据变化时更新表单
  useEffect(() => {
    if (template) {
      setFormData({
        template_name: template.template_name || '',
        template_description: template.template_description || '',
        prompt_template: template.prompt_template || '',
        default_parameters: template.default_parameters || {},
        is_active: template.is_active !== undefined ? template.is_active : true,
      });

      // 解析默认参数
      const defaultParams = template.default_parameters || {};
      let paramArray = [];

      // 如果有 parameters 数组，直接使用
      if (defaultParams.parameters && Array.isArray(defaultParams.parameters)) {
        paramArray = defaultParams.parameters.map(param => ({
          key: param.key || '',
          label: param.label || '',
          required: param.required || false,
          placeholder: param.placeholder || ''
        }));
      } else {
        // 否则从键值对转换为新格式
        paramArray = Object.entries(defaultParams).map(([key, value]) => ({
          key,
          label: key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
          required: false,
          placeholder: typeof value === 'string' ? value : ''
        }));
      }

      setParameters(paramArray);
    }
  }, [template]);

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    setError('');
  };

  const handleAddParameter = () => {
    if (newParameter.key && newParameter.label) {
      setParameters(prev => [...prev, { ...newParameter }]);
      setNewParameter({ key: '', label: '', required: false, placeholder: '' });
    }
  };

  const handleRemoveParameter = (index) => {
    setParameters(prev => prev.filter((_, i) => i !== index));
  };

  // 插入参数到提示词模板
  const insertParameterToPrompt = () => {
    if (!selectedParameter || !promptTextFieldRef) return;

    const textField = promptTextFieldRef;
    const cursorPosition = textField.selectionStart || 0;
    const currentValue = formData.prompt_template || '';
    const parameterText = `{{${selectedParameter}}}`;

    const newValue = currentValue.slice(0, cursorPosition) + parameterText + currentValue.slice(cursorPosition);

    handleInputChange('prompt_template', newValue);

    // 重新聚焦并设置光标位置
    setTimeout(() => {
      textField.focus();
      textField.setSelectionRange(cursorPosition + parameterText.length, cursorPosition + parameterText.length);
    }, 0);

    // 重置选择
    setSelectedParameter('');
  };

  const handleSubmit = async () => {
    try {
      setLoading(true);
      setError('');

      // 验证必填字段
      if (!formData.template_name.trim()) {
        setError('模板名称不能为空');
        return;
      }
      if (formData.template_name.trim().length < 2) {
        setError('模板名称至少需要2个字符');
        return;
      }
      if (formData.template_name.trim().length > 200) {
        setError('模板名称不能超过200个字符');
        return;
      }
      if (!formData.prompt_template.trim()) {
        setError('提示词模板不能为空');
        return;
      }
      if (formData.prompt_template.trim().length < 10) {
        setError('提示词模板至少需要10个字符');
        return;
      }

      // 构建默认参数对象
      const defaultParameters = {
        parameters: parameters.map(param => ({
          key: param.key,
          label: param.label,
          required: param.required,
          placeholder: param.placeholder
        }))
      };

      const templateData = {
        ...formData,
        template_name: formData.template_name.trim(),
        template_description: formData.template_description.trim() || null,
        prompt_template: formData.prompt_template.trim(),
        default_parameters: parameters.length > 0 ? defaultParameters : null,
      };

      const response = await templateService.updateTemplate(template.id, templateData);

      if (response.success) {
        onSuccess && onSuccess(response.data);
        handleClose();
      } else {
        setError(response.message || '更新模板失败');
      }
    } catch (error) {
      console.error('更新模板失败:', error);
      setError(error.response?.data?.message || '更新模板失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setError('');
    setActiveTab(0);
    onClose();
  };

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  if (!template) return null;

  return (
    <Dialog 
      open={open} 
      onClose={handleClose} 
      maxWidth="md" 
      fullWidth
      sx={{
        '& .MuiDialog-paper': {
          borderRadius: 0,
          border: '1px solid #e5e5e5',
        }
      }}
    >
      <DialogTitle sx={{ 
        borderBottom: '1px solid #e0e0e0',
        backgroundColor: '#fff',
        p: 3
      }}>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography variant="h5" sx={{ fontWeight: 600, color: '#333' }}>
            编辑AI模板
          </Typography>
          <IconButton onClick={handleClose} sx={{ color: '#666' }}>
            <Close />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent sx={{ p: 0, backgroundColor: '#fafafa' }}>
        {error && (
          <Alert severity="error" sx={{ m: 3, mb: 0, borderRadius: 1 }}>
            {error}
          </Alert>
        )}

        {/* 标签页导航 */}
        <Box sx={{ borderBottom: 1, borderColor: 'divider', backgroundColor: '#fff' }}>
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            sx={{
              px: 3,
              '& .MuiTab-root': {
                textTransform: 'none',
                fontWeight: 500,
                fontSize: '0.95rem',
                minHeight: 48,
              }
            }}
          >
            <Tab
              icon={<Settings />}
              iconPosition="start"
              label="模板设置"
              sx={{ gap: 1 }}
            />
            <Tab
              icon={<Tune />}
              iconPosition="start"
              label="参数设置"
              sx={{ gap: 1 }}
            />
          </Tabs>
        </Box>

        <Box sx={{ p: 3 }}>
          {/* 标签页内容 */}
          {activeTab === 0 && (
            <Box>
              {/* 基本信息区域 */}
              <Box sx={{ mb: 5 }}>
                <Typography variant="h6" sx={{ fontWeight: 600, color: '#333', mb: 4, fontSize: '1.1rem' }}>
                  基本信息
                </Typography>

                {/* 第一行：模板名称 */}
                <Box sx={{ mb: 3 }}>
                  <TextField
                    fullWidth
                    label="模板名称"
                    placeholder="请输入模板名称，例如：营销文案生成器"
                    value={formData.template_name}
                    onChange={(e) => handleInputChange('template_name', e.target.value)}
                    required
                    helperText={`2-200个字符，当前：${formData.template_name.length}个字符`}
                    error={formData.template_name.length > 0 && (formData.template_name.length < 2 || formData.template_name.length > 200)}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 1,
                        backgroundColor: '#fff',
                        '& fieldset': { borderColor: '#e0e0e0' },
                        '&:hover fieldset': { borderColor: '#1976d2' },
                        '&.Mui-focused fieldset': { borderColor: '#1976d2' },
                      }
                    }}
                  />
                </Box>

                {/* 第二行：启用状态 */}
                <Box sx={{ mb: 3, display: 'flex', justifyContent: 'flex-start' }}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={formData.is_active}
                        onChange={(e) => handleInputChange('is_active', e.target.checked)}
                        color="success"
                      />
                    }
                    label="启用状态"
                    sx={{
                      m: 0,
                      '& .MuiFormControlLabel-label': {
                        fontSize: '0.95rem',
                        color: '#666',
                        fontWeight: 500
                      }
                    }}
                  />
                </Box>

                {/* 第三行：模板描述 */}
                <Box>
                  <TextField
                    fullWidth
                    label="模板描述"
                    placeholder="请简要描述模板的用途和特点"
                    value={formData.template_description}
                    onChange={(e) => handleInputChange('template_description', e.target.value)}
                    multiline
                    rows={3}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 1,
                        backgroundColor: '#fff',
                        '& fieldset': { borderColor: '#e0e0e0' },
                        '&:hover fieldset': { borderColor: '#1976d2' },
                        '&.Mui-focused fieldset': { borderColor: '#1976d2' },
                      }
                    }}
                  />
                </Box>
              </Box>

              {/* 提示词模板区域 */}
              <Box sx={{ mb: 4 }}>
                <Typography variant="h6" sx={{ fontWeight: 600, color: '#333', mb: 3, fontSize: '1.1rem' }}>
                  提示词模板
                </Typography>

                {/* 参数选择和插入工具 */}
                {parameters.length > 0 && (
                  <Box sx={{
                    mb: 2,
                    p: 2,
                    backgroundColor: '#f8f9fa',
                    borderRadius: 1,
                    border: '1px solid #e9ecef'
                  }}>
                    <Typography variant="body2" sx={{ mb: 1, fontWeight: 500, color: '#495057' }}>
                      快速插入参数
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
                      <FormControl size="small" sx={{ minWidth: 200 }}>
                        <InputLabel>选择参数</InputLabel>
                        <Select
                          value={selectedParameter}
                          label="选择参数"
                          onChange={(e) => setSelectedParameter(e.target.value)}
                        >
                          {parameters.map((param) => (
                            <MenuItem key={param.key} value={param.key}>
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                <Chip
                                  label={param.key}
                                  size="small"
                                  color="primary"
                                  variant="outlined"
                                  sx={{ fontSize: '0.75rem', fontFamily: 'monospace' }}
                                />
                                <Typography variant="body2" sx={{ color: '#666' }}>
                                  {param.label}
                                </Typography>
                                {param.required && (
                                  <Typography variant="caption" sx={{ color: '#d32f2f' }}>
                                    *必填
                                  </Typography>
                                )}
                              </Box>
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                      <Button
                        variant="contained"
                        size="small"
                        onClick={insertParameterToPrompt}
                        disabled={!selectedParameter}
                        sx={{
                          backgroundColor: '#3b82f6',
                          '&:hover': { backgroundColor: '#2563eb' }
                        }}
                      >
                        插入参数
                      </Button>
                    </Box>
                  </Box>
                )}

                <TextField
                  fullWidth
                  label="提示词模板"
                  placeholder="请输入提示词模板，可以使用 {{参数名}} 的形式定义参数变量"
                  value={formData.prompt_template}
                  onChange={(e) => handleInputChange('prompt_template', e.target.value)}
                  multiline
                  rows={8}
                  required
                  helperText={`至少需要10个字符，当前：${formData.prompt_template.length}个字符`}
                  error={formData.prompt_template.length > 0 && formData.prompt_template.length < 10}
                  inputRef={(ref) => setPromptTextFieldRef(ref)}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 1,
                      backgroundColor: '#fff',
                      '& fieldset': { borderColor: '#e0e0e0' },
                      '&:hover fieldset': { borderColor: '#1976d2' },
                      '&.Mui-focused fieldset': { borderColor: '#1976d2' },
                    },
                    '& .MuiInputBase-input': {
                      fontFamily: 'Monaco, Consolas, "Courier New", monospace',
                      fontSize: '0.9rem',
                      lineHeight: 1.6,
                    }
                  }}
                />
              </Box>
            </Box>
          )}

          {/* 第二个标签页：参数设置 */}
          {activeTab === 1 && (
            <Box>
              {/* 默认参数区域 */}
              <Box>
                <Typography variant="h6" sx={{ fontWeight: 600, color: '#333', mb: 3, fontSize: '1.1rem' }}>
                  参数设置
                </Typography>

                <Typography variant="body2" sx={{ color: '#666', mb: 3 }}>
                  为模板中的参数变量设置默认值，用户可以在使用时修改这些值
                </Typography>

                {/* 参数列表 */}
                {parameters.length > 0 && (
                  <Box sx={{ mb: 3 }}>
                    {parameters.map((param, index) => (
                      <Box
                        key={index}
                        sx={{
                          display: 'flex',
                          gap: 2,
                          mb: 2,
                          p: 2,
                          backgroundColor: '#f8f9fa',
                          borderRadius: 1,
                          border: '1px solid #e9ecef'
                        }}
                      >
                        <Chip
                          label={param.key}
                          size="small"
                          color="primary"
                          variant="outlined"
                          sx={{ minWidth: 100 }}
                        />
                        <Box sx={{ flex: 1 }}>
                          <Typography variant="body2" sx={{ fontWeight: 600, color: '#333', mb: 0.5 }}>
                            {param.label}
                          </Typography>
                          <Typography variant="caption" sx={{ color: '#666', display: 'block' }}>
                            {param.required ? '必填' : '可选'} • {param.placeholder || '无提示'}
                          </Typography>
                        </Box>
                        <IconButton
                          size="small"
                          onClick={() => handleRemoveParameter(index)}
                          sx={{ color: '#f44336' }}
                        >
                          <Delete fontSize="small" />
                        </IconButton>
                      </Box>
                    ))}
                  </Box>
                )}

                {/* 添加参数 */}
                <Box sx={{
                  p: 3,
                  backgroundColor: '#f8f9fa',
                  borderRadius: 1,
                  border: '2px dashed #dee2e6'
                }}>
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={3}>
                      <TextField
                        fullWidth
                        size="small"
                        label="参数名"
                        placeholder="例如：topic"
                        value={newParameter.key}
                        onChange={(e) => setNewParameter(prev => ({ ...prev, key: e.target.value }))}
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            backgroundColor: '#fff',
                            borderRadius: 1,
                          }
                        }}
                      />
                    </Grid>
                    <Grid item xs={12} sm={3}>
                      <TextField
                        fullWidth
                        size="small"
                        label="显示标签"
                        placeholder="例如：文章主题"
                        value={newParameter.label}
                        onChange={(e) => setNewParameter(prev => ({ ...prev, label: e.target.value }))}
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            backgroundColor: '#fff',
                            borderRadius: 1,
                          }
                        }}
                      />
                    </Grid>
                    <Grid item xs={12} sm={4}>
                      <TextField
                        fullWidth
                        size="small"
                        label="提示文本"
                        placeholder="例如：请输入文章主题"
                        value={newParameter.placeholder}
                        onChange={(e) => setNewParameter(prev => ({ ...prev, placeholder: e.target.value }))}
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            backgroundColor: '#fff',
                            borderRadius: 1,
                          }
                        }}
                      />
                    </Grid>
                    <Grid item xs={12} sm={2}>
                      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                        <FormControlLabel
                          control={
                            <Switch
                              size="small"
                              checked={newParameter.required}
                              onChange={(e) => setNewParameter(prev => ({ ...prev, required: e.target.checked }))}
                            />
                          }
                          label="必填"
                          sx={{ m: 0, '& .MuiFormControlLabel-label': { fontSize: '0.8rem' } }}
                        />
                        <Button
                          fullWidth
                          variant="outlined"
                          size="small"
                          startIcon={<Add />}
                          onClick={handleAddParameter}
                          disabled={!newParameter.key || !newParameter.label}
                          sx={{
                            borderRadius: 1,
                            textTransform: 'none',
                            height: '32px',
                            fontSize: '0.8rem'
                          }}
                        >
                          添加
                        </Button>
                      </Box>
                    </Grid>
                  </Grid>

                  {parameters.length === 0 && (
                    <Typography
                      variant="body2"
                      sx={{
                        color: '#999',
                        textAlign: 'center',
                        mt: 2,
                        fontStyle: 'italic'
                      }}
                    >
                      暂无参数，点击上方添加按钮添加参数
                    </Typography>
                  )}
                </Box>
              </Box>
            </Box>
          )}
        </Box>
      </DialogContent>

      <DialogActions sx={{
        p: 3,
        borderTop: '1px solid #e0e0e0',
        backgroundColor: '#fff',
        gap: 2
      }}>
        <Button
          onClick={handleClose}
          variant="text"
          sx={{
            color: '#666',
            borderRadius: 0.5,
            '&:hover': { backgroundColor: '#f5f5f5' }
          }}
        >
          取消
        </Button>
        <Button
          onClick={handleSubmit}
          variant="text"
          disabled={loading}
          sx={{
            color: '#333',
            borderRadius: 0.5,
            '&:hover': { backgroundColor: '#f5f5f5' }
          }}
        >
          {loading ? '更新中...' : '保存更改'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default EditTemplateDialog;
