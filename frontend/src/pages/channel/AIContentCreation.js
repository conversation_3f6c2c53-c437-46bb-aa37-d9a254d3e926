import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Typography,
  Paper,
  IconButton,
  TextField,
  Button,
  Divider,
  Grid,
  Card,
  CardContent,
  Tabs,
  Tab,
  Chip,
  Alert,
  Snackbar,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Tooltip
} from '@mui/material';
import {
  Send,
  AutoAwesome,
  Settings,
  SmartToy,
  ContentCopy,
  Download,
  Save,
  Refresh,
  Psychology,
  LibraryBooks,
  Chat,
  Add,
  Tune,
  Storage
} from '@mui/icons-material';

import ConversationManager from '../../components/conversation/ConversationManager';
import KnowledgeManager from '../../components/knowledge/KnowledgeManager';
import ApiService from '../../services/api';

function AIContentCreation() {
  // 核心状态
  const [currentConversationId, setCurrentConversationId] = useState(null);
  const [inputMessage, setInputMessage] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedContent, setGeneratedContent] = useState('');

  // 界面状态
  const [activeTab, setActiveTab] = useState(0);
  const [showSettings, setShowSettings] = useState(false);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });

  // 知识库状态
  const [selectedKnowledgeBases, setSelectedKnowledgeBases] = useState([]);
  const [knowledgeEnabled, setKnowledgeEnabled] = useState(false);

  // 模板状态
  const [selectedTemplate, setSelectedTemplate] = useState('');
  const [templateParameters, setTemplateParameters] = useState({});

  // AI设置
  const [aiSettings, setAiSettings] = useState({
    model: 'doubao',
    temperature: 0.7,
    maxTokens: 2000,
    tone: 'professional',
    length: 'medium'
  });

  const messagesEndRef = useRef(null);

  // 处理对话选择
  const handleConversationSelect = (conversationId) => {
    setCurrentConversationId(conversationId);
    setGeneratedContent('');

    if (conversationId) {
      loadConversationHistory(conversationId);
    }
  };

  // 加载对话历史
  const loadConversationHistory = async (conversationId) => {
    try {
      const response = await ApiService.get(`/api/v1/conversations/${conversationId}`);
      const conversation = response.data;

      // 设置知识库
      if (conversation.knowledge_bases) {
        setSelectedKnowledgeBases(conversation.knowledge_bases);
        setKnowledgeEnabled(conversation.knowledge_bases.length > 0);
      }

      // 设置模板参数
      if (conversation.template_parameters) {
        setTemplateParameters(conversation.template_parameters);
      }

      // 显示最后的AI回复作为生成内容
      const lastAssistantMessage = conversation.messages
        .filter(msg => msg.message_type === 'ASSISTANT')
        .pop();

      if (lastAssistantMessage) {
        setGeneratedContent(lastAssistantMessage.content);
      }

    } catch (error) {
      console.error('加载对话历史失败:', error);
      setSnackbar({
        open: true,
        message: '加载对话历史失败',
        severity: 'error'
      });
    }
  };

  // 创建新对话
  const handleNewConversation = async () => {
    try {
      const response = await ApiService.post('/api/v1/conversations', {
        title: `新对话 ${new Date().toLocaleString()}`,
        template_id: selectedTemplate || null,
        template_parameters: templateParameters,
        knowledge_bases: knowledgeEnabled ? selectedKnowledgeBases : []
      });

      const newConversation = response.data;
      setCurrentConversationId(newConversation.id);
      setGeneratedContent('');

      setSnackbar({
        open: true,
        message: '新对话创建成功',
        severity: 'success'
      });

    } catch (error) {
      console.error('创建对话失败:', error);
      setSnackbar({
        open: true,
        message: '创建对话失败',
        severity: 'error'
      });
    }
  };

  // 发送消息并生成内容
  const handleSendMessage = async () => {
    if (!inputMessage.trim() || isGenerating) return;

    // 如果没有当前对话，先创建一个
    let conversationId = currentConversationId;
    if (!conversationId) {
      try {
        const response = await ApiService.post('/api/v1/conversations', {
          title: inputMessage.slice(0, 50) + '...',
          template_id: selectedTemplate || null,
          template_parameters: templateParameters,
          knowledge_bases: knowledgeEnabled ? selectedKnowledgeBases : []
        });
        conversationId = response.data.id;
        setCurrentConversationId(conversationId);
      } catch (error) {
        setSnackbar({
          open: true,
          message: '创建对话失败',
          severity: 'error'
        });
        return;
      }
    }

    setIsGenerating(true);
    setGeneratedContent('');

    try {
      // 使用V2版本的API
      const response = await fetch('/api/v1/ai/content/generate/v2', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('ai_seo_auth_token')}`
        },
        body: JSON.stringify({
          content_type: 'article',
          topic: inputMessage,
          keywords: inputMessage.split(/\s+/).filter(word => word.length > 0).slice(0, 5).concat(['内容', 'AI']).slice(0, 3),
          conversation_id: conversationId,
          template_id: selectedTemplate || null,
          template_parameters: templateParameters,
          knowledge_bases: knowledgeEnabled ? selectedKnowledgeBases : [],
          is_first_request: !currentConversationId,
          ai_model: aiSettings.model,
          tone: aiSettings.tone,
          length: aiSettings.length
        })
      });

      if (!response.ok) {
        throw new Error('API调用失败');
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = '';

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6));

              if (data.type === 'content') {
                setGeneratedContent(prev => prev + data.content);
              } else if (data.type === 'final') {
                setGeneratedContent(data.generated_content || '');
              } else if (data.type === 'error') {
                throw new Error(data.error || '生成失败');
              }
            } catch (e) {
              console.error('解析SSE数据失败:', e);
            }
          }
        }
      }

      setInputMessage('');

    } catch (error) {
      console.error('生成内容失败:', error);
      setSnackbar({
        open: true,
        message: '生成内容失败，请稍后重试',
        severity: 'error'
      });
    } finally {
      setIsGenerating(false);
    }
  };

  // 复制内容
  const handleCopyContent = () => {
    navigator.clipboard.writeText(generatedContent);
    setSnackbar({
      open: true,
      message: '内容已复制到剪贴板',
      severity: 'success'
    });
  };

  // 保存内容
  const handleSaveContent = () => {
    const blob = new Blob([generatedContent], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `AI生成内容_${new Date().toISOString().slice(0, 10)}.txt`;
    a.click();
    URL.revokeObjectURL(url);

    setSnackbar({
      open: true,
      message: '内容已保存',
      severity: 'success'
    });
  };

  return (
    <Box sx={{
      minHeight: '100vh',
      backgroundColor: '#f8fafc',
      display: 'flex',
      flexDirection: 'column'
    }}>
      {/* 顶部工具栏 */}
      <Paper sx={{
        p: 2,
        mb: 2,
        borderRadius: 0,
        borderBottom: '1px solid #e2e8f0',
        backgroundColor: '#fff'
      }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <SmartToy sx={{ color: '#3b82f6', fontSize: 28 }} />
            <Typography variant="h5" sx={{ fontWeight: 600, color: '#1e293b' }}>
              AI内容创作
            </Typography>
          </Box>

          <Box sx={{ display: 'flex', gap: 1 }}>
            <Button
              variant="outlined"
              startIcon={<Add />}
              onClick={handleNewConversation}
              sx={{ borderRadius: 2 }}
            >
              新对话
            </Button>
            <IconButton
              onClick={() => setShowSettings(true)}
              sx={{ color: '#64748b' }}
            >
              <Settings />
            </IconButton>
          </Box>
        </Box>
      </Paper>

      {/* 主要内容区域 */}
      <Box sx={{ flex: 1, display: 'flex', px: 2, pb: 2, gap: 2 }}>
        {/* 左侧：对话管理 */}
        <Paper sx={{
          width: 320,
          borderRadius: 2,
          overflow: 'hidden',
          border: '1px solid #e2e8f0'
        }}>
          <Tabs
            value={activeTab}
            onChange={(e, newValue) => setActiveTab(newValue)}
            sx={{
              borderBottom: '1px solid #e2e8f0',
              '& .MuiTab-root': {
                textTransform: 'none',
                fontWeight: 500,
                minHeight: 48
              }
            }}
          >
            <Tab
              icon={<Chat />}
              iconPosition="start"
              label="对话"
              sx={{ gap: 1 }}
            />
            <Tab
              icon={<LibraryBooks />}
              iconPosition="start"
              label="知识库"
              sx={{ gap: 1 }}
            />
          </Tabs>

          <Box sx={{ height: 'calc(100vh - 200px)', overflow: 'hidden' }}>
            {activeTab === 0 && (
              <ConversationManager
                onConversationSelect={handleConversationSelect}
                currentConversationId={currentConversationId}
              />
            )}
            {activeTab === 1 && (
              <KnowledgeManager
                selectedKnowledgeBases={selectedKnowledgeBases}
                onKnowledgeBasesChange={setSelectedKnowledgeBases}
                knowledgeEnabled={knowledgeEnabled}
                onKnowledgeEnabledChange={setKnowledgeEnabled}
              />
            )}
          </Box>
        </Paper>

        {/* 右侧：创作区域 */}
        <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column', gap: 2 }}>
          {/* 生成内容显示区 */}
          <Paper sx={{
            flex: 1,
            borderRadius: 2,
            overflow: 'hidden',
            border: '1px solid #e2e8f0',
            display: 'flex',
            flexDirection: 'column'
          }}>
            {/* 内容头部 */}
            <Box sx={{
              p: 2,
              borderBottom: '1px solid #e2e8f0',
              backgroundColor: '#f8fafc',
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center'
            }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Psychology sx={{ color: '#3b82f6' }} />
                <Typography variant="h6" sx={{ fontWeight: 600, color: '#1e293b' }}>
                  AI生成内容
                </Typography>
                {isGenerating && (
                  <CircularProgress size={16} sx={{ ml: 1 }} />
                )}
              </Box>

              {generatedContent && (
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Tooltip title="复制内容">
                    <IconButton
                      size="small"
                      onClick={handleCopyContent}
                      sx={{ color: '#64748b' }}
                    >
                      <ContentCopy fontSize="small" />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="保存内容">
                    <IconButton
                      size="small"
                      onClick={handleSaveContent}
                      sx={{ color: '#64748b' }}
                    >
                      <Download fontSize="small" />
                    </IconButton>
                  </Tooltip>
                </Box>
              )}
            </Box>

            {/* 内容显示区 */}
            <Box sx={{
              flex: 1,
              p: 3,
              overflow: 'auto',
              backgroundColor: '#fff'
            }}>
              {isGenerating ? (
                <Box sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                  height: '100%',
                  color: '#64748b'
                }}>
                  <AutoAwesome sx={{ fontSize: 48, mb: 2, color: '#3b82f6' }} />
                  <Typography variant="h6" sx={{ mb: 1 }}>
                    AI正在创作中...
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    请稍候，正在为您生成优质内容
                  </Typography>
                </Box>
              ) : generatedContent ? (
                <Box>
                  <Typography
                    variant="body1"
                    sx={{
                      whiteSpace: 'pre-wrap',
                      lineHeight: 1.8,
                      color: '#334155',
                      fontSize: '0.95rem'
                    }}
                  >
                    {generatedContent}
                  </Typography>
                </Box>
              ) : (
                <Box sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                  height: '100%',
                  color: '#94a3b8'
                }}>
                  <SmartToy sx={{ fontSize: 64, mb: 2 }} />
                  <Typography variant="h6" sx={{ mb: 1 }}>
                    开始AI创作
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center' }}>
                    在下方输入您的创作需求<br />
                    AI将为您生成高质量内容
                  </Typography>
                </Box>
              )}
            </Box>
          </Paper>

          {/* 输入区域 */}
          <Paper sx={{
            borderRadius: 2,
            border: '1px solid #e2e8f0',
            overflow: 'hidden'
          }}>
            <Box sx={{ p: 2 }}>
              <TextField
                fullWidth
                multiline
                rows={3}
                placeholder="描述您想要创作的内容，例如：写一篇关于人工智能发展趋势的文章..."
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                variant="outlined"
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                    backgroundColor: '#f8fafc',
                    '& fieldset': {
                      borderColor: '#e2e8f0',
                    },
                    '&:hover fieldset': {
                      borderColor: '#3b82f6',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: '#3b82f6',
                    },
                  },
                  '& .MuiInputBase-input': {
                    fontSize: '0.95rem',
                    lineHeight: 1.6,
                  }
                }}
                onKeyPress={(e) => {
                  if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                    e.preventDefault();
                    handleSendMessage();
                  }
                }}
              />

              <Box sx={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                mt: 2
              }}>
                <Box sx={{ display: 'flex', gap: 1 }}>
                  {knowledgeEnabled && selectedKnowledgeBases.length > 0 && (
                    <Chip
                      icon={<Storage />}
                      label={`${selectedKnowledgeBases.length}个知识库`}
                      size="small"
                      color="primary"
                      variant="outlined"
                    />
                  )}
                  {selectedTemplate && (
                    <Chip
                      icon={<Tune />}
                      label="使用模板"
                      size="small"
                      color="secondary"
                      variant="outlined"
                    />
                  )}
                </Box>

                <Button
                  variant="contained"
                  endIcon={<Send />}
                  onClick={handleSendMessage}
                  disabled={!inputMessage.trim() || isGenerating}
                  sx={{
                    borderRadius: 2,
                    px: 3,
                    backgroundColor: '#3b82f6',
                    '&:hover': {
                      backgroundColor: '#2563eb',
                    }
                  }}
                >
                  {isGenerating ? '生成中...' : '开始创作'}
                </Button>
              </Box>
            </Box>
          </Paper>
        </Box>
      </Box>

      {/* AI设置对话框 */}
      <Dialog
        open={showSettings}
        onClose={() => setShowSettings(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>AI设置</DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 1 }}>
            <FormControl fullWidth sx={{ mb: 3 }}>
              <InputLabel>AI模型</InputLabel>
              <Select
                value={aiSettings.model}
                label="AI模型"
                onChange={(e) => setAiSettings(prev => ({ ...prev, model: e.target.value }))}
              >
                <MenuItem value="doubao">豆包</MenuItem>
                <MenuItem value="gpt-3.5-turbo">GPT-3.5</MenuItem>
                <MenuItem value="gpt-4">GPT-4</MenuItem>
              </Select>
            </FormControl>

            <FormControl fullWidth sx={{ mb: 3 }}>
              <InputLabel>语气风格</InputLabel>
              <Select
                value={aiSettings.tone}
                label="语气风格"
                onChange={(e) => setAiSettings(prev => ({ ...prev, tone: e.target.value }))}
              >
                <MenuItem value="professional">专业</MenuItem>
                <MenuItem value="casual">轻松</MenuItem>
                <MenuItem value="formal">正式</MenuItem>
                <MenuItem value="creative">创意</MenuItem>
              </Select>
            </FormControl>

            <FormControl fullWidth sx={{ mb: 3 }}>
              <InputLabel>内容长度</InputLabel>
              <Select
                value={aiSettings.length}
                label="内容长度"
                onChange={(e) => setAiSettings(prev => ({ ...prev, length: e.target.value }))}
              >
                <MenuItem value="short">简短</MenuItem>
                <MenuItem value="medium">中等</MenuItem>
                <MenuItem value="long">详细</MenuItem>
              </Select>
            </FormControl>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowSettings(false)}>
            取消
          </Button>
          <Button
            onClick={() => setShowSettings(false)}
            variant="contained"
          >
            保存设置
          </Button>
        </DialogActions>
      </Dialog>

      {/* 消息提示 */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={3000}
        onClose={() => setSnackbar(prev => ({ ...prev, open: false }))}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert
          onClose={() => setSnackbar(prev => ({ ...prev, open: false }))}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default AIContentCreation;

export default AIContentCreation;