import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Typography,
  Button,
  Chip,
  TextField,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tabs,
  Tab,
  Alert,
  Snackbar,
  Badge,
  LinearProgress,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Dialog,
  DialogContent,
  DialogActions,
  IconButton,
} from '@mui/material';
import {
  Search,
  Refresh,
  Schedule,
  AttachMoney,
  Visibility,
  Assignment,
  ChevronLeft,
  ChevronRight,
  Send,
  AutoAwesome,
  Close,
  People,
  CloudUpload,
  AttachFile,
  InsertDriveFile,
  Image,
  VideoFile,
  PictureAsPdf,
  Description,
  Archive,
  Delete,
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';
import ApiService from '../../services/api';
import { contentService } from '../../services/contentService';
import uploadService from '../../services/uploadService';

const StyledContainer = styled(Box)(({ theme }) => ({
  width: '100%',
  minHeight: '100vh',
  backgroundColor: 'white',
}));

const HeaderSection = styled(Box)(({ theme }) => ({
  backgroundColor: 'white',
  paddingBottom: 0,
}));

const ContentSection = styled(Box)(({ theme }) => ({
  backgroundColor: 'white',
}));

const StyledTabs = styled(Tabs)(({ theme }) => ({
  borderBottom: `1px solid ${theme.palette.divider}`,
  minHeight: 48,
  '& .MuiTab-root': {
    minHeight: 48,
    py: 1.5
  },
  '& .MuiTabs-indicator': {
    height: 3,
    borderRadius: '3px 3px 0 0',
  },
}));

function TaskManagement() {
  const [currentTab, setCurrentTab] = useState(0);
  const [tasks, setTasks] = useState({ direct: [], creative: [] });
  const [filteredTasks, setFilteredTasks] = useState([]);
  const [filters, setFilters] = useState({
    priceRange: 'all',
    deadline: 'all',
    keyword: '',
  });
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });
  const [selectedTask, setSelectedTask] = useState(null);
  const [showDetailDialog, setShowDetailDialog] = useState(false);
  const [showDeliveryDialog, setShowDeliveryDialog] = useState(false);
  const [deliveryFormData, setDeliveryFormData] = useState({
    provided_content_title: '',
    provided_content_text: '',
    provided_content_files: [],
    delivery_notes: ''
  });
  const [uploadedFiles, setUploadedFiles] = useState([]);
  const [uploading, setUploading] = useState(false);
  const itemsPerPage = 4;

  // 加载已接单的任务数据
  const loadTasks = useCallback(async () => {
    try {
      setLoading(true);

      // 调用API获取内容需求列表，只获取已接单的需求
      const response = await ApiService.getChannelRequirements({
        status: 'ACCEPTED'  // 只显示已接单的需求
      });

      if (response && response.success) {
        const requests = response.data?.items || [];

        // 按类型分类任务
        const directTasks = requests.filter(req => req.request_type === 'PUBLISH_CONTENT') || [];
        const creativeTasks = requests.filter(req => req.request_type === 'CREATE_CONTENT') || [];

        // 转换数据格式以适配任务管理组件（只使用后端真实字段）
        const formatTask = (req) => ({
          id: req.id,
          title: req.request_title || '未命名任务',
          category: req.request_type === 'CREATE_CONTENT' ? '内容创作' : '稿件发布',
          client: req.company_info?.company_name || '未知客户',
          deadline: req.deadline ? new Date(req.deadline).toLocaleDateString() : '待定',
          reward: parseFloat(req.fixed_price || 0),
          status: getTaskStatus(req.status),
          description: req.request_description || '暂无描述',
          // 保留原始数据
          originalData: req
        });

        setTasks({
          direct: directTasks.map(formatTask),
          creative: creativeTasks.map(formatTask)
        });
      } else {
        setSnackbar({
          open: true,
          message: '获取任务列表失败',
          severity: 'error'
        });
        // 设置空数据
        setTasks({ direct: [], creative: [] });
      }
    } catch (error) {
      console.error('加载任务失败:', error);
      setSnackbar({
        open: true,
        message: error.message || '加载任务失败',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  }, []);

  // 将后端状态转换为前端任务状态
  const getTaskStatus = (backendStatus) => {
    switch (backendStatus) {
      case 'ACCEPTED': return 'pending';      // 已接单，待开始
      case 'IN_PROGRESS': return 'in_progress'; // 进行中
      case 'DELIVERED': return 'in_progress';   // 已交付，等待验收
      case 'COMPLETED': return 'completed';     // 已完成
      case 'CANCELLED': return 'cancelled';     // 已取消
      default: return 'pending';
    }
  };



  // 获取状态显示文本
  const getStatusText = (status) => {
    switch (status) {
      case 'pending': return '待开始';
      case 'in_progress': return '进行中';
      case 'completed': return '已完成';
      default: return '未知';
    }
  };

  // 获取状态颜色
  const getStatusColor = (status) => {
    switch (status) {
      case 'pending': return '#fef3c7';
      case 'in_progress': return '#dbeafe';
      case 'completed': return '#d1fae5';
      default: return '#f3f4f6';
    }
  };

  // 获取状态文字颜色
  const getStatusTextColor = (status) => {
    switch (status) {
      case 'pending': return '#92400e';
      case 'in_progress': return '#1e40af';
      case 'completed': return '#065f46';
      default: return '#374151';
    }
  };

  useEffect(() => {
    loadTasks();
  }, [loadTasks]);




  const handleTabChange = (event, newValue) => {
    setCurrentTab(newValue);
    setCurrentPage(1); // Reset pagination when switching tabs
  };

  // 处理文件上传
  const handleFileUpload = async (files) => {
    if (!files || files.length === 0) return;

    try {
      setUploading(true);

      // 验证文件
      const allowedTypes = ['image/*', 'video/*', '.pdf', '.doc', '.docx', '.txt', '.zip', '.rar'];
      const maxSize = 10 * 1024 * 1024; // 10MB

      const validFiles = [];
      for (const file of files) {
        const validation = uploadService.validateFile(file, allowedTypes, maxSize);
        if (validation.valid) {
          validFiles.push(file);
        } else {
          setSnackbar({
            open: true,
            message: `文件 ${file.name} 验证失败: ${validation.error}`,
            severity: 'error'
          });
        }
      }

      if (validFiles.length === 0) return;

      // 上传文件到TOS
      const uploadResults = await uploadService.uploadFiles(
        validFiles,
        'content_delivery', // 业务类型
        selectedTask?.id,    // 业务ID
        false,              // 不公开
        24 * 7,             // 7天有效期
        (_, __, filename, ___, error) => {
          // 上传进度回调
          if (error) {
            console.error(`上传失败: ${filename} - ${error}`);
          }
        }
      );

      // 处理上传结果
      const successfulUploads = uploadResults.filter(result => result.success);
      const failedUploads = uploadResults.filter(result => !result.success);

      if (successfulUploads.length > 0) {
        const newFiles = successfulUploads.map(upload => ({
          id: upload.result.file_id,
          name: upload.file.name,
          size: upload.file.size,
          type: upload.file.type,
          url: upload.result.file_url,
          upload_result: upload.result
        }));

        setUploadedFiles(prev => [...prev, ...newFiles]);
        setDeliveryFormData(prev => ({
          ...prev,
          provided_content_files: [...prev.provided_content_files, ...newFiles]
        }));

        setSnackbar({
          open: true,
          message: `成功上传 ${successfulUploads.length} 个文件`,
          severity: 'success'
        });
      }

      if (failedUploads.length > 0) {
        setSnackbar({
          open: true,
          message: `${failedUploads.length} 个文件上传失败`,
          severity: 'warning'
        });
      }

    } catch (error) {
      setSnackbar({
        open: true,
        message: '文件上传失败: ' + error.message,
        severity: 'error'
      });
    } finally {
      setUploading(false);
    }
  };

  // 删除已上传的文件
  const handleRemoveFile = (fileIndex) => {
    setUploadedFiles(prev => prev.filter((_, index) => index !== fileIndex));
    setDeliveryFormData(prev => ({
      ...prev,
      provided_content_files: prev.provided_content_files.filter((_, index) => index !== fileIndex)
    }));
  };

  // 根据文件类型获取对应的图标
  const getFileIcon = (fileName, fileType) => {
    const extension = fileName.toLowerCase().split('.').pop();
    const type = fileType?.toLowerCase() || '';

    if (type.startsWith('image/') || ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(extension)) {
      return <Image sx={{ fontSize: 20, color: '#10b981' }} />;
    }
    if (type.startsWith('video/') || ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm'].includes(extension)) {
      return <VideoFile sx={{ fontSize: 20, color: '#f59e0b' }} />;
    }
    if (extension === 'pdf') {
      return <PictureAsPdf sx={{ fontSize: 20, color: '#ef4444' }} />;
    }
    if (['doc', 'docx'].includes(extension)) {
      return <Description sx={{ fontSize: 20, color: '#3b82f6' }} />;
    }
    if (['zip', 'rar', '7z', 'tar', 'gz'].includes(extension)) {
      return <Archive sx={{ fontSize: 20, color: '#8b5cf6' }} />;
    }
    if (['txt', 'md', 'log'].includes(extension)) {
      return <Description sx={{ fontSize: 20, color: '#6b7280' }} />;
    }
    return <InsertDriveFile sx={{ fontSize: 20, color: '#6b7280' }} />;
  };

  // 处理拖拽事件
  const handleDragOver = (e) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDragEnter = (e) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();

    if (uploading) return;

    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileUpload(files);
    }
  };



  // 筛选函数
  const applyFilters = useCallback(() => {
    const currentTasks = currentTab === 0 ? tasks.direct : tasks.creative;
    let filtered = [...currentTasks];

    // 关键词筛选
    if (filters.keyword) {
      filtered = filtered.filter(task =>
        task.title.toLowerCase().includes(filters.keyword.toLowerCase()) ||
        task.client.toLowerCase().includes(filters.keyword.toLowerCase()) ||
        task.description.toLowerCase().includes(filters.keyword.toLowerCase())
      );
    }

    // 价格范围筛选
    if (filters.priceRange !== 'all') {
      filtered = filtered.filter(task => {
        const reward = task.reward;
        switch (filters.priceRange) {
          case 'low': return reward < 1000;
          case 'medium': return reward >= 1000 && reward < 3000;
          case 'high': return reward >= 3000;
          default: return true;
        }
      });
    }

    // 截止时间筛选
    if (filters.deadline !== 'all') {
      const now = new Date();
      filtered = filtered.filter(task => {
        const deadline = new Date(task.deadline);
        const diffDays = Math.ceil((deadline - now) / (1000 * 60 * 60 * 24));
        switch (filters.deadline) {
          case 'today': return diffDays <= 1;
          case 'week': return diffDays <= 7;
          case 'month': return diffDays <= 30;
          default: return true;
        }
      });
    }

    setFilteredTasks(filtered);
  }, [currentTab, tasks, filters]);

  useEffect(() => {
    applyFilters();
  }, [applyFilters]);



  return (
    <StyledContainer>
      {/* 顶部标题区域 */}
      <HeaderSection>
        <Box sx={{ px: 4, py: 3 }}>
          <Box>
            <Typography variant="h4" sx={{
              fontWeight: 700,
              color: '#1a1a1a',
              mb: 0.5,
              fontSize: { xs: '1.5rem', md: '1.75rem' }
            }}>
              我的任务
            </Typography>
            <Typography variant="body2" sx={{
              color: '#6b7280',
              fontSize: '0.9rem'
            }}>
              管理您已接单的内容创作任务，跟踪进度和收益
            </Typography>
          </Box>
        </Box>

        {/* 标签页 */}
        <StyledTabs value={currentTab} onChange={handleTabChange} sx={{ minHeight: 48, mb: 0 }}>
          <Tab
            icon={<Send />}
            label={
              <Box display="flex" alignItems="center" gap={1}>
                稿件直发
                <Badge
                  badgeContent={tasks.direct.length}
                  color="primary"
                  sx={{
                    '& .MuiBadge-badge': {
                      backgroundColor: '#3b82f6',
                      color: 'white',
                      fontSize: '0.75rem',
                      fontWeight: 600,
                      minWidth: '20px',
                      height: '20px'
                    }
                  }}
                >
                  <Box sx={{ width: 8 }} />
                </Badge>
              </Box>
            }
          />
          <Tab
            icon={<AutoAwesome />}
            label={
              <Box display="flex" alignItems="center" gap={1}>
                创作需求
                <Badge
                  badgeContent={tasks.creative.length}
                  color="secondary"
                  sx={{
                    '& .MuiBadge-badge': {
                      backgroundColor: '#f59e0b',
                      color: 'white',
                      fontSize: '0.75rem',
                      fontWeight: 600,
                      minWidth: '20px',
                      height: '20px'
                    }
                  }}
                >
                  <Box sx={{ width: 8 }} />
                </Badge>
              </Box>
            }
          />
        </StyledTabs>
      </HeaderSection>

      {/* 内容区域 */}
      <ContentSection>
        <Box sx={{ pt: 0, pb: 4 }}>

          {/* 分页控制 */}
          <Box sx={{
            display: 'flex',
            justifyContent: 'flex-end',
            alignItems: 'center',
            mb: 0,
            mt: 0,
            pt: 0,
            px: 4,
            backgroundColor: 'white',
            borderTop: '1px solid #e2e8f0'
          }}>

            {/* 上一页/下一页按钮 */}
            {filteredTasks.length > itemsPerPage && (
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Typography variant="body2" sx={{ color: '#6b7280', fontSize: '0.85rem', mr: 1 }}>
                  第 {currentPage} / {Math.ceil(filteredTasks.length / itemsPerPage)} 页
                </Typography>
                <Button
                  size="small"
                  variant="outlined"
                  startIcon={<ChevronLeft />}
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                  disabled={currentPage === 1}
                  sx={{
                    minWidth: 80,
                    borderColor: '#e5e7eb',
                    color: '#6b7280',
                    fontSize: '0.85rem',
                    py: 0.5,
                    '&:hover': {
                      borderColor: '#d1d5db',
                      backgroundColor: '#f9fafb'
                    },
                    '&:disabled': {
                      borderColor: '#f3f4f6',
                      color: '#d1d5db'
                    }
                  }}
                >
                  上一页
                </Button>
                <Button
                  size="small"
                  variant="outlined"
                  endIcon={<ChevronRight />}
                  onClick={() => setCurrentPage(prev => Math.min(Math.ceil(filteredTasks.length / itemsPerPage), prev + 1))}
                  disabled={currentPage === Math.ceil(filteredTasks.length / itemsPerPage)}
                  sx={{
                    minWidth: 80,
                    borderColor: '#e5e7eb',
                    color: '#6b7280',
                    fontSize: '0.85rem',
                    py: 0.5,
                    '&:hover': {
                      borderColor: '#d1d5db',
                      backgroundColor: '#f9fafb'
                    },
                    '&:disabled': {
                      borderColor: '#f3f4f6',
                      color: '#d1d5db'
                    }
                  }}
                >
                  下一页
                </Button>
              </Box>
            )}
          </Box>

          {/* 任务列表 - 表格格式 */}
          {loading ? (
            <Box
              display="flex"
              flexDirection="column"
              alignItems="center"
              justifyContent="center"
              height="400px"
              sx={{ backgroundColor: 'transparent' }}
            >
              <LinearProgress sx={{ width: '200px', mb: 2 }} />
              <Typography variant="body2" color="text.secondary">
                正在加载任务数据...
              </Typography>
            </Box>
          ) : filteredTasks.length === 0 ? (
            <Box sx={{ px: 4 }}>
              <Box
                display="flex"
                flexDirection="column"
                alignItems="center"
                justifyContent="center"
                height="400px"
                sx={{ backgroundColor: 'transparent' }}
              >
              <Assignment sx={{ fontSize: 64, color: 'text.disabled', mb: 2 }} />
              <Typography variant="h6" color="text.secondary" gutterBottom>
                暂无已接单的任务
              </Typography>
              <Typography variant="body2" color="text.secondary">
                去浏览需求页面接单，或者稍后再来看看
              </Typography>
              </Box>
            </Box>
          ) : (
            <Box sx={{ px: 0, pt: 0 }}>
              <TableContainer component={Paper} sx={{
                border: 'none',
                borderRadius: 0,
                overflow: 'hidden',
                boxShadow: 'none'
              }}>
                {/* 搜索和筛选栏 - 作为表格的一部分 */}
                <Box sx={{
                  pl: 0,
                  pr: 4,
                  py: 3,
                  pb: 0
                }}>
                  <Box sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: 2,
                    flexWrap: 'wrap'
                  }}>
                    {/* 搜索框 */}
                    <TextField
                      size="small"
                      placeholder="搜索任务标题、客户或描述..."
                      value={filters.keyword}
                      onChange={(e) => setFilters(prev => ({ ...prev, keyword: e.target.value }))}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Search sx={{ color: '#64748b', fontSize: 20 }} />
                          </InputAdornment>
                        ),
                      }}
                      sx={{
                        flex: 1,
                        minWidth: 300,
                        maxWidth: 450,
                        '& .MuiOutlinedInput-root': {
                          backgroundColor: 'white',
                          borderRadius: 1.5,
                          '& fieldset': {
                            borderColor: '#cbd5e1',
                          },
                          '&:hover fieldset': {
                            borderColor: '#94a3b8',
                          },
                          '&.Mui-focused fieldset': {
                            borderColor: '#3b82f6',
                            borderWidth: 2,
                          }
                        }
                      }}
                    />

                    {/* 筛选器组 */}
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, flexWrap: 'wrap' }}>
                      {/* 价格范围筛选 */}
                      <FormControl size="small" sx={{ minWidth: 130 }}>
                        <InputLabel sx={{ color: '#64748b', fontSize: '0.875rem' }}>价格范围</InputLabel>
                        <Select
                          value={filters.priceRange}
                          label="价格范围"
                          onChange={(e) => setFilters(prev => ({ ...prev, priceRange: e.target.value }))}
                          sx={{
                            backgroundColor: 'white',
                            borderRadius: 1.5,
                            '& fieldset': {
                              borderColor: '#cbd5e1',
                            },
                            '&:hover fieldset': {
                              borderColor: '#94a3b8',
                            },
                            '&.Mui-focused fieldset': {
                              borderColor: '#3b82f6',
                              borderWidth: 2,
                            }
                          }}
                        >
                          <MenuItem value="all">不限价格</MenuItem>
                          <MenuItem value="low">￥1000以下</MenuItem>
                          <MenuItem value="medium">￥1000-3000</MenuItem>
                          <MenuItem value="high">￥3000以上</MenuItem>
                        </Select>
                      </FormControl>

                      {/* 截止时间筛选 */}
                      <FormControl size="small" sx={{ minWidth: 130 }}>
                        <InputLabel sx={{ color: '#64748b', fontSize: '0.875rem' }}>截止时间</InputLabel>
                        <Select
                          value={filters.deadline}
                          label="截止时间"
                          onChange={(e) => setFilters(prev => ({ ...prev, deadline: e.target.value }))}
                          sx={{
                            backgroundColor: 'white',
                            borderRadius: 1.5,
                            '& fieldset': {
                              borderColor: '#cbd5e1',
                            },
                            '&:hover fieldset': {
                              borderColor: '#94a3b8',
                            },
                            '&.Mui-focused fieldset': {
                              borderColor: '#3b82f6',
                              borderWidth: 2,
                            }
                          }}
                        >
                          <MenuItem value="all">不限时间</MenuItem>
                          <MenuItem value="today">今天截止</MenuItem>
                          <MenuItem value="week">本周截止</MenuItem>
                          <MenuItem value="month">本月截止</MenuItem>
                        </Select>
                      </FormControl>

                      {/* 操作按钮组 */}
                      <Box sx={{ display: 'flex', gap: 1 }}>
                        {/* 刷新按钮 */}
                        <Button
                          variant="outlined"
                          startIcon={<Refresh />}
                          onClick={loadTasks}
                          disabled={loading}
                          sx={{
                            borderColor: '#cbd5e1',
                            color: '#64748b',
                            backgroundColor: 'white',
                            borderRadius: 1.5,
                            px: 2,
                            '&:hover': {
                              borderColor: '#94a3b8',
                              backgroundColor: '#f1f5f9'
                            },
                            '&:disabled': {
                              borderColor: '#e2e8f0',
                              color: '#94a3b8'
                            }
                          }}
                        >
                          刷新
                        </Button>

                        {/* 清空筛选按钮 */}
                        {(filters.keyword || filters.priceRange !== 'all' || filters.deadline !== 'all') && (
                          <Button
                            variant="text"
                            size="small"
                            onClick={() => setFilters({ keyword: '', priceRange: 'all', deadline: 'all' })}
                            sx={{
                              color: '#64748b',
                              borderRadius: 1.5,
                              px: 2,
                              '&:hover': {
                                backgroundColor: '#f1f5f9'
                              }
                            }}
                          >
                            清空
                          </Button>
                        )}
                      </Box>
                    </Box>
                  </Box>
                </Box>

              <Table sx={{ minWidth: 650 }}>
                <TableHead>
                  <TableRow sx={{ backgroundColor: '#f8fafc' }}>
                    <TableCell sx={{ fontWeight: 600, color: '#374151', fontSize: '0.875rem' }}>
                      类型
                    </TableCell>
                    <TableCell sx={{ fontWeight: 600, color: '#374151', fontSize: '0.875rem' }}>
                      任务标题
                    </TableCell>
                    <TableCell sx={{ fontWeight: 600, color: '#374151', fontSize: '0.875rem' }}>
                      客户
                    </TableCell>
                    <TableCell sx={{ fontWeight: 600, color: '#374151', fontSize: '0.875rem' }}>
                      报酬
                    </TableCell>
                    <TableCell sx={{ fontWeight: 600, color: '#374151', fontSize: '0.875rem' }}>
                      截止时间
                    </TableCell>
                    <TableCell sx={{ fontWeight: 600, color: '#374151', fontSize: '0.875rem' }}>
                      操作
                    </TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {filteredTasks
                    .slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage)
                    .map((task, index) => (
                      <TableRow
                        key={task.id}
                        sx={{
                          '&:hover': {
                            backgroundColor: '#f9fafb'
                          },
                          '&:last-child td': {
                            borderBottom: 0
                          }
                        }}
                      >
                        <TableCell>
                          <Chip
                            icon={task.category === '内容创作' ? <AutoAwesome sx={{ fontSize: 14 }} /> : <Send sx={{ fontSize: 14 }} />}
                            label={task.category === '内容创作' ? '创作' : '直发'}
                            size="small"
                            sx={{
                              height: 24,
                              fontSize: '0.75rem',
                              backgroundColor: task.category === '内容创作' ? '#fef3c7' : '#dbeafe',
                              color: task.category === '内容创作' ? '#a16207' : '#1e40af',
                            }}
                          />
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2" sx={{
                            fontWeight: 600,
                            color: '#1a1a1a',
                            fontSize: '0.875rem'
                          }}>
                            {task.title}
                          </Typography>
                          <Typography variant="caption" sx={{
                            color: '#6b7280',
                            fontSize: '0.75rem',
                            display: 'block',
                            mt: 0.5
                          }}>
                            {task.description?.length > 50 ? `${task.description.substring(0, 50)}...` : task.description}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2" sx={{ fontSize: '0.875rem' }}>
                            {task.client}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2" sx={{
                            fontWeight: 600,
                            color: '#059669',
                            fontSize: '0.875rem'
                          }}>
                            ¥{task.reward?.toLocaleString()}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2" sx={{ fontSize: '0.875rem' }}>
                            {task.deadline}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Box sx={{ display: 'flex', gap: 1 }}>
                            <Button
                              variant="outlined"
                              size="small"
                              startIcon={<Visibility sx={{ fontSize: 16 }} />}
                              onClick={() => {
                                setSelectedTask(task);
                                setShowDetailDialog(true);
                              }}
                              sx={{
                                minWidth: 70,
                                borderColor: '#e5e7eb',
                                color: '#6b7280',
                                borderRadius: 1.5,
                                fontSize: '0.75rem',
                                fontWeight: 500,
                                py: 0.5,
                                px: 1.5,
                                transition: 'all 0.2s ease',
                                '&:hover': {
                                  borderColor: '#3b82f6',
                                  backgroundColor: '#f0f9ff',
                                  color: '#3b82f6',
                                  transform: 'translateY(-1px)',
                                  boxShadow: '0 2px 8px rgba(59, 130, 246, 0.15)'
                                }
                              }}
                            >
                              查看
                            </Button>
                            {task.status === 'pending' && (
                              <Button
                                variant="contained"
                                size="small"
                                startIcon={<Send sx={{ fontSize: 16 }} />}
                                onClick={() => {
                                  setSelectedTask(task);
                                  setDeliveryFormData({
                                    provided_content_title: '',
                                    provided_content_text: '',
                                    provided_content_files: [],
                                    delivery_notes: ''
                                  });
                                  setUploadedFiles([]);
                                  setShowDeliveryDialog(true);
                                }}
                                sx={{
                                  minWidth: 70,
                                  background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
                                  color: 'white',
                                  borderRadius: 1.5,
                                  fontSize: '0.75rem',
                                  fontWeight: 600,
                                  py: 0.5,
                                  px: 1.5,
                                  boxShadow: '0 2px 8px rgba(16, 185, 129, 0.3)',
                                  transition: 'all 0.2s ease',
                                  '&:hover': {
                                    background: 'linear-gradient(135deg, #059669 0%, #047857 100%)',
                                    transform: 'translateY(-1px)',
                                    boxShadow: '0 4px 12px rgba(16, 185, 129, 0.4)'
                                  }
                                }}
                              >
                                交付
                              </Button>
                            )}
                            {task.status === 'in_progress' && (
                              <Chip
                                label="进行中"
                                size="small"
                                sx={{
                                  backgroundColor: '#dbeafe',
                                  color: '#1e40af',
                                  fontSize: '0.75rem',
                                  height: 24
                                }}
                              />
                            )}
                            {task.status === 'completed' && (
                              <Chip
                                label="已完成"
                                size="small"
                                sx={{
                                  backgroundColor: '#d1fae5',
                                  color: '#065f46',
                                  fontSize: '0.75rem',
                                  height: 24
                                }}
                              />
                            )}
                          </Box>
                        </TableCell>
                      </TableRow>
                    ))}
                </TableBody>
              </Table>
              </TableContainer>
            </Box>
          )}
        </Box>
      </ContentSection>

      {/* 任务详情对话框 */}
      <Dialog
        open={showDetailDialog}
        onClose={() => setShowDetailDialog(false)}
        maxWidth="lg"
        fullWidth
        sx={{
          '& .MuiDialog-paper': {
            borderRadius: 2,
            maxHeight: '90vh'
          }
        }}
      >
        {selectedTask && (
          <>
            {/* 头部区域 */}
            <Box sx={{
              backgroundColor: '#fff',
              borderBottom: '1px solid #e5e7eb',
              p: 2.5,
              position: 'relative'
            }}>
              <IconButton
                onClick={() => setShowDetailDialog(false)}
                sx={{
                  position: 'absolute',
                  right: 16,
                  top: 16,
                  color: '#6b7280',
                  '&:hover': { backgroundColor: '#f3f4f6' }
                }}
              >
                <Close />
              </IconButton>

              <Box sx={{ pr: 6 }}>
                <Typography variant="h5" sx={{ fontWeight: 600, mb: 1, color: '#1f2937' }}>
                  {selectedTask.title}
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                  <Chip
                    label={selectedTask.category}
                    sx={{
                      backgroundColor: selectedTask.category === '内容创作' ? '#fef3c7' : '#dbeafe',
                      color: selectedTask.category === '内容创作' ? '#a16207' : '#1e40af',
                      fontWeight: 600
                    }}
                  />
                  <Chip
                    label={getStatusText(selectedTask.status)}
                    sx={{
                      backgroundColor: getStatusColor(selectedTask.status),
                      color: getStatusTextColor(selectedTask.status),
                      fontWeight: 600
                    }}
                  />
                  <Typography variant="body2" sx={{ color: '#6b7280' }}>
                    客户：{selectedTask.client}
                  </Typography>
                </Box>

                {/* 关键信息卡片 */}
                <Box sx={{ display: 'flex', gap: 2 }}>
                  <Box sx={{ flex: 1 }}>
                    <Box sx={{
                      textAlign: 'center',
                      p: 1.5,
                      backgroundColor: '#f8fafc',
                      borderRadius: 1,
                      border: '1px solid #e2e8f0'
                    }}>
                      <AttachMoney sx={{ fontSize: 24, mb: 0.5, color: '#10b981' }} />
                      <Typography variant="h6" sx={{ fontWeight: 600, color: '#1f2937' }}>
                        ¥{selectedTask.reward || 0}
                      </Typography>
                      <Typography variant="caption" sx={{ color: '#6b7280' }}>任务报酬</Typography>
                    </Box>
                  </Box>
                  <Box sx={{ flex: 1 }}>
                    <Box sx={{
                      textAlign: 'center',
                      p: 1.5,
                      backgroundColor: '#f8fafc',
                      borderRadius: 1,
                      border: '1px solid #e2e8f0'
                    }}>
                      <Schedule sx={{ fontSize: 24, mb: 0.5, color: '#f59e0b' }} />
                      <Typography variant="body2" sx={{ fontWeight: 600, color: '#1f2937' }}>
                        {selectedTask.deadline}
                      </Typography>
                      <Typography variant="caption" sx={{ color: '#6b7280' }}>截止时间</Typography>
                    </Box>
                  </Box>
                  <Box sx={{ flex: 1 }}>
                    <Box sx={{
                      textAlign: 'center',
                      p: 1.5,
                      backgroundColor: '#f8fafc',
                      borderRadius: 1,
                      border: '1px solid #e2e8f0'
                    }}>
                      <People sx={{ fontSize: 24, mb: 0.5, color: '#3b82f6' }} />
                      <Typography variant="body2" sx={{ fontWeight: 600, color: '#1f2937' }}>
                        {selectedTask.originalData?.company_info?.company_name || '未知'}
                      </Typography>
                      <Typography variant="caption" sx={{ color: '#6b7280' }}>发布公司</Typography>
                    </Box>
                  </Box>
                </Box>
              </Box>
            </Box>

            {/* 内容区域 */}
            <DialogContent sx={{ p: 0 }}>
              <Box sx={{ p: 3 }}>
                {/* 任务描述 */}
                {selectedTask.description && (
                  <Box sx={{ mb: 3 }}>
                    <Typography variant="h6" sx={{
                      fontWeight: 600,
                      color: '#1e293b',
                      mb: 2,
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1
                    }}>
                      <Box sx={{
                        width: 4,
                        height: 20,
                        backgroundColor: '#3b82f6',
                        borderRadius: 1
                      }} />
                      任务描述
                    </Typography>
                    <Paper sx={{
                      p: 3,
                      backgroundColor: '#f8fafc',
                      border: '1px solid #e2e8f0',
                      borderRadius: 2,
                      boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
                    }}>
                      <Typography variant="body1" sx={{
                        color: '#374151',
                        lineHeight: 1.7,
                        whiteSpace: 'pre-wrap',
                        fontSize: '0.95rem'
                      }}>
                        {selectedTask.description}
                      </Typography>
                    </Paper>
                  </Box>
                )}



                {/* 原始数据信息（如果需要的话） */}
                {selectedTask.originalData && (
                  <Box sx={{ mb: 3 }}>
                    <Typography variant="h6" sx={{
                      fontWeight: 600,
                      color: '#1e293b',
                      mb: 2,
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1
                    }}>
                      <Box sx={{
                        width: 4,
                        height: 20,
                        backgroundColor: '#f59e0b',
                        borderRadius: 1
                      }} />
                      任务详细信息
                    </Typography>
                    <Paper sx={{
                      p: 3,
                      backgroundColor: '#fffbeb',
                      border: '1px solid #fed7aa',
                      borderRadius: 2,
                      boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
                    }}>
                      <Box sx={{ display: 'flex', gap: 3 }}>
                        <Box sx={{ flex: 1 }}>
                          <Typography variant="body2" sx={{ color: '#6b7280', mb: 0.5 }}>
                            任务类型：
                          </Typography>
                          <Typography variant="body1" sx={{ fontWeight: 500, color: '#374151' }}>
                            {selectedTask.originalData.request_type === 'CREATE_CONTENT' ? '内容创作' : '稿件发布'}
                          </Typography>
                        </Box>
                        <Box sx={{ flex: 1 }}>
                          <Typography variant="body2" sx={{ color: '#6b7280', mb: 0.5 }}>
                            创建时间：
                          </Typography>
                          <Typography variant="body1" sx={{ fontWeight: 500, color: '#374151' }}>
                            {selectedTask.originalData.created_at ? new Date(selectedTask.originalData.created_at).toLocaleDateString() : '未知'}
                          </Typography>
                        </Box>
                      </Box>
                    </Paper>
                  </Box>
                )}
              </Box>
            </DialogContent>

            {/* 底部操作按钮 */}
            <DialogActions sx={{ p: 3, borderTop: '1px solid #e5e7eb' }}>
              <Button
                onClick={() => setShowDetailDialog(false)}
                sx={{ color: '#6b7280' }}
              >
                关闭
              </Button>
              {selectedTask && selectedTask.status === 'pending' && (
                <Button
                  variant="contained"
                  startIcon={<Send />}
                  onClick={() => {
                    setShowDetailDialog(false);
                    setDeliveryFormData({
                      provided_content_title: '',
                      provided_content_text: '',
                      provided_content_files: [],
                      delivery_notes: ''
                    });
                    setUploadedFiles([]);
                    setShowDeliveryDialog(true);
                  }}
                  sx={{
                    backgroundColor: '#10b981',
                    '&:hover': { backgroundColor: '#059669' }
                  }}
                >
                  交付内容
                </Button>
              )}
            </DialogActions>
          </>
        )}
      </Dialog>

      {/* 内容交付对话框 */}
      <Dialog
        open={showDeliveryDialog}
        onClose={() => setShowDeliveryDialog(false)}
        maxWidth="md"
        fullWidth
        sx={{
          '& .MuiDialog-paper': {
            borderRadius: 2,
            maxHeight: '90vh'
          }
        }}
      >
        <Box sx={{
          backgroundColor: '#fff',
          borderBottom: '1px solid #e5e7eb',
          p: 2.5,
          position: 'relative'
        }}>
          <IconButton
            onClick={() => setShowDeliveryDialog(false)}
            sx={{
              position: 'absolute',
              right: 16,
              top: 16,
              color: '#6b7280',
              '&:hover': { backgroundColor: '#f3f4f6' }
            }}
          >
            <Close />
          </IconButton>

          <Typography variant="h5" sx={{ fontWeight: 600, mb: 1, color: '#1f2937', pr: 6 }}>
            交付内容
          </Typography>
          <Typography variant="body2" sx={{ color: '#6b7280' }}>
            任务：{selectedTask?.title}
          </Typography>
        </Box>

        <DialogContent sx={{ p: 3 }}>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
            {/* 内容标题 */}
            <TextField
              fullWidth
              label="内容标题"
              value={deliveryFormData.provided_content_title}
              onChange={(e) => setDeliveryFormData(prev => ({ ...prev, provided_content_title: e.target.value }))}
              placeholder="请输入交付内容的标题（至少5个字符）..."
              helperText={`当前字符数: ${deliveryFormData.provided_content_title?.length || 0}/5（最少）`}
              error={deliveryFormData.provided_content_title && deliveryFormData.provided_content_title.length < 5}
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: 1.5,
                }
              }}
            />

            {/* 内容正文 */}
            <TextField
              fullWidth
              multiline
              rows={8}
              label="内容正文"
              value={deliveryFormData.provided_content_text}
              onChange={(e) => setDeliveryFormData(prev => ({ ...prev, provided_content_text: e.target.value }))}
              placeholder="请输入交付的内容正文（至少50个字符）..."
              helperText={`当前字符数: ${deliveryFormData.provided_content_text?.length || 0}/50（最少）`}
              error={deliveryFormData.provided_content_text && deliveryFormData.provided_content_text.length < 50}
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: 1.5,
                }
              }}
            />

            {/* 附件上传区域 */}
            <Box>
              <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 600, color: '#374151' }}>
                附件文件（可选）
              </Typography>

              {/* 文件上传区域 */}
              <Paper
                sx={{
                  border: '2px dashed #d1d5db',
                  borderRadius: 3,
                  p: 4,
                  textAlign: 'center',
                  backgroundColor: uploading ? '#f0f9ff' : '#fafbfc',
                  cursor: uploading ? 'not-allowed' : 'pointer',
                  transition: 'all 0.3s ease',
                  position: 'relative',
                  overflow: 'hidden',
                  '&:hover': {
                    borderColor: uploading ? '#d1d5db' : '#3b82f6',
                    backgroundColor: uploading ? '#f0f9ff' : '#f0f9ff',
                    transform: uploading ? 'none' : 'translateY(-1px)',
                    boxShadow: uploading ? 'none' : '0 4px 12px rgba(59, 130, 246, 0.15)'
                  },
                  '&::before': uploading ? {} : {
                    content: '""',
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    background: 'linear-gradient(45deg, transparent 30%, rgba(59, 130, 246, 0.05) 50%, transparent 70%)',
                    opacity: 0,
                    transition: 'opacity 0.3s ease',
                  },
                  '&:hover::before': uploading ? {} : {
                    opacity: 1,
                  }
                }}
                onClick={() => {
                  if (!uploading) {
                    document.getElementById('file-upload-input').click();
                  }
                }}
                onDragOver={handleDragOver}
                onDragEnter={handleDragEnter}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
              >
                <input
                  id="file-upload-input"
                  type="file"
                  multiple
                  accept="image/*,video/*,.pdf,.doc,.docx,.txt,.zip,.rar"
                  style={{ display: 'none' }}
                  onChange={(e) => {
                    if (e.target.files && e.target.files.length > 0) {
                      handleFileUpload(Array.from(e.target.files));
                      e.target.value = ''; // 清空input，允许重复选择同一文件
                    }
                  }}
                />

                {uploading ? (
                  <Box sx={{ position: 'relative', zIndex: 1 }}>
                    <LinearProgress
                      sx={{
                        mb: 2,
                        width: '70%',
                        mx: 'auto',
                        height: 6,
                        borderRadius: 3,
                        backgroundColor: '#e5e7eb',
                        '& .MuiLinearProgress-bar': {
                          borderRadius: 3,
                          backgroundColor: '#3b82f6'
                        }
                      }}
                    />
                    <Typography variant="body2" sx={{
                      color: '#3b82f6',
                      mb: 0.5,
                      fontWeight: 500
                    }}>
                      正在上传文件...
                    </Typography>
                  </Box>
                ) : (
                  <Box sx={{ position: 'relative', zIndex: 1 }}>
                    <CloudUpload sx={{
                      fontSize: 56,
                      color: '#9ca3af',
                      mb: 2,
                      transition: 'all 0.3s ease'
                    }} />
                    <Typography variant="body1" sx={{
                      color: '#374151',
                      mb: 1,
                      fontWeight: 500,
                      fontSize: '1rem'
                    }}>
                      点击上传文件或拖拽文件到此处
                    </Typography>
                    <Typography variant="caption" sx={{
                      color: '#9ca3af',
                      fontSize: '0.875rem',
                      display: 'block',
                      lineHeight: 1.5
                    }}>
                      支持图片、文档、视频等格式，单个文件不超过10MB
                    </Typography>
                  </Box>
                )}
              </Paper>

              {/* 已上传文件列表 */}
              {uploadedFiles.length > 0 && (
                <Box sx={{ mt: 3 }}>
                  <Box sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1,
                    mb: 2
                  }}>
                    <AttachFile sx={{ fontSize: 18, color: '#6b7280' }} />
                    <Typography variant="subtitle2" sx={{
                      fontWeight: 600,
                      color: '#374151',
                      fontSize: '0.875rem'
                    }}>
                      已上传文件 ({uploadedFiles.length})
                    </Typography>
                  </Box>
                  {uploadedFiles.map((file, index) => (
                    <Box
                      key={index}
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        p: 2,
                        mb: 1.5,
                        backgroundColor: '#ffffff',
                        border: '1px solid #e5e7eb',
                        borderRadius: 2,
                        boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
                        transition: 'all 0.2s ease',
                        '&:hover': {
                          borderColor: '#d1d5db',
                          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                        }
                      }}
                    >
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5, flex: 1 }}>
                        {getFileIcon(file.name, file.type)}
                        <Box sx={{ flex: 1, minWidth: 0 }}>
                          <Typography variant="body2" sx={{
                            fontWeight: 500,
                            color: '#111827',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            whiteSpace: 'nowrap',
                            mb: 0.5
                          }}>
                            {file.name}
                          </Typography>
                          <Typography variant="caption" sx={{
                            color: '#6b7280',
                            fontSize: '0.75rem'
                          }}>
                            {uploadService.formatFileSize(file.size)}
                          </Typography>
                        </Box>
                      </Box>
                      <IconButton
                        size="small"
                        color="error"
                        onClick={() => handleRemoveFile(index)}
                        sx={{
                          ml: 1,
                          '&:hover': {
                            backgroundColor: '#fef2f2'
                          }
                        }}
                      >
                        <Delete sx={{ fontSize: 18 }} />
                      </IconButton>
                    </Box>
                  ))}
                </Box>
              )}
            </Box>

            {/* 交付说明 */}
            <TextField
              fullWidth
              multiline
              rows={3}
              label="交付说明"
              value={deliveryFormData.delivery_notes}
              onChange={(e) => setDeliveryFormData(prev => ({ ...prev, delivery_notes: e.target.value }))}
              placeholder="请输入交付说明，如制作思路、注意事项等（至少10个字符）..."
              helperText="如不填写，将使用默认交付说明"
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: 1.5,
                }
              }}
            />
          </Box>
        </DialogContent>

        <DialogActions sx={{ p: 3, borderTop: '1px solid #e5e7eb' }}>
          <Button
            onClick={() => setShowDeliveryDialog(false)}
            sx={{ color: '#6b7280' }}
          >
            取消
          </Button>
          <Button
            variant="contained"
            startIcon={<Send />}
            onClick={async () => {
              try {
                setLoading(true);

                // 准备交付数据，映射到后端期望的字段名
                const deliveryData = {
                  content_title: deliveryFormData.provided_content_title,
                  content_text: deliveryFormData.provided_content_text,
                  delivery_note: deliveryFormData.delivery_notes?.trim() || '内容已按要求完成制作，请查收。',
                  content_attachments: uploadedFiles.map(file => ({
                    file_id: file.id,
                    filename: file.name,
                    file_size: file.size,
                    file_type: file.type,
                    file_url: file.url
                  }))
                };

                // 调用真实的内容交付API
                const response = await contentService.deliverContent(selectedTask.id, deliveryData);

                if (response.success) {
                  setShowDeliveryDialog(false);
                  setUploadedFiles([]); // 清空已上传文件
                  setSnackbar({
                    open: true,
                    message: '内容交付成功！等待客户验收',
                    severity: 'success'
                  });

                  // 刷新任务列表
                  loadTasks();
                } else {
                  throw new Error(response.message || '交付失败');
                }
              } catch (error) {
                setSnackbar({
                  open: true,
                  message: '交付失败：' + error.message,
                  severity: 'error'
                });
              } finally {
                setLoading(false);
              }
            }}
            disabled={
              !deliveryFormData.provided_content_title ||
              !deliveryFormData.provided_content_text ||
              deliveryFormData.provided_content_title.length < 5 ||
              deliveryFormData.provided_content_text.length < 50
            }
            sx={{
              backgroundColor: '#10b981',
              '&:hover': { backgroundColor: '#059669' },
              '&:disabled': { backgroundColor: '#d1d5db' }
            }}
          >
            确认交付
          </Button>
        </DialogActions>
      </Dialog>

      {/* 错误提示 */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </StyledContainer>
  );
}

export default TaskManagement;