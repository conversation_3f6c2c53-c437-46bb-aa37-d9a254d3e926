import React, { useState, useEffect, Fragment } from 'react';
import subscriptionService from '../../services/subscriptionService';
import orderService from '../../services/orderService';
// import paymentService from '../../services/paymentService'; // 暂时未使用
import {
  Box,
  Container,
  Typography,
  Card,
  Button,
  LinearProgress,
  Drawer,
  List,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Avatar,
  Chip,
  Divider,
  Collapse,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  Radio,
  // RadioGroup, // 暂时未使用
  // FormControlLabel, // 暂时未使用
  IconButton,
  CircularProgress,
} from '@mui/material';
import {
  Dashboard,
  Store,
  Search,
  Assignment,
  Assessment,
  MonetizationOn,
  AutoAwesome,
  Article,
  LibraryBooks,
  Settings,
  Logout,
  Person,
  StarBorder,
  WorkspacePremium,
  ExpandLess,
  ExpandMore,
  Work,
  AccountBalance,
  Create,
  ManageAccounts,
  Campaign,
  Close,
  CheckCircle,
  Rocket,
  Diamond,
  Menu,
  MenuOpen,
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';

// Import components
import TaskManagement from './TaskManagement';
import WithdrawManagement from './WithdrawManagement';
import WritingTemplates from './WritingTemplates';
import KnowledgeBase from './KnowledgeBase';
import AccountSettings from './AccountSettings';
import AIContentCreation from '../enterprise/AIContentCreation';
import ChannelContentManagement from './ChannelContentManagement';
import BrowseRequirements from '../../components/channel/BrowseRequirements';
import RevenueStatistics from '../../components/channel/RevenueStatistics';
import MyChannels from './MyChannels';
import ChannelAnnouncements from './ChannelAnnouncements';
import announcementService from '../../services/announcementService';

function ChannelControlCenter() {
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  const [currentPage, setCurrentPage] = useState('dashboard');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [openMenus, setOpenMenus] = useState({
    'order-platform': false,
    'finance': false,
    'content': false,
    'account': false
  });
  const [upgradeDialogOpen, setUpgradeDialogOpen] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState(null); // 初始为null，等待API数据加载后设置
  const [selectedDuration, setSelectedDuration] = useState('monthly');
  const [availablePlans, setAvailablePlans] = useState([]);
  const [loadingPlans, setLoadingPlans] = useState(false);
  const [currentSubscription, setCurrentSubscription] = useState(null);
  const [isProcessingPayment, setIsProcessingPayment] = useState(false);
  const [paymentMethod, setPaymentMethod] = useState('alipay');

  // 侧边栏折叠状态
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  // 公告相关状态
  const [announcements, setAnnouncements] = useState([]);
  const [announcementLoading, setAnnouncementLoading] = useState(true);
  const [selectedAnnouncement, setSelectedAnnouncement] = useState(null);
  const [announcementDetailOpen, setAnnouncementDetailOpen] = useState(false);
  const [detailLoading, setDetailLoading] = useState(false);

  // 获取可用订阅计划
  const fetchAvailablePlans = async (billingCycle = null, forceRefreshSubscription = false) => {
    try {
      setLoadingPlans(true);

      // 如果需要强制刷新订阅状态（升级后）
      let latestSubscription = currentSubscription;
      if (forceRefreshSubscription) {
        try {
          const subscriptionResponse = await subscriptionService.getCurrentSubscription('provider');
          setCurrentSubscription(subscriptionResponse);
          latestSubscription = subscriptionResponse;
        } catch (error) {
          latestSubscription = null;
          setCurrentSubscription(null);
        }
      }

      // 首先获取渠道商专用套餐
      const params = {
        target_user_type: 'provider',  // 只获取渠道商套餐
        active_only: true
      };
      
      // 如果指定了付费周期，添加到参数中
      if (billingCycle) {
        params.billing_cycle = billingCycle;
      }

      let response = await subscriptionService.getAvailablePlans(params);

      // 处理不同的响应格式
      let plansData = [];
      if (Array.isArray(response)) {
        plansData = response;
      } else if (response.success && response.data) {
        plansData = response.data;
      } else if (response.data) {
        plansData = response.data;
      }



      // 根据 is_purchased标记设置当前订阅
      const purchasedPlan = plansData.find(p => p.is_purchased);
      if (purchasedPlan && purchasedPlan.current_subscription_id) {
        // 如果没有当前订阅信息，可以从这里设置
        if (!currentSubscription) {
          setCurrentSubscription({
            plan_id: purchasedPlan.id,
            plan_name: purchasedPlan.plan_name,
            current_subscription_id: purchasedPlan.current_subscription_id
          });
        }
      }

      if (plansData && plansData.length > 0) {
        // 转换API数据格式为组件需要的格式
        const formattedPlans = plansData.map(plan => {
          // 根据计划类型设置图标和颜色
          let icon, color;
          if (plan.plan_name.includes('企业') || plan.plan_type === 'enterprise') {
            icon = <WorkspacePremium sx={{ fontSize: 28 }} />;
            color = '#f59e0b';
          } else if (plan.plan_name.includes('专业') || plan.plan_type === 'pro') {
            icon = <Diamond sx={{ fontSize: 28 }} />;
            color = '#8b5cf6';
          } else {
            icon = <Rocket sx={{ fontSize: 28 }} />;
            color = '#3b82f6';
          }

          // 构建功能列表
          const features = [];
          if (plan.max_content_requests) {
            features.push(`${plan.max_content_requests}个内容请求`);
          }
          if (plan.max_monitoring_projects) {
            features.push(`${plan.max_monitoring_projects}个监控项目`);
          }
          if (plan.max_api_calls) {
            features.push(`${plan.max_api_calls}次API调用`);
          }
          if (plan.max_team_members) {
            features.push(`${plan.max_team_members}个团队成员`);
          }
          if (plan.max_channels) {
            features.push(`${plan.max_channels}个渠道管理`);
          }
          if (plan.max_service_orders) {
            features.push(`${plan.max_service_orders}个服务订单`);
          }

          // 添加功能特性
          if (plan.features) {
            if (plan.features.support) {
              const supportMap = {
                'email': '邮件支持',
                'priority': '优先技术支持',
                'dedicated': '专属客服支持'
              };
              features.push(supportMap[plan.features.support] || '客服支持');
            }
            if (plan.features.analytics) {
              const analyticsMap = {
                'basic': '基础数据分析',
                'advanced': '高级数据分析',
                'enterprise': '企业级数据分析'
              };
              features.push(analyticsMap[plan.features.analytics] || '数据分析');
            }
            if (plan.features.api) {
              features.push('API接入权限');
            }
            if (plan.features.custom) {
              features.push('自定义品牌');
            }
          }

          // 添加佣金信息
          if (plan.commission_rate && parseFloat(plan.commission_rate) > 0) {
            features.push(`${(parseFloat(plan.commission_rate) * 100).toFixed(1)}% 佣金比例`);
          }

          // 检查是否是当前订阅的套餐（使用后端返回的标记）
          const isCurrentSubscriptionPlan = plan.is_purchased || false;
          
          // 判断是否是非渠道商套餐
          const isNonProviderPlan = plan.target_user_type && plan.target_user_type !== 'provider';
          
          return {
            id: plan.id, // 使用真正的UUID作为id
            plan_code: plan.plan_code, // 保留plan_code用于显示
            name: plan.plan_name,
            icon: icon,
            features: features,
            pricing: {
              monthly: parseFloat(plan.monthly_price) || 0,
              quarterly: parseFloat(plan.quarterly_price) || 0,
              yearly: parseFloat(plan.yearly_price) || 0
            },
            color: color,
            popular: plan.display_order === 2 && !isNonProviderPlan, // 非渠道商套餐不标记为热门
            description: isNonProviderPlan ? 
              `${plan.plan_name} - 您当前的套餐` : 
              `${plan.plan_name} - 适合渠道商用户`,
            is_purchased: isCurrentSubscriptionPlan || plan.is_purchased || false, // 是否已购买
            current_subscription_id: plan.current_subscription_id || null, // 当前订阅ID
            is_current_plan: isCurrentSubscriptionPlan, // 标记是否是当前套餐
            is_non_provider: isNonProviderPlan // 标记是否是非渠道商套餐
          };
        });


        setAvailablePlans(formattedPlans);

        // 如果还没有选中套餐，选择第一个可用的套餐
        if (!selectedPlan && formattedPlans.length > 0) {
          const firstAvailablePlan = formattedPlans.find(plan => {
            const isCurrentPlan = latestSubscription &&
              (String(latestSubscription.plan_id) === String(plan.id)) &&
              (latestSubscription.billing_cycle === selectedDuration);
            return !plan.is_purchased && !isCurrentPlan;
          });

          if (firstAvailablePlan) {
            setSelectedPlan(firstAvailablePlan.id);
          } else if (formattedPlans.length > 0) {
            setSelectedPlan(formattedPlans[0].id);
          }
        }

        // 检查当前选中的套餐是否应该被禁用，如果是则重新选择
        if (formattedPlans.length > 0 && selectedPlan) {
          const currentSelectedPlan = formattedPlans.find(plan => String(plan.id) === String(selectedPlan));

          if (currentSelectedPlan) {
            // 检查是否是当前用户正在使用的套餐且付费周期相同
            const isCurrentPlan = latestSubscription &&
              (String(latestSubscription.plan_id) === String(currentSelectedPlan.id)) &&
              (latestSubscription.billing_cycle === selectedDuration);
            const shouldDisable = currentSelectedPlan.is_purchased || isCurrentPlan;

            if (shouldDisable) {
              // 当前选中的套餐应该被禁用，选择第一个未禁用的套餐
              const firstAvailablePlan = formattedPlans.find(plan => {
                // 检查是否是当前用户正在使用的套餐且付费周期相同
                const isCurrentPlan = latestSubscription &&
                  (String(latestSubscription.plan_id) === String(plan.id)) &&
                  (latestSubscription.billing_cycle === selectedDuration);
                const shouldDisable = plan.is_purchased || isCurrentPlan;
                return !shouldDisable;
              });

              if (firstAvailablePlan) {
                setSelectedPlan(firstAvailablePlan.id);
              }
            }
          } else {
            // 当前选中的套餐不在新列表中，选择第一个未禁用的套餐
            const firstAvailablePlan = formattedPlans.find(plan => {
              // 检查是否是当前用户正在使用的套餐且付费周期相同
              const isCurrentPlan = latestSubscription &&
                (String(latestSubscription.plan_id) === String(plan.id)) &&
                (latestSubscription.billing_cycle === selectedDuration);
              const shouldDisable = plan.is_purchased || isCurrentPlan;
              return !shouldDisable;
            });

            if (firstAvailablePlan) {
              setSelectedPlan(firstAvailablePlan.id);
            } else if (formattedPlans.length > 0) {
              // 如果没有可用的套餐，选择第一个
              setSelectedPlan(formattedPlans[0].id);
            }
          }
        }
      }
    } catch (error) {
      console.error('获取渠道商订阅计划失败:', error);
      // 如果API调用失败，设置空数组，不使用静态数据
      setAvailablePlans([]);
    } finally {
      setLoadingPlans(false);
    }
  };

  // 获取当前用户订阅状态
  const fetchCurrentSubscription = async () => {
    try {
      const response = await subscriptionService.getCurrentSubscription('provider');
      setCurrentSubscription(response);

      // 如果用户有订阅，自动切换到用户当前的付费周期
      if (response && response.billing_cycle && response.billing_cycle !== selectedDuration) {
        setSelectedDuration(response.billing_cycle);
      }

      return response;
    } catch (error) {
      // 用户可能没有订阅，这是正常的

      setCurrentSubscription(null);
      return null;
    }
  };

  // 升级后刷新状态
  const refreshAfterUpgrade = async (newBillingCycle) => {
    // 先获取最新的订阅状态
    const latestSubscription = await fetchCurrentSubscription();
    // 然后刷新套餐列表
    await fetchAvailablePlans(newBillingCycle);
    return latestSubscription;
  };

  // 组件挂载时获取数据
  useEffect(() => {
    const initializeData = async () => {
      // 并行获取公告、套餐列表和当前订阅状态
      await Promise.all([
        fetchAnnouncements(),
        fetchAvailablePlans(),  // 直接获取套餐，后端会自动标记当前使用的套餐
        fetchCurrentSubscription()  // 获取当前订阅状态
      ]);
    };
    initializeData();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  // 当付费周期改变时重新获取套餐数据
  useEffect(() => {
    if (upgradeDialogOpen) {
      fetchAvailablePlans(selectedDuration);
    }
  }, [selectedDuration, upgradeDialogOpen]); // eslint-disable-line react-hooks/exhaustive-deps

  // 当付费周期改变时（包括自动切换），重新获取套餐数据
  useEffect(() => {
    // 只有在组件已经初始化后才重新获取套餐数据
    if (currentSubscription !== undefined) {
      fetchAvailablePlans(selectedDuration);
    }
  }, [selectedDuration]); // eslint-disable-line react-hooks/exhaustive-deps

  // 导航菜单配置 - 改为二级菜单结构
  const navigationItems = [
    {
      id: 'dashboard',
      title: '数据概览',
      icon: <Dashboard />,
      path: 'dashboard',
      isStandalone: true
    },
    {
      id: 'order-platform',
      title: '接单平台',
      icon: <Work />,
      children: [
        {
          id: 'browse',
          title: '浏览需求',
          icon: <Search />,
          path: 'browse'
        },
        {
          id: 'tasks',
          title: '我的任务',
          icon: <Assignment />,
          path: 'tasks'
        }
      ]
    },
    {
      id: 'finance',
      title: '财务管理',
      icon: <AccountBalance />,
      children: [
        {
          id: 'revenue',
          title: '收益统计',
          icon: <Assessment />,
          path: 'revenue'
        },
        {
          id: 'withdraw',
          title: '提现管理',
          icon: <MonetizationOn />,
          path: 'withdraw'
        }
      ]
    },
    {
      id: 'content',
      title: '内容创作',
      icon: <Create />,
      children: [
        {
          id: 'ai-content',
          title: 'AI内容创作',
          icon: <AutoAwesome />,
          path: 'ai-content'
        },
        {
          id: 'content-list',
          title: '内容列表',
          icon: <Article />,
          path: 'content-list'
        },
        {
          id: 'writing-templates',
          title: '写作模板',
          icon: <Article />,
          path: 'writing-templates'
        },
        {
          id: 'knowledge-base',
          title: '知识库管理',
          icon: <LibraryBooks />,
          path: 'knowledge-base'
        }
      ]
    },
    {
      id: 'announcements',
      title: '最新公告',
      icon: <Campaign />,
      path: 'announcements',
      isStandalone: true
    },
    {
      id: 'account',
      title: '账户设置',
      icon: <ManageAccounts />,
      children: [
        {
          id: 'channels',
          title: '我的渠道',
          icon: <Store />,
          path: 'channels'
        },
        {
          id: 'account',
          title: '系统设置',
          icon: <Settings />,
          path: 'account'
        }
      ]
    }
  ];

  const handleMenuClick = (item) => {
    if (item.children && !sidebarCollapsed) {
      // 如果有子菜单且侧边栏未折叠，则切换菜单展开状态
      setOpenMenus(prev => ({
        ...prev,
        [item.id]: !prev[item.id]
      }));
    } else if (item.children && sidebarCollapsed) {
      // 如果有子菜单但侧边栏已折叠，跳转到第一个子菜单项
      const firstChild = item.children[0];
      if (firstChild && firstChild.path) {
        setCurrentPage(firstChild.path);
      }
    } else if (item.path) {
      // 如果是独立菜单项，直接跳转
      setCurrentPage(item.path);
    }
  };

  const handleToggleMenu = (menuId) => {
    setOpenMenus(prev => ({
      ...prev,
      [menuId]: !prev[menuId]
    }));
  };

  // 订阅套餐配置已移除，完全依赖API数据

  const durationOptions = [
    { id: 'monthly', label: '月付', discount: 0 },
    { id: 'quarterly', label: '季付', discount: 10 },
    { id: 'yearly', label: '年付', discount: 17 }
  ];

  const handleUpgradeClick = async () => {
    setUpgradeDialogOpen(true);
    // 获取可用套餐，后端会自动标记当前使用的套餐
    await fetchAvailablePlans(selectedDuration);
  };

  const handleCloseDialog = () => {
    setUpgradeDialogOpen(false);
  };

  // 处理续费
  const handleRenewalClick = async () => {
    if (!currentSubscription) {
      alert('没有找到当前订阅信息');
      return;
    }

    try {
      const renewalData = {
        subscription_id: currentSubscription.id,
        billing_cycle: currentSubscription.billing_cycle,
        payment_method: 'default',
        apply_discount: true
      };

      const response = await subscriptionService.renewSubscription(renewalData);

      if (response) {
        alert('续费成功！');
        // 重新获取套餐列表，后端会自动更新当前套餐状态
        await fetchAvailablePlans(selectedDuration);
      }
    } catch (error) {
      console.error('续费失败:', error);
      alert('续费失败：' + (error.response?.data?.detail || error.message));
    }
  };

  const handleConfirmUpgrade = async () => {
    try {
      setIsProcessingPayment(true);

      // 找到选中的套餐
      const selectedPlanData = availablePlans.find(plan => plan.id === selectedPlan);
      if (!selectedPlanData) {
        alert('请选择一个有效的套餐');
        return;
      }

      // 判断是升级还是购买新套餐
      const isSamePlan = currentSubscription && (currentSubscription.plan_id === selectedPlan);
      const isDifferentBillingCycle = currentSubscription && (currentSubscription.billing_cycle !== selectedDuration);

      if (currentSubscription && isSamePlan && isDifferentBillingCycle) {
        // 相同套餐不同付费周期 - 走升级流程
        const upgradeData = {
          target_plan_id: selectedPlan,
          billing_cycle: selectedDuration,
          effective_immediately: true,
          prorate: true
        };

        const response = await subscriptionService.upgradeSubscription(upgradeData);

        // 检查响应格式 - 后端直接返回 SubscriptionChangeResponse
        if (response && response.status === 'success') {
          alert(response.message || '升级成功！');
          setUpgradeDialogOpen(false);

          // 延迟一下再刷新，确保后端数据已更新
          await new Promise(resolve => setTimeout(resolve, 1000));

          // 刷新套餐数据，强制刷新
          await fetchAvailablePlans(selectedDuration, true);
        } else {
          throw new Error('升级失败');
        }
      } else {
        // 新用户购买 或 不同套餐购买 - 走完整购买流程（创建订单）
        // 新用户购买流程：订单 → 支付 → 订阅

        // 1. 创建订单
        const serviceMonths = selectedDuration === 'monthly' ? 1 :
                             selectedDuration === 'quarterly' ? 3 :
                             selectedDuration === 'yearly' ? 12 : 12;

        const orderData = {
          plan_id: selectedPlan,
          service_months: serviceMonths,
          customer_note: '渠道商用户购买套餐',
          auto_renewal: false
        };

        const orderResponse = await orderService.createSubscriptionOrder(orderData);

        // 检查响应格式 - 后端返回 { success: true, data: {...}, message: "..." }
        if (!orderResponse || !orderResponse.success || !orderResponse.data || !orderResponse.data.id) {
          console.error('订单响应格式错误:', orderResponse);
          throw new Error('订单创建失败');
        }

        // 后端已经自动完成支付，直接处理成功逻辑
        
        // 检查订单状态是否为已支付
        if (orderResponse.data.order_status === 'paid') {
          alert(orderResponse.message || '订阅成功！');
          setUpgradeDialogOpen(false);

          // 延迟一下再刷新，确保后端数据已更新
          await new Promise(resolve => setTimeout(resolve, 1000));

          // 重新获取套餐数据，强制刷新
          await fetchAvailablePlans(selectedDuration, true);
        } else {
          console.error('订单状态异常:', orderResponse.data.order_status);
          throw new Error('订单支付未完成');
        }
      }
    } catch (error) {
      let errorMessage = currentSubscription ? '升级失败：' : '订阅失败：';

      if (error.response && error.response.data && error.response.data.detail) {
        const detail = error.response.data.detail;
        if (detail.includes('already has an active subscription')) {
          errorMessage = '您已经有一个活跃的订阅，无法重复订阅。如需升级，请使用升级功能。';
        } else {
          errorMessage += detail;
        }
      } else {
        errorMessage += error.message || '未知错误';
      }

      alert(errorMessage);
    } finally {
      setIsProcessingPayment(false);
    }
  };

  // 渲染页面内容
  const renderPageContent = () => {
    try {
      switch (currentPage) {
        case 'dashboard':
          return renderDashboardContent();
        case 'browse':
          return <BrowseRequirements />;
        case 'tasks':
          return <TaskManagement />;
        case 'revenue':
          return <RevenueStatistics />;
        case 'withdraw':
          return <WithdrawManagement />;
        case 'channels':
          return renderChannelsContent();
        case 'ai-content':
          return <AIContentCreation />;
        case 'content-list':
          return <ChannelContentManagement />;
        case 'writing-templates':
          return <WritingTemplates />;
        case 'knowledge-base':
          return <KnowledgeBase />;
        case 'announcements':
          return <ChannelAnnouncements />;
        case 'account':
          return <AccountSettings />;
        default:
          return renderDashboardContent();
      }
    } catch (err) {

      return (
        <Box sx={{ p: 3, textAlign: 'center' }}>
          <Typography variant="h5" sx={{ mb: 2, color: 'error.main' }}>
            页面加载出错
          </Typography>
          <Typography variant="body1" color="text.secondary">
            {err.message || '未知错误，请刷新页面重试'}
          </Typography>
        </Box>
      );
    }
  };

  // 获取渠道商公告数据
  const fetchAnnouncements = async () => {
    try {
      setAnnouncementLoading(true);

      const data = await announcementService.getAnnouncements({
        page: 1,
        page_size: 5, // 只获取前5条用于展示
        target_audience: 'channel' // 渠道商专用参数
      });

      let items = [];
      if (data && data.success && data.data) {
        if (Array.isArray(data.data.items)) {
          items = data.data.items;
        } else if (Array.isArray(data.data)) {
          items = data.data;
        }
      } else if (data && Array.isArray(data.items)) {
        items = data.items;
      } else if (data && Array.isArray(data)) {
        items = data;
      }

      if (items && Array.isArray(items)) {
        // 转换API数据格式为组件需要的格式
        const formattedAnnouncements = items.map(item => ({
          id: item.id,
          title: item.title,
          content: item.content || item.summary || '',
          type: mapAnnouncementType(item.type),
          date: formatDate(item.publish_time || item.created_at),
          priority: mapPriority(item.priority),
          author: item.creator_name || '系统管理员'
        }));

        setAnnouncements(formattedAnnouncements);
      } else {
        console.warn('无法处理的数据格式:', data);
        setAnnouncements([]);
      }
    } catch (error) {
      console.error('获取公告数据失败:', error);
      setAnnouncements([]);
    } finally {
      setAnnouncementLoading(false);
    }
  };

  // 映射公告类型
  const mapAnnouncementType = (apiType) => {
    const typeMap = {
      'system': 'warning',
      'maintenance': 'warning',
      'feature': 'success',
      'promotion': 'announcement',
      'notice': 'info'
    };
    return typeMap[apiType] || 'info';
  };

  // 映射优先级
  const mapPriority = (apiPriority) => {
    const priorityMap = {
      'high': 'high',
      'medium': 'medium',
      'low': 'low'
    };
    return priorityMap[apiPriority] || 'medium';
  };

  // 格式化日期
  const formatDate = (dateString) => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // 获取公告详情
  const fetchAnnouncementDetail = async (announcementId) => {
    try {
      setDetailLoading(true);

      const data = await announcementService.getAnnouncementById(announcementId);

      // 检查不同的响应格式
      let item = null;
      if (data && data.success && data.data) {
        item = data.data;
      } else if (data && data.data) {
        // 有些API直接返回data
        item = data.data;
      } else if (data && data.success !== false && data.id) {
        // 直接返回公告对象
        item = data;
      } else if (data && data.items && Array.isArray(data.items) && data.items.length > 0) {
        // 处理可能的数组响应格式
        item = data.items[0];
      } else {
        console.warn('无法解析公告详情响应:', data);
        alert('获取公告详情失败');
        return;
      }

      if (item) {
        // 转换API数据格式
        const formattedAnnouncement = {
          id: item.id,
          title: item.title,
          content: item.content || item.summary || '',
          summary: item.summary || '',
          type: mapAnnouncementType(item.type),
          date: formatDate(item.publish_time || item.created_at),
          priority: mapPriority(item.priority),
          author: item.creator_name || '系统管理员',
          viewCount: item.view_count || 0,
          targetAudience: item.target_audience,
          status: item.status,
          isPinned: item.is_pinned,
          isPopup: item.is_popup,
          expireTime: item.expire_time ? formatDate(item.expire_time) : null
        };

        setSelectedAnnouncement(formattedAnnouncement);
        setAnnouncementDetailOpen(true);
      } else {
        console.error('公告详情数据为空');
        alert('获取公告详情失败');
      }
    } catch (error) {
      console.error('获取公告详情失败:', error);
      alert('获取公告详情失败，请稍后重试');
    } finally {
      setDetailLoading(false);
    }
  };

  // 处理公告点击事件
  const handleAnnouncementClick = (announcement) => {
    fetchAnnouncementDetail(announcement.id);
  };

  // 关闭公告详情弹窗
  const handleCloseAnnouncementDetail = () => {
    setAnnouncementDetailOpen(false);
    setSelectedAnnouncement(null);
  };

  // 获取公告图标
  const getAnnouncementIcon = (type) => {
    switch(type) {
      case 'warning':
        return <Settings sx={{ color: '#f59e0b', fontSize: 18 }} />;
      case 'info':
        return <Dashboard sx={{ color: '#3b82f6', fontSize: 18 }} />;
      case 'success':
        return <CheckCircle sx={{ color: '#10b981', fontSize: 18 }} />;
      case 'announcement':
        return <Campaign sx={{ color: '#8b5cf6', fontSize: 18 }} />;
      default:
        return <Article sx={{ color: '#6b7280', fontSize: 18 }} />;
    }
  };

  // 获取公告背景颜色
  const getAnnouncementBgColor = (type) => {
    switch(type) {
      case 'warning': return '#fef3c7';
      case 'info': return '#dbeafe';
      case 'success': return '#d1fae5';
      case 'announcement': return '#ede9fe';
      default: return '#f3f4f6';
    }
  };

  // 渲染控制台内容
  const renderDashboardContent = () => {
    // 模拟最近任务数据
    const recentTasks = [
      { id: 1, title: '为科技公司撰写产品介绍', status: '进行中', reward: '¥3,500', deadline: '2024-01-20', client: '深圳科技有限公司' },
      { id: 2, title: 'AI行业分析报告', status: '待审核', reward: '¥5,000', deadline: '2024-01-18', client: '北京智能科技' },
      { id: 3, title: '企业品牌故事创作', status: '已完成', reward: '¥2,800', deadline: '2024-01-15', client: '上海品牌咨询' },
      { id: 4, title: 'SEO优化方案设计', status: '进行中', reward: '¥4,200', deadline: '2024-01-22', client: '广州电商集团' },
      { id: 5, title: '内容营销策略规划', status: '待接单', reward: '¥6,000', deadline: '2024-01-25', client: '杭州互联网公司' }
    ];

    const getStatusColor = (status) => {
      switch(status) {
        case '进行中': return '#3b82f6';
        case '待审核': return '#f59e0b';
        case '已完成': return '#10b981';
        case '待接单': return '#8b5cf6';
        default: return '#6b7280';
      }
    };

    return (
      <Box sx={{ width: '100%', backgroundColor: 'white', minHeight: '100vh' }}>
        {/* 顶部欢迎区域 */}
        <Box sx={{
          borderBottom: '1px solid #f0f0f0',
          backgroundColor: 'white'
        }}>
          <Container maxWidth="xl">
            <Box sx={{ py: 5 }}>
              <Typography variant="h3" sx={{
                fontWeight: 700,
                color: '#1a1a1a',
                mb: 1,
                fontSize: { xs: '2rem', md: '2.5rem' }
              }}>
                渠道商控制台
              </Typography>
              <Typography variant="body1" sx={{
                color: '#6b7280',
                fontSize: '1.125rem'
              }}>
                管理任务，追踪收益，提升服务质量
              </Typography>
            </Box>
          </Container>
        </Box>

        {/* 核心数据统计栏 */}
        <Box sx={{
          borderBottom: '1px solid #f0f0f0',
          backgroundColor: '#fafafa'
        }}>
          <Container maxWidth="xl">
            <Box sx={{ py: 4 }}>
              <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', sm: 'repeat(2, 1fr)', md: 'repeat(4, 1fr)' }, gap: 4 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Box sx={{
                    width: 44,
                    height: 44,
                    borderRadius: '12px',
                    backgroundColor: '#eff6ff',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <MonetizationOn sx={{ color: '#3b82f6', fontSize: 22 }} />
                  </Box>
                  <Box>
                    <Typography variant="caption" sx={{ 
                      color: '#9ca3af', 
                      fontSize: '0.75rem',
                      fontWeight: 500,
                      textTransform: 'uppercase',
                      letterSpacing: '0.05em'
                    }}>
                      总收益
                    </Typography>
                    <Typography variant="h6" sx={{ 
                      color: '#1a1a1a',
                      fontWeight: 600,
                      fontSize: '1.5rem',
                      mt: 0.5
                    }}>
                      ¥58,600
                    </Typography>
                  </Box>
                </Box>

                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Box sx={{
                    width: 44,
                    height: 44,
                    borderRadius: '12px',
                    backgroundColor: '#fef3c7',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <Assignment sx={{ color: '#f59e0b', fontSize: 22 }} />
                  </Box>
                  <Box>
                    <Typography variant="caption" sx={{ 
                      color: '#9ca3af', 
                      fontSize: '0.75rem',
                      fontWeight: 500,
                      textTransform: 'uppercase',
                      letterSpacing: '0.05em'
                    }}>
                      进行中任务
                    </Typography>
                    <Typography variant="h6" sx={{ 
                      color: '#1a1a1a',
                      fontWeight: 600,
                      fontSize: '1.5rem',
                      mt: 0.5
                    }}>
                      5
                    </Typography>
                  </Box>
                </Box>

                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Box sx={{
                    width: 44,
                    height: 44,
                    borderRadius: '12px',
                    backgroundColor: '#f0fdf4',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <Assessment sx={{ color: '#10b981', fontSize: 22 }} />
                  </Box>
                  <Box>
                    <Typography variant="caption" sx={{ 
                      color: '#9ca3af', 
                      fontSize: '0.75rem',
                      fontWeight: 500,
                      textTransform: 'uppercase',
                      letterSpacing: '0.05em'
                    }}>
                      完成任务
                    </Typography>
                    <Typography variant="h6" sx={{ 
                      color: '#1a1a1a',
                      fontWeight: 600,
                      fontSize: '1.5rem',
                      mt: 0.5
                    }}>
                      23
                    </Typography>
                  </Box>
                </Box>

                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Box sx={{
                    width: 44,
                    height: 44,
                    borderRadius: '12px',
                    backgroundColor: '#fce7f3',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <AutoAwesome sx={{ color: '#ec4899', fontSize: 22 }} />
                  </Box>
                  <Box>
                    <Typography variant="caption" sx={{ 
                      color: '#9ca3af', 
                      fontSize: '0.75rem',
                      fontWeight: 500,
                      textTransform: 'uppercase',
                      letterSpacing: '0.05em'
                    }}>
                      服务评分
                    </Typography>
                    <Typography variant="h6" sx={{ 
                      color: '#1a1a1a',
                      fontWeight: 600,
                      fontSize: '1.5rem',
                      mt: 0.5
                    }}>
                      4.8/5.0
                    </Typography>
                  </Box>
                </Box>
              </Box>
            </Box>
          </Container>
        </Box>

        {/* 主要内容区域 */}
        <Box sx={{ backgroundColor: 'white' }}>
          <Container maxWidth="xl">
            <Box sx={{ py: 5 }}>
              <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', md: 'repeat(2, 1fr)' }, gap: 4 }}>
                {/* 公告栏 */}
                <Card sx={{ 
                  borderRadius: 2,
                  border: '1px solid #e5e7eb',
                  boxShadow: 'none',
                  height: '100%',
                  minHeight: 400,
                  maxHeight: 500,
                  display: 'flex',
                  flexDirection: 'column'
                }}>
                  <Box sx={{ p: 2 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Box sx={{ 
                          backgroundColor: '#eff6ff',
                          width: 28,
                          height: 28,
                          borderRadius: '50%',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center'
                        }}>
                          <Dashboard sx={{ color: '#3b82f6', fontSize: 18 }} />
                        </Box>
                        <Typography variant="subtitle1" sx={{ 
                          fontWeight: 600,
                          color: '#1a1a1a',
                          fontSize: '1rem'
                        }}>
                          系统公告
                        </Typography>
                      </Box>
                      <Button
                        size="small"
                        variant="text"
                        sx={{ color: '#6b7280', fontSize: '0.75rem' }}
                        onClick={() => {
                          setCurrentPage('announcements');
                        }}
                      >
                        查看全部
                      </Button>
                    </Box>

                    {/* 公告列表 */}
                    <List sx={{ p: 0, flex: 1, overflowY: 'auto' }}>
                      {announcementLoading ? (
                        // 加载状态
                        <Box sx={{ display: 'flex', justifyContent: 'center', py: 3 }}>
                          <Typography variant="body2" color="text.secondary">
                            加载中...
                          </Typography>
                        </Box>
                      ) : announcements.length === 0 ? (
                        // 空状态
                        <Box sx={{ display: 'flex', justifyContent: 'center', py: 3 }}>
                          <Typography variant="body2" color="text.secondary">
                            暂无公告
                          </Typography>
                        </Box>
                      ) : (
                        // 公告列表
                        announcements.slice(0, 3).map((announcement, index) => (
                        <Fragment key={announcement.id}>
                          <ListItemButton
                            sx={{
                              px: 1,
                              py: 0.5,
                              borderRadius: 1,
                              mb: 0.5,
                              backgroundColor: getAnnouncementBgColor(announcement.type),
                              '&:hover': {
                                backgroundColor: announcement.type === 'warning' ? '#fde68a' :
                                               announcement.type === 'info' ? '#bfdbfe' :
                                               announcement.type === 'success' ? '#a7f3d0' :
                                               announcement.type === 'announcement' ? '#ddd6fe' :
                                               '#e5e7eb',
                                cursor: 'pointer'
                              }
                            }}
                            onClick={() => handleAnnouncementClick(announcement)}
                          >
                            <ListItemIcon sx={{ minWidth: 32 }}>
                              {getAnnouncementIcon(announcement.type)}
                            </ListItemIcon>
                            <ListItemText
                              primary={announcement.title + (announcement.priority === 'high' ? ' [重要]' : '')}
                              secondary={
                                `${announcement.content.length > 60 ? announcement.content.substring(0, 60) + '...' : announcement.content} • ${announcement.date.split(' ')[0]}`
                              }
                              primaryTypographyProps={{
                                variant: 'body2',
                                sx: {
                                  fontWeight: 600,
                                  color: announcement.priority === 'high' ? '#ef4444' : '#1a1a1a',
                                  fontSize: '0.875rem'
                                }
                              }}
                              secondaryTypographyProps={{
                                variant: 'caption',
                                sx: {
                                  color: '#4b5563',
                                  fontSize: '0.75rem'
                                }
                              }}
                            />
                          </ListItemButton>
                        </Fragment>
                      ))
                      )}
                    </List>
                  </Box>
                </Card>

                {/* 最近的任务 */}
                <Card sx={{ 
                  borderRadius: 2,
                  border: '1px solid #e5e7eb',
                  boxShadow: 'none',
                  height: '100%',
                  minHeight: 400,
                  maxHeight: 500,
                  display: 'flex',
                  flexDirection: 'column'
                }}>
                  <Box sx={{ p: 2 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Box sx={{ 
                          backgroundColor: '#f0fdf4',
                          width: 28,
                          height: 28,
                          borderRadius: '50%',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center'
                        }}>
                          <Assignment sx={{ color: '#10b981', fontSize: 18 }} />
                        </Box>
                        <Typography variant="subtitle1" sx={{ 
                          fontWeight: 600,
                          color: '#1a1a1a',
                          fontSize: '1rem'
                        }}>
                          最近的任务
                        </Typography>
                      </Box>
                      <Button 
                        variant="text" 
                        sx={{ 
                          color: '#6b7280',
                          fontSize: '0.75rem'
                        }}
                      >
                        查看全部
                      </Button>
                    </Box>

                    <List sx={{ p: 0 }}>
                      {recentTasks.map((task, index) => (
                        <Fragment key={task.id}>
                          <ListItemButton
                            sx={{ 
                              px: 1,
                              py: 1,
                              '&:hover': {
                                backgroundColor: '#f9fafb'
                              }
                            }}
                          >
                            <ListItemText
                              primary={`${task.title} [${task.status}]`}
                              secondary={`${task.client} · 截止：${task.deadline} · ${task.reward}`}
                              primaryTypographyProps={{
                                variant: 'body2',
                                sx: {
                                  fontWeight: 500,
                                  color: '#1a1a1a',
                                  fontSize: '0.875rem'
                                }
                              }}
                              secondaryTypographyProps={{
                                variant: 'caption',
                                sx: {
                                  color: '#6b7280',
                                  fontSize: '0.75rem'
                                }
                              }}
                            />
                          </ListItemButton>
                        </Fragment>
                      ))}
                    </List>
                  </Box>
                </Card>
              </Box>
            </Box>
          </Container>
        </Box>
      </Box>
    );
  };

  // 渲染我的渠道内容
  const renderChannelsContent = () => <MyChannels />;

  // 如果有错误，显示错误信息
  if (error) {
    return (
      <Box sx={{
        minHeight: '100vh',
        backgroundColor: '#f5f5f5',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <Card sx={{ maxWidth: 500, p: 4 }}>
          <Typography variant="h5" sx={{ mb: 2, textAlign: 'center', color: 'error.main' }}>
            加载出错
          </Typography>
          <Typography variant="body1" color="text.secondary" sx={{ textAlign: 'center' }}>
            {error}
          </Typography>
          <Button 
            variant="contained" 
            sx={{ mt: 2, display: 'block', mx: 'auto' }}
            onClick={() => window.location.reload()}
          >
            刷新页面
          </Button>
        </Card>
      </Box>
    );
  }

  // 如果用户未登录，显示简单的登录提示
  if (!user) {
    return (
      <Box sx={{
        minHeight: '100vh',
        backgroundColor: '#f5f5f5',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <Card sx={{ maxWidth: 400, p: 4 }}>
          <Typography variant="h5" sx={{ mb: 2, textAlign: 'center' }}>
            渠道商控制中心
          </Typography>
          <Typography variant="body1" color="text.secondary" sx={{ textAlign: 'center' }}>
            请先登录后访问
          </Typography>
        </Card>
      </Box>
    );
  }

  // 正常渲染主界面
  return (
    <Box sx={{ display: 'flex', height: '100vh', backgroundColor: '#ffffff' }}>
      {/* 侧边栏 */}
      <Drawer
        variant="persistent"
        anchor="left"
        open={sidebarOpen}
        sx={{
          width: sidebarOpen ? (sidebarCollapsed ? 72 : 280) : 0,
          flexShrink: 0,
          transition: 'width 0.3s ease',
          '& .MuiDrawer-paper': {
            width: sidebarCollapsed ? 72 : 280,
            boxSizing: 'border-box',
            backgroundColor: '#fff',
            borderRight: '1px solid #e0e0e0',
            boxShadow: '2px 0 8px rgba(0,0,0,0.1)',
            height: '100vh',
            overflow: 'hidden',
            display: 'flex',
            flexDirection: 'column',
            transition: 'width 0.3s ease'
          },
        }}
      >
        {/* 侧边栏头部 */}
        <Box sx={{
          p: sidebarCollapsed ? 1 : 3,
          borderBottom: '1px solid #e0e0e0',
          transition: 'padding 0.3s ease'
        }}>
          {!sidebarCollapsed ? (
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Store sx={{ fontSize: 28, color: '#1976d2' }} />
              <Typography variant="h6" sx={{ fontWeight: 700, color: '#1a1a1a', flex: 1 }}>
                渠道商控制中心
              </Typography>
              <IconButton
                onClick={() => setSidebarCollapsed(true)}
                size="small"
                sx={{ color: '#666' }}
              >
                <MenuOpen />
              </IconButton>
            </Box>
          ) : (
            <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 1 }}>
              <Store sx={{ fontSize: 24, color: '#1976d2' }} />
              <IconButton
                onClick={() => setSidebarCollapsed(false)}
                size="small"
                sx={{ color: '#666' }}
              >
                <Menu />
              </IconButton>
            </Box>
          )}
        </Box>

        {/* 用户信息 - 只在侧边栏展开时显示 */}
        {!sidebarCollapsed && (
          <Box sx={{
            p: 2,
            borderBottom: '1px solid #e0e0e0'
          }}>
            <Box sx={{
              p: 2,
              backgroundColor: '#f8fafc',
              borderRadius: 2,
              border: '1px solid #e5e7eb'
            }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>
                <Avatar sx={{
                  width: 40,
                  height: 40,
                  backgroundColor: '#3b82f6',
                  fontSize: '1rem'
                }}>
                  {user?.name ? user.name.charAt(0).toUpperCase() : 'U'}
                </Avatar>
                <Box sx={{ flex: 1 }}>
                  <Typography variant="body1" sx={{
                    fontWeight: 600,
                    color: '#1a1a1a',
                    fontSize: '0.95rem'
                  }}>
                    {user?.name || user?.username || '渠道商用户'}
                  </Typography>
                  <Typography variant="caption" sx={{
                    color: '#6b7280',
                    fontSize: '0.75rem'
                  }}>
                    {user?.email || '<EMAIL>'}
                  </Typography>
                </Box>
              </Box>
            </Box>
          </Box>
        )}

        {/* 导航菜单 */}
        <List sx={{
          flexGrow: 1,
          py: 2,
          overflow: 'auto',
          height: 0
        }}>
          {navigationItems.map((item) => (
            <Box key={item.id}>
              {/* 独立菜单项（如数据概览） */}
              {item.isStandalone ? (
                <ListItemButton
                  onClick={() => handleMenuClick(item)}
                  selected={currentPage === item.path}
                  sx={{
                    mx: sidebarCollapsed ? 0.5 : 2,
                    mb: 1,
                    borderRadius: 2,
                    backgroundColor: currentPage === item.path ? 'rgba(25, 118, 210, 0.1)' : 'transparent',
                    '&:hover': {
                      backgroundColor: 'rgba(25, 118, 210, 0.05)',
                    },
                    justifyContent: sidebarCollapsed ? 'center' : 'flex-start',
                    px: sidebarCollapsed ? 1 : 2
                  }}
                >
                  <ListItemIcon sx={{
                    color: currentPage === item.path ? '#1976d2' : 'inherit',
                    minWidth: sidebarCollapsed ? 'auto' : 40,
                    justifyContent: 'center'
                  }}>
                    {item.icon}
                  </ListItemIcon>
                  {!sidebarCollapsed && (
                    <ListItemText
                      primary={item.title}
                      sx={{
                        '& .MuiListItemText-primary': {
                          fontWeight: currentPage === item.path ? 600 : 400,
                          color: currentPage === item.path ? '#1976d2' : 'inherit'
                        }
                      }}
                    />
                  )}
                </ListItemButton>
              ) : (
                /* 一级菜单 */
                <>
                  <ListItemButton
                    onClick={() => handleMenuClick(item)}
                    sx={{
                      mx: sidebarCollapsed ? 0.5 : 2,
                      mb: 1,
                      borderRadius: 2,
                      backgroundColor: 'transparent',
                      '&:hover': {
                        backgroundColor: 'rgba(25, 118, 210, 0.05)',
                      },
                      justifyContent: sidebarCollapsed ? 'center' : 'flex-start',
                      px: sidebarCollapsed ? 1 : 2
                    }}
                  >
                    <ListItemIcon sx={{
                      color: 'inherit',
                      minWidth: sidebarCollapsed ? 'auto' : 40,
                      justifyContent: 'center'
                    }}>
                      {item.icon}
                    </ListItemIcon>
                    {!sidebarCollapsed && (
                      <>
                        <ListItemText
                          primary={item.title}
                          sx={{
                            '& .MuiListItemText-primary': {
                              fontWeight: 600,
                              fontSize: '0.95rem'
                            }
                          }}
                        />
                        {openMenus[item.id] ? <ExpandLess /> : <ExpandMore />}
                      </>
                    )}
                  </ListItemButton>

                  {/* 二级菜单 - 只在侧边栏展开时显示 */}
                  {item.children && !sidebarCollapsed && (
                    <Collapse in={openMenus[item.id]} timeout="auto" unmountOnExit>
                      <List component="div" disablePadding>
                        {item.children.map((child) => (
                          <ListItemButton
                            key={child.id}
                            onClick={() => handleMenuClick(child)}
                            selected={currentPage === child.path}
                            sx={{
                              pl: 6,
                              mx: 2,
                              mb: 0.5,
                              borderRadius: 2,
                              backgroundColor: currentPage === child.path ? 'rgba(25, 118, 210, 0.1)' : 'transparent',
                              '&:hover': {
                                backgroundColor: 'rgba(25, 118, 210, 0.05)',
                              }
                            }}
                          >
                            <ListItemIcon sx={{
                              minWidth: 35,
                              color: currentPage === child.path ? '#1976d2' : '#6b7280'
                            }}>
                              {React.cloneElement(child.icon, {
                                sx: { fontSize: 18 }
                              })}
                            </ListItemIcon>
                            <ListItemText
                              primary={child.title}
                              sx={{
                                '& .MuiListItemText-primary': {
                                  fontSize: '0.875rem',
                                  color: currentPage === child.path ? '#1976d2' : '#4b5563',
                                  fontWeight: currentPage === child.path ? 600 : 400
                                }
                              }}
                            />
                          </ListItemButton>
                        ))}
                      </List>
                    </Collapse>
                  )}
                </>
              )}
            </Box>
          ))}
        </List>

        {/* 订阅组件 - 只在侧边栏展开时显示 */}
        {!sidebarCollapsed && (
          <Box sx={{
            p: 2,
            borderTop: '1px solid #e0e0e0'
          }}>
            <Card sx={{ 
              backgroundColor: '#f0f7ff',
              border: '1px solid #dbeafe',
              borderRadius: 2,
              p: 2,
              boxShadow: 'none'
            }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1.5 }}>
                <WorkspacePremium sx={{ fontSize: 20, color: '#3b82f6' }} />
                <Typography variant="subtitle2" sx={{ 
                  fontWeight: 600,
                  fontSize: '0.9rem',
                  color: '#1e40af'
                }}>
                  订阅状态
                </Typography>
              </Box>
              
              <Box sx={{ mb: 1.5 }}>
                <Chip
                  label={currentSubscription ? currentSubscription.plan_name : "免费版"}
                  size="small"
                  sx={{
                    backgroundColor: currentSubscription ? '#dcfce7' : '#dbeafe',
                    color: currentSubscription ? '#16a34a' : '#1e40af',
                    fontWeight: 500,
                    fontSize: '0.7rem',
                    height: 20
                  }}
                />
                {currentSubscription ? (
                  <Box sx={{ mt: 0.75 }}>
                    <Typography variant="caption" sx={{
                      display: 'block',
                      color: '#64748b',
                      fontSize: '0.65rem'
                    }}>
                      付费周期: {currentSubscription.billing_cycle === 'monthly' ? '月付' :
                                currentSubscription.billing_cycle === 'quarterly' ? '季付' :
                                currentSubscription.billing_cycle === 'yearly' ? '年付' : currentSubscription.billing_cycle}
                    </Typography>
                    <Typography variant="caption" sx={{
                      display: 'block',
                      color: '#64748b',
                      fontSize: '0.65rem'
                    }}>
                      到期时间: {new Date(currentSubscription.end_date).toLocaleDateString('zh-CN')}
                    </Typography>
                    <Typography variant="caption" sx={{
                      display: 'block',
                      color: currentSubscription.days_remaining <= 7 ? '#dc2626' : '#64748b',
                      fontSize: '0.65rem'
                    }}>
                      剩余天数: {currentSubscription.days_remaining}天
                    </Typography>
                    <Box sx={{ mt: 1, display: 'flex', gap: 0.5 }}>
                      <Button
                        size="small"
                        variant="outlined"
                        onClick={() => handleRenewalClick()}
                        sx={{
                          fontSize: '0.6rem',
                          height: 20,
                          minWidth: 'auto',
                          px: 1,
                          borderColor: '#16a34a',
                          color: '#16a34a',
                          '&:hover': {
                            borderColor: '#15803d',
                            backgroundColor: 'rgba(22, 163, 74, 0.04)'
                          }
                        }}
                      >
                        续费
                      </Button>
                      <Button
                        size="small"
                        variant="text"
                        onClick={() => setUpgradeDialogOpen(true)}
                        sx={{
                          fontSize: '0.6rem',
                          height: 20,
                          minWidth: 'auto',
                          px: 1,
                          color: '#3b82f6'
                        }}
                      >
                        升级
                      </Button>
                    </Box>
                  </Box>
                ) : (
                  <Typography variant="caption" sx={{
                    display: 'block',
                    mt: 0.75,
                    color: '#64748b',
                    fontSize: '0.7rem'
                  }}>
                    升级解锁更多功能
                  </Typography>
                )}
              </Box>
              
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5, mb: 1.5 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.75 }}>
                  <StarBorder sx={{ fontSize: 12, color: '#93c5fd' }} />
                  <Typography variant="caption" sx={{ fontSize: '0.65rem', color: '#475569' }}>
                    无限任务接单
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.75 }}>
                  <StarBorder sx={{ fontSize: 12, color: '#93c5fd' }} />
                  <Typography variant="caption" sx={{ fontSize: '0.65rem', color: '#475569' }}>
                    优先任务推荐
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.75 }}>
                  <StarBorder sx={{ fontSize: 12, color: '#93c5fd' }} />
                  <Typography variant="caption" sx={{ fontSize: '0.65rem', color: '#475569' }}>
                    高级数据分析
                  </Typography>
                </Box>
              </Box>
              
              <Button
                fullWidth
                variant="outlined"
                size="small"
                sx={{
                  borderColor: '#3b82f6',
                  color: '#3b82f6',
                  fontSize: '0.75rem',
                  py: 0.5,
                  textTransform: 'none',
                  '&:hover': {
                    backgroundColor: '#eff6ff',
                    borderColor: '#2563eb'
                  }
                }}
                onClick={handleUpgradeClick}
              >
                升级专业版
              </Button>
            </Card>
          </Box>
        )}

        {/* 退出按钮 */}
        <Box sx={{
          p: sidebarCollapsed ? 1 : 2,
          borderTop: '1px solid #e0e0e0',
          mt: 'auto',
          transition: 'padding 0.3s ease'
        }}>
          <ListItemButton
            onClick={() => {
              logout();
              navigate('/auth/login');
            }}
            sx={{
              borderRadius: 2,
              color: '#dc2626',
              transition: 'all 0.2s',
              justifyContent: sidebarCollapsed ? 'center' : 'flex-start',
              px: sidebarCollapsed ? 1 : 2,
              '&:hover': {
                backgroundColor: 'rgba(220, 38, 38, 0.05)',
                color: '#b91c1c',
              }
            }}
          >
            <ListItemIcon sx={{
              color: 'inherit',
              minWidth: sidebarCollapsed ? 'auto' : 40,
              justifyContent: 'center'
            }}>
              <Logout />
            </ListItemIcon>
            {!sidebarCollapsed && (
              <ListItemText
                primary="退出登录"
                sx={{
                  '& .MuiListItemText-primary': {
                    fontWeight: 500
                  }
                }}
              />
            )}
          </ListItemButton>
        </Box>
      </Drawer>

      {/* 主内容区域 */}
      <Box sx={{
        flexGrow: 1,
        display: 'flex',
        flexDirection: 'column',
        marginLeft: sidebarOpen ? 0 : '-280px',
        transition: 'margin-left 0.3s ease'
      }}>
        {loading && <LinearProgress />}

        {/* 页面内容 */}
        <Box sx={{
          flex: 1,
          overflow: 'auto',
          minHeight: 0
        }}>
          {renderPageContent()}
        </Box>
      </Box>

      {/* 升级专业版弹窗 */}
      <Dialog
        open={upgradeDialogOpen}
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 2,
            backgroundColor: '#ffffff'
          }
        }}
      >
        <DialogTitle sx={{ 
          backgroundColor: '#f0f7ff',
          borderBottom: '1px solid #dbeafe',
          pb: 2
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
              <WorkspacePremium sx={{ color: '#3b82f6', fontSize: 28 }} />
              <Typography variant="h5" sx={{ 
                fontWeight: 600,
                color: '#1e40af'
              }}>
                选择订阅套餐
              </Typography>
            </Box>
            <IconButton onClick={handleCloseDialog} size="small">
              <Close />
            </IconButton>
          </Box>
        </DialogTitle>

        <DialogContent sx={{ backgroundColor: '#fafbfc', py: 3 }}>
          <Grid container spacing={3}>
            {/* 左侧 - 套餐卡片 */}
            <Grid item xs={12} md={7}>
              {/* 付费周期选择 */}
              <Box sx={{ mb: 3 }}>
                <Typography variant="subtitle2" sx={{ mb: 1.5, color: '#1e40af', fontWeight: 600 }}>
                  选择付费周期
                </Typography>
                <Box sx={{ 
                  display: 'flex',
                  backgroundColor: '#f0f7ff',
                  borderRadius: 2,
                  p: 0.5,
                  border: '1px solid #dbeafe'
                }}>
                  {durationOptions.map((option) => (
                    <Button
                      key={option.id}
                      variant={selectedDuration === option.id ? 'contained' : 'text'}
                      size="small"
                      onClick={() => setSelectedDuration(option.id)}
                      sx={{
                        flex: 1,
                        mx: 0.25,
                        borderRadius: 1.5,
                        py: 1,
                        backgroundColor: selectedDuration === option.id ? '#3b82f6' : 'transparent',
                        color: selectedDuration === option.id ? 'white' : '#64748b',
                        '&:hover': {
                          backgroundColor: selectedDuration === option.id ? '#2563eb' : '#e0f2fe'
                        }
                      }}
                    >
                      <Box>
                        <Box>{option.label}</Box>
                        {option.discount > 0 && (
                          <Typography variant="caption" sx={{
                            display: 'block',
                            fontSize: '0.65rem',
                            opacity: 0.9
                          }}>
                            省{option.discount}%
                          </Typography>
                        )}
                      </Box>
                    </Button>
                  ))}
                </Box>
              </Box>

              {/* 套餐选择卡片 */}
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                {loadingPlans ? (
                  <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
                    <CircularProgress />
                  </Box>
                ) : (
                  availablePlans.map((plan) => {
                  const currentPrice = plan.pricing[selectedDuration];
                  const monthlyEquivalent = selectedDuration === 'monthly' ? currentPrice :
                                           selectedDuration === 'quarterly' ? Math.round(currentPrice / 3) :
                                           Math.round(currentPrice / 12);

                  // 检查是否是当前用户正在使用的套餐且付费周期相同
                  const isCurrentPlan = (plan.is_current_plan || 
                    (currentSubscription && String(currentSubscription.plan_id) === String(plan.id))) &&
                    currentSubscription?.billing_cycle === selectedDuration;
                  const shouldDisable = isCurrentPlan;

                  return (
                    <Card
                      key={plan.id}
                      onClick={() => !shouldDisable && setSelectedPlan(plan.id)}
                      sx={{
                        position: 'relative',
                        cursor: shouldDisable ? 'default' : 'pointer',
                        border: '2px solid',
                        borderColor: shouldDisable ? '#10b981' : (selectedPlan === plan.id ? plan.color : '#e5e7eb'),
                        backgroundColor: shouldDisable ? '#f0fdf4' : (selectedPlan === plan.id ? '#f0f7ff' : 'white'),
                        opacity: shouldDisable ? 0.8 : 1,
                        transition: 'all 0.2s',
                        '&:hover': {
                          borderColor: shouldDisable ? '#10b981' : plan.color,
                          transform: shouldDisable ? 'none' : 'translateX(4px)',
                          boxShadow: '0 4px 12px rgba(0,0,0,0.08)'
                        }
                      }}
                    >
                      {shouldDisable && (
                        <Chip
                          label="正在使用"
                          size="small"
                          sx={{
                            position: 'absolute',
                            top: 12,
                            right: 12,
                            backgroundColor: '#10b981',
                            color: 'white',
                            fontWeight: 600,
                            fontSize: '0.65rem',
                            height: 20
                          }}
                        />
                      )}
                      {plan.is_non_provider && !shouldDisable && (
                        <Chip
                          label="非渠道商套餐"
                          size="small"
                          sx={{
                            position: 'absolute',
                            top: 12,
                            right: 12,
                            backgroundColor: '#f59e0b',
                            color: 'white',
                            fontWeight: 600,
                            fontSize: '0.65rem',
                            height: 20
                          }}
                        />
                      )}
                      {plan.popular && !plan.is_purchased && !plan.is_non_provider && (
                        <Chip
                          label="推荐"
                          size="small"
                          sx={{
                            position: 'absolute',
                            top: 12,
                            right: 12,
                            backgroundColor: '#3b82f6',
                            color: 'white',
                            fontWeight: 600,
                            fontSize: '0.65rem',
                            height: 20
                          }}
                        />
                      )}

                      <Box sx={{ p: 2.5, display: 'flex', alignItems: 'center', gap: 2 }}>
                        <Radio
                          checked={selectedPlan === plan.id}
                          sx={{
                            color: '#e5e7eb',
                            '&.Mui-checked': {
                              color: plan.color
                            }
                          }}
                        />
                        
                        <Box sx={{
                          width: 44,
                          height: 44,
                          borderRadius: 2,
                          backgroundColor: selectedPlan === plan.id ? plan.color : '#f3f4f6',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          flexShrink: 0
                        }}>
                          {React.cloneElement(plan.icon, { 
                            sx: { 
                              color: selectedPlan === plan.id ? 'white' : plan.color,
                              fontSize: 22
                            }
                          })}
                        </Box>
                        
                        <Box sx={{ flex: 1 }}>
                          <Typography variant="h6" sx={{ 
                            fontWeight: 600,
                            color: '#1a1a1a',
                            fontSize: '1.1rem'
                          }}>
                            {plan.name}
                          </Typography>
                          <Typography variant="body2" sx={{ 
                            color: '#64748b',
                            fontSize: '0.8rem'
                          }}>
                            {plan.features.slice(0, 2).join(' · ')}
                          </Typography>
                        </Box>

                        <Box sx={{ textAlign: 'right' }}>
                          <Box sx={{ display: 'flex', alignItems: 'baseline', gap: 0.5 }}>
                            <Typography variant="h5" sx={{ 
                              fontWeight: 700,
                              color: plan.color
                            }}>
                              ¥{monthlyEquivalent}
                            </Typography>
                            <Typography variant="body2" sx={{ color: '#6b7280' }}>
                              /月
                            </Typography>
                          </Box>
                          <Typography variant="caption" sx={{ 
                            color: '#94a3b8',
                            fontSize: '0.7rem'
                          }}>
                            {selectedDuration === 'monthly' ? '按月付' :
                             selectedDuration === 'quarterly' ? `¥${currentPrice}/季` :
                             `¥${currentPrice}/年`}
                          </Typography>
                        </Box>
                      </Box>
                    </Card>
                  );
                }))}
              </Box>
            </Grid>

            {/* 右侧 - 说明区域 */}
            <Grid item xs={12} md={5}>
              <Box sx={{ 
                position: 'sticky',
                top: 0,
                p: 3,
                backgroundColor: '#f0f7ff',
                borderRadius: 2,
                border: '1px solid #dbeafe'
              }}>
                {/* 选中套餐详情 */}
                <Typography variant="h6" sx={{ 
                  mb: 2,
                  color: '#1e40af',
                  fontWeight: 600,
                  fontSize: '1rem'
                }}>
                  {availablePlans.find(p => p.id === selectedPlan)?.name}功能详情
                </Typography>

                <Box sx={{ mb: 3 }}>
                  {availablePlans.find(p => p.id === selectedPlan)?.features.map((feature, index) => (
                    <Box key={index} sx={{ 
                      display: 'flex', 
                      alignItems: 'flex-start', 
                      gap: 1,
                      mb: 1.5
                    }}>
                      <CheckCircle sx={{ 
                        fontSize: 16,
                        color: '#10b981',
                        mt: 0.25,
                        flexShrink: 0
                      }} />
                      <Typography variant="body2" sx={{ 
                        color: '#475569',
                        fontSize: '0.85rem',
                        lineHeight: 1.5
                      }}>
                        {feature}
                      </Typography>
                    </Box>
                  ))}
                </Box>

                <Divider sx={{ my: 2 }} />

                {/* 价格明细 */}
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" sx={{ 
                    mb: 1,
                    color: '#64748b',
                    fontSize: '0.85rem'
                  }}>
                    价格明细
                  </Typography>
                  
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.75 }}>
                    <Typography variant="body2" sx={{ color: '#64748b', fontSize: '0.85rem' }}>
                      原价
                    </Typography>
                    <Typography variant="body2" sx={{ 
                      color: '#64748b', 
                      fontSize: '0.85rem',
                      textDecoration: selectedDuration !== 'monthly' ? 'line-through' : 'none'
                    }}>
                      ¥{(() => {
                        const plan = availablePlans.find(p => p.id === selectedPlan);
                        if (selectedDuration === 'monthly') return plan?.pricing.monthly;
                        if (selectedDuration === 'quarterly') return plan?.pricing.monthly * 3;
                        return plan?.pricing.monthly * 12;
                      })()}
                    </Typography>
                  </Box>

                  {selectedDuration !== 'monthly' && (
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.75 }}>
                      <Typography variant="body2" sx={{ color: '#10b981', fontSize: '0.85rem' }}>
                        优惠
                      </Typography>
                      <Typography variant="body2" sx={{ color: '#10b981', fontSize: '0.85rem' }}>
                        -{durationOptions.find(d => d.id === selectedDuration)?.discount}%
                      </Typography>
                    </Box>
                  )}
                </Box>

                {/* 总价 */}
                <Box sx={{ 
                  p: 2,
                  backgroundColor: 'white',
                  borderRadius: 1.5,
                  textAlign: 'center'
                }}>
                  <Typography variant="body2" sx={{ color: '#64748b', mb: 0.5, fontSize: '0.8rem' }}>
                    应付总额
                  </Typography>
                  <Typography variant="h4" sx={{ 
                    fontWeight: 700,
                    color: '#1e40af',
                    mb: 0.5
                  }}>
                    ¥{availablePlans.find(p => p.id === selectedPlan)?.pricing[selectedDuration]}
                  </Typography>
                  <Typography variant="caption" sx={{ 
                    color: '#64748b',
                    fontSize: '0.7rem'
                  }}>
                    {selectedDuration === 'monthly' ? '每月自动续费' :
                     selectedDuration === 'quarterly' ? '每3个月自动续费' :
                     '每年自动续费'}
                  </Typography>
                </Box>

                {/* 服务保障 */}
                <Box sx={{ mt: 2, pt: 2, borderTop: '1px solid #dbeafe' }}>
                  <Typography variant="caption" sx={{ 
                    display: 'block',
                    color: '#64748b',
                    fontSize: '0.7rem',
                    textAlign: 'center'
                  }}>
                    · 7天无理由退款 · 发票支持 · 24小时客服 ·
                  </Typography>
                </Box>
              </Box>
            </Grid>
          </Grid>
        </DialogContent>

        <DialogActions sx={{ 
          backgroundColor: '#f0f7ff',
          borderTop: '1px solid #dbeafe',
          p: 2
        }}>
          <Button 
            onClick={handleCloseDialog}
            sx={{ 
              color: '#64748b',
              '&:hover': {
                backgroundColor: '#f1f5f9'
              }
            }}
          >
            取消
          </Button>
          <Button
            variant="contained"
            onClick={handleConfirmUpgrade}
            disabled={isProcessingPayment}
            sx={{
              backgroundColor: '#3b82f6',
              px: 4,
              '&:hover': {
                backgroundColor: '#2563eb'
              }
            }}
          >
            {isProcessingPayment ? '处理中...' : (() => {
              if (!currentSubscription) return '立即订阅';
              const isSamePlan = currentSubscription.plan_id === selectedPlan;
              const isDifferentBillingCycle = currentSubscription.billing_cycle !== selectedDuration;
              if (isSamePlan && isDifferentBillingCycle) return '立即升级';
              return '立即购买';
            })()}
          </Button>
        </DialogActions>
      </Dialog>

      {/* 公告详情弹窗 */}
      <Dialog
        open={announcementDetailOpen}
        onClose={handleCloseAnnouncementDetail}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: { borderRadius: 2 }
        }}
      >
        <DialogTitle sx={{
          pb: 2,
          display: 'flex',
          alignItems: 'center',
          gap: 2
        }}>
          {selectedAnnouncement && (
            <>
              {getAnnouncementIcon(selectedAnnouncement.type)}
              <Box>
                <Typography variant="h6" component="div">
                  {selectedAnnouncement.title}
                  {selectedAnnouncement.priority === 'high' && (
                    <Chip
                      label="重要"
                      size="small"
                      color="error"
                      sx={{ ml: 1 }}
                    />
                  )}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {selectedAnnouncement.author} • {selectedAnnouncement.date}
                  {selectedAnnouncement.viewCount > 0 && ` • 阅读 ${selectedAnnouncement.viewCount} 次`}
                </Typography>
              </Box>
            </>
          )}
        </DialogTitle>

        <DialogContent sx={{ py: 3 }}>
          {detailLoading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
              <CircularProgress />
            </Box>
          ) : selectedAnnouncement ? (
            <Box>
              {/* 公告摘要 */}
              {selectedAnnouncement.summary && selectedAnnouncement.summary !== selectedAnnouncement.content && (
                <Box sx={{ mb: 3, p: 2, backgroundColor: '#f8f9fa', borderRadius: 1 }}>
                  <Typography variant="subtitle2" color="primary" gutterBottom>
                    摘要
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {selectedAnnouncement.summary}
                  </Typography>
                </Box>
              )}

              {/* 公告内容 */}
              <Typography variant="body1" sx={{ lineHeight: 1.8, whiteSpace: 'pre-wrap' }}>
                {selectedAnnouncement.content}
              </Typography>

              {/* 公告信息 */}
              <Box sx={{ mt: 3, pt: 2, borderTop: '1px solid #e5e7eb' }}>
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">
                      <strong>目标受众：</strong>
                      {selectedAnnouncement.targetAudience === 'all' ? '全部用户' :
                       selectedAnnouncement.targetAudience === 'enterprise' ? '企业用户' :
                       selectedAnnouncement.targetAudience === 'channel' ? '渠道商' :
                       selectedAnnouncement.targetAudience === 'agent' ? '代理商' :
                       selectedAnnouncement.targetAudience}
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">
                      <strong>状态：</strong>
                      {selectedAnnouncement.status === 'published' ? '已发布' :
                       selectedAnnouncement.status === 'draft' ? '草稿' :
                       selectedAnnouncement.status}
                    </Typography>
                  </Grid>
                  {selectedAnnouncement.expireTime && (
                    <Grid item xs={12}>
                      <Typography variant="body2" color="text.secondary">
                        <strong>过期时间：</strong>{selectedAnnouncement.expireTime}
                      </Typography>
                    </Grid>
                  )}
                </Grid>
              </Box>
            </Box>
          ) : null}
        </DialogContent>

        <DialogActions sx={{ px: 3, pb: 2 }}>
          <Button onClick={handleCloseAnnouncementDetail}>
            关闭
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}

export default ChannelControlCenter;