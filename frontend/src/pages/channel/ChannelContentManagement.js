import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  Box,
  Typography,
  Button,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  FormControl,
  InputLabel,
  Select,

  Grid,
  Paper,
  InputAdornment,

  LinearProgress,
  Snackbar,
  Alert,
} from '@mui/material';
import {

  Article,
  Edit,
  Send,

  Search,
  CalendarToday,
  Delete,
  TrendingUp,
  FilePresent,
  Download,
  Visibility,
  CheckCircle,
  RateReview,
  Cancel,
} from '@mui/icons-material';
import { contentService } from '../../services/contentService';
import uploadService from '../../services/uploadService';


// 按group_id分组需求的工具函数
const groupRequirementsByGroupId = (requirements) => {
  const groups = {};

  requirements.forEach(req => {
    // 兼容处理：如果没有group_id，使用id作为group_id（每个需求独立分组）
    const groupId = req.group_id || req.id;
    if (!groups[groupId]) {
      groups[groupId] = {
        ...req, // 使用第一个需求作为基础数据
        channels: [], // 存储所有渠道商信息
        totalChannels: 0,
        acceptedChannels: 0,
        pendingChannels: 0,
        rejectedChannels: 0
      };
    }

    // 添加渠道商信息
    groups[groupId].channels.push({
      id: req.id,
      provider_id: req.provider_id,
      provider_info: req.provider_info, // 使用provider_info而不是channel_info
      status: req.status,
      accepted_at: req.accepted_at,
      estimated_delivery_days: req.estimated_delivery_days,
      accept_message: req.accept_message
    });

    // 统计状态
    groups[groupId].totalChannels++;
    if (req.status === 'ACCEPTED') {
      groups[groupId].acceptedChannels++;
    } else if (req.status === 'PENDING') {
      groups[groupId].pendingChannels++;
    } else if (req.status === 'REJECTED') {
      groups[groupId].rejectedChannels++;
    }
  });

  // 转换为数组并设置聚合状态
  return Object.values(groups).map(group => ({
    ...group,
    // 设置聚合状态：如果有接单的就显示进行中，否则显示等待中
    aggregatedStatus: group.acceptedChannels > 0 ? 'IN_PROGRESS' : 'PENDING'
  }));
};

// 状态颜色辅助函数
const getStatusColor = (status) => {
  const normalizedStatus = status?.toLowerCase();
  const colors = {
    pending: '#f59e0b',
    accepted: '#3b82f6',
    in_progress: '#3b82f6',
    delivered: '#8b5cf6',
    completed: '#10b981',
    cancelled: '#ef4444',
    rejected: '#ef4444',
    // 添加大写版本的映射
    'PENDING': '#f59e0b',
    'ACCEPTED': '#3b82f6',
    'IN_PROGRESS': '#3b82f6',
    'DELIVERED': '#8b5cf6',
    'COMPLETED': '#10b981',
    'CANCELLED': '#ef4444',
    'REJECTED': '#ef4444'
  };
  return colors[normalizedStatus] || colors[status] || '#6b7280';
};

const getStatusBgColor = (status) => {
  const normalizedStatus = status?.toLowerCase();
  const colors = {
    pending: '#fef3c7',
    accepted: '#dbeafe',
    in_progress: '#dbeafe',
    delivered: '#e9d5ff',
    completed: '#d1fae5',
    cancelled: '#fee2e2',
    rejected: '#fee2e2',
    // 添加大写版本的映射
    'PENDING': '#fef3c7',
    'ACCEPTED': '#dbeafe',
    'IN_PROGRESS': '#dbeafe',
    'DELIVERED': '#e9d5ff',
    'COMPLETED': '#d1fae5',
    'CANCELLED': '#fee2e2',
    'REJECTED': '#fee2e2'
  };
  return colors[normalizedStatus] || colors[status] || '#f3f4f6';
};

// 优先级颜色映射 (暂时保留，可能在未来使用)
// const getPriorityColor = (priority) => {
//   const colors = {
//     high: '#ef4444',
//     medium: '#f59e0b',
//     low: '#10b981'
//   };
//   return colors[priority] || '#6b7280';
// };

// 状态标签转换函数
const getStatusLabel = (status) => {
  const normalizedStatus = status?.toLowerCase();
  const labels = {
    pending: '待处理',
    accepted: '已接受',
    in_progress: '进行中',
    delivered: '已交付',
    completed: '已完成',
    cancelled: '已取消',
    rejected: '已拒绝',
    // 添加大写版本的映射
    'PENDING': '待处理',
    'ACCEPTED': '已接受',
    'IN_PROGRESS': '进行中',
    'DELIVERED': '已交付',
    'COMPLETED': '已完成',
    'CANCELLED': '已取消',
    'REJECTED': '已拒绝'
  };
  return labels[normalizedStatus] || labels[status] || status;
};

function EnterpriseContentManagement() {
  // const [currentTab, setCurrentTab] = useState(0); // 暂时未使用

  const [searchQuery, setSearchQuery] = useState('');
  const [searchInput, setSearchInput] = useState(''); // 用于输入框的即时显示

  // 防抖搜索功能
  useEffect(() => {
    const timer = setTimeout(() => {
      setSearchQuery(searchInput);
    }, 300); // 300ms 防抖延迟

    return () => clearTimeout(timer);
  }, [searchInput]);
  const [filterType, setFilterType] = useState('all');
  const [contentCategory, setContentCategory] = useState('requirements'); // 'requirements' 或 'articles'

  const [sortBy, setSortBy] = useState('createDate');
  const [sortOrder, setSortOrder] = useState('desc');
  const [formErrors, setFormErrors] = useState({});
  const [openDetailDialog, setOpenDetailDialog] = useState(false);
  const [selectedRequirementDetail, setSelectedRequirementDetail] = useState(null);
  const [openProviderDialog, setOpenProviderDialog] = useState(false);
  const [selectedProviderInfo, setSelectedProviderInfo] = useState(null);
  const [openDeliveryDialog, setOpenDeliveryDialog] = useState(false);
  const [selectedDeliveryContent, setSelectedDeliveryContent] = useState(null);
  const [deliveryLoading, setDeliveryLoading] = useState(false);
  const [openReviewDialog, setOpenReviewDialog] = useState(false);
  const [reviewLoading, setReviewLoading] = useState(false);
  const [reviewFormData, setReviewFormData] = useState({
    review_status: 'approved',
    review_note: '',
    review_score: 5, // 默认评分为5分
    revision_requirements: '',
    revision_deadline: null,
    revision_required: false // 添加这个字段用于控制 Checkbox
  });
  const [uploadedFiles, setUploadedFiles] = useState([]);
  const [loading, setLoading] = useState(false);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'info' });
  const [submitLoading, setSubmitLoading] = useState(false);
  const [error, setError] = useState(null);
  const [requirements, setRequirements] = useState([]);
  const [channels, setChannels] = useState([]); // 渠道分类列表
  const [selectedChannelId, setSelectedChannelId] = useState(''); // 选中的渠道ID
  const [channelsLoading, setChannelsLoading] = useState(false); // 渠道分类加载状态
  
  // 渠道选择相关状态
  const [primaryCategory, setPrimaryCategory] = useState(''); // 一级分类
  const [secondaryCategory, setSecondaryCategory] = useState(''); // 二级分类
  const [accountTags, setAccountTags] = useState(''); // 三级分类（账号标签）
  const [channelSortBy, setChannelSortBy] = useState('recommended'); // 排序方式
  const [openChannelDialog, setOpenChannelDialog] = useState(false); // 渠道选择弹窗

  // 新需求表单数据
  const [formData, setFormData] = useState({
    title: '',
    type: 'submission',
    description: '', // 需求描述
    content: '', // 发布稿件的内容
    requirements: '', // 创建需求的要求
    attachments: [],
    selectedChannels: [], // 选择的渠道列表
    price: '' // 预算价格
  });

  // 加载需求列表
  const loadRequirements = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const params = {
        page: 1,
        size: 20,
      };

      // 添加筛选条件
      if (filterType !== 'all') {
        params.request_type = filterType;
      }
      if (searchQuery) {
        params.keyword = searchQuery;
      }

      const response = await contentService.getEnterpriseContentRequests(params);

      if (response.success && response.data) {
        const items = response.data.items || [];

        // 按group_id分组需求
        const groupedRequirements = groupRequirementsByGroupId(items);

        setRequirements(groupedRequirements);
      } else {
        const errorMsg = response.message || '获取内容需求列表失败';
        setError(errorMsg);
        setRequirements([]);
      }
    } catch (error) {
      const errorMsg = error.message || '加载需求列表失败';
      setError(errorMsg);
      setRequirements([]);
    } finally {
      setLoading(false);
    }
  }, [filterType, searchQuery]);

  // 加载渠道分类列表
  const loadChannels = useCallback(async () => {
    try {
      setChannelsLoading(true);
      const response = await contentService.getChannelCategories();

      if (response.success && response.data && response.data.categories) {
        // 转换数据格式，确保每个渠道都有必要的字段
        const channelData = response.data.categories.map(channel => ({
          id: channel.id,
          name: channel.category_name || '未命名渠道',
          description: channel.category_description || '暂无描述',
          followerCount: channel.provider_count || 0,
          category: channel.category_code || 'general',
          status: channel.is_active ? 'active' : 'inactive',
          minPrice: channel.min_price || 0,
          maxPrice: channel.max_price || 0,
          commissionRate: channel.commission_rate || 0,
          isFeatured: channel.is_featured || false,
          sortOrder: channel.sort_order || 0
        }));
        setChannels(channelData);
      } else {
        setChannels([]);
      }
    } catch (error) {
      setChannels([]);
    } finally {
      setChannelsLoading(false);
    }
  }, []);

  // 加载数据的useEffect
  useEffect(() => {
    loadRequirements();
    loadChannels();
  }, [loadRequirements, loadChannels]);

  // 监听筛选条件变化，重新加载数据
  useEffect(() => {
    if (requirements.length > 0) { // 只有在已经有数据的情况下才重新加载
      loadRequirements();
    }
  }, [filterType, searchQuery, loadRequirements, requirements.length]);

  // 筛选和排序逻辑 - 使用useMemo优化性能
  const filteredRequirements = useMemo(() => {
    return requirements
      .filter(req => {
        // 内容分类筛选
        if (contentCategory === 'requirements') {
          // 需求管理：只显示CREATE_CONTENT类型
          if (req.request_type !== 'CREATE_CONTENT') {
            return false;
          }
        } else if (contentCategory === 'articles') {
          // 稿件管理：只显示PUBLISH_CONTENT类型
          if (req.request_type !== 'PUBLISH_CONTENT') {
            return false;
          }
        }

        // 搜索筛选
        if (searchQuery) {
          const title = req.request_title || req.title || '';
          const tags = req.tags || [];
          const titleMatch = title.toLowerCase().includes(searchQuery.toLowerCase());
          const tagMatch = Array.isArray(tags) && tags.some(tag =>
            tag && tag.toLowerCase().includes(searchQuery.toLowerCase())
          );
          if (!titleMatch && !tagMatch) {
            return false;
          }
        }
        return true;
      })
      .sort((a, b) => {
        // 按创建时间倒序排列（最新的在前）
        const aTime = new Date(a.created_at || a.createDate);
        const bTime = new Date(b.created_at || b.createDate);
        return bTime - aTime;
      });
  }, [requirements, searchQuery, contentCategory]);








  // 文件上传处理
  const handleFileUpload = (event) => {
    const files = Array.from(event.target.files);
    const newFiles = files.map(file => ({
      id: Date.now() + Math.random(),
      name: file.name,
      size: file.size,
      type: file.type,
      file: file
    }));
    setUploadedFiles([...uploadedFiles, ...newFiles]);
  };

  const removeFile = (fileId) => {
    setUploadedFiles(uploadedFiles.filter(f => f.id !== fileId));
  };

  // 查看需求详情
  const handleViewDetail = (requirement) => {
    setSelectedRequirementDetail(requirement);
    setOpenDetailDialog(true);
  };

  // 查看渠道商详情
  const handleViewProviderDetail = (providerInfo, event) => {
    event.stopPropagation(); // 阻止事件冒泡
    setSelectedProviderInfo(providerInfo);
    setOpenProviderDialog(true);
  };

  // 查看交付内容
  const handleViewDelivery = async (requestId, event) => {
    event.stopPropagation();
    try {
      setDeliveryLoading(true);
      const response = await contentService.getDeliveredContent(requestId);
      if (response.success) {
        setSelectedDeliveryContent(response.data);
        setOpenDeliveryDialog(true);
      } else {
        setSnackbar({
          open: true,
          message: '获取交付内容失败',
          severity: 'error'
        });
      }
    } catch (error) {
      setSnackbar({
        open: true,
        message: error.message || '获取交付内容失败',
        severity: 'error'
      });
    } finally {
      setDeliveryLoading(false);
    }
  };

  // 打开审核对话框
  const handleOpenReview = (event) => {
    event.stopPropagation();
    setOpenReviewDialog(true);
  };

  // 提交内容审核
  const handleSubmitReview = async () => {
    if (!selectedDeliveryContent) return;

    // 验证表单数据
    if (!reviewFormData.review_note.trim()) {
      setSnackbar({
        open: true,
        message: '请填写审核意见',
        severity: 'error'
      });
      return;
    }

    if (reviewFormData.review_status === 'revision_required') {
      if (!reviewFormData.revision_requirements.trim()) {
        setSnackbar({
          open: true,
          message: '选择需要修改时，请填写修改要求',
          severity: 'error'
        });
        return;
      }
      if (!reviewFormData.revision_deadline) {
        setSnackbar({
          open: true,
          message: '选择需要修改时，请设置修改截止时间',
          severity: 'error'
        });
        return;
      }
    }

    try {
      setReviewLoading(true);

      // 准备提交数据，移除前端特有的字段
      const submitData = {
        review_status: reviewFormData.review_status,
        review_note: reviewFormData.review_note.trim(),
        review_score: reviewFormData.review_score,
        ...(reviewFormData.review_status === 'revision_required' && {
          revision_requirements: reviewFormData.revision_requirements.trim(),
          revision_deadline: reviewFormData.revision_deadline ? new Date(reviewFormData.revision_deadline).toISOString() : null
        })
      };

      const response = await contentService.reviewContent(selectedRequirementDetail.id, submitData);
      if (response.success) {
        setSnackbar({
          open: true,
          message: '内容审核提交成功',
          severity: 'success'
        });
        setOpenReviewDialog(false);
        setOpenDeliveryDialog(false);
        // 重置表单数据
        setReviewFormData({
          review_status: 'approved',
          review_note: '',
          review_score: 5,
          revision_requirements: '',
          revision_deadline: null,
          revision_required: false
        });
        // 重新加载数据
        await loadRequirements();
      } else {
        setSnackbar({
          open: true,
          message: '内容审核失败',
          severity: 'error'
        });
      }
    } catch (error) {
      setSnackbar({
        open: true,
        message: error.message || '内容审核失败',
        severity: 'error'
      });
    } finally {
      setReviewLoading(false);
    }
  };

  // 下载附件文件
  const handleDownloadFile = async (file) => {
    try {
      let fileId = '';
      let fileName = 'download';

      // 处理不同的文件数据结构，提取文件ID和文件名
      if (typeof file === 'string') {
        // 如果file是字符串，可能是文件名
        fileName = file;
        setSnackbar({
          open: true,
          message: '无法下载文件：缺少文件ID',
          severity: 'error'
        });
        return;
      } else if (file && typeof file === 'object') {
        // 如果file是对象，尝试获取文件ID和文件名
        fileId = file.id || file.file_id;
        fileName = file.name || file.filename || file.original_name || 'download';
      }

      // 检查是否有文件ID
      if (!fileId) {
        setSnackbar({
          open: true,
          message: '无法下载文件：文件ID不存在',
          severity: 'error'
        });
        return;
      }

      // 使用后端API下载，确保权限验证和预签名URL
      try {
        await uploadService.downloadFile(fileId, fileName);
        setSnackbar({
          open: true,
          message: '文件下载成功',
          severity: 'success'
        });
      } catch (error) {
        setSnackbar({
          open: true,
          message: '下载文件失败：' + error.message,
          severity: 'error'
        });
      }
    } catch (error) {
      setSnackbar({
        open: true,
        message: '下载文件失败：' + error.message,
        severity: 'error'
      });
    }
  };





  return (
    <Box sx={{ width: '100%', backgroundColor: '#fafafa', minHeight: '100vh' }}>
      {/* 顶部标题区域 - 扁平化设计 */}
      <Box sx={{
        backgroundColor: 'white',
        borderBottom: '1px solid #e0e0e0',
        px: 4,
        py: 3
      }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box>
            <Typography variant="h4" sx={{
              fontWeight: 500,
              color: '#1f2937',
              mb: 0.5
            }}>
              内容管理
            </Typography>
            <Typography variant="body2" sx={{
              color: '#6b7280'
            }}>
              浏览和管理企业发布的内容需求，接单赚取收益
            </Typography>
          </Box>

        </Box>

      </Box>



      {/* 主内容区域 */}
      <Box sx={{ backgroundColor: '#fafafa', px: 4, py: 3 }}>
        {/* 搜索框和筛选标签栏 - 扁平化设计 */}
        <Box sx={{
          display: 'flex',
          alignItems: 'center',
          gap: 4,
          mb: 3,
          pb: 2,
          borderBottom: '1px solid #e5e7eb'
        }}>
          {/* 搜索框 */}
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <TextField
              size="small"
              placeholder="搜索需求标题或标签..."
              value={searchInput}
              onChange={(e) => setSearchInput(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Search sx={{ color: '#6b7280', fontSize: 20 }} />
                  </InputAdornment>
                ),
              }}
              sx={{
                minWidth: 300,
                '& .MuiOutlinedInput-root': {
                  backgroundColor: '#fff',
                  borderRadius: 2,
                  '& fieldset': {
                    borderColor: '#e5e7eb',
                  },
                  '&:hover fieldset': {
                    borderColor: '#3b82f6',
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: '#3b82f6',
                    borderWidth: '2px'
                  }
                },
                '& .MuiInputAdornment-root': {
                  marginRight: 1
                }
              }}
            />
          </Box>

          {/* 内容分类标签 */}
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Typography variant="body2" sx={{ color: '#6b7280', fontWeight: 500 }}>
              内容分类:
            </Typography>
            <Box sx={{ display: 'flex', gap: 1 }}>
              {[
                { value: 'articles', label: '稿件管理', icon: '📄' },
                { value: 'requirements', label: '需求管理', icon: '📋' }
              ].map((category) => (
                <Button
                  key={category.value}
                  size="small"
                  variant={category.value === contentCategory ? 'contained' : 'outlined'}
                  onClick={() => setContentCategory(category.value)}
                  sx={{
                    borderRadius: 1,
                    fontSize: '0.8rem',
                    px: 3,
                    py: 1,
                    minWidth: 'auto',
                    ...(category.value === contentCategory ? {
                      backgroundColor: '#3b82f6',
                      color: 'white',
                      boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
                      '&:hover': {
                        backgroundColor: '#2563eb',
                        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
                      }
                    } : {
                      borderColor: '#e5e7eb',
                      color: '#6b7280',
                      '&:hover': {
                        borderColor: '#3b82f6',
                        backgroundColor: 'rgba(59, 130, 246, 0.04)',
                        color: '#3b82f6'
                      }
                    })
                  }}
                >
                  {category.icon} {category.label}
                </Button>
              ))}
            </Box>
          </Box>
        </Box>

        {/* 需求列表 - 扁平化设计 */}
        <Box sx={{
          display: 'grid',
          gridTemplateColumns: {
            xs: '1fr',
            sm: 'repeat(2, 1fr)',
            lg: 'repeat(3, 1fr)',
            xl: 'repeat(4, 1fr)'
          },
          gap: 2,
          width: '100%'
        }}>
          {loading ? (
            // 加载状态 - 不使用卡片样式
            <Box
              sx={{
                gridColumn: '1 / -1',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                height: '400px',
                backgroundColor: 'transparent'
              }}
            >
              <LinearProgress sx={{ width: '200px', mb: 2 }} />
              <Typography variant="body2" color="text.secondary">
                正在加载内容...
              </Typography>
            </Box>
          ) : error ? (
            // 错误状态 - 不使用卡片样式
            <Box sx={{
              gridColumn: '1 / -1',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              height: '300px',
              backgroundColor: 'transparent'
            }}>
              <Typography variant="h6" sx={{ color: '#ef4444', mb: 2 }}>
                {error}
              </Typography>
              <Button
                variant="outlined"
                onClick={loadRequirements}
                sx={{ color: '#3b82f6', borderColor: '#3b82f6' }}
              >
                重新加载
              </Button>
            </Box>
          ) : filteredRequirements.length === 0 ? (
            // 空状态 - 不使用卡片样式
            <Box sx={{
              gridColumn: '1 / -1',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              height: '300px',
              backgroundColor: 'transparent'
            }}>
              <Article sx={{ fontSize: 64, color: '#e5e7eb', mb: 2 }} />
              <Typography variant="h6" sx={{ color: '#9ca3af', mb: 1 }}>
                {searchQuery.trim()
                  ? '未找到匹配的需求'
                  : contentCategory === 'requirements'
                    ? '暂无需求管理内容'
                    : '暂无稿件管理内容'
                }
              </Typography>
              <Typography variant="body2" sx={{ color: '#d1d5db' }}>
                {searchQuery.trim()
                  ? '尝试调整搜索关键词或清空搜索条件'
                  : contentCategory === 'requirements'
                    ? '点击"创建需求"创建您的第一个内容创作需求'
                    : '点击"发布稿件"发布您的第一个稿件'
                }
              </Typography>
            </Box>
          ) : (
            // 需求卡片列表
            filteredRequirements.map((req) => (
              <Box key={req.id} sx={{
                backgroundColor: '#fff',
                border: '1px solid #e5e7eb',
                borderRadius: 1,
                p: 2,
                height: '200px',
                display: 'flex',
                flexDirection: 'column',
                transition: 'all 0.2s ease',
                overflow: 'hidden',
                cursor: 'pointer',
                boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
                '&:hover': {
                  borderColor: '#3b82f6',
                  backgroundColor: '#f9fafb',
                  transform: 'translateY(-2px)',
                  boxShadow: '0 4px 12px -2px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
                }
              }}
              onClick={() => handleViewDetail(req)}
              >
                {/* 标题行 */}
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1.5 }}>
                  <Typography variant="subtitle1" sx={{
                    fontWeight: 600,
                    color: '#1f2937',
                    flex: 1,
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                    mr: 1,
                    fontSize: '1rem'
                  }}>
                    {req.request_title || '未命名需求'}
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                    {/* 操作图标按钮 */}
                    <IconButton
                      size="small"
                      sx={{
                        color: '#10b981',
                        padding: '2px',
                        '&:hover': {
                          backgroundColor: '#ecfdf5',
                          color: '#059669'
                        }
                      }}
                      onClick={(e) => {
                        e.stopPropagation();
                        // TODO: 添加编辑功能
                      }}
                    >
                      <Edit sx={{ fontSize: 14 }} />
                    </IconButton>
                    <IconButton
                      size="small"
                      sx={{
                        color: '#ef4444',
                        padding: '2px',
                        '&:hover': {
                          backgroundColor: '#fef2f2',
                          color: '#dc2626'
                        }
                      }}
                      onClick={(e) => {
                        e.stopPropagation();
                        // TODO: 添加删除确认对话框
                        if (window.confirm('确定要删除这个需求吗？')) {
                          // 删除逻辑待实现
                        }
                      }}
                    >
                      <Delete sx={{ fontSize: 14 }} />
                    </IconButton>
                  </Box>
                </Box>

                {/* 类型标签 */}
                <Box sx={{ mb: 1.5 }}>
                  <Chip
                    label={req.request_type === 'CREATE_CONTENT' ? '创作需求' : '投稿需求'}
                    size="small"
                    sx={{
                      fontSize: '0.7rem',
                      height: '20px',
                      backgroundColor: req.request_type === 'CREATE_CONTENT' ? '#fef3c7' : '#dbeafe',
                      color: req.request_type === 'CREATE_CONTENT' ? '#92400e' : '#1e40af',
                      fontWeight: 500
                    }}
                  />
                </Box>

                {/* 标签区域 */}
                <Box sx={{ mb: 2, minHeight: '24px', display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
                  {(Array.isArray(req.tags) ? req.tags.slice(0, 3) : []).filter(Boolean).map((tag, i) => (
                    <Chip
                      key={i}
                      label={tag}
                      size="small"
                      sx={{
                        backgroundColor: '#f3f4f6',
                        color: '#6b7280',
                        fontSize: '0.65rem',
                        height: '16px',
                        '& .MuiChip-label': {
                          px: 0.5
                        }
                      }}
                    />
                  ))}
                  {Array.isArray(req.tags) && req.tags.length > 3 && (
                    <Typography variant="caption" sx={{ color: '#9ca3af', alignSelf: 'center' }}>
                      +{req.tags.length - 3}
                    </Typography>
                  )}
                </Box>

                {/* 底部信息 */}
                <Box sx={{ mt: 'auto', pt: 1, borderTop: '1px solid #f3f4f6' }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Typography variant="body2" sx={{
                      color: parseFloat(req.fixed_price || 0) > 0 ? '#059669' : '#6b7280',
                      fontWeight: parseFloat(req.fixed_price || 0) > 0 ? 600 : 400,
                      fontSize: '0.875rem'
                    }}>
                      {parseFloat(req.fixed_price || 0) > 0 ? `¥${parseFloat(req.fixed_price).toFixed(2)}` : '价格面议'}
                    </Typography>
                    <Typography variant="caption" sx={{ color: '#9ca3af', fontSize: '0.7rem' }}>
                      截止: {req.deadline ? new Date(req.deadline).toLocaleDateString() : '待定'}
                    </Typography>
                  </Box>
                </Box>
              </Box>
            ))
          )}
        </Box>
      </Box>







      {/* 需求详情查看对话框 */}
      <Dialog
        open={openDetailDialog}
        onClose={() => setOpenDetailDialog(false)}
        maxWidth="md"
        fullWidth
        sx={{
          '& .MuiDialog-paper': {
            borderRadius: 2,
            boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
            overflow: 'hidden'
          }
        }}
      >
        <DialogTitle sx={{
          backgroundColor: '#fff',
          borderBottom: '1px solid #e5e7eb',
          pb: 2
        }}>
          <Box sx={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between' }}>
            <Box sx={{ flex: 1 }}>
              <Typography variant="h5" sx={{ fontWeight: 700, mb: 1, color: '#1f2937' }}>
                {selectedRequirementDetail?.request_title || selectedRequirementDetail?.title || '需求详情'}
              </Typography>
              <Typography variant="body2" sx={{ color: '#6b7280', mb: 2 }}>
                查看需求的完整信息
              </Typography>
              <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                <Chip
                  label={selectedRequirementDetail?.request_type === 'CREATE_CONTENT' ? '创作需求' : '投稿需求'}
                  size="small"
                  icon={selectedRequirementDetail?.request_type === 'CREATE_CONTENT' ? <Edit sx={{ fontSize: 16 }} /> : <Send sx={{ fontSize: 16 }} />}
                  variant="outlined"
                  sx={{
                    borderColor: '#e5e7eb',
                    color: '#6b7280',
                    fontWeight: 500
                  }}
                />
              </Box>
            </Box>
          </Box>
        </DialogTitle>
        <DialogContent sx={{ p: 0 }}>
          {selectedRequirementDetail && (
            <Box>
              {/* 快速信息卡片 */}
              <Box sx={{
                background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)',
                p: 3,
                borderBottom: '1px solid #e2e8f0'
              }}>
                <Grid container spacing={3}>
                  <Grid item xs={12} md={4}>
                    <Box sx={{ textAlign: 'center' }}>
                      <CalendarToday sx={{ fontSize: 32, color: '#3b82f6', mb: 1 }} />
                      <Typography variant="h6" sx={{ fontWeight: 600, color: '#1e293b', mb: 0.5 }}>
                        创建时间
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {selectedRequirementDetail.created_at ? new Date(selectedRequirementDetail.created_at).toLocaleDateString() : '未知'}
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <Box sx={{ textAlign: 'center' }}>
                      <TrendingUp sx={{ fontSize: 32, color: '#10b981', mb: 1 }} />
                      <Typography variant="h6" sx={{ fontWeight: 600, color: '#1e293b', mb: 0.5 }}>
                        价格
                      </Typography>
                      <Typography variant="body2" sx={{
                        color: parseFloat(selectedRequirementDetail.fixed_price || 0) > 0 ? '#059669' : '#6b7280',
                        fontWeight: parseFloat(selectedRequirementDetail.fixed_price || 0) > 0 ? 600 : 400
                      }}>
                        {parseFloat(selectedRequirementDetail.fixed_price || 0) > 0 ? `¥${parseFloat(selectedRequirementDetail.fixed_price).toFixed(2)}` : '价格面议'}
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <Box sx={{ textAlign: 'center' }}>
                      <CalendarToday sx={{ fontSize: 32, color: '#f59e0b', mb: 1 }} />
                      <Typography variant="h6" sx={{ fontWeight: 600, color: '#1e293b', mb: 0.5 }}>
                        截止时间
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {selectedRequirementDetail.deadline ? new Date(selectedRequirementDetail.deadline).toLocaleDateString() : '未设置'}
                      </Typography>
                    </Box>
                  </Grid>
                </Grid>
              </Box>

              {/* 详细内容区域 */}
              <Box sx={{ p: 3 }}>
                {/* 标签展示 */}
                {selectedRequirementDetail.tags && selectedRequirementDetail.tags.length > 0 && (
                  <Box sx={{ mb: 3 }}>
                    <Typography variant="h6" sx={{
                      fontWeight: 600,
                      color: '#1e293b',
                      mb: 2,
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1
                    }}>
                      <Box sx={{
                        width: 4,
                        height: 20,
                        backgroundColor: '#3b82f6',
                        borderRadius: 1
                      }} />
                      相关标签
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                      {selectedRequirementDetail.tags.map((tag, index) => (
                        <Chip
                          key={index}
                          label={tag}
                          size="medium"
                          sx={{
                            backgroundColor: '#f1f5f9',
                            color: '#475569',
                            fontWeight: 500,
                            border: '1px solid #e2e8f0',
                            '&:hover': {
                              backgroundColor: '#e2e8f0',
                              transform: 'translateY(-1px)',
                              boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
                            },
                            transition: 'all 0.2s ease'
                          }}
                        />
                      ))}
                    </Box>
                  </Box>
                )}

                {/* 需求描述 */}
                {selectedRequirementDetail.request_description && (
                  <Box sx={{ mb: 3 }}>
                    <Typography variant="h6" sx={{
                      fontWeight: 600,
                      color: '#1e293b',
                      mb: 2,
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1
                    }}>
                      <Box sx={{
                        width: 4,
                        height: 20,
                        backgroundColor: '#3b82f6',
                        borderRadius: 1
                      }} />
                      需求描述
                    </Typography>
                    <Paper sx={{
                      p: 3,
                      backgroundColor: '#f8fafc',
                      border: '1px solid #e2e8f0',
                      borderRadius: 2,
                      boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
                    }}>
                      <Typography variant="body1" sx={{
                        color: '#374151',
                        lineHeight: 1.7,
                        whiteSpace: 'pre-wrap',
                        fontSize: '0.95rem'
                      }}>
                        {selectedRequirementDetail.request_description}
                      </Typography>
                    </Paper>
                  </Box>
                )}

                {/* 稿件标题 - 仅稿件直发显示 */}
                {selectedRequirementDetail.provided_content_title && (
                  <Box sx={{ mb: 3 }}>
                    <Typography variant="h6" sx={{
                      fontWeight: 600,
                      color: '#1e293b',
                      mb: 2,
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1
                    }}>
                      <Box sx={{
                        width: 4,
                        height: 20,
                        backgroundColor: '#8b5cf6',
                        borderRadius: 1
                      }} />
                      稿件标题
                    </Typography>
                    <Paper sx={{
                      p: 3,
                      backgroundColor: '#faf5ff',
                      border: '1px solid #e9d5ff',
                      borderRadius: 2,
                      boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
                    }}>
                      <Typography variant="h6" sx={{
                        color: '#374151',
                        fontWeight: 600,
                        fontSize: '1.1rem'
                      }}>
                        {selectedRequirementDetail.provided_content_title}
                      </Typography>
                    </Paper>
                  </Box>
                )}

                {/* 稿件内容 - 仅稿件直发显示 */}
                {selectedRequirementDetail.provided_content_text && (
                  <Box sx={{ mb: 3 }}>
                    <Typography variant="h6" sx={{
                      fontWeight: 600,
                      color: '#1e293b',
                      mb: 2,
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1
                    }}>
                      <Box sx={{
                        width: 4,
                        height: 20,
                        backgroundColor: '#10b981',
                        borderRadius: 1
                      }} />
                      稿件内容
                    </Typography>
                    <Paper sx={{
                      p: 3,
                      backgroundColor: '#f0fdf4',
                      border: '1px solid #bbf7d0',
                      borderRadius: 2,
                      boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
                    }}>
                      <Typography variant="body1" sx={{
                        color: '#374151',
                        lineHeight: 1.7,
                        whiteSpace: 'pre-wrap',
                        fontSize: '0.95rem'
                      }}>
                        {selectedRequirementDetail.provided_content_text}
                      </Typography>
                    </Paper>
                  </Box>
                )}

                {/* 创作要求 - 仅创作需求显示 */}
                {selectedRequirementDetail.creation_requirements && (
                  <Box sx={{ mb: 3 }}>
                    <Typography variant="h6" sx={{
                      fontWeight: 600,
                      color: '#1e293b',
                      mb: 2,
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1
                    }}>
                      <Box sx={{
                        width: 4,
                        height: 20,
                        backgroundColor: '#f59e0b',
                        borderRadius: 1
                      }} />
                      创作要求
                    </Typography>
                    <Paper sx={{
                      p: 3,
                      backgroundColor: '#fffbeb',
                      border: '1px solid #fed7aa',
                      borderRadius: 2,
                      boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
                    }}>
                      <Typography variant="body1" sx={{
                        color: '#374151',
                        lineHeight: 1.7,
                        whiteSpace: 'pre-wrap',
                        fontSize: '0.95rem'
                      }}>
                        {selectedRequirementDetail.creation_requirements}
                      </Typography>
                    </Paper>
                  </Box>
                )}

                {/* 附件信息 */}
                {selectedRequirementDetail.provided_content_files && selectedRequirementDetail.provided_content_files.length > 0 && (
                  <Box sx={{ mb: 4 }}>
                    <Typography variant="h6" sx={{
                      fontWeight: 600,
                      color: '#1e293b',
                      mb: 2,
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1
                    }}>
                      <Box sx={{
                        width: 4,
                        height: 20,
                        backgroundColor: '#f59e0b',
                        borderRadius: 1
                      }} />
                      附件文件
                    </Typography>
                    <Grid container spacing={2}>
                      {selectedRequirementDetail.provided_content_files.map((file, index) => (
                        <Grid item xs={12} sm={6} key={index}>
                          <Paper sx={{
                            p: 2,
                            backgroundColor: '#fff',
                            borderRadius: 2,
                            border: '1px solid #e2e8f0',
                            display: 'flex',
                            alignItems: 'center',
                            gap: 2,
                            cursor: 'pointer',
                            transition: 'all 0.2s ease',
                            '&:hover': {
                              backgroundColor: '#f8fafc',
                              transform: 'translateY(-1px)',
                              boxShadow: '0 4px 6px rgba(0,0,0,0.1)',
                              borderColor: '#3b82f6'
                            }
                          }}
                          onClick={() => handleDownloadFile(file)}
                          >
                            <FilePresent sx={{ color: '#3b82f6', fontSize: 24 }} />
                            <Typography variant="body2" sx={{
                              flex: 1,
                              color: '#374151',
                              fontWeight: 500,
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              whiteSpace: 'nowrap'
                            }}>
                              {typeof file === 'string' ? file : file.name || file.filename || '未知文件'}
                            </Typography>
                            <IconButton
                              size="small"
                              sx={{
                                color: '#6b7280',
                                '&:hover': {
                                  color: '#3b82f6',
                                  backgroundColor: 'rgba(59, 130, 246, 0.1)'
                                }
                              }}
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDownloadFile(file);
                              }}
                            >
                              <Download fontSize="small" />
                            </IconButton>
                          </Paper>
                        </Grid>
                      ))}
                    </Grid>
                  </Box>
                )}

                {/* 渠道商状态信息 */}
                {selectedRequirementDetail.channels && selectedRequirementDetail.channels.length > 0 && (
                  <Box sx={{ mb: 3 }}>
                    <Typography variant="h6" sx={{
                      fontWeight: 600,
                      color: '#1e293b',
                      mb: 2,
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1
                    }}>
                      <Box sx={{
                        width: 4,
                        height: 20,
                        backgroundColor: '#8b5cf6',
                        borderRadius: 1
                      }} />
                      渠道商响应状态
                    </Typography>
                    <Grid container spacing={2}>
                      {selectedRequirementDetail.channels.map((channel, index) => (
                        <Grid item xs={12} sm={6} md={4} key={index}>
                          <Paper sx={{
                            p: 3,
                            backgroundColor: '#fff',
                            borderRadius: 2,
                            border: '1px solid #e2e8f0',
                            height: '100%',
                            transition: 'all 0.2s ease',
                            cursor: 'pointer',
                            '&:hover': {
                              backgroundColor: '#f8fafc',
                              transform: 'translateY(-1px)',
                              boxShadow: '0 4px 6px rgba(0,0,0,0.1)',
                              borderColor: '#3b82f6'
                            }
                          }}
                          onClick={(e) => handleViewProviderDetail(channel.provider_info, e)}
                          >
                            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                              <Typography variant="subtitle2" sx={{ fontWeight: 600, color: '#1e293b' }}>
                                {channel.provider_info?.provider_name || '未知渠道商'}
                              </Typography>
                              <Chip
                                label={getStatusLabel(channel.status)}
                                size="small"
                                sx={{
                                  backgroundColor: getStatusBgColor(channel.status),
                                  color: getStatusColor(channel.status),
                                  fontWeight: 600,
                                  fontSize: '0.7rem'
                                }}
                              />
                            </Box>
                            {channel.status === 'ACCEPTED' && (
                              <Box sx={{ mt: 1 }}>
                                <Typography variant="body2" color="text.secondary" sx={{ mb: 0.5 }}>
                                  接单时间：{channel.accepted_at ? new Date(channel.accepted_at).toLocaleDateString() : '未知'}
                                </Typography>
                                {channel.estimated_delivery_days && (
                                  <Typography variant="body2" color="text.secondary" sx={{ mb: 0.5 }}>
                                    预计交付：{channel.estimated_delivery_days}天
                                  </Typography>
                                )}
                                {channel.accept_message && (
                                  <Typography variant="body2" color="text.secondary" sx={{
                                    fontStyle: 'italic',
                                    overflow: 'hidden',
                                    textOverflow: 'ellipsis',
                                    whiteSpace: 'nowrap'
                                  }}>
                                    "{channel.accept_message}"
                                  </Typography>
                                )}
                              </Box>
                            )}
                            {channel.status === 'DELIVERED' && (
                              <Box sx={{ mt: 1 }}>
                                <Button
                                  variant="outlined"
                                  size="small"
                                  startIcon={<Visibility />}
                                  onClick={(e) => handleViewDelivery(selectedRequirementDetail.id, e)}
                                  disabled={deliveryLoading}
                                  sx={{
                                    borderColor: '#8b5cf6',
                                    color: '#8b5cf6',
                                    fontSize: '0.75rem',
                                    py: 0.5,
                                    px: 1.5,
                                    '&:hover': {
                                      backgroundColor: 'rgba(139, 92, 246, 0.1)',
                                      borderColor: '#7c3aed'
                                    }
                                  }}
                                >
                                  查看交付内容
                                </Button>
                              </Box>
                            )}
                            <Box sx={{ mt: 2, pt: 2, borderTop: '1px solid #f1f5f9' }}>
                              <Typography variant="caption" sx={{
                                color: '#94a3b8',
                                fontSize: '0.7rem',
                                display: 'flex',
                                alignItems: 'center',
                                gap: 0.5
                              }}>
                                💡 点击查看渠道商详情
                              </Typography>
                            </Box>
                          </Paper>
                        </Grid>
                      ))}
                    </Grid>
                  </Box>
                )}
              </Box>
            </Box>
          )}
        </DialogContent>

        <DialogActions sx={{
          borderTop: '1px solid #e5e7eb',
          px: 3,
          py: 2,
          justifyContent: 'flex-end',
          gap: 2
        }}>
          <Button
            variant="outlined"
            startIcon={<Edit />}
            sx={{
              borderColor: '#3b82f6',
              color: '#3b82f6',
              fontWeight: 500,
              px: 3,
              py: 1,
              borderRadius: 1,
              '&:hover': {
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                borderColor: '#2563eb'
              }
            }}
          >
            编辑需求
          </Button>
          <Button
            onClick={() => setOpenDetailDialog(false)}
            variant="contained"
            sx={{
              backgroundColor: '#6b7280',
              color: 'white',
              fontWeight: 500,
              px: 3,
              py: 1,
              borderRadius: 1,
              '&:hover': {
                backgroundColor: '#4b5563'
              }
            }}
          >
            关闭
          </Button>
        </DialogActions>
      </Dialog>

      {/* 渠道商详情对话框 */}
      <Dialog
        open={openProviderDialog}
        onClose={() => setOpenProviderDialog(false)}
        maxWidth="sm"
        fullWidth
        sx={{
          '& .MuiDialog-paper': {
            borderRadius: 2,
            boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
            overflow: 'hidden'
          }
        }}
      >
        <DialogTitle sx={{
          backgroundColor: '#f0f9ff',
          borderBottom: '1px solid #bae6fd',
          pb: 2
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Box sx={{
              width: 40,
              height: 40,
              borderRadius: '50%',
              backgroundColor: '#06b6d4',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'white',
              fontWeight: 600,
              fontSize: '1.2rem'
            }}>
              {selectedProviderInfo?.provider_name?.charAt(0) || '渠'}
            </Box>
            <Box>
              <Typography variant="h6" sx={{ fontWeight: 700, color: '#0f172a', mb: 0.5 }}>
                {selectedProviderInfo?.provider_name || '渠道商详情'}
              </Typography>
              <Typography variant="body2" sx={{ color: '#64748b' }}>
                查看渠道商的详细信息和服务能力
              </Typography>
            </Box>
          </Box>
        </DialogTitle>
        <DialogContent sx={{ p: 3 }}>
          {selectedProviderInfo && (
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle1" sx={{ color: '#0f172a', fontWeight: 600, mb: 2 }}>
                    基本信息
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <Typography variant="body2" sx={{ color: '#64748b', mb: 0.5 }}>
                        渠道商类型
                      </Typography>
                      <Typography variant="body1" sx={{ color: '#0f172a', fontWeight: 500 }}>
                        {selectedProviderInfo.provider_type === 'individual' ? '个人' :
                         selectedProviderInfo.provider_type === 'company' ? '企业' : '未知'}
                      </Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" sx={{ color: '#64748b', mb: 0.5 }}>
                        验证状态
                      </Typography>
                      <Chip
                        label={selectedProviderInfo.verification_status === 'verified' ? '已验证' : '未验证'}
                        size="small"
                        color={selectedProviderInfo.verification_status === 'verified' ? 'success' : 'default'}
                        sx={{ fontWeight: 500 }}
                      />
                    </Grid>
                  </Grid>
                </Box>
              </Grid>

              <Grid item xs={12}>
                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle1" sx={{ color: '#0f172a', fontWeight: 600, mb: 2 }}>
                    联系方式
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={6}>
                      <Typography variant="body2" sx={{ color: '#64748b', mb: 0.5 }}>
                        联系电话
                      </Typography>
                      <Typography variant="body1" sx={{ color: '#0f172a' }}>
                        {selectedProviderInfo.contact_phone || '未提供'}
                      </Typography>
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <Typography variant="body2" sx={{ color: '#64748b', mb: 0.5 }}>
                        联系邮箱
                      </Typography>
                      <Typography variant="body1" sx={{ color: '#0f172a' }}>
                        {selectedProviderInfo.contact_email || '未提供'}
                      </Typography>
                    </Grid>
                    <Grid item xs={12}>
                      <Typography variant="body2" sx={{ color: '#64748b', mb: 0.5 }}>
                        联系地址
                      </Typography>
                      <Typography variant="body1" sx={{ color: '#0f172a' }}>
                        {selectedProviderInfo.contact_address || '未提供'}
                      </Typography>
                    </Grid>
                  </Grid>
                </Box>
              </Grid>

              <Grid item xs={12}>
                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle1" sx={{ color: '#0f172a', fontWeight: 600, mb: 2 }}>
                    服务能力
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <Typography variant="body2" sx={{ color: '#64748b', mb: 0.5 }}>
                        服务评分
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="h6" sx={{ color: '#f59e0b', fontWeight: 600 }}>
                          {parseFloat(selectedProviderInfo.service_rating || 0).toFixed(1)}
                        </Typography>
                        <Typography variant="body2" sx={{ color: '#64748b' }}>
                          / 5.0
                        </Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" sx={{ color: '#64748b', mb: 0.5 }}>
                        完成订单
                      </Typography>
                      <Typography variant="h6" sx={{ color: '#10b981', fontWeight: 600 }}>
                        {selectedProviderInfo.completed_orders || 0} 单
                      </Typography>
                    </Grid>
                  </Grid>
                </Box>
              </Grid>

              {selectedProviderInfo.business_description && (
                <Grid item xs={12}>
                  <Box>
                    <Typography variant="subtitle1" sx={{ color: '#0f172a', fontWeight: 600, mb: 2 }}>
                      业务描述
                    </Typography>
                    <Paper sx={{
                      p: 2,
                      backgroundColor: '#f8fafc',
                      border: '1px solid #e2e8f0',
                      borderRadius: 1
                    }}>
                      <Typography variant="body2" sx={{ color: '#475569', lineHeight: 1.6 }}>
                        {selectedProviderInfo.business_description}
                      </Typography>
                    </Paper>
                  </Box>
                </Grid>
              )}
            </Grid>
          )}
        </DialogContent>
        <DialogActions sx={{
          borderTop: '1px solid #e2e8f0',
          px: 3,
          py: 2,
          justifyContent: 'flex-end'
        }}>
          <Button
            onClick={() => setOpenProviderDialog(false)}
            variant="contained"
            sx={{
              backgroundColor: '#06b6d4',
              color: 'white',
              fontWeight: 500,
              px: 3,
              py: 1,
              borderRadius: 1,
              '&:hover': {
                backgroundColor: '#0891b2'
              }
            }}
          >
            关闭
          </Button>
        </DialogActions>
      </Dialog>

      {/* 查看交付内容对话框 */}
      <Dialog
        open={openDeliveryDialog}
        onClose={() => setOpenDeliveryDialog(false)}
        maxWidth="md"
        fullWidth
        sx={{
          '& .MuiDialog-paper': {
            borderRadius: 2,
            boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
            overflow: 'hidden'
          }
        }}
      >
        <DialogTitle sx={{
          backgroundColor: '#f0f9ff',
          borderBottom: '1px solid #e5e7eb',
          color: '#1e293b',
          fontWeight: 600,
          fontSize: '1.1rem',
          py: 2
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <CheckCircle sx={{ color: '#8b5cf6' }} />
            交付内容详情
          </Box>
        </DialogTitle>

        <DialogContent sx={{ p: 0 }}>
          {selectedDeliveryContent && (
            <Box sx={{ p: 3 }}>
              {/* 内容标题 */}
              <Box sx={{ mb: 3 }}>
                <Typography variant="h6" sx={{ fontWeight: 600, color: '#1e293b', mb: 1 }}>
                  {selectedDeliveryContent.content_title}
                </Typography>
                <Chip
                  label="已交付"
                  size="small"
                  sx={{
                    backgroundColor: '#e9d5ff',
                    color: '#8b5cf6',
                    fontWeight: 600
                  }}
                />
              </Box>

              {/* 内容正文 */}
              <Box sx={{ mb: 3 }}>
                <Typography variant="subtitle2" sx={{ fontWeight: 600, color: '#374151', mb: 1 }}>
                  内容正文
                </Typography>
                <Paper sx={{ p: 2, backgroundColor: '#f9fafb', border: '1px solid #e5e7eb' }}>
                  <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap', lineHeight: 1.6 }}>
                    {selectedDeliveryContent.content_text}
                  </Typography>
                </Paper>
              </Box>

              {/* 内容摘要 */}
              {selectedDeliveryContent.content_summary && (
                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle2" sx={{ fontWeight: 600, color: '#374151', mb: 1 }}>
                    内容摘要
                  </Typography>
                  <Paper sx={{ p: 2, backgroundColor: '#f9fafb', border: '1px solid #e5e7eb' }}>
                    <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap', lineHeight: 1.6 }}>
                      {selectedDeliveryContent.content_summary}
                    </Typography>
                  </Paper>
                </Box>
              )}

              {/* 附件列表 */}
              {selectedDeliveryContent.content_attachments && selectedDeliveryContent.content_attachments.length > 0 && (
                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle2" sx={{ fontWeight: 600, color: '#374151', mb: 1 }}>
                    附件文件
                  </Typography>
                  <Grid container spacing={2}>
                    {selectedDeliveryContent.content_attachments.map((file, index) => (
                      <Grid item xs={12} sm={6} key={index}>
                        <Paper sx={{
                          p: 2,
                          border: '1px solid #e5e7eb',
                          borderRadius: 1,
                          display: 'flex',
                          alignItems: 'center',
                          gap: 2,
                          '&:hover': {
                            backgroundColor: '#f9fafb'
                          }
                        }}>
                          <FilePresent sx={{ color: '#6b7280' }} />
                          <Box sx={{ flex: 1, minWidth: 0 }}>
                            <Typography variant="body2" sx={{ fontWeight: 500, color: '#1e293b' }}>
                              {file.filename}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {file.file_size ? `${(file.file_size / 1024 / 1024).toFixed(2)} MB` : '未知大小'}
                            </Typography>
                          </Box>
                          <Button
                            size="small"
                            startIcon={<Download />}
                            onClick={() => handleDownloadFile(file)}
                            sx={{ minWidth: 'auto' }}
                          >
                            下载
                          </Button>
                        </Paper>
                      </Grid>
                    ))}
                  </Grid>
                </Box>
              )}

              {/* 交付说明 */}
              {selectedDeliveryContent.delivery_note && (
                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle2" sx={{ fontWeight: 600, color: '#374151', mb: 1 }}>
                    交付说明
                  </Typography>
                  <Paper sx={{ p: 2, backgroundColor: '#f0f9ff', border: '1px solid #bfdbfe' }}>
                    <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap', lineHeight: 1.6, color: '#1e40af' }}>
                      {selectedDeliveryContent.delivery_note}
                    </Typography>
                  </Paper>
                </Box>
              )}

              {/* 交付时间 */}
              <Box sx={{ pt: 2, borderTop: '1px solid #e5e7eb' }}>
                <Typography variant="caption" color="text.secondary">
                  交付时间：{selectedDeliveryContent.delivered_at ? new Date(selectedDeliveryContent.delivered_at).toLocaleString() : '未知'}
                </Typography>
              </Box>
            </Box>
          )}
        </DialogContent>

        <DialogActions sx={{
          borderTop: '1px solid #e5e7eb',
          px: 3,
          py: 2,
          justifyContent: 'space-between'
        }}>
          <Button
            variant="outlined"
            startIcon={<RateReview />}
            onClick={handleOpenReview}
            sx={{
              borderColor: '#10b981',
              color: '#10b981',
              fontWeight: 500,
              '&:hover': {
                backgroundColor: 'rgba(16, 185, 129, 0.1)',
                borderColor: '#059669'
              }
            }}
          >
            内容审核
          </Button>
          <Button
            onClick={() => setOpenDeliveryDialog(false)}
            variant="contained"
            sx={{
              backgroundColor: '#6b7280',
              color: 'white',
              fontWeight: 500,
              '&:hover': {
                backgroundColor: '#4b5563'
              }
            }}
          >
            关闭
          </Button>
        </DialogActions>
      </Dialog>

      {/* 内容审核对话框 */}
      <Dialog
        open={openReviewDialog}
        onClose={() => setOpenReviewDialog(false)}
        maxWidth="sm"
        fullWidth
        sx={{
          '& .MuiDialog-paper': {
            borderRadius: 2,
            boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
            overflow: 'hidden'
          }
        }}
      >
        <DialogTitle sx={{
          backgroundColor: '#f0fdf4',
          borderBottom: '1px solid #e5e7eb',
          color: '#1e293b',
          fontWeight: 600,
          fontSize: '1.1rem',
          py: 2
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <RateReview sx={{ color: '#10b981' }} />
            内容审核
          </Box>
        </DialogTitle>

        <DialogContent sx={{ p: 3 }}>
          <Box sx={{ mb: 3 }}>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              请对渠道商交付的内容进行审核，选择审核结果并填写审核意见。
            </Typography>

            {/* 审核状态选择 */}
            <FormControl fullWidth sx={{ mb: 3 }}>
              <InputLabel>审核结果</InputLabel>
              <Select
                value={reviewFormData.review_status}
                label="审核结果"
                onChange={(e) => setReviewFormData(prev => ({ ...prev, review_status: e.target.value }))}
              >
                <MenuItem value="approved">
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <CheckCircle sx={{ color: '#10b981', fontSize: '1rem' }} />
                    通过审核
                  </Box>
                </MenuItem>
                <MenuItem value="rejected">
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Cancel sx={{ color: '#ef4444', fontSize: '1rem' }} />
                    拒绝审核
                  </Box>
                </MenuItem>
                <MenuItem value="revision_required">
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Edit sx={{ color: '#f59e0b', fontSize: '1rem' }} />
                    需要修改
                  </Box>
                </MenuItem>
              </Select>
            </FormControl>

            {/* 审核意见 */}
            <TextField
              fullWidth
              multiline
              rows={4}
              label="审核意见"
              value={reviewFormData.review_note}
              onChange={(e) => setReviewFormData(prev => ({ ...prev, review_note: e.target.value }))}
              placeholder="请输入审核意见，如修改建议、问题说明等..."
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: 1.5,
                }
              }}
            />

            {/* 修改要求和截止时间 */}
            {reviewFormData.review_status === 'revision_required' && (
              <Box sx={{ mt: 3 }}>
                <TextField
                  fullWidth
                  multiline
                  rows={3}
                  label="修改要求"
                  value={reviewFormData.revision_requirements}
                  onChange={(e) => setReviewFormData(prev => ({ ...prev, revision_requirements: e.target.value }))}
                  placeholder="请详细说明需要修改的内容和要求..."
                  required
                  sx={{
                    mb: 2,
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 1.5,
                    }
                  }}
                />
                <TextField
                  fullWidth
                  type="datetime-local"
                  label="修改截止时间"
                  value={reviewFormData.revision_deadline || ''}
                  onChange={(e) => setReviewFormData(prev => ({ ...prev, revision_deadline: e.target.value }))}
                  required
                  InputLabelProps={{
                    shrink: true,
                  }}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 1.5,
                    }
                  }}
                />
              </Box>
            )}
          </Box>
        </DialogContent>

        <DialogActions sx={{
          borderTop: '1px solid #e5e7eb',
          px: 3,
          py: 2,
          justifyContent: 'flex-end',
          gap: 2
        }}>
          <Button
            onClick={() => setOpenReviewDialog(false)}
            variant="outlined"
            sx={{
              borderColor: '#6b7280',
              color: '#6b7280',
              fontWeight: 500,
              '&:hover': {
                backgroundColor: 'rgba(107, 114, 128, 0.1)',
                borderColor: '#4b5563'
              }
            }}
          >
            取消
          </Button>
          <Button
            onClick={handleSubmitReview}
            variant="contained"
            disabled={reviewLoading || !reviewFormData.review_note.trim()}
            sx={{
              backgroundColor: '#10b981',
              color: 'white',
              fontWeight: 500,
              '&:hover': {
                backgroundColor: '#059669'
              },
              '&:disabled': {
                backgroundColor: '#d1d5db'
              }
            }}
          >
            {reviewLoading ? '提交中...' : '提交审核'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar 通知 */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
}

export default EnterpriseContentManagement;
