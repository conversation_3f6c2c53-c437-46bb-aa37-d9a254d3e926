import React, { useState } from 'react';
import {
  Box,
  Typography,
  Grid,
  Button,
  TextField,
  FormControl,
  Select,
  MenuItem,
  Tabs,
  Tab,
  Paper,
  Chip,
  IconButton,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Avatar,
  Divider,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from '@mui/material';
import {
  MonetizationOn,
  AccountBalance,
  Timeline,
  Add,
  Edit,
  Delete,
  CheckCircle,
  Pending,
  Cancel,
  AttachMoney,
  CalendarToday,
  Receipt,
  AccountBalanceWallet,
  History,
  Info,
  ChevronLeft,
  ChevronRight,
  Divider as DividerIcon,
} from '@mui/icons-material';

function WithdrawManagement() {
  const [currentTab, setCurrentTab] = useState(0);
  const [withdrawAmount, setWithdrawAmount] = useState('');
  const [withdrawMethod, setWithdrawMethod] = useState('');
  const [withdrawRemark, setWithdrawRemark] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 7;

  // 模拟数据
  const withdrawData = {
    availableAmount: 12345.67,
    processingAmount: 2000.00,
    totalWithdrawn: 45000,
    monthlyWithdrawn: 8500,
  };


  const withdrawHistory = [
    { 
      id: 1, 
      amount: 5000, 
      date: '2024-01-15 14:30', 
      status: 'completed', 
      method: '银行卡',
      bankInfo: '工商银行(1234)',
      fee: 100,
      actualAmount: 4900
    },
    { 
      id: 2, 
      amount: 3000, 
      date: '2024-01-10 09:15', 
      status: 'processing', 
      method: '支付宝',
      bankInfo: '支付宝账户',
      fee: 60,
      actualAmount: 2940
    },
    { 
      id: 3, 
      amount: 2500, 
      date: '2024-01-05 16:45', 
      status: 'completed', 
      method: '银行卡',
      bankInfo: '建设银行(5678)',
      fee: 50,
      actualAmount: 2450
    },
    { 
      id: 4, 
      amount: 1000, 
      date: '2024-01-02 11:20', 
      status: 'rejected', 
      method: '银行卡',
      bankInfo: '工商银行(1234)',
      fee: 20,
      actualAmount: 980
    },
    { 
      id: 5, 
      amount: 1500, 
      date: '2023-12-28 10:30', 
      status: 'completed', 
      method: '支付宝',
      bankInfo: '支付宝账户',
      fee: 30,
      actualAmount: 1470
    },
    { 
      id: 6, 
      amount: 8000, 
      date: '2023-12-25 15:20', 
      status: 'completed', 
      method: '银行卡',
      bankInfo: '工商银行(1234)',
      fee: 160,
      actualAmount: 7840
    },
    { 
      id: 7, 
      amount: 3500, 
      date: '2023-12-20 09:45', 
      status: 'completed', 
      method: '支付宝',
      bankInfo: '支付宝账户',
      fee: 70,
      actualAmount: 3430
    },
    { 
      id: 8, 
      amount: 2000, 
      date: '2023-12-15 16:30', 
      status: 'completed', 
      method: '银行卡',
      bankInfo: '建设银行(5678)',
      fee: 40,
      actualAmount: 1960
    },
    { 
      id: 9, 
      amount: 4500, 
      date: '2023-12-10 11:15', 
      status: 'completed', 
      method: '银行卡',
      bankInfo: '工商银行(1234)',
      fee: 90,
      actualAmount: 4410
    },
    { 
      id: 10, 
      amount: 6000, 
      date: '2023-12-05 13:50', 
      status: 'completed', 
      method: '支付宝',
      bankInfo: '支付宝账户',
      fee: 120,
      actualAmount: 5880
    },
  ];

  const handleWithdrawSubmit = () => {
    console.log('提现申请提交');
  };

  const handleWithdrawReset = () => {
    setWithdrawAmount('');
    setWithdrawMethod('');
    setWithdrawRemark('');
  };

  const getStatusChip = (status) => {
    switch(status) {
      case 'completed':
        return <Chip label="已完成" size="small" sx={{ backgroundColor: '#dcfce7', color: '#166534', height: 24, fontSize: '0.8rem' }} />;
      case 'processing':
        return <Chip label="处理中" size="small" sx={{ backgroundColor: '#fef3c7', color: '#92400e', height: 24, fontSize: '0.8rem' }} />;
      case 'rejected':
        return <Chip label="已拒绝" size="small" sx={{ backgroundColor: '#fee2e2', color: '#991b1b', height: 24, fontSize: '0.8rem' }} />;
      default:
        return null;
    }
  };

  return (
    <Box sx={{ width: '100%', height: '100vh', backgroundColor: 'white', display: 'flex', flexDirection: 'column' }}>
      {/* 顶部标题区域 */}
      <Box sx={{
        borderBottom: '1px solid #f0f0f0',
        backgroundColor: 'white',
        px: 4,
        py: 3
      }}>
        <Typography variant="h4" sx={{ 
          fontWeight: 700, 
          color: '#1a1a1a', 
          mb: 0.5,
          fontSize: '2rem'
        }}>
          提现管理
        </Typography>
        <Typography variant="body1" sx={{ 
          color: '#6b7280',
          fontSize: '1rem' 
        }}>
          管理您的提现申请和银行卡信息
        </Typography>
      </Box>

      {/* 统计卡片区域 */}
      <Box sx={{
        borderBottom: '1px solid #f0f0f0',
        backgroundColor: '#fafafa',
        px: 4,
        py: 2
      }}>
        <Grid container spacing={3}>
          <Grid item xs={6} md={3}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Box sx={{
                width: 40,
                height: 40,
                borderRadius: '10px',
                backgroundColor: '#f0fdf4',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}>
                <AttachMoney sx={{ color: '#10b981', fontSize: 20 }} />
              </Box>
              <Box>
                <Typography variant="caption" sx={{ 
                  color: '#9ca3af', 
                  fontSize: '0.75rem',
                  fontWeight: 500,
                  textTransform: 'uppercase',
                  letterSpacing: '0.025em'
                }}>
                  可提现余额
                </Typography>
                <Typography variant="h6" sx={{ 
                  color: '#1a1a1a',
                  fontWeight: 600,
                  fontSize: '1.25rem'
                }}>
                  ¥{withdrawData.availableAmount.toLocaleString()}
                </Typography>
              </Box>
            </Box>
          </Grid>

          <Grid item xs={6} md={3}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Box sx={{
                width: 40,
                height: 40,
                borderRadius: '10px',
                backgroundColor: '#fef3c7',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}>
                <Timeline sx={{ color: '#f59e0b', fontSize: 20 }} />
              </Box>
              <Box>
                <Typography variant="caption" sx={{ 
                  color: '#9ca3af', 
                  fontSize: '0.75rem',
                  fontWeight: 500,
                  textTransform: 'uppercase',
                  letterSpacing: '0.025em'
                }}>
                  处理中金额
                </Typography>
                <Typography variant="h6" sx={{ 
                  color: '#1a1a1a',
                  fontWeight: 600,
                  fontSize: '1.25rem'
                }}>
                  ¥{withdrawData.processingAmount.toLocaleString()}
                </Typography>
              </Box>
            </Box>
          </Grid>

          <Grid item xs={6} md={3}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Box sx={{
                width: 40,
                height: 40,
                borderRadius: '10px',
                backgroundColor: '#dbeafe',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}>
                <History sx={{ color: '#3b82f6', fontSize: 20 }} />
              </Box>
              <Box>
                <Typography variant="caption" sx={{ 
                  color: '#9ca3af', 
                  fontSize: '0.75rem',
                  fontWeight: 500,
                  textTransform: 'uppercase',
                  letterSpacing: '0.025em'
                }}>
                  累计提现
                </Typography>
                <Typography variant="h6" sx={{ 
                  color: '#1a1a1a',
                  fontWeight: 600,
                  fontSize: '1.25rem'
                }}>
                  ¥{withdrawData.totalWithdrawn.toLocaleString()}
                </Typography>
              </Box>
            </Box>
          </Grid>

          <Grid item xs={6} md={3}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Box sx={{
                width: 40,
                height: 40,
                borderRadius: '10px',
                backgroundColor: '#dcfce7',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}>
                <CalendarToday sx={{ color: '#16a34a', fontSize: 20 }} />
              </Box>
              <Box>
                <Typography variant="caption" sx={{ 
                  color: '#9ca3af', 
                  fontSize: '0.75rem',
                  fontWeight: 500,
                  textTransform: 'uppercase',
                  letterSpacing: '0.025em'
                }}>
                  本月提现
                </Typography>
                <Typography variant="h6" sx={{ 
                  color: '#1a1a1a',
                  fontWeight: 600,
                  fontSize: '1.25rem'
                }}>
                  ¥{withdrawData.monthlyWithdrawn.toLocaleString()}
                </Typography>
              </Box>
            </Box>
          </Grid>
        </Grid>
      </Box>

      {/* 内容区域 */}
      <Box sx={{ flex: 1, px: 4, py: 3, overflow: 'hidden', display: 'flex', flexDirection: 'column', minHeight: 0 }}>
        {/* 标签页 */}
        <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
          <Tabs value={currentTab} onChange={(e, v) => setCurrentTab(v)} sx={{ minHeight: 42 }}>
            <Tab label="申请提现" sx={{ fontSize: '1rem', minHeight: 42, py: 1 }} />
            <Tab label="提现记录" sx={{ fontSize: '1rem', minHeight: 42, py: 1 }} />
          </Tabs>
        </Box>

        {/* 标签内容 */}
        <Box sx={{ flex: 1, overflow: 'auto', minHeight: 0 }}>
        {currentTab === 0 ? (
          /* 申请提现页面 */
          <Box sx={{ height: '100%' }}>
            <Grid container spacing={3} sx={{ height: '100%' }}>
              {/* 申请提现表单 */}
              <Grid item xs={12} md={7} sx={{ display: 'flex' }}>
                <Paper sx={{ 
                  border: '1px solid #e5e7eb',
                  borderRadius: 2,
                  p: 3.5,
                  flex: 1,
                  display: 'flex',
                  flexDirection: 'column'
                }}>
                  <Typography variant="h6" sx={{ fontWeight: 600, mb: 2.5, fontSize: '1.2rem' }}>
                    提现申请
                  </Typography>
                  
                  <Box sx={{ mb: 2.5 }}>
                    <Typography variant="body1" sx={{ mb: 0.75, fontWeight: 500, color: '#374151', fontSize: '0.9rem' }}>
                      提现金额
                    </Typography>
                    <TextField
                      fullWidth
                      size="small"
                      placeholder="请输入提现金额"
                      value={withdrawAmount}
                      onChange={(e) => setWithdrawAmount(e.target.value)}
                      InputProps={{
                        startAdornment: <Typography sx={{ mr: 1, color: '#9ca3af', fontSize: '0.95rem' }}>¥</Typography>,
                        sx: { fontSize: '0.95rem', height: 44 }
                      }}
                    />
                    <Box sx={{ display: 'flex', gap: 1, mt: 1 }}>
                      {[500, 1000, 2000].map((amount) => (
                        <Button
                          key={amount}
                          size="small"
                          variant="outlined"
                          onClick={() => setWithdrawAmount(amount.toString())}
                          sx={{ 
                            borderColor: '#e5e7eb',
                            color: '#6b7280',
                            fontSize: '0.8rem',
                            py: 0.5,
                            px: 1.5,
                            minWidth: 'auto',
                            '&:hover': {
                              borderColor: '#d1d5db',
                              backgroundColor: '#f9fafb'
                            }
                          }}
                        >
                          ¥{amount}
                        </Button>
                      ))}
                      <Button
                        size="small"
                        variant="outlined"
                        onClick={() => setWithdrawAmount(withdrawData.availableAmount.toString())}
                        sx={{ 
                          borderColor: '#e5e7eb',
                          color: '#6b7280',
                          fontSize: '0.7rem',
                          py: 0.25,
                          px: 1.5,
                          minWidth: 'auto',
                          '&:hover': {
                            borderColor: '#d1d5db',
                            backgroundColor: '#f9fafb'
                          }
                        }}
                      >
                        全部
                      </Button>
                    </Box>
                    <Typography variant="body2" sx={{ color: '#9ca3af', mt: 0.75, display: 'block', fontSize: '0.8rem' }}>
                      最低提现金额：¥100，手续费：2%
                    </Typography>
                  </Box>

                  <Box sx={{ mb: 2.5 }}>
                    <Typography variant="body1" sx={{ mb: 0.75, fontWeight: 500, color: '#374151', fontSize: '0.9rem' }}>
                      提现方式
                    </Typography>
                    <FormControl fullWidth size="small">
                      <Select
                        value={withdrawMethod}
                        onChange={(e) => setWithdrawMethod(e.target.value)}
                        displayEmpty
                        sx={{ fontSize: '0.95rem', height: 44 }}
                      >
                        <MenuItem value="" sx={{ fontSize: '0.95rem' }}>请选择提现方式</MenuItem>
                        <MenuItem value="bank" sx={{ fontSize: '0.95rem' }}>银行卡</MenuItem>
                        <MenuItem value="alipay" sx={{ fontSize: '0.95rem' }}>支付宝</MenuItem>
                      </Select>
                    </FormControl>
                  </Box>

                  <Box sx={{ mb: 2.5 }}>
                    <Typography variant="body1" sx={{ mb: 0.75, fontWeight: 500, color: '#374151', fontSize: '0.9rem' }}>
                      备注（可选）
                    </Typography>
                    <TextField
                      fullWidth
                      size="small"
                      multiline
                      rows={3}
                      placeholder="请输入提现备注..."
                      value={withdrawRemark}
                      onChange={(e) => setWithdrawRemark(e.target.value)}
                      sx={{ '& .MuiInputBase-input': { fontSize: '0.95rem' } }}
                    />
                  </Box>

                  <Box sx={{ display: 'flex', gap: 2, mt: 'auto' }}>
                    <Button 
                      fullWidth
                      variant="outlined" 
                      onClick={handleWithdrawReset}
                      size="medium"
                      sx={{ 
                        borderColor: '#e5e7eb',
                        color: '#6b7280',
                        fontSize: '0.95rem',
                        height: 40,
                        '&:hover': {
                          borderColor: '#d1d5db',
                          backgroundColor: '#f9fafb'
                        }
                      }}
                    >
                      重置
                    </Button>
                    <Button
                      fullWidth
                      variant="contained"
                      onClick={handleWithdrawSubmit}
                      size="medium"
                      sx={{ 
                        backgroundColor: '#10b981',
                        fontSize: '0.95rem',
                        height: 40,
                        '&:hover': { 
                          backgroundColor: '#059669' 
                        }
                      }}
                    >
                      申请提现
                    </Button>
                  </Box>
                </Paper>
              </Grid>

              {/* 注意事项 */}
              <Grid item xs={12} md={5} sx={{ display: 'flex' }}>
                <Paper sx={{ 
                  border: '1px solid #e5e7eb',
                  borderRadius: 2,
                  p: 3.5,
                  flex: 1,
                  backgroundColor: '#f9fafb'
                }}>
                  <Typography variant="h6" sx={{ fontWeight: 600, mb: 2.5, fontSize: '1.2rem', color: '#1a1a1a' }}>
                    注意事项
                  </Typography>
                  
                  <Box sx={{ mb: 2.5 }}>
                    <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1, mb: 1.5 }}>
                      <Info sx={{ fontSize: 20, color: '#3b82f6', mt: 0.25 }} />
                      <Box>
                        <Typography variant="subtitle2" sx={{ fontWeight: 600, color: '#374151', mb: 0.5, fontSize: '0.9rem' }}>
                          提现规则
                        </Typography>
                        <Typography variant="body2" sx={{ color: '#6b7280', lineHeight: 1.6, fontSize: '0.8rem' }}>
                          每日最多可提现 3 次，单笔最高限额 50,000 元。提现申请提交后，一般在1-3个工作日内到账。
                        </Typography>
                      </Box>
                    </Box>
                  </Box>

                  <Box sx={{ mb: 2.5 }}>
                    <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1, mb: 1.5 }}>
                      <Receipt sx={{ fontSize: 20, color: '#10b981', mt: 0.25 }} />
                      <Box>
                        <Typography variant="subtitle2" sx={{ fontWeight: 600, color: '#374151', mb: 0.5, fontSize: '0.9rem' }}>
                          手续费说明
                        </Typography>
                        <Typography variant="body2" sx={{ color: '#6b7280', lineHeight: 1.6, fontSize: '0.8rem' }}>
                          提现手续费为提现金额的2%，最低收取2元。手续费将从提现金额中自动扣除。
                        </Typography>
                      </Box>
                    </Box>
                  </Box>

                  <Box sx={{ mb: 2.5 }}>
                    <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1, mb: 1.5 }}>
                      <AccountBalanceWallet sx={{ fontSize: 20, color: '#f59e0b', mt: 0.25 }} />
                      <Box>
                        <Typography variant="subtitle2" sx={{ fontWeight: 600, color: '#374151', mb: 0.5, fontSize: '0.9rem' }}>
                          支付方式
                        </Typography>
                        <Typography variant="body2" sx={{ color: '#6b7280', lineHeight: 1.6, fontSize: '0.8rem' }}>
                          请在“账户设置”中的“支付方式”页面管理您的银行卡和支付宝账户。默认支付方式将用于接收提现款项。
                        </Typography>
                      </Box>
                    </Box>
                  </Box>

                  <Divider sx={{ my: 2 }} />

                  <Box>
                    <Typography variant="subtitle2" sx={{ fontWeight: 600, color: '#374151', mb: 1.5, fontSize: '0.9rem' }}>
                      常见问题
                    </Typography>
                    <List dense sx={{ p: 0 }}>
                      <ListItem sx={{ px: 0, py: 0.5 }}>
                        <ListItemIcon sx={{ minWidth: 28 }}>
                          <CheckCircle sx={{ fontSize: 16, color: '#10b981' }} />
                        </ListItemIcon>
                        <ListItemText 
                          primary="提现为什么没有到账？"
                          primaryTypographyProps={{ variant: 'caption', color: '#6b7280', fontSize: '0.75rem' }}
                        />
                      </ListItem>
                      <ListItem sx={{ px: 0, py: 0.5 }}>
                        <ListItemIcon sx={{ minWidth: 28 }}>
                          <CheckCircle sx={{ fontSize: 16, color: '#10b981' }} />
                        </ListItemIcon>
                        <ListItemText 
                          primary="可能是银行处理延迟，请耐心等待或3个工作日"
                          primaryTypographyProps={{ variant: 'caption', color: '#6b7280', fontSize: '0.75rem' }}
                        />
                      </ListItem>
                      <ListItem sx={{ px: 0, py: 0.5 }}>
                        <ListItemIcon sx={{ minWidth: 28 }}>
                          <CheckCircle sx={{ fontSize: 16, color: '#10b981' }} />
                        </ListItemIcon>
                        <ListItemText 
                          primary="如有疑问，请联系客服：400-123-4567"
                          primaryTypographyProps={{ variant: 'caption', color: '#6b7280', fontSize: '0.75rem' }}
                        />
                      </ListItem>
                    </List>
                  </Box>
                </Paper>
              </Grid>
            </Grid>
          </Box>
        ) : (
          /* 提现记录页面 */
          <Paper sx={{ 
            border: '1px solid #e5e7eb',
            borderRadius: 2,
            overflow: 'hidden',
            height: '100%',
            display: 'flex',
            flexDirection: 'column'
          }}>
            <TableContainer sx={{ flex: 1 }}>
              <Table size="medium" stickyHeader>
                <TableHead>
                  <TableRow>
                    <TableCell sx={{ backgroundColor: '#fafafa', fontWeight: 600, fontSize: '0.95rem', py: 2 }}>提现时间</TableCell>
                    <TableCell sx={{ backgroundColor: '#fafafa', fontWeight: 600, fontSize: '0.95rem', py: 2 }}>提现金额</TableCell>
                    <TableCell sx={{ backgroundColor: '#fafafa', fontWeight: 600, fontSize: '0.95rem', py: 2 }}>手续费</TableCell>
                    <TableCell sx={{ backgroundColor: '#fafafa', fontWeight: 600, fontSize: '0.95rem', py: 2 }}>实际到账</TableCell>
                    <TableCell sx={{ backgroundColor: '#fafafa', fontWeight: 600, fontSize: '0.95rem', py: 2 }}>提现方式</TableCell>
                    <TableCell align="center" sx={{ backgroundColor: '#fafafa', fontWeight: 600, fontSize: '0.95rem', py: 2 }}>状态</TableCell>
                    <TableCell align="center" sx={{ backgroundColor: '#fafafa', fontWeight: 600, fontSize: '0.95rem', py: 2 }}>操作</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {withdrawHistory
                    .slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage)
                    .map((record) => (
                    <TableRow key={record.id} hover>
                      <TableCell sx={{ fontSize: '0.9rem', color: '#374151', py: 2 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.75 }}>
                          <CalendarToday sx={{ fontSize: 16, color: '#9ca3af' }} />
                          {record.date}
                        </Box>
                      </TableCell>
                      <TableCell sx={{ fontSize: '0.9rem', fontWeight: 600, color: '#374151', py: 2 }}>
                        ¥{record.amount.toLocaleString()}
                      </TableCell>
                      <TableCell sx={{ fontSize: '0.9rem', color: '#ef4444', py: 2 }}>
                        -¥{record.fee}
                      </TableCell>
                      <TableCell sx={{ fontSize: '0.9rem', fontWeight: 600, color: '#10b981', py: 2 }}>
                        ¥{record.actualAmount.toLocaleString()}
                      </TableCell>
                      <TableCell sx={{ fontSize: '0.9rem', color: '#374151', py: 2 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.75 }}>
                          {record.method === '银行卡' ? <AccountBalance sx={{ fontSize: 16 }} /> : <AccountBalanceWallet sx={{ fontSize: 16 }} />}
                          {record.bankInfo}
                        </Box>
                      </TableCell>
                      <TableCell align="center" sx={{ py: 2 }}>
                        {getStatusChip(record.status)}
                      </TableCell>
                      <TableCell align="center" sx={{ py: 2 }}>
                        <IconButton size="small" sx={{ color: '#6b7280', p: 0.75 }}>
                          <Receipt sx={{ fontSize: 18 }} />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
            
            {/* 分页 */}
            <Box sx={{ 
              display: 'flex', 
              justifyContent: 'space-between', 
              alignItems: 'center', 
              p: 2, 
              borderTop: '1px solid #e5e7eb'
            }}>
              <Typography variant="body2" sx={{ color: '#6b7280', fontSize: '0.85rem' }}>
                共 {withdrawHistory.length} 条记录，当前第 {currentPage} 页
              </Typography>
              <Box sx={{ display: 'flex', gap: 0.5, alignItems: 'center' }}>
                <Button
                  size="small"
                  variant="outlined"
                  onClick={() => setCurrentPage(currentPage - 1)}
                  disabled={currentPage === 1}
                  sx={{ 
                    minWidth: 32,
                    borderColor: '#e5e7eb',
                    color: '#6b7280',
                    fontSize: '0.85rem',
                    px: 1,
                    '&:hover': {
                      borderColor: '#d1d5db',
                      backgroundColor: '#f9fafb'
                    },
                    '&.Mui-disabled': {
                      borderColor: '#e5e7eb',
                      color: '#d1d5db'
                    }
                  }}
                >
                  <ChevronLeft sx={{ fontSize: 18 }} />
                </Button>
                
                {/* 页码按钮 */}
                {(() => {
                  const totalPages = Math.ceil(withdrawHistory.length / itemsPerPage);
                  const pages = [];
                  let startPage = 1;
                  let endPage = totalPages;
                  
                  // 显示逻辑：最多显示5个页码
                  if (totalPages > 5) {
                    if (currentPage <= 3) {
                      endPage = 5;
                    } else if (currentPage >= totalPages - 2) {
                      startPage = totalPages - 4;
                    } else {
                      startPage = currentPage - 2;
                      endPage = currentPage + 2;
                    }
                  }
                  
                  for (let i = startPage; i <= endPage; i++) {
                    pages.push(
                      <Button
                        key={i}
                        size="small"
                        variant={currentPage === i ? "contained" : "outlined"}
                        onClick={() => setCurrentPage(i)}
                        sx={{ 
                          minWidth: 32,
                          borderColor: currentPage === i ? '#3b82f6' : '#e5e7eb',
                          backgroundColor: currentPage === i ? '#3b82f6' : 'transparent',
                          color: currentPage === i ? 'white' : '#6b7280',
                          fontSize: '0.85rem',
                          px: 1.5,
                          '&:hover': {
                            borderColor: currentPage === i ? '#2563eb' : '#d1d5db',
                            backgroundColor: currentPage === i ? '#2563eb' : '#f9fafb'
                          }
                        }}
                      >
                        {i}
                      </Button>
                    );
                  }
                  
                  return pages;
                })()}
                
                <Button
                  size="small"
                  variant="outlined"
                  onClick={() => setCurrentPage(currentPage + 1)}
                  disabled={currentPage * itemsPerPage >= withdrawHistory.length}
                  sx={{ 
                    minWidth: 32,
                    borderColor: '#e5e7eb',
                    color: '#6b7280',
                    fontSize: '0.85rem',
                    px: 1,
                    '&:hover': {
                      borderColor: '#d1d5db',
                      backgroundColor: '#f9fafb'
                    },
                    '&.Mui-disabled': {
                      borderColor: '#e5e7eb',
                      color: '#d1d5db'
                    }
                  }}
                >
                  <ChevronRight sx={{ fontSize: 18 }} />
                </Button>
              </Box>
            </Box>
          </Paper>
        )}
        </Box>
      </Box>
    </Box>
  );
}

export default WithdrawManagement;