import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Button,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
  Skeleton,
  Grid,
  Paper,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Visibility,
  CheckCircle,
  RadioButtonUnchecked,
  Campaign,
  Announcement,
  Info,
  Warning,
  CheckCircle as CheckCircleIcon,
  Close
} from '@mui/icons-material';
import announcementService from '../../services/announcementService';

function ChannelAnnouncements() {
  const [announcements, setAnnouncements] = useState([]);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(5);
  const [totalCount, setTotalCount] = useState(0);
  const [selectedAnnouncement, setSelectedAnnouncement] = useState(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [detailLoading, setDetailLoading] = useState(false);

  // 统计数据
  const [stats, setStats] = useState({
    total: 0,
    unread: 0,
    highPriority: 0,
    featureUpdates: 0
  });

  // 获取公告列表
  const fetchAnnouncements = async (currentPage = 0) => {
    try {
      setLoading(true);
      console.log('渠道商公告页面获取数据...');

      const data = await announcementService.getAnnouncements({
        page: currentPage + 1,
        page_size: rowsPerPage,
        target_audience: 'channel' // 渠道商专用参数
      });

      console.log('渠道商公告API响应:', data);

      let items = [];
      let total = 0;

      if (data && data.success && data.data) {
        if (Array.isArray(data.data.items)) {
          items = data.data.items;
          total = data.data.total || items.length;
        } else if (Array.isArray(data.data)) {
          items = data.data;
          total = items.length;
        }
      } else if (data && Array.isArray(data.items)) {
        items = data.items;
        total = data.total || items.length;
      } else if (data && Array.isArray(data)) {
        items = data;
        total = items.length;
      }

      if (items && Array.isArray(items)) {
        console.log('获取到的items:', items);

        // 转换API数据格式为组件需要的格式
        const formattedAnnouncements = items.map(item => ({
          id: item.id,
          title: item.title,
          content: item.content || item.summary || '',
          summary: item.summary || '',
          type: mapAnnouncementType(item.type),
          priority: mapPriority(item.priority),
          publishTime: item.publish_time,
          author: item.creator_name || '系统管理员',
          readCount: item.view_count || 0,
          isRead: false, // 暂时设为false，等后端实现已读状态
          tags: getAnnouncementTags(item),
          category: item.type,
          targetAudience: item.target_audience,
          status: item.status,
          isPinned: item.is_pinned,
          isPopup: item.is_popup,
          expireTime: item.expire_time
        }));


        setAnnouncements(formattedAnnouncements);
        setTotalCount(total);

        // 计算统计数据
        const newStats = {
          total: total,
          unread: formattedAnnouncements.filter(item => !item.isRead).length,
          highPriority: formattedAnnouncements.filter(item => item.priority === 'high').length,
          featureUpdates: formattedAnnouncements.filter(item => item.category === 'feature').length
        };
        setStats(newStats);
      } else {

        setAnnouncements([]);
        setTotalCount(0);
      }
    } catch (error) {

      setAnnouncements([]);
      setTotalCount(0);
    } finally {
      setLoading(false);
    }
  };

  // 映射公告类型
  const mapAnnouncementType = (apiType) => {
    const typeMap = {
      'system': 'warning',
      'maintenance': 'warning',
      'feature': 'success',
      'promotion': 'announcement',
      'notice': 'info'
    };
    return typeMap[apiType] || 'info';
  };

  // 映射优先级
  const mapPriority = (apiPriority) => {
    const priorityMap = {
      'high': 'high',
      'medium': 'medium',
      'low': 'low'
    };
    return priorityMap[apiPriority] || 'medium';
  };

  // 获取公告标签
  const getAnnouncementTags = (item) => {
    const tags = [];
    if (item.is_pinned) tags.push('置顶');
    if (item.priority === 'high') tags.push('重要');
    if (item.target_audience === 'all') tags.push('全部');
    if (item.target_audience === 'channel') tags.push('渠道商');
    if (item.type) tags.push(getTypeLabel(item.type));
    return tags;
  };

  // 获取类型标签
  const getTypeLabel = (type) => {
    const typeLabels = {
      'system': '系统',
      'maintenance': '维护',
      'feature': '功能',
      'promotion': '推广',
      'notice': '通知'
    };
    return typeLabels[type] || type;
  };

  // 获取公告详情
  const fetchAnnouncementDetail = async (id) => {
    try {
      console.log('获取公告详情，ID:', id);
      const response = await announcementService.getAnnouncementById(id);
      console.log('公告详情API响应:', response);

      // 检查不同的响应格式
      let item = null;
      if (response.success && response.data) {
        item = response.data;
      } else if (response.data) {
        // 有些API直接返回data
        item = response.data;
      } else if (response.success !== false && response.id) {
        // 直接返回公告对象
        item = response;
      } else {
        console.warn('无法解析公告详情响应:', response);
        return null;
      }

      if (item) {
        const detailData = {
          id: item.id,
          title: item.title,
          content: item.content,
          summary: item.summary,
          type: mapAnnouncementType(item.type),
          priority: mapPriority(item.priority),
          publishTime: item.publish_time,
          author: item.creator_name || '系统管理员',
          readCount: item.view_count || 0,
          isRead: true, // 查看详情后标记为已读
          tags: getAnnouncementTags(item),
          category: item.type,
          targetAudience: item.target_audience,
          status: item.status,
          isPinned: item.is_pinned,
          isPopup: item.is_popup,
          expireTime: item.expire_time
        };
        console.log('格式化后的公告详情:', detailData);
        return detailData;
      }
      
      return null;
    } catch (error) {
      console.error('获取公告详情失败:', error);
      throw error;
    }
  };

  // 处理公告点击
  const handleAnnouncementClick = async (announcement) => {
    console.log('点击公告:', announcement.title);
    
    // 先设置基本的公告信息，确保弹窗能够显示
    setSelectedAnnouncement(announcement);
    setDialogOpen(true);
    setDetailLoading(true);

    try {
      // 获取详细信息
      const detailData = await fetchAnnouncementDetail(announcement.id);
      console.log('获取到详细信息:', detailData);
      
      // 如果获取到详细信息，则更新；否则保持基本信息
      if (detailData) {
        setSelectedAnnouncement(detailData);
      } else {
        console.warn('未获取到详细信息，使用基本信息');
        // 确保基本信息有content字段
        setSelectedAnnouncement({
          ...announcement,
          content: announcement.summary || announcement.content || '暂无详细内容'
        });
      }

      // 标记为已读并更新阅读次数
      setAnnouncements(prev =>
        prev.map(item =>
          item.id === announcement.id
            ? { ...item, isRead: true, readCount: (item.readCount || 0) + 1 }
            : item
        )
      );

      // TODO: 标记已读API暂时注释，等后端实现
      // try {
      //   await announcementService.markAsRead(announcement.id);
      // } catch (error) {
      //   console.warn('标记已读失败:', error);
      // }

    } catch (error) {
      console.error('获取公告详情失败:', error);
      // 如果获取详情失败，仍然显示基本信息
      setSelectedAnnouncement({
        ...announcement,
        content: announcement.summary || announcement.content || '获取详细内容失败，请稍后重试'
      });
    } finally {
      setDetailLoading(false);
    }
  };

  // 关闭详情对话框
  const handleCloseDialog = (event, reason) => {
    console.log('关闭弹窗, reason:', reason);
    // 防止在加载时意外关闭
    if (detailLoading && reason === 'backdropClick') {
      console.log('正在加载，阻止关闭');
      return;
    }
    setDialogOpen(false);
    setSelectedAnnouncement(null);
    setDetailLoading(false);
  };

  // 处理分页变化
  const handleChangePage = (event, newPage) => {
    setPage(newPage);
    fetchAnnouncements(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    const newRowsPerPage = parseInt(event.target.value, 10);
    setRowsPerPage(newRowsPerPage);
    setPage(0);
    fetchAnnouncements(0);
  };

  // 组件挂载时获取数据
  useEffect(() => {
    fetchAnnouncements();
  }, []);

  // 获取优先级颜色
  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high':
        return 'error';
      case 'medium':
        return 'warning';
      case 'low':
        return 'success';
      default:
        return 'default';
    }
  };

  // 获取优先级标签
  const getPriorityLabel = (priority) => {
    switch (priority) {
      case 'high':
        return '高';
      case 'medium':
        return '中';
      case 'low':
        return '低';
      default:
        return '中';
    }
  };

  // 获取类型图标
  const getTypeIcon = (type) => {
    switch (type) {
      case 'warning':
        return <Warning sx={{ color: '#f59e0b' }} />;
      case 'info':
        return <Info sx={{ color: '#3b82f6' }} />;
      case 'success':
        return <CheckCircleIcon sx={{ color: '#10b981' }} />;
      case 'announcement':
        return <Campaign sx={{ color: '#8b5cf6' }} />;
      default:
        return <Announcement sx={{ color: '#6b7280' }} />;
    }
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      {/* 页面标题 */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" sx={{ fontWeight: 700, color: '#1a1a1a', mb: 1 }}>
          最新公告
        </Typography>
        <Typography variant="body1" sx={{ color: '#6b7280' }}>
          查看系统公告和重要通知信息
        </Typography>
      </Box>

      {/* 统计卡片 */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ borderRadius: 2, border: '1px solid #e5e7eb', boxShadow: 'none' }}>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h4" sx={{ fontWeight: 700, color: '#3b82f6', mb: 1 }}>
                {stats.total}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                总公告数
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ borderRadius: 2, border: '1px solid #e5e7eb', boxShadow: 'none' }}>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h4" sx={{ fontWeight: 700, color: '#f59e0b', mb: 1 }}>
                {stats.unread}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                未读公告
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ borderRadius: 2, border: '1px solid #e5e7eb', boxShadow: 'none' }}>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h4" sx={{ fontWeight: 700, color: '#ef4444', mb: 1 }}>
                {stats.highPriority}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                高优先级
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ borderRadius: 2, border: '1px solid #e5e7eb', boxShadow: 'none' }}>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h4" sx={{ fontWeight: 700, color: '#10b981', mb: 1 }}>
                {stats.featureUpdates}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                功能更新
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* 公告表格 */}
      <Card sx={{ borderRadius: 2, border: '1px solid #e5e7eb', boxShadow: 'none' }}>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow sx={{ backgroundColor: '#f8f9fa' }}>
                <TableCell sx={{ fontWeight: 600, color: '#374151' }}>状态</TableCell>
                <TableCell sx={{ fontWeight: 600, color: '#374151' }}>标题</TableCell>
                <TableCell sx={{ fontWeight: 600, color: '#374151' }}>类型</TableCell>
                <TableCell sx={{ fontWeight: 600, color: '#374151' }}>优先级</TableCell>
                <TableCell sx={{ fontWeight: 600, color: '#374151' }}>发布时间</TableCell>
                <TableCell sx={{ fontWeight: 600, color: '#374151' }}>阅读次数</TableCell>
                <TableCell sx={{ fontWeight: 600, color: '#374151' }}>操作</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                // 加载状态
                Array.from({ length: rowsPerPage }).map((_, index) => (
                  <TableRow key={index}>
                    <TableCell><Skeleton width={30} height={30} /></TableCell>
                    <TableCell><Skeleton width="80%" /></TableCell>
                    <TableCell><Skeleton width={60} /></TableCell>
                    <TableCell><Skeleton width={40} /></TableCell>
                    <TableCell><Skeleton width={100} /></TableCell>
                    <TableCell><Skeleton width={50} /></TableCell>
                    <TableCell><Skeleton width={80} /></TableCell>
                  </TableRow>
                ))
              ) : announcements.length === 0 ? (
                // 空状态
                <TableRow>
                  <TableCell colSpan={7} sx={{ textAlign: 'center', py: 4 }}>
                    <Typography variant="body2" color="text.secondary">
                      暂无公告数据
                    </Typography>
                  </TableCell>
                </TableRow>
              ) : (
                // 公告列表
                announcements.map((announcement) => (
                  <TableRow
                    key={announcement.id}
                    sx={{
                      '&:hover': {
                        backgroundColor: '#f5f5f5'
                      }
                    }}
                  >
                    {/* 状态列 */}
                    <TableCell>
                      {announcement.isRead ? (
                        <Tooltip title="已读">
                          <CheckCircle sx={{ color: '#10b981', fontSize: 20 }} />
                        </Tooltip>
                      ) : (
                        <Tooltip title="未读">
                          <RadioButtonUnchecked sx={{ color: '#3b82f6', fontSize: 20 }} />
                        </Tooltip>
                      )}
                    </TableCell>

                    {/* 标题列 */}
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        {getTypeIcon(announcement.type)}
                        <Box>
                          <Typography variant="body2" sx={{ fontWeight: 600, color: '#1a1a1a' }}>
                            {announcement.title}
                          </Typography>
                          {announcement.tags && announcement.tags.length > 0 && (
                            <Box sx={{ display: 'flex', gap: 0.5, mt: 0.5 }}>
                              {announcement.tags.slice(0, 3).map((tag, index) => (
                                <Chip
                                  key={index}
                                  label={tag}
                                  size="small"
                                  variant="outlined"
                                  sx={{ fontSize: '0.75rem', height: 20 }}
                                />
                              ))}
                            </Box>
                          )}
                        </Box>
                      </Box>
                    </TableCell>

                    {/* 类型列 */}
                    <TableCell>
                      <Chip
                        label={getTypeLabel(announcement.category)}
                        size="small"
                        variant="outlined"
                        color={announcement.type === 'warning' ? 'warning' :
                               announcement.type === 'success' ? 'success' :
                               announcement.type === 'info' ? 'info' : 'default'}
                      />
                    </TableCell>

                    {/* 优先级列 */}
                    <TableCell>
                      <Chip
                        label={getPriorityLabel(announcement.priority)}
                        size="small"
                        color={getPriorityColor(announcement.priority)}
                      />
                    </TableCell>

                    {/* 发布时间列 */}
                    <TableCell>
                      <Typography variant="body2" color="text.secondary">
                        {announcement.publishTime ? new Date(announcement.publishTime).toLocaleDateString('zh-CN') : '-'}
                      </Typography>
                    </TableCell>

                    {/* 阅读次数列 */}
                    <TableCell>
                      <Typography variant="body2" color="text.secondary">
                        {announcement.readCount || 0}
                      </Typography>
                    </TableCell>

                    {/* 操作列 */}
                    <TableCell>
                      <Button
                        size="small"
                        variant="outlined"
                        startIcon={<Visibility />}
                        onClick={(e) => {
                          e.stopPropagation();
                          handleAnnouncementClick(announcement);
                        }}
                        sx={{ fontSize: '0.75rem' }}
                      >
                        查看
                      </Button>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>

        {/* 分页 */}
        <TablePagination
          component="div"
          count={totalCount}
          page={page}
          onPageChange={handleChangePage}
          rowsPerPage={rowsPerPage}
          onRowsPerPageChange={handleChangeRowsPerPage}
          rowsPerPageOptions={[5, 10, 25]}
          labelRowsPerPage="每页显示:"
          labelDisplayedRows={({ from, to, count }) => `${from}-${to} 共 ${count} 条`}
        />
      </Card>

      {/* 公告详情弹窗 */}
      <Dialog
        open={dialogOpen}
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
        slotProps={{
          paper: {
            sx: { borderRadius: 2 }
          }
        }}
      >
        <DialogTitle sx={{
          pb: 2,
          display: 'flex',
          alignItems: 'center',
          gap: 2
        }}>
          {selectedAnnouncement && (
            <>
              {getTypeIcon(selectedAnnouncement.type)}
              <Box sx={{ flex: 1 }}>
                <Typography variant="h6" component="div">
                  {selectedAnnouncement.title}
                  {selectedAnnouncement.priority === 'high' && (
                    <Chip
                      label="重要"
                      size="small"
                      color="error"
                      sx={{ ml: 1 }}
                    />
                  )}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {selectedAnnouncement.author} • {selectedAnnouncement.publishTime ? new Date(selectedAnnouncement.publishTime).toLocaleString('zh-CN') : '-'}
                  {selectedAnnouncement.readCount > 0 && ` • 阅读 ${selectedAnnouncement.readCount} 次`}
                </Typography>
              </Box>
              <IconButton onClick={handleCloseDialog} size="small">
                <Close />
              </IconButton>
            </>
          )}
        </DialogTitle>

        <DialogContent sx={{ py: 3 }}>
          {detailLoading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
              <CircularProgress />
            </Box>
          ) : selectedAnnouncement ? (
            <Box>
              {/* 公告摘要 */}
              {selectedAnnouncement.summary && selectedAnnouncement.summary !== selectedAnnouncement.content && (
                <Box sx={{ mb: 3, p: 2, backgroundColor: '#f8f9fa', borderRadius: 1 }}>
                  <Typography variant="subtitle2" color="primary" gutterBottom>
                    摘要
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {selectedAnnouncement.summary}
                  </Typography>
                </Box>
              )}

              {/* 公告内容 */}
              <Typography variant="body1" sx={{ lineHeight: 1.8, whiteSpace: 'pre-wrap' }}>
                {selectedAnnouncement.content}
              </Typography>

              {/* 公告标签 */}
              {selectedAnnouncement.tags && selectedAnnouncement.tags.length > 0 && (
                <Box sx={{ mt: 3 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    标签
                  </Typography>
                  <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                    {selectedAnnouncement.tags.map((tag, index) => (
                      <Chip
                        key={index}
                        label={tag}
                        size="small"
                        variant="outlined"
                      />
                    ))}
                  </Box>
                </Box>
              )}

              {/* 公告信息 */}
              <Box sx={{ mt: 3, pt: 2, borderTop: '1px solid #e5e7eb' }}>
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">
                      <strong>目标受众：</strong>
                      {selectedAnnouncement.targetAudience === 'all' ? '全部用户' :
                       selectedAnnouncement.targetAudience === 'enterprise' ? '企业用户' :
                       selectedAnnouncement.targetAudience === 'channel' ? '渠道商' :
                       selectedAnnouncement.targetAudience === 'agent' ? '代理商' :
                       selectedAnnouncement.targetAudience}
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">
                      <strong>状态：</strong>
                      {selectedAnnouncement.status === 'published' ? '已发布' :
                       selectedAnnouncement.status === 'draft' ? '草稿' :
                       selectedAnnouncement.status}
                    </Typography>
                  </Grid>
                  {selectedAnnouncement.expireTime && (
                    <Grid item xs={12}>
                      <Typography variant="body2" color="text.secondary">
                        <strong>过期时间：</strong>
                        {new Date(selectedAnnouncement.expireTime).toLocaleString('zh-CN')}
                      </Typography>
                    </Grid>
                  )}
                </Grid>
              </Box>
            </Box>
          ) : null}
        </DialogContent>

        <DialogActions sx={{ px: 3, pb: 2 }}>
          <Button onClick={handleCloseDialog}>
            关闭
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
}

export default ChannelAnnouncements;