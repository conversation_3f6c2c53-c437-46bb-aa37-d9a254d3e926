import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Ty<PERSON>graphy,
  Button,
  IconButton,
  TextField,
  MenuItem,
  InputAdornment,
  FormControl,
  Select,
  LinearProgress,
  Alert,
  Tooltip,
  Tabs,
  Tab,
  Chip,
} from '@mui/material';
import {
  Store,
  Search,
  Add,
  Refresh,
  Delete,
  Link,
  LinkOff,
  Edit,
  ToggleOff,
  ToggleOn,
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';
import ChannelBindingForm from '../../components/channel/ChannelBindingForm';
import CreateServiceForm from '../../components/channel/CreateServiceForm';
import EditServiceForm from '../../components/channel/EditServiceForm';
import ServiceDetailDialog from '../../components/channel/ServiceDetailDialog';
import channelService from '../../services/channelService';

const StyledContainer = styled(Box)(() => ({
  height: 'calc(100vh - 64px)',
  display: 'flex',
  flexDirection: 'column',
  backgroundColor: '#fafafa',
  overflow: 'hidden',
}));

const HeaderSection = styled(Box)(({ theme }) => ({
  padding: theme.spacing(3),
  backgroundColor: '#fff',
  borderBottom: '1px solid #e0e0e0',
}));

const ContentSection = styled(Box)(({ theme }) => ({
  flex: 1,
  overflow: 'auto',
  padding: theme.spacing(3),
  '&::-webkit-scrollbar': {
    width: '8px',
  },
  '&::-webkit-scrollbar-track': {
    background: 'transparent',
  },
  '&::-webkit-scrollbar-thumb': {
    background: '#e0e0e0',
    borderRadius: '4px',
  },
}));

const ChannelCard = styled(Card)(() => ({
  width: '100%',
  height: '240px',
  display: 'flex',
  flexDirection: 'column',
  borderRadius: '0',
  border: '1px solid #e5e5e5',
  transition: 'all 0.2s ease',
  cursor: 'pointer',
  boxShadow: 'none',
  position: 'relative',
  overflow: 'hidden',
  backgroundColor: '#fff',
  '&:hover': {
    borderColor: '#d0d0d0',
    backgroundColor: '#fafafa',
    '& .channel-actions': {
      opacity: 1,
    },
  },
}));

function MyChannels() {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [bindingDialogOpen, setBindingDialogOpen] = useState(false);
  const [createServiceDialogOpen, setCreateServiceDialogOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [dataLoading, setDataLoading] = useState(true);
  const [error, setError] = useState(null);

  // 服务详情对话框状态
  const [serviceDetailOpen, setServiceDetailOpen] = useState(false);
  const [selectedServiceData, setSelectedServiceData] = useState(null);

  // 编辑服务对话框状态
  const [editServiceDialogOpen, setEditServiceDialogOpen] = useState(false);
  const [selectedEditService, setSelectedEditService] = useState(null);

  // 真实的渠道数据（已绑定的系统服务）
  const [channels, setChannels] = useState([]);
  // 原始API数据，用于服务详情显示
  const [originalChannelsData, setOriginalChannelsData] = useState([]);

  // 渠道商创建的服务数据
  const [myCreatedServices, setMyCreatedServices] = useState([]);
  const [createdServicesLoading, setCreatedServicesLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('bound'); // 'bound' | 'created'
  // 原始创建服务数据，用于服务详情显示
  const [originalCreatedServicesData, setOriginalCreatedServicesData] = useState([]);

  // 加载已绑定的服务数据
  const loadBoundServices = async () => {
    try {
      setDataLoading(true);
      setError(null);

      // 调用获取我的系统服务列表接口
      const response = await channelService.getMyCategories();

      if (response.success && response.data && response.data.items) {
        // 保存原始数据
        setOriginalChannelsData(response.data.items);

        // 转换数据格式以适配前端组件
        const formattedChannels = response.data.items.map(mapping => {
          const service = mapping.service || {};
          const category = service.category || {};

          // 调试信息
          console.log('原始数据:', {
            mapping_id: mapping.id,
            service_name: service.service_name,
            portal_type: service.portal_type,
            channel_type: service.channel_type,
            category_name: category.category_name
          });

          // 直接使用后端返回的平台类型，简化逻辑
          let platformName = service.portal_type || service.channel_type || category.category_name || '系统服务';

          console.log('平台映射调试:', {
            service_name: service.service_name,
            portal_type: service.portal_type,
            channel_type: service.channel_type,
            category_name: category.category_name,
            final_platform: platformName
          });

          return {
            id: mapping.id,
            serviceId: service.id || mapping.service_id,
            serviceName: service.service_name || '未知服务',
            category: category.category_name || '未分类',
            platform: platformName,
            status: mapping.is_active && service.is_active ? 'active' : 'inactive',
            basePrice: service.base_price || 0,
            priceUnit: service.price_unit || '次',
            deliveryTime: service.delivery_time || 0,
            revisionCount: service.revision_count || 0,
            rating: 0, // 暂时没有评分数据
            soldCount: 0, // 暂时没有销售数据
            monthlyRevenue: 0, // 暂时没有收益数据
            completedTasks: 0, // 暂时没有完成任务数据
            pendingTasks: 0,   // 暂时没有待处理任务数据
            bindDate: new Date(mapping.created_at).toLocaleDateString('zh-CN'),
            isFeatured: false, // 暂时没有推荐标识
            performance: {
              completionRate: 0,
              avgDeliveryTime: service.delivery_time || 0,
              customerSatisfaction: 0
            }
          };
        });

        setChannels(formattedChannels);
      } else {
        setChannels([]);
        setOriginalChannelsData([]);
        setError(response.message || '获取服务列表失败');
      }
    } catch (error) {
      setError('加载服务列表失败，请稍后重试');
      setChannels([]);
      setOriginalChannelsData([]);
    } finally {
      setDataLoading(false);
    }
  };

  // 加载渠道商创建的服务数据
  const loadMyCreatedServices = async () => {
    try {
      setCreatedServicesLoading(true);
      setError(null);

      // 调用获取我创建的服务列表接口
      const response = await channelService.getMyCreatedServices();

      if (response.success && response.data && response.data.items) {
        // 保存原始数据
        setOriginalCreatedServicesData(response.data.items);

        // 转换数据格式以适配前端组件
        const formattedServices = response.data.items.map(service => ({
          id: service.id,
          serviceName: service.service_name || '未知服务',
          serviceCode: service.service_code || '',
          category: service.category_name || '',
          description: service.service_description || '',
          basePrice: service.base_price || 0,
          discountPrice: service.discount_price || null,
          priceUnit: service.price_unit || '次',
          deliveryTime: service.delivery_time || 0,
          revisionCount: service.revision_count || 0,
          channelType: service.channel_type || '',
          portalType: service.portal_type || '',
          coverageArea: service.coverage_area || [],
          serviceFeatures: service.service_features || [],
          // 审批状态相关
          approvalStatus: service.approval_status || 'pending',
          approvalNote: service.approval_note || '',
          approvalTime: service.approval_time || null,
          // 服务状态
          isActive: service.is_active || false,
          // 时间信息
          createdAt: service.created_at,
          updatedAt: service.updated_at,
          // 统计信息
          soldCount: service.sold_count || 0,
          ratingScore: service.rating_score || 0
        }));

        setMyCreatedServices(formattedServices);
      } else {
        setOriginalCreatedServicesData([]);
        setMyCreatedServices([]);
      }
    } catch (error) {
      console.error('加载我创建的服务失败:', error);
      setError('加载服务列表失败，请稍后重试');
      setOriginalCreatedServicesData([]);
      setMyCreatedServices([]);
    } finally {
      setCreatedServicesLoading(false);
    }
  };

  // 组件挂载时加载数据
  useEffect(() => {
    loadBoundServices();
    loadMyCreatedServices();
  }, []);



  const getStatusColor = (status) => {
    switch (status) {
      case 'active':
        return { bg: '#d1fae5', text: '#065f46', label: '运营中' };
      case 'inactive':
        return { bg: '#fee2e2', text: '#991b1b', label: '已停用' };
      default:
        return { bg: '#f3f4f6', text: '#374151', label: '未知' };
    }
  };

  // 获取审批状态颜色
  const getApprovalStatusColor = (status) => {
    switch (status) {
      case 'pending':
        return { bg: '#fef3c7', text: '#92400e', label: '待审核' };
      case 'approved':
        return { bg: '#d1fae5', text: '#065f46', label: '已通过' };
      case 'rejected':
        return { bg: '#fee2e2', text: '#991b1b', label: '已拒绝' };
      default:
        return { bg: '#f3f4f6', text: '#374151', label: '未知' };
    }
  };

  // 处理服务状态切换（启用/禁用）
  const handleToggleServiceStatus = async (service) => {
    const newStatus = !service.isActive;
    const actionText = newStatus ? '启用' : '禁用';

    if (!window.confirm(`确定要${actionText}服务"${service.serviceName}"吗？`)) {
      return;
    }

    try {
      setLoading(true);

      const response = await channelService.updateMyService(service.id, {
        is_active: newStatus
      });

      if (response.success) {
        alert(`服务${actionText}成功！`);
        // 重新加载服务列表
        loadMyCreatedServices();
      } else {
        alert(response.message || `${actionText}失败，请重试`);
      }
    } catch (error) {
      alert(`${actionText}服务失败，请稍后重试`);
    } finally {
      setLoading(false);
    }
  };

  // 处理删除服务
  const handleDeleteService = async (service) => {
    if (!window.confirm(`确定要删除服务"${service.serviceName}"吗？此操作不可撤销。`)) {
      return;
    }

    try {
      setLoading(true);

      const response = await channelService.deleteMyService(service.id);

      if (response.success) {
        alert('服务删除成功！');
        // 重新加载服务列表
        loadMyCreatedServices();
      } else {
        alert(response.message || '删除失败，请重试');
      }
    } catch (error) {
      alert('删除服务失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 处理查看服务详情
  const handleViewServiceDetails = (service) => {
    // 找到对应的原始数据（包含完整的service信息）
    const originalData = originalCreatedServicesData.find(item => item.id === service.id);

    // 对于我创建的服务，API直接返回服务数据，不需要嵌套在service字段中
    setSelectedServiceData({
      service: originalData || service,
      type: 'created' // 标识这是创建的服务
    });
    setServiceDetailOpen(true);
  };

  // 处理编辑服务
  const handleEditService = (service) => {
    setSelectedEditService(service);
    setEditServiceDialogOpen(true);
  };



  const handleBindChannel = async (bindingData) => {
    setLoading(true);

    try {
      // 只发送后端需要的字段
      const finalBindingData = {
        is_active: bindingData.is_active !== undefined ? bindingData.is_active : true
      };

      // 调用绑定系统服务API
      const response = await channelService.bindSystemService(bindingData.service_id, finalBindingData);

      if (response.success) {
        setBindingDialogOpen(false);

        // 显示成功消息
        alert('服务绑定成功！您的自定义配置已保存。');

        // 重新加载已绑定的服务列表
        loadBoundServices();
      } else {
        alert(response.message || '绑定失败，请重试');
      }
    } catch (error) {
      alert('绑定系统服务失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  const handleUnbindChannel = async (channel) => {
    if (!window.confirm(`确定要删除服务"${channel.serviceName}"吗？此操作不可撤销。`)) {
      return;
    }

    try {
      setLoading(true);

      // 调用删除接口
      const response = await channelService.unbindSystemService(channel.serviceId);

      if (response.success) {
        alert('服务删除成功！');
        // 重新加载服务列表
        loadBoundServices();
      } else {
        alert(response.message || '删除失败，请重试');
      }
    } catch (error) {
      alert('删除服务失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  const handleToggleStatus = async (channel) => {
    const newStatus = channel.status === 'active' ? false : true;
    const actionText = newStatus ? '启用' : '暂停';

    if (!window.confirm(`确定要${actionText}服务"${channel.serviceName}"吗？`)) {
      return;
    }

    try {
      setLoading(true);

      // 调用更新状态接口
      const response = await channelService.updateServiceStatus(channel.serviceId, {
        is_active: newStatus
      });

      if (response.success) {
        alert(`服务${actionText}成功！`);
        // 重新加载服务列表
        loadBoundServices();
      } else {
        alert(response.message || `${actionText}失败，请重试`);
      }
    } catch (error) {
      alert(`${actionText}服务失败，请稍后重试`);
    } finally {
      setLoading(false);
    }
  };

  const handleViewDetails = (channel) => {
    // 找到对应的原始数据（包含完整的service信息）
    const originalData = originalChannelsData.find(item => item.id === channel.id);
    setSelectedServiceData(originalData);
    setServiceDetailOpen(true);
  };

  // 过滤渠道
  const filteredChannels = channels.filter(channel => {
    const matchesSearch = (channel.serviceName || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
                          (channel.category || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
                          (channel.platform || '').toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || channel.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  // 过滤我创建的服务
  const filteredCreatedServices = myCreatedServices.filter(service => {
    const matchesSearch = (service.serviceName || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
                          (service.category || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
                          (service.channelType || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
                          (service.portalType || '').toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || service.approvalStatus === statusFilter;
    return matchesSearch && matchesStatus;
  });

  return (
    <StyledContainer>
      {/* 头部区域 */}
      <HeaderSection>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Box>
            <Typography variant="h5" sx={{ fontWeight: 600, color: '#333', mb: 0.5 }}>
              我的渠道
            </Typography>
            <Typography variant="body2" sx={{ color: '#999' }}>
              管理您绑定的渠道服务和自己创建的服务
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Button
              variant="outlined"
              startIcon={<Add />}
              onClick={() => setCreateServiceDialogOpen(true)}
              sx={{
                color: '#4285f4',
                borderColor: '#4285f4',
                borderRadius: 1,
                '&:hover': {
                  backgroundColor: '#f8f9ff',
                  borderColor: '#3367d6'
                }
              }}
            >
              创建渠道服务
            </Button>
            <Button
              variant="contained"
              startIcon={<Add />}
              onClick={() => setBindingDialogOpen(true)}
              sx={{
                color: '#fff',
                backgroundColor: '#4285f4',
                borderRadius: 1,
                '&:hover': {
                  backgroundColor: '#3367d6'
                }
              }}
            >
              绑定系统服务
            </Button>
          </Box>
        </Box>

        {/* 标签页 */}
        <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
          <Tabs
            value={activeTab}
            onChange={(_, newValue) => setActiveTab(newValue)}
            sx={{
              '& .MuiTab-root': {
                textTransform: 'none',
                fontWeight: 500,
                fontSize: '0.95rem',
                minHeight: 48,
              }
            }}
          >
            <Tab
              label={
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Link />
                  <span>已绑定服务</span>
                  <Chip
                    label={channels.length}
                    size="small"
                    sx={{
                      height: 20,
                      fontSize: '0.75rem',
                      backgroundColor: '#e3f2fd',
                      color: '#1976d2'
                    }}
                  />
                </Box>
              }
              value="bound"
            />
            <Tab
              label={
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Store />
                  <span>我创建的服务</span>
                  <Chip
                    label={myCreatedServices.length}
                    size="small"
                    sx={{
                      height: 20,
                      fontSize: '0.75rem',
                      backgroundColor: '#f5f5f5',
                      color: '#666666'
                    }}
                  />
                </Box>
              }
              value="created"
            />
          </Tabs>
        </Box>

        {/* 搜索和筛选栏 */}
        <Box display="flex" gap={2} alignItems="center" sx={{ pt: 2, borderTop: '1px solid #f0f0f0' }}>
          <TextField
            placeholder="搜索渠道名称、分类或平台..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            size="small"
            sx={{
              flex: 1,
              maxWidth: 400,
              '& .MuiOutlinedInput-root': {
                borderRadius: 0.5,
                '& fieldset': {
                  borderColor: '#e5e5e5',
                },
                '&:hover fieldset': {
                  borderColor: '#d0d0d0',
                },
              }
            }}
            slotProps={{
              input: {
                startAdornment: (
                  <InputAdornment position="start">
                    <Search sx={{ color: '#999' }} />
                  </InputAdornment>
                ),
              }
            }}
          />

          <FormControl size="small" sx={{ minWidth: 120 }}>
            <Select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              displayEmpty
              sx={{
                borderRadius: 0.5,
                '& .MuiOutlinedInput-notchedOutline': {
                  borderColor: '#e5e5e5',
                },
                '&:hover .MuiOutlinedInput-notchedOutline': {
                  borderColor: '#d0d0d0',
                },
              }}
            >
              <MenuItem value="all">全部状态</MenuItem>
              {activeTab === 'bound' ? [
                <MenuItem key="active" value="active">运营中</MenuItem>,
                <MenuItem key="inactive" value="inactive">已停用</MenuItem>
              ] : [
                <MenuItem key="pending" value="pending">待审核</MenuItem>,
                <MenuItem key="approved" value="approved">已通过</MenuItem>,
                <MenuItem key="rejected" value="rejected">已拒绝</MenuItem>
              ]}
            </Select>
          </FormControl>

          <Button
            variant="outlined"
            startIcon={<Refresh />}
            onClick={() => {
              if (activeTab === 'bound') {
                loadBoundServices();
              } else {
                loadMyCreatedServices();
              }
            }}
            sx={{
              borderRadius: 0.5,
              borderColor: '#e5e5e5',
              color: '#666',
              '&:hover': {
                borderColor: '#d0d0d0',
                backgroundColor: '#f5f5f5'
              }
            }}
          >
            刷新数据
          </Button>
        </Box>
      </HeaderSection>

      {/* 内容区域 */}
      <ContentSection>
        {/* 错误提示 */}
        {error && (
          <Box sx={{ mb: 2 }}>
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
              <Button
                size="small"
                onClick={() => {
                  if (activeTab === 'bound') {
                    loadBoundServices();
                  } else {
                    loadMyCreatedServices();
                  }
                }}
                sx={{ ml: 2 }}
              >
                重试
              </Button>
            </Alert>
          </Box>
        )}

        {/* 加载状态 */}
        {((activeTab === 'bound' && dataLoading) || (activeTab === 'created' && createdServicesLoading)) && (
          <Box
            display="flex"
            flexDirection="column"
            alignItems="center"
            justifyContent="center"
            height="400px"
          >
            <LinearProgress sx={{ width: '200px', mb: 2 }} />
            <Typography variant="body2" color="text.secondary">
              {activeTab === 'bound' ? '正在加载已绑定服务...' : '正在加载我创建的服务...'}
            </Typography>
          </Box>
        )}

        {/* 内容区域 - 根据activeTab显示不同内容 */}
        {activeTab === 'bound' && !dataLoading && (
          <>
            {/* 已绑定服务列表 */}
            {filteredChannels.length > 0 ? (
          <Box
            sx={{
              display: 'flex !important',
              flexWrap: 'wrap !important',
              gap: '24px !important',
              justifyContent: 'flex-start !important',
              alignItems: 'flex-start !important',
              width: '100% !important',
              '& > *': {
                flexShrink: '0 !important',
                flexGrow: '0 !important',
                width: '320px !important',
                height: '240px !important'
              }
            }}
          >
            {filteredChannels.map((channel) => {
              const statusStyle = getStatusColor(channel.status);
              return (
                <ChannelCard
                  key={channel.id}
                  onClick={() => handleViewDetails(channel)}
                >
                  {/* 顶部操作按钮 */}
                  <Box
                    className="channel-actions"
                    sx={{
                      position: 'absolute',
                      top: 8,
                      right: 8,
                      opacity: 0,
                      transition: 'opacity 0.2s ease',
                      display: 'flex',
                      gap: 0.5,
                      zIndex: 1,
                    }}
                  >
                    <Tooltip title={channel.status === 'active' ? '暂停服务' : '启用服务'} arrow>
                      <IconButton
                        size="small"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleToggleStatus(channel);
                        }}
                        sx={{
                          backgroundColor: 'rgba(255, 255, 255, 0.9)',
                          color: channel.status === 'active' ? '#f59e0b' : '#10b981',
                          '&:hover': {
                            backgroundColor: '#fff',
                            color: channel.status === 'active' ? '#f59e0b' : '#10b981'
                          }
                        }}
                      >
                        {channel.status === 'active' ? <LinkOff fontSize="small" /> : <Link fontSize="small" />}
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="删除服务" arrow>
                      <IconButton
                        size="small"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleUnbindChannel(channel);
                        }}
                        sx={{
                          backgroundColor: 'rgba(255, 255, 255, 0.9)',
                          color: '#666',
                          '&:hover': {
                            backgroundColor: '#fff',
                            color: '#f44336'
                          }
                        }}
                      >
                        <Delete fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </Box>

                  <CardContent
                    sx={{
                      p: 2.5,
                      display: 'flex !important',
                      flexDirection: 'column !important',
                      height: '100% !important',
                      width: '100% !important',
                      position: 'relative',
                      boxSizing: 'border-box !important',
                      overflow: 'hidden !important',
                      cursor: 'pointer'
                    }}
                  >
                    {/* 标题区域 - 固定高度 */}
                    <Box sx={{
                      height: '60px !important',
                      mb: 1.5,
                      width: '100% !important',
                      overflow: 'visible !important'
                    }}>
                      <Typography
                        variant="subtitle1"
                        sx={{
                          fontWeight: 500,
                          color: '#333',
                          mb: 0.5,
                          overflow: 'hidden !important',
                          textOverflow: 'ellipsis !important',
                          display: '-webkit-box !important',
                          WebkitLineClamp: 2,
                          WebkitBoxOrient: 'vertical',
                          lineHeight: 1.2,
                          height: '2.4em !important',
                          fontSize: '0.95rem',
                          width: '100% !important',
                          wordBreak: 'break-word'
                        }}
                      >
                        {channel.serviceName}
                      </Typography>
                      <Box sx={{
                        display: 'flex !important',
                        gap: 0.5,
                        height: '24px !important',
                        overflow: 'visible !important',
                        width: '100% !important',
                        alignItems: 'center'
                      }}>
                        <Typography variant="caption" sx={{
                          color: statusStyle.text,
                          backgroundColor: statusStyle.bg,
                          border: '1px solid #e5e5e5',
                          px: 0.8,
                          py: 0.3,
                          borderRadius: 0.5,
                          fontSize: '0.65rem',
                          lineHeight: 1.2,
                          minHeight: '20px !important',
                          display: 'inline-flex !important',
                          alignItems: 'center',
                          whiteSpace: 'nowrap !important',
                          flexShrink: 0
                        }}>
                          {statusStyle.label}
                        </Typography>

                      </Box>
                    </Box>

                  {/* 分类和平台信息 */}
                  <Box sx={{
                    height: '40px',
                    mb: 1.5,
                    width: '100%',
                    overflow: 'hidden'
                  }}>
                    <Box sx={{
                      display: 'flex',
                      gap: 0.5,
                      flexWrap: 'wrap',
                      height: '100%',
                      alignItems: 'flex-start'
                    }}>
                      {channel.category && (
                        <Typography variant="caption" sx={{
                          color: '#666',
                          border: '1px solid #e5e5e5',
                          px: 0.8,
                          py: 0.3,
                          borderRadius: 0.5,
                          fontSize: '0.65rem',
                          lineHeight: 1.2,
                          minHeight: '20px',
                          display: 'inline-flex',
                          alignItems: 'center',
                          whiteSpace: 'nowrap',
                          flexShrink: 0
                        }}>
                          {channel.category}
                        </Typography>
                      )}
                      {channel.platform && (
                        <Typography variant="caption" sx={{
                          color: '#2563eb',
                          backgroundColor: '#eff6ff',
                          border: '1px solid #e5e5e5',
                          px: 0.8,
                          py: 0.3,
                          borderRadius: 0.5,
                          fontSize: '0.65rem',
                          lineHeight: 1.2,
                          minHeight: '20px',
                          display: 'inline-flex',
                          alignItems: 'center',
                          whiteSpace: 'nowrap',
                          flexShrink: 0
                        }}>
                          {channel.platform}
                        </Typography>
                      )}
                    </Box>
                  </Box>

                    {/* 描述区域 */}
                    <Box sx={{
                      height: '40px !important',
                      mb: 1.5,
                      width: '100% !important',
                      overflow: 'hidden !important'
                    }}>
                      <Typography
                        variant="body2"
                        sx={{
                          color: '#666',
                          fontSize: '0.8rem',
                          lineHeight: 1.4,
                          overflow: 'hidden !important',
                          textOverflow: 'ellipsis !important',
                          display: '-webkit-box !important',
                          WebkitLineClamp: 2,
                          WebkitBoxOrient: 'vertical',
                          height: '2.8em !important',
                          width: '100% !important'
                        }}
                      >
                        {[channel.category, channel.platform, `¥${channel.basePrice}/${channel.priceUnit}`].filter(Boolean).join(' • ')}
                      </Typography>
                    </Box>

                    {/* 统计信息 */}
                    <Box sx={{
                      height: '30px !important',
                      mb: 1.5,
                      width: '100% !important',
                      display: 'flex !important',
                      alignItems: 'center',
                      justifyContent: 'space-between'
                    }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.3 }}>
                      </Box>
                      <Typography variant="caption" sx={{ color: '#999', fontSize: '0.65rem' }}>
                        {channel.bindDate}
                      </Typography>
                    </Box>
                  </CardContent>
                </ChannelCard>
              );
            })}
          </Box>
        ) : (
          <Box
            display="flex"
            flexDirection="column"
            alignItems="center"
            justifyContent="center"
            height="400px"
          >
            <Store sx={{ fontSize: 64, color: 'text.disabled', mb: 2 }} />
            <Typography variant="h6" color="text.secondary" gutterBottom>
              暂无绑定的渠道服务
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
              点击右上角"绑定系统服务"开始添加您的第一个渠道
            </Typography>
            <Button
              variant="contained"
              startIcon={<Add />}
              onClick={() => setBindingDialogOpen(true)}
              sx={{
                backgroundColor: '#3b82f6',
                '&:hover': { backgroundColor: '#2563eb' }
              }}
            >
              绑定系统服务
            </Button>
          </Box>
        )}
          </>
        )}

        {/* 我创建的服务列表 */}
        {activeTab === 'created' && !createdServicesLoading && (
          <>
            {filteredCreatedServices.length > 0 ? (
              <Box
                sx={{
                  display: 'flex !important',
                  flexWrap: 'wrap !important',
                  gap: '24px !important',
                  justifyContent: 'flex-start !important',
                  alignItems: 'flex-start !important',
                  width: '100% !important',
                  '& > *': {
                    flexShrink: '0 !important',
                    flexGrow: '0 !important',
                    width: '320px !important',
                    minHeight: '280px !important'
                  }
                }}
              >
                {filteredCreatedServices.map((service) => {
                  const approvalStatusStyle = getApprovalStatusColor(service.approvalStatus);
                  return (
                    <ChannelCard
                      key={service.id}
                      onClick={() => handleViewServiceDetails(service)}
                    >
                      {/* 顶部操作按钮 */}
                      <Box
                        className="channel-actions"
                        sx={{
                          position: 'absolute',
                          top: 8,
                          right: 8,
                          opacity: 0,
                          transition: 'opacity 0.2s ease',
                          display: 'flex',
                          gap: 0.5,
                          zIndex: 1,
                        }}
                      >
                        {service.approvalStatus === 'approved' && (
                          <Tooltip title={service.isActive ? '禁用服务' : '启用服务'}>
                            <IconButton
                              size="small"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleToggleServiceStatus(service);
                              }}
                              sx={{
                                backgroundColor: 'rgba(255, 255, 255, 0.9)',
                                '&:hover': { backgroundColor: 'white' }
                              }}
                            >
                              {service.isActive ? <ToggleOn color="success" /> : <ToggleOff color="disabled" />}
                            </IconButton>
                          </Tooltip>
                        )}
                        <Tooltip title="编辑服务">
                          <IconButton
                            size="small"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleEditService(service);
                            }}
                            sx={{
                              backgroundColor: 'rgba(255, 255, 255, 0.9)',
                              '&:hover': { backgroundColor: 'white' }
                            }}
                          >
                            <Edit fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="删除服务">
                          <IconButton
                            size="small"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDeleteService(service);
                            }}
                            sx={{
                              backgroundColor: 'rgba(255, 255, 255, 0.9)',
                              '&:hover': { backgroundColor: 'white' }
                            }}
                          >
                            <Delete fontSize="small" color="error" />
                          </IconButton>
                        </Tooltip>
                      </Box>

                      <CardContent sx={{ p: 2, height: '100%', display: 'flex', flexDirection: 'column' }}>
                        {/* 服务名称和状态 */}
                        <Box sx={{ mb: 1.5, flex: '0 0 auto' }}>
                          <Typography
                            variant="h6"
                            sx={{
                              fontWeight: 600,
                              color: '#333',
                              mb: 1,
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              display: '-webkit-box',
                              WebkitLineClamp: 2,
                              WebkitBoxOrient: 'vertical',
                              lineHeight: 1.2,
                              height: '2.4em',
                              fontSize: '0.95rem',
                            }}
                          >
                            {service.serviceName}
                          </Typography>
                          <Box sx={{ display: 'flex', gap: 0.5, alignItems: 'center', flexWrap: 'wrap' }}>
                            <Chip
                              label={approvalStatusStyle.label}
                              size="small"
                              sx={{
                                backgroundColor: approvalStatusStyle.bg,
                                color: approvalStatusStyle.text,
                                fontSize: '0.65rem',
                                height: 20,
                              }}
                            />
                            {service.approvalStatus === 'approved' && (
                              <Chip
                                label={service.isActive ? '已启用' : '已禁用'}
                                size="small"
                                sx={{
                                  backgroundColor: service.isActive ? '#d1fae5' : '#fee2e2',
                                  color: service.isActive ? '#065f46' : '#991b1b',
                                  fontSize: '0.65rem',
                                  height: 20,
                                }}
                              />
                            )}
                          </Box>
                        </Box>

                        {/* 服务描述 */}
                        <Box sx={{ mb: 1.5, flex: '1 1 auto' }}>
                          <Typography
                            variant="body2"
                            sx={{
                              color: '#666',
                              fontSize: '0.8rem',
                              lineHeight: 1.4,
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              display: '-webkit-box',
                              WebkitLineClamp: 3,
                              WebkitBoxOrient: 'vertical',
                            }}
                          >
                            {service.description || '暂无描述'}
                          </Typography>
                        </Box>

                        {/* 价格和基本信息 */}
                        <Box sx={{ flex: '0 0 auto' }}>
                          <Typography variant="body2" sx={{ color: '#666', fontSize: '0.8rem', mb: 1 }}>
                            {[service.category, service.channelType, service.portalType].filter(Boolean).join(' • ')}
                          </Typography>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                            <Typography variant="body1" sx={{ fontWeight: 600, color: '#e91e63' }}>
                              ¥{service.basePrice}/{service.priceUnit}
                            </Typography>
                            <Typography variant="caption" sx={{ color: '#999', fontSize: '0.65rem' }}>
                              {new Date(service.createdAt).toLocaleDateString('zh-CN')}
                            </Typography>
                          </Box>
                        </Box>
                      </CardContent>
                    </ChannelCard>
                  );
                })}
              </Box>
            ) : (
              <Box
                display="flex"
                flexDirection="column"
                alignItems="center"
                justifyContent="center"
                height="400px"
              >
                <Store sx={{ fontSize: 64, color: 'text.disabled', mb: 2 }} />
                <Typography variant="h6" color="text.secondary" gutterBottom>
                  {myCreatedServices.length === 0 ? '暂无创建的服务' : '没有找到匹配的服务'}
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                  {myCreatedServices.length === 0
                    ? '点击右上角"创建渠道服务"开始创建您的第一个服务'
                    : '请尝试调整搜索条件或筛选器'
                  }
                </Typography>
                {myCreatedServices.length === 0 && (
                  <Button
                    variant="contained"
                    startIcon={<Add />}
                    onClick={() => setCreateServiceDialogOpen(true)}
                    sx={{
                      backgroundColor: '#3b82f6',
                      '&:hover': { backgroundColor: '#2563eb' }
                    }}
                  >
                    创建渠道服务
                  </Button>
                )}
              </Box>
            )}
          </>
        )}
      </ContentSection>

      {/* 绑定渠道对话框 */}
      <ChannelBindingForm
        open={bindingDialogOpen}
        onClose={() => setBindingDialogOpen(false)}
        onSubmit={handleBindChannel}
        loading={loading}
        boundServices={channels}
      />

      {/* 创建服务对话框 */}
      <CreateServiceForm
        open={createServiceDialogOpen}
        onClose={() => setCreateServiceDialogOpen(false)}
        onSuccess={() => {
          loadMyCreatedServices(); // 重新加载我创建的服务数据
          setActiveTab('created'); // 切换到我创建的服务标签页
        }}
      />

      {/* 服务详情对话框 */}
      <ServiceDetailDialog
        open={serviceDetailOpen}
        onClose={() => setServiceDetailOpen(false)}
        serviceData={selectedServiceData}
      />

      {/* 编辑服务对话框 */}
      <EditServiceForm
        open={editServiceDialogOpen}
        onClose={() => setEditServiceDialogOpen(false)}
        service={selectedEditService}
        onSuccess={() => {
          loadMyCreatedServices(); // 重新加载我创建的服务数据
        }}
      />
    </StyledContainer>
  );
}

export default MyChannels;