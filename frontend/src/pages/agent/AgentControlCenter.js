import React, { useState, useEffect, Fragment } from 'react';
import {
  Box,
  Typography,
  Grid,
  Avatar,
  Chip,
  Button,
  LinearProgress,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  Switch,
  Alert,
  Fab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  Drawer,
  ListItemButton,
  Collapse,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  InputAdornment,
  IconButton,
  Container,
  Card,
  Paper,
  CircularProgress,
} from '@mui/material';
import {
  Campaign,
  Link,
  TrendingUp,
  Analytics,
  MonetizationOn,
  People,
  Settings,
  Notifications,
  Share,
  Assessment,
  GroupAdd,
  Dashboard,
  AccountTree,
  School,
  ExpandLess,
  ExpandMore,
  Menu,
  MenuOpen,
  Close,
  Search,
  Add,
  FileDownload,
  Edit,
  Visibility,
  Email,
  Schedule,
  FileCopy,
  <PERSON><PERSON>hart,
  <PERSON>use,
  AttachMoney,
  CheckCircle,
  Refresh,
  PlayArrow,
  Delete,
  Article,
  Logout,
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import { useTheme } from '@mui/material/styles';
import ApiService from '../../services/api';
import AgentAccountSettings from './AgentAccountSettings';
import AgentAnnouncements from './AgentAnnouncements';
import announcementService from '../../services/announcementService';

function AgentControlCenter() {
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  const theme = useTheme();
  const [stats, setStats] = useState({
    referralLinks: 0,
    activeReferrals: 0,
    conversionRate: 0,
    linkClicks: 0,
    totalCustomers: 0,
    totalRevenue: 0,
  });
  const [statsLoading, setStatsLoading] = useState(false);
  const [notifications, setNotifications] = useState(true);
  const [settingsOpen, setSettingsOpen] = useState(false);
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [currentPage, setCurrentPage] = useState('dashboard');
  const [expandedMenus, setExpandedMenus] = useState({});

  // 推广链接相关状态
  const [referralLinks, setReferralLinks] = useState([]);
  const [linkForm, setLinkForm] = useState({
    link_name: '',
    target_page: '',
    campaign_name: '',
    description: ''
  });

  // 客户管理相关状态
  const [customers, setCustomers] = useState([]);
  const [customersLoading, setCustomersLoading] = useState(false);
  const [customersPagination, setCustomersPagination] = useState({
    page: 1,
    size: 20,
    total: 0
  });

  const [customersStatistics, setCustomersStatistics] = useState({
    total_customers: 0,
    active_customers: 0,
    trial_customers: 0,
    inactive_customers: 0,
    total_revenue: 0,
    avg_spent_per_customer: 0
  });
  const [customersFilters, setCustomersFilters] = useState({
    status: '',
    search: ''
  });

  // 公告相关状态
  const [announcements, setAnnouncements] = useState([]);
  const [announcementLoading, setAnnouncementLoading] = useState(true);
  const [selectedAnnouncement, setSelectedAnnouncement] = useState(null);
  const [announcementDetailOpen, setAnnouncementDetailOpen] = useState(false);
  const [detailLoading, setDetailLoading] = useState(false);

  // 客户详情对话框状态
  const [customerDetailDialog, setCustomerDetailDialog] = useState({
    open: false,
    customer: null,
    loading: false
  });
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    page: 1,
    size: 20,
    total: 0
  });

  // 提现相关状态
  const [earningsData, setEarningsData] = useState({
    available_balance: 0,
    pending_balance: 0,
    withdraw_fee_rate: 0.02
  });
  const [withdrawDialogOpen, setWithdrawDialogOpen] = useState(false);
  const [withdrawFilters, setWithdrawFilters] = useState({
    status: ''
  });
  const [withdrawRecords, setWithdrawRecords] = useState([]);
  const [withdrawLoading, setWithdrawLoading] = useState(false);
  const [withdrawPagination, setWithdrawPagination] = useState({
    page: 1,
    size: 20,
    total: 0
  });
  const [withdrawForm, setWithdrawForm] = useState({
    withdraw_amount: '',
    withdraw_method: '',
    bank_account: '',
    alipay_account: '',
    withdraw_reason: ''
  });

  // 编辑对话框状态
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [editingLink, setEditingLink] = useState(null);
  const [editForm, setEditForm] = useState({
    link_name: '',
    target_page: '',
    campaign_name: '',
    description: ''
  });

  // 导航菜单配置
  const navigationItems = [
    {
      id: 'dashboard',
      title: '推广概览',
      icon: <Dashboard />,
      path: 'dashboard'
    },
    {
      id: 'promotion',
      title: '推广工具',
      icon: <Campaign />,
      path: 'promotion/links'
    },
    {
      id: 'commission',
      title: '收益管理',
      icon: <MonetizationOn />,
      path: 'commission/withdraw'
    },
    {
      id: 'customers',
      title: '客户管理',
      icon: <People />,
      path: 'customers/list'
    },
    {
      id: 'announcements',
      title: '最新公告',
      icon: <Campaign />,
      path: 'announcements'
    },
    {
      id: 'settings',
      title: '个人设置',
      icon: <Settings />,
      path: 'settings'
    }
  ];

  const handleMenuClick = (item) => {
    setCurrentPage(item.path);
  };

  const handleSubMenuClick = (path) => {
    setCurrentPage(path);
  };

  // 获取页面标题
  const getPageTitle = () => {
    const pageTitles = {
      'dashboard': '推广概览',
      'promotion/links': '推广工具',
      'commission/withdraw': '收益管理',
      'customers/list': '客户管理',
      'settings': '个人设置'
    };
    return pageTitles[currentPage] || '代理商控制中心';
  };

  // 获取页面描述
  const getPageDescription = () => {
    const pageDescriptions = {
      'dashboard': '查看推广数据和收益统计',
      'promotion/links': '生成和管理您的推广链接',
      'commission/withdraw': '管理您的佣金提现',
      'customers/list': '查看和管理推广客户',
      'settings': '管理个人资料和账户设置'
    };
    return pageDescriptions[currentPage] || '管理您的推广业务和佣金收入';
  };

  // API调用函数
  const fetchReferralLinks = async () => {
    try {
      setLoading(true);
      const response = await ApiService.getReferralLinks({
        page: pagination.page,
        size: pagination.size
      });

      if (response.success) {
        setReferralLinks(response.data.items || []);
        setPagination(prev => ({
          ...prev,
          total: response.data.total || 0
        }));
      }
    } catch (error) {
      console.error('获取推广链接失败:', error);
      // 这里可以添加错误提示
    } finally {
      setLoading(false);
    }
  };

  // 获取客户列表
  const fetchCustomers = async () => {
    try {
      setCustomersLoading(true);
      const response = await ApiService.getAgentCustomers({
        page: customersPagination.page,
        size: customersPagination.size,
        status: customersFilters.status || undefined,
        search: customersFilters.search || undefined,
        sort_by: 'register_date',
        sort_order: 'desc'
      });

      if (response.success) {
        setCustomers(response.data.items || []);
        setCustomersPagination(prev => ({
          ...prev,
          total: response.data.pagination?.total || 0
        }));
        setCustomersStatistics(response.data.statistics || {});
      }
    } catch (error) {
      console.error('获取客户列表失败:', error);
      // 这里可以添加错误提示
    } finally {
      setCustomersLoading(false);
    }
  };

  // 获取代理商统计数据 - 暂时使用模拟数据
  const fetchAgentStatistics = async () => {
    try {
      setStatsLoading(true);
      // 后端接口暂未实现，使用模拟数据
      // TODO: 等待后端实现 /agents/statistics 接口
      setStats({
        referralLinks: referralLinks.length || 0,
        activeReferrals: customers.filter(c => c.status === 'active').length || 0,
        conversionRate: 25.5,
        linkClicks: 1250,
        totalCustomers: customers.length || 0,
        totalRevenue: 45680,
      });
    } catch (error) {
      console.error('获取代理商统计失败:', error);
    } finally {
      setStatsLoading(false);
    }
  };

  // 获取客户统计数据 - 暂时使用模拟数据
  const fetchCustomersStatistics = async () => {
    try {
      // 后端接口暂未实现，使用模拟数据
      // TODO: 等待后端实现 /agents/customers/statistics 接口
      setCustomersStatistics({
        total_customers: customers.length || 0,
        active_customers: customers.filter(c => c.status === 'active').length || 0,
        vip_customers: customers.filter(c => c.level === 'VIP').length || 0,
        trial_customers: customers.filter(c => c.status === 'trial').length || 0
      });
    } catch (error) {
      console.error('获取客户统计失败:', error);
    }
  };

  // 查看客户详情
  const handleViewCustomer = async (customer) => {
    try {
      setCustomerDetailDialog(prev => ({ ...prev, open: true, loading: true, customer: null }));

      const response = await ApiService.getCustomerDetail(customer.id);
      if (response.success) {
        setCustomerDetailDialog(prev => ({
          ...prev,
          customer: response.data,
          loading: false
        }));
      }
    } catch (error) {
      console.error('获取客户详情失败:', error);
      setCustomerDetailDialog(prev => ({ ...prev, loading: false }));
      alert('获取客户详情失败：' + (error.response?.data?.detail || error.message));
    }
  };

  // 关闭客户详情对话框
  const handleCloseCustomerDetail = () => {
    setCustomerDetailDialog({ open: false, customer: null, loading: false });
  };

  // 获取收益统计数据
  const fetchEarningsData = async () => {
    try {
      const response = await ApiService.getChannelEarnings();
      if (response.success) {
        const data = response.data;
        setEarningsData({
          available_balance: data.summary?.pending_settlement || 0,
          pending_balance: data.summary?.current_month_earnings || 0,
          total_withdrawn: data.summary?.total_earnings || 0,
          withdraw_fee_rate: 0.02 // 默认2%手续费
        });
      }
    } catch (error) {
      console.error('获取收益统计失败:', error);
    }
  };

  // 获取佣金记录
  const fetchWithdrawRecords = async () => {
    try {
      setWithdrawLoading(true);
      const response = await ApiService.getCommissions({
        page: withdrawPagination.page,
        size: withdrawPagination.size,
        status: withdrawFilters.status || undefined
      });

      if (response.success) {
        setWithdrawRecords(response.data.items || []);
        setWithdrawPagination(prev => ({
          ...prev,
          total: response.data.pagination?.total || 0
        }));
      }
    } catch (error) {
      console.error('获取佣金记录失败:', error);
    } finally {
      setWithdrawLoading(false);
    }
  };

  // 申请佣金结算
  const handleWithdrawSubmit = async () => {
    try {
      const response = await ApiService.applyCommissionSettlement({
        settlement_amount: parseFloat(withdrawForm.amount),
        bank_account: {
          account_name: withdrawForm.account_holder,
          bank_name: withdrawForm.bank_name,
          account_number: withdrawForm.bank_account
        },
        note: withdrawForm.withdraw_reason || '代理商佣金结算申请'
      });

      if (response.success) {
        setWithdrawDialogOpen(false);
        setWithdrawForm({
          amount: '',
          bank_account: '',
          bank_name: '',
          account_holder: '',
          withdraw_reason: ''
        });
        // 重新获取数据
        fetchWithdrawRecords();
        fetchEarningsData();
        // 显示成功消息
        alert('佣金结算申请提交成功！');
      }
    } catch (error) {
      console.error('佣金结算申请失败:', error);
      alert('佣金结算申请失败：' + (error.response?.data?.detail || error.message));
    }
  };

  const createReferralLink = async () => {
    try {
      setLoading(true);
      const response = await ApiService.createReferralLink(linkForm);

      if (response.success) {
        // 重置表单
        setLinkForm({
          link_name: '',
          target_page: '',
          campaign_name: '',
          description: ''
        });
        // 刷新列表
        await fetchReferralLinks();
        // 这里可以添加成功提示
        alert('推广链接创建成功！');
      }
    } catch (error) {
      console.error('创建推广链接失败:', error);
      alert('创建推广链接失败：' + (error.response?.data?.detail || error.message));
    } finally {
      setLoading(false);
    }
  };

  // 编辑推广链接
  const handleEditLink = (link) => {
    setEditingLink(link);
    setEditForm({
      link_name: link.link_name,
      target_page: link.target_page,
      campaign_name: link.campaign_name || '',
      description: link.description || ''
    });
    setEditDialogOpen(true);
  };

  const updateReferralLink = async () => {
    try {
      setLoading(true);
      const response = await ApiService.updateReferralLink(editingLink.id, editForm);

      if (response.success) {
        setEditDialogOpen(false);
        setEditingLink(null);
        await fetchReferralLinks();
        alert('推广链接更新成功！');
      }
    } catch (error) {
      console.error('更新推广链接失败:', error);
      alert('更新推广链接失败：' + (error.response?.data?.detail || error.message));
    } finally {
      setLoading(false);
    }
  };

  // 删除推广链接
  const handleDeleteLink = async (link) => {
    if (!window.confirm(`确定要删除推广链接"${link.link_name}"吗？此操作不可恢复。`)) {
      return;
    }

    try {
      setLoading(true);
      const response = await ApiService.deleteReferralLink(link.id);

      if (response.success) {
        await fetchReferralLinks();
        alert('推广链接删除成功！');
      }
    } catch (error) {
      console.error('删除推广链接失败:', error);
      alert('删除推广链接失败：' + (error.response?.data?.detail || error.message));
    } finally {
      setLoading(false);
    }
  };

  // 切换推广链接状态
  const handleToggleLinkStatus = async (link) => {
    const action = link.is_active ? '停用' : '启用';
    if (!window.confirm(`确定要${action}推广链接"${link.link_name}"吗？`)) {
      return;
    }

    try {
      setLoading(true);
      const response = await ApiService.toggleReferralLinkStatus(link.id);

      if (response.success) {
        await fetchReferralLinks();
        alert(`推广链接${action}成功！`);
      }
    } catch (error) {
      console.error(`${action}推广链接失败:`, error);
      alert(`${action}推广链接失败：` + (error.response?.data?.detail || error.message));
    } finally {
      setLoading(false);
    }
  };

  // 复制链接
  const handleCopyLink = async (url) => {
    try {
      await navigator.clipboard.writeText(url);
      alert('推广链接已复制到剪贴板！');
    } catch (error) {
      console.error('复制失败:', error);
      // 降级方案：使用传统的复制方法
      const textArea = document.createElement('textarea');
      textArea.value = url;
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      try {
        document.execCommand('copy');
        alert('推广链接已复制到剪贴板！');
      } catch (fallbackError) {
        console.error('降级复制也失败:', fallbackError);
        alert('复制失败，请手动复制链接');
      }
      document.body.removeChild(textArea);
    }
  };

  // 页面加载时获取数据
  useEffect(() => {
    if (currentPage === 'dashboard') {
      fetchAgentStatistics();
    } else if (currentPage === 'promotion/links') {
      fetchReferralLinks();
    } else if (currentPage === 'customers/list') {
      fetchCustomers();
    }
  }, [currentPage, pagination.page, pagination.size, customersPagination.page, customersPagination.size]);

  // 初始加载统计数据
  useEffect(() => {
    fetchAgentStatistics();
    fetchAnnouncements();
  }, []);

  // 客户筛选条件变化时重新获取数据
  useEffect(() => {
    if (currentPage === 'customers/list') {
      setCustomersPagination(prev => ({ ...prev, page: 1 }));
      fetchCustomers();
    }
  }, [customersFilters.status, customersFilters.search]);

  // 渲染页面内容
  const renderPageContent = () => {
    if (currentPage === 'dashboard') {
      return renderDashboardContent();
    } else if (currentPage === 'promotion/links') {
      return renderPromotionContent();
    } else if (currentPage === 'commission/withdraw') {
      return renderCommissionContent();
    } else if (currentPage === 'customers/list') {
      return renderCustomersContent();
    } else if (currentPage === 'announcements') {
      return <AgentAnnouncements />;
    } else if (currentPage === 'settings') {
      return renderSettingsContent();
    }
    return renderDashboardContent(); // 默认显示概览
  };

  // 渲染推广工具页面
  const renderPromotionContent = () => {
    if (currentPage === 'promotion/links') {
      return (
        <Box sx={{ width: '100%', p: 3, backgroundColor: 'white' }}>
          {/* 页面标题和操作按钮区域 - 白色背景带圆角 */}
          <Box sx={{
            backgroundColor: '#fff',
            borderRadius: 0,
            border: '1px solid #e5e7eb',
            p: 4,
            mb: 3,
            border: '1px solid #e5e7eb'
          }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Link sx={{ fontSize: 32, color: '#1a1a1a' }} />
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 700, color: '#1a1a1a', mb: 0.5 }}>
                    推广链接
                  </Typography>
                </Box>
              </Box>
            </Box>
          </Box>



          {/* 链接生成器 */}
          <Box sx={{ mb: 4 }}>
            <Typography variant="h6" sx={{ fontWeight: 600, mb: 3, display: 'flex', alignItems: 'center', gap: 1, color: '#1a1a1a' }}>
              <Add />
              生成新的推广链接
            </Typography>
            <Box sx={{ borderRadius: 2, border: '1px solid #e5e7eb' }}>
              <Box sx={{ p: 4 }}>
                <Box sx={{ display: 'flex', gap: 2, mb: 3, mt: 2, flexWrap: 'nowrap', overflowX: 'auto' }}>
                  <Box sx={{ minWidth: 200, flex: 1, mt: 1 }}>
                    <TextField
                      fullWidth
                      label="链接名称"
                      variant="outlined"
                      value={linkForm.link_name}
                      onChange={(e) => setLinkForm(prev => ({ ...prev, link_name: e.target.value }))}
                      InputLabelProps={{ shrink: true }}
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          borderRadius: 1,
                          '&:hover .MuiOutlinedInput-notchedOutline': {
                            borderColor: '#1a1a1a',
                          },
                          '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                            borderColor: '#1a1a1a',
                          }
                        }
                      }}
                    />
                  </Box>
                  <Box sx={{ minWidth: 150, flex: 1, mt: 1 }}>
                    <FormControl fullWidth>
                      <InputLabel shrink sx={{ backgroundColor: '#fff', px: 1 }}>推广页面</InputLabel>
                      <Select
                        label="推广页面"
                        value={linkForm.target_page}
                        onChange={(e) => setLinkForm(prev => ({ ...prev, target_page: e.target.value }))}
                        sx={{
                          borderRadius: 1,
                          '&:hover .MuiOutlinedInput-notchedOutline': {
                            borderColor: '#1a1a1a',
                          },
                          '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                            borderColor: '#1a1a1a',
                          }
                        }}
                      >
                        <MenuItem value="">首页</MenuItem>
                        <MenuItem value="auth/register">注册页面</MenuItem>
                      </Select>
                    </FormControl>
                  </Box>
                  <Box sx={{ minWidth: 150, flex: 1, mt: 1 }}>
                    <TextField
                      fullWidth
                      label="活动名称"
                      variant="outlined"
                      value={linkForm.campaign_name}
                      onChange={(e) => setLinkForm(prev => ({ ...prev, campaign_name: e.target.value }))}
                      InputLabelProps={{ shrink: true }}
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          borderRadius: 1,
                          '&:hover .MuiOutlinedInput-notchedOutline': {
                            borderColor: '#1a1a1a',
                          },
                          '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                            borderColor: '#1a1a1a',
                          }
                        }
                      }}
                    />
                  </Box>
                  <Box sx={{ minWidth: 150, flex: 1, mt: 1 }}>
                    <TextField
                      fullWidth
                      label="备注信息"
                      variant="outlined"
                      value={linkForm.description}
                      onChange={(e) => setLinkForm(prev => ({ ...prev, description: e.target.value }))}
                      InputLabelProps={{ shrink: true }}
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          borderRadius: 1,
                          '&:hover .MuiOutlinedInput-notchedOutline': {
                            borderColor: '#1a1a1a',
                          },
                          '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                            borderColor: '#1a1a1a',
                          }
                        }
                      }}
                    />
                  </Box>
                  <Box sx={{ display: 'flex', gap: 1, alignItems: 'flex-end', minWidth: 180 }}>
                    <Button
                      variant="outlined"
                      sx={{ px: 3, py: 1.5, borderRadius: 2, minWidth: 80 }}
                      onClick={() => setLinkForm({
                        link_name: '',
                        target_page: '',
                        campaign_name: '',
                        description: ''
                      })}
                    >
                      重置
                    </Button>
                    <Button
                      variant="contained"
                      disabled={loading || !linkForm.link_name.trim() || !linkForm.target_page}
                      onClick={createReferralLink}
                      sx={{
                        px: 3,
                        py: 1.5,
                        borderRadius: 1,
                        minWidth: 90,
                        backgroundColor: '#1a1a1a',
                        '&:hover': {
                          backgroundColor: '#2c2c2c',
                        },
                        '&:disabled': {
                          backgroundColor: '#e0e0e0',
                        }
                      }}
                    >
                      {loading ? '生成中...' : '生成链接'}
                    </Button>
                  </Box>
                </Box>
              </Box>
            </Box>
          </Box>

          {/* 推广链接管理 */}
          <Box>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
              <Typography variant="h6" sx={{ fontWeight: 600, color: '#1a1a1a', display: 'flex', alignItems: 'center', gap: 1 }}>
                <Assessment />
                我的推广链接
              </Typography>
              <Box sx={{ display: 'flex', gap: 2 }}>
                <FormControl size="small" sx={{ minWidth: 120 }}>
                  <InputLabel>状态筛选</InputLabel>
                  <Select label="状态筛选" defaultValue="">
                    <MenuItem value="">全部状态</MenuItem>
                    <MenuItem value="active">活跃</MenuItem>
                    <MenuItem value="inactive">停用</MenuItem>
                    <MenuItem value="expired">已过期</MenuItem>
                  </Select>
                </FormControl>
                <FormControl size="small" sx={{ minWidth: 120 }}>
                  <InputLabel>渠道筛选</InputLabel>
                  <Select label="渠道筛选" defaultValue="">
                    <MenuItem value="">全部渠道</MenuItem>
                    <MenuItem value="wechat">微信</MenuItem>
                    <MenuItem value="weibo">微博</MenuItem>
                    <MenuItem value="qq">QQ</MenuItem>
                    <MenuItem value="email">邮件</MenuItem>
                  </Select>
                </FormControl>
                <Button
                  variant="outlined"
                  startIcon={<Refresh />}
                  sx={{ borderRadius: 2 }}
                  onClick={fetchReferralLinks}
                  disabled={loading}
                >
                  {loading ? '刷新中...' : '刷新数据'}
                </Button>
              </Box>
            </Box>

            {/* 推广链接表格 */}
            <TableContainer sx={{
              borderRadius: 2,
              border: '1px solid rgba(0,0,0,0.08)',
              overflowX: 'auto',
              backgroundColor: '#fff',
              border: '1px solid #e5e7eb'
            }}>
              <Table>
                <TableHead>
                  <TableRow sx={{ backgroundColor: '#f8f9fa' }}>
                    <TableCell sx={{ fontWeight: 600, color: '#1a1a1a', fontSize: '0.875rem' }}>链接名称</TableCell>
                    <TableCell sx={{ fontWeight: 600, color: '#1a1a1a', fontSize: '0.875rem' }}>推广链接</TableCell>
                    <TableCell sx={{ fontWeight: 600, color: '#1a1a1a', fontSize: '0.875rem' }}>渠道</TableCell>
                    <TableCell sx={{ fontWeight: 600, color: '#1a1a1a', fontSize: '0.875rem' }}>点击数</TableCell>
                    <TableCell sx={{ fontWeight: 600, color: '#1a1a1a', fontSize: '0.875rem' }}>转化数</TableCell>
                    <TableCell sx={{ fontWeight: 600, color: '#1a1a1a', fontSize: '0.875rem' }}>状态</TableCell>
                    <TableCell sx={{ fontWeight: 600, color: '#1a1a1a', fontSize: '0.875rem' }}>创建时间</TableCell>
                    <TableCell sx={{ fontWeight: 600, color: '#1a1a1a', fontSize: '0.875rem' }}>操作</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {referralLinks.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={8} sx={{ textAlign: 'center', py: 4 }}>
                        <Typography variant="body2" sx={{ color: '#666' }}>
                          暂无推广链接，请先生成推广链接
                        </Typography>
                      </TableCell>
                    </TableRow>
                  ) : (
                    referralLinks.map((link, index) => (
                      <TableRow key={link.id} sx={{
                        '&:hover': { backgroundColor: '#f8f9fa' },
                        borderBottom: '1px solid rgba(0,0,0,0.08)'
                      }}>
                        <TableCell sx={{ fontSize: '0.875rem', fontWeight: 500 }}>
                          {link.link_name}
                        </TableCell>
                      <TableCell sx={{ fontSize: '0.875rem', maxWidth: 200 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Typography
                            variant="body2"
                            sx={{
                              fontFamily: 'Monaco, Consolas, monospace',
                              fontSize: '0.75rem',
                              color: '#666',
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              whiteSpace: 'nowrap',
                              maxWidth: 150
                            }}
                          >
                            {link.referral_url}
                          </Typography>
                          <IconButton
                            size="small"
                            sx={{ p: 0.5 }}
                            onClick={() => handleCopyLink(link.referral_url)}
                            title="复制链接"
                          >
                            <FileCopy sx={{ fontSize: 14 }} />
                          </IconButton>
                        </Box>
                      </TableCell>
                      <TableCell sx={{ fontSize: '0.875rem' }}>
                        {link.campaign_name || '-'}
                      </TableCell>
                      <TableCell sx={{ fontSize: '0.875rem', fontWeight: 500 }}>
                        {link.total_clicks || 0}
                      </TableCell>
                      <TableCell sx={{ fontSize: '0.875rem', fontWeight: 500 }}>
                        {link.total_conversions || 0}
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={link.is_active ? '活跃' : '停用'}
                          color={link.is_active ? 'success' : 'default'}
                          size="small"
                          sx={{ fontSize: '0.75rem' }}
                        />
                      </TableCell>
                      <TableCell sx={{ fontSize: '0.875rem' }}>
                        {new Date(link.created_at).toLocaleDateString('zh-CN')}
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', gap: 0.5 }}>
                          <IconButton
                            size="small"
                            sx={{ p: 0.5 }}
                            onClick={() => handleEditLink(link)}
                            title="编辑链接"
                          >
                            <Edit sx={{ fontSize: 16 }} />
                          </IconButton>
                          <IconButton
                            size="small"
                            sx={{ p: 0.5 }}
                            onClick={() => handleToggleLinkStatus(link)}
                            title={link.is_active ? '停用链接' : '启用链接'}
                          >
                            {link.is_active ? <Pause sx={{ fontSize: 16 }} /> : <PlayArrow sx={{ fontSize: 16 }} />}
                          </IconButton>
                          <IconButton
                            size="small"
                            sx={{ p: 0.5, color: 'error.main' }}
                            onClick={() => handleDeleteLink(link)}
                            title="删除链接"
                          >
                            <Delete sx={{ fontSize: 16 }} />
                          </IconButton>
                        </Box>
                      </TableCell>
                    </TableRow>
                  )))}
                </TableBody>
              </Table>

              {/* 分页 */}
              <Box sx={{
                p: 3,
                borderTop: '1px solid rgba(0,0,0,0.08)',
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                backgroundColor: '#fafafa'
              }}>
                <Typography variant="body2" sx={{ color: '#666' }}>
                  显示 {((pagination.page - 1) * pagination.size) + 1}-{Math.min(pagination.page * pagination.size, pagination.total)} 条，共 {pagination.total} 条记录
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Button
                    variant="outlined"
                    size="small"
                    disabled={pagination.page <= 1}
                    sx={{ minWidth: 80 }}
                    onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}
                  >
                    上一页
                  </Button>
                  <Typography variant="body2" sx={{ color: '#666' }}>
                    第 {pagination.page} 页，共 {Math.ceil(pagination.total / pagination.size)} 页
                  </Typography>
                  <Button
                    variant="outlined"
                    size="small"
                    disabled={pagination.page >= Math.ceil(pagination.total / pagination.size)}
                    sx={{ minWidth: 80 }}
                    onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}
                  >
                    下一页
                  </Button>
                </Box>
              </Box>
            </TableContainer>
          </Box>

          {/* 编辑推广链接对话框 */}
          <Dialog
            open={editDialogOpen}
            onClose={() => setEditDialogOpen(false)}
            maxWidth="md"
            fullWidth
          >
            <DialogTitle>编辑推广链接</DialogTitle>
            <DialogContent>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3, pt: 2 }}>
                <TextField
                  fullWidth
                  label="链接名称"
                  variant="outlined"
                  value={editForm.link_name}
                  onChange={(e) => setEditForm(prev => ({ ...prev, link_name: e.target.value }))}
                />
                <FormControl fullWidth>
                  <InputLabel>推广页面</InputLabel>
                  <Select
                    label="推广页面"
                    value={editForm.target_page}
                    onChange={(e) => setEditForm(prev => ({ ...prev, target_page: e.target.value }))}
                  >
                    <MenuItem value="">首页</MenuItem>
                    <MenuItem value="auth/register">注册页面</MenuItem>
                  </Select>
                </FormControl>
                <TextField
                  fullWidth
                  label="活动名称"
                  variant="outlined"
                  value={editForm.campaign_name}
                  onChange={(e) => setEditForm(prev => ({ ...prev, campaign_name: e.target.value }))}
                />
                <TextField
                  fullWidth
                  label="备注信息"
                  variant="outlined"
                  multiline
                  rows={3}
                  value={editForm.description}
                  onChange={(e) => setEditForm(prev => ({ ...prev, description: e.target.value }))}
                />
              </Box>
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setEditDialogOpen(false)}>
                取消
              </Button>
              <Button
                variant="contained"
                onClick={updateReferralLink}
                disabled={loading || !editForm.link_name.trim()}
              >
                {loading ? '保存中...' : '保存'}
              </Button>
            </DialogActions>
          </Dialog>
        </Box>
      );


    }
    return null;
  };

  // 渲染佣金管理页面
  const renderCommissionContent = () => {

    if (currentPage === 'commission/withdraw') {
      return (
        <Box sx={{ width: '100%', p: 3, backgroundColor: 'white' }}>
          {/* 页面标题区域 - 白色背景带圆角 */}
          <Box sx={{
            backgroundColor: '#fff',
            borderRadius: 0,
            border: '1px solid #e5e7eb',
            p: 4,
            mb: 3,
            border: '1px solid #e5e7eb'
          }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <MonetizationOn sx={{ fontSize: 32, color: '#1a1a1a' }} />
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 700, color: '#1a1a1a', mb: 0.5 }}>
                    佣金管理
                  </Typography>
                </Box>
              </Box>

            </Box>
          </Box>
          {/* 可提现金额卡片 */}
            <Box sx={{
              borderRadius: 0,
              mb: 3,
              backgroundColor: '#fff',
              border: '2px solid #1a1a1a',
              boxShadow: '0 4px 20px rgba(156, 39, 176, 0.1)'
            }}>
              <Box sx={{ p: 4 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Box>
                    <Typography variant="h6" sx={{ mb: 1, color: '#1a1a1a', fontWeight: 600 }}>
                      可提现金额
                    </Typography>
                    <Typography variant="h2" sx={{ fontWeight: 700, mb: 2, color: '#1a1a1a' }}>
                      ¥{earningsData.available_balance.toFixed(2)}
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 4 }}>
                      <Box>
                        <Typography variant="body2" sx={{ color: '#666' }}>待结算：</Typography>
                        <Typography variant="h6" sx={{ color: '#f57c00', fontWeight: 600 }}>¥{earningsData.pending_balance.toFixed(2)}</Typography>
                      </Box>
                      <Box>
                        <Typography variant="body2" sx={{ color: '#666' }}>提现手续费：</Typography>
                        <Typography variant="h6" sx={{ color: '#666', fontWeight: 600 }}>{(earningsData.withdraw_fee_rate * 100).toFixed(1)}%</Typography>
                      </Box>
                    </Box>
                  </Box>
                  <Button
                    variant="contained"
                    size="large"
                    onClick={() => setWithdrawDialogOpen(true)}
                    sx={{
                      backgroundColor: '#1a1a1a',
                      color: '#fff',
                      '&:hover': { backgroundColor: '#2c2c2c' },
                      px: 4,
                      py: 1.5,
                      fontSize: '1.1rem',
                      fontWeight: 600
                    }}
                  >
                    申请结算
                  </Button>
                </Box>
              </Box>
            </Box>

          {/* 佣金记录 */}
          <Box sx={{ borderRadius: 2, border: '1px solid #e5e7eb', backgroundColor: '#fff' }}>
            <Box sx={{ p: 0 }}>
              {/* 筛选标签 */}
              <Box sx={{ p: 3, borderBottom: '1px solid #e0e0e0' }}>
                <Typography variant="h6" sx={{ fontWeight: 600, color: '#1a1a1a', mb: 2 }}>
                  佣金记录
                </Typography>
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Chip
                    label="全部"
                    variant={withdrawFilters.status === '' ? "filled" : "outlined"}
                    onClick={() => setWithdrawFilters(prev => ({ ...prev, status: '' }))}
                    sx={{
                      backgroundColor: withdrawFilters.status === '' ? '#1a1a1a' : 'transparent',
                      color: withdrawFilters.status === '' ? '#fff' : '#666',
                      cursor: 'pointer'
                    }}
                  />
                  <Chip
                    label="待结算"
                    variant={withdrawFilters.status === 'pending' ? "filled" : "outlined"}
                    onClick={() => setWithdrawFilters(prev => ({ ...prev, status: 'pending' }))}
                    sx={{
                      backgroundColor: withdrawFilters.status === 'pending' ? '#1a1a1a' : 'transparent',
                      color: withdrawFilters.status === 'pending' ? '#fff' : '#666',
                      cursor: 'pointer'
                    }}
                  />
                  <Chip
                    label="已结算"
                    variant={withdrawFilters.status === 'settled' ? "filled" : "outlined"}
                    onClick={() => setWithdrawFilters(prev => ({ ...prev, status: 'settled' }))}
                    sx={{
                      backgroundColor: withdrawFilters.status === 'settled' ? '#1a1a1a' : 'transparent',
                      color: withdrawFilters.status === 'settled' ? '#fff' : '#666',
                      cursor: 'pointer'
                    }}
                  />
                  <Chip
                    label="已取消"
                    variant={withdrawFilters.status === 'cancelled' ? "filled" : "outlined"}
                    onClick={() => setWithdrawFilters(prev => ({ ...prev, status: 'cancelled' }))}
                    sx={{
                      backgroundColor: withdrawFilters.status === 'cancelled' ? '#1a1a1a' : 'transparent',
                      color: withdrawFilters.status === 'cancelled' ? '#fff' : '#666',
                      cursor: 'pointer'
                    }}
                  />
                </Box>
              </Box>

              {/* 佣金记录列表 */}
              <Box>
                {withdrawLoading ? (
                  <Box sx={{ p: 4, textAlign: 'center' }}>
                    <LinearProgress sx={{ mb: 2 }} />
                    <Typography variant="body2" color="text.secondary">
                      正在加载佣金记录...
                    </Typography>
                  </Box>
                ) : withdrawRecords.length === 0 ? (
                  <Box sx={{ p: 4, textAlign: 'center' }}>
                    <MonetizationOn sx={{ fontSize: 48, color: '#ccc', mb: 2 }} />
                    <Typography variant="h6" color="text.secondary" sx={{ mb: 1 }}>
                      暂无佣金记录
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {withdrawFilters.status ? '没有符合筛选条件的佣金记录' : '还没有佣金记录，快去推广客户吧！'}
                    </Typography>
                  </Box>
                ) : (
                  <>
                    {withdrawRecords.map((record, index) => (
                  <Box
                    key={record.id}
                    sx={{
                      p: 3,
                      borderBottom: index === withdrawRecords.length - 1 ? 'none' : '1px solid #e0e0e0',
                      '&:hover': { backgroundColor: '#f8f9fa' }
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 4, flex: 1 }}>
                        <Box>
                          <Typography variant="h6" sx={{ fontWeight: 600, color: '#1a1a1a' }}>
                            ¥{record.commission_amount?.toFixed(2) || '0.00'}
                          </Typography>
                          <Typography variant="caption" sx={{ color: '#666' }}>
                            {record.commission_date ? new Date(record.commission_date).toLocaleString() : ''}
                          </Typography>
                          <Typography variant="caption" sx={{ color: '#666', display: 'block' }}>
                            订单号：{record.order_no || record.id}
                          </Typography>
                        </Box>
                        <Box>
                          <Typography variant="body2" sx={{ color: '#333', fontWeight: 500 }}>
                            {record.customer_name || '未知客户'}
                          </Typography>
                          <Typography variant="caption" sx={{ color: '#666' }}>
                            {record.product_name || record.order_type === 'subscription' ? '套餐订单' : '内容服务'}
                          </Typography>
                        </Box>
                        <Box>
                          <Chip
                            label={
                              record.status === 'settled' ? '已结算' :
                              record.status === 'pending' ? '待结算' :
                              record.status === 'cancelled' ? '已取消' : '未知状态'
                            }
                            size="small"
                            sx={{
                              backgroundColor:
                                record.status === 'settled' ? '#e8f5e8' :
                                record.status === 'pending' ? '#fff3e0' :
                                record.status === 'cancelled' ? '#ffebee' : '#f5f5f5',
                              color:
                                record.status === 'settled' ? '#2e7d32' :
                                record.status === 'pending' ? '#f57c00' :
                                record.status === 'cancelled' ? '#c62828' : '#666',
                              fontWeight: 600
                            }}
                          />
                        </Box>
                        <Box sx={{ flex: 1 }}>
                          {record.settled_at && (
                            <Typography variant="body2" sx={{ color: '#666' }}>
                              结算时间：{new Date(record.settled_at).toLocaleString()}
                            </Typography>
                          )}
                        </Box>
                      </Box>
                      <Box>
                        <Button variant="outlined" size="small">
                          查看详情
                        </Button>
                      </Box>
                    </Box>
                  </Box>
                    ))}
                  </>
                )}
              </Box>
            </Box>
          </Box>
        </Box>
      );
    }

    return null;
  };

  // 渲染客户管理页面
  const renderCustomersContent = () => {
    if (currentPage === 'customers/list') {
      // 使用从API获取的客户数据，而不是硬编码数据
      const customersData = customers;

      return (
        <Box sx={{ width: '100%', p: 3, backgroundColor: 'white' }}>
          {/* 页面标题和操作按钮区域 - 白色背景带圆角 */}
          <Box sx={{
            backgroundColor: '#fff',
            borderRadius: 0,
            border: '1px solid #e5e7eb',
            p: 4,
            mb: 3,
            border: '1px solid #e5e7eb'
          }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <People sx={{ fontSize: 32, color: '#1a1a1a' }} />
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 700, color: '#1a1a1a', mb: 0.5 }}>
                    客户列表
                  </Typography>
                </Box>
              </Box>

            </Box>
          </Box>


          <Box sx={{ borderRadius: 2, border: '1px solid #e5e7eb', backgroundColor: '#fff' }}>
            <Box sx={{ p: 0 }}>
              {/* 筛选器区域 */}
              <Box sx={{ p: 3, borderBottom: '1px solid #e0e0e0', display: 'flex', gap: 2, flexWrap: 'wrap', alignItems: 'center' }}>
                <TextField
                  placeholder="搜索客户姓名、邮箱或公司"
                  variant="outlined"
                  size="small"
                  value={customersFilters.search}
                  onChange={(e) => setCustomersFilters(prev => ({ ...prev, search: e.target.value }))}
                  sx={{ minWidth: 250 }}
                  InputProps={{
                    startAdornment: <Search sx={{ color: '#666', mr: 1 }} />
                  }}
                />

                <FormControl size="small" sx={{ minWidth: 120 }}>
                  <InputLabel>状态筛选</InputLabel>
                  <Select
                    value={customersFilters.status}
                    label="状态筛选"
                    onChange={(e) => setCustomersFilters(prev => ({ ...prev, status: e.target.value }))}
                  >
                    <MenuItem value="">全部状态</MenuItem>
                    <MenuItem value="active">活跃</MenuItem>
                    <MenuItem value="inactive">未激活</MenuItem>
                  </Select>
                </FormControl>

                <Button
                  variant="outlined"
                  onClick={() => setCustomersFilters({ status: '', search: '' })}
                  sx={{ ml: 'auto' }}
                >
                  重置筛选
                </Button>
              </Box>

              {/* 可滚动的表格容器 */}
              <Box sx={{
                overflowX: 'auto',
                '&::-webkit-scrollbar': {
                  height: 8,
                },
                '&::-webkit-scrollbar-track': {
                  backgroundColor: '#f1f1f1',
                  borderRadius: 0,
                },
                '&::-webkit-scrollbar-thumb': {
                  backgroundColor: '#c1c1c1',
                  borderRadius: 0,
                  '&:hover': {
                    backgroundColor: '#a8a8a8',
                  },
                },
              }}>
                {/* 表格头部 */}
                <Box sx={{ p: 2, backgroundColor: '#f8f9fa', borderBottom: '1px solid #e0e0e0' }}>
                  <Box sx={{
                    display: 'flex',
                    alignItems: 'center',
                    minWidth: '1210px', // 设置最小宽度确保所有列都能显示
                    gap: 1
                  }}>
                    <Box sx={{ width: '350px', flexShrink: 0 }}>
                      <Typography variant="body2" sx={{ fontWeight: 600, color: '#666' }}>
                        客户信息
                      </Typography>
                    </Box>
                    <Box sx={{ width: '320px', flexShrink: 0 }}>
                      <Typography variant="body2" sx={{ fontWeight: 600, color: '#666' }}>
                        联系方式
                      </Typography>
                    </Box>
                    <Box sx={{ width: '110px', flexShrink: 0 }}>
                      <Typography variant="body2" sx={{ fontWeight: 600, color: '#666' }}>
                        状态
                      </Typography>
                    </Box>
                    <Box sx={{ width: '130px', flexShrink: 0 }}>
                      <Typography variant="body2" sx={{ fontWeight: 600, color: '#666' }}>
                        注册时间
                      </Typography>
                    </Box>
                    <Box sx={{ width: '150px', flexShrink: 0 }}>
                      <Typography variant="body2" sx={{ fontWeight: 600, color: '#666' }}>
                        累计消费
                      </Typography>
                    </Box>
                    <Box sx={{ width: '150px', flexShrink: 0 }}>
                      <Typography variant="body2" sx={{ fontWeight: 600, color: '#666' }}>
                        操作
                      </Typography>
                    </Box>
                  </Box>
                </Box>

                {/* 客户数据行 */}
                <Box>
                {customersLoading ? (
                  <Box sx={{ p: 4, textAlign: 'center' }}>
                    <LinearProgress sx={{ mb: 2 }} />
                    <Typography variant="body2" color="text.secondary">
                      正在加载客户数据...
                    </Typography>
                  </Box>
                ) : customersData.length === 0 ? (
                  <Box sx={{ p: 4, textAlign: 'center' }}>
                    <People sx={{ fontSize: 48, color: '#ccc', mb: 2 }} />
                    <Typography variant="h6" color="text.secondary" sx={{ mb: 1 }}>
                      暂无客户数据
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {customersFilters.search || customersFilters.status
                        ? '没有符合筛选条件的客户'
                        : '还没有推荐的客户，快去分享推广链接吧！'}
                    </Typography>
                  </Box>
                ) : (
                  customersData.map((customer, index) => (
                  <Box
                    key={customer.id}
                    sx={{
                      p: 2,
                      borderBottom: index === customersData.length - 1 ? 'none' : '1px solid #e0e0e0',
                      '&:hover': { backgroundColor: '#f8f9fa' }
                    }}
                  >
                    <Box sx={{
                      display: 'flex',
                      alignItems: 'center',
                      minWidth: '1210px', // 与表头保持一致的最小宽度
                      gap: 1
                    }}>
                      <Box sx={{ width: '350px', flexShrink: 0 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                          <Avatar
                            sx={{
                              width: 40,
                              height: 40,
                              backgroundColor: '#1a1a1a',
                              fontSize: '1rem',
                              fontWeight: 600
                            }}
                          >
                            {customer.name.charAt(0)}
                          </Avatar>
                          <Box sx={{ minWidth: 0, flex: 1 }}>
                            <Typography variant="body2" sx={{ fontWeight: 600, color: '#1a1a1a' }}>
                              {customer.name}
                            </Typography>
                            <Typography variant="caption" sx={{ color: '#666' }}>
                              {customer.company}
                            </Typography>
                          </Box>
                        </Box>
                      </Box>
                      <Box sx={{ width: '320px', flexShrink: 0 }}>
                        <Typography variant="body2" sx={{ color: '#1a1a1a', fontSize: '0.875rem' }}>
                          {customer.email}
                        </Typography>
                        <Typography variant="caption" sx={{ color: '#666' }}>
                          {customer.phone}
                        </Typography>
                      </Box>
                      <Box sx={{ width: '110px', flexShrink: 0 }}>
                        <Chip
                          label={customer.status === 'active' ? '活跃' : customer.status === 'trial' ? '试用' : '未激活'}
                          size="small"
                          sx={{
                            backgroundColor: customer.status === 'active' ? '#e8f5e8' :
                                           customer.status === 'trial' ? '#fff3e0' : '#ffebee',
                            color: customer.status === 'active' ? '#2e7d32' :
                                   customer.status === 'trial' ? '#f57c00' : '#c62828',
                            fontWeight: 600,
                            fontSize: '0.75rem'
                          }}
                        />
                      </Box>
                      <Box sx={{ width: '130px', flexShrink: 0 }}>
                        <Typography variant="body2" sx={{ color: '#666' }}>
                          {customer.register_date}
                        </Typography>
                      </Box>
                      <Box sx={{ width: '150px', flexShrink: 0 }}>
                        <Typography variant="body2" sx={{ color: '#1a1a1a', fontWeight: 600 }}>
                          ¥{customer.total_spent?.toFixed(2) || '0.00'}
                        </Typography>
                      </Box>
                      <Box sx={{ width: '150px', flexShrink: 0 }}>
                        <Box sx={{ display: 'flex', gap: 0.5, justifyContent: 'flex-start' }}>
                          <IconButton
                            size="small"
                            sx={{ color: '#1a1a1a', p: 0.5 }}
                            onClick={() => handleViewCustomer(customer)}
                            title="查看客户详情"
                          >
                            <Visibility sx={{ fontSize: 18 }} />
                          </IconButton>
                        </Box>
                      </Box>
                    </Box>
                  </Box>
                  ))
                )}
              </Box>
              </Box>

              {/* 分页 */}
              <Box sx={{ p: 3, borderTop: '1px solid #e0e0e0', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography variant="body2" sx={{ color: '#666' }}>
                  显示 1-5 条，共 5 条记录
                </Typography>
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Button variant="outlined" size="small" disabled>
                    上一页
                  </Button>
                  <Button
                    variant="contained"
                    size="small"
                    sx={{
                      backgroundColor: '#1a1a1a',
                      '&:hover': { backgroundColor: '#2c2c2c' },
                      minWidth: '32px'
                    }}
                  >
                    1
                  </Button>
                  <Button variant="outlined" size="small" disabled>
                    下一页
                  </Button>
                </Box>
              </Box>
            </Box>
          </Box>
        </Box>
      );
    }
    return null;
  };


  // 渲染培训中心页面 - 功能已移除，返回空
  const renderTrainingContent = () => {
    if (currentPage === 'training/courses') {
      const courses = [
        {
          id: 1,
          title: '代理商入门指南',
          description: '了解代理商基础知识，掌握推广技巧和方法',
          duration: '45分钟',
          level: '初级',
          progress: 100,
          status: 'completed',
          instructor: '张老师',
          rating: 4.8,
          students: 1250
        },
        {
          id: 2,
          title: '高效推广策略',
          description: '学习如何制定有效的推广策略，提高转化率',
          duration: '60分钟',
          level: '中级',
          progress: 65,
          status: 'in-progress',
          instructor: '李老师',
          rating: 4.9,
          students: 890
        },
        {
          id: 3,
          title: '客户关系管理',
          description: '掌握客户维护技巧，提升客户满意度和忠诚度',
          duration: '50分钟',
          level: '中级',
          progress: 0,
          status: 'not-started',
          instructor: '王老师',
          rating: 4.7,
          students: 650
        },
        {
          id: 4,
          title: '数据分析与优化',
          description: '学会分析推广数据，优化推广效果',
          duration: '75分钟',
          level: '高级',
          progress: 0,
          status: 'not-started',
          instructor: '赵老师',
          rating: 4.9,
          students: 420
        }
      ];

      return (
        <Box sx={{
          borderRadius: 0,
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.08)',
          border: 'none',
          maxHeight: 'calc(100vh - 150px)',
          overflow: 'hidden',
        }}>
          <Box sx={{
            p: 4,
            maxHeight: 'calc(100vh - 150px)',
            overflowY: 'auto',
            '&::-webkit-scrollbar': {
              width: '8px',
            },
            '&::-webkit-scrollbar-track': {
              backgroundColor: 'rgba(0,0,0,0.1)',
              borderRadius: 0,
            },
            '&::-webkit-scrollbar-thumb': {
              backgroundColor: 'rgba(0,0,0,0.3)',
              borderRadius: 0,
              '&:hover': {
                backgroundColor: 'rgba(0,0,0,0.5)',
              },
            },
          }}>
            <Grid container spacing={4}>
              {/* 培训概览 */}
              <Grid item xs={12}>
                <Box sx={{ mb: 4 }}>
                  <Typography variant="h4" sx={{ fontWeight: 800, color: '#1a1a1a', mb: 2 }}>
                    培训课程中心
                  </Typography>
                  <Typography variant="body1" sx={{ color: '#666', fontSize: '1rem', mb: 3 }}>
                    提升您的推广技能，成为优秀的代理商
                  </Typography>

                  {/* 学习进度统计 */}
                  <Grid container spacing={3}>
                    <Grid item xs={12} sm={4}>
                      <Box sx={{ borderRadius: 0, p: 3, background: 'white', border: '1px solid #e5e7eb', color: '#1a1a1a' }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                          <Box>
                            <Typography variant="h4" sx={{ fontWeight: 800, mb: 1 }}>4</Typography>
                            <Typography variant="body2" sx={{ opacity: 0.9 }}>总课程数</Typography>
                          </Box>
                          <School sx={{ fontSize: 40, opacity: 0.8 }} />
                        </Box>
                      </Box>
                    </Grid>
                    <Grid item xs={12} sm={4}>
                      <Box sx={{ borderRadius: 0, p: 3, background: 'white', border: '1px solid #e5e7eb', color: '#1a1a1a' }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                          <Box>
                            <Typography variant="h4" sx={{ fontWeight: 800, mb: 1 }}>1</Typography>
                            <Typography variant="body2" sx={{ opacity: 0.9 }}>已完成</Typography>
                          </Box>
                          <Assessment sx={{ fontSize: 40, opacity: 0.8 }} />
                        </Box>
                      </Box>
                    </Grid>
                    <Grid item xs={12} sm={4}>
                      <Box sx={{ borderRadius: 0, p: 3, background: 'white', border: '1px solid #e5e7eb', color: '#1a1a1a' }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                          <Box>
                            <Typography variant="h4" sx={{ fontWeight: 800, mb: 1 }}>41%</Typography>
                            <Typography variant="body2" sx={{ opacity: 0.9 }}>总进度</Typography>
                          </Box>
                          <TrendingUp sx={{ fontSize: 40, opacity: 0.8 }} />
                        </Box>
                      </Box>
                    </Grid>
                  </Grid>
                </Box>
              </Grid>

              {/* 课程列表 */}
              <Grid item xs={12}>
                <Grid container spacing={3}>
                  {courses.map((course) => (
                    <Grid item xs={12} md={6} key={course.id}>
                      <Box sx={{
                        borderRadius: 1,
                        height: '100%',
                        transition: 'all 0.3s ease',
                        border: '1px solid rgba(0,0,0,0.08)',
                        '&:hover': {
                          transform: 'translateY(-4px)',
                          boxShadow: '0 12px 32px rgba(0,0,0,0.15)',
                          borderColor: '#1a1a1a'
                        }
                      }}>
                        <Box sx={{ p: 4 }}>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                            <Chip
                              label={course.level}
                              size="small"
                              sx={{
                                backgroundColor: course.level === '初级' ? '#e8f5e8' :
                                               course.level === '中级' ? '#fff3e0' : '#ffebee',
                                color: course.level === '初级' ? '#2e7d32' :
                                       course.level === '中级' ? '#f57c00' : '#c62828',
                                fontWeight: 600
                              }}
                            />
                            <Chip
                              label={course.status === 'completed' ? '已完成' :
                                     course.status === 'in-progress' ? '学习中' : '未开始'}
                              size="small"
                              sx={{
                                backgroundColor: course.status === 'completed' ? '#e8f5e8' :
                                               course.status === 'in-progress' ? '#f3e5f5' : '#f5f5f5',
                                color: course.status === 'completed' ? '#2e7d32' :
                                       course.status === 'in-progress' ? '#1565c0' : '#666',
                                fontWeight: 600
                              }}
                            />
                          </Box>

                          <Typography variant="h6" sx={{ fontWeight: 700, color: '#1a1a1a', mb: 2 }}>
                            {course.title}
                          </Typography>

                          <Typography variant="body2" sx={{ color: '#666', mb: 3, lineHeight: 1.6 }}>
                            {course.description}
                          </Typography>

                          {/* 课程信息 */}
                          <Box sx={{ display: 'flex', gap: 3, mb: 3, fontSize: '0.875rem', color: '#666' }}>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                              <span>⏱️</span>
                              <span>{course.duration}</span>
                            </Box>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                              <span>👨‍🏫</span>
                              <span>{course.instructor}</span>
                            </Box>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                              <span>⭐</span>
                              <span>{course.rating}</span>
                            </Box>
                          </Box>

                          {/* 学习进度 */}
                          {course.progress > 0 && (
                            <Box sx={{ mb: 3 }}>
                              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                                <Typography variant="body2" sx={{ color: '#666', fontSize: '0.875rem' }}>
                                  学习进度
                                </Typography>
                                <Typography variant="body2" sx={{ color: '#1a1a1a', fontWeight: 600, fontSize: '0.875rem' }}>
                                  {course.progress}%
                                </Typography>
                              </Box>
                              <LinearProgress
                                variant="determinate"
                                value={course.progress}
                                sx={{
                                  height: 6,
                                  borderRadius: 1,
                                  backgroundColor: '#ffffff',
                                  '& .MuiLinearProgress-bar': {
                                    backgroundColor: '#1a1a1a',
                                    borderRadius: 3
                                  }
                                }}
                              />
                            </Box>
                          )}

                          <Box sx={{ display: 'flex', gap: 2 }}>
                            <Button
                              variant={course.status === 'not-started' ? 'contained' : 'outlined'}
                              fullWidth
                              sx={{
                                borderRadius: 1,
                                py: 1.5,
                                fontWeight: 600,
                                backgroundColor: course.status === 'not-started' ? '#1a1a1a' : 'transparent',
                                borderColor: '#1a1a1a',
                                color: course.status === 'not-started' ? '#fff' : '#1a1a1a',
                                '&:hover': {
                                  backgroundColor: course.status === 'not-started' ? '#2c2c2c' : 'rgba(156, 39, 176, 0.08)'
                                }
                              }}
                            >
                              {course.status === 'completed' ? '重新学习' :
                               course.status === 'in-progress' ? '继续学习' : '开始学习'}
                            </Button>
                          </Box>
                        </Box>
                      </Box>
                    </Grid>
                  ))}
                </Grid>
              </Grid>
            </Grid>
          </Box>
        </Box>
      );
    } else if (currentPage === 'training/resources') {
      const resources = [
        {
          id: 1,
          title: '代理商推广手册',
          description: '详细的推广指南和最佳实践',
          type: 'PDF',
          size: '2.5MB',
          downloads: 1250,
          category: '指南'
        },
        {
          id: 2,
          title: '客户沟通话术模板',
          description: '专业的客户沟通话术和技巧',
          type: 'DOCX',
          size: '1.2MB',
          downloads: 890,
          category: '模板'
        },
        {
          id: 3,
          title: '推广数据分析表格',
          description: 'Excel模板，帮助分析推广效果',
          type: 'XLSX',
          size: '850KB',
          downloads: 650,
          category: '工具'
        },
        {
          id: 4,
          title: '社交媒体推广视频教程',
          description: '如何在社交媒体上有效推广',
          type: 'MP4',
          size: '125MB',
          downloads: 420,
          category: '视频'
        }
      ];

      return (
        <Box sx={{
          borderRadius: 0,
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.08)',
          border: 'none',
          maxHeight: 'calc(100vh - 150px)',
          overflow: 'hidden',
        }}>
          <Box sx={{
            p: 4,
            maxHeight: 'calc(100vh - 150px)',
            overflowY: 'auto',
            '&::-webkit-scrollbar': {
              width: '8px',
            },
            '&::-webkit-scrollbar-track': {
              backgroundColor: 'rgba(0,0,0,0.1)',
              borderRadius: 0,
            },
            '&::-webkit-scrollbar-thumb': {
              backgroundColor: 'rgba(0,0,0,0.3)',
              borderRadius: 0,
              '&:hover': {
                backgroundColor: 'rgba(0,0,0,0.5)',
              },
            },
          }}>
            <Grid container spacing={4}>
              <Grid item xs={12}>
                <Box sx={{ mb: 4 }}>
                  <Typography variant="h4" sx={{ fontWeight: 800, color: '#1a1a1a', mb: 2 }}>
                    学习资源库
                  </Typography>
                  <Typography variant="body1" sx={{ color: '#666', fontSize: '1rem' }}>
                    下载各种学习资料，提升您的专业技能
                  </Typography>
                </Box>
              </Grid>

              <Grid item xs={12}>
                <Grid container spacing={3}>
                  {resources.map((resource) => (
                    <Grid item xs={12} sm={6} md={4} key={resource.id}>
                      <Box sx={{
                        borderRadius: 1,
                        height: '100%',
                        transition: 'all 0.3s ease',
                        border: '1px solid rgba(0,0,0,0.08)',
                        '&:hover': {
                          transform: 'translateY(-4px)',
                          boxShadow: '0 12px 32px rgba(0,0,0,0.15)',
                          borderColor: '#1a1a1a'
                        }
                      }}>
                        <Box sx={{ p: 3 }}>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                            <Chip
                              label={resource.category}
                              size="small"
                              sx={{
                                backgroundColor: '#f3e5f5',
                                color: '#1a1a1a',
                                fontWeight: 600
                              }}
                            />
                            <Typography variant="caption" sx={{ color: '#666', fontWeight: 600 }}>
                              {resource.type}
                            </Typography>
                          </Box>

                          <Typography variant="h6" sx={{ fontWeight: 700, color: '#1a1a1a', mb: 2 }}>
                            {resource.title}
                          </Typography>

                          <Typography variant="body2" sx={{ color: '#666', mb: 3, lineHeight: 1.6 }}>
                            {resource.description}
                          </Typography>

                          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3, fontSize: '0.875rem', color: '#666' }}>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                              <span>📁</span>
                              <span>{resource.size}</span>
                            </Box>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                              <span>⬇️</span>
                              <span>{resource.downloads}</span>
                            </Box>
                          </Box>

                          <Button
                            variant="contained"
                            fullWidth
                            sx={{
                              borderRadius: 1,
                              py: 1.5,
                              fontWeight: 600,
                              backgroundColor: '#1a1a1a',
                              '&:hover': { backgroundColor: '#374151' }
                            }}
                          >
                            下载资源
                          </Button>
                        </Box>
                      </Box>
                    </Grid>
                  ))}
                </Grid>
              </Grid>
            </Grid>
          </Box>
        </Box>
      );
    }
    return null;
  };

  // 渲染账户设置页面
  const renderAccountContent = () => {
    if (currentPage === 'account/profile') {
      return (
        <Box sx={{
          borderRadius: 0,
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.08)',
          border: 'none',
          maxHeight: 'calc(100vh - 150px)',
          overflow: 'hidden',
        }}>
          <Box sx={{
            p: 4,
            maxHeight: 'calc(100vh - 150px)',
            overflowY: 'auto',
            '&::-webkit-scrollbar': {
              width: '8px',
            },
            '&::-webkit-scrollbar-track': {
              backgroundColor: 'rgba(0,0,0,0.1)',
              borderRadius: 0,
            },
            '&::-webkit-scrollbar-thumb': {
              backgroundColor: 'rgba(0,0,0,0.3)',
              borderRadius: 0,
              '&:hover': {
                backgroundColor: 'rgba(0,0,0,0.5)',
              },
            },
          }}>
            <Grid container spacing={4}>
              <Grid item xs={12}>
                <Typography variant="h4" sx={{ fontWeight: 800, color: '#1a1a1a', mb: 2 }}>
                  个人资料
                </Typography>
                <Typography variant="body1" sx={{ color: '#666', fontSize: '1rem', mb: 4 }}>
                  管理您的个人信息和账户设置
                </Typography>
              </Grid>

              {/* 头像和基本信息 */}
              <Grid item xs={12} md={4}>
                <Box sx={{ borderRadius: 0, border: '1px solid rgba(0,0,0,0.08)', textAlign: 'center' }}>
                  <Box sx={{ p: 4 }}>
                    <Avatar sx={{
                      width: 120,
                      height: 120,
                      mx: 'auto',
                      mb: 3,
                      backgroundColor: '#1a1a1a',
                      fontSize: '3rem',
                      fontWeight: 700
                    }}>
                      张
                    </Avatar>
                    <Typography variant="h5" sx={{ fontWeight: 700, color: '#1a1a1a', mb: 1 }}>
                      张代理
                    </Typography>
                    <Typography variant="body2" sx={{ color: '#666', mb: 2 }}>
                      高级代理商
                    </Typography>
                    <Chip
                      label="VIP会员"
                      sx={{
                        backgroundColor: '#f39c12',
                        color: '#fff',
                        fontWeight: 600,
                        mb: 3
                      }}
                    />
                    <Button variant="outlined" fullWidth sx={{
                      borderColor: '#1a1a1a',
                      color: '#1a1a1a',
                      borderRadius: 0,
                      '&:hover': {
                        borderColor: '#374151',
                        backgroundColor: 'rgba(156, 39, 176, 0.08)'
                      }
                    }}>
                      更换头像
                    </Button>
                  </Box>
                </Box>
              </Grid>

              {/* 个人信息表单 */}
              <Grid item xs={12} md={8}>
                <Box sx={{ borderRadius: 0, border: '1px solid rgba(0,0,0,0.08)' }}>
                  <Box sx={{ p: 4 }}>
                    <Typography variant="h6" sx={{ fontWeight: 700, color: '#1a1a1a', mb: 3 }}>
                      基本信息
                    </Typography>

                    <Grid container spacing={3}>
                      <Grid item xs={12} sm={6}>
                        <TextField
                          fullWidth
                          label="姓名"
                          defaultValue="张代理"
                          variant="outlined"
                          sx={{
                            '& .MuiOutlinedInput-root': {
                              borderRadius: 1,
                              '&:hover .MuiOutlinedInput-notchedOutline': {
                                borderColor: '#1a1a1a'
                              },
                              '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                                borderColor: '#1a1a1a'
                              }
                            }
                          }}
                        />
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <TextField
                          fullWidth
                          label="昵称"
                          defaultValue="优秀代理商"
                          variant="outlined"
                          sx={{
                            '& .MuiOutlinedInput-root': {
                              borderRadius: 1,
                              '&:hover .MuiOutlinedInput-notchedOutline': {
                                borderColor: '#1a1a1a'
                              },
                              '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                                borderColor: '#1a1a1a'
                              }
                            }
                          }}
                        />
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <TextField
                          fullWidth
                          label="邮箱"
                          defaultValue="<EMAIL>"
                          variant="outlined"
                          sx={{
                            '& .MuiOutlinedInput-root': {
                              borderRadius: 1,
                              '&:hover .MuiOutlinedInput-notchedOutline': {
                                borderColor: '#1a1a1a'
                              },
                              '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                                borderColor: '#1a1a1a'
                              }
                            }
                          }}
                        />
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <TextField
                          fullWidth
                          label="手机号"
                          defaultValue="138****8888"
                          variant="outlined"
                          sx={{
                            '& .MuiOutlinedInput-root': {
                              borderRadius: 1,
                              '&:hover .MuiOutlinedInput-notchedOutline': {
                                borderColor: '#1a1a1a'
                              },
                              '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                                borderColor: '#1a1a1a'
                              }
                            }
                          }}
                        />
                      </Grid>
                      <Grid item xs={12}>
                        <TextField
                          fullWidth
                          label="公司名称"
                          defaultValue="优秀代理商工作室"
                          variant="outlined"
                          sx={{
                            '& .MuiOutlinedInput-root': {
                              borderRadius: 1,
                              '&:hover .MuiOutlinedInput-notchedOutline': {
                                borderColor: '#1a1a1a'
                              },
                              '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                                borderColor: '#1a1a1a'
                              }
                            }
                          }}
                        />
                      </Grid>
                      <Grid item xs={12}>
                        <TextField
                          fullWidth
                          label="个人简介"
                          multiline
                          rows={4}
                          defaultValue="专业的代理商，致力于为客户提供优质的服务和产品推广。"
                          variant="outlined"
                          sx={{
                            '& .MuiOutlinedInput-root': {
                              borderRadius: 1,
                              '&:hover .MuiOutlinedInput-notchedOutline': {
                                borderColor: '#1a1a1a'
                              },
                              '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                                borderColor: '#1a1a1a'
                              }
                            }
                          }}
                        />
                      </Grid>
                    </Grid>

                    <Box sx={{ mt: 4, display: 'flex', gap: 2 }}>
                      <Button variant="contained" sx={{
                        backgroundColor: '#1a1a1a',
                        borderRadius: 1,
                        px: 4,
                        py: 1.5,
                        '&:hover': { backgroundColor: '#374151' }
                      }}>
                        保存修改
                      </Button>
                      <Button variant="outlined" sx={{
                        borderColor: '#1a1a1a',
                        color: '#1a1a1a',
                        borderRadius: 1,
                        px: 4,
                        py: 1.5,
                        '&:hover': {
                          borderColor: '#374151',
                          backgroundColor: 'rgba(156, 39, 176, 0.08)'
                        }
                      }}>
                        重置
                      </Button>
                    </Box>
                  </Box>
                </Box>
              </Grid>
            </Grid>
          </Box>
        </Box>
      );
    } else if (currentPage === 'account/security') {
      return (
        <Box sx={{
          borderRadius: 0,
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.08)',
          border: 'none',
          maxHeight: 'calc(100vh - 150px)',
          overflow: 'hidden',
        }}>
          <Box sx={{ p: 4 }}>
            <Typography variant="h4" sx={{ fontWeight: 800, color: '#1a1a1a', mb: 2 }}>
              安全设置
            </Typography>
            <Typography variant="body1" sx={{ color: '#666', fontSize: '1rem', mb: 4 }}>
              管理您的账户安全选项
            </Typography>

            <Grid container spacing={4}>
              {/* 密码修改 */}
              <Grid item xs={12} md={6}>
                <Box sx={{ borderRadius: 0, border: '1px solid rgba(0,0,0,0.08)' }}>
                  <Box sx={{ p: 4 }}>
                    <Typography variant="h6" sx={{ fontWeight: 700, color: '#1a1a1a', mb: 3 }}>
                      修改密码
                    </Typography>

                    <Grid container spacing={3}>
                      <Grid item xs={12}>
                        <TextField
                          fullWidth
                          label="当前密码"
                          type="password"
                          variant="outlined"
                          sx={{
                            '& .MuiOutlinedInput-root': {
                              borderRadius: 1,
                              '&:hover .MuiOutlinedInput-notchedOutline': {
                                borderColor: '#1a1a1a'
                              },
                              '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                                borderColor: '#1a1a1a'
                              }
                            }
                          }}
                        />
                      </Grid>
                      <Grid item xs={12}>
                        <TextField
                          fullWidth
                          label="新密码"
                          type="password"
                          variant="outlined"
                          sx={{
                            '& .MuiOutlinedInput-root': {
                              borderRadius: 1,
                              '&:hover .MuiOutlinedInput-notchedOutline': {
                                borderColor: '#1a1a1a'
                              },
                              '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                                borderColor: '#1a1a1a'
                              }
                            }
                          }}
                        />
                      </Grid>
                      <Grid item xs={12}>
                        <TextField
                          fullWidth
                          label="确认新密码"
                          type="password"
                          variant="outlined"
                          sx={{
                            '& .MuiOutlinedInput-root': {
                              borderRadius: 1,
                              '&:hover .MuiOutlinedInput-notchedOutline': {
                                borderColor: '#1a1a1a'
                              },
                              '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                                borderColor: '#1a1a1a'
                              }
                            }
                          }}
                        />
                      </Grid>
                    </Grid>

                    <Button variant="contained" sx={{
                      backgroundColor: '#1a1a1a',
                      borderRadius: 0,
                      px: 3,
                      mt: 3,
                      '&:hover': { backgroundColor: '#374151' }
                    }}>
                      修改密码
                    </Button>
                  </Box>
                </Box>
              </Grid>

              {/* 安全选项 */}
              <Grid item xs={12} md={6}>
                <Box sx={{ borderRadius: 0, border: '1px solid rgba(0,0,0,0.08)' }}>
                  <Box sx={{ p: 4 }}>
                    <Typography variant="h6" sx={{ fontWeight: 700, color: '#1a1a1a', mb: 3 }}>
                      安全选项
                    </Typography>

                    <List sx={{ p: 0 }}>
                      {[
                        { label: '双因素认证', description: '使用手机验证码增强账户安全', enabled: true },
                        { label: '登录通知', description: '新设备登录时发送通知', enabled: true },
                        { label: '异常登录保护', description: '检测异常登录行为并阻止', enabled: false },
                        { label: '定期密码提醒', description: '定期提醒您更新密码', enabled: false }
                      ].map((item, index) => (
                        <ListItem key={index} sx={{ px: 0, py: 2 }}>
                          <ListItemText
                            primary={
                              <Typography sx={{ fontWeight: 600, color: '#1a1a1a', fontSize: '0.95rem' }}>
                                {item.label}
                              </Typography>
                            }
                            secondary={
                              <Typography sx={{ color: '#666', fontSize: '0.875rem', mt: 0.5 }}>
                                {item.description}
                              </Typography>
                            }
                          />
                          <ListItemSecondaryAction>
                            <Switch
                              defaultChecked={item.enabled}
                              sx={{
                                '& .MuiSwitch-switchBase.Mui-checked': {
                                  color: '#1a1a1a',
                                  '&:hover': {
                                    backgroundColor: 'rgba(156, 39, 176, 0.08)',
                                  },
                                },
                                '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                                  backgroundColor: '#1a1a1a',
                                },
                              }}
                            />
                          </ListItemSecondaryAction>
                        </ListItem>
                      ))}
                    </List>
                  </Box>
                </Box>
              </Grid>
            </Grid>
          </Box>
        </Box>
      );
    } else if (currentPage === 'account/preferences') {
      return (
        <Box sx={{
          borderRadius: 0,
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.08)',
          border: 'none',
          maxHeight: 'calc(100vh - 150px)',
          overflow: 'hidden',
        }}>
          <Box sx={{ p: 4 }}>
            <Typography variant="h4" sx={{ fontWeight: 800, color: '#1a1a1a', mb: 2 }}>
              偏好设置
            </Typography>
            <Typography variant="body1" sx={{ color: '#666', fontSize: '1rem', mb: 4 }}>
              配置您的个人偏好和系统设置
            </Typography>

            <Grid container spacing={4}>
              {/* 界面设置 */}
              <Grid item xs={12} md={6}>
                <Box sx={{ borderRadius: 0, border: '1px solid rgba(0,0,0,0.08)' }}>
                  <Box sx={{ p: 4 }}>
                    <Typography variant="h6" sx={{ fontWeight: 700, color: '#1a1a1a', mb: 3 }}>
                      界面设置
                    </Typography>

                    <Grid container spacing={3}>
                      <Grid item xs={12}>
                        <FormControl fullWidth>
                          <InputLabel>主题模式</InputLabel>
                          <Select
                            label="主题模式"
                            defaultValue="light"
                            sx={{
                              borderRadius: 1,
                              '&:hover .MuiOutlinedInput-notchedOutline': {
                                borderColor: '#1a1a1a'
                              },
                              '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                                borderColor: '#1a1a1a'
                              }
                            }}
                          >
                            <MenuItem value="light">浅色模式</MenuItem>
                            <MenuItem value="dark">深色模式</MenuItem>
                            <MenuItem value="auto">跟随系统</MenuItem>
                          </Select>
                        </FormControl>
                      </Grid>
                      <Grid item xs={12}>
                        <FormControl fullWidth>
                          <InputLabel>语言设置</InputLabel>
                          <Select
                            label="语言设置"
                            defaultValue="zh-CN"
                            sx={{
                              borderRadius: 1,
                              '&:hover .MuiOutlinedInput-notchedOutline': {
                                borderColor: '#1a1a1a'
                              },
                              '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                                borderColor: '#1a1a1a'
                              }
                            }}
                          >
                            <MenuItem value="zh-CN">简体中文</MenuItem>
                            <MenuItem value="en-US">English</MenuItem>
                            <MenuItem value="ja-JP">日本語</MenuItem>
                          </Select>
                        </FormControl>
                      </Grid>
                      <Grid item xs={12}>
                        <FormControl fullWidth>
                          <InputLabel>时区设置</InputLabel>
                          <Select
                            label="时区设置"
                            defaultValue="Asia/Shanghai"
                            sx={{
                              borderRadius: 1,
                              '&:hover .MuiOutlinedInput-notchedOutline': {
                                borderColor: '#1a1a1a'
                              },
                              '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                                borderColor: '#1a1a1a'
                              }
                            }}
                          >
                            <MenuItem value="Asia/Shanghai">北京时间 (UTC+8)</MenuItem>
                            <MenuItem value="America/New_York">纽约时间 (UTC-5)</MenuItem>
                            <MenuItem value="Europe/London">伦敦时间 (UTC+0)</MenuItem>
                          </Select>
                        </FormControl>
                      </Grid>
                    </Grid>
                  </Box>
                </Box>
              </Grid>

              {/* 通知设置 */}
              <Grid item xs={12} md={6}>
                <Box sx={{ borderRadius: 0, border: '1px solid rgba(0,0,0,0.08)' }}>
                  <Box sx={{ p: 4 }}>
                    <Typography variant="h6" sx={{ fontWeight: 700, color: '#1a1a1a', mb: 3 }}>
                      通知偏好
                    </Typography>

                    <List sx={{ p: 0 }}>
                      {[
                        { label: '邮件通知', description: '通过邮件接收重要通知' },
                        { label: '短信通知', description: '通过短信接收紧急通知' },
                        { label: '浏览器通知', description: '在浏览器中显示通知' },
                        { label: '推广数据报告', description: '定期接收数据分析报告' }
                      ].map((item, index) => (
                        <ListItem key={index} sx={{ px: 0, py: 2 }}>
                          <ListItemText
                            primary={
                              <Typography sx={{ fontWeight: 600, color: '#1a1a1a', fontSize: '0.95rem' }}>
                                {item.label}
                              </Typography>
                            }
                            secondary={
                              <Typography sx={{ color: '#666', fontSize: '0.875rem', mt: 0.5 }}>
                                {item.description}
                              </Typography>
                            }
                          />
                          <ListItemSecondaryAction>
                            <Switch
                              defaultChecked={index < 2}
                              sx={{
                                '& .MuiSwitch-switchBase.Mui-checked': {
                                  color: '#1a1a1a',
                                  '&:hover': {
                                    backgroundColor: 'rgba(156, 39, 176, 0.08)',
                                  },
                                },
                                '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                                  backgroundColor: '#1a1a1a',
                                },
                              }}
                            />
                          </ListItemSecondaryAction>
                        </ListItem>
                      ))}
                    </List>
                  </Box>
                </Box>
              </Grid>

              {/* 数据设置 */}
              <Grid item xs={12}>
                <Box sx={{ borderRadius: 0, border: '1px solid rgba(0,0,0,0.08)' }}>
                  <Box sx={{ p: 4 }}>
                    <Typography variant="h6" sx={{ fontWeight: 700, color: '#1a1a1a', mb: 3 }}>
                      数据设置
                    </Typography>

                    <Grid container spacing={3}>
                      <Grid item xs={12} sm={6} md={3}>
                        <FormControl fullWidth>
                          <InputLabel>数据刷新频率</InputLabel>
                          <Select
                            label="数据刷新频率"
                            defaultValue="5"
                            sx={{
                              borderRadius: 1,
                              '&:hover .MuiOutlinedInput-notchedOutline': {
                                borderColor: '#1a1a1a'
                              },
                              '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                                borderColor: '#1a1a1a'
                              }
                            }}
                          >
                            <MenuItem value="1">1分钟</MenuItem>
                            <MenuItem value="5">5分钟</MenuItem>
                            <MenuItem value="10">10分钟</MenuItem>
                            <MenuItem value="30">30分钟</MenuItem>
                          </Select>
                        </FormControl>
                      </Grid>
                      <Grid item xs={12} sm={6} md={3}>
                        <FormControl fullWidth>
                          <InputLabel>默认时间范围</InputLabel>
                          <Select
                            label="默认时间范围"
                            defaultValue="7"
                            sx={{
                              borderRadius: 1,
                              '&:hover .MuiOutlinedInput-notchedOutline': {
                                borderColor: '#1a1a1a'
                              },
                              '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                                borderColor: '#1a1a1a'
                              }
                            }}
                          >
                            <MenuItem value="1">今天</MenuItem>
                            <MenuItem value="7">最近7天</MenuItem>
                            <MenuItem value="30">最近30天</MenuItem>
                            <MenuItem value="90">最近90天</MenuItem>
                          </Select>
                        </FormControl>
                      </Grid>
                      <Grid item xs={12} sm={6} md={3}>
                        <FormControl fullWidth>
                          <InputLabel>图表类型</InputLabel>
                          <Select
                            label="图表类型"
                            defaultValue="line"
                            sx={{
                              borderRadius: 1,
                              '&:hover .MuiOutlinedInput-notchedOutline': {
                                borderColor: '#1a1a1a'
                              },
                              '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                                borderColor: '#1a1a1a'
                              }
                            }}
                          >
                            <MenuItem value="line">折线图</MenuItem>
                            <MenuItem value="bar">柱状图</MenuItem>
                            <MenuItem value="area">面积图</MenuItem>
                          </Select>
                        </FormControl>
                      </Grid>
                      <Grid item xs={12} sm={6} md={3}>
                        <FormControl fullWidth>
                          <InputLabel>数据精度</InputLabel>
                          <Select
                            label="数据精度"
                            defaultValue="2"
                            sx={{
                              borderRadius: 1,
                              '&:hover .MuiOutlinedInput-notchedOutline': {
                                borderColor: '#1a1a1a'
                              },
                              '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                                borderColor: '#1a1a1a'
                              }
                            }}
                          >
                            <MenuItem value="0">整数</MenuItem>
                            <MenuItem value="1">1位小数</MenuItem>
                            <MenuItem value="2">2位小数</MenuItem>
                            <MenuItem value="3">3位小数</MenuItem>
                          </Select>
                        </FormControl>
                      </Grid>
                    </Grid>

                    <Box sx={{ mt: 4, display: 'flex', gap: 2 }}>
                      <Button variant="contained" sx={{
                        backgroundColor: '#1a1a1a',
                        borderRadius: 1,
                        px: 4,
                        py: 1.5,
                        '&:hover': { backgroundColor: '#374151' }
                      }}>
                        保存设置
                      </Button>
                      <Button variant="outlined" sx={{
                        borderColor: '#1a1a1a',
                        color: '#1a1a1a',
                        borderRadius: 1,
                        px: 4,
                        py: 1.5,
                        '&:hover': {
                          borderColor: '#374151',
                          backgroundColor: 'rgba(156, 39, 176, 0.08)'
                        }
                      }}>
                        重置为默认
                      </Button>
                    </Box>
                  </Box>
                </Box>
              </Grid>
            </Grid>
          </Box>
        </Box>
      );
    }
    return null;
  };

  // 渲染系统设置页面（保留兼容性）
  const renderSettingsContent = () => {
    return <AgentAccountSettings />;
  };



  // 获取活动颜色
  const getActivityColor = (colorName) => {
    const colorMap = {
      'primary': '#1a1a1a',
      'warning': '#f39c12',
      'info': '#2196f3',
      'success': '#4caf50',
      'error': '#f44336'
    };
    return colorMap[colorName] || '#1a1a1a';
  };

  // 获取代理商公告数据
  const fetchAnnouncements = async () => {
    try {
      setAnnouncementLoading(true);
      console.log('代理商概况页面获取公告数据...');

      const data = await announcementService.getAnnouncements({
        page: 1,
        page_size: 5, // 只获取前5条用于展示
        target_audience: 'agent' // 代理商专用参数
      });

      console.log('代理商公告API响应:', data);

      let items = [];
      if (data && data.success && data.data) {
        if (Array.isArray(data.data.items)) {
          items = data.data.items;
        } else if (Array.isArray(data.data)) {
          items = data.data;
        }
      } else if (data && Array.isArray(data.items)) {
        items = data.items;
      } else if (data && Array.isArray(data)) {
        items = data;
      }

      if (items && Array.isArray(items)) {
        console.log('获取到的items:', items);

        // 转换API数据格式为组件需要的格式
        const formattedAnnouncements = items.map(item => ({
          id: item.id,
          title: item.title,
          content: item.content || item.summary || '',
          type: mapAnnouncementType(item.type),
          date: formatDate(item.publish_time || item.created_at),
          priority: mapPriority(item.priority),
          author: item.creator_name || '系统管理员'
        }));

        console.log('格式化后的公告数据:', formattedAnnouncements);
        setAnnouncements(formattedAnnouncements);
      } else {
        console.warn('无法处理的数据格式:', data);
        setAnnouncements([]);
      }
    } catch (error) {
      console.error('获取公告数据失败:', error);
      setAnnouncements([]);
    } finally {
      setAnnouncementLoading(false);
    }
  };

  // 映射公告类型
  const mapAnnouncementType = (apiType) => {
    const typeMap = {
      'system': 'warning',
      'maintenance': 'warning',
      'feature': 'success',
      'promotion': 'announcement',
      'notice': 'info'
    };
    return typeMap[apiType] || 'info';
  };

  // 映射优先级
  const mapPriority = (apiPriority) => {
    const priorityMap = {
      'high': 'high',
      'medium': 'medium',
      'low': 'low'
    };
    return priorityMap[apiPriority] || 'medium';
  };

  // 格式化日期
  const formatDate = (dateString) => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // 获取公告详情
  const fetchAnnouncementDetail = async (announcementId) => {
    try {
      setDetailLoading(true);
      console.log('获取公告详情，ID:', announcementId);

      const data = await announcementService.getAnnouncementById(announcementId);
      console.log('公告详情API响应:', data);

      // 检查不同的响应格式
      let item = null;
      if (data && data.success && data.data) {
        item = data.data;
      } else if (data && data.data) {
        // 有些API直接返回data
        item = data.data;
      } else if (data && data.success !== false && data.id) {
        // 直接返回公告对象
        item = data;
      } else if (data && data.items && Array.isArray(data.items) && data.items.length > 0) {
        // 处理可能的数组响应格式
        item = data.items[0];
      } else {
        console.warn('无法解析公告详情响应:', data);
        alert('获取公告详情失败');
        return;
      }

      if (item) {
        // 转换API数据格式
        const formattedAnnouncement = {
          id: item.id,
          title: item.title,
          content: item.content || item.summary || '',
          summary: item.summary || '',
          type: mapAnnouncementType(item.type),
          date: formatDate(item.publish_time || item.created_at),
          priority: mapPriority(item.priority),
          author: item.creator_name || '系统管理员',
          viewCount: item.view_count || 0,
          targetAudience: item.target_audience,
          status: item.status,
          isPinned: item.is_pinned,
          isPopup: item.is_popup,
          expireTime: item.expire_time ? formatDate(item.expire_time) : null
        };

        console.log('格式化后的公告详情:', formattedAnnouncement);
        setSelectedAnnouncement(formattedAnnouncement);
        setAnnouncementDetailOpen(true);
      } else {
        console.error('公告详情数据为空');
        alert('获取公告详情失败');
      }
    } catch (error) {
      console.error('获取公告详情失败:', error);
      alert('获取公告详情失败，请稍后重试');
    } finally {
      setDetailLoading(false);
    }
  };

  // 处理公告点击事件
  const handleAnnouncementClick = (announcement) => {
    console.log('点击公告:', announcement);
    fetchAnnouncementDetail(announcement.id);
  };

  // 关闭公告详情弹窗
  const handleCloseAnnouncementDetail = () => {
    setAnnouncementDetailOpen(false);
    setSelectedAnnouncement(null);
  };

  // 获取公告图标
  const getAnnouncementIcon = (type) => {
    switch(type) {
      case 'warning':
        return <Settings sx={{ color: '#f59e0b', fontSize: 18 }} />;
      case 'info':
        return <Dashboard sx={{ color: '#3b82f6', fontSize: 18 }} />;
      case 'success':
        return <CheckCircle sx={{ color: '#10b981', fontSize: 18 }} />;
      case 'announcement':
        return <Campaign sx={{ color: '#8b5cf6', fontSize: 18 }} />;
      default:
        return <Article sx={{ color: '#6b7280', fontSize: 18 }} />;
    }
  };

  // 获取公告背景颜色
  const getAnnouncementBgColor = (type) => {
    switch(type) {
      case 'warning': return '#fef3c7';
      case 'info': return '#dbeafe';
      case 'success': return '#d1fae5';
      case 'announcement': return '#ede9fe';
      default: return '#f3f4f6';
    }
  };

  // 渲染控制台内容
  const renderDashboardContent = () => {

    // 模拟最近推荐注册的用户数据
    const recentRegisteredUsers = [
      { id: 1, name: '北京科技有限公司', intention: '企业推广' },
      { id: 2, name: '上海商贸集团', intention: '品牌营销' },
      { id: 3, name: '深圳创新科技', intention: 'SEO优化' },
      { id: 4, name: '广州品牌营销', intention: '内容营销' },
      { id: 5, name: '杭州数字科技', intention: '全网推广' }
    ];

    const getStatusColor = (status) => {
      switch(status) {
        case '高意向': return '#10b981';
        case '待跟进': return '#f59e0b';
        case '新线索': return '#3b82f6';
        default: return '#6b7280';
      }
    };

    const getAnnouncementIcon = (type) => {
      switch(type) {
        case 'warning':
          return <Settings sx={{ color: '#f59e0b', fontSize: 18 }} />;
        case 'info':
          return <Campaign sx={{ color: '#3b82f6', fontSize: 18 }} />;
        case 'success':
          return <MonetizationOn sx={{ color: '#10b981', fontSize: 18 }} />;
        default:
          return <Article sx={{ color: '#6b7280', fontSize: 18 }} />;
      }
    };

    const getAnnouncementBgColor = (type) => {
      switch(type) {
        case 'warning': return '#fef3c7';
        case 'info': return '#dbeafe';
        case 'success': return '#d1fae5';
        default: return '#f3f4f6';
      }
    };

    return (
      <Box sx={{ width: '100%', backgroundColor: 'white', minHeight: '100vh' }}>
        {/* 顶部欢迎区域 */}
        <Box sx={{
          borderBottom: '1px solid #f0f0f0',
          backgroundColor: 'white'
        }}>
          <Container maxWidth="xl">
            <Box sx={{ py: 5 }}>
              <Typography variant="h3" sx={{
                fontWeight: 700,
                color: '#1a1a1a',
                mb: 1,
                fontSize: { xs: '2rem', md: '2.5rem' }
              }}>
                代理商推广中心
              </Typography>
              <Typography variant="body1" sx={{
                color: '#6b7280',
                fontSize: '1.125rem'
              }}>
                管理推广链接，追踪佣金收益，拓展客户资源
              </Typography>
            </Box>
          </Container>
        </Box>

        {/* 核心数据统计栏 */}
        <Box sx={{
          borderBottom: '1px solid #f0f0f0',
          backgroundColor: '#fafafa'
        }}>
          <Container maxWidth="xl">
            <Box sx={{ py: 4 }}>
              <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', sm: 'repeat(2, 1fr)', md: 'repeat(4, 1fr)' }, gap: 4 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Box sx={{
                    width: 44,
                    height: 44,
                    borderRadius: '12px',
                    backgroundColor: '#eff6ff',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <MonetizationOn sx={{ color: '#3b82f6', fontSize: 22 }} />
                  </Box>
                  <Box>
                    <Typography variant="caption" sx={{ 
                      color: '#9ca3af', 
                      fontSize: '0.75rem',
                      fontWeight: 500,
                      textTransform: 'uppercase',
                      letterSpacing: '0.05em'
                    }}>
                      总佣金
                    </Typography>
                    <Typography variant="h6" sx={{ 
                      color: '#1a1a1a',
                      fontWeight: 600,
                      fontSize: '1.5rem',
                      mt: 0.5
                    }}>
                      ¥45,680
                    </Typography>
                  </Box>
                </Box>

                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Box sx={{
                    width: 44,
                    height: 44,
                    borderRadius: '12px',
                    backgroundColor: '#fef3c7',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <People sx={{ color: '#f59e0b', fontSize: 22 }} />
                  </Box>
                  <Box>
                    <Typography variant="caption" sx={{ 
                      color: '#9ca3af', 
                      fontSize: '0.75rem',
                      fontWeight: 500,
                      textTransform: 'uppercase',
                      letterSpacing: '0.05em'
                    }}>
                      推广客户
                    </Typography>
                    <Typography variant="h6" sx={{ 
                      color: '#1a1a1a',
                      fontWeight: 600,
                      fontSize: '1.5rem',
                      mt: 0.5
                    }}>
                      156
                    </Typography>
                  </Box>
                </Box>

                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Box sx={{
                    width: 44,
                    height: 44,
                    borderRadius: '12px',
                    backgroundColor: '#f0fdf4',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <TrendingUp sx={{ color: '#10b981', fontSize: 22 }} />
                  </Box>
                  <Box>
                    <Typography variant="caption" sx={{ 
                      color: '#9ca3af', 
                      fontSize: '0.75rem',
                      fontWeight: 500,
                      textTransform: 'uppercase',
                      letterSpacing: '0.05em'
                    }}>
                      转化率
                    </Typography>
                    <Typography variant="h6" sx={{ 
                      color: '#1a1a1a',
                      fontWeight: 600,
                      fontSize: '1.5rem',
                      mt: 0.5
                    }}>
                      25.5%
                    </Typography>
                  </Box>
                </Box>

                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Box sx={{
                    width: 44,
                    height: 44,
                    borderRadius: '12px',
                    backgroundColor: '#fce7f3',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <Link sx={{ color: '#ec4899', fontSize: 22 }} />
                  </Box>
                  <Box>
                    <Typography variant="caption" sx={{ 
                      color: '#9ca3af', 
                      fontSize: '0.75rem',
                      fontWeight: 500,
                      textTransform: 'uppercase',
                      letterSpacing: '0.05em'
                    }}>
                      活跃链接
                    </Typography>
                    <Typography variant="h6" sx={{ 
                      color: '#1a1a1a',
                      fontWeight: 600,
                      fontSize: '1.5rem',
                      mt: 0.5
                    }}>
                      12
                    </Typography>
                  </Box>
                </Box>
              </Box>
            </Box>
          </Container>
        </Box>

        {/* 主要内容区域 */}
        <Box sx={{ backgroundColor: 'white' }}>
          <Container maxWidth="xl">
            <Box sx={{ py: 5 }}>
              <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', md: 'repeat(2, 1fr)' }, gap: 4 }}>
                {/* 公告栏 */}
                <Card sx={{ 
                  borderRadius: 2,
                  border: '1px solid #e5e7eb',
                  boxShadow: 'none',
                  height: '100%',
                  minHeight: 400,
                  maxHeight: 500,
                  display: 'flex',
                  flexDirection: 'column'
                }}>
                  <Box sx={{ p: 2 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Box sx={{ 
                          backgroundColor: '#eff6ff',
                          width: 28,
                          height: 28,
                          borderRadius: '50%',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center'
                        }}>
                          <Dashboard sx={{ color: '#3b82f6', fontSize: 18 }} />
                        </Box>
                        <Typography variant="subtitle1" sx={{ 
                          fontWeight: 600,
                          color: '#1a1a1a',
                          fontSize: '1rem'
                        }}>
                          系统公告
                        </Typography>
                      </Box>
                      <Button
                        size="small"
                        variant="text"
                        sx={{ color: '#6b7280', fontSize: '0.75rem' }}
                        onClick={() => {
                          console.log('跳转到最新公告页面');
                          setCurrentPage('announcements');
                        }}
                      >
                        查看全部
                      </Button>
                    </Box>

                    {/* 公告列表 */}
                    <List sx={{ p: 0, flex: 1, overflowY: 'auto' }}>
                      {announcementLoading ? (
                        // 加载状态
                        <Box sx={{ display: 'flex', justifyContent: 'center', py: 3 }}>
                          <Typography variant="body2" color="text.secondary">
                            加载中...
                          </Typography>
                        </Box>
                      ) : announcements.length === 0 ? (
                        // 空状态
                        <Box sx={{ display: 'flex', justifyContent: 'center', py: 3 }}>
                          <Typography variant="body2" color="text.secondary">
                            暂无公告
                          </Typography>
                        </Box>
                      ) : (
                        // 公告列表
                        announcements.slice(0, 3).map((announcement, index) => (
                        <Fragment key={announcement.id}>
                          <ListItemButton
                            sx={{
                              px: 1,
                              py: 0.5,
                              borderRadius: 1,
                              mb: 0.5,
                              backgroundColor: getAnnouncementBgColor(announcement.type),
                              '&:hover': {
                                backgroundColor: announcement.type === 'warning' ? '#fde68a' :
                                               announcement.type === 'info' ? '#bfdbfe' :
                                               announcement.type === 'success' ? '#a7f3d0' :
                                               announcement.type === 'announcement' ? '#ddd6fe' :
                                               '#e5e7eb',
                                cursor: 'pointer'
                              }
                            }}
                            onClick={() => handleAnnouncementClick(announcement)}
                          >
                            <ListItemIcon sx={{ minWidth: 32 }}>
                              {getAnnouncementIcon(announcement.type)}
                            </ListItemIcon>
                            <ListItemText
                              primary={
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5, mb: 0.25 }}>
                                  <Typography variant="body2" sx={{ fontWeight: 600, color: '#1a1a1a', fontSize: '0.875rem' }}>
                                    {announcement.title}
                                  </Typography>
                                  {announcement.priority === 'high' && (
                                    <Box sx={{ 
                                      px: 0.5,
                                      py: 0,
                                      backgroundColor: '#ef4444',
                                      color: 'white',
                                      fontSize: '0.65rem',
                                      borderRadius: 0.5,
                                      fontWeight: 600
                                    }}>
                                      重要
                                    </Box>
                                  )}
                                </Box>
                              }
                              secondary={
                                <Box>
                                  <Typography variant="caption" sx={{ color: '#4b5563', mb: 0.5, display: 'block', fontSize: '0.75rem' }}>
                                    {announcement.content.length > 60 ? announcement.content.substring(0, 60) + '...' : announcement.content} • {announcement.date.split(' ')[0]}
                                  </Typography>
                                </Box>
                              }
                            />
                          </ListItemButton>
                        </Fragment>
                      ))
                      )}
                    </List>
                  </Box>
                </Card>

                {/* 推荐客户 */}
                <Card sx={{ 
                  borderRadius: 2,
                  border: '1px solid #e5e7eb',
                  boxShadow: 'none',
                  height: '100%',
                  minHeight: 400,
                  maxHeight: 500,
                  display: 'flex',
                  flexDirection: 'column'
                }}>
                  <Box sx={{ p: 2 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Box sx={{ 
                          backgroundColor: '#f0fdf4',
                          width: 28,
                          height: 28,
                          borderRadius: '50%',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center'
                        }}>
                          <People sx={{ color: '#10b981', fontSize: 18 }} />
                        </Box>
                        <Typography variant="subtitle1" sx={{ 
                          fontWeight: 600,
                          color: '#1a1a1a',
                          fontSize: '1rem'
                        }}>
                          最近推荐注册的用户
                        </Typography>
                      </Box>
                      <Button 
                        variant="text" 
                        sx={{ 
                          color: '#6b7280',
                          fontSize: '0.75rem'
                        }}
                      >
                        查看全部
                      </Button>
                    </Box>

                    <List sx={{ p: 0 }}>
                      {recentRegisteredUsers.map((customer, index) => (
                        <Fragment key={customer.id}>
                          <ListItemButton
                            sx={{ 
                              px: 1,
                              py: 1,
                              '&:hover': {
                                backgroundColor: '#f9fafb'
                              }
                            }}
                          >
                            <ListItemText
                              primary={
                                <Typography variant="body2" sx={{ fontWeight: 500, color: '#1a1a1a', fontSize: '0.875rem' }}>
                                  {customer.name}
                                </Typography>
                              }
                              secondary={
                                <Typography variant="caption" sx={{ color: '#6b7280', fontSize: '0.75rem' }}>
                                  意向：{customer.intention}
                                </Typography>
                              }
                            />
                          </ListItemButton>
                        </Fragment>
                      ))}
                    </List>
                  </Box>
                </Card>
              </Box>
            </Box>
          </Container>
        </Box>
      </Box>
    );
  };

  const quickActions = [
    {
      id: 1,
      title: '生成推广链接',
      description: '创建专属推广链接',
      icon: <Link />,
      color: 'primary',
      action: () => setCurrentPage('promotion/links'),
      gradient: 'linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%)'
    },
    {
      id: 4,
      title: '分享推广',
      description: '快速分享到社交媒体',
      icon: <Campaign />,
      color: 'warning',
      action: () => {
        if (navigator.share) {
          navigator.share({
            title: 'AI搜索引擎优化平台',
            text: '专业的AI搜索引擎优化服务，提升您的网站排名！',
            url: window.location.origin + '?ref=agent123'
          });
        } else {
          const link = window.location.origin + '?ref=agent123';
          navigator.clipboard.writeText(link).then(() => {
            alert('推广链接已复制到剪贴板！');
          });
        }
      },
      gradient: 'linear-gradient(135deg, #64b5f6 0%, #42a5f5 100%)'
    },
  ];





  return (
    <Box sx={{ display: 'flex', height: '100vh', backgroundColor: '#ffffff' }}>
      {/* 侧边栏 */}
      <Drawer
        variant="persistent"
        anchor="left"
        open={sidebarOpen}
        sx={{
          width: sidebarOpen ? (sidebarCollapsed ? 72 : 280) : 0,
          flexShrink: 0,
          transition: 'width 0.3s ease',
          '& .MuiDrawer-paper': {
            width: sidebarCollapsed ? 72 : 280,
            boxSizing: 'border-box',
            backgroundColor: '#fff',
            borderRight: '1px solid #e0e0e0',
            boxShadow: '2px 0 8px rgba(0,0,0,0.1)',
            height: '100vh',
            overflow: 'hidden',
            display: 'flex',
            flexDirection: 'column',
            transition: 'width 0.3s ease'
          },
        }}
      >
        {/* 侧边栏头部 */}
        <Box sx={{
          p: sidebarCollapsed ? 1 : 3,
          borderBottom: '1px solid #e0e0e0',
          transition: 'padding 0.3s ease'
        }}>
          {!sidebarCollapsed ? (
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Campaign sx={{ fontSize: 28, color: '#1976d2' }} />
              <Typography variant="h6" sx={{ fontWeight: 700, color: '#1a1a1a', flex: 1 }}>
                代理商推广中心
              </Typography>
              <IconButton
                onClick={() => setSidebarCollapsed(true)}
                size="small"
                sx={{ color: '#666' }}
              >
                <MenuOpen />
              </IconButton>
            </Box>
          ) : (
            <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 1 }}>
              <Campaign sx={{ fontSize: 24, color: '#1976d2' }} />
              <IconButton
                onClick={() => setSidebarCollapsed(false)}
                size="small"
                sx={{ color: '#666' }}
              >
                <Menu />
              </IconButton>
            </Box>
          )}
        </Box>

        {/* 用户账户信息 - 只在侧边栏展开时显示 */}
        {!sidebarCollapsed && (
          <Box sx={{
            p: 2,
            borderBottom: '1px solid #e0e0e0'
          }}>
            <Box sx={{
              p: 2,
              backgroundColor: '#f8fafc',
              borderRadius: 2,
              border: '1px solid #e5e7eb'
            }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Avatar sx={{
                  width: 40,
                  height: 40,
                  backgroundColor: '#3b82f6',
                  fontSize: '1rem'
                }}>
                  {user?.name ? user.name.charAt(0).toUpperCase() : 'A'}
                </Avatar>
                <Box sx={{ flex: 1 }}>
                  <Typography variant="body1" sx={{
                    fontWeight: 600,
                    color: '#1a1a1a',
                    fontSize: '0.95rem'
                  }}>
                    {user?.name || user?.username || '代理商用户'}
                  </Typography>
                  <Typography variant="caption" sx={{
                    color: '#6b7280',
                    fontSize: '0.75rem'
                  }}>
                    {user?.email || '<EMAIL>'}
                  </Typography>
                </Box>
              </Box>
            </Box>
          </Box>
        )}

        {/* 导航菜单 */}
        <List sx={{
          flexGrow: 1,
          py: 2,
          overflow: 'auto',
          height: 0
        }}>
            {navigationItems.map((item) => (
            <ListItemButton
              key={item.id}
              onClick={() => handleMenuClick(item)}
              selected={currentPage === item.path}
              sx={{
                mx: sidebarCollapsed ? 0.5 : 2,
                mb: 1,
                borderRadius: 2,
                backgroundColor: currentPage === item.path ? 'rgba(25, 118, 210, 0.1)' : 'transparent',
                '&:hover': {
                  backgroundColor: 'rgba(25, 118, 210, 0.05)',
                },
                justifyContent: sidebarCollapsed ? 'center' : 'flex-start',
                px: sidebarCollapsed ? 1 : 2
              }}
            >
              <ListItemIcon sx={{
                color: currentPage === item.path ? '#1976d2' : 'inherit',
                minWidth: sidebarCollapsed ? 'auto' : 40,
                justifyContent: 'center'
              }}>
                {item.icon}
              </ListItemIcon>
              {!sidebarCollapsed && (
                <ListItemText
                  primary={item.title}
                  sx={{
                    '& .MuiListItemText-primary': {
                      fontWeight: currentPage === item.path ? 600 : 400,
                      color: currentPage === item.path ? '#1976d2' : 'inherit'
                    }
                  }}
                />
              )}
            </ListItemButton>
          ))}
          </List>

        {/* 退出按钮 */}
        <Box sx={{
          p: sidebarCollapsed ? 1 : 2,
          borderTop: '1px solid #e0e0e0',
          mt: 'auto',
          transition: 'padding 0.3s ease'
        }}>
          <ListItemButton
            onClick={() => {
              logout();
              navigate('/auth/login');
            }}
            sx={{
              borderRadius: 2,
              color: '#dc2626',
              transition: 'all 0.2s',
              justifyContent: sidebarCollapsed ? 'center' : 'flex-start',
              px: sidebarCollapsed ? 1 : 2,
              '&:hover': {
                backgroundColor: 'rgba(220, 38, 38, 0.05)',
                color: '#b91c1c',
              }
            }}
          >
            <ListItemIcon sx={{
              color: 'inherit',
              minWidth: sidebarCollapsed ? 'auto' : 40,
              justifyContent: 'center'
            }}>
              <Logout />
            </ListItemIcon>
            {!sidebarCollapsed && (
              <ListItemText
                primary="退出登录"
                sx={{
                  '& .MuiListItemText-primary': {
                    fontWeight: 500
                  }
                }}
              />
            )}
          </ListItemButton>
        </Box>
      </Drawer>

      {/* 主内容区域 */}
      <Box sx={{
        flexGrow: 1,
        display: 'flex',
        flexDirection: 'column',
        marginLeft: sidebarOpen ? 0 : '-280px',
        transition: 'margin-left 0.3s ease'
      }}>
        {renderPageContent()}
      </Box>

      {/* 设置对话框 */}
      <Dialog open={settingsOpen} onClose={() => setSettingsOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>控制中心设置</DialogTitle>
        <DialogContent>
          <List>
            <ListItem>
              <ListItemIcon>
                <Notifications />
              </ListItemIcon>
              <ListItemText primary="通知提醒" secondary="接收系统通知和提醒" />
              <ListItemSecondaryAction>
                <Switch
                  checked={notifications}
                  onChange={(e) => setNotifications(e.target.checked)}
                />
              </ListItemSecondaryAction>
            </ListItem>

          </List>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setSettingsOpen(false)}>关闭</Button>
        </DialogActions>
      </Dialog>

      {/* 提现申请对话框 */}
      <Dialog
        open={withdrawDialogOpen}
        onClose={() => setWithdrawDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>申请佣金结算</DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <TextField
              fullWidth
              label="结算金额"
              type="number"
              value={withdrawForm.amount}
              onChange={(e) => setWithdrawForm(prev => ({ ...prev, amount: e.target.value }))}
              sx={{ mb: 2 }}
              helperText={`可结算佣金：¥${earningsData.available_balance.toFixed(2)}`}
            />
            <TextField
              fullWidth
              label="银行账号"
              value={withdrawForm.bank_account}
              onChange={(e) => setWithdrawForm(prev => ({ ...prev, bank_account: e.target.value }))}
              sx={{ mb: 2 }}
            />
            <TextField
              fullWidth
              label="银行名称"
              value={withdrawForm.bank_name}
              onChange={(e) => setWithdrawForm(prev => ({ ...prev, bank_name: e.target.value }))}
              sx={{ mb: 2 }}
            />
            <TextField
              fullWidth
              label="账户持有人"
              value={withdrawForm.account_holder}
              onChange={(e) => setWithdrawForm(prev => ({ ...prev, account_holder: e.target.value }))}
              sx={{ mb: 2 }}
            />
            <TextField
              fullWidth
              label="结算说明（可选）"
              multiline
              rows={3}
              value={withdrawForm.withdraw_reason}
              onChange={(e) => setWithdrawForm(prev => ({ ...prev, withdraw_reason: e.target.value }))}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setWithdrawDialogOpen(false)}>取消</Button>
          <Button
            onClick={handleWithdrawSubmit}
            variant="contained"
            disabled={!withdrawForm.amount || !withdrawForm.bank_account || !withdrawForm.bank_name || !withdrawForm.account_holder}
          >
            提交申请
          </Button>
        </DialogActions>
      </Dialog>

      {/* 客户详情对话框 */}
      <Dialog
        open={customerDetailDialog.open}
        onClose={handleCloseCustomerDetail}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          borderBottom: '1px solid #e0e0e0'
        }}>
          <Typography variant="h6" sx={{ fontWeight: 600 }}>
            客户详情
          </Typography>
          <IconButton onClick={handleCloseCustomerDetail} size="small">
            <Close />
          </IconButton>
        </DialogTitle>
        <DialogContent sx={{ p: 0 }}>
          {customerDetailDialog.loading ? (
            <Box sx={{ p: 4, textAlign: 'center' }}>
              <LinearProgress sx={{ mb: 2 }} />
              <Typography variant="body2" color="text.secondary">
                正在加载客户详情...
              </Typography>
            </Box>
          ) : customerDetailDialog.customer ? (
            <Box sx={{ p: 3 }}>
              <Box sx={{ borderRadius: 0, border: '1px solid #e5e7eb', border: '1px solid #e5e7eb' }}>
                <Box sx={{ p: 4 }}>
                  {/* 客户头像和基本信息 */}
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 4 }}>
                    <Avatar sx={{
                      width: 80,
                      height: 80,
                      backgroundColor: '#1a1a1a',
                      fontSize: '2rem',
                      fontWeight: 700,
                      mr: 3
                    }}>
                      {customerDetailDialog.customer.name.charAt(0)}
                    </Avatar>
                    <Box sx={{ flex: 1 }}>
                      <Typography variant="h5" sx={{ fontWeight: 700, color: '#1a1a1a', mb: 1 }}>
                        {customerDetailDialog.customer.name}
                      </Typography>
                      <Box sx={{ display: 'flex', gap: 1, mb: 1 }}>
                        <Chip
                          label={customerDetailDialog.customer.level}
                          size="small"
                          sx={{
                            backgroundColor: customerDetailDialog.customer.level === 'VIP' ? '#f39c12' :
                              customerDetailDialog.customer.level === '活跃' ? '#27ae60' :
                              customerDetailDialog.customer.level === '试用' ? '#3498db' : '#95a5a6',
                            color: '#fff',
                            fontWeight: 600
                          }}
                        />
                        <Chip
                          label={customerDetailDialog.customer.status === 'active' ? '活跃' :
                                customerDetailDialog.customer.status === 'trial' ? '试用' : '未激活'}
                          size="small"
                          sx={{
                            backgroundColor: customerDetailDialog.customer.status === 'active' ? '#e8f5e8' :
                              customerDetailDialog.customer.status === 'trial' ? '#e3f2fd' : '#ffebee',
                            color: customerDetailDialog.customer.status === 'active' ? '#2e7d32' :
                              customerDetailDialog.customer.status === 'trial' ? '#1976d2' : '#c62828',
                            fontWeight: 600
                          }}
                        />
                      </Box>
                      <Typography variant="body2" color="text.secondary">
                        {customerDetailDialog.customer.company}
                      </Typography>
                    </Box>
                  </Box>

                  <Divider sx={{ mb: 3 }} />

                  {/* 三列布局 */}
                  <Grid container spacing={4}>
                    {/* 基本信息 */}
                    <Grid item xs={12} md={4}>
                      <Typography variant="h6" sx={{ fontWeight: 600, mb: 2, color: '#1a1a1a' }}>
                        基本信息
                      </Typography>
                      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                        <Box>
                          <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 0.5 }}>
                            邮箱
                          </Typography>
                          <Typography variant="body2" sx={{ fontWeight: 500 }}>
                            {customerDetailDialog.customer.email}
                          </Typography>
                        </Box>
                        <Box>
                          <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 0.5 }}>
                            电话
                          </Typography>
                          <Typography variant="body2" sx={{ fontWeight: 500 }}>
                            {customerDetailDialog.customer.phone}
                          </Typography>
                        </Box>
                        <Box>
                          <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 0.5 }}>
                            公司
                          </Typography>
                          <Typography variant="body2" sx={{ fontWeight: 500 }}>
                            {customerDetailDialog.customer.company}
                          </Typography>
                        </Box>
                      </Box>
                    </Grid>

                    {/* 推荐信息 */}
                    <Grid item xs={12} md={4}>
                      <Typography variant="h6" sx={{ fontWeight: 600, mb: 2, color: '#1a1a1a' }}>
                        推荐信息
                      </Typography>
                      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                        <Box>
                          <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 0.5 }}>
                            推荐码
                          </Typography>
                          <Typography variant="body2" sx={{ fontWeight: 500, fontFamily: 'monospace', backgroundColor: '#f5f5f5', p: 1, borderRadius: 1 }}>
                            {customerDetailDialog.customer.referral_code}
                          </Typography>
                        </Box>
                        <Box>
                          <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 0.5 }}>
                            推荐日期
                          </Typography>
                          <Typography variant="body2" sx={{ fontWeight: 500 }}>
                            {customerDetailDialog.customer.referral_date}
                          </Typography>
                        </Box>
                        <Box>
                          <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 0.5 }}>
                            注册日期
                          </Typography>
                          <Typography variant="body2" sx={{ fontWeight: 500 }}>
                            {customerDetailDialog.customer.register_date}
                          </Typography>
                        </Box>
                        <Box>
                          <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 0.5 }}>
                            最后登录
                          </Typography>
                          <Typography variant="body2" sx={{ fontWeight: 500 }}>
                            {customerDetailDialog.customer.last_login}
                          </Typography>
                        </Box>
                      </Box>
                    </Grid>

                    {/* 消费统计 */}
                    <Grid item xs={12} md={4}>
                      <Typography variant="h6" sx={{ fontWeight: 600, mb: 2, color: '#1a1a1a' }}>
                        消费统计
                      </Typography>
                      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                        <Box sx={{ textAlign: 'center', p: 2, backgroundColor: '#f8f4ff', borderRadius: 2, border: '1px solid #e0d4ff' }}>
                          <Typography variant="h4" sx={{ fontWeight: 700, color: '#1a1a1a', mb: 0.5 }}>
                            ¥{customerDetailDialog.customer.total_spent?.toFixed(2) || '0.00'}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            总消费金额
                          </Typography>
                        </Box>
                        <Box sx={{ display: 'flex', gap: 1 }}>
                          <Box sx={{ flex: 1, textAlign: 'center', p: 1.5, backgroundColor: '#f0f8f0', borderRadius: 2 }}>
                            <Typography variant="h6" sx={{ fontWeight: 700, color: '#27ae60', mb: 0.5 }}>
                              {customerDetailDialog.customer.orders_count || 0}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              订单数量
                            </Typography>
                          </Box>
                          <Box sx={{ flex: 1, textAlign: 'center', p: 1.5, backgroundColor: '#f8f8f8', borderRadius: 2 }}>
                            <Typography variant="body2" sx={{ fontWeight: 600, color: '#1a1a1a', mb: 0.5 }}>
                              {customerDetailDialog.customer.last_order_date === '无订单' ? '无' : customerDetailDialog.customer.last_order_date}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              最后订单
                            </Typography>
                          </Box>
                        </Box>
                      </Box>
                    </Grid>
                  </Grid>
                </Box>
              </Box>
            </Box>
          ) : (
            <Box sx={{ p: 4, textAlign: 'center' }}>
              <Typography variant="body2" color="text.secondary">
                无法加载客户详情
              </Typography>
            </Box>
          )}
        </DialogContent>
        <DialogActions sx={{ p: 2, borderTop: '1px solid #e0e0e0' }}>
          <Button onClick={handleCloseCustomerDetail} variant="outlined">
            关闭
          </Button>
        </DialogActions>
      </Dialog>

      {/* 公告详情弹窗 */}
      <Dialog
        open={announcementDetailOpen}
        onClose={handleCloseAnnouncementDetail}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: { borderRadius: 2 }
        }}
      >
        <DialogTitle sx={{
          pb: 2,
          display: 'flex',
          alignItems: 'center',
          gap: 2
        }}>
          {selectedAnnouncement && (
            <>
              {getAnnouncementIcon(selectedAnnouncement.type)}
              <Box>
                <Typography variant="h6" component="div">
                  {selectedAnnouncement.title}
                  {selectedAnnouncement.priority === 'high' && (
                    <Chip
                      label="重要"
                      size="small"
                      color="error"
                      sx={{ ml: 1 }}
                    />
                  )}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {selectedAnnouncement.author} • {selectedAnnouncement.date}
                  {selectedAnnouncement.viewCount > 0 && ` • 阅读 ${selectedAnnouncement.viewCount} 次`}
                </Typography>
              </Box>
            </>
          )}
        </DialogTitle>

        <DialogContent sx={{ py: 3 }}>
          {detailLoading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
              <CircularProgress />
            </Box>
          ) : selectedAnnouncement ? (
            <Box>
              {/* 公告摘要 */}
              {selectedAnnouncement.summary && selectedAnnouncement.summary !== selectedAnnouncement.content && (
                <Box sx={{ mb: 3, p: 2, backgroundColor: '#f8f9fa', borderRadius: 1 }}>
                  <Typography variant="subtitle2" color="primary" gutterBottom>
                    摘要
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {selectedAnnouncement.summary}
                  </Typography>
                </Box>
              )}

              {/* 公告内容 */}
              <Typography variant="body1" sx={{ lineHeight: 1.8, whiteSpace: 'pre-wrap' }}>
                {selectedAnnouncement.content}
              </Typography>

              {/* 公告信息 */}
              <Box sx={{ mt: 3, pt: 2, borderTop: '1px solid #e5e7eb' }}>
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">
                      <strong>目标受众：</strong>
                      {selectedAnnouncement.targetAudience === 'all' ? '全部用户' :
                       selectedAnnouncement.targetAudience === 'enterprise' ? '企业用户' :
                       selectedAnnouncement.targetAudience === 'channel' ? '渠道商' :
                       selectedAnnouncement.targetAudience === 'agent' ? '代理商' :
                       selectedAnnouncement.targetAudience}
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">
                      <strong>状态：</strong>
                      {selectedAnnouncement.status === 'published' ? '已发布' :
                       selectedAnnouncement.status === 'draft' ? '草稿' :
                       selectedAnnouncement.status}
                    </Typography>
                  </Grid>
                  {selectedAnnouncement.expireTime && (
                    <Grid item xs={12}>
                      <Typography variant="body2" color="text.secondary">
                        <strong>过期时间：</strong>{selectedAnnouncement.expireTime}
                      </Typography>
                    </Grid>
                  )}
                </Grid>
              </Box>
            </Box>
          ) : null}
        </DialogContent>

        <DialogActions sx={{ px: 3, pb: 2 }}>
          <Button onClick={handleCloseAnnouncementDetail}>
            关闭
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}

export default AgentControlCenter;
