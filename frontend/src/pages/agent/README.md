# 代理商控制中心 (Agent Control Center)

## 概述

代理商控制中心是一个专为代理商设计的营销推广管理平台，提供完整的推广工具和佣金管理功能。该系统参考了 `agent/` 目录中的HTML模板设计，使用React和Material-UI构建，提供现代化的用户界面和丰富的功能。

## 功能特性

### 1. 推广概览 (Dashboard)
- **统计卡片**: 显示累计佣金、推荐用户、转化率、链接点击等关键指标
- **佣金趋势图**: 可视化展示佣金收入趋势（支持7天、30天、90天视图）
- **快速操作**: 提供生成推广链接、下载素材、查看统计、分享推广等快捷功能
- **最近活动**: 实时显示推广活动动态，包括新用户注册、佣金获得等

### 2. 推广工具 (Promotion Tools)

#### 推广链接管理
- **链接生成器**: 
  - 自定义链接名称
  - 选择推广页面（首页、产品介绍、解决方案、注册页面）
  - 设置推广渠道（微信、微博、QQ、邮件、其他）
  - 添加备注信息
- **链接列表**: 
  - 显示所有推广链接及其统计数据
  - 支持按状态筛选（活跃/停用）
  - 提供复制链接、查看统计等操作
  - 显示点击数、转化数、创建时间等信息

#### 推广素材库
- **素材筛选**: 
  - 按素材类型筛选（图片、视频、文档、横幅）
  - 按适用场景筛选（社交媒体、邮件营销、网站推广、印刷物料）
  - 按尺寸规格筛选（方形、横版、竖版、横幅）
- **素材展示**: 
  - 网格布局展示所有素材
  - 显示素材预览、标题、描述
  - 显示尺寸、文件大小、格式、创建日期
  - 提供预览和下载功能
- **素材类型**: 
  - AI搜索优化产品介绍海报
  - 企业宣传视频模板
  - 邮件营销HTML模板
  - 网站横幅广告
  - 微信朋友圈推广图
  - 产品功能介绍PPT模板

### 3. 收益管理 (Revenue Management)

#### 提现管理
- **提现申请**: 代理商可以申请提现可用余额
- **提现记录**: 查看历史提现记录和状态
- **余额管理**: 显示当前可提现余额和待结算金额

## 技术实现

### 前端技术栈
- **React**: 主要框架
- **Material-UI (MUI)**: UI组件库
- **React Router**: 路由管理
- **Context API**: 状态管理（用户认证等）

### 组件结构
```
AgentControlCenter.js
├── 导航菜单 (Navigation Menu)
├── 页面内容渲染 (Page Content Renderer)
│   ├── 推广概览 (Dashboard)
│   ├── 推广链接 (Promotion Links)
│   ├── 推广素材 (Promotion Materials)
│   └── 提现管理 (Withdraw Management)
└── 设置对话框 (Settings Dialog)
```

### 数据模拟
所有数据均使用硬编码模拟数据，包括：
- 统计数据（用户数、转化率、链接点击等）
- 推广链接列表
- 推广素材库
- 提现记录

## 设计参考

该组件的设计和功能参考了 `agent/` 目录中的HTML模板：
- `agent/dashboard.html` - 主框架和导航结构
- `agent/overview.html` - 推广概览页面
- `agent/promotion/links.html` - 推广链接管理
- `agent/promotion/materials.html` - 推广素材库
- `agent/commission/statistics.html` - 佣金统计页面

## 使用方法

1. **导入组件**:
```jsx
import AgentControlCenter from './pages/agent/AgentControlCenter';
```

2. **在路由中使用**:
```jsx
<Route path="/agent/control-center" element={<AgentControlCenter />} />
```

3. **确保认证上下文**:
组件需要在 `AuthContext` 提供者内使用，以获取用户信息。

## 测试

提供了完整的单元测试 (`AgentControlCenter.test.js`)，覆盖：
- 组件渲染
- 导航功能
- 页面切换
- 用户交互
- 数据显示

运行测试：
```bash
npm test AgentControlCenter.test.js
```

## 未来扩展

1. **图表集成**: 集成 Chart.js 或 Recharts 实现真实的数据可视化
2. **API集成**: 连接后端API获取真实数据
3. **实时更新**: 使用WebSocket实现实时数据更新
4. **导出功能**: 添加数据导出为Excel/PDF功能
5. **移动端优化**: 进一步优化移动端体验
6. **国际化**: 添加多语言支持

## 注意事项

- 当前版本使用模拟数据，不连接后端接口
- 所有统计数据和图表都是静态展示
- 文件下载功能为模拟实现
- 分享功能使用浏览器原生API，在不支持的浏览器中会降级为复制链接
