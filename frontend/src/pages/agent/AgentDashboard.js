import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Button,
  Avatar,
  Chip,
  LinearProgress,
  IconButton,
  Tooltip,
  Paper,
  Divider,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from '@mui/material';
import {
  Campaign,
  TrendingUp,
  Analytics,
  Group,
  Link,
  Assessment,
  Dashboard,
  Refresh,
  MoreVert,
  CheckCircle,
  Schedule,
  AttachMoney,
  Person,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';

function AgentDashboard() {
  const navigate = useNavigate();
  const [stats, setStats] = useState({
    totalCommission: 28450,
    referredUsers: 156,
    activeUsers: 89,
    monthlyCommission: 5680
  });

  const quickActions = [
    {
      id: 1,
      title: '推广链接',
      description: '生成和管理推广链接',
      icon: <Link />,
      color: 'primary',
      action: () => navigate('/agent/referral-links'),
      gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
    },
    {
      id: 2,
      title: '佣金管理',
      description: '查看佣金详情和提现',
      icon: <AttachMoney />,
      color: 'success',
      action: () => navigate('/agent/commission'),
      gradient: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)'
    },
    {
      id: 3,
      title: '用户管理',
      description: '管理推荐的用户',
      icon: <Group />,
      color: 'info',
      action: () => navigate('/agent/user-management'),
      gradient: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)'
    },
    {
      id: 4,
      title: '业绩分析',
      description: '查看推广业绩和统计',
      icon: <Analytics />,
      color: 'warning',
      action: () => navigate('/agent/performance'),
      gradient: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)'
    },
  ];

  const statCards = [
    {
      title: '总佣金',
      value: `¥${stats.totalCommission.toLocaleString()}`,
      icon: <AttachMoney />,
      color: 'success',
      change: '+18%'
    },
    {
      title: '推荐用户',
      value: stats.referredUsers,
      icon: <Group />,
      color: 'primary',
      change: '+25'
    },
    {
      title: '活跃用户',
      value: stats.activeUsers,
      icon: <Person />,
      color: 'info',
      change: '+12'
    },
    {
      title: '月度佣金',
      value: `¥${stats.monthlyCommission.toLocaleString()}`,
      icon: <TrendingUp />,
      color: 'warning',
      change: '+32%'
    },
  ];

  const recentReferrals = [
    { name: '张三', email: '<EMAIL>', status: '活跃', commission: '¥280', date: '2024-01-15' },
    { name: '李四', email: '<EMAIL>', status: '新用户', commission: '¥150', date: '2024-01-14' },
    { name: '王五', email: '<EMAIL>', status: '活跃', commission: '¥320', date: '2024-01-13' },
  ];

  return (
    <Box sx={{ minHeight: '100vh', backgroundColor: '#f5f5f5' }}>
      {/* 页面标题 */}
      <Box sx={{ p: 3, pb: 2 }}>
        <Typography variant="h4" sx={{ fontWeight: 700, mb: 1, display: 'flex', alignItems: 'center' }}>
          <Campaign sx={{ mr: 2, color: 'warning.main' }} />
          代理商控制中心
        </Typography>
        <Typography variant="body1" color="text.secondary">
          管理您的推广业务和佣金收益
        </Typography>
      </Box>

      {/* 大卡片包装内容 - 固定高度 */}
      <Box sx={{ p: 3, pt: 0 }}>
        <Card sx={{
          borderRadius: 3,
          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
          border: '1px solid',
          borderColor: 'divider',
          maxHeight: 'calc(100vh - 200px)',
          overflow: 'hidden',
        }}>
          <CardContent sx={{
            p: 4,
            maxHeight: 'calc(100vh - 240px)',
            overflowY: 'auto',
            '&::-webkit-scrollbar': {
              width: '8px',
            },
            '&::-webkit-scrollbar-track': {
              backgroundColor: 'rgba(0,0,0,0.1)',
              borderRadius: '4px',
            },
            '&::-webkit-scrollbar-thumb': {
              backgroundColor: 'rgba(0,0,0,0.3)',
              borderRadius: '4px',
              '&:hover': {
                backgroundColor: 'rgba(0,0,0,0.5)',
              },
            },
          }}>

        {/* 统计卡片 */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          {statCards.map((stat) => (
            <Grid item xs={12} sm={6} md={3} key={stat.title}>
              <Card sx={{ borderRadius: 2, height: '100%' }}>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                    <Avatar sx={{ backgroundColor: `${stat.color}.light`, color: `${stat.color}.main` }}>
                      {stat.icon}
                    </Avatar>
                    <Chip 
                      label={stat.change} 
                      size="small" 
                      color={stat.change.startsWith('+') ? 'success' : 'error'}
                    />
                  </Box>
                  <Typography variant="h4" sx={{ fontWeight: 700, mb: 1 }}>
                    {stat.value}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {stat.title}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>

        {/* 快速操作 */}
        <Card sx={{ borderRadius: 3, mb: 4 }}>
          <CardContent sx={{ p: 3 }}>
            <Typography variant="h6" sx={{ fontWeight: 600, mb: 3, display: 'flex', alignItems: 'center' }}>
              <Dashboard sx={{ mr: 1, color: 'warning.main' }} />
              快速操作
            </Typography>
            <Grid container spacing={2}>
              {quickActions.map((action) => (
                <Grid item xs={12} sm={6} md={3} key={action.id}>
                  <Card
                    sx={{
                      borderRadius: 2,
                      border: '1px solid',
                      borderColor: 'divider',
                      background: action.gradient,
                      color: 'white',
                      cursor: 'pointer',
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        transform: 'translateY(-4px)',
                        boxShadow: '0 8px 25px rgba(0,0,0,0.15)',
                      }
                    }}
                    onClick={action.action}
                  >
                    <CardContent sx={{ p: 2.5, textAlign: 'center' }}>
                      <Avatar sx={{
                        backgroundColor: 'rgba(255,255,255,0.2)',
                        color: 'white',
                        width: 48,
                        height: 48,
                        mx: 'auto',
                        mb: 2
                      }}>
                        {action.icon}
                      </Avatar>
                      <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                        {action.title}
                      </Typography>
                      <Typography variant="body2" sx={{ opacity: 0.9 }}>
                        {action.description}
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </CardContent>
        </Card>

        {/* 最近推荐用户 */}
        <Card sx={{ borderRadius: 3 }}>
          <CardContent sx={{ p: 3 }}>
            <Typography variant="h6" sx={{ fontWeight: 600, mb: 3 }}>
              最近推荐用户
            </Typography>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>用户名</TableCell>
                    <TableCell>邮箱</TableCell>
                    <TableCell>状态</TableCell>
                    <TableCell>佣金</TableCell>
                    <TableCell>注册日期</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {recentReferrals.map((user, index) => (
                    <TableRow key={index}>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Avatar sx={{ width: 32, height: 32, mr: 2, fontSize: '0.875rem' }}>
                            {user.name.charAt(0)}
                          </Avatar>
                          {user.name}
                        </Box>
                      </TableCell>
                      <TableCell>{user.email}</TableCell>
                      <TableCell>
                        <Chip 
                          label={user.status} 
                          size="small" 
                          color={user.status === '活跃' ? 'success' : 'primary'}
                        />
                      </TableCell>
                      <TableCell>
                        <Typography sx={{ fontWeight: 600, color: 'success.main' }}>
                          {user.commission}
                        </Typography>
                      </TableCell>
                      <TableCell>{user.date}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>
          </CardContent>
        </Card>
      </Box>
    </Box>
  );
}

export default AgentDashboard;
