import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Tabs,
  Tab,
  TextField,
  Button,
  Grid,
  Card,
  CardContent,
  Switch,
  FormControlLabel,
  Divider,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  InputAdornment,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  ListItemIcon,
  Paper,
  CircularProgress,
  Skeleton,
} from '@mui/material';
import {
  Person,
  Security,
  Notifications,
  Save,
  Edit,
  ContentCopy,
  Visibility,
  VisibilityOff,
  Add,
  Delete,
  Email,
  Phone,
  LocationOn,
  Business,
  Description,
  CheckCircle,
  Warning,
  Info,
  Refresh,
  Key,
  Api,
  Settings,
  Payment,
  AccountBalance,
  CreditCard,
  Receipt,
  Lock,
  Smartphone,
  Devices,
  History,
  Domain,
  CalendarToday,
  Work,
  Group,
} from '@mui/icons-material';
import agentService from '../../services/agentService';

function AccountSettings() {
  // Component state
  const [currentTab, setCurrentTab] = useState(0);
  const [showApiKey, setShowApiKey] = useState(false);
  const [openApiDialog, setOpenApiDialog] = useState(false);
  const [openPaymentDialog, setOpenPaymentDialog] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // 账户信息数据（只读）
  const [accountInfo, setAccountInfo] = useState(null);

  // 获取代理商信息
  const fetchAgentInfo = async () => {
    try {
      setLoading(true);
      setError(null);
      console.log('开始获取代理商信息...');

      const response = await agentService.getAgentInfo();
      console.log('代理商信息响应:', response);

      // 处理不同的响应格式
      let data = null;
      if (response && response.success && response.data) {
        data = response.data;
      } else if (response && response.data) {
        data = response.data;
      } else if (response && response.success !== false) {
        data = response;
      }

      if (data) {
        // 格式化代理商信息数据
        const formattedInfo = {
          name: data.agent_name || data.name || '-',
          type: data.agent_type || data.type || '-',
          idNumber: data.id_number || data.idNumber || '-',
          businessType: data.business_type || data.businessType || '-',
          scale: data.team_size || data.scale || '-',
          address: data.business_address || data.address || '-',
          phone: data.contact_phone || data.phone || '-',
          email: data.contact_email || data.email || '-',
          website: data.official_website || data.website || '-',
          description: data.business_description || data.description || '-',
          foundDate: data.establishment_date || data.foundDate || '-',
          bankAccount: data.bank_account || data.bankAccount || '-',
          verificationStatus: data.verification_status || 'pending',
          createdAt: data.created_at || '-',
          updatedAt: data.updated_at || '-'
        };

        console.log('格式化后的代理商信息:', formattedInfo);
        setAccountInfo(formattedInfo);
      } else {
        console.warn('无法解析代理商信息响应:', response);
        setError('无法获取代理商信息');
      }
    } catch (error) {
      console.error('获取代理商信息失败:', error);
      setError('获取代理商信息失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 组件挂载时获取代理商信息
  useEffect(() => {
    fetchAgentInfo();
  }, []);

  // API密钥数据
  const [apiKey] = useState({
    id: 1,
    name: 'API密钥',
    key: 'ag_live_9876543210fedcba',
    status: 'active',
    createDate: '2024-01-05',
    lastUsed: '2024-01-16 15:45',
    requests: 12456
  });

  // 通知设置数据
  const [notificationSettings, setNotificationSettings] = useState({
    email: {
      newTask: true,
      taskComplete: true,
      payment: true,
      monthlyReport: false,
      promotion: false,
    },
    sms: {
      newTask: false,
      taskComplete: false,
      payment: true,
      monthlyReport: false,
      promotion: false,
    }
  });

  // 支付方式数据
  const [paymentMethods, setPaymentMethods] = useState([
    {
      id: 1,
      type: 'bank',
      name: '建设银行',
      account: '**** **** **** 8888',
      createdAt: '2024-01-01',
      status: 'active',
      isDefault: true
    },
    {
      id: 2,
      type: 'alipay',
      name: '支付宝',
      account: '138****8888',
      createdAt: '2024-01-10',
      status: 'active',
      isDefault: false
    }
  ]);

  // 登录记录数据
  const [loginHistory] = useState([
    { id: 1, device: 'Chrome - Windows', ip: '***********', location: '北京', time: '2024-01-16 14:30', current: true },
    { id: 2, device: 'Safari - MacOS', ip: '***********', location: '上海', time: '2024-01-15 10:20', current: false },
    { id: 3, device: 'Mobile - iOS', ip: '***********', location: '北京', time: '2024-01-14 18:45', current: false },
  ]);

  // 新支付方式表单
  const [newPaymentMethod, setNewPaymentMethod] = useState({
    type: 'bank',
    name: '',
    account: '',
  });

  const handleTabChange = (event, newValue) => {
    setCurrentTab(newValue);
  };

  const handleCopyApiKey = (key) => {
    navigator.clipboard.writeText(key);
    // 这里可以添加提示消息
  };



  const handleNotificationChange = (channel, type) => {
    setNotificationSettings(prev => ({
      ...prev,
      [channel]: {
        ...prev[channel],
        [type]: !prev[channel][type]
      }
    }));
  };

  const handleAddPaymentMethod = () => {
    const newMethod = {
      id: Date.now(),
      ...newPaymentMethod,
      createdAt: new Date().toISOString().split('T')[0],
      status: 'active',
      isDefault: paymentMethods.length === 0
    };
    
    setPaymentMethods([...paymentMethods, newMethod]);
    setOpenPaymentDialog(false);
    setNewPaymentMethod({ type: 'bank', name: '', account: '' });
  };

  const handleSetDefaultPayment = (id) => {
    setPaymentMethods(paymentMethods.map(method => ({
      ...method,
      isDefault: method.id === id
    })));
  };

  const handleDeletePayment = (id) => {
    setPaymentMethods(paymentMethods.filter(method => method.id !== id));
  };

  // 渲染账户信息标签页
  const renderAccountInfo = () => {
    if (loading) {
      return (
        <Box>
          <Box sx={{ mb: 3 }}>
            <Typography variant="h6" sx={{ fontWeight: 600, color: '#1a1a1a' }}>
              账户基本信息
            </Typography>
          </Box>
          <Card sx={{ borderRadius: 2, border: '1px solid #e5e7eb', boxShadow: 'none' }}>
            <CardContent sx={{ p: 4 }}>
              <Grid container spacing={3}>
                {Array.from({ length: 8 }).map((_, index) => (
                  <Grid item xs={12} md={6} key={index}>
                    <Skeleton variant="rectangular" height={56} />
                  </Grid>
                ))}
                <Grid item xs={12}>
                  <Skeleton variant="rectangular" height={120} />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Box>
      );
    }

    if (error) {
      return (
        <Box>
          <Box sx={{ mb: 3 }}>
            <Typography variant="h6" sx={{ fontWeight: 600, color: '#1a1a1a' }}>
              账户基本信息
            </Typography>
          </Box>
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        </Box>
      );
    }

    if (!accountInfo) {
      return (
        <Box>
          <Alert severity="info">
            暂无账户信息
          </Alert>
        </Box>
      );
    }

    return (
    <Box>
      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h6" sx={{ fontWeight: 600, color: '#1a1a1a' }}>
          账户基本信息
        </Typography>
        <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
          {accountInfo.verificationStatus && accountInfo.verificationStatus !== 'unknown' && (
            <Chip
              label={
                accountInfo.verificationStatus === 'approved' ? '已认证' :
                accountInfo.verificationStatus === 'pending' ? '待审核' :
                accountInfo.verificationStatus === 'rejected' ? '审核未通过' : ''
              }
              color={
                accountInfo.verificationStatus === 'approved' ? 'success' :
                accountInfo.verificationStatus === 'pending' ? 'warning' :
                accountInfo.verificationStatus === 'rejected' ? 'error' : 'default'
              }
              size="small"
              icon={
                accountInfo.verificationStatus === 'approved' ? <CheckCircle /> :
                accountInfo.verificationStatus === 'pending' ? <Info /> :
                accountInfo.verificationStatus === 'rejected' ? <Warning /> : null
              }
            />
          )}
        </Box>
      </Box>

      <Card sx={{ borderRadius: 2, border: '1px solid #e5e7eb', boxShadow: 'none' }}>
        <CardContent sx={{ p: 4 }}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="姓名/企业名称"
                value={accountInfo.name || '-'}
                InputProps={{
                  readOnly: true,
                  startAdornment: (
                    <InputAdornment position="start">
                      <Person sx={{ color: '#9ca3af' }} />
                    </InputAdornment>
                  ),
                }}
                variant="filled"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="账户类型"
                value={accountInfo.type || '-'}
                InputProps={{
                  readOnly: true,
                  startAdornment: (
                    <InputAdornment position="start">
                      <Work sx={{ color: '#9ca3af' }} />
                    </InputAdornment>
                  ),
                }}
                variant="filled"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="身份证号/统一社会信用代码"
                value={accountInfo.idNumber || '-'}
                InputProps={{
                  readOnly: true,
                  startAdornment: (
                    <InputAdornment position="start">
                      <CreditCard sx={{ color: '#9ca3af' }} />
                    </InputAdornment>
                  ),
                }}
                variant="filled"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="业务类型"
                value={accountInfo.businessType || '-'}
                InputProps={{
                  readOnly: true,
                  startAdornment: (
                    <InputAdornment position="start">
                      <Work sx={{ color: '#9ca3af' }} />
                    </InputAdornment>
                  ),
                }}
                variant="filled"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="团队规模"
                value={accountInfo.scale || '-'}
                InputProps={{
                  readOnly: true,
                  startAdornment: (
                    <InputAdornment position="start">
                      <Group sx={{ color: '#9ca3af' }} />
                    </InputAdornment>
                  ),
                }}
                variant="filled"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="注册日期"
                value={accountInfo.foundDate || '-'}
                InputProps={{
                  readOnly: true,
                  startAdornment: (
                    <InputAdornment position="start">
                      <CalendarToday sx={{ color: '#9ca3af' }} />
                    </InputAdornment>
                  ),
                }}
                variant="filled"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="联系地址"
                value={accountInfo.address || '-'}
                InputProps={{
                  readOnly: true,
                  startAdornment: (
                    <InputAdornment position="start">
                      <LocationOn sx={{ color: '#9ca3af' }} />
                    </InputAdornment>
                  ),
                }}
                variant="filled"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="联系电话"
                value={accountInfo.phone || '-'}
                InputProps={{
                  readOnly: true,
                  startAdornment: (
                    <InputAdornment position="start">
                      <Phone sx={{ color: '#9ca3af' }} />
                    </InputAdornment>
                  ),
                }}
                variant="filled"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="邮箱"
                value={accountInfo.email || '-'}
                InputProps={{
                  readOnly: true,
                  startAdornment: (
                    <InputAdornment position="start">
                      <Email sx={{ color: '#9ca3af' }} />
                    </InputAdornment>
                  ),
                }}
                variant="filled"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="官方网站"
                value={accountInfo.website || '-'}
                InputProps={{
                  readOnly: true,
                  startAdornment: (
                    <InputAdornment position="start">
                      <Domain sx={{ color: '#9ca3af' }} />
                    </InputAdornment>
                  ),
                }}
                variant="filled"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="银行账户"
                value={accountInfo.bankAccount || '-'}
                InputProps={{
                  readOnly: true,
                  startAdornment: (
                    <InputAdornment position="start">
                      <AccountBalance sx={{ color: '#9ca3af' }} />
                    </InputAdornment>
                  ),
                }}
                variant="filled"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="业务简介"
                value={accountInfo.description || '-'}
                InputProps={{
                  readOnly: true,
                  startAdornment: (
                    <InputAdornment position="start" sx={{ alignSelf: 'flex-start', mt: 1 }}>
                      <Description sx={{ color: '#9ca3af' }} />
                    </InputAdornment>
                  ),
                }}
                multiline
                rows={4}
                variant="filled"
              />
            </Grid>
          </Grid>

          {/* 更新信息 */}
          <Box sx={{ mt: 4, p: 2, backgroundColor: '#f9fafb', borderRadius: 1 }}>
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <Typography variant="caption" sx={{ color: '#6b7280' }}>
                  <strong>创建时间：</strong>
                  {accountInfo.createdAt ? new Date(accountInfo.createdAt).toLocaleString('zh-CN') : '-'}
                </Typography>
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography variant="caption" sx={{ color: '#6b7280' }}>
                  <strong>最后更新：</strong>
                  {accountInfo.updatedAt ? new Date(accountInfo.updatedAt).toLocaleString('zh-CN') : '-'}
                </Typography>
              </Grid>
            </Grid>
          </Box>
        </CardContent>
      </Card>
    </Box>
    );
  };

  // 渲染API管理标签页
  const renderApiManagement = () => (
    <Box>
      <Box sx={{ mb: 3 }}>
        <Typography variant="h6" sx={{ fontWeight: 600, color: '#1a1a1a', mb: 0.5 }}>
          API密钥管理
        </Typography>
        <Typography variant="body2" sx={{ color: '#6b7280' }}>
          使用API密钥调用平台接口服务
        </Typography>
      </Box>

      <Alert severity="info" sx={{ mb: 3 }}>
        <Typography variant="body2">
          请妥善保管您的API密钥，不要在公开场合暴露。如果密钥泄露，请立即重新生成。
        </Typography>
      </Alert>

      {/* API密钥管理 */}
      <Card sx={{ borderRadius: 2, border: '1px solid #e5e7eb', boxShadow: 'none', p: 3, mb: 3 }}>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Key sx={{ fontSize: 20, color: '#6b7280' }} />
                <Typography variant="subtitle1" sx={{ fontWeight: 600, color: '#1a1a1a' }}>
                  {apiKey.name}
                </Typography>
                <Chip
                  label="启用"
                  size="small"
                  sx={{
                    backgroundColor: '#dcfce7',
                    color: '#166534',
                    fontWeight: 500
                  }}
                />
              </Box>
              <Button
                variant="outlined"
                startIcon={<Refresh />}
                size="small"
                onClick={() => {}}
                sx={{ 
                  borderColor: '#e5e7eb',
                  color: '#6b7280',
                  '&:hover': {
                    borderColor: '#d1d5db',
                    backgroundColor: '#f9fafb'
                  }
                }}
              >
                重新生成
              </Button>
            </Box>
            
            <Box sx={{ backgroundColor: '#f9fafb', p: 2, borderRadius: 1, mb: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                <Typography variant="body2" sx={{ color: '#6b7280', fontWeight: 500 }}>
                  密钥值：
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Typography
                  variant="body1"
                  sx={{
                    fontFamily: 'monospace',
                    fontSize: '0.95rem',
                    color: '#374151',
                    flex: 1
                  }}
                >
                  {showApiKey ? apiKey.key : '••••••••••••••••••••••••••••••••'}
                </Typography>
                <IconButton
                  size="small"
                  onClick={() => setShowApiKey(!showApiKey)}
                  sx={{ color: '#6b7280' }}
                >
                  {showApiKey ? <VisibilityOff fontSize="small" /> : <Visibility fontSize="small" />}
                </IconButton>
                <IconButton
                  size="small"
                  onClick={() => handleCopyApiKey(apiKey.key)}
                  sx={{ color: '#6b7280' }}
                >
                  <ContentCopy fontSize="small" />
                </IconButton>
              </Box>
            </Box>

            <Grid container spacing={2}>
              <Grid item xs={12} sm={4}>
                <Typography variant="body2" sx={{ color: '#6b7280', mb: 0.5 }}>
                  创建时间
                </Typography>
                <Typography variant="body1" sx={{ color: '#374151', fontWeight: 500 }}>
                  {apiKey.createDate}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={4}>
                <Typography variant="body2" sx={{ color: '#6b7280', mb: 0.5 }}>
                  最后使用
                </Typography>
                <Typography variant="body1" sx={{ color: '#374151', fontWeight: 500 }}>
                  {apiKey.lastUsed}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={4}>
                <Typography variant="body2" sx={{ color: '#6b7280', mb: 0.5 }}>
                  调用次数
                </Typography>
                <Typography variant="body1" sx={{ color: '#374151', fontWeight: 500 }}>
                  {apiKey.requests.toLocaleString()} 次
                </Typography>
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      </Card>
    </Box>
  );

  // 渲染通知设置标签页
  const renderNotificationSettings = () => (
    <Box>
      <Box sx={{ mb: 3 }}>
        <Typography variant="h6" sx={{ fontWeight: 600, color: '#1a1a1a', mb: 0.5 }}>
          通知设置
        </Typography>
        <Typography variant="body2" sx={{ color: '#6b7280' }}>
          管理您接收系统通知的方式和类型
        </Typography>
      </Box>

      <Grid container spacing={3}>
        {/* 左侧：通知设置卡片 */}
        <Grid item xs={12} md={8}>
          <Grid container spacing={3}>
            {/* 邮件通知 */}
            <Grid item xs={12}>
              <Card sx={{ borderRadius: 2, border: '1px solid #e5e7eb', boxShadow: 'none', height: '100%' }}>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 3 }}>
                    <Email sx={{ color: '#3b82f6' }} />
                    <Typography variant="h6" sx={{ fontWeight: 600, color: '#1a1a1a' }}>
                      邮件通知
                    </Typography>
                  </Box>
                  <List>
                    <ListItem sx={{ px: 0 }}>
                      <ListItemText
                        primary="新任务通知"
                        secondary="收到新任务时通知"
                        primaryTypographyProps={{ variant: 'body2', fontWeight: 500 }}
                        secondaryTypographyProps={{ variant: 'caption', color: '#6b7280' }}
                      />
                      <ListItemSecondaryAction>
                        <Switch
                          checked={notificationSettings.email.newTask}
                          onChange={() => handleNotificationChange('email', 'newTask')}
                          color="primary"
                        />
                      </ListItemSecondaryAction>
                    </ListItem>
                    <ListItem sx={{ px: 0 }}>
                      <ListItemText
                        primary="任务完成"
                        secondary="任务完成时通知"
                        primaryTypographyProps={{ variant: 'body2', fontWeight: 500 }}
                        secondaryTypographyProps={{ variant: 'caption', color: '#6b7280' }}
                      />
                      <ListItemSecondaryAction>
                        <Switch
                          checked={notificationSettings.email.taskComplete}
                          onChange={() => handleNotificationChange('email', 'taskComplete')}
                          color="primary"
                        />
                      </ListItemSecondaryAction>
                    </ListItem>
                    <ListItem sx={{ px: 0 }}>
                      <ListItemText
                        primary="支付通知"
                        secondary="收款、提现通知"
                        primaryTypographyProps={{ variant: 'body2', fontWeight: 500 }}
                        secondaryTypographyProps={{ variant: 'caption', color: '#6b7280' }}
                      />
                      <ListItemSecondaryAction>
                        <Switch
                          checked={notificationSettings.email.payment}
                          onChange={() => handleNotificationChange('email', 'payment')}
                          color="primary"
                        />
                      </ListItemSecondaryAction>
                    </ListItem>
                    <ListItem sx={{ px: 0 }}>
                      <ListItemText
                        primary="月度报告"
                        secondary="每月数据分析报告"
                        primaryTypographyProps={{ variant: 'body2', fontWeight: 500 }}
                        secondaryTypographyProps={{ variant: 'caption', color: '#6b7280' }}
                      />
                      <ListItemSecondaryAction>
                        <Switch
                          checked={notificationSettings.email.monthlyReport}
                          onChange={() => handleNotificationChange('email', 'monthlyReport')}
                          color="primary"
                        />
                      </ListItemSecondaryAction>
                    </ListItem>
                    <ListItem sx={{ px: 0 }}>
                      <ListItemText
                        primary="营销推广"
                        secondary="产品优惠、活动信息"
                        primaryTypographyProps={{ variant: 'body2', fontWeight: 500 }}
                        secondaryTypographyProps={{ variant: 'caption', color: '#6b7280' }}
                      />
                      <ListItemSecondaryAction>
                        <Switch
                          checked={notificationSettings.email.promotion}
                          onChange={() => handleNotificationChange('email', 'promotion')}
                          color="primary"
                        />
                      </ListItemSecondaryAction>
                    </ListItem>
                  </List>
                </CardContent>
              </Card>
            </Grid>

            {/* 短信通知 */}
            <Grid item xs={12}>
              <Card sx={{ borderRadius: 2, border: '1px solid #e5e7eb', boxShadow: 'none', height: '100%' }}>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 3 }}>
                    <Phone sx={{ color: '#10b981' }} />
                    <Typography variant="h6" sx={{ fontWeight: 600, color: '#1a1a1a' }}>
                      短信通知
                    </Typography>
                  </Box>
                  <List>
                    <ListItem sx={{ px: 0 }}>
                      <ListItemText
                        primary="新任务通知"
                        secondary="收到新任务时通知"
                        primaryTypographyProps={{ variant: 'body2', fontWeight: 500 }}
                        secondaryTypographyProps={{ variant: 'caption', color: '#6b7280' }}
                      />
                      <ListItemSecondaryAction>
                        <Switch
                          checked={notificationSettings.sms.newTask}
                          onChange={() => handleNotificationChange('sms', 'newTask')}
                          color="primary"
                        />
                      </ListItemSecondaryAction>
                    </ListItem>
                    <ListItem sx={{ px: 0 }}>
                      <ListItemText
                        primary="任务完成"
                        secondary="任务完成时通知"
                        primaryTypographyProps={{ variant: 'body2', fontWeight: 500 }}
                        secondaryTypographyProps={{ variant: 'caption', color: '#6b7280' }}
                      />
                      <ListItemSecondaryAction>
                        <Switch
                          checked={notificationSettings.sms.taskComplete}
                          onChange={() => handleNotificationChange('sms', 'taskComplete')}
                          color="primary"
                        />
                      </ListItemSecondaryAction>
                    </ListItem>
                    <ListItem sx={{ px: 0 }}>
                      <ListItemText
                        primary="支付通知"
                        secondary="收款、提现通知"
                        primaryTypographyProps={{ variant: 'body2', fontWeight: 500 }}
                        secondaryTypographyProps={{ variant: 'caption', color: '#6b7280' }}
                      />
                      <ListItemSecondaryAction>
                        <Switch
                          checked={notificationSettings.sms.payment}
                          onChange={() => handleNotificationChange('sms', 'payment')}
                          color="primary"
                        />
                      </ListItemSecondaryAction>
                    </ListItem>
                    <ListItem sx={{ px: 0 }}>
                      <ListItemText
                        primary="月度报告"
                        secondary="每月数据分析报告"
                        primaryTypographyProps={{ variant: 'body2', fontWeight: 500 }}
                        secondaryTypographyProps={{ variant: 'caption', color: '#6b7280' }}
                      />
                      <ListItemSecondaryAction>
                        <Switch
                          checked={notificationSettings.sms.monthlyReport}
                          onChange={() => handleNotificationChange('sms', 'monthlyReport')}
                          color="primary"
                        />
                      </ListItemSecondaryAction>
                    </ListItem>
                    <ListItem sx={{ px: 0 }}>
                      <ListItemText
                        primary="营销推广"
                        secondary="产品优惠、活动信息"
                        primaryTypographyProps={{ variant: 'body2', fontWeight: 500 }}
                        secondaryTypographyProps={{ variant: 'caption', color: '#6b7280' }}
                      />
                      <ListItemSecondaryAction>
                        <Switch
                          checked={notificationSettings.sms.promotion}
                          onChange={() => handleNotificationChange('sms', 'promotion')}
                          color="primary"
                        />
                      </ListItemSecondaryAction>
                    </ListItem>
                  </List>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Grid>

        {/* 右侧：通知说明 */}
        <Grid item xs={12} md={4}>
          <Card sx={{ borderRadius: 2, border: '1px solid #e5e7eb', boxShadow: 'none', backgroundColor: '#f9fafb' }}>
            <CardContent>
              <Typography variant="h6" sx={{ fontWeight: 600, color: '#1a1a1a', mb: 3 }}>
                通知说明
              </Typography>
              
              <Box sx={{ mb: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1.5, mb: 2 }}>
                  <Email sx={{ fontSize: 24, color: '#3b82f6', mt: 0.5 }} />
                  <Box>
                    <Typography variant="subtitle2" sx={{ fontWeight: 600, color: '#374151', mb: 1 }}>
                      邮件通知
                    </Typography>
                    <Typography variant="body2" sx={{ color: '#6b7280', lineHeight: 1.6 }}>
                      通过您的注册邮箱接收通知，支持详细内容和附件。适合接收详细报告和非紧急通知。
                    </Typography>
                  </Box>
                </Box>
              </Box>

              <Box sx={{ mb: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1.5, mb: 2 }}>
                  <Phone sx={{ fontSize: 24, color: '#10b981', mt: 0.5 }} />
                  <Box>
                    <Typography variant="subtitle2" sx={{ fontWeight: 600, color: '#374151', mb: 1 }}>
                      短信通知
                    </Typography>
                    <Typography variant="body2" sx={{ color: '#6b7280', lineHeight: 1.6 }}>
                      紧急重要通知实时推送，确保及时获得关键信息。适合支付告警和紧急通知。
                    </Typography>
                  </Box>
                </Box>
              </Box>

              <Divider sx={{ my: 3 }} />

              <Box>
                <Typography variant="subtitle2" sx={{ fontWeight: 600, color: '#374151', mb: 2 }}>
                  注意事项
                </Typography>
                <List dense>
                  <ListItem sx={{ px: 0, py: 0.5 }}>
                    <ListItemIcon sx={{ minWidth: 28 }}>
                      <CheckCircle sx={{ fontSize: 16, color: '#10b981' }} />
                    </ListItemIcon>
                    <ListItemText 
                      primary="通知设置实时生效"
                      primaryTypographyProps={{ variant: 'caption', color: '#6b7280' }}
                    />
                  </ListItem>
                  <ListItem sx={{ px: 0, py: 0.5 }}>
                    <ListItemIcon sx={{ minWidth: 28 }}>
                      <CheckCircle sx={{ fontSize: 16, color: '#10b981' }} />
                    </ListItemIcon>
                    <ListItemText 
                      primary="短信通知可能产生额外费用"
                      primaryTypographyProps={{ variant: 'caption', color: '#6b7280' }}
                    />
                  </ListItem>
                  <ListItem sx={{ px: 0, py: 0.5 }}>
                    <ListItemIcon sx={{ minWidth: 28 }}>
                      <CheckCircle sx={{ fontSize: 16, color: '#10b981' }} />
                    </ListItemIcon>
                    <ListItemText 
                      primary="确保联系方式正确有效"
                      primaryTypographyProps={{ variant: 'caption', color: '#6b7280' }}
                    />
                  </ListItem>
                </List>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>
        <Button
          variant="contained"
          startIcon={<Save />}
          sx={{ backgroundColor: '#3b82f6', '&:hover': { backgroundColor: '#2563eb' } }}
        >
          保存通知设置
        </Button>
      </Box>
    </Box>
  );

  // 渲染支付方式标签页
  const renderPaymentMethods = () => (
    <Box>
      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Box>
          <Typography variant="h6" sx={{ fontWeight: 600, color: '#1a1a1a', mb: 0.5 }}>
            支付方式管理
          </Typography>
          <Typography variant="body2" sx={{ color: '#6b7280' }}>
            管理您的收款方式，用于接收任务报酬和提现
          </Typography>
        </Box>
        <Button
          variant="contained"
          startIcon={<Add />}
          onClick={() => setOpenPaymentDialog(true)}
          sx={{ backgroundColor: '#3b82f6', '&:hover': { backgroundColor: '#2563eb' } }}
        >
          添加支付方式
        </Button>
      </Box>

      <Grid container spacing={3}>
        {paymentMethods.map((method) => (
          <Grid item xs={12} md={6} key={method.id}>
            <Card sx={{ borderRadius: 2, border: '1px solid #e5e7eb', boxShadow: 'none', position: 'relative' }}>
              {method.isDefault && (
                <Chip 
                  label="默认" 
                  size="small" 
                  sx={{
                    position: 'absolute',
                    top: 16,
                    right: 16,
                    backgroundColor: '#dcfce7',
                    color: '#166534',
                    fontWeight: 500
                  }}
                />
              )}
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
                  <Box sx={{
                    width: 48,
                    height: 48,
                    borderRadius: '12px',
                    backgroundColor: method.type === 'bank' ? '#dbeafe' : '#fef3c7',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    {method.type === 'bank' ? (
                      <AccountBalance sx={{ color: '#3b82f6' }} />
                    ) : (
                      <Payment sx={{ color: '#f59e0b' }} />
                    )}
                  </Box>
                  <Box>
                    <Typography variant="subtitle1" sx={{ fontWeight: 600, color: '#1a1a1a' }}>
                      {method.name}
                    </Typography>
                    <Typography variant="body2" sx={{ color: '#6b7280' }}>
                      {method.account}
                    </Typography>
                  </Box>
                </Box>

                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
                  <Box>
                    <Typography variant="caption" sx={{ color: '#9ca3af' }}>
                      添加时间
                    </Typography>
                    <Typography variant="body2" sx={{ color: '#374151', fontWeight: 500 }}>
                      {method.createdAt}
                    </Typography>
                  </Box>
                  <Box>
                    <Typography variant="caption" sx={{ color: '#9ca3af' }}>
                      状态
                    </Typography>
                    <Typography variant="body2" sx={{ color: '#374151', fontWeight: 500 }}>
                      {method.status === 'active' ? '正常' : '停用'}
                    </Typography>
                  </Box>
                </Box>

                <Box sx={{ display: 'flex', gap: 1 }}>
                  {!method.isDefault && (
                    <Button
                      size="small"
                      variant="outlined"
                      onClick={() => handleSetDefaultPayment(method.id)}
                      sx={{
                        borderColor: '#e5e7eb',
                        color: '#6b7280',
                        '&:hover': {
                          borderColor: '#d1d5db',
                          backgroundColor: '#f9fafb'
                        }
                      }}
                    >
                      设为默认
                    </Button>
                  )}
                  <Button
                    size="small"
                    variant="outlined"
                    sx={{
                      borderColor: '#e5e7eb',
                      color: '#6b7280',
                      '&:hover': {
                        borderColor: '#d1d5db',
                        backgroundColor: '#f9fafb'
                      }
                    }}
                  >
                    编辑
                  </Button>
                  <Button
                    size="small"
                    variant="outlined"
                    color="error"
                    onClick={() => handleDeletePayment(method.id)}
                    disabled={method.isDefault}
                  >
                    删除
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      <Alert severity="info" sx={{ mt: 3 }}>
        <Typography variant="body2">
          提示：默认支付方式将用于接收所有任务报酬。请确保支付信息准确无误。
        </Typography>
      </Alert>
    </Box>
  );

  return (
    <Box sx={{ width: '100%', backgroundColor: 'white', minHeight: '100vh' }}>
      {/* 顶部标题区域 */}
      <Box sx={{
        borderBottom: '1px solid #f0f0f0',
        backgroundColor: 'white'
      }}>
        <Container maxWidth="xl">
          <Box sx={{ py: 5 }}>
            <Typography variant="h3" sx={{
              fontWeight: 700,
              color: '#1a1a1a',
              mb: 1,
              fontSize: { xs: '2rem', md: '2.5rem' }
            }}>
              账户设置
            </Typography>
            <Typography variant="body1" sx={{
              color: '#6b7280',
              fontSize: '1.125rem'
            }}>
              管理您的账户信息、安全设置和通知偏好
            </Typography>
          </Box>
        </Container>
      </Box>

      {/* 标签导航 */}
      <Box sx={{
        borderBottom: '1px solid #e5e7eb',
        backgroundColor: 'white'
      }}>
        <Container maxWidth="xl">
          <Tabs
            value={currentTab}
            onChange={handleTabChange}
            sx={{
              '& .MuiTab-root': {
                textTransform: 'none',
                fontWeight: 500,
                fontSize: '0.95rem',
                color: '#6b7280',
                py: 2,
                px: 3,
                '&.Mui-selected': {
                  color: '#3b82f6',
                },
              },
              '& .MuiTabs-indicator': {
                backgroundColor: '#3b82f6',
                height: 3,
              },
            }}
          >
            <Tab icon={<Person sx={{ fontSize: 20 }} />} iconPosition="start" label="账户信息" />
            <Tab icon={<Key sx={{ fontSize: 20 }} />} iconPosition="start" label="API管理" />
            <Tab icon={<Notifications sx={{ fontSize: 20 }} />} iconPosition="start" label="通知设置" />
            <Tab icon={<Payment sx={{ fontSize: 20 }} />} iconPosition="start" label="支付方式" />
          </Tabs>
        </Container>
      </Box>

      {/* 内容区域 */}
      <Box sx={{ backgroundColor: 'white' }}>
        <Container maxWidth="xl">
          <Box sx={{ py: 4 }}>
            {currentTab === 0 && renderAccountInfo()}
            {currentTab === 1 && renderApiManagement()}
            {currentTab === 2 && renderNotificationSettings()}
            {currentTab === 3 && renderPaymentMethods()}
          </Box>
        </Container>
      </Box>

      {/* 添加支付方式对话框 */}
      <Dialog
        open={openPaymentDialog}
        onClose={() => setOpenPaymentDialog(false)}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 2,
            boxShadow: '0 25px 50px -12px rgb(0 0 0 / 0.25)'
          }
        }}
      >
        <DialogTitle sx={{ borderBottom: '1px solid #e5e7eb', pb: 2 }}>
          <Typography variant="h6" sx={{ fontWeight: 600, color: '#1f2937' }}>
            添加支付方式
          </Typography>
        </DialogTitle>
        <DialogContent sx={{ pt: 3 }}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>支付类型</InputLabel>
                <Select
                  value={newPaymentMethod.type}
                  label="支付类型"
                  onChange={(e) => setNewPaymentMethod({ ...newPaymentMethod, type: e.target.value })}
                >
                  <MenuItem value="bank">银行卡</MenuItem>
                  <MenuItem value="alipay">支付宝</MenuItem>
                  <MenuItem value="wechat">微信支付</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label={newPaymentMethod.type === 'bank' ? '开户银行' : '账户名称'}
                value={newPaymentMethod.name}
                onChange={(e) => setNewPaymentMethod({ ...newPaymentMethod, name: e.target.value })}
                placeholder={newPaymentMethod.type === 'bank' ? '例如：建设银行' : '例如：支付宝账户'}
                required
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label={newPaymentMethod.type === 'bank' ? '银行卡号' : '账户号码'}
                value={newPaymentMethod.account}
                onChange={(e) => setNewPaymentMethod({ ...newPaymentMethod, account: e.target.value })}
                placeholder={newPaymentMethod.type === 'bank' ? '请输入银行卡号' : '请输入账户号码'}
                required
              />
            </Grid>
          </Grid>
          <Alert severity="info" sx={{ mt: 3 }}>
            <Typography variant="body2">
              请确保支付信息准确无误，错误的信息可能导致无法收款。
            </Typography>
          </Alert>
        </DialogContent>
        <DialogActions sx={{ borderTop: '1px solid #e5e7eb', px: 3, py: 2 }}>
          <Button onClick={() => setOpenPaymentDialog(false)} sx={{ color: '#6b7280' }}>
            取消
          </Button>
          <Button
            onClick={handleAddPaymentMethod}
            variant="contained"
            sx={{
              backgroundColor: '#3b82f6',
              '&:hover': { backgroundColor: '#2563eb' }
            }}
          >
            添加
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}

export default AccountSettings;