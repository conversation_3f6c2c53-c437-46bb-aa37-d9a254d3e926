import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Button,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip,
  Stack,
  ToggleButton,
  ToggleButtonGroup,
  Paper,

  CircularProgress,
} from '@mui/material';
import {
  CheckCircle,
  Cancel,
  Star,
  TrendingUp,
  Shield,
  Bolt,
  Groups,
  EmojiEvents,
  ArrowForward,
  AutoAwesome,
  AccessTime,
  GpsFixed,

} from '@mui/icons-material';
import Footer from '../components/layout/Footer';
import subscriptionService from '../services/subscriptionService';
// import { useAuth } from '../contexts/AuthContext';

const Pricing = () => {
  const [billingCycle, setBillingCycle] = useState('monthly');
  const [plans, setPlans] = useState([]);
  const [loading, setLoading] = useState(true);
  // const { user } = useAuth(); // 暂时不需要用户信息

  const handleBillingChange = (event, newValue) => {
    if (newValue !== null) {
      setBillingCycle(newValue);
    }
  };

  // 获取套餐数据
  useEffect(() => {
    const fetchPlans = async () => {
      try {
        setLoading(true);
        const response = await subscriptionService.getAvailablePlans({
          active_only: true
        });

        let plansData = [];
        if (Array.isArray(response)) {
          plansData = response;
        } else if (response.success && response.data) {
          plansData = response.data;
        } else if (response.data) {
          plansData = response.data;
        }

        if (plansData && plansData.length > 0) {
          // 转换API数据格式
          const formattedPlans = plansData.map(plan => {
            // 根据计划类型设置颜色
            let color, gradient;
            if (plan.plan_name.includes('企业') || plan.plan_type === 'enterprise') {
              color = '#f59e0b';
              gradient = 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)';
            } else if (plan.plan_name.includes('专业') || plan.plan_type === 'pro') {
              color = '#666666';
              gradient = 'linear-gradient(135deg, #666666 0%, #555555 100%)';
            } else {
              color = '#3b82f6';
              gradient = 'linear-gradient(135deg, #3b82f6 0%, #2563eb 100%)';
            }

            // 构建功能列表
            const features = [];
            if (plan.max_content_requests) {
              features.push({ name: `${plan.max_content_requests}个内容请求`, included: true });
            }
            if (plan.max_monitoring_projects) {
              features.push({ name: `${plan.max_monitoring_projects}个监控项目`, included: true });
            }
            if (plan.max_api_calls) {
              features.push({ name: `${plan.max_api_calls}次API调用`, included: true });
            }
            if (plan.max_team_members) {
              features.push({ name: `${plan.max_team_members}个团队成员`, included: true });
            }

            return {
              id: plan.id,
              name: plan.plan_name,
              price: parseFloat(plan.monthly_price) || 0,
              yearlyPrice: parseFloat(plan.yearly_price) || (parseFloat(plan.monthly_price) * 12),
              popular: plan.display_order === 2,
              badge: plan.display_order === 2 ? '推荐' : null,
              buttonColor: color,
              gradient: gradient,
              features: features,
              is_purchased: plan.is_purchased || false,
              current_subscription_id: plan.current_subscription_id || null
            };
          });

          setPlans(formattedPlans);
        } else {
          // 使用默认静态数据
          setPlans(getDefaultPlans());
        }
      } catch (error) {
        console.error('获取套餐失败:', error);
        // 使用默认静态数据
        setPlans(getDefaultPlans());
      } finally {
        setLoading(false);
      }
    };

    fetchPlans();
  }, []);

  // 默认静态套餐数据
  const getDefaultPlans = () => [
    {
      id: 'basic',
      name: '基础会员',
      price: 99,
      yearlyPrice: 990,
      popular: false,
      badge: null,
      buttonColor: '#3b82f6',
      gradient: 'linear-gradient(135deg, #3b82f6 0%, #2563eb 100%)',
      features: [
        { name: '30个内容请求', included: true },
        { name: '5个监控项目', included: true },
        { name: '1000次API调用', included: true },
        { name: '邮件支持', included: true },
      ],
      is_purchased: false
    },
    {
      id: 'pro',
      name: '专业会员',
      price: 299,
      yearlyPrice: 2990,
      popular: true,
      badge: '推荐',
      buttonColor: '#8b5cf6',
      gradient: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',
      features: [
        { name: '100个内容请求', included: true },
        { name: '20个监控项目', included: true },
        { name: '10000次API调用', included: true },
        { name: '优先技术支持', included: true },
      ],
      is_purchased: false
    },
    {
      id: 'enterprise',
      name: '企业会员',
      price: 999,
      yearlyPrice: 9990,
      popular: false,
      badge: null,
      buttonColor: '#f59e0b',
      gradient: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',
      features: [
        { name: '无限内容请求', included: true },
        { name: '无限监控项目', included: true },
        { name: '无限API调用', included: true },
        { name: '专属客服支持', included: true },
      ],
      is_purchased: false
    }
  ];



  const valueProps = [
    {
      icon: <TrendingUp sx={{ fontSize: 40 }} />,
      title: 'ROI提升300%',
      description: '平均每家企业通过我们的服务，内容营销ROI提升超过300%'
    },
    {
      icon: <AccessTime sx={{ fontSize: 40 }} />,
      title: '节省80%时间',
      description: '自动化匹配和AI优化，让您的内容创作效率提升5倍'
    },
    {
      icon: <GpsFixed sx={{ fontSize: 40 }} />,
      title: '精准匹配率95%',
      description: 'AI智能算法确保每次都能找到最适合的内容创作者'
    },
    {
      icon: <Shield sx={{ fontSize: 40 }} />,
      title: '三重质量保障',
      description: '机器审核、人工复核、客户确认，确保内容品质'
    }
  ];

  const workflowSteps = [
    { step: '1', title: '发布需求', desc: 'AI智能分析行业特征' },
    { step: '2', title: '精准匹配', desc: '推荐5-10个优质创作者' },
    { step: '3', title: '自主选择', desc: '查看作品集后决策' },
    { step: '4', title: '内容创作', desc: '三级审核保证质量' },
    { step: '5', title: '多平台发布', desc: '覆盖主流AI搜索平台' },
    { step: '6', title: '效果监测', desc: '实时数据反馈优化' }
  ];

  const additionalServices = [
    {
      name: 'AI内容优化建议',
      price: '299',
      description: '自动生成改进方案，提升内容质量'
    },
    {
      name: 'API接入服务',
      price: '定制报价',
      description: '系统集成，自动化工作流程'
    }
  ];

  const coreMechanisms = [
    {
      icon: <Groups sx={{ fontSize: 30 }} />,
      title: '供需匹配机制',
      color: '#3b82f6',
      bgGradient: 'linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%)',
      features: [
        '智能语义理解与意图识别',
        '构建创作者能力画像',
        '多维度匹配成功率预测'
      ]
    },
    {
      icon: <Shield sx={{ fontSize: 30 }} />,
      title: '质量保障机制',
      color: '#10b981',
      bgGradient: 'linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%)',
      features: [
        '创作者资质严格审核',
        'AI原创检测与专业度评分',
        '三级审核确保品质'
      ]
    },
    {
      icon: <EmojiEvents sx={{ fontSize: 30 }} />,
      title: '利益分配机制',
      color: '#8b5cf6',
      bgGradient: 'linear-gradient(135deg, #faf5ff 0%, #f3e8ff 100%)',
      features: [
        '透明的费用结构',
        '创作者收益最大化',
        'T+7快速结算保障'
      ]
    }
  ];

  const whyPayReasons = [
    {
      icon: <Bolt sx={{ fontSize: 40 }} />,
      title: '时间就是金钱',
      description: '传统内容营销需要大量时间寻找创作者、沟通需求、审核质量。我们的AI系统可以在几分钟内完成这些工作，让您专注于核心业务。'
    },
    {
      icon: <Star sx={{ fontSize: 40 }} />,
      title: '品质决定效果',
      description: '经过严格筛选的创作者、三级质量审核、AI优化建议，确保每一份内容都能达到专业水准，真正为您带来价值。'
    },
    {
      icon: <TrendingUp sx={{ fontSize: 40 }} />,
      title: '数据驱动增长',
      description: '实时效果监测、智能优化建议、成功案例分析，让每一次投入都有据可依，持续提升营销效果。'
    },
    {
      icon: <Shield sx={{ fontSize: 40 }} />,
      title: '风险最小化',
      description: '专业的服务团队、完善的保障机制、透明的结算流程，让您的每一分投入都有保障，无需担心风险。'
    }
  ];

  return (
    <Box sx={{ minHeight: '100vh', bgcolor: 'white' }}>
      {/* Hero Section - 与Home页面一致的配色 */}
      <Box
        sx={{
          py: 10,
          background: 'linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #334155 50%, #475569 75%, #64748b 100%)',
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        {/* 动态背景装饰 */}
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: `
              radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.15) 0%, transparent 50%),
              radial-gradient(circle at 80% 20%, rgba(16, 185, 129, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 40% 40%, rgba(200, 200, 200, 0.1) 0%, transparent 50%)
            `,
            pointerEvents: 'none',
          }}
        />

        {/* 网格背景 */}
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundImage: `
              linear-gradient(rgba(255,255,255,0.02) 1px, transparent 1px),
              linear-gradient(90deg, rgba(255,255,255,0.02) 1px, transparent 1px)
            `,
            backgroundSize: '50px 50px',
            pointerEvents: 'none',
          }}
        />
        <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 1 }}>
          <Box sx={{ textAlign: 'center', maxWidth: 900, mx: 'auto' }}>
            <Chip
              icon={<AutoAwesome />}
              label="智能内容营销，让每一分投入都有价值"
              sx={{
                mb: 3,
                px: 2,
                py: 3,
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                color: '#60a5fa',
                border: '1px solid rgba(59, 130, 246, 0.2)',
                fontSize: '0.9rem',
                fontWeight: 600,
              }}
            />
            <Typography
              variant="h2"
              sx={{
                fontWeight: 800,
                fontSize: { xs: '2.5rem', md: '3.5rem' },
                mb: 3,
                background: 'linear-gradient(135deg, #ffffff 0%, #e2e8f0 50%, #cbd5e1 100%)',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
              }}
            >
              为什么选择付费？因为专业创造价值
            </Typography>
            <Typography
              variant="h5"
              sx={{
                color: '#cbd5e1',
                mb: 6,
                fontWeight: 400,
                fontSize: { xs: '1.1rem', md: '1.3rem' },
              }}
            >
              不是所有的内容都能带来流量，不是所有的流量都能转化为价值
            </Typography>

            <Grid container spacing={3} justifyContent="center">
              {valueProps.map((prop, index) => (
                <Grid size={{ xs: 12, sm: 6, md: 3 }} key={index}>
                  <Paper
                    elevation={0}
                    sx={{
                      p: 3,
                      height: '100%',
                      bgcolor: 'white',
                      borderRadius: 3,
                      boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
                      transition: 'all 0.3s',
                      '&:hover': {
                        transform: 'translateY(-5px)',
                        boxShadow: '0 8px 30px rgba(0,0,0,0.12)',
                      },
                    }}
                  >
                    <Box sx={{ color: '#3b82f6', mb: 2 }}>{prop.icon}</Box>
                    <Typography variant="h5" sx={{ fontWeight: 700, mb: 1 }}>
                      {prop.title}
                    </Typography>
                    <Typography variant="body2" sx={{ color: '#64748b' }}>
                      {prop.description}
                    </Typography>
                  </Paper>
                </Grid>
              ))}
            </Grid>
          </Box>
        </Container>
      </Box>

      {/* Workflow Section */}
      <Box sx={{ py: 8, bgcolor: '#fafafa' }}>
        <Container maxWidth="lg">
          <Box sx={{ textAlign: 'center', mb: 6 }}>
            <Typography variant="h3" sx={{ fontWeight: 700, mb: 2 }}>
              完整闭环的业务流程
            </Typography>
            <Typography variant="h6" sx={{ color: '#64748b' }}>
              从需求到效果，每一步都精心设计
            </Typography>
          </Box>
          <Grid container spacing={2}>
            {workflowSteps.map((item, index) => (
              <Grid size={{ xs: 6, sm: 4, md: 2 }} key={index}>
                <Box
                  sx={{
                    position: 'relative',
                    textAlign: 'center',
                  }}
                >
                  <Paper
                    elevation={0}
                    sx={{
                      p: 2,
                      bgcolor: 'white',
                      borderRadius: 2,
                      border: '1px solid #e5e7eb',
                      transition: 'all 0.3s',
                      '&:hover': {
                        transform: 'translateY(-3px)',
                        boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
                      },
                    }}
                  >
                    <Box
                      sx={{
                        width: 48,
                        height: 48,
                        borderRadius: '50%',
                        background: 'linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%)',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: 'white',
                        fontSize: '1.2rem',
                        fontWeight: 700,
                        mx: 'auto',
                        mb: 2,
                      }}
                    >
                      {item.step}
                    </Box>
                    <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
                      {item.title}
                    </Typography>
                    <Typography variant="caption" sx={{ color: '#64748b' }}>
                      {item.desc}
                    </Typography>
                  </Paper>
                  {index < workflowSteps.length - 1 && (
                    <Box
                      sx={{
                        display: { xs: 'none', md: 'block' },
                        position: 'absolute',
                        top: '50%',
                        right: -8,
                        transform: 'translateY(-50%)',
                        color: '#e5e7eb',
                      }}
                    >
                      <ArrowForward />
                    </Box>
                  )}
                </Box>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>

      {/* Core Mechanisms */}
      <Box sx={{ py: 8, bgcolor: 'white' }}>
        <Container maxWidth="lg">
          <Box sx={{ textAlign: 'center', mb: 6 }}>
            <Typography variant="h3" sx={{ fontWeight: 700, mb: 2 }}>
              核心运行机制
            </Typography>
            <Typography variant="h6" sx={{ color: '#64748b' }}>
              三大机制保障服务品质与效果
            </Typography>
          </Box>
          <Grid container spacing={4} justifyContent="center">
            {coreMechanisms.map((mechanism, index) => (
              <Grid size={{ xs: 12, md: 4 }} key={index}>
                <Paper
                  elevation={0}
                  sx={{
                    height: '100%',
                    borderRadius: 3,
                    overflow: 'hidden',
                    background: mechanism.bgGradient,
                    border: '1px solid #e5e7eb',
                  }}
                >
                  <Box sx={{ p: 4 }}>
                    <Box
                      sx={{
                        width: 60,
                        height: 60,
                        borderRadius: 2,
                        bgcolor: mechanism.color,
                        color: 'white',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        mb: 3,
                      }}
                    >
                      {mechanism.icon}
                    </Box>
                    <Typography variant="h5" sx={{ fontWeight: 700, mb: 3 }}>
                      {mechanism.title}
                    </Typography>
                    <List dense>
                      {mechanism.features.map((feature, idx) => (
                        <ListItem key={idx} sx={{ px: 0 }}>
                          <ListItemIcon sx={{ minWidth: 28 }}>
                            <CheckCircle sx={{ color: '#10b981', fontSize: 20 }} />
                          </ListItemIcon>
                          <ListItemText
                            primary={feature}
                            primaryTypographyProps={{
                              variant: 'body2',
                              color: '#475569',
                            }}
                          />
                        </ListItem>
                      ))}
                    </List>
                  </Box>
                </Paper>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>

      {/* Pricing Plans */}
      <Box sx={{ py: 10, bgcolor: '#fafafa' }}>
        <Container maxWidth="lg">
          <Box sx={{ textAlign: 'center', mb: 6 }}>
            <Typography variant="h3" sx={{ fontWeight: 700, mb: 2 }}>
              选择适合您的会员计划
            </Typography>
            <Typography variant="h6" sx={{ color: '#64748b', mb: 4 }}>
              投资专业服务，收获超值回报
            </Typography>

            {/* Billing Toggle */}
            <Box sx={{ display: 'flex', justifyContent: 'center' }}>
              <ToggleButtonGroup
                value={billingCycle}
                exclusive
                onChange={handleBillingChange}
                sx={{
                  bgcolor: 'white',
                  borderRadius: 10,
                  '& .MuiToggleButton-root': {
                    px: 4,
                    py: 1.5,
                    textTransform: 'none',
                    fontSize: '1rem',
                    fontWeight: 500,
                    border: 'none',
                    '&.Mui-selected': {
                      bgcolor: '#3b82f6',
                      color: 'white',
                      '&:hover': {
                        bgcolor: '#2563eb',
                      },
                    },
                  },
                }}
              >
                <ToggleButton value="monthly">月付</ToggleButton>
                <ToggleButton value="yearly">
                  <Stack direction="row" spacing={1} alignItems="center">
                    <span>年付</span>
                    <Chip
                      label="省2个月"
                      size="small"
                      sx={{
                        bgcolor: '#10b981',
                        color: 'white',
                        height: 22,
                        fontSize: '0.75rem',
                      }}
                    />
                  </Stack>
                </ToggleButton>
              </ToggleButtonGroup>
            </Box>
          </Box>

          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', py: 8 }}>
              <CircularProgress size={60} />
            </Box>
          ) : (
            <Grid container spacing={4} justifyContent="center">
              {plans.map((plan) => (
                <Grid size={{ xs: 12, md: 4 }} key={plan.id}>
                  <Card
                    sx={{
                      height: '100%',
                      position: 'relative',
                      borderRadius: 3,
                      overflow: 'visible',
                      transform: plan.popular ? 'scale(1.05)' : 'scale(1)',
                      boxShadow: plan.popular
                        ? '0 20px 60px rgba(59, 130, 246, 0.2)'
                        : '0 4px 20px rgba(0,0,0,0.08)',
                      transition: 'all 0.3s',
                      opacity: plan.is_purchased ? 0.8 : 1,
                      border: plan.is_purchased ? '2px solid #10b981' : 'none',
                      '&:hover': {
                        transform: plan.popular ? 'scale(1.08)' : 'scale(1.02)',
                        boxShadow: plan.popular
                          ? '0 25px 70px rgba(59, 130, 246, 0.3)'
                          : '0 8px 30px rgba(0,0,0,0.12)',
                      },
                    }}
                  >
                    {plan.is_purchased && (
                      <Chip
                        label="已购买"
                        sx={{
                          position: 'absolute',
                          top: -15,
                          left: '50%',
                          transform: 'translateX(-50%)',
                          bgcolor: '#10b981',
                          color: 'white',
                          fontWeight: 600,
                          px: 2,
                          zIndex: 1,
                        }}
                      />
                    )}
                    {plan.badge && !plan.is_purchased && (
                      <Chip
                        icon={<Star />}
                        label={plan.badge}
                        sx={{
                          position: 'absolute',
                          top: -15,
                          left: '50%',
                          transform: 'translateX(-50%)',
                          bgcolor: plan.popular ? '#f59e0b' : '#8b5cf6',
                          color: 'white',
                          fontWeight: 600,
                          px: 2,
                          zIndex: 1,
                        }}
                      />
                    )}
                  <Box
                    sx={{
                      background: plan.popular ? plan.gradient : '#fafafa',
                      p: 4,
                      color: plan.popular ? 'white' : 'inherit',
                    }}
                  >
                    <Typography variant="h5" sx={{ fontWeight: 700, mb: 2 }}>
                      {plan.name}
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'baseline', mb: 2 }}>
                      <Typography variant="h3" sx={{ fontWeight: 800 }}>
                        ¥{billingCycle === 'monthly' ? plan.price : plan.yearlyPrice}
                      </Typography>
                      <Typography variant="h6" sx={{ ml: 1, opacity: 0.8 }}>
                        /{billingCycle === 'monthly' ? '月' : '年'}
                      </Typography>
                    </Box>
                    {billingCycle === 'yearly' && plan.yearlyPrice < (plan.price * 12) && (
                      <Chip
                        label={`年付节省 ¥${(plan.price * 12) - plan.yearlyPrice}`}
                        sx={{
                          bgcolor: 'rgba(16, 185, 129, 0.2)',
                          color: plan.popular ? 'white' : '#10b981',
                          fontWeight: 500,
                        }}
                      />
                    )}
                  </Box>
                  <CardContent sx={{ p: 4 }}>
                    <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 3 }}>
                      包含功能
                    </Typography>
                    <List dense>
                      {plan.features.map((feature, index) => (
                        <ListItem key={index} sx={{ px: 0, py: 0.5 }}>
                          <ListItemIcon sx={{ minWidth: 32 }}>
                            {feature.included ? (
                              <CheckCircle sx={{ color: '#10b981', fontSize: 22 }} />
                            ) : (
                              <Cancel sx={{ color: '#e5e7eb', fontSize: 22 }} />
                            )}
                          </ListItemIcon>
                          <ListItemText
                            primary={feature.name || feature.text}
                            primaryTypographyProps={{
                              variant: 'body2',
                              sx: {
                                color: feature.included ? '#1e293b' : '#94a3b8',
                                textDecoration: feature.included ? 'none' : 'line-through',
                              },
                            }}
                          />
                        </ListItem>
                      ))}
                    </List>
                    <Button
                      fullWidth
                      variant={plan.is_purchased ? "outlined" : "contained"}
                      size="large"
                      disabled={plan.is_purchased}
                      sx={{
                        mt: 3,
                        py: 1.5,
                        bgcolor: plan.is_purchased ? 'transparent' : plan.buttonColor,
                        borderColor: plan.is_purchased ? '#10b981' : plan.buttonColor,
                        color: plan.is_purchased ? '#10b981' : 'white',
                        borderRadius: 2,
                        textTransform: 'none',
                        fontWeight: 600,
                        fontSize: '1rem',
                        '&:hover': {
                          bgcolor: plan.is_purchased ? 'transparent' : plan.buttonColor,
                          filter: plan.is_purchased ? 'none' : 'brightness(0.9)',
                        },
                        '&.Mui-disabled': {
                          color: '#10b981',
                          borderColor: '#10b981',
                        },
                      }}
                    >
                      {plan.is_purchased ? '已购买' : '立即订阅'}
                    </Button>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
          )}

          {/* Additional Services */}
          <Box
            sx={{
              mt: 8,
              p: 4,
              borderRadius: 3,
              background: 'linear-gradient(135deg, #eff6ff 0%, #faf5ff 100%)',
            }}
          >
            <Typography variant="h4" sx={{ fontWeight: 700, mb: 4, textAlign: 'center' }}>
              服务
            </Typography>
            <Grid container spacing={3} justifyContent="center">
              {additionalServices.map((service, index) => (
                <Grid size={{ xs: 12, sm: 6, md: 5 }} key={index}>
                  <Paper
                    elevation={0}
                    sx={{
                      p: 3,
                      borderRadius: 2,
                      border: '1px solid #e5e7eb',
                      bgcolor: 'white',
                      transition: 'all 0.3s',
                      '&:hover': {
                        boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
                      },
                    }}
                  >
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                      <Typography variant="h6" sx={{ fontWeight: 600 }}>
                        {service.name}
                      </Typography>
                      <Typography variant="h6" sx={{ color: '#3b82f6', fontWeight: 700 }}>
                        ¥{service.price}
                      </Typography>
                    </Box>
                    <Typography variant="body2" sx={{ color: '#64748b' }}>
                      {service.description}
                    </Typography>
                  </Paper>
                </Grid>
              ))}
            </Grid>
          </Box>
        </Container>
      </Box>

      {/* FAQ Section */}
      <Box sx={{ py: 8, bgcolor: 'white' }}>
        <Container maxWidth="md">
          <Typography variant="h3" sx={{ fontWeight: 700, mb: 6, textAlign: 'center' }}>
            常见问题
          </Typography>
          <Stack spacing={3}>
            <Paper elevation={0} sx={{ p: 3, bgcolor: '#fafafa', borderRadius: 2 }}>
              <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                可以随时取消订阅吗？
              </Typography>
              <Typography variant="body2" sx={{ color: '#64748b' }}>
                当然可以。您可以随时在账户设置中取消订阅，当前计费周期结束前仍可使用所有功能。
              </Typography>
            </Paper>
            <Paper elevation={0} sx={{ p: 3, bgcolor: '#fafafa', borderRadius: 2 }}>
              <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                如何保证内容质量？
              </Typography>
              <Typography variant="body2" sx={{ color: '#64748b' }}>
                我们通过创作者资质审核、AI原创检测、专业度评分、人工复核等多重机制确保内容质量。
              </Typography>
            </Paper>
            <Paper elevation={0} sx={{ p: 3, bgcolor: '#fafafa', borderRadius: 2 }}>
              <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                年付有什么优惠？
              </Typography>
              <Typography variant="body2" sx={{ color: '#64748b' }}>
                选择年付可以享受10个月的价格获得12个月的服务，相当于节省2个月的费用。
              </Typography>
            </Paper>
          </Stack>
        </Container>
      </Box>

      {/* Footer */}
      <Footer />
    </Box>
  );
};

export default Pricing;