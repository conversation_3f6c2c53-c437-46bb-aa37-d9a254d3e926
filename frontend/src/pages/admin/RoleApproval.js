import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  Avatar,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Button,
  IconButton,
  TextField,
  InputAdornment,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Alert,
  Grid,
  Divider,
  Snackbar,
} from '@mui/material';
import {
  Search,
  FilterList,
  Visibility,
  CheckCircle,
  Cancel,
  AccessTime,
  Person,
  Business,
  Group,
  Article,
  Assignment,
  AssignmentTurnedIn,
  AttachFile,
  Description,
  Download,
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import apiService from '../../services/api';

function RoleApproval() {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [applications, setApplications] = useState([]);
  const [filteredApplications, setFilteredApplications] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');

  const [roleFilter, setRoleFilter] = useState('enterprise_user');
  const [selectedApplication, setSelectedApplication] = useState(null);
  const [reviewDialogOpen, setReviewDialogOpen] = useState(false);
  const [reviewForm, setReviewForm] = useState({
    action: '',
    feedback: '',
  });
  const [error, setError] = useState('');
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'info'
  });

  // 加载待审批数据的函数
  const fetchApplications = async () => {
    try {
      setLoading(true);
      setError('');

      // 并行调用三个接口获取待审批数据
      const [companiesResponse, channelsResponse, agentsResponse] = await Promise.allSettled([
        apiService.get('/companies/pending'),
        apiService.get('/channels/pending'),
        apiService.get('/agents/pending')
      ]);

      const allApplications = [];

      // 处理企业用户数据
      if (companiesResponse.status === 'fulfilled' && companiesResponse.value.success) {
        const companies = companiesResponse.value.data.items || [];
        companies.forEach(company => {
          allApplications.push({
            id: `company_${company.id}`,
            originalId: company.id,
            roleType: 'enterprise_user',
            status: company.verification_status === 'pending' ? 'pending' : company.verification_status,
            userName: company.legal_person,
            userEmail: company.contact_email,
            companyName: company.company_name,
            businessLicense: company.company_code,
            contactPhone: company.contact_phone,
            submitTime: new Date(company.created_at).toLocaleString('zh-CN'),
            reviewTime: company.verification_time ? new Date(company.verification_time).toLocaleString('zh-CN') : null,
            feedback: company.verification_note,
            applicationReason: company.business_scope || '申请企业用户权限',
            industry: company.industry,
            companySize: company.company_size,
            headquarters: company.headquarters_location,
            website: company.official_website,
            // 添加文件相关字段
            businessLicenseUrl: company.business_license_url,
            otherFiles: company.other_files || [],
            socialMediaLinks: company.social_media_links || {},
          });
        });
      }

      // 处理渠道商数据
      if (channelsResponse.status === 'fulfilled' && channelsResponse.value.success) {
        const channels = channelsResponse.value.data.items || [];
        channels.forEach(channel => {
          allApplications.push({
            id: `channel_${channel.id}`,
            originalId: channel.id,
            roleType: 'channel_user',
            status: channel.verification_status === 'pending' ? 'pending' : channel.verification_status,
            userName: channel.real_name || channel.provider_name,
            userEmail: channel.contact_email,
            companyName: channel.company_name || channel.provider_name,
            businessLicense: channel.business_license,
            contactPhone: channel.contact_phone,
            submitTime: new Date(channel.created_at).toLocaleString('zh-CN'),
            reviewTime: channel.verification_time ? new Date(channel.verification_time).toLocaleString('zh-CN') : null,
            feedback: channel.verification_note,
            applicationReason: channel.service_description || '申请成为内容提供商',
            providerType: channel.provider_type,
            serviceAreas: channel.service_areas,
          });
        });
      }

      // 处理代理商数据
      if (agentsResponse.status === 'fulfilled' && agentsResponse.value.success) {
        const agents = agentsResponse.value.data.agents || [];
        agents.forEach(agent => {
          allApplications.push({
            id: `agent_${agent.id}`,
            originalId: agent.id,
            roleType: 'agent_user',
            status: agent.verification_status === 'pending' ? 'pending' : agent.verification_status,
            userName: agent.agent_name,
            userEmail: agent.contact_email,
            companyName: agent.agent_name, // 代理商使用agent_name作为公司名
            businessLicense: agent.agent_code, // 使用agent_code作为业务许可证号
            contactPhone: agent.contact_phone,
            submitTime: new Date(agent.created_at).toLocaleString('zh-CN'),
            reviewTime: agent.verification_time ? new Date(agent.verification_time).toLocaleString('zh-CN') : null,
            feedback: agent.verification_note,
            applicationReason: agent.agent_description || '申请成为代理商',
            agentLevel: agent.agent_level,
            serviceAreas: agent.service_regions,
            specialties: agent.specialties,
          });
        });
      }

      // 按提交时间排序（最新的在前）
      allApplications.sort((a, b) => new Date(b.submitTime) - new Date(a.submitTime));

      setApplications(allApplications);
      setFilteredApplications(allApplications);
    } catch (err) {
      console.error('加载申请数据失败:', err);
      setError('加载申请数据失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 加载待审批数据
  useEffect(() => {
    fetchApplications();
  }, []);

  // 过滤申请
  useEffect(() => {
    let filtered = applications;

    if (searchTerm) {
      filtered = filtered.filter(app =>
        app.userName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        app.userEmail.toLowerCase().includes(searchTerm.toLowerCase()) ||
        app.companyName.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // 按角色筛选
    filtered = filtered.filter(app => app.roleType === roleFilter);

    setFilteredApplications(filtered);
  }, [applications, searchTerm, roleFilter]);

  const getRoleLabel = (roleType) => {
    const roleMap = {
      enterprise_user: '企业用户',
      agent_user: '代理商',
      channel_user: '渠道商',
    };
    return roleMap[roleType] || roleType;
  };

  const getRoleColor = (roleType) => {
    const colorMap = {
      enterprise_user: 'primary',
      agent_user: 'success',
      channel_user: 'warning',
    };
    return colorMap[roleType] || 'default';
  };

  const getStatusColor = (status) => {
    const statusMap = {
      pending: 'warning',
      approved: 'success',
      rejected: 'error',
      verified: 'success',
    };
    return statusMap[status] || 'default';
  };

  const getStatusLabel = (status) => {
    const statusMap = {
      pending: '待审核',
      approved: '已通过',
      rejected: '已拒绝',
      verified: '已通过',
    };
    return statusMap[status] || status;
  };

  const handleReviewApplication = (application) => {
    setSelectedApplication(application);
    setReviewDialogOpen(true);
    setReviewForm({
      action: '',
      feedback: '',
    });
  };

  // 文件下载处理函数
  const handleFileDownload = (fileUrl, fileName) => {
    if (!fileUrl) return;

    // 创建一个临时链接来下载文件
    const link = document.createElement('a');
    link.href = fileUrl;
    link.download = fileName || '文件';
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // 渲染企业用户详情
  const renderEnterpriseUserDetails = (application) => (
    <Grid container spacing={2} justifyContent="center" alignItems="flex-start">
      <Grid item xs={12} md={5}>
        <Card sx={{ height: '100%', boxShadow: 'none', border: 'none' }}>
          <CardContent sx={{ p: 2 }}>
            <Box sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 1,
              mb: 2,
              pb: 1,
              borderBottom: '1px solid',
              borderColor: 'primary.100'
            }}>
              <Avatar sx={{
                bgcolor: 'primary.100',
                color: 'primary.main',
                width: 24,
                height: 24
              }}>
                <Business fontSize="small" />
              </Avatar>
              <Typography variant="subtitle2" sx={{ fontWeight: 600, color: 'primary.main' }}>
                企业基本信息
              </Typography>
            </Box>

            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', p: 1 }}>
                <Typography variant="caption" sx={{ fontWeight: 600, color: 'text.secondary' }}>
                  企业名称
                </Typography>
                <Typography variant="caption" sx={{ fontWeight: 600, color: 'text.primary' }}>
                  {application.companyName}
                </Typography>
              </Box>

              <Box sx={{ display: 'flex', justifyContent: 'space-between', p: 1 }}>
                <Typography variant="caption" sx={{ fontWeight: 600, color: 'text.secondary' }}>
                  法人代表
                </Typography>
                <Typography variant="caption" sx={{ fontWeight: 600, color: 'text.primary' }}>
                  {application.userName}
                </Typography>
              </Box>

              <Box sx={{ display: 'flex', justifyContent: 'space-between', p: 1 }}>
                <Typography variant="caption" sx={{ fontWeight: 600, color: 'text.secondary' }}>
                  营业执照号
                </Typography>
                <Typography variant="caption" sx={{ fontWeight: 600, color: 'text.primary' }}>
                  {application.businessLicense || '未提供'}
                </Typography>
              </Box>

              <Box sx={{ display: 'flex', justifyContent: 'space-between', p: 1 }}>
                <Typography variant="caption" sx={{ fontWeight: 600, color: 'text.secondary' }}>
                  联系电话
                </Typography>
                <Typography variant="caption" sx={{ fontWeight: 600, color: 'text.primary' }}>
                  {application.contactPhone || '未提供'}
                </Typography>
              </Box>

              <Divider sx={{ my: 1 }} />

              <Box sx={{ display: 'flex', justifyContent: 'space-between', p: 1 }}>
                <Typography variant="caption" sx={{ fontWeight: 600, color: 'primary.main' }}>
                  所属行业
                </Typography>
                <Typography variant="caption" sx={{ fontWeight: 600, color: 'primary.dark' }}>
                  {application.industry || '未提供'}
                </Typography>
              </Box>

              <Box sx={{ display: 'flex', justifyContent: 'space-between', p: 1 }}>
                <Typography variant="caption" sx={{ fontWeight: 600, color: 'primary.main' }}>
                  企业规模
                </Typography>
                <Typography variant="caption" sx={{ fontWeight: 600, color: 'primary.dark' }}>
                  {application.companySize || '未提供'}
                </Typography>
              </Box>

              <Box sx={{ display: 'flex', justifyContent: 'space-between', p: 1 }}>
                <Typography variant="caption" sx={{ fontWeight: 600, color: 'primary.main' }}>
                  总部地址
                </Typography>
                <Typography variant="caption" sx={{ fontWeight: 600, color: 'primary.dark', textAlign: 'right', maxWidth: '60%' }}>
                  {application.headquarters || '未提供'}
                </Typography>
              </Box>

              <Box sx={{ display: 'flex', justifyContent: 'space-between', p: 1 }}>
                <Typography variant="caption" sx={{ fontWeight: 600, color: 'primary.main' }}>
                  官方网站
                </Typography>
                <Typography variant="caption" sx={{ fontWeight: 600, color: 'primary.dark', textAlign: 'right', maxWidth: '60%' }}>
                  {application.website || '未提供'}
                </Typography>
              </Box>
            </Box>
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12} md={6}>
        <Card sx={{ height: '100%', boxShadow: 'none', border: 'none' }}>
          <CardContent sx={{ p: 2 }}>
            <Box sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 1,
              mb: 2,
              pb: 1,
              borderBottom: '1px solid',
              borderColor: 'info.100'
            }}>
              <Avatar sx={{
                bgcolor: 'info.100',
                color: 'info.main',
                width: 24,
                height: 24
              }}>
                <Article fontSize="small" />
              </Avatar>
              <Typography variant="subtitle2" sx={{ fontWeight: 600, color: 'info.main' }}>
                申请理由
              </Typography>
            </Box>

            <Box sx={{
              p: 1.5,
              bgcolor: 'rgba(25, 118, 210, 0.04)',
              borderRadius: 1,
              minHeight: 80,
              maxHeight: 120,
              overflow: 'auto',
              border: '1px solid',
              borderColor: 'info.100',
              borderLeft: '3px solid',
              borderLeftColor: 'info.main'
            }}>
              <Typography variant="body2" sx={{
                lineHeight: 1.5,
                color: 'text.primary',
                fontSize: '13px'
              }}>
                {application.applicationReason}
              </Typography>
            </Box>

            <Box sx={{
              mt: 1.5,
              pt: 1.5,
              borderTop: '1px solid',
              borderColor: 'grey.200',
              bgcolor: 'grey.50',
              borderRadius: 0.5,
              p: 1
            }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5, mb: 0.5 }}>
                <AccessTime fontSize="small" color="action" />
                <Typography variant="caption" sx={{ fontWeight: 600, color: 'text.primary' }}>
                  申请时间: {application.submitTime}
                </Typography>
              </Box>
              {application.reviewTime && (
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                  <CheckCircle fontSize="small" color="success" />
                  <Typography variant="caption" sx={{ fontWeight: 600, color: 'success.main' }}>
                    审核时间: {application.reviewTime}
                  </Typography>
                </Box>
              )}
            </Box>
          </CardContent>
        </Card>
      </Grid>

      {/* 资质文件卡片 */}
      <Grid item xs={12}>
        <Card sx={{ boxShadow: 'none', border: 'none' }}>
          <CardContent sx={{ p: 2 }}>
            <Box sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 1,
              mb: 2,
              pb: 1,
              borderBottom: '1px solid',
              borderColor: 'warning.100'
            }}>
              <Avatar sx={{
                bgcolor: 'warning.100',
                color: 'warning.main',
                width: 24,
                height: 24
              }}>
                <AttachFile fontSize="small" />
              </Avatar>
              <Typography variant="subtitle2" sx={{ fontWeight: 600, color: 'warning.main' }}>
                资质文件
              </Typography>
            </Box>

            <Grid container spacing={2}>
              {/* 营业执照 */}
              {application.businessLicenseUrl && (
                <Grid item xs={12} sm={6}>
                  <Box sx={{
                    p: 2,
                    border: '1px solid',
                    borderColor: 'grey.200',
                    borderRadius: 1,
                    bgcolor: 'grey.50',
                    '&:hover': {
                      bgcolor: 'grey.100',
                      cursor: 'pointer'
                    }
                  }}
                  onClick={() => handleFileDownload(application.businessLicenseUrl, '营业执照')}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Description color="primary" />
                      <Box sx={{ flex: 1 }}>
                        <Typography variant="body2" sx={{ fontWeight: 600 }}>
                          营业执照
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          点击查看/下载
                        </Typography>
                      </Box>
                      <Download fontSize="small" color="action" />
                    </Box>
                  </Box>
                </Grid>
              )}

              {/* 其他资质文件 */}
              {application.otherFiles && application.otherFiles.length > 0 &&
                application.otherFiles.map((fileUrl, index) => (
                  <Grid item xs={12} sm={6} key={index}>
                    <Box sx={{
                      p: 2,
                      border: '1px solid',
                      borderColor: 'grey.200',
                      borderRadius: 1,
                      bgcolor: 'grey.50',
                      '&:hover': {
                        bgcolor: 'grey.100',
                        cursor: 'pointer'
                      }
                    }}
                    onClick={() => handleFileDownload(fileUrl, `资质文件${index + 1}`)}
                    >
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <AttachFile color="secondary" />
                        <Box sx={{ flex: 1 }}>
                          <Typography variant="body2" sx={{ fontWeight: 600 }}>
                            资质文件 {index + 1}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            点击查看/下载
                          </Typography>
                        </Box>
                        <Download fontSize="small" color="action" />
                      </Box>
                    </Box>
                  </Grid>
                ))
              }

              {/* 如果没有文件 */}
              {!application.businessLicenseUrl && (!application.otherFiles || application.otherFiles.length === 0) && (
                <Grid item xs={12}>
                  <Box sx={{
                    p: 3,
                    textAlign: 'center',
                    color: 'text.secondary',
                    bgcolor: 'grey.50',
                    borderRadius: 1,
                    border: '1px dashed',
                    borderColor: 'grey.300'
                  }}>
                    <Typography variant="body2">
                      暂无上传的资质文件
                    </Typography>
                  </Box>
                </Grid>
              )}
            </Grid>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );

  // 渲染代理商详情
  const renderAgentUserDetails = (application) => (
    <Grid container spacing={2} justifyContent="center" alignItems="flex-start">
      <Grid item xs={12} md={6}>
        <Card sx={{ height: '100%', boxShadow: 'none', border: 'none' }}>
          <CardContent sx={{ p: 2 }}>
            <Box sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 1,
              mb: 2,
              pb: 1,
              borderBottom: '1px solid',
              borderColor: 'success.100'
            }}>
              <Avatar sx={{
                bgcolor: 'success.100',
                color: 'success.main',
                width: 24,
                height: 24
              }}>
                <Group fontSize="small" />
              </Avatar>
              <Typography variant="subtitle2" sx={{ fontWeight: 600, color: 'success.main' }}>
                代理商基本信息
              </Typography>
            </Box>

            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', p: 1 }}>
                <Typography variant="caption" sx={{ fontWeight: 600, color: 'text.secondary' }}>
                  代理商名称
                </Typography>
                <Typography variant="caption" sx={{ fontWeight: 600, color: 'text.primary' }}>
                  {application.companyName}
                </Typography>
              </Box>

              <Box sx={{ display: 'flex', justifyContent: 'space-between', p: 1 }}>
                <Typography variant="caption" sx={{ fontWeight: 600, color: 'text.secondary' }}>
                  负责人
                </Typography>
                <Typography variant="caption" sx={{ fontWeight: 600, color: 'text.primary' }}>
                  {application.userName}
                </Typography>
              </Box>

              <Box sx={{ display: 'flex', justifyContent: 'space-between', p: 1 }}>
                <Typography variant="caption" sx={{ fontWeight: 600, color: 'text.secondary' }}>
                  代理商代码
                </Typography>
                <Typography variant="caption" sx={{ fontWeight: 600, color: 'text.primary' }}>
                  {application.businessLicense || '未提供'}
                </Typography>
              </Box>

              <Box sx={{ display: 'flex', justifyContent: 'space-between', p: 1 }}>
                <Typography variant="caption" sx={{ fontWeight: 600, color: 'text.secondary' }}>
                  联系电话
                </Typography>
                <Typography variant="caption" sx={{ fontWeight: 600, color: 'text.primary' }}>
                  {application.contactPhone || '未提供'}
                </Typography>
              </Box>

              <Divider sx={{ my: 1 }} />

              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', p: 1 }}>
                <Typography variant="caption" sx={{ fontWeight: 600, color: 'success.main' }}>
                  代理商等级
                </Typography>
                <Chip
                  label={application.agentLevel || '未设置'}
                  size="small"
                  sx={{
                    bgcolor: 'success.main',
                    color: 'white',
                    fontWeight: 600,
                    fontSize: '10px',
                    height: '18px'
                  }}
                />
              </Box>

              <Box sx={{ display: 'flex', justifyContent: 'space-between', p: 1 }}>
                <Typography variant="caption" sx={{ fontWeight: 600, color: 'success.main' }}>
                  服务区域
                </Typography>
                <Typography variant="caption" sx={{ fontWeight: 600, color: 'success.dark', textAlign: 'right', maxWidth: '60%' }}>
                  {application.serviceAreas?.join(', ') || '未提供'}
                </Typography>
              </Box>

              <Box sx={{ display: 'flex', justifyContent: 'space-between', p: 1 }}>
                <Typography variant="caption" sx={{ fontWeight: 600, color: 'success.main' }}>
                  专业领域
                </Typography>
                <Typography variant="caption" sx={{ fontWeight: 600, color: 'success.dark', textAlign: 'right', maxWidth: '60%' }}>
                  {application.specialties?.join(', ') || '未提供'}
                </Typography>
              </Box>
            </Box>
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12} md={6}>
        <Card sx={{ height: '100%', boxShadow: 'none', border: 'none' }}>
          <CardContent sx={{ p: 2 }}>
            <Box sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 1,
              mb: 2,
              pb: 1,
              borderBottom: '1px solid',
              borderColor: 'info.100'
            }}>
              <Avatar sx={{
                bgcolor: 'info.100',
                color: 'info.main',
                width: 24,
                height: 24
              }}>
                <Article fontSize="small" />
              </Avatar>
              <Typography variant="subtitle2" sx={{ fontWeight: 600, color: 'info.main' }}>
                申请理由
              </Typography>
            </Box>

            <Box sx={{
              p: 1.5,
              bgcolor: 'rgba(76, 175, 80, 0.04)',
              borderRadius: 1,
              minHeight: 80,
              maxHeight: 120,
              overflow: 'auto',
              border: '1px solid',
              borderColor: 'success.100',
              borderLeft: '3px solid',
              borderLeftColor: 'success.main'
            }}>
              <Typography variant="body2" sx={{
                lineHeight: 1.5,
                color: 'text.primary',
                fontSize: '13px'
              }}>
                {application.applicationReason}
              </Typography>
            </Box>

            <Box sx={{
              mt: 1.5,
              pt: 1.5,
              borderTop: '1px solid',
              borderColor: 'grey.200',
              bgcolor: 'grey.50',
              borderRadius: 0.5,
              p: 1
            }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5, mb: 0.5 }}>
                <AccessTime fontSize="small" color="action" />
                <Typography variant="caption" sx={{ fontWeight: 600, color: 'text.primary' }}>
                  申请时间: {application.submitTime}
                </Typography>
              </Box>
              {application.reviewTime && (
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                  <CheckCircle fontSize="small" color="success" />
                  <Typography variant="caption" sx={{ fontWeight: 600, color: 'success.main' }}>
                    审核时间: {application.reviewTime}
                  </Typography>
                </Box>
              )}
            </Box>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );

  // 渲染渠道商详情
  const renderChannelUserDetails = (application) => (
    <Grid container spacing={2} justifyContent="center" alignItems="flex-start">
      <Grid item xs={12} md={6}>
        <Card sx={{ height: '100%', boxShadow: 'none', border: 'none' }}>
          <CardContent sx={{ p: 2 }}>
            <Box sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 1,
              mb: 2,
              pb: 1,
              borderBottom: '1px solid',
              borderColor: 'warning.100'
            }}>
              <Avatar sx={{
                bgcolor: 'warning.100',
                color: 'warning.main',
                width: 24,
                height: 24
              }}>
                <Article fontSize="small" />
              </Avatar>
              <Typography variant="subtitle2" sx={{ fontWeight: 600, color: 'warning.main' }}>
                渠道商基本信息
              </Typography>
            </Box>

            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', p: 1 }}>
                <Typography variant="caption" sx={{ fontWeight: 600, color: 'text.secondary' }}>
                  提供商名称
                </Typography>
                <Typography variant="caption" sx={{ fontWeight: 600, color: 'text.primary' }}>
                  {application.companyName}
                </Typography>
              </Box>

              <Box sx={{ display: 'flex', justifyContent: 'space-between', p: 1 }}>
                <Typography variant="caption" sx={{ fontWeight: 600, color: 'text.secondary' }}>
                  负责人
                </Typography>
                <Typography variant="caption" sx={{ fontWeight: 600, color: 'text.primary' }}>
                  {application.userName}
                </Typography>
              </Box>

              <Box sx={{ display: 'flex', justifyContent: 'space-between', p: 1 }}>
                <Typography variant="caption" sx={{ fontWeight: 600, color: 'text.secondary' }}>
                  业务许可证
                </Typography>
                <Typography variant="caption" sx={{ fontWeight: 600, color: 'text.primary' }}>
                  {application.businessLicense || '未提供'}
                </Typography>
              </Box>

              <Box sx={{ display: 'flex', justifyContent: 'space-between', p: 1 }}>
                <Typography variant="caption" sx={{ fontWeight: 600, color: 'text.secondary' }}>
                  联系电话
                </Typography>
                <Typography variant="caption" sx={{ fontWeight: 600, color: 'text.primary' }}>
                  {application.contactPhone || '未提供'}
                </Typography>
              </Box>

              <Divider sx={{ my: 1 }} />

              <Box sx={{ display: 'flex', justifyContent: 'space-between', p: 1 }}>
                <Typography variant="caption" sx={{ fontWeight: 600, color: 'warning.main' }}>
                  提供商类型
                </Typography>
                <Typography variant="caption" sx={{ fontWeight: 600, color: 'warning.dark' }}>
                  {application.providerType || '未提供'}
                </Typography>
              </Box>

              <Box sx={{ display: 'flex', justifyContent: 'space-between', p: 1 }}>
                <Typography variant="caption" sx={{ fontWeight: 600, color: 'warning.main' }}>
                  服务领域
                </Typography>
                <Typography variant="caption" sx={{ fontWeight: 600, color: 'warning.dark', textAlign: 'right', maxWidth: '60%' }}>
                  {application.serviceAreas?.join(', ') || '未提供'}
                </Typography>
              </Box>
            </Box>
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12} md={6}>
        <Card sx={{ height: '100%', boxShadow: 'none', border: 'none' }}>
          <CardContent sx={{ p: 2 }}>
            <Box sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 1,
              mb: 2,
              pb: 1,
              borderBottom: '1px solid',
              borderColor: 'info.100'
            }}>
              <Avatar sx={{
                bgcolor: 'info.100',
                color: 'info.main',
                width: 24,
                height: 24
              }}>
                <Article fontSize="small" />
              </Avatar>
              <Typography variant="subtitle2" sx={{ fontWeight: 600, color: 'info.main' }}>
                服务描述
              </Typography>
            </Box>

            <Box sx={{
              p: 1.5,
              bgcolor: 'rgba(237, 108, 2, 0.04)',
              borderRadius: 1,
              minHeight: 80,
              maxHeight: 120,
              overflow: 'auto',
              border: '1px solid',
              borderColor: 'warning.100',
              borderLeft: '3px solid',
              borderLeftColor: 'warning.main'
            }}>
              <Typography variant="body2" sx={{
                lineHeight: 1.5,
                color: 'text.primary',
                fontSize: '13px'
              }}>
                {application.applicationReason}
              </Typography>
            </Box>

            <Box sx={{
              mt: 1.5,
              pt: 1.5,
              borderTop: '1px solid',
              borderColor: 'grey.200',
              bgcolor: 'grey.50',
              borderRadius: 0.5,
              p: 1
            }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5, mb: 0.5 }}>
                <AccessTime fontSize="small" color="action" />
                <Typography variant="caption" sx={{ fontWeight: 600, color: 'text.primary' }}>
                  申请时间: {application.submitTime}
                </Typography>
              </Box>
              {application.reviewTime && (
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                  <CheckCircle fontSize="small" color="success" />
                  <Typography variant="caption" sx={{ fontWeight: 600, color: 'success.main' }}>
                    审核时间: {application.reviewTime}
                  </Typography>
                </Box>
              )}
            </Box>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );

  // 根据角色类型渲染对应的详情组件
  const renderApplicationDetails = (application) => {
    switch (application.roleType) {
      case 'enterprise_user':
        return renderEnterpriseUserDetails(application);
      case 'agent_user':
        return renderAgentUserDetails(application);
      case 'channel_user':
        return renderChannelUserDetails(application);
      default:
        return renderEnterpriseUserDetails(application);
    }
  };

  const handleSubmitReview = async () => {
    if (!selectedApplication || !reviewForm.action || !reviewForm.feedback) {
      setSnackbar({
        open: true,
        message: '请选择审核决定并填写反馈意见',
        severity: 'warning'
      });
      return;
    }

    // 验证反馈内容长度（后端要求至少5个字符）
    if (reviewForm.feedback.trim().length < 5) {
      setSnackbar({
        open: true,
        message: '审核反馈至少需要5个字符，请提供更详细的审核意见',
        severity: 'warning'
      });
      return;
    }

    // 验证反馈内容长度上限（后端限制500个字符）
    if (reviewForm.feedback.trim().length > 500) {
      setSnackbar({
        open: true,
        message: '审核反馈不能超过500个字符',
        severity: 'warning'
      });
      return;
    }

    try {
      setLoading(true);

      // 根据角色类型调用不同的审核接口
      const { roleType, originalId } = selectedApplication;

      // 根据后端API要求构建请求数据
      let reviewData = {};

      if (roleType === 'enterprise_user') {
        // 企业审核：status 使用 verified/rejected（与后端保持一致）
        reviewData = {
          status: reviewForm.action === 'approve' ? 'verified' : 'rejected',
          note: reviewForm.feedback,
          notify_user: true
        };
      } else if (roleType === 'channel_user' || roleType === 'agent_user') {
        // 渠道商和代理商审核：status 使用 verified/rejected
        reviewData = {
          status: reviewForm.action === 'approve' ? 'verified' : 'rejected',
          note: reviewForm.feedback,
          notify_user: true
        };
      }

      let endpoint = '';
      if (roleType === 'enterprise_user') {
        endpoint = `/companies/${originalId}/verification`;
      } else if (roleType === 'channel_user') {
        endpoint = `/channels/${originalId}/verification`;
      } else if (roleType === 'agent_user') {
        endpoint = `/agents/${originalId}/verification`;
      }

      if (endpoint) {
        await apiService.put(endpoint, reviewData);

        setSnackbar({
          open: true,
          message: `申请已${reviewForm.action === 'approve' ? '通过' : '拒绝'}`,
          severity: 'success'
        });

        setReviewDialogOpen(false);
        setReviewForm({ action: '', feedback: '' });

        // 刷新数据
        fetchApplications();
      }
    } catch (err) {
      console.error('审核提交失败:', err);
      setSnackbar({
        open: true,
        message: err.message || '审核操作失败，请重试',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };



  if (loading) {
    return (
      <Box sx={{ minHeight: '100vh', backgroundColor: '#f5f5f5' }}>
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 400 }}>
          <CircularProgress />
        </Box>
      </Box>
    );
  }

  return (
    <Box sx={{ width: '100%', p: 3, backgroundColor: '#f5f5f5', minHeight: '100vh' }}>
      {/* 页面标题和操作按钮区域 */}
      <Box sx={{
        backgroundColor: '#fff',
        borderRadius: 3,
        border: '1px solid #e0e0e0',
        p: 4,
        mb: 3,
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
      }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <AssignmentTurnedIn sx={{ fontSize: 32, color: '#1976d2' }} />
            <Box>
              <Typography variant="h4" sx={{ fontWeight: 700, color: '#1a1a1a', mb: 0.5 }}>
                角色审批
              </Typography>
              <Typography variant="body1" sx={{ color: '#666' }}>
                审核用户角色申请，管理平台权限分配
              </Typography>
            </Box>
          </Box>
        </Box>
      </Box>

      {/* 错误提示 */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}



            {/* Filters */}
            <Card sx={{ mb: 3 }}>
              <CardContent>
                <Grid container spacing={2} alignItems="center">
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      placeholder="搜索申请..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Search />
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <FormControl fullWidth>
                      <InputLabel>角色筛选</InputLabel>
                      <Select
                        value={roleFilter}
                        label="角色筛选"
                        onChange={(e) => setRoleFilter(e.target.value)}
                      >
                        <MenuItem value="enterprise_user">企业用户</MenuItem>
                        <MenuItem value="agent_user">代理商</MenuItem>
                        <MenuItem value="channel_user">渠道商</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>

            {/* Applications Table */}
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    申请列表 ({filteredApplications.length})
                  </Typography>
                </Box>
                <TableContainer component={Paper} variant="outlined">
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>申请人</TableCell>
                        <TableCell>申请角色</TableCell>
                        <TableCell>公司名称</TableCell>
                        <TableCell>状态</TableCell>
                        <TableCell>申请时间</TableCell>
                        <TableCell>操作</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {filteredApplications.map((application) => (
                        <TableRow key={application.id} hover>
                          <TableCell>
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <Avatar sx={{ width: 40, height: 40, mr: 2 }}>
                                {application.userName.charAt(0)}
                              </Avatar>
                              <Box>
                                <Typography variant="body2" sx={{ fontWeight: 500 }}>
                                  {application.userName}
                                </Typography>
                                <Typography variant="caption" color="text.secondary">
                                  {application.userEmail}
                                </Typography>
                              </Box>
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={getRoleLabel(application.roleType)}
                              color={getRoleColor(application.roleType)}
                              size="small"
                            />
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">
                              {application.companyName}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={getStatusLabel(application.status)}
                              color={getStatusColor(application.status)}
                              size="small"
                            />
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2" color="text.secondary">
                              {application.submitTime}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <IconButton
                              size="small"
                              onClick={() => handleReviewApplication(application)}
                              color="primary"
                            >
                              <Visibility fontSize="small" />
                            </IconButton>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </CardContent>
            </Card>

            {/* Review Dialog */}
            <Dialog
              open={reviewDialogOpen}
              onClose={() => setReviewDialogOpen(false)}
              maxWidth="lg"
              fullWidth
              PaperProps={{
                sx: {
                  borderRadius: 3,
                  boxShadow: '0 8px 40px rgba(0, 0, 0, 0.12)',
                  maxHeight: '90vh',
                  width: '85%',
                  maxWidth: '1100px',
                  margin: 'auto'
                }
              }}
            >

              <DialogContent sx={{ p: 3 }}>
                {selectedApplication && (
                  <Grid container spacing={3} justifyContent="center" alignItems="flex-start">
                    {/* 申请信息卡片 */}
                    <Grid item xs={12} md={7}>
                      <Card variant="outlined" sx={{ borderRadius: 2 }}>
                        <CardContent sx={{ p: 0 }}>
                          {/* 申请人头部信息 */}
                          <Box sx={{
                            background: `linear-gradient(135deg, ${getRoleColor(selectedApplication.roleType) === 'primary' ? '#1976d2' : getRoleColor(selectedApplication.roleType) === 'success' ? '#2e7d32' : '#ed6c02'} 0%, ${getRoleColor(selectedApplication.roleType) === 'primary' ? '#1565c0' : getRoleColor(selectedApplication.roleType) === 'success' ? '#1b5e20' : '#e65100'} 100%)`,
                            color: 'white',
                            p: 2,
                            borderRadius: '8px 8px 0 0'
                          }}>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                              <Avatar sx={{
                                bgcolor: 'rgba(255,255,255,0.2)',
                                color: 'white',
                                width: 48,
                                height: 48,
                                border: '2px solid rgba(255,255,255,0.3)'
                              }}>
                                {selectedApplication.roleType === 'enterprise_user' && <Business />}
                                {selectedApplication.roleType === 'channel_user' && <Article />}
                                {selectedApplication.roleType === 'agent_user' && <Group />}
                              </Avatar>
                              <Box sx={{ flex: 1 }}>
                                <Typography variant="h6" sx={{ fontWeight: 600, mb: 0.5, color: 'white' }}>
                                  {selectedApplication.userName}
                                </Typography>
                                <Box sx={{ display: 'flex', gap: 1, mb: 0.5 }}>
                                  <Chip
                                    label={getRoleLabel(selectedApplication.roleType)}
                                    sx={{
                                      bgcolor: 'rgba(255,255,255,0.2)',
                                      color: 'white',
                                      fontWeight: 600,
                                      fontSize: '11px',
                                      height: '20px'
                                    }}
                                    size="small"
                                  />
                                  <Chip
                                    label={getStatusLabel(selectedApplication.status)}
                                    sx={{
                                      bgcolor: selectedApplication.status === 'pending' ? 'rgba(255,193,7,0.9)' : 'rgba(255,255,255,0.2)',
                                      color: selectedApplication.status === 'pending' ? '#000' : 'white',
                                      fontWeight: 600,
                                      fontSize: '11px',
                                      height: '20px'
                                    }}
                                    size="small"
                                  />
                                </Box>
                                <Typography variant="body2" sx={{ color: 'rgba(255,255,255,0.9)' }}>
                                  {selectedApplication.userEmail}
                                </Typography>
                              </Box>
                            </Box>
                          </Box>

                          {/* 详细信息 */}
                          <Box sx={{ p: 2 }}>
                            {renderApplicationDetails(selectedApplication)}
                          </Box>
                        </CardContent>
                      </Card>
                    </Grid>

                    {/* 审核操作卡片 */}
                    <Grid item xs={12} md={4}>
                      <Card variant="outlined" sx={{ borderRadius: 2, height: 'fit-content' }}>
                        <CardContent sx={{ p: 2 }}>
                          <Box sx={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: 1,
                            mb: 2
                          }}>
                            <Avatar sx={{
                              bgcolor: 'primary.main',
                              width: 28,
                              height: 28
                            }}>
                              <Assignment fontSize="small" />
                            </Avatar>
                            <Box>
                              <Typography variant="subtitle1" sx={{ fontWeight: 600, color: 'primary.main' }}>
                                审核操作
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                请仔细审核申请信息并提供详细反馈
                              </Typography>
                            </Box>
                          </Box>

                          <Box sx={{ mb: 2 }}>
                            <Typography variant="body2" sx={{ fontWeight: 600, mb: 1, color: 'text.primary' }}>
                              审核反馈 <span style={{ color: '#f44336' }}>*</span>
                            </Typography>
                            <TextField
                              fullWidth
                              multiline
                              rows={4}
                              value={reviewForm.feedback}
                              onChange={(e) => setReviewForm(prev => ({ ...prev, feedback: e.target.value }))}
                              placeholder={
                                reviewForm.action === 'approve'
                                  ? '请说明通过的理由，如：申请材料完整，符合平台要求...'
                                  : reviewForm.action === 'reject'
                                  ? '请详细说明拒绝的原因，如：营业执照信息不完整，需要补充...'
                                  : '请提供详细的审核意见...'
                              }
                              size="small"
                              helperText={
                                <span style={{ display: 'flex', justifyContent: 'space-between', width: '100%' }}>
                                  <span style={{ fontSize: '11px' }}>
                                    此反馈将发送给申请人，请确保内容准确详细（至少5个字符）
                                  </span>
                                  <span style={{
                                    color: reviewForm.feedback.length < 5 ? '#f44336' : '#4caf50',
                                    fontSize: '11px'
                                  }}>
                                    {reviewForm.feedback.length}/500
                                  </span>
                                </span>
                              }
                              inputProps={{ maxLength: 500 }}
                            />
                          </Box>

                          <Box sx={{ mb: 2 }}>
                            <Typography variant="body2" sx={{ fontWeight: 600, mb: 1, color: 'text.primary' }}>
                              审核决定
                            </Typography>
                            <Box sx={{ display: 'flex', gap: 1, mb: 1.5 }}>
                              <Button
                                variant={reviewForm.action === 'approve' ? 'contained' : 'outlined'}
                                color="success"
                                size="small"
                                startIcon={<CheckCircle fontSize="small" />}
                                onClick={() => setReviewForm(prev => ({ ...prev, action: 'approve' }))}
                                sx={{
                                  flex: 1,
                                  py: 0.5,
                                  textTransform: 'none',
                                  fontWeight: 600,
                                  fontSize: '12px'
                                }}
                              >
                                通过申请
                              </Button>
                              <Button
                                variant={reviewForm.action === 'reject' ? 'contained' : 'outlined'}
                                color="error"
                                size="small"
                                startIcon={<Cancel fontSize="small" />}
                                onClick={() => setReviewForm(prev => ({ ...prev, action: 'reject' }))}
                                sx={{
                                  flex: 1,
                                  py: 0.5,
                                  textTransform: 'none',
                                  fontWeight: 600,
                                  fontSize: '12px'
                                }}
                              >
                                拒绝申请
                              </Button>
                            </Box>

                            {reviewForm.action && (
                              <Alert
                                severity={reviewForm.action === 'approve' ? 'success' : 'warning'}
                                sx={{ borderRadius: 1, fontSize: '11px', py: 0.5 }}
                              >
                                {reviewForm.action === 'approve'
                                  ? '通过后，申请人将获得相应角色权限'
                                  : '拒绝后，申请人需要重新提交申请'}
                              </Alert>
                            )}
                          </Box>
                        </CardContent>
                      </Card>
                    </Grid>
                  </Grid>
                )}
              </DialogContent>
              <DialogActions sx={{ p: 3, gap: 2, justifyContent: 'flex-end' }}>
                <Button
                  onClick={() => setReviewDialogOpen(false)}
                  variant="outlined"
                  size="large"
                >
                  取消
                </Button>
                <Button
                  onClick={handleSubmitReview}
                  variant="contained"
                  size="large"
                  disabled={!reviewForm.action || !reviewForm.feedback || reviewForm.feedback.trim().length < 5}
                  sx={{
                    minWidth: 120,
                    background: reviewForm.action === 'approve'
                      ? 'linear-gradient(135deg, #4caf50 0%, #45a049 100%)'
                      : reviewForm.action === 'reject'
                      ? 'linear-gradient(135deg, #f44336 0%, #d32f2f 100%)'
                      : 'linear-gradient(135deg, #2196f3 0%, #1976d2 100%)',
                    '&:hover': {
                      background: reviewForm.action === 'approve'
                        ? 'linear-gradient(135deg, #45a049 0%, #388e3c 100%)'
                        : reviewForm.action === 'reject'
                        ? 'linear-gradient(135deg, #d32f2f 0%, #c62828 100%)'
                        : 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',
                    }
                  }}
                >
                  {reviewForm.action === 'approve' ? '通过申请' :
                   reviewForm.action === 'reject' ? '拒绝申请' : '提交审核'}
                </Button>
              </DialogActions>
            </Dialog>

      {/* Snackbar 通知 */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar(prev => ({ ...prev, open: false }))}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert
          onClose={() => setSnackbar(prev => ({ ...prev, open: false }))}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
}

export default RoleApproval;
