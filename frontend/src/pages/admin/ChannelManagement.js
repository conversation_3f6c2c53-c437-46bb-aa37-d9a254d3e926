import React, { useState, useEffect } from 'react';
import {
  Box,
  Typo<PERSON>,
  Grid,
  Card,
  CardContent,
  Avatar,
  Chip,
  Button,
  IconButton,
  TextField,
  InputAdornment,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Alert,
  Tooltip,
} from '@mui/material';
import {
  Search,
  Edit,
  Delete,
  Visibility,
  CheckCircle,
  Add,
  Category,
  Warning,
  Store,
  ToggleOn,
  ToggleOff,
} from '@mui/icons-material';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
} from '@mui/material';
import apiService from '../../services/api';

function ChannelManagement() {
  const [loading, setLoading] = useState(true);
  const [categories, setCategories] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');

  const [selectedChannel, setSelectedChannel] = useState(null);
  const [channelDetailOpen, setChannelDetailOpen] = useState(false);
  const [categoryDialogOpen, setCategoryDialogOpen] = useState(false);

  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [editCategory, setEditCategory] = useState(null);
  const [newCategory, setNewCategory] = useState({
    category_name: '',
    category_code: '',
    category_description: '',
    is_active: true,
  });
  const [alert, setAlert] = useState({ open: false, message: '', severity: 'success' });





  // 加载数据
  useEffect(() => {
    loadCategories();
  }, []);



  // 自动关闭提示
  useEffect(() => {
    if (alert.open) {
      const timer = setTimeout(() => {
        setAlert({ ...alert, open: false });
      }, 4000); // 4秒后自动关闭
      return () => clearTimeout(timer);
    }
  }, [alert]);

  const loadCategories = async () => {
    try {
      setLoading(true);
      // 调用渠道分类列表接口
      const response = await apiService.getChannelCategories();
      if (response.success) {
        const categoriesData = Array.isArray(response.data.categories) ? response.data.categories : [];
        setCategories(categoriesData);
      } else {
        setCategories([]);
      }
    } catch (error) {
      console.error('加载分类数据失败:', error);
      setCategories([]);
    } finally {
      setLoading(false);
    }
  };



  // 关闭对话框并重置表单
  const handleCloseDialog = () => {
    setCategoryDialogOpen(false);
    setNewCategory({
      category_name: '',
      category_code: '',
      category_description: '',
      is_active: true,
    });
  };

  // 创建渠道分类
  const handleCreateCategory = async () => {
    try {
      // 准备API数据
      const apiData = {
        category_name: newCategory.category_name,
        category_code: newCategory.category_code,
        category_description: newCategory.category_description,
        is_active: newCategory.is_active,
      };

      const response = await apiService.createChannelCategory(apiData);
      if (response.success) {
        // 重新加载分类列表
        await loadCategories();
        // 关闭对话框
        handleCloseDialog();
        // 显示成功提示
        setAlert({
          open: true,
          message: `渠道分类"${newCategory.category_name}"创建成功！`,
          severity: 'success'
        });
      } else {
        // 显示错误提示
        setAlert({
          open: true,
          message: response.message || '创建失败，请重试',
          severity: 'error'
        });
      }
    } catch (error) {
      console.error('创建分类失败:', error);
      // 显示错误提示
      setAlert({
        open: true,
        message: '创建失败，请检查网络连接或联系管理员',
        severity: 'error'
      });
    }
  };

  // 编辑渠道分类
  const handleEditCategory = async () => {
    try {
      // 准备API数据
      const apiData = {
        category_name: editCategory.category_name,
        category_code: editCategory.category_code,
        category_description: editCategory.category_description,
      };

      const response = await apiService.updateChannelCategory(editCategory.id, apiData);
      if (response.success) {
        // 重新加载分类列表
        await loadCategories();
        // 关闭对话框
        handleCloseEditDialog();
        // 显示成功提示
        setAlert({
          open: true,
          message: `渠道分类"${editCategory.category_name}"更新成功！`,
          severity: 'success'
        });
      } else {
        // 显示错误提示
        setAlert({
          open: true,
          message: response.message || '更新失败，请重试',
          severity: 'error'
        });
      }
    } catch (error) {
      console.error('更新分类失败:', error);
      // 显示错误提示
      setAlert({
        open: true,
        message: '更新失败，请检查网络连接或联系管理员',
        severity: 'error'
      });
    }
  };

  // 关闭编辑对话框
  const handleCloseEditDialog = () => {
    setEditDialogOpen(false);
    setEditCategory(null);
  };

  // 切换分类状态
  const handleToggleCategoryStatus = async (category) => {
    const newStatus = !category.is_active;
    const actionText = newStatus ? '启用' : '禁用';

    try {
      const response = await apiService.updateChannelCategory(category.id, {
        category_name: category.category_name,
        category_code: category.category_code,
        category_description: category.category_description,
        is_active: newStatus,
      });

      if (response.success) {
        setAlert({
          open: true,
          message: `分类"${category.category_name}"已${actionText}！`,
          severity: 'success'
        });
        await loadCategories();
      } else {
        setAlert({
          open: true,
          message: response.message || `${actionText}失败，请重试`,
          severity: 'error'
        });
      }
    } catch (error) {
      console.error(`${actionText}分类失败:`, error);
      setAlert({
        open: true,
        message: `${actionText}失败，请检查网络连接或联系管理员`,
        severity: 'error'
      });
    }
  };









  const handleChannelAction = async (action, category) => {
    if (!category) return;

    try {
      switch (action) {
        case 'delete':
          const response = await apiService.deleteChannelCategory(category.id);
          if (response.success) {
            setAlert({
              open: true,
              message: `分类"${category.category_name}"删除成功！`,
              severity: 'success'
            });
            await loadCategories();
          } else {
            setAlert({
              open: true,
              message: response.message || '删除失败，请重试',
              severity: 'error'
            });
          }
          break;
        default:
          break;
      }
    } catch (error) {
      console.error('操作失败:', error);
      setAlert({
        open: true,
        message: '操作失败，请检查网络连接或联系管理员',
        severity: 'error'
      });
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ width: '100%', p: 3, backgroundColor: '#f5f5f5', minHeight: '100vh' }}>
      {/* 提示信息 */}
      {alert.open && (
        <Alert
          severity={alert.severity}
          onClose={() => setAlert({ ...alert, open: false })}
          sx={{ mb: 3, borderRadius: 2 }}
        >
          {alert.message}
        </Alert>
      )}

      {/* 页面标题和操作按钮区域 */}
      <Box sx={{
        backgroundColor: '#fff',
        borderRadius: 3,
        border: '1px solid #e0e0e0',
        p: 4,
        mb: 3,
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
      }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Store sx={{ fontSize: 32, color: '#1976d2' }} />
            <Box>
              <Typography variant="h4" sx={{ fontWeight: 700, color: '#1a1a1a', mb: 0.5 }}>
                渠道管理
              </Typography>
            </Box>
          </Box>
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={() => setCategoryDialogOpen(true)}
            sx={{ borderRadius: 2 }}
          >
            添加新渠道
          </Button>
        </Box>
      </Box>

      {/* 搜索过滤和渠道列表 */}
      <Card sx={{ borderRadius: 3, boxShadow: '0 2px 8px rgba(0,0,0,0.1)' }}>
        <CardContent>
          {/* 搜索和过滤区域 */}
          <Grid container spacing={3} alignItems="center" sx={{ mb: 3 }}>
            <Grid item xs={12} md={8}>
              <TextField
                fullWidth
                placeholder="搜索分类信息..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Search />
                    </InputAdornment>
                  ),
                }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                    minHeight: 56,
                  },
                  '& .MuiInputBase-input': {
                    fontSize: '0.95rem',
                  },
                  '& .MuiInputBase-input::placeholder': {
                    fontSize: '0.9rem',
                    opacity: 0.7,
                  }
                }}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <FormControl fullWidth>
                <InputLabel>状态筛选</InputLabel>
                <Select
                  value={statusFilter}
                  label="状态筛选"
                  onChange={(e) => setStatusFilter(e.target.value)}
                  sx={{ borderRadius: 2 }}
                >
                  <MenuItem value="all">全部状态</MenuItem>
                  <MenuItem value="active">启用</MenuItem>
                  <MenuItem value="inactive">禁用</MenuItem>
                </Select>
              </FormControl>
            </Grid>

          </Grid>

          {/* 渠道列表 */}
          <Box sx={{ border: '1px solid #e0e0e0', borderRadius: 2 }}>
            <Box sx={{
              p: 3,
              borderBottom: '1px solid #e0e0e0',
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              borderRadius: '8px 8px 0 0'
            }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Store sx={{ color: 'white', fontSize: 24 }} />
                <Typography variant="h6" sx={{ fontWeight: 600, color: 'white' }}>
                  渠道列表 ({Array.isArray(categories) ? categories.length : 0})
                </Typography>
              </Box>
            </Box>
            <TableContainer component={Paper} sx={{ boxShadow: 'none' }}>
              <Table sx={{ minWidth: 650 }}>
                <TableHead>
                  <TableRow sx={{ backgroundColor: '#f8fafc' }}>
                    <TableCell sx={{ fontWeight: 600, color: '#374151', py: 2 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Category sx={{ fontSize: 18 }} />
                        渠道信息
                      </Box>
                    </TableCell>
                    <TableCell sx={{ fontWeight: 600, color: '#374151', py: 2 }}>分类代码</TableCell>
                    <TableCell sx={{ fontWeight: 600, color: '#374151', py: 2 }}>描述</TableCell>
                    <TableCell sx={{ fontWeight: 600, color: '#374151', py: 2 }}>状态</TableCell>
                    <TableCell sx={{ fontWeight: 600, color: '#374151', py: 2 }}>关联数量</TableCell>
                    <TableCell sx={{ fontWeight: 600, color: '#374151', py: 2 }}>创建时间</TableCell>
                    <TableCell sx={{ fontWeight: 600, color: '#374151', py: 2, textAlign: 'center' }}>操作</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {Array.isArray(categories) ? categories.map((category) => (
                    <TableRow
                      key={category.id}
                      sx={{
                        '&:hover': {
                          backgroundColor: '#f8fafc',
                          '& .action-buttons': {
                            opacity: 1
                          }
                        },
                        transition: 'all 0.2s ease'
                      }}
                    >
                      {/* 渠道信息 */}
                      <TableCell sx={{ py: 3 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                          <Avatar sx={{
                            bgcolor: '#667eea',
                            width: 40,
                            height: 40,
                            boxShadow: '0 2px 8px rgba(102, 126, 234, 0.3)'
                          }}>
                            <Category />
                          </Avatar>
                          <Box>
                            <Typography variant="subtitle1" sx={{ fontWeight: 600, color: '#1e293b' }}>
                              {category.category_name}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              ID: {category.id}
                            </Typography>
                          </Box>
                        </Box>
                      </TableCell>

                      {/* 分类代码 */}
                      <TableCell sx={{ py: 3 }}>
                        <Chip
                          label={category.category_code}
                          variant="outlined"
                          size="small"
                          sx={{
                            fontFamily: 'monospace',
                            backgroundColor: '#f1f5f9',
                            borderColor: '#cbd5e1'
                          }}
                        />
                      </TableCell>

                      {/* 描述 */}
                      <TableCell sx={{ py: 3, maxWidth: 200 }}>
                        <Typography
                          variant="body2"
                          color="text.secondary"
                          sx={{
                            display: '-webkit-box',
                            WebkitLineClamp: 2,
                            WebkitBoxOrient: 'vertical',
                            overflow: 'hidden',
                            lineHeight: 1.4
                          }}
                        >
                          {category.category_description || '暂无描述'}
                        </Typography>
                      </TableCell>

                      {/* 状态 */}
                      <TableCell sx={{ py: 3 }}>
                        <Chip
                          label={category.is_active ? '启用' : '禁用'}
                          color={category.is_active ? 'success' : 'error'}
                          size="small"
                          icon={category.is_active ? <CheckCircle /> : <Warning />}
                          sx={{ fontWeight: 500 }}
                        />
                      </TableCell>

                      {/* 关联数量 */}
                      <TableCell sx={{ py: 3 }}>
                        <Typography variant="body2" sx={{ fontWeight: 600, color: '#374151' }}>
                          {category.provider_count || 0}
                        </Typography>
                      </TableCell>

                      {/* 创建时间 */}
                      <TableCell sx={{ py: 3 }}>
                        <Typography variant="body2" color="text.secondary">
                          {category.created_at ? new Date(category.created_at).toLocaleDateString('zh-CN') : '-'}
                        </Typography>
                      </TableCell>

                      {/* 操作按钮 */}
                      <TableCell sx={{ py: 3, textAlign: 'center' }}>
                        <Box
                          className="action-buttons"
                          sx={{
                            display: 'flex',
                            justifyContent: 'center',
                            gap: 1,
                            opacity: 0.7,
                            transition: 'opacity 0.2s ease'
                          }}
                        >
                          <Tooltip title="查看详情">
                            <IconButton
                              size="small"
                              onClick={() => {
                                setSelectedChannel(category);
                                setChannelDetailOpen(true);
                              }}
                              sx={{
                                bgcolor: '#3b82f6',
                                color: 'white',
                                '&:hover': { bgcolor: '#2563eb' },
                                width: 32,
                                height: 32
                              }}
                            >
                              <Visibility fontSize="small" />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="编辑分类">
                            <IconButton
                              size="small"
                              onClick={() => {
                                setEditCategory({
                                  id: category.id,
                                  category_name: category.category_name || '',
                                  category_code: category.category_code || '',
                                  category_description: category.category_description || '',
                                  is_active: category.is_active ?? true,
                                });
                                setEditDialogOpen(true);
                              }}
                              sx={{
                                bgcolor: '#ff9800',
                                color: 'white',
                                '&:hover': { bgcolor: '#f57c00' },
                                width: 32,
                                height: 32
                              }}
                            >
                              <Edit fontSize="small" />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title={category.is_active ? '禁用分类' : '启用分类'}>
                            <IconButton
                              size="small"
                              onClick={() => handleToggleCategoryStatus(category)}
                              sx={{
                                bgcolor: category.is_active ? '#f59e0b' : '#10b981',
                                color: 'white',
                                '&:hover': {
                                  bgcolor: category.is_active ? '#d97706' : '#059669'
                                },
                                width: 32,
                                height: 32
                              }}
                            >
                              {category.is_active ? <ToggleOff fontSize="small" /> : <ToggleOn fontSize="small" />}
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="删除分类">
                            <IconButton
                              size="small"
                              onClick={() => {
                                if (window.confirm(`确定要删除分类"${category.category_name}"吗？此操作不可撤销。`)) {
                                  handleChannelAction('delete', category);
                                }
                              }}
                              sx={{
                                bgcolor: '#ef4444',
                                color: 'white',
                                '&:hover': { bgcolor: '#dc2626' },
                                width: 32,
                                height: 32
                              }}
                            >
                              <Delete fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </TableCell>
                    </TableRow>
                  )) : null}
                </TableBody>
              </Table>
            </TableContainer>

            {/* 空状态 */}
            {(!Array.isArray(categories) || categories.length === 0) && (
              <Box sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                py: 8
              }}>
                <Category sx={{ fontSize: 64, color: 'grey.300', mb: 2 }} />
                <Typography variant="h6" color="text.secondary" sx={{ mb: 1 }}>
                  暂无渠道数据
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  点击上方"添加新渠道"按钮创建第一个渠道
                </Typography>
              </Box>
            )}
          </Box>
        </CardContent>
      </Card>



      {/* 添加分类对话框 */}
      <Dialog
        open={categoryDialogOpen}
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
        scroll="paper"
        PaperProps={{
          sx: {
            borderRadius: 3,
            maxHeight: '95vh',
            margin: 1,
            overflow: 'visible'
          }
        }}
      >
        <DialogTitle sx={{
          pb: 2,
          borderBottom: '1px solid #e0e0e0',
          display: 'flex',
          alignItems: 'center',
          gap: 2
        }}>
          <Box sx={{
            width: 40,
            height: 40,
            borderRadius: 2,
            bgcolor: '#1976d2',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            <Add sx={{ color: 'white' }} />
          </Box>
          <Box>
            <Typography variant="h6" sx={{ fontWeight: 600 }}>
              添加新渠道
            </Typography>
          </Box>
        </DialogTitle>
        <DialogContent sx={{
          pt: 3,
          pb: 4,
          minHeight: '500px',
          '&::-webkit-scrollbar': {
            width: '6px',
          },
          '&::-webkit-scrollbar-track': {
            background: '#f1f1f1',
            borderRadius: '3px',
          },
          '&::-webkit-scrollbar-thumb': {
            background: '#c1c1c1',
            borderRadius: '3px',
          },
          '&::-webkit-scrollbar-thumb:hover': {
            background: '#a8a8a8',
          },
        }}>
          <Box>
            {/* 基本信息 */}
            <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2, color: '#1976d2' }}>
              基本信息
            </Typography>

            {/* 分类名称 */}
            <Box sx={{ mb: 3 }}>
              <TextField
                fullWidth
                label="分类名称"
                value={newCategory.category_name}
                onChange={(e) => setNewCategory({ ...newCategory, category_name: e.target.value })}
                required
                variant="outlined"
                placeholder="请输入分类名称"
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                    '&:hover fieldset': {
                      borderColor: '#1976d2',
                    }
                  }
                }}
              />
            </Box>

            {/* 分类代码 */}
            <Box sx={{ mb: 3 }}>
              <TextField
                fullWidth
                label="分类代码"
                value={newCategory.category_code}
                onChange={(e) => setNewCategory({ ...newCategory, category_code: e.target.value })}
                required
                variant="outlined"
                placeholder="请输入分类代码"
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                    '&:hover fieldset': {
                      borderColor: '#1976d2',
                    }
                  }
                }}
              />
            </Box>

            {/* 分类描述 */}
            <Box sx={{ mb: 3 }}>
              <TextField
                fullWidth
                label="分类描述"
                multiline
                rows={4}
                value={newCategory.category_description}
                onChange={(e) => setNewCategory({ ...newCategory, category_description: e.target.value })}
                variant="outlined"
                placeholder="请输入分类的详细描述"
                sx={{
                  width: '100%',
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                    '&:hover fieldset': {
                      borderColor: '#1976d2',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: '#1976d2',
                    }
                  },
                  '& .MuiInputLabel-root.Mui-focused': {
                    color: '#1976d2',
                  }
                }}
              />
            </Box>

            {/* 状态设置 */}
            <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2, color: '#1976d2' }}>
              状态设置
            </Typography>
            <Box sx={{ mb: 2 }}>
              <FormControl fullWidth variant="outlined">
                <InputLabel>状态</InputLabel>
                <Select
                  value={newCategory.is_active}
                  label="状态"
                  onChange={(e) => setNewCategory({ ...newCategory, is_active: e.target.value })}
                  displayEmpty
                  sx={{
                    borderRadius: 2,
                    '&:hover .MuiOutlinedInput-notchedOutline': {
                      borderColor: '#1976d2',
                    }
                  }}
                >
                  <MenuItem value={true}>启用</MenuItem>
                  <MenuItem value={false}>禁用</MenuItem>
                </Select>
              </FormControl>
            </Box>
          </Box>
        </DialogContent>
        <DialogActions sx={{
          p: 3,
          borderTop: '1px solid #e0e0e0',
          gap: 2
        }}>
          <Button
            onClick={handleCloseDialog}
            variant="outlined"
            sx={{
              borderRadius: 2,
              px: 3,
              py: 1,
              borderColor: '#e0e0e0',
              color: '#666',
              '&:hover': {
                borderColor: '#1976d2',
                backgroundColor: '#f5f5f5'
              }
            }}
          >
            取消
          </Button>
          <Button
            onClick={handleCreateCategory}
            variant="contained"
            disabled={!newCategory.category_name?.trim() || !newCategory.category_code?.trim()}
            sx={{
              borderRadius: 2,
              px: 4,
              py: 1,
              background: 'linear-gradient(45deg, #1976d2 30%, #42a5f5 90%)',
              boxShadow: '0 3px 5px 2px rgba(25, 118, 210, .3)',
              '&:hover': {
                background: 'linear-gradient(45deg, #1565c0 30%, #1976d2 90%)',
              },
              '&:disabled': {
                background: '#e0e0e0',
                color: '#999'
              }
            }}
          >
            创建分类
          </Button>
        </DialogActions>
      </Dialog>

      {/* 渠道详情对话框 */}
      <Dialog
        open={channelDetailOpen}
        onClose={() => setChannelDetailOpen(false)}
        maxWidth="lg"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 3,
            maxHeight: '90vh',
            overflow: 'hidden'
          }
        }}
      >
        <DialogTitle sx={{
          pb: 2,
          borderBottom: '1px solid #e0e0e0',
          display: 'flex',
          alignItems: 'center',
          gap: 2
        }}>
          <Box sx={{
            width: 40,
            height: 40,
            borderRadius: 2,
            bgcolor: '#2e7d32',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            <Store sx={{ color: 'white' }} />
          </Box>
          <Box>
            <Typography variant="h6" sx={{ fontWeight: 600 }}>
              渠道详情
            </Typography>
          </Box>
        </DialogTitle>

        <DialogContent sx={{
          p: 4,
          bgcolor: '#f8fafc',
          '&::-webkit-scrollbar': {
            width: '6px',
          },
          '&::-webkit-scrollbar-track': {
            background: '#f1f1f1',
            borderRadius: '3px',
          },
          '&::-webkit-scrollbar-thumb': {
            background: '#c1c1c1',
            borderRadius: '3px',
          },
          '&::-webkit-scrollbar-thumb:hover': {
            background: '#a8a8a8',
          },
        }}>
          {selectedChannel && (
            <Box>
              {/* 详细信息区域 */}
              <Grid container spacing={4}>
                  {/* 左侧 - 基本信息 */}
                  <Grid item xs={12} md={6}>
                    <Typography variant="h6" sx={{
                      fontWeight: 700,
                      mb: 3,
                      color: '#2d3748'
                    }}>
                      基本信息
                    </Typography>

                    <Box sx={{ space: 3 }}>
                      <Box sx={{ mb: 3 }}>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 1, fontWeight: 500 }}>
                          分类名称
                        </Typography>
                        <Typography variant="body1" sx={{
                          fontWeight: 600,
                          color: '#2d3748',
                          bgcolor: '#f7fafc',
                          p: 2,
                          borderRadius: 2,
                          border: '1px solid #e2e8f0'
                        }}>
                          {selectedChannel.category_name}
                        </Typography>
                      </Box>

                      <Box sx={{ mb: 3 }}>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 1, fontWeight: 500 }}>
                          分类代码
                        </Typography>
                        <Typography variant="body1" sx={{
                          fontFamily: 'monospace',
                          fontWeight: 600,
                          color: '#2d3748',
                          bgcolor: '#f7fafc',
                          p: 2,
                          borderRadius: 2,
                          border: '1px solid #e2e8f0'
                        }}>
                          {selectedChannel.category_code}
                        </Typography>
                      </Box>

                      <Box>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 1, fontWeight: 500 }}>
                          分类描述
                        </Typography>
                        <Typography variant="body1" sx={{
                          fontWeight: 500,
                          color: '#2d3748',
                          bgcolor: '#f7fafc',
                          p: 2,
                          borderRadius: 2,
                          border: '1px solid #e2e8f0',
                          minHeight: 60,
                          display: 'flex',
                          alignItems: 'center'
                        }}>
                          {selectedChannel.category_description || '暂无描述'}
                        </Typography>
                      </Box>
                    </Box>
                  </Grid>

                  {/* 右侧 - 统计信息 */}
                  <Grid item xs={12} md={6}>
                    <Typography variant="h6" sx={{
                      fontWeight: 700,
                      mb: 3,
                      color: '#2d3748'
                    }}>
                      统计信息
                    </Typography>

                    <Grid container spacing={2}>
                      <Grid item xs={6}>
                        <Box sx={{
                          textAlign: 'center',
                          p: 2,
                          bgcolor: '#f0fff4',
                          borderRadius: 2,
                          border: '1px solid #c6f6d5'
                        }}>
                          <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                            创建时间
                          </Typography>
                          <Typography variant="body1" sx={{ fontWeight: 600, color: '#2d3748' }}>
                            {new Date(selectedChannel.created_at).toLocaleDateString('zh-CN')}
                          </Typography>
                        </Box>
                      </Grid>

                      <Grid item xs={6}>
                        <Box sx={{
                          textAlign: 'center',
                          p: 2,
                          bgcolor: '#fffaf0',
                          borderRadius: 2,
                          border: '1px solid #fbd38d'
                        }}>
                          <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                            最后更新
                          </Typography>
                          <Typography variant="body1" sx={{ fontWeight: 600, color: '#2d3748' }}>
                            {new Date(selectedChannel.updated_at).toLocaleDateString('zh-CN')}
                          </Typography>
                        </Box>
                      </Grid>
                    </Grid>
                  </Grid>
                </Grid>
            </Box>
          )}
        </DialogContent>
        <DialogActions sx={{
          p: 2,
          borderTop: '1px solid #e2e8f0',
          bgcolor: '#f8fafc',
          gap: 2,
          justifyContent: 'flex-end'
        }}>
          <Button
            onClick={() => setChannelDetailOpen(false)}
            variant="contained"
            size="small"
            sx={{
              borderRadius: 2,
              px: 3,
              py: 1,
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              color: 'white',
              fontWeight: 600,
              fontSize: '0.875rem',
              boxShadow: '0 2px 8px rgba(102, 126, 234, 0.3)',
              '&:hover': {
                background: 'linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%)',
                boxShadow: '0 4px 12px rgba(102, 126, 234, 0.5)',
                transform: 'translateY(-1px)'
              },
              transition: 'all 0.3s ease'
            }}
          >
            关闭
          </Button>
        </DialogActions>
      </Dialog>

      {/* 编辑分类对话框 */}
      <Dialog
        open={editDialogOpen}
        onClose={handleCloseEditDialog}
        maxWidth="md"
        fullWidth
        scroll="paper"
        PaperProps={{
          sx: {
            borderRadius: 3,
            maxHeight: '95vh',
            margin: 1,
            overflow: 'visible'
          }
        }}
      >
        <DialogTitle sx={{
          pb: 2,
          borderBottom: '1px solid #e0e0e0',
          display: 'flex',
          alignItems: 'center',
          gap: 2
        }}>
          <Box sx={{
            width: 40,
            height: 40,
            borderRadius: 2,
            bgcolor: '#ff9800',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            <Edit sx={{ color: 'white' }} />
          </Box>
          <Box>
            <Typography variant="h6" sx={{ fontWeight: 600 }}>
              编辑渠道
            </Typography>
          </Box>
        </DialogTitle>
        <DialogContent sx={{
          pt: 3,
          pb: 4,
          minHeight: '500px',
          '&::-webkit-scrollbar': {
            width: '6px',
          },
          '&::-webkit-scrollbar-track': {
            background: '#f1f1f1',
            borderRadius: '3px',
          },
          '&::-webkit-scrollbar-thumb': {
            background: '#c1c1c1',
            borderRadius: '3px',
          },
          '&::-webkit-scrollbar-thumb:hover': {
            background: '#a8a8a8',
          },
        }}>
          {editCategory && (
            <Box>
              {/* 基本信息 */}
              <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2, color: '#1976d2' }}>
                基本信息
              </Typography>

              {/* 分类名称 */}
              <Box sx={{ mb: 3 }}>
                <TextField
                  fullWidth
                  label="分类名称"
                  value={editCategory.category_name || ''}
                  onChange={(e) => setEditCategory({ ...editCategory, category_name: e.target.value })}
                  required
                  variant="outlined"
                  placeholder="请输入分类名称"
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 2,
                      '&:hover fieldset': {
                        borderColor: '#1976d2',
                      }
                    }
                  }}
                />
              </Box>

              {/* 分类代码 */}
              <Box sx={{ mb: 3 }}>
                <TextField
                  fullWidth
                  label="分类代码"
                  value={editCategory.category_code || ''}
                  onChange={(e) => setEditCategory({ ...editCategory, category_code: e.target.value })}
                  required
                  variant="outlined"
                  placeholder="请输入分类代码"
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 2,
                      '&:hover fieldset': {
                        borderColor: '#1976d2',
                      }
                    }
                  }}
                />
              </Box>

              {/* 分类描述 */}
              <Box sx={{ mb: 3 }}>
                <TextField
                  fullWidth
                  label="分类描述"
                  multiline
                  rows={4}
                  value={editCategory.category_description || ''}
                  onChange={(e) => setEditCategory({ ...editCategory, category_description: e.target.value })}
                  variant="outlined"
                  placeholder="请输入分类的详细描述"
                  sx={{
                    width: '100%',
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 2,
                      '&:hover fieldset': {
                        borderColor: '#1976d2',
                      },
                      '&.Mui-focused fieldset': {
                        borderColor: '#1976d2',
                      }
                    },
                    '& .MuiInputLabel-root.Mui-focused': {
                      color: '#1976d2',
                    }
                  }}
                />
              </Box>


            </Box>
          )}
        </DialogContent>
        <DialogActions sx={{
          p: 3,
          borderTop: '1px solid #e0e0e0',
          gap: 2
        }}>
          <Button
            onClick={handleCloseEditDialog}
            variant="outlined"
            sx={{
              borderRadius: 2,
              px: 3,
              py: 1,
              borderColor: '#e0e0e0',
              color: '#666',
              '&:hover': {
                borderColor: '#1976d2',
                backgroundColor: '#f5f5f5'
              }
            }}
          >
            取消
          </Button>
          <Button
            onClick={handleEditCategory}
            variant="contained"
            disabled={!editCategory?.category_name?.trim()}
            sx={{
              borderRadius: 2,
              px: 4,
              py: 1,
              background: 'linear-gradient(45deg, #ff9800 30%, #ffb74d 90%)',
              boxShadow: '0 3px 5px 2px rgba(255, 152, 0, .3)',
              '&:hover': {
                background: 'linear-gradient(45deg, #f57c00 30%, #ff9800 90%)',
              },
              '&:disabled': {
                background: '#e0e0e0',
                color: '#999'
              }
            }}
          >
            更新分类
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}

export default ChannelManagement;
