import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import {
  Box,
  Container,
  Typography,
  Card,
  CardContent,
  Paper,
  Button,
  IconButton,
  Badge,
  Tabs,
  Tab,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Drawer,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Checkbox,
  Toolbar,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  CircularProgress,
  Collapse,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemButton,
  ListItemSecondaryAction,
  Divider,
  InputAdornment,
  Stack,
  Switch,
  FormControlLabel,
  LinearProgress,
  Avatar,
  AvatarGroup,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  FormGroup,
  FormHelperText,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  SpeedDial,
  SpeedDialAction,
  SpeedDialIcon,
  Fab,
} from '@mui/material';
import {
  Timeline,
  TimelineItem,
  TimelineSeparator,
  TimelineConnector,
  TimelineContent,
  TimelineDot,
  TimelineOppositeContent,
} from '@mui/lab';
import {
  Dashboard,
  History,
  BatchPrediction,
  Warning,
  Error,
  Info,
  CheckCircle,
  Cancel,
  Refresh,
  Search,
  FilterList,
  Download,
  Upload,
  Delete,
  Visibility,
  Close,
  PlayArrow,
  Pause,
  Stop,
  ChevronRight,
  TrendingUp,
  TrendingDown,
  AccessTime,
  Assignment,
  PendingActions,
  Loop,
  Done,
  Block,
  MoneyOff,
  LocalShipping,
  Paid,
  Payment,
  Speed,
  Timeline as TimelineIcon,
  NotificationsActive,
  ErrorOutline,
  CheckCircleOutline,
  WarningAmber,
  NavigateNext,
  NavigateBefore,
  FastForward,
  FastRewind,
  MoreVert,
  ArrowUpward,
  ArrowDownward,
  SwapVert,
  SyncAlt,
  AutorenewOutlined,
} from '@mui/icons-material';
import {
  PieChart, Pie, Cell, ResponsiveContainer, Tooltip as RechartsTooltip, Legend,
  LineChart, Line, XAxis, YAxis, CartesianGrid, 
  BarChart, Bar, AreaChart, Area,
  RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar,
  Sankey
} from 'recharts';
import { format, formatDistanceToNow, parseISO, subDays, startOfDay, endOfDay } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { useSnackbar } from 'notistack';
import statusService from '../../services/statusService';

// Custom hook for real-time updates
const useRealtimeUpdates = (callback, enabled = true) => {
  useEffect(() => {
    if (!enabled) return;
    
    const unsubscribe = statusService.subscribeToStatusUpdates(callback, 5000);
    return unsubscribe;
  }, [callback, enabled]);
};

// Tab panel component
function TabPanel({ children, value, index, ...other }) {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`status-tabpanel-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

// Status icon component
const StatusIcon = ({ status, size = 'medium' }) => {
  const iconMap = {
    pending: <PendingActions />,
    processing: <Loop />,
    completed: <CheckCircle />,
    cancelled: <Cancel />,
    failed: <Error />,
    refunded: <MoneyOff />,
    shipped: <LocalShipping />,
    delivered: <Done />,
    paid: <Paid />,
    unpaid: <Payment />
  };
  
  const sizeMap = {
    small: 20,
    medium: 24,
    large: 32
  };
  
  const Icon = iconMap[status] || <Info />;
  return React.cloneElement(Icon, { sx: { fontSize: sizeMap[size] } });
};

const StatusMonitor = () => {
  const { enqueueSnackbar } = useSnackbar();
  
  // State management
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState(0);
  const [statusLogs, setStatusLogs] = useState([]);
  const [statistics, setStatistics] = useState(null);
  const [transitions, setTransitions] = useState(null);
  const [selectedOrders, setSelectedOrders] = useState([]);
  const [realtimeEnabled, setRealtimeEnabled] = useState(true);
  const [rightDrawerOpen, setRightDrawerOpen] = useState(true);
  
  // Filter states
  const [logFilters, setLogFilters] = useState({
    orderId: '',
    fromStatus: '',
    toStatus: '',
    changedBy: '',
    startDate: null,
    endDate: null
  });
  
  // Pagination states
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalLogs, setTotalLogs] = useState(0);
  
  // Dialog states
  const [detailDialog, setDetailDialog] = useState(false);
  const [selectedLog, setSelectedLog] = useState(null);
  const [batchDialog, setBatchDialog] = useState(false);
  
  // Batch operation states
  const [batchFormData, setBatchFormData] = useState({
    toStatus: '',
    changeReason: '',
    scheduleTime: null
  });
  
  // Chart data
  const [chartData, setChartData] = useState(null);
  
  // Alerts and notifications
  const [alerts, setAlerts] = useState([]);
  const [anomalies, setAnomalies] = useState([]);

  // Load initial data
  useEffect(() => {
    loadStatusLogs();
    loadStatistics();
    loadTransitions();
    loadChartData();
    detectAnomalies();
  }, []);

  // Real-time updates
  useRealtimeUpdates((newLogs) => {
    if (realtimeEnabled && activeTab === 1) { // Only update when on change log tab
      setStatusLogs(prev => {
        const updated = [...newLogs, ...prev];
        return updated.slice(0, 100); // Keep only latest 100
      });
      
      // Check for alerts
      checkForAlerts(newLogs);
    }
  }, realtimeEnabled);

  // Data loading functions
  const loadStatusLogs = async () => {
    setLoading(true);
    try {
      const response = await statusService.getStatusLogs({
        ...logFilters,
        page: page + 1,
        limit: rowsPerPage
      });
      setStatusLogs(response.items);
      setTotalLogs(response.total);
    } catch (error) {
      enqueueSnackbar('加载状态日志失败', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const loadStatistics = async () => {
    try {
      const stats = await statusService.getStatusStatistics(
        subDays(new Date(), 30),
        new Date()
      );
      setStatistics(stats);
    } catch (error) {

    }
  };

  const loadTransitions = async () => {
    try {
      const response = await statusService.getStatusTransitions();
      setTransitions(response);
    } catch (error) {

    }
  };

  const loadChartData = async () => {
    // Use mock data for now
    const mockData = statusService.generateMockChartData();
    setChartData(mockData);
  };

  const detectAnomalies = () => {
    // Simulate anomaly detection
    const mockAnomalies = [
      { id: 1, type: 'long_processing', message: '订单 #12345 处理时间超过预期', severity: 'warning' },
      { id: 2, type: 'unusual_pattern', message: '检测到异常取消率上升', severity: 'error' },
      { id: 3, type: 'stuck_status', message: '5个订单停留在处理中超过24小时', severity: 'warning' }
    ];
    setAnomalies(mockAnomalies);
  };

  const checkForAlerts = (logs) => {
    // Check for patterns that need alerts
    const failedCount = logs.filter(l => l.to_status === 'failed').length;
    if (failedCount > 3) {
      const alert = {
        id: Date.now(),
        type: 'high_failure_rate',
        message: `检测到高失败率: ${failedCount}个订单失败`,
        severity: 'error',
        timestamp: new Date()
      };
      setAlerts(prev => [alert, ...prev].slice(0, 10));
      enqueueSnackbar(alert.message, { variant: 'error' });
    }
  };

  // Action handlers
  const handleBatchUpdate = async () => {
    if (selectedOrders.length === 0) {
      enqueueSnackbar('请选择要更新的订单', { variant: 'warning' });
      return;
    }
    
    try {
      const result = await statusService.batchUpdateStatus(
        selectedOrders,
        batchFormData.toStatus,
        batchFormData.changeReason
      );
      enqueueSnackbar(
        `成功更新 ${result.success_count} 个订单`,
        { variant: 'success' }
      );
      setBatchDialog(false);
      setSelectedOrders([]);
      loadStatusLogs();
      loadStatistics();
    } catch (error) {
      enqueueSnackbar('批量更新失败', { variant: 'error' });
    }
  };

  const handleValidateTransition = async (orderId, toStatus) => {
    try {
      const result = await statusService.validateStatusTransition(orderId, toStatus);
      if (result.valid) {
        enqueueSnackbar('状态转换有效', { variant: 'success' });
        return true;
      }
    } catch (error) {
      enqueueSnackbar(error.response?.data?.detail || '验证失败', { variant: 'error' });
      return false;
    }
  };

  const handleExportLogs = () => {
    // Implement export functionality
    enqueueSnackbar('导出功能开发中', { variant: 'info' });
  };

  const handleOrderSelect = (orderId) => {
    setSelectedOrders(prev => {
      if (prev.includes(orderId)) {
        return prev.filter(id => id !== orderId);
      }
      return [...prev, orderId];
    });
  };

  const handleSelectAll = (event) => {
    if (event.target.checked) {
      const allIds = statusLogs.map(log => log.order_id);
      setSelectedOrders(allIds);
    } else {
      setSelectedOrders([]);
    }
  };

  // Render statistics cards
  const renderStatisticsCards = () => (
    <Box sx={{ display: 'flex', gap: 3, mb: 3, flexWrap: 'wrap' }}>
      <Card sx={{ flex: '1 1 250px', minWidth: 250 }}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Box>
              <Typography color="text.secondary" gutterBottom>
                待处理订单
              </Typography>
              <Typography variant="h4">
                {statistics?.status_counts?.pending || 0}
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                <TrendingUp sx={{ color: 'warning.main', mr: 0.5 }} />
                <Typography variant="body2" color="warning.main">
                  +12% 较昨日
                </Typography>
              </Box>
            </Box>
            <Avatar sx={{ bgcolor: 'warning.light' }}>
              <PendingActions />
            </Avatar>
          </Box>
        </CardContent>
      </Card>

      <Card sx={{ flex: '1 1 250px', minWidth: 250 }}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Box>
              <Typography color="text.secondary" gutterBottom>
                处理中订单
              </Typography>
              <Typography variant="h4">
                {statistics?.status_counts?.processing || 0}
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                <Loop sx={{ color: 'info.main', mr: 0.5 }} />
                <Typography variant="body2" color="info.main">
                  平均处理5.2小时
                </Typography>
              </Box>
            </Box>
            <Avatar sx={{ bgcolor: 'info.light' }}>
              <Loop />
            </Avatar>
          </Box>
        </CardContent>
      </Card>

      <Card sx={{ flex: '1 1 250px', minWidth: 250 }}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Box>
              <Typography color="text.secondary" gutterBottom>
                已完成订单
              </Typography>
              <Typography variant="h4">
                {statistics?.status_counts?.completed || 0}
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                <TrendingUp sx={{ color: 'success.main', mr: 0.5 }} />
                <Typography variant="body2" color="success.main">
                  +23% 本周
                </Typography>
              </Box>
            </Box>
            <Avatar sx={{ bgcolor: 'success.light' }}>
              <CheckCircle />
            </Avatar>
          </Box>
        </CardContent>
      </Card>

      <Card sx={{ flex: '1 1 250px', minWidth: 250 }}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Box>
              <Typography color="text.secondary" gutterBottom>
                异常订单
              </Typography>
              <Typography variant="h4">
                {anomalies.length}
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                <Warning sx={{ color: 'error.main', mr: 0.5 }} />
                <Typography variant="body2" color="error.main">
                  需要关注
                </Typography>
              </Box>
            </Box>
            <Avatar sx={{ bgcolor: 'error.light' }}>
              <ErrorOutline />
            </Avatar>
          </Box>
        </CardContent>
      </Card>
    </Box>
  );

  // Render status dashboard tab
  const renderDashboardTab = () => (
    <Box>
      {/* 图表区域 */}
      <Box sx={{ display: 'flex', gap: 3, mb: 3, flexWrap: 'wrap' }}>
        {/* Status Distribution Chart */}
        <Card sx={{ flex: '1 1 400px', minWidth: 400 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              状态分布
            </Typography>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={chartData?.statusDistribution || []}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {chartData?.statusDistribution?.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <RechartsTooltip />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Trend Chart */}
        <Card sx={{ flex: '1 1 400px', minWidth: 400 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              流转趋势
            </Typography>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={chartData?.trendData || []}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <RechartsTooltip />
                <Legend />
                <Line type="monotone" dataKey="pending" stroke="#FFA726" name="待处理" />
                <Line type="monotone" dataKey="processing" stroke="#42A5F5" name="处理中" />
                <Line type="monotone" dataKey="completed" stroke="#66BB6A" name="已完成" />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </Box>

      {/* Anomaly Alerts */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6">异常告警</Typography>
            <Badge badgeContent={anomalies.length} color="error">
              <NotificationsActive />
            </Badge>
          </Box>
          <List>
            {anomalies.map((anomaly) => (
              <ListItem key={anomaly.id}>
                <ListItemIcon>
                  {anomaly.severity === 'error' ? (
                    <Error color="error" />
                  ) : (
                    <Warning color="warning" />
                  )}
                </ListItemIcon>
                <ListItemText
                  primary={anomaly.message}
                  secondary={`类型: ${anomaly.type}`}
                />
                <ListItemSecondaryAction>
                  <IconButton edge="end" size="small">
                    <Close />
                  </IconButton>
                </ListItemSecondaryAction>
              </ListItem>
            ))}
          </List>
          {anomalies.length === 0 && (
            <Alert severity="success">暂无异常告警</Alert>
          )}
        </CardContent>
      </Card>

      {/* Average Transition Time */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            平均转换时间
          </Typography>
          <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
            {Object.entries(chartData?.avgTransitionTime || {}).map(([key, value]) => (
              <Paper key={key} variant="outlined" sx={{ p: 2, flex: '1 1 200px', minWidth: 200 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <SyncAlt sx={{ mr: 1, color: 'primary.main' }} />
                  <Typography variant="body2" color="text.secondary">
                    {key.replace(/_/g, ' → ')}
                  </Typography>
                </Box>
                <Typography variant="h5">{value}</Typography>
              </Paper>
            ))}
          </Box>
        </CardContent>
      </Card>
    </Box>
  );

  // Render change log tab
  const renderChangeLogTab = () => (
    <Box>
      {/* Filters */}
      <Paper sx={{ p: 2, mb: 2 }}>
        <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', alignItems: 'center' }}>
          <TextField
            size="small"
            label="订单ID"
            value={logFilters.orderId}
            onChange={(e) => setLogFilters(prev => ({ ...prev, orderId: e.target.value }))}
            sx={{ minWidth: 150 }}
          />
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>原状态</InputLabel>
            <Select
              value={logFilters.fromStatus}
              onChange={(e) => setLogFilters(prev => ({ ...prev, fromStatus: e.target.value }))}
              label="原状态"
            >
              <MenuItem value="">全部</MenuItem>
              <MenuItem value="pending">待处理</MenuItem>
              <MenuItem value="processing">处理中</MenuItem>
              <MenuItem value="completed">已完成</MenuItem>
              <MenuItem value="cancelled">已取消</MenuItem>
            </Select>
          </FormControl>
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>新状态</InputLabel>
            <Select
              value={logFilters.toStatus}
              onChange={(e) => setLogFilters(prev => ({ ...prev, toStatus: e.target.value }))}
              label="新状态"
            >
              <MenuItem value="">全部</MenuItem>
              <MenuItem value="pending">待处理</MenuItem>
              <MenuItem value="processing">处理中</MenuItem>
              <MenuItem value="completed">已完成</MenuItem>
              <MenuItem value="cancelled">已取消</MenuItem>
            </Select>
          </FormControl>
          <Button
            variant="contained"
            startIcon={<Search />}
            onClick={loadStatusLogs}
          >
            搜索
          </Button>
          <Button
            variant="outlined"
            startIcon={<Refresh />}
            onClick={() => {
              setLogFilters({
                orderId: '',
                fromStatus: '',
                toStatus: '',
                changedBy: '',
                startDate: null,
                endDate: null
              });
              loadStatusLogs();
            }}
          >
            重置
          </Button>
          <FormControlLabel
            control={
              <Switch
                checked={realtimeEnabled}
                onChange={(e) => setRealtimeEnabled(e.target.checked)}
              />
            }
            label="实时更新"
          />
        </Box>
      </Paper>

      {/* Log Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell padding="checkbox">
                <Checkbox
                  indeterminate={selectedOrders.length > 0 && selectedOrders.length < statusLogs.length}
                  checked={statusLogs.length > 0 && selectedOrders.length === statusLogs.length}
                  onChange={handleSelectAll}
                />
              </TableCell>
              <TableCell>订单号</TableCell>
              <TableCell>原状态</TableCell>
              <TableCell>新状态</TableCell>
              <TableCell>变更原因</TableCell>
              <TableCell>操作人</TableCell>
              <TableCell>变更时间</TableCell>
              <TableCell align="center">操作</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {statusLogs.map((log) => (
              <TableRow key={log.id} hover>
                <TableCell padding="checkbox">
                  <Checkbox
                    checked={selectedOrders.includes(log.order_id)}
                    onChange={() => handleOrderSelect(log.order_id)}
                  />
                </TableCell>
                <TableCell>
                  <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
                    {log.order_no || log.order_id?.slice(0, 8)}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Chip
                    icon={<StatusIcon status={log.from_status} size="small" />}
                    label={statusService.formatStatus(log.from_status)}
                    size="small"
                    sx={{ bgcolor: statusService.getStatusColor(log.from_status) + '20' }}
                  />
                </TableCell>
                <TableCell>
                  <Chip
                    icon={<StatusIcon status={log.to_status} size="small" />}
                    label={statusService.formatStatus(log.to_status)}
                    size="small"
                    sx={{ bgcolor: statusService.getStatusColor(log.to_status) + '20' }}
                  />
                </TableCell>
                <TableCell>{log.change_reason || '-'}</TableCell>
                <TableCell>{log.changed_by_name || '-'}</TableCell>
                <TableCell>
                  {format(parseISO(log.changed_at), 'MM-dd HH:mm:ss')}
                </TableCell>
                <TableCell align="center">
                  <IconButton
                    size="small"
                    onClick={() => {
                      setSelectedLog(log);
                      setDetailDialog(true);
                    }}
                  >
                    <Visibility />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
        <TablePagination
          rowsPerPageOptions={[5, 10, 25, 50]}
          component="div"
          count={totalLogs}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={(e, newPage) => setPage(newPage)}
          onRowsPerPageChange={(e) => {
            setRowsPerPage(parseInt(e.target.value, 10));
            setPage(0);
          }}
        />
      </TableContainer>

      {statusLogs.length === 0 && !loading && (
        <Box sx={{ textAlign: 'center', py: 8 }}>
          <History sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
          <Typography variant="h6" color="text.secondary">
            暂无状态变更记录
          </Typography>
        </Box>
      )}
    </Box>
  );

  // Render batch operations tab
  const renderBatchOperationsTab = () => (
    <Box>
      {/* Batch Selection */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            批量选择器
          </Typography>
          <Alert severity="info" sx={{ mb: 2 }}>
            已选择 {selectedOrders.length} 个订单
          </Alert>

          <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', alignItems: 'end' }}>
            <FormControl sx={{ minWidth: 200 }}>
              <InputLabel>目标状态</InputLabel>
              <Select
                value={batchFormData.toStatus}
                onChange={(e) => setBatchFormData(prev => ({
                  ...prev,
                  toStatus: e.target.value
                }))}
                label="目标状态"
              >
                <MenuItem value="processing">处理中</MenuItem>
                <MenuItem value="completed">已完成</MenuItem>
                <MenuItem value="cancelled">已取消</MenuItem>
                <MenuItem value="refunded">已退款</MenuItem>
              </Select>
            </FormControl>
            <TextField
              label="变更原因"
              value={batchFormData.changeReason}
              onChange={(e) => setBatchFormData(prev => ({
                ...prev,
                changeReason: e.target.value
              }))}
              sx={{ minWidth: 200, flex: 1 }}
            />
            <Button
              variant="contained"
              size="large"
              startIcon={<BatchPrediction />}
              onClick={() => setBatchDialog(true)}
              disabled={selectedOrders.length === 0}
            >
              执行批量更新
            </Button>
          </Box>
        </CardContent>
      </Card>

      {/* Execution History */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            执行历史
          </Typography>
          <Timeline position="alternate">
            <TimelineItem>
              <TimelineOppositeContent sx={{ m: 'auto 0' }}>
                <Typography variant="body2" color="text.secondary">
                  2024-01-07 14:30
                </Typography>
              </TimelineOppositeContent>
              <TimelineSeparator>
                <TimelineConnector />
                <TimelineDot color="success">
                  <CheckCircle />
                </TimelineDot>
                <TimelineConnector />
              </TimelineSeparator>
              <TimelineContent sx={{ py: '12px', px: 2 }}>
                <Typography variant="h6" component="span">
                  批量完成
                </Typography>
                <Typography>成功更新 25 个订单状态为已完成</Typography>
              </TimelineContent>
            </TimelineItem>

            <TimelineItem>
              <TimelineOppositeContent sx={{ m: 'auto 0' }}>
                <Typography variant="body2" color="text.secondary">
                  2024-01-07 10:15
                </Typography>
              </TimelineOppositeContent>
              <TimelineSeparator>
                <TimelineConnector />
                <TimelineDot color="warning">
                  <Warning />
                </TimelineDot>
                <TimelineConnector />
              </TimelineSeparator>
              <TimelineContent sx={{ py: '12px', px: 2 }}>
                <Typography variant="h6" component="span">
                  部分成功
                </Typography>
                <Typography>10个订单更新成功，3个失败</Typography>
              </TimelineContent>
            </TimelineItem>

            <TimelineItem>
              <TimelineOppositeContent sx={{ m: 'auto 0' }}>
                <Typography variant="body2" color="text.secondary">
                  2024-01-06 16:45
                </Typography>
              </TimelineOppositeContent>
              <TimelineSeparator>
                <TimelineConnector />
                <TimelineDot color="error">
                  <Error />
                </TimelineDot>
                <TimelineConnector />
              </TimelineSeparator>
              <TimelineContent sx={{ py: '12px', px: 2 }}>
                <Typography variant="h6" component="span">
                  执行失败
                </Typography>
                <Typography>权限不足，操作被拒绝</Typography>
              </TimelineContent>
            </TimelineItem>
          </Timeline>
        </CardContent>
      </Card>

      {/* Scheduled Tasks */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            计划任务
          </Typography>
          <Alert severity="warning" sx={{ mb: 2 }}>
            您有 2 个待执行的计划任务
          </Alert>
          <List>
            <ListItem>
              <ListItemIcon>
                <AccessTime />
              </ListItemIcon>
              <ListItemText
                primary="批量取消超时订单"
                secondary="计划于 2024-01-08 00:00 执行，影响 15 个订单"
              />
              <ListItemSecondaryAction>
                <IconButton edge="end" color="error">
                  <Delete />
                </IconButton>
              </ListItemSecondaryAction>
            </ListItem>
            <ListItem>
              <ListItemIcon>
                <AccessTime />
              </ListItemIcon>
              <ListItemText
                primary="自动完成已发货订单"
                secondary="计划于 2024-01-08 09:00 执行，影响 32 个订单"
              />
              <ListItemSecondaryAction>
                <IconButton edge="end" color="error">
                  <Delete />
                </IconButton>
              </ListItemSecondaryAction>
            </ListItem>
          </List>
        </CardContent>
      </Card>
    </Box>
  );



  // Render quick navigation sidebar
  const renderQuickNavigation = () => (
    <Paper sx={{ p: 2, height: '100%' }}>
      <Typography variant="h6" gutterBottom>
        快速跳转
      </Typography>
      
      <List dense>
        <ListItemButton onClick={() => setActiveTab(0)}>
          <ListItemIcon>
            <Dashboard />
          </ListItemIcon>
          <ListItemText primary="状态大盘" />
        </ListItemButton>
        <ListItemButton onClick={() => setActiveTab(1)}>
          <ListItemIcon>
            <History />
          </ListItemIcon>
          <ListItemText primary="变更日志" />
        </ListItemButton>
        <ListItemButton onClick={() => setActiveTab(2)}>
          <ListItemIcon>
            <BatchPrediction />
          </ListItemIcon>
          <ListItemText primary="批量操作" />
        </ListItemButton>
      </List>
      
      <Divider sx={{ my: 2 }} />
      
      <Typography variant="subtitle2" gutterBottom>
        异常订单
      </Typography>
      
      <List dense>
        {anomalies.slice(0, 5).map((anomaly) => (
          <ListItem key={anomaly.id}>
            <ListItemIcon>
              <WarningAmber color="warning" />
            </ListItemIcon>
            <ListItemText
              primary={`订单 #${anomaly.id}`}
              secondary={anomaly.message}
              primaryTypographyProps={{ variant: 'body2' }}
              secondaryTypographyProps={{ variant: 'caption' }}
            />
          </ListItem>
        ))}
      </List>
      
      <Divider sx={{ my: 2 }} />
      
      <Typography variant="subtitle2" gutterBottom>
        快速操作
      </Typography>
      
      <Stack spacing={1}>
        <Button
          fullWidth
          size="small"
          startIcon={<Download />}
          onClick={handleExportLogs}
        >
          导出日志
        </Button>

        <Button
          fullWidth
          size="small"
          startIcon={<Refresh />}
          onClick={() => {
            loadStatusLogs();
            loadStatistics();
          }}
        >
          刷新数据
        </Button>
      </Stack>
    </Paper>
  );

  return (
    <Container maxWidth={false} sx={{ py: 3 }}>
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" gutterBottom>
          状态监控中心
        </Typography>
        <Typography variant="body1" color="text.secondary">
          实时监控和管理订单状态流转
        </Typography>
      </Box>

      {/* Statistics Cards */}
      {renderStatisticsCards()}

      <Box sx={{ display: 'flex', gap: 3 }}>
        {/* Main Content */}
        <Box sx={{ flex: rightDrawerOpen ? '1 1 70%' : '1 1 100%' }}>
          <Paper sx={{ mb: 2 }}>
            <Tabs
              value={activeTab}
              onChange={(e, newValue) => setActiveTab(newValue)}
              variant="scrollable"
              scrollButtons="auto"
            >
              <Tab icon={<Dashboard />} label="状态大盘" />
              <Tab
                icon={
                  <Badge badgeContent={realtimeEnabled ? "LIVE" : 0} color="error">
                    <History />
                  </Badge>
                }
                label="变更日志"
              />
              <Tab
                icon={
                  <Badge badgeContent={selectedOrders.length} color="primary">
                    <BatchPrediction />
                  </Badge>
                }
                label="批量操作"
              />
            </Tabs>
          </Paper>

          <Paper>
            <TabPanel value={activeTab} index={0}>
              {renderDashboardTab()}
            </TabPanel>
            <TabPanel value={activeTab} index={1}>
              {renderChangeLogTab()}
            </TabPanel>
            <TabPanel value={activeTab} index={2}>
              {renderBatchOperationsTab()}
            </TabPanel>
          </Paper>
        </Box>

        {/* Right Sidebar */}
        {rightDrawerOpen && (
          <Box sx={{ flex: '0 0 300px', minWidth: 300 }}>
            {renderQuickNavigation()}
          </Box>
        )}
      </Box>

      {/* Toggle Sidebar FAB */}
      <Fab
        color="primary"
        size="small"
        sx={{
          position: 'fixed',
          bottom: 16,
          right: 16
        }}
        onClick={() => setRightDrawerOpen(!rightDrawerOpen)}
      >
        {rightDrawerOpen ? <ChevronRight /> : <NavigateBefore />}
      </Fab>

      {/* Detail Dialog */}
      <Dialog
        open={detailDialog}
        onClose={() => setDetailDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>状态变更详情</DialogTitle>
        <DialogContent>
          {selectedLog && (
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 1 }}>
              <Box sx={{ display: 'flex', gap: 3, flexWrap: 'wrap' }}>
                <Box sx={{ flex: '1 1 200px' }}>
                  <Typography variant="body2" color="text.secondary">订单ID</Typography>
                  <Typography variant="body1">{selectedLog.order_id}</Typography>
                </Box>
                <Box sx={{ flex: '1 1 200px' }}>
                  <Typography variant="body2" color="text.secondary">订单号</Typography>
                  <Typography variant="body1">{selectedLog.order_no}</Typography>
                </Box>
              </Box>
              <Box sx={{ display: 'flex', gap: 3, flexWrap: 'wrap' }}>
                <Box sx={{ flex: '1 1 200px' }}>
                  <Typography variant="body2" color="text.secondary">原状态</Typography>
                  <Chip
                    icon={<StatusIcon status={selectedLog.from_status} size="small" />}
                    label={statusService.formatStatus(selectedLog.from_status)}
                  />
                </Box>
                <Box sx={{ flex: '1 1 200px' }}>
                  <Typography variant="body2" color="text.secondary">新状态</Typography>
                  <Chip
                    icon={<StatusIcon status={selectedLog.to_status} size="small" />}
                    label={statusService.formatStatus(selectedLog.to_status)}
                  />
                </Box>
              </Box>
              <Box>
                <Typography variant="body2" color="text.secondary">变更原因</Typography>
                <Typography variant="body1">{selectedLog.change_reason || '无'}</Typography>
              </Box>
              <Box>
                <Typography variant="body2" color="text.secondary">元数据</Typography>
                <Paper variant="outlined" sx={{ p: 2, bgcolor: 'grey.50' }}>
                  <pre style={{ margin: 0 }}>
                    {JSON.stringify(selectedLog.metadata || {}, null, 2)}
                  </pre>
                </Paper>
              </Box>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDetailDialog(false)}>关闭</Button>
        </DialogActions>
      </Dialog>

      {/* Batch Update Confirmation Dialog */}
      <Dialog
        open={batchDialog}
        onClose={() => setBatchDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>确认批量更新</DialogTitle>
        <DialogContent>
          <Alert severity="warning" sx={{ mb: 2 }}>
            您即将更新 {selectedOrders.length} 个订单的状态
          </Alert>
          <Typography>
            目标状态：{statusService.formatStatus(batchFormData.toStatus)}
          </Typography>
          <Typography>
            变更原因：{batchFormData.changeReason}
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setBatchDialog(false)}>取消</Button>
          <Button onClick={handleBatchUpdate} variant="contained" color="warning">
            确认执行
          </Button>
        </DialogActions>
      </Dialog>


    </Container>
  );
};

export default StatusMonitor;