import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Card,
  CardContent,
  Avatar,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Button,
  IconButton,
  TextField,
  InputAdornment,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Rating,
  CircularProgress,
  Alert,
  Tabs,
  Tab,
  Divider,
  Grid,
} from '@mui/material';
import {
  Search,
  FilterList,
  Visibility,
  CheckCircle,
  Cancel,
  Schedule,
  Article,
  Star,
  TrendingUp,
  Warning,
  Edit,
  Delete,
  Assignment,
  Create,
  Publish,
  Business,
  AttachMoney,
  AccessTime,
  Person,
  Description,
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';

function ContentReview() {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [directTasks, setDirectTasks] = useState([]);
  const [creativeTasks, setCreativeTasks] = useState([]);
  const [filteredTasks, setFilteredTasks] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [priorityFilter, setPriorityFilter] = useState('all');
  const [selectedTask, setSelectedTask] = useState(null);
  const [taskDialogOpen, setTaskDialogOpen] = useState(false);
  const [acceptForm, setAcceptForm] = useState({
    message: '',
    estimatedDays: 1,
  });
  const [tabValue, setTabValue] = useState(0);

  // 模拟数据加载
  useEffect(() => {
    const loadTasks = async () => {
      // 模拟API调用
      setTimeout(() => {
        const mockDirectTasks = [
          {
            id: 1,
            title: 'AI技术在教育领域的革新应用',
            client: '科技教育公司',
            clientEmail: '<EMAIL>',
            type: 'direct',
            status: 'pending',
            submitTime: '2024-01-20 14:30',
            deadline: '2024-01-25 18:00',
            wordCount: 2500,
            priority: 'high',
            budget: 1500,
            content: '人工智能技术正在深刻改变教育行业的面貌...',
            tags: ['AI', '教育', '技术'],
            channels: ['微信公众号', '知乎', '今日头条'],
            requirements: '需要专业的技术分析，包含实际案例',
          },
          {
            id: 2,
            title: '2024年SEO优化最佳实践指南',
            client: '数字营销公司',
            clientEmail: '<EMAIL>',
            type: 'direct',
            status: 'accepted',
            submitTime: '2024-01-20 11:20',
            acceptTime: '2024-01-20 15:45',
            deadline: '2024-01-28 12:00',
            wordCount: 3200,
            priority: 'medium',
            budget: 2000,
            content: '搜索引擎优化是数字营销的重要组成部分...',
            tags: ['SEO', '优化', '营销'],
            channels: ['官方博客', 'LinkedIn'],
            requirements: '需要包含最新的SEO策略和工具推荐',
          },
        ];

        const mockCreativeTasks = [
          {
            id: 3,
            title: '企业数字化转型案例分析',
            client: '咨询公司',
            clientEmail: '<EMAIL>',
            type: 'creative',
            status: 'pending',
            submitTime: '2024-01-19 16:45',
            deadline: '2024-01-30 17:00',
            wordCount: 2000,
            priority: 'medium',
            budget: 1800,
            requirements: '需要分析3-5个成功的数字化转型案例，包含具体的实施步骤和效果评估',
            tags: ['数字化', '转型', '案例分析'],
            targetAudience: '企业管理者',
            contentStyle: '专业分析',
          },
          {
            id: 4,
            title: '区块链技术发展趋势报告',
            client: '金融科技公司',
            clientEmail: '<EMAIL>',
            type: 'creative',
            status: 'in_progress',
            submitTime: '2024-01-21 10:15',
            acceptTime: '2024-01-21 14:30',
            deadline: '2024-02-05 16:00',
            wordCount: 3500,
            priority: 'high',
            budget: 2500,
            requirements: '深度分析区块链技术在金融、供应链、医疗等领域的应用前景',
            tags: ['区块链', '金融科技', '趋势分析'],
            targetAudience: '投资者和技术决策者',
            contentStyle: '深度报告',
          },
        ];

        setDirectTasks(mockDirectTasks);
        setCreativeTasks(mockCreativeTasks);
        setFilteredTasks(tabValue === 0 ? mockDirectTasks : mockCreativeTasks);
        setLoading(false);
      }, 1000);
    };

    loadTasks();
  }, []);

  // 切换标签页时更新数据
  useEffect(() => {
    const currentTasks = tabValue === 0 ? directTasks : creativeTasks;
    setFilteredTasks(currentTasks);
  }, [tabValue, directTasks, creativeTasks]);

  // 过滤任务
  useEffect(() => {
    const currentTasks = tabValue === 0 ? directTasks : creativeTasks;
    let filtered = currentTasks;

    if (searchTerm) {
      filtered = filtered.filter(task =>
        task.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        task.client.toLowerCase().includes(searchTerm.toLowerCase()) ||
        task.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    if (statusFilter !== 'all') {
      filtered = filtered.filter(task => task.status === statusFilter);
    }

    if (priorityFilter !== 'all') {
      filtered = filtered.filter(task => task.priority === priorityFilter);
    }

    setFilteredTasks(filtered);
  }, [directTasks, creativeTasks, tabValue, searchTerm, statusFilter, priorityFilter]);

  const getStatusColor = (status) => {
    const statusMap = {
      pending: 'warning',
      accepted: 'info',
      in_progress: 'primary',
      completed: 'success',
      cancelled: 'error',
    };
    return statusMap[status] || 'default';
  };

  const getStatusLabel = (status) => {
    const statusMap = {
      pending: '待接单',
      accepted: '已接单',
      in_progress: '进行中',
      completed: '已完成',
      cancelled: '已取消',
    };
    return statusMap[status] || status;
  };

  const getPriorityColor = (priority) => {
    const priorityMap = {
      high: 'error',
      medium: 'warning',
      low: 'info',
    };
    return priorityMap[priority] || 'default';
  };

  const getPriorityLabel = (priority) => {
    const priorityMap = {
      high: '高优先级',
      medium: '中优先级',
      low: '低优先级',
    };
    return priorityMap[priority] || priority;
  };

  const handleViewTask = (task) => {
    setSelectedTask(task);
    setTaskDialogOpen(true);
    setAcceptForm({
      message: '',
      estimatedDays: 1,
    });
  };

  const handleAcceptTask = async () => {
    try {
      // 这里调用API接受任务
      console.log('接受任务:', selectedTask.id, acceptForm);

      // 更新本地状态
      const updateTasks = (tasks) =>
        tasks.map(task =>
          task.id === selectedTask.id
            ? { ...task, status: 'accepted', acceptTime: new Date().toISOString() }
            : task
        );

      if (tabValue === 0) {
        setDirectTasks(updateTasks);
      } else {
        setCreativeTasks(updateTasks);
      }

      setTaskDialogOpen(false);
    } catch (error) {
      console.error('接受任务失败:', error);
    }
  };

  const getAllTasks = () => [...directTasks, ...creativeTasks];

  const stats = [
    {
      title: '待接单',
      value: getAllTasks().filter(t => t.status === 'pending').length,
      color: 'warning',
      icon: <Schedule />,
    },
    {
      title: '进行中',
      value: getAllTasks().filter(t => t.status === 'in_progress').length,
      color: 'primary',
      icon: <Article />,
    },
    {
      title: '已完成',
      value: getAllTasks().filter(t => t.status === 'completed').length,
      color: 'success',
      icon: <CheckCircle />,
    },
    {
      title: '总收益',
      value: `¥${getAllTasks().filter(t => t.status === 'completed').reduce((sum, t) => sum + (t.budget || 0), 0)}`,
      color: 'info',
      icon: <AttachMoney />,
    },
  ];

  if (loading) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 400 }}>
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  return (
    <Box sx={{ width: '100%', p: 3, backgroundColor: '#f5f5f5', minHeight: '100vh' }}>
      {/* 页面标题和操作按钮区域 */}
      <Box sx={{
        backgroundColor: '#fff',
        borderRadius: 3,
        border: '1px solid #e0e0e0',
        p: 4,
        mb: 3,
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
      }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Assignment sx={{ fontSize: 32, color: '#1976d2' }} />
            <Box>
              <Typography variant="h4" sx={{ fontWeight: 700, color: '#1a1a1a', mb: 0.5 }}>
                接单中心
              </Typography>
              <Typography variant="body1" sx={{ color: '#666' }}>
                管理企业客户直发稿件和创作需求任务
              </Typography>
            </Box>
          </Box>
        </Box>
      </Box>



            {/* Stats Cards */}
            <Grid container spacing={3} sx={{ mb: 4 }}>
        {stats.map((stat, index) => (
          <Grid item xs={12} sm={6} md={3} key={index}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Avatar
                    sx={{
                      bgcolor: `${stat.color}.light`,
                      color: `${stat.color}.main`,
                      width: 48,
                      height: 48,
                      mr: 2,
                    }}
                  >
                    {stat.icon}
                  </Avatar>
                  <Box>
                    <Typography variant="h4" sx={{ fontWeight: 600 }}>
                      {stat.value}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {stat.title}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* 标签页 */}
      <Card sx={{ mb: 3 }}>
        <Tabs
          value={tabValue}
          onChange={(e, newValue) => setTabValue(newValue)}
          sx={{ borderBottom: 1, borderColor: 'divider' }}
        >
          <Tab
            icon={<Publish />}
            label={`直发稿件 (${directTasks.length})`}
            iconPosition="start"
          />
          <Tab
            icon={<Create />}
            label={`创作需求 (${creativeTasks.length})`}
            iconPosition="start"
          />
        </Tabs>
      </Card>

      {/* Filters */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                placeholder="搜索任务..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Search />
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>状态筛选</InputLabel>
                <Select
                  value={statusFilter}
                  label="状态筛选"
                  onChange={(e) => setStatusFilter(e.target.value)}
                >
                  <MenuItem value="all">全部状态</MenuItem>
                  <MenuItem value="pending">待接单</MenuItem>
                  <MenuItem value="accepted">已接单</MenuItem>
                  <MenuItem value="in_progress">进行中</MenuItem>
                  <MenuItem value="completed">已完成</MenuItem>
                  <MenuItem value="cancelled">已取消</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>优先级筛选</InputLabel>
                <Select
                  value={priorityFilter}
                  label="优先级筛选"
                  onChange={(e) => setPriorityFilter(e.target.value)}
                >
                  <MenuItem value="all">全部优先级</MenuItem>
                  <MenuItem value="high">高优先级</MenuItem>
                  <MenuItem value="medium">中优先级</MenuItem>
                  <MenuItem value="low">低优先级</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={2}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<FilterList />}
                onClick={() => {
                  setSearchTerm('');
                  setStatusFilter('all');
                  setPriorityFilter('all');
                }}
              >
                重置
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Task Table */}
      <Card>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant="h6" sx={{ fontWeight: 600 }}>
              {tabValue === 0 ? '直发稿件' : '创作需求'} ({filteredTasks.length})
            </Typography>
          </Box>
          <TableContainer component={Paper} variant="outlined">
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>任务标题</TableCell>
                  <TableCell>客户</TableCell>
                  <TableCell>预算</TableCell>
                  <TableCell>状态</TableCell>
                  <TableCell>优先级</TableCell>
                  <TableCell>截止时间</TableCell>
                  <TableCell>操作</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredTasks.map((task) => (
                  <TableRow key={task.id} hover>
                    <TableCell>
                      <Box>
                        <Typography variant="body2" sx={{ fontWeight: 500, mb: 0.5 }}>
                          {task.title}
                        </Typography>
                        <Box sx={{ display: 'flex', gap: 0.5, mb: 0.5 }}>
                          {task.tags.slice(0, 3).map((tag, index) => (
                            <Chip
                              key={index}
                              label={tag}
                              size="small"
                              variant="outlined"
                              sx={{ fontSize: '0.7rem', height: 20 }}
                            />
                          ))}
                        </Box>
                        <Typography variant="caption" color="text.secondary">
                          {task.wordCount} 字 • {task.submitTime}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box>
                        <Typography variant="body2" sx={{ fontWeight: 500 }}>
                          {task.client}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {task.clientEmail}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <AttachMoney sx={{ fontSize: 16, color: 'success.main', mr: 0.5 }} />
                        <Typography variant="body2" sx={{ fontWeight: 600, color: 'success.main' }}>
                          {task.budget}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={getStatusLabel(task.status)}
                        color={getStatusColor(task.status)}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={getPriorityLabel(task.priority)}
                        color={getPriorityColor(task.priority)}
                        size="small"
                        variant="outlined"
                      />
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <AccessTime sx={{ fontSize: 16, color: 'text.secondary', mr: 0.5 }} />
                        <Typography variant="body2" color="text.secondary">
                          {new Date(task.deadline).toLocaleDateString()}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', gap: 1 }}>
                        <IconButton
                          size="small"
                          onClick={() => handleViewTask(task)}
                          color="primary"
                        >
                          <Visibility fontSize="small" />
                        </IconButton>
                        {task.status === 'pending' && (
                          <Button
                            size="small"
                            variant="contained"
                            onClick={() => handleViewTask(task)}
                            sx={{ minWidth: 60 }}
                          >
                            接单
                          </Button>
                        )}
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* Task Dialog */}
      <Dialog
        open={taskDialogOpen}
        onClose={() => setTaskDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Typography variant="h5" sx={{ fontWeight: 600 }}>
            任务详情
          </Typography>
        </DialogTitle>
        <DialogContent>
          {selectedTask && (
            <Box sx={{ mt: 2 }}>
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                    {selectedTask.title}
                  </Typography>
                  <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
                    <Chip
                      label={selectedTask.type === 'direct' ? '直发稿件' : '创作需求'}
                      size="small"
                      color="primary"
                    />
                    <Chip label={selectedTask.client} size="small" variant="outlined" />
                    <Chip
                      label={getPriorityLabel(selectedTask.priority)}
                      color={getPriorityColor(selectedTask.priority)}
                      size="small"
                    />
                    <Chip
                      label={`¥${selectedTask.budget}`}
                      color="success"
                      size="small"
                    />
                  </Box>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" color="text.secondary" sx={{ mb: 1 }}>
                    任务要求
                  </Typography>
                  <Box sx={{
                    p: 2,
                    bgcolor: 'grey.50',
                    borderRadius: 1,
                    maxHeight: 200,
                    overflow: 'auto'
                  }}>
                    <Typography variant="body2">
                      {selectedTask.requirements || selectedTask.content}
                    </Typography>
                  </Box>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" color="text.secondary" sx={{ mb: 1 }}>
                    任务信息
                  </Typography>
                  <Box sx={{ p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                      <strong>字数要求：</strong>{selectedTask.wordCount}
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                      <strong>预算：</strong>¥{selectedTask.budget}
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                      <strong>截止时间：</strong>{new Date(selectedTask.deadline).toLocaleString()}
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                      <strong>提交时间：</strong>{selectedTask.submitTime}
                    </Typography>
                    <Typography variant="body2">
                      <strong>标签：</strong>{selectedTask.tags.join(', ')}
                    </Typography>
                    {selectedTask.channels && (
                      <Typography variant="body2" sx={{ mt: 1 }}>
                        <strong>发布渠道：</strong>{selectedTask.channels.join(', ')}
                      </Typography>
                    )}
                    {selectedTask.targetAudience && (
                      <Typography variant="body2" sx={{ mt: 1 }}>
                        <strong>目标受众：</strong>{selectedTask.targetAudience}
                      </Typography>
                    )}
                  </Box>
                </Grid>

                {selectedTask.status === 'pending' && (
                  <Grid item xs={12}>
                    <Divider sx={{ my: 2 }} />
                    <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
                      接单操作
                    </Typography>

                    <Box sx={{ mb: 3 }}>
                      <Typography variant="subtitle2" sx={{ mb: 1 }}>
                        预计完成天数
                      </Typography>
                      <TextField
                        type="number"
                        value={acceptForm.estimatedDays}
                        onChange={(e) => setAcceptForm(prev => ({ ...prev, estimatedDays: parseInt(e.target.value) }))}
                        inputProps={{ min: 1, max: 30 }}
                        sx={{ width: 120 }}
                      />
                      <Typography variant="caption" color="text.secondary" sx={{ ml: 1 }}>
                        天
                      </Typography>
                    </Box>

                    <TextField
                      fullWidth
                      multiline
                      rows={4}
                      label="接单留言"
                      value={acceptForm.message}
                      onChange={(e) => setAcceptForm(prev => ({ ...prev, message: e.target.value }))}
                      placeholder="向客户说明您的执行计划和优势..."
                    />
                  </Grid>
                )}
              </Grid>
            </Box>
          )}
        </DialogContent>
        <DialogActions sx={{ p: 3 }}>
          <Button onClick={() => setTaskDialogOpen(false)}>
            取消
          </Button>
          {selectedTask && selectedTask.status === 'pending' && (
            <Button
              onClick={handleAcceptTask}
              variant="contained"
              disabled={!acceptForm.message.trim()}
            >
              确认接单
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </Box>
  );
}

export default ContentReview;
