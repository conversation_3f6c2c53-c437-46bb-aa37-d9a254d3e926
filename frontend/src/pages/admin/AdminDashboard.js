import React, { useState, useEffect, useRef } from 'react';
import mermaid from 'mermaid';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  CardActions,
  Avatar,
  Chip,
  Paper,
  Button,
  IconButton,
  Badge,
  Divider,
  CircularProgress,
  LinearProgress,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemButton,
  Stack,
  Fade,
  Grow,
  useTheme,
  alpha,
  Alert,
  AlertTitle,
  Drawer,
  AppBar,
  Toolbar,
  CssBaseline,
  Collapse,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import {
  Dashboard,
  People,
  ContentPaste,
  AccountBalance,
  Settings,
  Warning,
  CheckCircle,
  Delete,
  MoreVert,
  Store,
  Category,
  Assignment,
  ShoppingCart,
  Payment,
  Analytics,
  Description,
  Business,
  Announcement,
  Help,
  Work,
  CardMembership,
  TrendingUp,
  ArrowForward,
  Person,
  AdminPanelSettings,
  Timeline as TimelineIcon,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart,
  Speed,
  ExpandMore,
  Info,
  ErrorOutline,
  BookOutlined,
  School,
  MenuBook,
  Rule,
  Lightbulb,
  Security,
  VerifiedUser,
  NotificationsActive,
  Task,
  PlayCircleOutline,
  CheckCircleOutline,
  RadioButtonUnchecked,
  ExpandLess,
  ChevronRight,
  Home,
  Folder,
  FolderOpen,
  AccountTree,
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';

const drawerWidth = 320;

function AdminDashboard() {
  const { user } = useAuth();
  const navigate = useNavigate();
  const theme = useTheme();
  const [loading, setLoading] = useState(true);
  const [selectedModule, setSelectedModule] = useState('userApprovalFlow');
  const [expandedMenus, setExpandedMenus] = useState({});
  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialogContent, setDialogContent] = useState({ title: '', description: '', actions: [] });
  const [mermaidLoaded, setMermaidLoaded] = useState(false);
  const mermaidRef = useRef(null);
  const mermaidInstance = useRef(null);

  // 初始化Mermaid
  useEffect(() => {
    const initMermaid = () => {
      if (typeof window !== 'undefined' && !mermaidLoaded) {
        try {
          mermaid.initialize({
            startOnLoad: false,
            theme: 'default',
            securityLevel: 'loose',
            flowchart: {
              useMaxWidth: true,
              htmlLabels: true,
              curve: 'basis'
            }
          });

          mermaidInstance.current = mermaid;
          setMermaidLoaded(true);
        } catch (error) {
          console.error('Failed to initialize Mermaid:', error);
          // 设置为已加载以避免无限重试，但使用备用方案
          setMermaidLoaded(true);
        }
      }
    };

    initMermaid();
  }, [mermaidLoaded]);

  // 模拟数据加载
  useEffect(() => {
    const loadDashboardData = async () => {
      setTimeout(() => {
        setLoading(false);
      }, 800);
    };

    loadDashboardData();
  }, []);

  // 渲染Mermaid图表
  useEffect(() => {
    const renderMermaidChart = async () => {
      if (!mermaidLoaded || !mermaidInstance.current || !mermaidRef.current || !selectedModule || !flowDetails[selectedModule]) {
        return;
      }

      try {
        const flowData = flowDetails[selectedModule];
        const mermaidCode = generateMermaidCode(flowData);

        // 清空容器
        mermaidRef.current.innerHTML = '';

        // 检查 mermaidInstance.current 是否有 render 方法
        if (typeof mermaidInstance.current.render === 'function') {
          // 使用Mermaid渲染
          const { svg } = await mermaidInstance.current.render('mermaid-' + selectedModule, mermaidCode);
          mermaidRef.current.innerHTML = svg;
        } else {
          console.error('Mermaid render method not available');
          throw new Error('Mermaid render method not available');
        }

        // 添加交互事件监听器
        setTimeout(() => {
          const nodes = mermaidRef.current.querySelectorAll('.node');
          nodes.forEach((node, index) => {
            if (flowData.flowSteps[index]) {
              const step = flowData.flowSteps[index];

              // 设置鼠标样式
              node.style.cursor = 'pointer';

              // 添加点击事件
              node.addEventListener('click', () => {
                handleNodeClick(step);
              });

              // 创建悬停提示元素
              const tooltip = document.createElement('div');
              tooltip.style.cssText = `
                position: absolute;
                background: rgba(0, 0, 0, 0.9);
                color: white;
                padding: 12px;
                border-radius: 8px;
                font-size: 14px;
                max-width: 300px;
                z-index: 1000;
                pointer-events: none;
                opacity: 0;
                transition: opacity 0.3s ease;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
                line-height: 1.4;
              `;

              tooltip.innerHTML = `
                <div style="font-weight: bold; margin-bottom: 8px; color: #fff;">${step.title}</div>
                <div style="margin-bottom: 6px;">${step.description}</div>
                <div style="font-size: 12px; color: #ccc;">
                  <span style="background: rgba(255,255,255,0.2); padding: 2px 6px; border-radius: 4px;">
                    ⏱️ ${step.duration}
                  </span>
                </div>
              `;

              document.body.appendChild(tooltip);

              // 鼠标进入事件
              node.addEventListener('mouseenter', (e) => {
                tooltip.style.opacity = '1';

                // 计算位置
                const rect = node.getBoundingClientRect();
                const tooltipRect = tooltip.getBoundingClientRect();

                let left = rect.left + rect.width / 2 - tooltipRect.width / 2;
                let top = rect.top - tooltipRect.height - 10;

                // 边界检查
                if (left < 10) left = 10;
                if (left + tooltipRect.width > window.innerWidth - 10) {
                  left = window.innerWidth - tooltipRect.width - 10;
                }
                if (top < 10) {
                  top = rect.bottom + 10;
                }

                tooltip.style.left = left + 'px';
                tooltip.style.top = top + 'px';
              });

              // 鼠标离开事件
              node.addEventListener('mouseleave', () => {
                tooltip.style.opacity = '0';
              });

              // 添加悬停效果
              node.addEventListener('mouseenter', () => {
                node.style.transform = 'scale(1.05)';
                node.style.transition = 'transform 0.2s ease';
              });

              node.addEventListener('mouseleave', () => {
                node.style.transform = 'scale(1)';
              });
            }
          });
        }, 100);

      } catch (error) {

        // 如果Mermaid渲染失败，显示备用内容
        if (mermaidRef.current) {
          mermaidRef.current.innerHTML = `
            <div style="text-align: center; padding: 40px; color: #666;">
              <p>流程图加载中...</p>
              <p style="font-size: 12px;">如果持续显示此消息，请刷新页面</p>
            </div>
          `;
        }
      }
    };

    renderMermaidChart();
  }, [selectedModule, mermaidLoaded]);

  const handleMenuToggle = (menuId) => {
    setExpandedMenus(prev => ({
      ...prev,
      [menuId]: !prev[menuId]
    }));
  };

  // 处理节点点击事件
  const handleNodeClick = (step) => {
    if (step) {
      setDialogContent({
        title: step.title,
        description: step.description,
        actions: step.actions || []
      });
      setDialogOpen(true);
    }
  };

  // 生成Mermaid代码
  const generateMermaidCode = (flowData) => {
    const steps = flowData.flowSteps;
    let mermaidCode = 'flowchart LR\n';

    // 生成节点和连接
    steps.forEach((step, index) => {
      const nodeId = `step${index + 1}`;
      const nextIndex = index + 1;

      // 添加节点，使用简洁的标题
      const shortTitle = step.title.length > 8 ? step.title.substring(0, 8) + '...' : step.title;
      mermaidCode += `    ${nodeId}["${shortTitle}"]\n`;

      // 添加连接线
      if (nextIndex < steps.length) {
        const nextNodeId = `step${nextIndex + 1}`;
        mermaidCode += `    ${nodeId} --> ${nextNodeId}\n`;
      }
    });

    // 添加点击事件
    mermaidCode += '\n';
    steps.forEach((step, index) => {
      const nodeId = `step${index + 1}`;
      mermaidCode += `    click ${nodeId} "handleNodeClick${index}"\n`;
    });

    // 添加样式
    mermaidCode += '\n';
    steps.forEach((step, index) => {
      const nodeId = `step${index + 1}`;
      const colorClass = getStepStatusColor(step.status);
      mermaidCode += `    class ${nodeId} ${colorClass}Class\n`;
    });

    // 定义样式类
    mermaidCode += `
    classDef primaryClass fill:#e3f2fd,stroke:#1976d2,stroke-width:3px,color:#000,cursor:pointer
    classDef warningClass fill:#fff3e0,stroke:#f57c00,stroke-width:3px,color:#000,cursor:pointer
    classDef successClass fill:#e8f5e8,stroke:#388e3c,stroke-width:3px,color:#000,cursor:pointer
    classDef errorClass fill:#ffebee,stroke:#d32f2f,stroke-width:3px,color:#000,cursor:pointer
    classDef infoClass fill:#e1f5fe,stroke:#0288d1,stroke-width:3px,color:#000,cursor:pointer
    classDef secondaryClass fill:#f3e5f5,stroke:#7b1fa2,stroke-width:3px,color:#000,cursor:pointer
    `;

    return mermaidCode;
  };

  // 备用的简化流程图组件（从左到右布局）
  const renderSimpleFlowChart = (flowData) => {
    return (
      <Box sx={{ p: 2, overflowX: 'auto' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', minWidth: 'max-content', gap: 2 }}>
          {flowData.flowSteps.map((step, index) => (
            <Box key={step.id} sx={{ display: 'flex', alignItems: 'center' }}>
              {/* 步骤卡片 */}
              <Card
                sx={{
                  minWidth: 200,
                  cursor: 'pointer',
                  position: 'relative',
                  border: `2px solid ${theme.palette[getStepStatusColor(step.status)].main}`,
                  '&:hover': {
                    boxShadow: 4,
                    transform: 'translateY(-2px)',
                    transition: 'all 0.2s ease'
                  }
                }}
                onClick={() => handleNodeClick(step)}
              >
                <CardContent sx={{ p: 2, textAlign: 'center' }}>
                  {/* 步骤编号 */}
                  <Avatar sx={{
                    width: 32,
                    height: 32,
                    bgcolor: theme.palette[getStepStatusColor(step.status)].main,
                    mx: 'auto',
                    mb: 1,
                    fontSize: '0.9rem'
                  }}>
                    {index + 1}
                  </Avatar>

                  <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
                    {step.title}
                  </Typography>

                  <Chip
                    label={step.duration}
                    size="small"
                    color={getStepStatusColor(step.status)}
                    variant="outlined"
                  />
                </CardContent>
              </Card>

              {/* 连接箭头 */}
              {index < flowData.flowSteps.length - 1 && (
                <Box sx={{
                  mx: 1,
                  display: 'flex',
                  alignItems: 'center',
                  color: theme.palette[getStepStatusColor(step.status)].main
                }}>
                  <ArrowForward sx={{ fontSize: 24 }} />
                </Box>
              )}
            </Box>
          ))}
        </Box>
      </Box>
    );
  };

  // 业务流程菜单配置
  const businessFlows = [
    {
      id: 'userApprovalFlow',
      title: '用户审批流程',
      icon: <Assignment />,
      color: 'primary',
      description: '用户角色申请的完整审批流程'
    },
    {
      id: 'contentDistributionFlow',
      title: '官方稿件分发流程',
      icon: <ContentPaste />,
      color: 'warning',
      description: '官方稿件从创建到分发的完整流程'
    },
    {
      id: 'paymentRefundFlow',
      title: '支付退款流程',
      icon: <Payment />,
      color: 'success',
      description: '支付处理和退款操作的完整流程'
    }
  ];

  // 业务流程详细配置
  const flowDetails = {
    userApprovalFlow: {
      title: '用户审批流程',
      description: '用户角色申请从提交到审批完成的完整业务流程，包括材料审核、身份验证、权限分配等关键环节',
      icon: <Assignment />,
      color: 'primary',
      flowSteps: [
        {
          id: 'step1',
          title: '用户提交申请',
          description: '用户在前端页面提交角色申请，填写基本信息和上传相关材料',
          actions: [
            { text: '查看申请列表', path: '/admin/roles', description: '查看所有待审批的角色申请' }
          ],
          status: 'pending',
          duration: '用户操作',
          nextStep: 'step2'
        },
        {
          id: 'step2',
          title: '初步材料审核',
          description: '管理员检查申请材料的完整性和基本合规性',
          actions: [
            { text: '材料审核页面', path: '/admin/roles', description: '审核用户提交的申请材料' }
          ],
          status: 'review',
          duration: '1-2个工作日',
          nextStep: 'step3'
        },
        {
          id: 'step3',
          title: '身份验证',
          description: '核实申请人身份信息，验证提交材料的真实性',
          actions: [
            { text: '身份验证工具', path: '/admin/users', description: '使用系统工具验证用户身份' }
          ],
          status: 'verify',
          duration: '2-3个工作日',
          nextStep: 'step4'
        },
        {
          id: 'step4',
          title: '权限评估',
          description: '根据申请角色评估所需权限，确保权限分配合理',
          actions: [
            { text: '权限管理', path: '/super-admin/permissions', description: '查看和配置角色权限' }
          ],
          status: 'assess',
          duration: '1个工作日',
          nextStep: 'step5'
        },
        {
          id: 'step5',
          title: '审批决定',
          description: '管理员做出最终审批决定，通过或拒绝申请',
          actions: [
            { text: '审批操作', path: '/admin/roles', description: '执行最终的审批操作' }
          ],
          status: 'decision',
          duration: '1个工作日',
          nextStep: 'step6'
        },
        {
          id: 'step6',
          title: '结果通知',
          description: '系统自动通知用户审批结果，并激活相应权限',
          actions: [
            { text: '通知管理', path: '/admin/announcements', description: '管理系统通知和公告' }
          ],
          status: 'complete',
          duration: '即时',
          nextStep: null
        }
      ]
    },
    contentDistributionFlow: {
      title: '官方稿件分发流程',
      description: '官方稿件从创建、审核到多渠道分发的完整业务流程，确保内容质量和分发效率',
      icon: <ContentPaste />,
      color: 'warning',
      flowSteps: [
        {
          id: 'step1',
          title: '稿件创建',
          description: '内容团队创建官方稿件，包括文字、图片、视频等多媒体内容',
          actions: [
            { text: '内容管理', path: '/admin/content', description: '管理和创建官方稿件内容' }
          ],
          status: 'create',
          duration: '1-3个工作日',
          nextStep: 'step2'
        },
        {
          id: 'step2',
          title: '内容审核',
          description: '对稿件进行合规性审核，检查内容质量和法律风险',
          actions: [
            { text: '内容审核页面', path: '/admin/content', description: '审核待发布的官方稿件' }
          ],
          status: 'review',
          duration: '4-8小时',
          nextStep: 'step3'
        },
        {
          id: 'step3',
          title: '渠道配置',
          description: '选择合适的分发渠道，配置发布参数和时间安排',
          actions: [
            { text: '渠道管理', path: '/admin/channels', description: '配置内容分发渠道' },
            { text: '服务管理', path: '/admin/services', description: '管理渠道服务配置' },
            { text: '服务审批', path: '/admin/service-approval', description: '审批渠道商提交的服务申请' }
          ],
          status: 'config',
          duration: '30分钟-1小时',
          nextStep: 'step4'
        },
        {
          id: 'step4',
          title: '自动分发',
          description: '系统自动将稿件分发到配置的各个渠道平台',
          actions: [
            { text: '分发监控', path: '/admin/status-monitor', description: '监控稿件分发状态' }
          ],
          status: 'distribute',
          duration: '5-30分钟',
          nextStep: 'step5'
        },
        {
          id: 'step5',
          title: '效果监控',
          description: '监控稿件在各渠道的发布效果和用户反馈',
          actions: [
            { text: '数据分析', path: '/admin/analytics', description: '查看稿件分发效果数据' }
          ],
          status: 'monitor',
          duration: '持续监控',
          nextStep: null
        }
      ]
    },
    paymentRefundFlow: {
      title: '支付退款流程',
      description: '用户支付处理和退款申请的完整业务流程，确保资金安全和用户体验',
      icon: <Payment />,
      color: 'success',
      flowSteps: [
        {
          id: 'step1',
          title: '订单创建',
          description: '用户选择服务或产品，系统生成订单并计算费用',
          actions: [
            { text: '订单管理', path: '/admin/orders', description: '查看和管理所有订单' }
          ],
          status: 'create',
          duration: '即时',
          nextStep: 'step2'
        },
        {
          id: 'step2',
          title: '支付处理',
          description: '用户选择支付方式，系统调用第三方支付接口处理支付',
          actions: [
            { text: '支付中心', path: '/admin/payments', description: '监控和管理支付流程' }
          ],
          status: 'payment',
          duration: '1-5分钟',
          nextStep: 'step3'
        },
        {
          id: 'step3',
          title: '支付确认',
          description: '系统接收支付回调，确认支付状态并更新订单',
          actions: [
            { text: '支付监控', path: '/admin/payments', description: '监控支付状态和异常' }
          ],
          status: 'confirm',
          duration: '1-10分钟',
          nextStep: 'step4'
        },
        {
          id: 'step4',
          title: '服务激活',
          description: '支付成功后自动激活相应服务或发放产品',
          actions: [
            { text: '订单跟踪', path: '/admin/orders', description: '跟踪订单执行状态' }
          ],
          status: 'activate',
          duration: '即时',
          nextStep: 'step5'
        },
        {
          id: 'step5',
          title: '退款申请',
          description: '用户申请退款，系统记录退款原因和相关信息',
          actions: [
            { text: '退款管理', path: '/admin/payments', description: '处理用户退款申请' }
          ],
          status: 'refund_request',
          duration: '用户操作',
          nextStep: 'step6'
        },
        {
          id: 'step6',
          title: '退款审核',
          description: '管理员审核退款申请，验证退款条件和合规性',
          actions: [
            { text: '退款审核', path: '/admin/payments', description: '审核和处理退款申请' }
          ],
          status: 'refund_review',
          duration: '1-3个工作日',
          nextStep: 'step7'
        },
        {
          id: 'step7',
          title: '退款执行',
          description: '审核通过后执行退款操作，资金返回用户账户',
          actions: [
            { text: '财务对账', path: '/admin/payments', description: '进行财务对账和记录' }
          ],
          status: 'refund_complete',
          duration: '1-7个工作日',
          nextStep: null
        }
      ]
    }
  };
  // 渲染右侧业务流程菜单
  const renderBusinessFlowMenu = () => (
    <Paper sx={{ height: '100%', overflow: 'auto' }}>
      <Box sx={{ p: 2, borderBottom: '1px solid #e0e0e0' }}>
        <Typography variant="h6" sx={{ fontWeight: 600, display: 'flex', alignItems: 'center' }}>
          <AccountTree sx={{ mr: 1 }} />
          业务流程
        </Typography>
        <Typography variant="body2" color="textSecondary" sx={{ mt: 0.5 }}>
          点击查看详细流程图
        </Typography>
      </Box>
      <List sx={{ p: 0 }}>
        {businessFlows.map((flow) => (
          <ListItemButton
            key={flow.id}
            selected={selectedModule === flow.id}
            onClick={() => setSelectedModule(flow.id)}
            sx={{
              py: 2,
              px: 2,
              borderBottom: '1px solid #f0f0f0',
              flexDirection: 'column',
              alignItems: 'flex-start',
              '&:hover': {
                bgcolor: alpha(theme.palette[flow.color].main, 0.05),
              },
              '&.Mui-selected': {
                bgcolor: alpha(theme.palette[flow.color].main, 0.08),
                '&:hover': {
                  bgcolor: alpha(theme.palette[flow.color].main, 0.12),
                },
              },
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', mb: 1 }}>
              <Avatar sx={{
                width: 36,
                height: 36,
                bgcolor: theme.palette[flow.color].main,
                mr: 1.5
              }}>
                {React.cloneElement(flow.icon, { sx: { fontSize: 20, color: 'white' } })}
              </Avatar>
              <Box sx={{ flex: 1 }}>
                <Typography variant="subtitle1" sx={{ fontWeight: 600, lineHeight: 1.2 }}>
                  {flow.title}
                </Typography>
              </Box>
              <ChevronRight sx={{
                fontSize: 20,
                color: selectedModule === flow.id ? theme.palette[flow.color].main : 'text.secondary',
                transform: selectedModule === flow.id ? 'rotate(90deg)' : 'none',
                transition: 'all 0.2s'
              }} />
            </Box>
            <Typography variant="body2" color="textSecondary" sx={{ pl: 5.5, lineHeight: 1.3 }}>
              {flow.description}
            </Typography>
          </ListItemButton>
        ))}
      </List>
    </Paper>
  );

  // 获取流程步骤状态颜色
  const getStepStatusColor = (status) => {
    const statusColors = {
      pending: 'warning',
      review: 'info',
      verify: 'primary',
      assess: 'secondary',
      decision: 'error',
      complete: 'success',
      create: 'primary',
      config: 'info',
      distribute: 'warning',
      monitor: 'success',
      payment: 'info',
      confirm: 'primary',
      activate: 'success',
      refund_request: 'warning',
      refund_review: 'error',
      refund_complete: 'success'
    };
    return statusColors[status] || 'default';
  };

  // 渲染左侧流程详情
  const renderFlowDetail = () => {
    const currentFlow = flowDetails[selectedModule];
    if (!currentFlow) {
      return (
        <Paper sx={{ height: '100%', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
          <Box sx={{ textAlign: 'center' }}>
            <AccountTree sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h6" color="textSecondary">
              请选择右侧的业务流程查看详细信息
            </Typography>
          </Box>
        </Paper>
      );
    }

    return (
      <Paper sx={{ height: '100%', overflow: 'auto' }}>
        {/* 流程标题 */}
        <Box sx={{
          p: 3,
          borderBottom: '1px solid #e0e0e0',
          bgcolor: alpha(theme.palette[currentFlow.color].main, 0.05)
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Avatar sx={{
              width: 48,
              height: 48,
              bgcolor: theme.palette[currentFlow.color].main,
              mr: 2
            }}>
              {React.cloneElement(currentFlow.icon, { sx: { fontSize: 24, color: 'white' } })}
            </Avatar>
            <Box>
              <Typography variant="h5" sx={{ fontWeight: 600, mb: 0.5 }}>
                {currentFlow.title}
              </Typography>
              <Chip
                label="业务流程"
                color={currentFlow.color}
                size="small"
              />
            </Box>
          </Box>
          <Typography variant="body1" color="textSecondary">
            {currentFlow.description}
          </Typography>
        </Box>

        {/* Mermaid流程图展示 */}
        <Box sx={{ p: 3 }}>
          <Typography variant="h6" sx={{ fontWeight: 600, mb: 3, display: 'flex', alignItems: 'center' }}>
            <AccountTree sx={{ mr: 1 }} />
            交互式流程图
          </Typography>

          <Alert severity="info" sx={{ mb: 3 }}>
            <Typography variant="body2">
              点击流程图中的任意节点查看详细信息和操作选项
            </Typography>
          </Alert>

          {/* 流程图容器 */}
          <Paper
            sx={{
              p: 3,
              minHeight: 300,
              border: '1px solid #e0e0e0',
              borderRadius: 2,
              overflow: 'auto',
              bgcolor: '#fafafa',
              position: 'relative'
            }}
          >
            {mermaidLoaded ? (
              <div
                ref={mermaidRef}
                style={{
                  width: '100%',
                  minHeight: '250px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  overflowX: 'auto',
                  overflowY: 'hidden'
                }}
              />
            ) : (
              // 备用简化流程图
              <Box>
                <Typography variant="body2" color="textSecondary" sx={{ mb: 2, textAlign: 'center' }}>
                  简化流程图 (点击步骤查看详情)
                </Typography>
                {renderSimpleFlowChart(currentFlow)}
              </Box>
            )}

            {/* 操作提示 */}
            <Box sx={{
              position: 'absolute',
              top: 8,
              right: 8,
              display: 'flex',
              gap: 1
            }}>
              <Chip
                label="💡 悬停查看详情"
                size="small"
                variant="outlined"
                sx={{ fontSize: '0.75rem' }}
              />
              <Chip
                label="🖱️ 点击进入操作"
                size="small"
                variant="outlined"
                sx={{ fontSize: '0.75rem' }}
              />
            </Box>
          </Paper>


        </Box>

      </Paper>
    );
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <CircularProgress size={60} />
      </Box>
    );
  }

  return (
    <Box sx={{ display: 'flex', height: '100vh', bgcolor: '#f5f7fa' }}>
      <CssBaseline />

      {/* 页面标题栏 */}
      <AppBar
        position="fixed"
        sx={{
          zIndex: theme.zIndex.drawer + 1,
          bgcolor: 'white',
          color: 'text.primary',
          boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
        }}
      >
        <Toolbar>
          <Home sx={{ fontSize: 28, color: 'primary.main', mr: 2 }} />
          <Box sx={{ flex: 1 }}>
            <Typography variant="h6" sx={{ fontWeight: 600 }}>
              员工操作首页
            </Typography>
            <Typography variant="body2" color="textSecondary">
              欢迎 {user?.name || '管理员'}，选择右侧功能查看详细操作说明
            </Typography>
          </Box>
          <Chip
            label="管理员"
            color="primary"
            variant="outlined"
            size="small"
          />
        </Toolbar>
      </AppBar>

      {/* 主体内容区域 */}
      <Box sx={{ display: 'flex', flex: 1, mt: 8 }}>
        {/* 左侧流程详情区域 */}
        <Box sx={{
          flex: 1,
          p: 2,
          overflow: 'hidden',
        }}>
          {renderFlowDetail()}
        </Box>

        {/* 右侧业务流程菜单 */}
        <Box sx={{
          width: drawerWidth,
          flexShrink: 0,
          p: 2,
          pl: 0,
        }}>
          {renderBusinessFlowMenu()}
        </Box>
      </Box>

      {/* 节点详情对话框 */}
      <Dialog
        open={dialogOpen}
        onClose={() => setDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle sx={{
          bgcolor: alpha(theme.palette.primary.main, 0.05),
          borderBottom: '1px solid #e0e0e0'
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <PlayCircleOutline sx={{ mr: 1, color: 'primary.main' }} />
            {dialogContent.title}
          </Box>
        </DialogTitle>
        <DialogContent sx={{ pt: 3 }}>
          <Typography variant="body1" sx={{ mb: 3, lineHeight: 1.6 }}>
            {dialogContent.description}
          </Typography>

          {dialogContent.actions && dialogContent.actions.length > 0 && (
            <Box>
              <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
                相关操作
              </Typography>
              <Grid container spacing={2}>
                {dialogContent.actions.map((action, index) => (
                  <Grid item xs={12} sm={6} key={index}>
                    <Card
                      sx={{
                        cursor: 'pointer',
                        '&:hover': {
                          boxShadow: 4,
                          transform: 'translateY(-2px)',
                          transition: 'all 0.2s'
                        }
                      }}
                      onClick={() => {
                        navigate(action.path);
                        setDialogOpen(false);
                      }}
                    >
                      <CardContent sx={{ p: 2 }}>
                        <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 1 }}>
                          {action.text}
                        </Typography>
                        <Typography variant="body2" color="textSecondary">
                          {action.description}
                        </Typography>
                      </CardContent>
                      <CardActions sx={{ pt: 0, justifyContent: 'flex-end' }}>
                        <Button
                          size="small"
                          endIcon={<ArrowForward />}
                          color="primary"
                        >
                          进入
                        </Button>
                      </CardActions>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </Box>
          )}
        </DialogContent>
        <DialogActions sx={{ p: 3, pt: 1 }}>
          <Button onClick={() => setDialogOpen(false)} color="primary">
            关闭
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}

export default AdminDashboard;