import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Container,
  Typography,
  Card,
  CardContent,
  Tabs,
  Tab,
  Button,
  IconButton,
  Stack,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Paper,
  Chip
} from '@mui/material';

// Icons
import {
  Dashboard,
  Analytics,
  TrendingUp,
  Assessment,
  ShowChart,
  Timeline,
  BarChart as BarChartIcon,
  Fullscreen,
  Refresh,
  PieChart,
  People,
  ShoppingCart
} from '@mui/icons-material';

// Recharts
import {
  ResponsiveContainer,
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart as RechartsPieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend
} from 'recharts';

// 颜色配置
const COLORS = ['#8884d8', '#82ca9d', '#ffc658', '#ff7300', '#00ff00'];

// 生成模拟数据
const generateMockData = (days = 30) => {
  const data = [];
  const baseDate = new Date();

  for (let i = days - 1; i >= 0; i--) {
    const date = new Date(baseDate);
    date.setDate(date.getDate() - i);

    data.push({
      date: date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' }),
      revenue: Math.floor(Math.random() * 50000) + 100000,
      orders: Math.floor(Math.random() * 200) + 500,
      users: Math.floor(Math.random() * 1000) + 2000,
      conversion: (Math.random() * 5 + 2).toFixed(2),
      newUsers: Math.floor(Math.random() * 300) + 200,
      activeUsers: Math.floor(Math.random() * 800) + 1500
    });
  }

  return data;
};

// 饼图数据
const pieData = [
  { name: '订阅服务', value: 45, color: '#8884d8' },
  { name: 'AI内容生成', value: 30, color: '#82ca9d' },
  { name: '数据分析', value: 15, color: '#ffc658' },
  { name: '其他服务', value: 10, color: '#ff7300' }
];

function TabPanel({ children, value, index, ...other }) {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`analytics-tabpanel-${index}`}
      aria-labelledby={`analytics-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const AnalyticsCenter = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [dateRange, setDateRange] = useState('30');
  const [chartType, setChartType] = useState('line');
  const [data, setData] = useState([]);

  useEffect(() => {
    setData(generateMockData(parseInt(dateRange)));
  }, [dateRange]);

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const handleDateRangeChange = (event) => {
    setDateRange(event.target.value);
  };

  const handleChartTypeChange = (event) => {
    setChartType(event.target.value);
  };

  // 统计卡片数据
  const getStatsCards = () => {
    switch (activeTab) {
      case 0: // 经营概况
        return [
          { title: '总收入', value: '¥285,600.00', change: '+15.3%', icon: <TrendingUp />, color: '#4caf50' },
          { title: '订单数量', value: '1,256', change: '+8.2%', icon: <Assessment />, color: '#2196f3' },
          { title: '活跃用户', value: '3,420', change: '+12.1%', icon: <Dashboard />, color: '#ff9800' },
          { title: '转化率', value: '3.45%', change: '+2.3%', icon: <Analytics />, color: '#9c27b0' }
        ];
      case 1: // 订单分析
        return [
          { title: '总订单', value: '1,256', change: '+8.2%', icon: <ShoppingCart />, color: '#2196f3' },
          { title: '已完成', value: '1,089', change: '+12.5%', icon: <Assessment />, color: '#4caf50' },
          { title: '处理中', value: '134', change: '-5.1%', icon: <Timeline />, color: '#ff9800' },
          { title: '已取消', value: '33', change: '+1.2%', icon: <ShowChart />, color: '#f44336' }
        ];
      case 2: // 用户分析
        return [
          { title: '总用户', value: '12,456', change: '+18.3%', icon: <People />, color: '#2196f3' },
          { title: '新增用户', value: '234', change: '+25.1%', icon: <TrendingUp />, color: '#4caf50' },
          { title: '活跃用户', value: '3,420', change: '+12.1%', icon: <Dashboard />, color: '#ff9800' },
          { title: '留存率', value: '68.5%', change: '+3.2%', icon: <Analytics />, color: '#9c27b0' }
        ];
      default:
        return [];
    }
  };

  const renderChart = () => {
    const commonProps = {
      width: '100%',
      height: 350,
      data: data,
      margin: { top: 20, right: 30, left: 20, bottom: 5 }
    };

    let dataKey = 'revenue';
    let chartTitle = '收入趋势';

    switch (activeTab) {
      case 1: // 订单分析
        dataKey = 'orders';
        chartTitle = '订单趋势';
        break;
      case 2: // 用户分析
        dataKey = 'users';
        chartTitle = '用户趋势';
        break;
      default:
        dataKey = 'revenue';
        chartTitle = '收入趋势';
    }

    switch (chartType) {
      case 'area':
        return (
          <ResponsiveContainer {...commonProps}>
            <AreaChart data={data}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Area type="monotone" dataKey={dataKey} stackId="1" stroke="#8884d8" fill="#8884d8" />
            </AreaChart>
          </ResponsiveContainer>
        );
      case 'bar':
        return (
          <ResponsiveContainer {...commonProps}>
            <BarChart data={data}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Bar dataKey={dataKey} fill="#8884d8" />
            </BarChart>
          </ResponsiveContainer>
        );
      default:
        return (
          <ResponsiveContainer {...commonProps}>
            <LineChart data={data}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Line type="monotone" dataKey={dataKey} stroke="#8884d8" strokeWidth={2} />
            </LineChart>
          </ResponsiveContainer>
        );
    }
  };

  return (
    <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
      {/* 页面标题 */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          数据分析中心
        </Typography>
        <Typography variant="body1" color="text.secondary">
          实时监控业务数据和关键指标
        </Typography>
      </Box>

      {/* 标签页导航 */}
      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          variant="fullWidth"
        >
          <Tab
            icon={<Dashboard />}
            label="经营概况"
            iconPosition="start"
          />
          <Tab
            icon={<ShoppingCart />}
            label="订单分析"
            iconPosition="start"
          />
          <Tab
            icon={<People />}
            label="用户分析"
            iconPosition="start"
          />
        </Tabs>
      </Paper>

      {/* 标签页内容 */}
      <TabPanel value={activeTab} index={0}>
        {/* 经营概况内容 */}
        <Box>
          {/* 控制面板 */}
          <Paper sx={{ mb: 3, p: 2 }}>
            <Stack direction="row" spacing={2} alignItems="center">
              <FormControl size="small" sx={{ minWidth: 120 }}>
                <InputLabel>时间范围</InputLabel>
                <Select value={dateRange} onChange={handleDateRangeChange} label="时间范围">
                  <MenuItem value="7">最近7天</MenuItem>
                  <MenuItem value="30">最近30天</MenuItem>
                  <MenuItem value="90">最近90天</MenuItem>
                </Select>
              </FormControl>

              <FormControl size="small" sx={{ minWidth: 120 }}>
                <InputLabel>图表类型</InputLabel>
                <Select value={chartType} onChange={handleChartTypeChange} label="图表类型">
                  <MenuItem value="line">折线图</MenuItem>
                  <MenuItem value="area">面积图</MenuItem>
                  <MenuItem value="bar">柱状图</MenuItem>
                </Select>
              </FormControl>

              <Box sx={{ flexGrow: 1 }} />

              <IconButton>
                <Refresh />
              </IconButton>
              <IconButton>
                <Fullscreen />
              </IconButton>
            </Stack>
          </Paper>

          {/* 统计卡片 */}
          <Box sx={{ display: 'flex', gap: 2, mb: 3, flexWrap: 'wrap' }}>
            {getStatsCards().map((card, index) => (
              <Card key={index} sx={{ flex: '1 1 250px', minWidth: 250 }}>
                <CardContent>
                  <Stack direction="row" alignItems="center" justifyContent="space-between">
                    <Box>
                      <Typography color="text.secondary" gutterBottom variant="body2">
                        {card.title}
                      </Typography>
                      <Typography variant="h5" component="div">
                        {card.value}
                      </Typography>
                      <Chip
                        label={card.change}
                        size="small"
                        sx={{
                          mt: 1,
                          backgroundColor: card.color,
                          color: 'white'
                        }}
                      />
                    </Box>
                    <Box sx={{ color: card.color }}>
                      {card.icon}
                    </Box>
                  </Stack>
                </CardContent>
              </Card>
            ))}
          </Box>

          {/* 主要图表区域 */}
          <Box sx={{ display: 'flex', gap: 3, flexWrap: 'wrap' }}>
            <Card sx={{ flex: '2 1 600px', minWidth: 600 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  收入趋势分析
                </Typography>
                <Box sx={{ height: 350 }}>
                  {renderChart()}
                </Box>
              </CardContent>
            </Card>

            <Card sx={{ flex: '1 1 300px', minWidth: 300 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  业务分布
                </Typography>
                <Box sx={{ height: 350 }}>
                  <ResponsiveContainer width="100%" height="100%">
                    <RechartsPieChart>
                      <Pie
                        data={pieData}
                        cx="50%"
                        cy="50%"
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                        label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      >
                        {pieData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </RechartsPieChart>
                  </ResponsiveContainer>
                </Box>
              </CardContent>
            </Card>
          </Box>
        </Box>
      </TabPanel>

      <TabPanel value={activeTab} index={1}>
        {/* 订单分析内容 */}
        <Box>
          {/* 控制面板 */}
          <Paper sx={{ mb: 3, p: 2 }}>
            <Stack direction="row" spacing={2} alignItems="center">
              <FormControl size="small" sx={{ minWidth: 120 }}>
                <InputLabel>时间范围</InputLabel>
                <Select value={dateRange} onChange={handleDateRangeChange} label="时间范围">
                  <MenuItem value="7">最近7天</MenuItem>
                  <MenuItem value="30">最近30天</MenuItem>
                  <MenuItem value="90">最近90天</MenuItem>
                </Select>
              </FormControl>

              <FormControl size="small" sx={{ minWidth: 120 }}>
                <InputLabel>图表类型</InputLabel>
                <Select value={chartType} onChange={handleChartTypeChange} label="图表类型">
                  <MenuItem value="line">折线图</MenuItem>
                  <MenuItem value="area">面积图</MenuItem>
                  <MenuItem value="bar">柱状图</MenuItem>
                </Select>
              </FormControl>

              <Box sx={{ flexGrow: 1 }} />

              <IconButton>
                <Refresh />
              </IconButton>
              <IconButton>
                <Fullscreen />
              </IconButton>
            </Stack>
          </Paper>

          {/* 统计卡片 */}
          <Box sx={{ display: 'flex', gap: 2, mb: 3, flexWrap: 'wrap' }}>
            {getStatsCards().map((card, index) => (
              <Card key={index} sx={{ flex: '1 1 250px', minWidth: 250 }}>
                <CardContent>
                  <Stack direction="row" alignItems="center" justifyContent="space-between">
                    <Box>
                      <Typography color="text.secondary" gutterBottom variant="body2">
                        {card.title}
                      </Typography>
                      <Typography variant="h5" component="div">
                        {card.value}
                      </Typography>
                      <Chip
                        label={card.change}
                        size="small"
                        sx={{
                          mt: 1,
                          backgroundColor: card.color,
                          color: 'white'
                        }}
                      />
                    </Box>
                    <Box sx={{ color: card.color }}>
                      {card.icon}
                    </Box>
                  </Stack>
                </CardContent>
              </Card>
            ))}
          </Box>

          {/* 订单分析图表 */}
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                订单趋势分析
              </Typography>
              <Box sx={{ height: 400 }}>
                {renderChart()}
              </Box>
            </CardContent>
          </Card>
        </Box>
      </TabPanel>

      <TabPanel value={activeTab} index={2}>
        {/* 用户分析内容 */}
        <Box>
          {/* 控制面板 */}
          <Paper sx={{ mb: 3, p: 2 }}>
            <Stack direction="row" spacing={2} alignItems="center">
              <FormControl size="small" sx={{ minWidth: 120 }}>
                <InputLabel>时间范围</InputLabel>
                <Select value={dateRange} onChange={handleDateRangeChange} label="时间范围">
                  <MenuItem value="7">最近7天</MenuItem>
                  <MenuItem value="30">最近30天</MenuItem>
                  <MenuItem value="90">最近90天</MenuItem>
                </Select>
              </FormControl>

              <FormControl size="small" sx={{ minWidth: 120 }}>
                <InputLabel>图表类型</InputLabel>
                <Select value={chartType} onChange={handleChartTypeChange} label="图表类型">
                  <MenuItem value="line">折线图</MenuItem>
                  <MenuItem value="area">面积图</MenuItem>
                  <MenuItem value="bar">柱状图</MenuItem>
                </Select>
              </FormControl>

              <Box sx={{ flexGrow: 1 }} />

              <IconButton>
                <Refresh />
              </IconButton>
              <IconButton>
                <Fullscreen />
              </IconButton>
            </Stack>
          </Paper>

          {/* 统计卡片 */}
          <Box sx={{ display: 'flex', gap: 2, mb: 3, flexWrap: 'wrap' }}>
            {getStatsCards().map((card, index) => (
              <Card key={index} sx={{ flex: '1 1 250px', minWidth: 250 }}>
                <CardContent>
                  <Stack direction="row" alignItems="center" justifyContent="space-between">
                    <Box>
                      <Typography color="text.secondary" gutterBottom variant="body2">
                        {card.title}
                      </Typography>
                      <Typography variant="h5" component="div">
                        {card.value}
                      </Typography>
                      <Chip
                        label={card.change}
                        size="small"
                        sx={{
                          mt: 1,
                          backgroundColor: card.color,
                          color: 'white'
                        }}
                      />
                    </Box>
                    <Box sx={{ color: card.color }}>
                      {card.icon}
                    </Box>
                  </Stack>
                </CardContent>
              </Card>
            ))}
          </Box>

          {/* 用户分析图表 */}
          <Box sx={{ display: 'flex', gap: 3, flexWrap: 'wrap' }}>
            <Card sx={{ flex: '1 1 500px', minWidth: 500 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  用户增长趋势
                </Typography>
                <Box sx={{ height: 350 }}>
                  {renderChart()}
                </Box>
              </CardContent>
            </Card>

            <Card sx={{ flex: '1 1 400px', minWidth: 400 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  用户活跃度分析
                </Typography>
                <Box sx={{ height: 350 }}>
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={data.slice(-7)}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Bar dataKey="newUsers" fill="#8884d8" name="新增用户" />
                      <Bar dataKey="activeUsers" fill="#82ca9d" name="活跃用户" />
                    </BarChart>
                  </ResponsiveContainer>
                </Box>
              </CardContent>
            </Card>
          </Box>
        </Box>
      </TabPanel>
    </Container>
  );
};

export default AnalyticsCenter;
