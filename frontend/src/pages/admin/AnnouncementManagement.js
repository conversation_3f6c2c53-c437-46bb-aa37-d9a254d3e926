import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Button,
  IconButton,
  TextField,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Stack,
  Typography,
  Alert,
  Switch,
  FormControlLabel,
  Grid,
  Tooltip,

} from '@mui/material';
import {
  Add,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  Send as SendIcon,
  PushPin as PinIcon,
  Notifications as NotificationIcon,
  Search as SearchIcon,
  Campaign as CampaignIcon,
  Clear as ClearIcon
} from '@mui/icons-material';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { zhCN } from 'date-fns/locale';
import axios from 'axios';
import { API_ENDPOINTS, ApiConfig } from '../../config/api-config';
import { AppConfig } from '../../config/app-config';

// 创建axios实例
const axiosInstance = axios.create({
  baseURL: ApiConfig.baseURL,
  timeout: ApiConfig.timeout,
  headers: ApiConfig.defaultHeaders,
});

// 请求拦截器 - 添加token
axiosInstance.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem(`${AppConfig.storage.prefix}${AppConfig.storage.tokenKey}`);
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
axiosInstance.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token过期，跳转到登录页
      window.location.href = '/#/auth/login';
    }
    return Promise.reject(error);
  }
);

// 格式化时间用于提交
const formatTimeForSubmit = (dateTime) => {
  if (!dateTime) return null;

  // 如果是Date对象，转换为本地时间字符串
  if (dateTime instanceof Date) {
    const year = dateTime.getFullYear();
    const month = String(dateTime.getMonth() + 1).padStart(2, '0');
    const day = String(dateTime.getDate()).padStart(2, '0');
    const hours = String(dateTime.getHours()).padStart(2, '0');
    const minutes = String(dateTime.getMinutes()).padStart(2, '0');
    const seconds = String(dateTime.getSeconds()).padStart(2, '0');

    return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}`;
  }

  // 如果已经是字符串，直接返回
  if (typeof dateTime === 'string') {
    return dateTime;
  }

  return null;
};

const AnnouncementManagement = () => {
  const [announcements, setAnnouncements] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [total, setTotal] = useState(0);
  const [openDialog, setOpenDialog] = useState(false);
  const [dialogMode, setDialogMode] = useState('create');
  const [selectedAnnouncement, setSelectedAnnouncement] = useState(null);
  const [formData, setFormData] = useState({
    title: '',
    content: '',
    summary: '',
    type: 'notice',
    priority: 'normal',
    target_audience: 'all',
    is_pinned: false,
    is_popup: false,
    publish_time: null,
    expire_time: null
  });

  const [filters, setFilters] = useState({
    type: '',
    priority: '',
    status: '',
    target_audience: '',
    is_pinned: '',
    keyword: ''
  });




  const announcementTypes = [
    { value: 'system', label: '系统公告', color: 'primary' },
    { value: 'maintenance', label: '维护公告', color: 'warning' },
    { value: 'feature', label: '功能更新', color: 'info' },
    { value: 'promotion', label: '推广活动', color: 'success' },
    { value: 'notice', label: '通知公告', color: 'secondary' }
  ];

  const priorityLevels = [
    { value: 'low', label: '低', color: '#9e9e9e' },
    { value: 'normal', label: '普通', color: '#2196f3' },
    { value: 'high', label: '高', color: '#ff9800' },
    { value: 'urgent', label: '紧急', color: '#f44336' }
  ];

  const statusOptions = [
    { value: 'draft', label: '草稿', color: 'default' },
    { value: 'published', label: '已发布', color: 'success' },
    { value: 'archived', label: '已归档', color: 'secondary' }
  ];

  const targetAudiences = [
    { value: 'all', label: '所有用户' },
    { value: 'enterprise', label: '企业用户' },
    { value: 'channel', label: '渠道商' },
    { value: 'agent', label: '代理商' },
    { value: 'admin', label: '管理员' }
  ];

  // 组件挂载时自动加载一次数据
  useEffect(() => {
    fetchAnnouncements();
  }, []); // 只在组件挂载时执行一次

  // 当分页或过滤条件变化时重新加载数据
  useEffect(() => {
    fetchAnnouncements();
  }, [page, rowsPerPage, filters]);

  // 自动清除提示消息
  useEffect(() => {
    if (error || success) {
      const timer = setTimeout(() => {
        setError('');
        setSuccess('');
      }, 5000); // 5秒后自动清除

      return () => clearTimeout(timer);
    }
  }, [error, success]); // eslint-disable-line react-hooks/exhaustive-deps

  const fetchAnnouncements = async () => {
    setLoading(true);
    setError('');
    try {
      const params = {
        page: page + 1,
        page_size: rowsPerPage,
        ...Object.fromEntries(
          Object.entries(filters).filter(([_, v]) => v !== '')
        )
      };

      const response = await axiosInstance.get(API_ENDPOINTS.ANNOUNCEMENTS, { params });
      if (response.data.success) {
        const data = response.data.data;
        setAnnouncements(data.items || []);
        setTotal(data.pagination?.total || 0);
      } else {
        setError(response.data.message || '获取公告列表失败');
      }
    } catch (error) {
      console.error('获取公告列表失败:', error);
      const errorMessage = error.response?.data?.detail || error.response?.data?.message || error.message || '获取公告列表失败';
      setError(typeof errorMessage === 'string' ? errorMessage : '获取公告列表失败');
    } finally {
      setLoading(false);
    }
  };



  const handleOpenDialog = (mode, announcement = null) => {
    setDialogMode(mode);
    setSelectedAnnouncement(announcement);

    if ((mode === 'edit' || mode === 'view') && announcement) {
      const formDataToSet = {
        ...announcement,
        title: announcement.title || '',
        content: announcement.content || '',
        summary: announcement.summary || '',
        type: announcement.type || 'notice',
        priority: announcement.priority || 'normal',
        target_audience: announcement.target_audience || 'all',
        is_pinned: announcement.is_pinned || false,
        is_popup: announcement.is_popup || false,
        publish_time: announcement.publish_time ? new Date(announcement.publish_time) : null,
        expire_time: announcement.expire_time ? new Date(announcement.expire_time) : null
      };
      setFormData(formDataToSet);
    } else if (mode === 'create') {
      setFormData({
        title: '',
        content: '',
        summary: '',
        type: 'notice',
        priority: 'normal',
        target_audience: 'all',
        is_pinned: false,
        is_popup: false,
        publish_time: null,
        expire_time: null
      });
    }

    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedAnnouncement(null);
    setFormData({
      title: '',
      content: '',
      summary: '',
      type: 'notice',
      priority: 'normal',
      target_audience: 'all',
      is_pinned: false,
      is_popup: false,
      publish_time: null,
      expire_time: null
    });
  };

  const handleSubmit = async () => {
    setError('');
    setSuccess('');

    // 前端验证
    if (!formData.title.trim()) {
      setError('请输入公告标题');
      return;
    }
    if (!formData.content.trim()) {
      setError('请输入公告内容');
      return;
    }

    // 时间验证
    if (formData.publish_time && formData.expire_time) {
      if (new Date(formData.expire_time) <= new Date(formData.publish_time)) {
        setError('过期时间必须晚于发布时间');
        return;
      }
    }

    try {
      // 清理数据，将空的时间字段设为 null
      const submitData = {
        ...formData,
        publish_time: formatTimeForSubmit(formData.publish_time),
        expire_time: formatTimeForSubmit(formData.expire_time),
        summary: formData.summary || null
      };

      let response;
      if (dialogMode === 'create') {
        response = await axiosInstance.post(API_ENDPOINTS.ANNOUNCEMENTS, submitData);
        if (response.data.success) {
          setSuccess('公告创建成功');
        } else {
          setError(response.data.message || '创建失败');
          return;
        }
      } else if (dialogMode === 'edit') {
        // 只发送需要更新的字段
        const updateData = {
          title: formData.title,
          content: formData.content,
          summary: formData.summary,
          type: formData.type,
          priority: formData.priority,
          target_audience: formData.target_audience,
          is_pinned: formData.is_pinned,
          is_popup: formData.is_popup,
          publish_time: formatTimeForSubmit(formData.publish_time),
          expire_time: formatTimeForSubmit(formData.expire_time)
        };

        response = await axiosInstance.put(
          `${API_ENDPOINTS.ANNOUNCEMENTS}/${selectedAnnouncement.id}`,
          updateData
        );
        if (response.data.success) {
          setSuccess('公告更新成功');
        } else {
          setError(response.data.message || '更新失败');
          return;
        }
      }

      handleCloseDialog();
      fetchAnnouncements();
    } catch (error) {
      console.error('操作失败:', error);
      const errorMessage = error.response?.data?.detail || error.response?.data?.message || error.message || '操作失败';
      setError(typeof errorMessage === 'string' ? errorMessage : '操作失败');
    }
  };

  const handleDelete = async (id) => {
    if (!window.confirm('确定要删除这条公告吗？')) {
      return;
    }

    setError('');
    setSuccess('');

    try {
      const response = await axiosInstance.delete(`${API_ENDPOINTS.ANNOUNCEMENTS}/${id}`);
      if (response.data.success) {
        setSuccess('公告删除成功');
        fetchAnnouncements();
      } else {
        setError(response.data.message || '删除失败');
      }
    } catch (error) {
      console.error('删除失败:', error);
      const errorMessage = error.response?.data?.detail || error.response?.data?.message || error.message || '删除失败';
      setError(typeof errorMessage === 'string' ? errorMessage : '删除失败');
    }
  };

  const handlePublish = async (id) => {
    setError('');
    setSuccess('');

    try {
      // 通过更新状态为published来发布公告
      const response = await axiosInstance.put(`${API_ENDPOINTS.ANNOUNCEMENTS}/${id}`, {
        status: 'published'
      });

      if (response.data.success) {
        setSuccess('公告发布成功');
        fetchAnnouncements();
      } else {
        setError(response.data.message || '发布失败');
      }
    } catch (error) {
      console.error('发布失败:', error);
      const errorMessage = error.response?.data?.detail || error.response?.data?.message || error.message || '发布失败';
      setError(typeof errorMessage === 'string' ? errorMessage : '发布失败');
    }
  };

  const handleChangePage = (_, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleFilterChange = (field, value) => {
    setFilters(prev => ({
      ...prev,
      [field]: value
    }));
    setPage(0);
  };

  const clearFilters = () => {
    setFilters({
      type: '',
      priority: '',
      status: '',
      target_audience: '',
      is_pinned: '',
      keyword: ''
    });
    setPage(0);
  };

  const getTypeChip = (type) => {
    const typeConfig = announcementTypes.find(t => t.value === type);
    return typeConfig ? (
      <Chip 
        label={typeConfig.label} 
        size="small" 
        color={typeConfig.color}
      />
    ) : null;
  };

  const getPriorityChip = (priority) => {
    const priorityConfig = priorityLevels.find(p => p.value === priority);
    return priorityConfig ? (
      <Chip 
        label={priorityConfig.label} 
        size="small" 
        style={{ backgroundColor: priorityConfig.color, color: 'white' }}
      />
    ) : null;
  };



  return (
    <Box sx={{ width: '100%', p: 3, backgroundColor: '#f5f5f5', minHeight: '100vh' }}>
      {/* 页面标题区域 */}
      <Box sx={{
        backgroundColor: '#fff',
        borderRadius: 3,
        border: '1px solid #e0e0e0',
        p: 4,
        mb: 3,
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <CampaignIcon sx={{ fontSize: 32, color: '#1976d2' }} />
          <Box>
            <Typography variant="h4" sx={{ fontWeight: 700, color: '#1a1a1a', mb: 0.5 }}>
              公告管理
            </Typography>
            <Typography variant="body1" sx={{ color: '#666' }}>
              发布和管理系统公告，通知用户重要信息和更新
            </Typography>
          </Box>
        </Box>
      </Box>

      {error && (
        <Alert
          severity="error"
          sx={{ mb: 2 }}
          onClose={() => setError('')}
        >
          {error}
        </Alert>
      )}
      {success && (
        <Alert
          severity="success"
          sx={{ mb: 2 }}
          onClose={() => setSuccess('')}
        >
          {success}
        </Alert>
      )}

      <Paper sx={{ mb: 2, p: 2 }}>
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, alignItems: 'center' }}>
          {/* 筛选控件区域 */}
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, flex: 1, alignItems: 'center' }}>
            <FormControl size="small" sx={{ minWidth: 120 }}>
              <InputLabel>类型</InputLabel>
              <Select
                value={filters.type}
                onChange={(e) => handleFilterChange('type', e.target.value)}
                label="类型"
              >
                <MenuItem value="">全部</MenuItem>
                {announcementTypes.map(type => (
                  <MenuItem key={type.value} value={type.value}>
                    {type.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <FormControl size="small" sx={{ minWidth: 120 }}>
              <InputLabel>优先级</InputLabel>
              <Select
                value={filters.priority}
                onChange={(e) => handleFilterChange('priority', e.target.value)}
                label="优先级"
              >
                <MenuItem value="">全部</MenuItem>
                {priorityLevels.map(level => (
                  <MenuItem key={level.value} value={level.value}>
                    {level.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <FormControl size="small" sx={{ minWidth: 120 }}>
              <InputLabel>状态</InputLabel>
              <Select
                value={filters.status}
                onChange={(e) => handleFilterChange('status', e.target.value)}
                label="状态"
              >
                <MenuItem value="">全部</MenuItem>
                {statusOptions.map(status => (
                  <MenuItem key={status.value} value={status.value}>
                    {status.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <FormControl size="small" sx={{ minWidth: 120 }}>
              <InputLabel>目标用户</InputLabel>
              <Select
                value={filters.target_audience}
                onChange={(e) => handleFilterChange('target_audience', e.target.value)}
                label="目标用户"
              >
                <MenuItem value="">全部</MenuItem>
                {targetAudiences.map(audience => (
                  <MenuItem key={audience.value} value={audience.value}>
                    {audience.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <FormControl size="small" sx={{ minWidth: 120 }}>
              <InputLabel>置顶</InputLabel>
              <Select
                value={filters.is_pinned}
                onChange={(e) => handleFilterChange('is_pinned', e.target.value)}
                label="置顶"
              >
                <MenuItem value="">全部</MenuItem>
                <MenuItem value="true">已置顶</MenuItem>
                <MenuItem value="false">未置顶</MenuItem>
              </Select>
            </FormControl>

            <TextField
              size="small"
              placeholder="关键词搜索"
              value={filters.keyword}
              onChange={(e) => handleFilterChange('keyword', e.target.value)}
              sx={{ minWidth: 200 }}
              slotProps={{
                input: {
                  startAdornment: <SearchIcon />
                }
              }}
            />

            <Button
              variant="outlined"
              onClick={clearFilters}
              startIcon={<ClearIcon />}
              size="small"
            >
              清除
            </Button>
          </Box>

          {/* 新建公告按钮 - 固定在右侧 */}
          <Button
            variant="contained"
            color="success"
            onClick={() => handleOpenDialog('create')}
            startIcon={<Add />}
            size="small"
            sx={{ flexShrink: 0 }}
          >
            新建公告
          </Button>
        </Box>
      </Paper>

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>标题</TableCell>
              <TableCell>类型</TableCell>
              <TableCell>优先级</TableCell>
              <TableCell>目标用户</TableCell>
              <TableCell>状态</TableCell>
              <TableCell>特殊标记</TableCell>
              <TableCell>浏览次数</TableCell>
              <TableCell>创建时间</TableCell>
              <TableCell>操作</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={9} align="center">
                  <Typography>加载中...</Typography>
                </TableCell>
              </TableRow>
            ) : announcements.length === 0 ? (
              <TableRow>
                <TableCell colSpan={9} align="center">
                  <Typography>暂无数据</Typography>
                </TableCell>
              </TableRow>
            ) : announcements.map((announcement) => (
              <TableRow key={announcement.id}>
                <TableCell>
                  <Typography variant="body2" sx={{ maxWidth: 200, overflow: 'hidden', textOverflow: 'ellipsis' }}>
                    {announcement.title}
                  </Typography>
                </TableCell>
                <TableCell>{getTypeChip(announcement.type)}</TableCell>
                <TableCell>{getPriorityChip(announcement.priority)}</TableCell>
                <TableCell>
                  <Chip
                    label={targetAudiences.find(a => a.value === announcement.target_audience)?.label || announcement.target_audience}
                    size="small"
                    variant="outlined"
                  />
                </TableCell>
                <TableCell>
                  <Chip
                    label={statusOptions.find(s => s.value === announcement.status)?.label || announcement.status}
                    size="small"
                    color={statusOptions.find(s => s.value === announcement.status)?.color || 'default'}
                  />
                </TableCell>
                <TableCell>
                  <Stack direction="row" spacing={0.5}>
                    {announcement.is_pinned && (
                      <Tooltip title="置顶">
                        <PinIcon color="primary" fontSize="small" />
                      </Tooltip>
                    )}
                    {announcement.is_popup && (
                      <Tooltip title="弹窗">
                        <NotificationIcon color="warning" fontSize="small" />
                      </Tooltip>
                    )}
                  </Stack>
                </TableCell>
                <TableCell>
                  <Typography variant="caption">
                    {announcement.view_count || 0}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="caption">
                    {new Date(announcement.created_at).toLocaleDateString('zh-CN')}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Stack direction="row" spacing={1}>
                    <Tooltip title="查看">
                      <IconButton size="small" onClick={() => handleOpenDialog('view', announcement)}>
                        <ViewIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="编辑">
                      <IconButton size="small" onClick={() => handleOpenDialog('edit', announcement)}>
                        <EditIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                    {announcement.status === 'draft' && (
                      <Tooltip title="发布">
                        <IconButton size="small" onClick={() => handlePublish(announcement.id)}>
                          <SendIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    )}
                    <Tooltip title="删除">
                      <IconButton size="small" color="error" onClick={() => handleDelete(announcement.id)}>
                        <DeleteIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </Stack>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
        <TablePagination
          component="div"
          count={total}
          page={page}
          onPageChange={handleChangePage}
          rowsPerPage={rowsPerPage}
          onRowsPerPageChange={handleChangeRowsPerPage}
          rowsPerPageOptions={[5, 10, 25, 50]}
          labelRowsPerPage="每页显示"
        />
      </TableContainer>

      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="xl" fullWidth>
        <DialogTitle>
          {dialogMode === 'view' ? '查看公告' : dialogMode === 'edit' ? '编辑公告' : '创建公告'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            {/* 标题行 */}
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>
                基本信息
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={3}>
                  <TextField
                    fullWidth
                    label="标题"
                    value={formData.title}
                    onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                    disabled={dialogMode === 'view'}
                    size="small"
                  />
                </Grid>
                <Grid item xs={9}>
                  <TextField
                    fullWidth
                    label="摘要（可选）"
                    value={formData.summary}
                    onChange={(e) => setFormData({ ...formData, summary: e.target.value })}
                    disabled={dialogMode === 'view'}
                    size="small"
                  />
                </Grid>
              </Grid>
            </Box>

            {/* 内容行 */}
            <Box sx={{ mb: 3 }}>
              <TextField
                fullWidth
                label="内容"
                value={formData.content}
                onChange={(e) => setFormData({ ...formData, content: e.target.value })}
                disabled={dialogMode === 'view'}
                multiline
                rows={8}
                size="small"
                sx={{ width: '100%' }}
              />
            </Box>

            {/* 分类设置 */}
            <Box sx={{ mb: 3 }}>
              <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>
                分类设置
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={3}>
                  <FormControl fullWidth size="small">
                    <InputLabel>类型</InputLabel>
                    <Select
                      value={formData.type}
                      onChange={(e) => setFormData({ ...formData, type: e.target.value })}
                      disabled={dialogMode === 'view'}
                      label="类型"
                    >
                      {announcementTypes.map(type => (
                        <MenuItem key={type.value} value={type.value}>
                          {type.label}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={3}>
                  <FormControl fullWidth size="small">
                    <InputLabel>优先级</InputLabel>
                    <Select
                      value={formData.priority}
                      onChange={(e) => setFormData({ ...formData, priority: e.target.value })}
                      disabled={dialogMode === 'view'}
                      label="优先级"
                    >
                      {priorityLevels.map(level => (
                        <MenuItem key={level.value} value={level.value}>
                          {level.label}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={3}>
                  <FormControl fullWidth size="small">
                    <InputLabel>目标用户</InputLabel>
                    <Select
                      value={formData.target_audience}
                      onChange={(e) => setFormData({ ...formData, target_audience: e.target.value })}
                      disabled={dialogMode === 'view'}
                      label="目标用户"
                    >
                      {targetAudiences.map(audience => (
                        <MenuItem key={audience.value} value={audience.value}>
                          {audience.label}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={3}>
                  {/* 预留空间 */}
                </Grid>
              </Grid>
            </Box>

            {/* 时间设置 */}
            <Box sx={{ mb: 3 }}>
              <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>
                时间设置
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <LocalizationProvider dateAdapter={AdapterDateFns} locale={zhCN}>
                    <DateTimePicker
                      label="发布时间（可选）"
                      value={formData.publish_time}
                      onChange={(value) => setFormData({ ...formData, publish_time: value })}
                      disabled={dialogMode === 'view'}
                      slotProps={{
                        textField: {
                          fullWidth: true,
                          size: 'small'
                        }
                      }}
                    />
                  </LocalizationProvider>
                </Grid>
                <Grid item xs={6}>
                  <LocalizationProvider dateAdapter={AdapterDateFns} locale={zhCN}>
                    <DateTimePicker
                      label="过期时间（可选）"
                      value={formData.expire_time}
                      onChange={(value) => setFormData({ ...formData, expire_time: value })}
                      disabled={dialogMode === 'view'}
                      slotProps={{
                        textField: {
                          fullWidth: true,
                          size: 'small'
                        }
                      }}
                    />
                  </LocalizationProvider>
                </Grid>
              </Grid>
            </Box>

            {/* 显示选项 */}
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600, color: 'primary.main' }}>
                显示选项
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={formData.is_pinned}
                        onChange={(e) => setFormData({ ...formData, is_pinned: e.target.checked })}
                        disabled={dialogMode === 'view'}
                        color="primary"
                      />
                    }
                    label="置顶显示"
                  />
                </Grid>
                <Grid item xs={6}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={formData.is_popup}
                        onChange={(e) => setFormData({ ...formData, is_popup: e.target.checked })}
                        disabled={dialogMode === 'view'}
                        color="warning"
                      />
                    }
                    label="弹窗显示"
                  />
                </Grid>
              </Grid>
            </Box>
          </Box>
        </DialogContent>
        <DialogActions sx={{ px: 3, py: 2, gap: 1 }}>
          <Button
            onClick={handleCloseDialog}
            variant="outlined"
            size="large"
          >
            {dialogMode === 'view' ? '关闭' : '取消'}
          </Button>
          {dialogMode !== 'view' && (
            <Button
              onClick={handleSubmit}
              variant="contained"
              size="large"
              sx={{ minWidth: 100 }}
            >
              {dialogMode === 'edit' ? '更新公告' : '创建公告'}
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default AnnouncementManagement;