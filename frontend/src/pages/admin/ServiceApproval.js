import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Button,
  IconButton,
  TextField,
  InputAdornment,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Alert,
  Snackbar,
  Grid,
  Avatar,
  Tooltip,
} from '@mui/material';
import {
  Search,
  Visibility,
  CheckCircle,
  Cancel,
  Assignment,
} from '@mui/icons-material';
import apiService from '../../services/api';

function ServiceApproval() {
  const [loading, setLoading] = useState(true);
  const [services, setServices] = useState([]);
  const [filteredServices, setFilteredServices] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [categories, setCategories] = useState([]);

  // 审批对话框状态
  const [approvalDialogOpen, setApprovalDialogOpen] = useState(false);
  const [selectedService, setSelectedService] = useState(null);
  const [approvalForm, setApprovalForm] = useState({
    action: '',
    note: ''
  });

  // 详情对话框状态
  const [detailDialogOpen, setDetailDialogOpen] = useState(false);

  // 提示信息
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success'
  });

  // 加载待审批服务列表
  const fetchPendingServices = async () => {
    try {
      setLoading(true);
      const response = await apiService.getPendingServices({
        page: 1,
        size: 100
      });

      console.log('待审批服务API响应:', response);

      if (response && response.success) {
        const servicesData = Array.isArray(response.data?.items) ? response.data.items : [];
        console.log('解析的服务数据:', servicesData);
        setServices(servicesData);
        setFilteredServices(servicesData);
      } else {
        console.error('获取待审批服务失败:', response?.message);
        setServices([]);
        setFilteredServices([]);
        setSnackbar({
          open: true,
          message: response?.message || '获取待审批服务失败',
          severity: 'error'
        });
      }
    } catch (error) {
      console.error('获取待审批服务异常:', error);
      setServices([]);
      setFilteredServices([]);
      setSnackbar({
        open: true,
        message: '获取待审批服务失败，请重试',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  // 加载分类列表
  const loadCategories = async () => {
    try {
      const response = await apiService.getChannelCategories();
      if (response.success) {
        const categoriesData = Array.isArray(response.data.categories) ? response.data.categories : [];
        setCategories(categoriesData);
      }
    } catch (error) {
      console.error('获取分类失败:', error);
    }
  };

  useEffect(() => {
    fetchPendingServices();
    loadCategories();
  }, []);

  // 搜索和筛选
  useEffect(() => {
    let filtered = services;

    if (searchTerm) {
      filtered = filtered.filter(service =>
        service.service_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        service.service_description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        service.service_code?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (categoryFilter !== 'all') {
      filtered = filtered.filter(service => service.category?.id === categoryFilter);
    }

    setFilteredServices(filtered);
  }, [services, searchTerm, categoryFilter]);

  // 获取状态标签
  const getStatusChip = (status) => {
    const statusMap = {
      pending: { label: '待审批', color: 'warning' },
      approved: { label: '已通过', color: 'success' },
      rejected: { label: '已拒绝', color: 'error' }
    };
    const config = statusMap[status] || { label: status, color: 'default' };
    return <Chip label={config.label} color={config.color} size="small" />;
  };

  // 获取分类名称
  const getCategoryName = (service) => {
    // 后端返回的数据中，分类信息在 category 字段中
    return service.category ? service.category.category_name : '未知分类';
  };

  // 格式化价格
  const formatPrice = (price) => {
    return price ? `¥${parseFloat(price).toFixed(2)}` : '未设置';
  };

  // 格式化时间
  const formatTime = (timeString) => {
    if (!timeString) return '未知时间';
    try {
      return new Date(timeString).toLocaleString('zh-CN');
    } catch (error) {
      return '时间格式错误';
    }
  };

  // 打开审批对话框
  const handleOpenApproval = (service) => {
    setSelectedService(service);
    setApprovalForm({ action: '', note: '' });
    setApprovalDialogOpen(true);
  };

  // 打开详情对话框
  const handleOpenDetail = (service) => {
    setSelectedService(service);
    setDetailDialogOpen(true);
  };

  // 提交审批
  const handleSubmitApproval = async () => {
    if (!approvalForm.action) {
      setSnackbar({
        open: true,
        message: '请选择审批操作',
        severity: 'error'
      });
      return;
    }

    try {
      setLoading(true);

      const approvalData = {
        status: approvalForm.action,
        note: approvalForm.note
      };

      await apiService.approveService(selectedService.id, approvalData);

      setSnackbar({
        open: true,
        message: `服务审批${approvalForm.action === 'approved' ? '通过' : '拒绝'}成功`,
        severity: 'success'
      });

      setApprovalDialogOpen(false);
      setApprovalForm({ action: '', note: '' });

      // 刷新数据
      fetchPendingServices();
    } catch (error) {
      console.error('审批提交失败:', error);
      setSnackbar({
        open: true,
        message: error.message || '审批操作失败，请重试',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  if (loading && services.length === 0) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '400px' }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ width: '100%', p: 3, backgroundColor: '#f5f5f5', minHeight: '100vh' }}>
      {/* 页面标题 */}
      <Box sx={{
        backgroundColor: '#fff',
        borderRadius: 3,
        border: '1px solid #e0e0e0',
        p: 4,
        mb: 3,
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Assignment sx={{ fontSize: 32, color: '#1976d2' }} />
          <Box>
            <Typography variant="h4" sx={{ fontWeight: 700, color: '#1a1a1a', mb: 0.5 }}>
              服务审批
            </Typography>
            <Typography variant="body1" sx={{ color: '#666' }}>
              审批渠道商提交的服务申请
            </Typography>
          </Box>
        </Box>
      </Box>



      {/* 搜索和筛选 */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                placeholder="搜索服务名称、描述或代码..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                slotProps={{
                  input: {
                    startAdornment: (
                      <InputAdornment position="start">
                        <Search />
                      </InputAdornment>
                    ),
                  }
                }}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>分类筛选</InputLabel>
                <Select
                  value={categoryFilter}
                  label="分类筛选"
                  onChange={(e) => setCategoryFilter(e.target.value)}
                >
                  <MenuItem value="all">全部分类</MenuItem>
                  {categories.map((category) => (
                    <MenuItem key={category.id} value={category.id}>
                      {category.category_name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* 服务列表 */}
      <Card>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant="h6" sx={{ fontWeight: 600 }}>
              待审批服务列表 ({filteredServices.length})
            </Typography>
          </Box>

          {filteredServices.length === 0 ? (
            <Box sx={{ textAlign: 'center', py: 4 }}>
              <Assignment sx={{ fontSize: 64, color: '#ccc', mb: 2 }} />
              <Typography variant="h6" color="text.secondary">
                暂无待审批服务
              </Typography>
              <Typography variant="body2" color="text.secondary">
                所有服务都已审批完成
              </Typography>
            </Box>
          ) : (
            <TableContainer component={Paper} variant="outlined">
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>服务名称</TableCell>
                    <TableCell>服务代码</TableCell>
                    <TableCell>服务描述</TableCell>
                    <TableCell>分类</TableCell>
                    <TableCell>价格</TableCell>
                    <TableCell>提交者</TableCell>
                    <TableCell>提交时间</TableCell>
                    <TableCell>状态</TableCell>
                    <TableCell>操作</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {filteredServices.map((service) => (
                    <TableRow
                      key={service.id}
                      hover
                      sx={{
                        '&:hover': {
                          backgroundColor: '#f8fafc',
                          '& .action-buttons': {
                            opacity: 1
                          }
                        },
                        transition: 'all 0.2s ease'
                      }}
                    >
                      <TableCell sx={{ py: 3 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                          <Avatar sx={{
                            bgcolor: '#667eea',
                            width: 40,
                            height: 40,
                            boxShadow: '0 2px 8px rgba(102, 126, 234, 0.3)'
                          }}>
                            <Assignment />
                          </Avatar>
                          <Box>
                            <Typography variant="subtitle1" sx={{ fontWeight: 600, color: '#1e293b' }}>
                              {service.service_name}
                            </Typography>
                          </Box>
                        </Box>
                      </TableCell>
                      <TableCell sx={{ py: 3 }}>
                        <Typography variant="body2" sx={{ fontWeight: 500, color: '#64748b' }}>
                          {service.service_code}
                        </Typography>
                      </TableCell>
                      <TableCell sx={{ py: 3, maxWidth: 250 }}>
                        <Typography
                          variant="body2"
                          color="text.secondary"
                          sx={{
                            display: '-webkit-box',
                            WebkitLineClamp: 2,
                            WebkitBoxOrient: 'vertical',
                            overflow: 'hidden',
                            lineHeight: 1.4
                          }}
                        >
                          {service.service_description || '暂无描述'}
                        </Typography>
                      </TableCell>
                      <TableCell sx={{ py: 3 }}>
                        <Chip
                          label={getCategoryName(service)}
                          variant="outlined"
                          size="small"
                          sx={{
                            backgroundColor: '#f1f5f9',
                            borderColor: '#cbd5e1',
                            fontWeight: 500
                          }}
                        />
                      </TableCell>
                      <TableCell sx={{ py: 3 }}>
                        <Typography variant="body2" sx={{ fontWeight: 600, color: '#059669' }}>
                          {formatPrice(service.base_price)}
                          {service.price_unit && (
                            <Typography component="span" variant="caption" color="text.secondary" sx={{ ml: 0.5 }}>
                              /{service.price_unit}
                            </Typography>
                          )}
                        </Typography>
                        {service.discount_price && (
                          <Typography variant="caption" sx={{ color: '#f59e0b', fontWeight: 500 }}>
                            优惠: {formatPrice(service.discount_price)}
                          </Typography>
                        )}
                      </TableCell>
                      <TableCell sx={{ py: 3 }}>
                        <Box>
                          <Typography variant="body2" sx={{ fontWeight: 500, color: '#374151' }}>
                            {service.creator_info?.provider_name || '未知用户'}
                          </Typography>
                          {service.creator_info?.company_name && (
                            <Typography variant="caption" color="text.secondary">
                              {service.creator_info.company_name}
                            </Typography>
                          )}
                        </Box>
                      </TableCell>
                      <TableCell sx={{ py: 3 }}>
                        <Typography variant="body2" sx={{ fontWeight: 500, color: '#64748b' }}>
                          {formatTime(service.created_at)}
                        </Typography>
                      </TableCell>
                      <TableCell sx={{ py: 3 }}>
                        {getStatusChip(service.approval_status)}
                      </TableCell>
                      <TableCell sx={{ py: 3, textAlign: 'center' }}>
                        <Box
                          className="action-buttons"
                          sx={{
                            display: 'flex',
                            justifyContent: 'center',
                            gap: 1,
                            opacity: 0.7,
                            transition: 'opacity 0.2s ease'
                          }}
                        >
                          <Tooltip title="查看详情">
                            <IconButton
                              size="small"
                              onClick={() => handleOpenDetail(service)}
                              sx={{
                                bgcolor: '#3b82f6',
                                color: 'white',
                                '&:hover': { bgcolor: '#2563eb' },
                                width: 32,
                                height: 32
                              }}
                            >
                              <Visibility fontSize="small" />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="审批服务">
                            <Button
                              size="small"
                              variant="contained"
                              onClick={() => handleOpenApproval(service)}
                              sx={{
                                minWidth: 'auto',
                                px: 1.5,
                                py: 0.5,
                                fontSize: '0.75rem',
                                bgcolor: '#10b981',
                                '&:hover': { bgcolor: '#059669' }
                              }}
                            >
                              审批
                            </Button>
                          </Tooltip>
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </CardContent>
      </Card>

      {/* 审批对话框 */}
      <Dialog
        open={approvalDialogOpen}
        onClose={() => setApprovalDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          <Typography variant="h6" sx={{ fontWeight: 600 }}>
            审批服务申请
          </Typography>
        </DialogTitle>
        <DialogContent>
          {selectedService && (
            <Box sx={{ pt: 1 }}>
              <Typography variant="subtitle2" sx={{ mb: 2, fontWeight: 600 }}>
                服务信息：{selectedService.service_name}
              </Typography>

              <FormControl fullWidth sx={{ mb: 3 }}>
                <InputLabel>审批操作</InputLabel>
                <Select
                  value={approvalForm.action}
                  label="审批操作"
                  onChange={(e) => setApprovalForm(prev => ({ ...prev, action: e.target.value }))}
                >
                  <MenuItem value="approved">
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <CheckCircle color="success" />
                      通过审批
                    </Box>
                  </MenuItem>
                  <MenuItem value="rejected">
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Cancel color="error" />
                      拒绝申请
                    </Box>
                  </MenuItem>
                </Select>
              </FormControl>

              <TextField
                fullWidth
                multiline
                rows={4}
                label="审批备注"
                placeholder="请输入审批意见或备注信息..."
                value={approvalForm.note}
                onChange={(e) => setApprovalForm(prev => ({ ...prev, note: e.target.value }))}
              />
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setApprovalDialogOpen(false)}>
            取消
          </Button>
          <Button
            variant="contained"
            onClick={handleSubmitApproval}
            disabled={!approvalForm.action}
          >
            提交审批
          </Button>
        </DialogActions>
      </Dialog>

      {/* 服务详情对话框 */}
      <Dialog
        open={detailDialogOpen}
        onClose={() => setDetailDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle sx={{
          pb: 2,
          borderBottom: '1px solid #e0e0e0',
          display: 'flex',
          alignItems: 'center',
          gap: 2
        }}>
          <Box sx={{
            width: 40,
            height: 40,
            borderRadius: 2,
            bgcolor: '#1976d2',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            <Visibility sx={{ color: 'white' }} />
          </Box>
          <Box>
            <Typography variant="h6" sx={{ fontWeight: 600 }}>
              服务详情
            </Typography>
            <Typography variant="body2" color="text.secondary">
              查看待审批服务的详细信息
            </Typography>
          </Box>
        </DialogTitle>
        <DialogContent>
          {selectedService && (
            <Box sx={{ pt: 3 }}>
              {/* 基本信息 - 一排显示 */}
              <Box sx={{ mb: 4 }}>
                <Typography variant="h6" sx={{ fontWeight: 600, mb: 3, color: '#1976d2', display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Assignment sx={{ fontSize: 20 }} />
                  基本信息
                </Typography>
                <Grid container spacing={3}>
                  <Grid item xs={12} md={2.4}>
                    <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#f8f9fa', borderRadius: 2 }}>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>服务名称</Typography>
                      <Typography variant="body1" sx={{ fontWeight: 600 }}>{selectedService.service_name}</Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} md={2.4}>
                    <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#f8f9fa', borderRadius: 2 }}>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>服务代码</Typography>
                      <Typography variant="body1" sx={{ fontWeight: 600 }}>{selectedService.service_code}</Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} md={2.4}>
                    <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#f8f9fa', borderRadius: 2 }}>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>服务分类</Typography>
                      <Typography variant="body1" sx={{ fontWeight: 600 }}>{getCategoryName(selectedService)}</Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} md={2.4}>
                    <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#f8f9fa', borderRadius: 2 }}>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>基础价格</Typography>
                      <Typography variant="body1" sx={{ fontWeight: 600, color: '#4caf50' }}>
                        {formatPrice(selectedService.base_price)}
                        {selectedService.price_unit && `/${selectedService.price_unit}`}
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} md={2.4}>
                    <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#f8f9fa', borderRadius: 2 }}>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>审批状态</Typography>
                      {getStatusChip(selectedService.approval_status)}
                    </Box>
                  </Grid>
                </Grid>

                {/* 第二行 - 其他信息 */}
                <Grid container spacing={3} sx={{ mt: 2 }}>
                  {selectedService.discount_price && (
                    <Grid item xs={12} md={2.4}>
                      <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#f8f9fa', borderRadius: 2 }}>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>优惠价格</Typography>
                        <Typography variant="body1" sx={{ fontWeight: 600, color: '#ff9800' }}>
                          {formatPrice(selectedService.discount_price)}
                        </Typography>
                      </Box>
                    </Grid>
                  )}
                  <Grid item xs={12} md={2.4}>
                    <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#f8f9fa', borderRadius: 2 }}>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>交付时间</Typography>
                      <Typography variant="body1" sx={{ fontWeight: 600 }}>{selectedService.delivery_time || 24} 小时</Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} md={2.4}>
                    <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#f8f9fa', borderRadius: 2 }}>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>修改次数</Typography>
                      <Typography variant="body1" sx={{ fontWeight: 600 }}>{selectedService.revision_count || 2} 次</Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} md={2.4}>
                    <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#f8f9fa', borderRadius: 2 }}>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>提交时间</Typography>
                      <Typography variant="body1" sx={{ fontWeight: 600 }}>{formatTime(selectedService.created_at)}</Typography>
                    </Box>
                  </Grid>
                </Grid>
              </Box>

              {/* 服务描述 */}
              <Box sx={{ mb: 4 }}>
                <Typography variant="h6" sx={{ fontWeight: 600, mb: 2, color: '#1976d2' }}>
                  服务描述
                </Typography>
                <Box sx={{ p: 3, bgcolor: '#f8f9fa', borderRadius: 2, border: '1px solid #e0e0e0' }}>
                  <Typography variant="body1" sx={{ lineHeight: 1.6 }}>
                    {selectedService.service_description || '暂无描述'}
                  </Typography>
                </Box>
              </Box>

              {/* 提交者信息 */}
              <Box sx={{ mb: 3 }}>
                <Typography variant="h6" sx={{ fontWeight: 600, mb: 2, color: '#1976d2' }}>
                  提交者信息
                </Typography>
                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#f8f9fa', borderRadius: 2 }}>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>提交者</Typography>
                      <Typography variant="body1" sx={{ fontWeight: 600 }}>
                        {selectedService.creator_info?.provider_name || '未知用户'}
                      </Typography>
                    </Box>
                  </Grid>
                  {selectedService.creator_info?.company_name && (
                    <Grid item xs={12} md={6}>
                      <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#f8f9fa', borderRadius: 2 }}>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>公司名称</Typography>
                        <Typography variant="body1" sx={{ fontWeight: 600 }}>
                          {selectedService.creator_info.company_name}
                        </Typography>
                      </Box>
                    </Grid>
                  )}
                </Grid>
              </Box>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDetailDialogOpen(false)}>
            关闭
          </Button>
        </DialogActions>
      </Dialog>

      {/* 提示信息 */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar(prev => ({ ...prev, open: false }))}
      >
        <Alert
          onClose={() => setSnackbar(prev => ({ ...prev, open: false }))}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
}

export default ServiceApproval;
