import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  IconButton,
  TextField,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Stack,
  Typography,
  Alert,
  Grid,
  InputAdornment,
  Checkbox,
  Tooltip,
  Switch
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
  Visibility as ViewIcon
} from '@mui/icons-material';
import axiosInstance from '../../config/axios-config';
import { ApiConfig } from '../../config/api-config';
import {
  getPlanTypeLabel,
  getUserTypeLabel,
  getPlanTypeOptions,
  getUserTypeOptions,
  formatFeatureDisplay
} from '../../utils/planTypeUtils';

const ProductManagement = () => {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [total, setTotal] = useState(0);
  const [openDialog, setOpenDialog] = useState(false);
  const [dialogMode, setDialogMode] = useState('create'); // create, edit, view
  const [selectedProduct, setSelectedProduct] = useState(null);
  
  // 表单数据
  const [formData, setFormData] = useState({
    name: '',                    // plan_name
    plan_code: '',              // plan_code
    plan_type: '',              // plan_type
    target_user_type: '',       // target_user_type
    pricing: {
      monthly: '',              // monthly_price
      quarterly: '',            // quarterly_price
      semiannual: '',          // semi_annual_price
      annual: ''               // yearly_price
    },
    features: [],               // features
    target_roles: [],           // 用于UI显示，实际使用target_user_type
    is_active: true,            // is_active
    display_order: 0,           // display_order
    quotas: {
      max_content_requests: 0,     // max_content_requests
      max_monitoring_projects: 0,  // max_monitoring_projects
      max_api_calls: 0,           // max_api_calls
      max_team_members: 1,        // max_team_members
      max_service_orders: 0,      // max_service_orders
      max_channels: 1,            // max_channels
      commission_rate: null       // commission_rate
    },
    // 只读字段
    created_at: '',
    updated_at: ''
  });

  // 筛选条件
  const [filters, setFilters] = useState({
    keyword: '',
    role: '',
    status: ''
  });

  // 角色选项 - 使用工具函数获取
  const roleOptions = getUserTypeOptions();

  // 价格期限选项
  const pricingPeriods = [
    { key: 'monthly', label: '月付', suffix: '/月' },
    { key: 'quarterly', label: '季度', suffix: '/季' },
    { key: 'semiannual', label: '半年', suffix: '/半年' },
    { key: 'annual', label: '年付', suffix: '/年' }
  ];

  useEffect(() => {
    fetchProducts();
  }, [page, rowsPerPage, filters]);

  // 获取套餐列表
  const fetchProducts = async () => {
    setLoading(true);
    setError('');
    try {
      const params = new URLSearchParams();

      // 添加筛选参数
      if (filters.role) {
        params.append('target_user_type', filters.role);
      }
      if (filters.status) {
        params.append('active_only', filters.status === 'active');
      }

      const url = `/subscriptions/plans/management?${params.toString()}`;
      const response = await axiosInstance.get(url);

      setProducts(response.data);
    } catch (error) {
      console.error('获取套餐列表失败:', error);
      console.error('错误详情:', error);
      setError(error.response?.data?.detail || error.message || '获取套餐列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取单个套餐详情
  const fetchProductDetail = async (productId) => {
    try {
      const response = await axiosInstance.get(`/subscriptions/plans/management/${productId}`);
      return response.data;
    } catch (error) {
      console.error('获取套餐详情失败:', error);
      setError(error.response?.data?.detail || '获取套餐详情失败');
      return null;
    }
  };

  // 打开对话框
  const handleOpenDialog = async (mode, product = null) => {
    setDialogMode(mode);
    setSelectedProduct(product);

    if (mode === 'edit' && product) {
      // 获取完整的套餐详情
      const productDetail = await fetchProductDetail(product.id);
      if (productDetail) {
        // 正确解析功能特性
        let featuresList = [];
        if (productDetail.features) {
          if (productDetail.features.features && Array.isArray(productDetail.features.features)) {
            featuresList = productDetail.features.features;
          } else if (Array.isArray(productDetail.features)) {
            featuresList = productDetail.features;
          } else if (typeof productDetail.features === 'object') {
            // 处理对象格式的功能特性 {key: value} 或 {feature1: true, feature2: true}
            featuresList = Object.entries(productDetail.features).map(([key, value]) => {
              // 如果值是字符串，使用中文格式化
              if (typeof value === 'string') {
                return formatFeatureDisplay(key, value);
              }
              // 如果值是布尔值且为true，只返回key的中文
              return value ? formatFeatureDisplay(key, '') : null;
            }).filter(Boolean);
          }
        }

        setFormData({
          name: productDetail.plan_name,
          plan_code: productDetail.plan_code || '',
          plan_type: productDetail.plan_type || '',
          target_user_type: productDetail.target_user_type || '',
          pricing: {
            monthly: productDetail.monthly_price || '',
            quarterly: productDetail.quarterly_price || '',
            semiannual: productDetail.semi_annual_price || '',
            annual: productDetail.yearly_price || ''
          },
          features: featuresList,
          target_roles: productDetail.target_user_type ? [productDetail.target_user_type] : [],
          is_active: productDetail.is_active,
          display_order: productDetail.display_order || 0,
          quotas: {
            max_content_requests: productDetail.max_content_requests || 0,
            max_monitoring_projects: productDetail.max_monitoring_projects || 0,
            max_api_calls: productDetail.max_api_calls || 0,
            max_team_members: productDetail.max_team_members || 1,
            max_service_orders: productDetail.max_service_orders || 0,
            max_channels: productDetail.max_channels || 1,
            commission_rate: productDetail.commission_rate || null
          },
          // 只读字段
          created_at: productDetail.created_at || '',
          updated_at: productDetail.updated_at || ''
        });
      } else {
        // 如果获取详情失败，使用列表中的数据作为备选
        setFormData({
          name: product.plan_name,
          description: product.plan_type || '',
          pricing: {
            monthly: product.monthly_price || '',
            quarterly: product.quarterly_price || '',
            semiannual: product.semi_annual_price || '',
            annual: product.yearly_price || ''
          },
          features: product.features ?
            (typeof product.features === 'object' ?
              Object.entries(product.features).map(([key, value]) => {
                if (typeof value === 'string') {
                  return formatFeatureDisplay(key, value);
                }
                return value ? formatFeatureDisplay(key, '') : null;
              }).filter(Boolean) :
              []) : [],
          target_roles: product.target_user_type ? [product.target_user_type] : [],
          is_active: product.is_active
        });
      }
    } else if (mode === 'view' && product) {
      // 查看模式也获取完整详情
      const productDetail = await fetchProductDetail(product.id);
      if (productDetail) {
        // 正确解析功能特性
        let featuresList = [];
        if (productDetail.features) {
          if (productDetail.features.features && Array.isArray(productDetail.features.features)) {
            featuresList = productDetail.features.features;
          } else if (Array.isArray(productDetail.features)) {
            featuresList = productDetail.features;
          } else if (typeof productDetail.features === 'object') {
            // 处理对象格式的功能特性 {key: value} 或 {feature1: true, feature2: true}
            featuresList = Object.entries(productDetail.features).map(([key, value]) => {
              // 如果值是字符串，使用中文格式化
              if (typeof value === 'string') {
                return formatFeatureDisplay(key, value);
              }
              // 如果值是布尔值且为true，只返回key的中文
              return value ? formatFeatureDisplay(key, '') : null;
            }).filter(Boolean);
          }
        }

        setFormData({
          name: productDetail.plan_name,
          plan_code: productDetail.plan_code || '',
          plan_type: productDetail.plan_type || '',
          target_user_type: productDetail.target_user_type || '',
          pricing: {
            monthly: productDetail.monthly_price || '',
            quarterly: productDetail.quarterly_price || '',
            semiannual: productDetail.semi_annual_price || '',
            annual: productDetail.yearly_price || ''
          },
          features: featuresList,
          target_roles: productDetail.target_user_type ? [productDetail.target_user_type] : [],
          is_active: productDetail.is_active,
          display_order: productDetail.display_order || 0,
          quotas: {
            max_content_requests: productDetail.max_content_requests || 0,
            max_monitoring_projects: productDetail.max_monitoring_projects || 0,
            max_api_calls: productDetail.max_api_calls || 0,
            max_team_members: productDetail.max_team_members || 1,
            max_service_orders: productDetail.max_service_orders || 0,
            max_channels: productDetail.max_channels || 1,
            commission_rate: productDetail.commission_rate || null
          },
          // 只读字段
          created_at: productDetail.created_at || '',
          updated_at: productDetail.updated_at || ''
        });
      }
    } else if (mode === 'create') {
      setFormData({
        name: '',
        description: '',
        pricing: {
          monthly: '',
          quarterly: '',
          semiannual: '',
          annual: ''
        },
        features: [],
        target_roles: [],
        is_active: true,
        quotas: {
          max_content_requests: 0,
          max_monitoring_projects: 0,
          max_api_calls: 0,
          max_team_members: 1,
          max_service_orders: 0,
          max_channels: 1,
          commission_rate: null
        },
        plan_code: '',
        display_order: 0,
        created_at: '',
        updated_at: ''
      });
    }

    setOpenDialog(true);
  };

  // 关闭对话框
  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedProduct(null);
    setFormData({
      name: '',                    // plan_name
      plan_code: '',              // plan_code
      plan_type: '',              // plan_type
      target_user_type: '',       // target_user_type
      pricing: {
        monthly: '',              // monthly_price
        quarterly: '',            // quarterly_price
        semiannual: '',          // semi_annual_price
        annual: ''               // yearly_price
      },
      features: [],               // features
      target_roles: [],           // 用于UI显示，实际使用target_user_type
      is_active: true,            // is_active
      display_order: 0,           // display_order
      quotas: {
        max_content_requests: 0,     // max_content_requests
        max_monitoring_projects: 0,  // max_monitoring_projects
        max_api_calls: 0,           // max_api_calls
        max_team_members: 1,        // max_team_members
        max_service_orders: 0,      // max_service_orders
        max_channels: 1,            // max_channels
        commission_rate: null       // commission_rate
      }
    });
  };

  // 提交表单
  const handleSubmit = async () => {
    setError('');
    setSuccess('');

    // 表单验证
    if (!formData.name || !formData.pricing.monthly) {
      setError('请填写套餐名称和月付价格');
      return;
    }

    if (!formData.plan_code) {
      setError('请填写套餐代码');
      return;
    }

    if (!formData.plan_type) {
      setError('请选择套餐类型');
      return;
    }

    try {
      const requestData = {
        plan_code: formData.plan_code || formData.name.toLowerCase().replace(/\s+/g, '_'),
        plan_name: formData.name,
        plan_type: formData.plan_type || 'basic',
        target_user_type: formData.target_user_type || formData.target_roles[0] || 'both',
        monthly_price: parseFloat(formData.pricing.monthly),
        quarterly_price: formData.pricing.quarterly ? parseFloat(formData.pricing.quarterly) : null,
        semi_annual_price: formData.pricing.semiannual ? parseFloat(formData.pricing.semiannual) : null,
        yearly_price: formData.pricing.annual ? parseFloat(formData.pricing.annual) : null,
        max_content_requests: formData.quotas?.max_content_requests || 0,
        max_monitoring_projects: formData.quotas?.max_monitoring_projects || 0,
        max_api_calls: formData.quotas?.max_api_calls || 0,
        max_team_members: formData.quotas?.max_team_members || 1,
        max_service_orders: formData.quotas?.max_service_orders || 0,
        max_channels: formData.quotas?.max_channels || 1,
        commission_rate: formData.quotas?.commission_rate || null,
        features: formData.features.reduce((acc, feature) => {
          acc[feature] = true;
          return acc;
        }, {}),
        is_active: formData.is_active,
        display_order: formData.display_order || 0
      };

      if (dialogMode === 'create') {
        await axiosInstance.post('/subscriptions/plans/management', requestData);
        setSuccess('套餐创建成功');
      } else if (dialogMode === 'edit') {
        await axiosInstance.put(`/subscriptions/plans/management/${selectedProduct.id}`, requestData);
        setSuccess('套餐更新成功');
      }

      handleCloseDialog();
      fetchProducts();
    } catch (error) {
      console.error('操作失败:', error);
      setError(error.response?.data?.detail || '操作失败');
    }
  };

  // 删除套餐
  const handleDelete = async (id) => {
    if (!window.confirm('确定要删除这个套餐吗？删除后无法恢复。')) {
      return;
    }

    setError('');
    setSuccess('');

    try {
      await axiosInstance.delete(`/subscriptions/plans/management/${id}`);
      setSuccess('套餐删除成功');
      fetchProducts();
    } catch (error) {
      console.error('删除失败:', error);
      setError(error.response?.data?.detail || '删除失败');
    }
  };



  // 添加新功能特性
  const [newFeature, setNewFeature] = useState('');
  const handleAddFeature = () => {
    if (newFeature && !formData.features.includes(newFeature)) {
      setFormData(prev => ({
        ...prev,
        features: [...prev.features, newFeature]
      }));
      setNewFeature('');
    }
  };



  // 筛选处理
  const handleFilterChange = (field, value) => {
    setFilters(prev => ({
      ...prev,
      [field]: value
    }));
    setPage(0);
  };





  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        套餐管理
      </Typography>

      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        管理不同角色的订阅套餐，设置价格、功能和订阅时长
      </Typography>



      {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}
      {success && <Alert severity="success" sx={{ mb: 2 }}>{success}</Alert>}

      {/* 操作栏 */}
      <Paper sx={{ mb: 2, p: 2 }}>
        {/* 筛选区域和操作按钮 */}
        <Stack direction="row" spacing={2} alignItems="center" justifyContent="space-between" flexWrap="wrap">
          <Stack direction="row" spacing={2} alignItems="center" flexWrap="wrap" sx={{ flex: 1 }}>
            <TextField
              size="small"
              label="搜索产品"
              value={filters.keyword}
              onChange={(e) => handleFilterChange('keyword', e.target.value)}
              sx={{ minWidth: 200 }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
            />
            <FormControl size="small" sx={{ minWidth: 120 }}>
              <InputLabel shrink={true} sx={{ backgroundColor: 'white', px: 1 }}>目标角色</InputLabel>
              <Select
                value={filters.role}
                onChange={(e) => handleFilterChange('role', e.target.value)}
                notched={true}
                displayEmpty
              >
                <MenuItem value="">全部</MenuItem>
                {roleOptions.map(role => (
                  <MenuItem key={role.value} value={role.value}>
                    {role.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            <FormControl size="small" sx={{ minWidth: 100 }}>
              <InputLabel shrink={true} sx={{ backgroundColor: 'white', px: 1 }}>状态</InputLabel>
              <Select
                value={filters.status}
                onChange={(e) => handleFilterChange('status', e.target.value)}
                notched={true}
                displayEmpty
              >
                <MenuItem value="">全部</MenuItem>
                <MenuItem value="active">激活</MenuItem>
                <MenuItem value="inactive">未激活</MenuItem>
              </Select>
            </FormControl>

          </Stack>

          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => handleOpenDialog('create')}
            sx={{ minWidth: 120 }}
          >
            添加套餐
          </Button>
        </Stack>
      </Paper>

      {/* 产品列表 */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>套餐名称</TableCell>
              <TableCell>套餐类型</TableCell>
              <TableCell>月付价格</TableCell>
              <TableCell>其他价格</TableCell>
              <TableCell>目标用户</TableCell>
              <TableCell>创建时间</TableCell>
              <TableCell>状态</TableCell>
              <TableCell>操作</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {products.map((product) => (
              <TableRow key={product.id}>
                <TableCell>
                  <Typography variant="subtitle2" fontWeight="bold">
                    {product.plan_name}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="body2" sx={{ maxWidth: 200 }}>
                    {getPlanTypeLabel(product.plan_type)}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="body1" fontWeight="bold" color="primary">
                    ¥{product.monthly_price || '-'}/月
                  </Typography>
                </TableCell>
                <TableCell>
                  <Stack spacing={0.5}>
                    {product.quarterly_price && (
                      <Typography variant="caption">
                        季度: ¥{product.quarterly_price}
                      </Typography>
                    )}
                    {product.semi_annual_price && (
                      <Typography variant="caption">
                        半年: ¥{product.semi_annual_price}
                      </Typography>
                    )}
                    {product.yearly_price && (
                      <Typography variant="caption">
                        年付: ¥{product.yearly_price}
                      </Typography>
                    )}
                  </Stack>
                </TableCell>
                <TableCell>
                  <Chip
                    label={getUserTypeLabel(product.target_user_type)}
                    size="small"
                    color="primary"
                    variant="outlined"
                  />
                </TableCell>
                <TableCell>
                  <Typography variant="body2" color="text.secondary">
                    {product.created_at ? new Date(product.created_at).toLocaleDateString() : '-'}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Chip
                    label={product.is_active ? '激活' : '未激活'}
                    size="small"
                    color={product.is_active ? 'success' : 'default'}
                  />
                </TableCell>
                <TableCell>
                  <Stack direction="row" spacing={1}>
                    <Tooltip title="查看">
                      <IconButton size="small" onClick={() => handleOpenDialog('view', product)}>
                        <ViewIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="编辑">
                      <IconButton size="small" onClick={() => handleOpenDialog('edit', product)}>
                        <EditIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="删除">
                      <IconButton size="small" color="error" onClick={() => handleDelete(product.id)}>
                        <DeleteIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </Stack>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>

      </TableContainer>

      {/* 添加/编辑对话框 */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="lg" fullWidth>
        <DialogTitle sx={{
          bgcolor: dialogMode === 'view' ? 'info.main' : dialogMode === 'edit' ? 'warning.main' : 'primary.main',
          color: 'white',
          display: 'flex',
          alignItems: 'center',
          gap: 1
        }}>
          {dialogMode === 'view' && <ViewIcon />}
          {dialogMode === 'edit' && <EditIcon />}
          {dialogMode === 'create' && <AddIcon />}
          {dialogMode === 'view' ? '查看套餐详情' : dialogMode === 'edit' ? '编辑套餐信息' : '添加新套餐'}
        </DialogTitle>
        <DialogContent sx={{ p: 0 }}>
          <Box>
            {/* 基本信息区域 */}
            <Box sx={{ p: 3, bgcolor: 'white' }}>
              <Typography variant="h6" sx={{ mb: 3, color: 'primary.main', display: 'flex', alignItems: 'center', gap: 1 }}>
                📝 基本信息
              </Typography>
              {/* 第一行：套餐名称和套餐代码 */}
              <Box sx={{ display: 'flex', gap: 3, mb: 3 }}>
                <Box sx={{ width: '50%' }}>
                  <TextField
                    fullWidth
                    label="套餐名称"
                    value={formData.name}
                    onChange={(e) => {
                      const newName = e.target.value;
                      setFormData(prev => ({
                        ...prev,
                        name: newName,
                        // 如果是创建模式且套餐代码为空，自动生成建议的套餐代码
                        plan_code: dialogMode === 'create' && !prev.plan_code
                          ? newName.toLowerCase().replace(/[^a-z0-9]/g, '_').replace(/_+/g, '_').replace(/^_|_$/g, '')
                          : prev.plan_code
                      }));
                    }}
                    disabled={dialogMode === 'view'}
                    required
                    variant="outlined"
                    placeholder="例如：企业基础版"
                    helperText="套餐的显示名称"
                  />
                </Box>
                <Box sx={{ width: '50%' }}>
                  <TextField
                    fullWidth
                    label="套餐代码"
                    value={formData.plan_code || ''}
                    onChange={(e) => setFormData({ ...formData, plan_code: e.target.value })}
                    disabled={dialogMode === 'view'}
                    required
                    variant="outlined"
                    placeholder="例如：basic, pro, enterprise"
                    helperText={dialogMode === 'create' ? "套餐的唯一标识符（会根据套餐名称自动生成）" : "套餐的唯一标识符"}
                  />
                </Box>
              </Box>

              {/* 第二行：套餐类型和激活状态 */}
              <Box sx={{ display: 'flex', gap: 3, mb: 3, alignItems: 'flex-start' }}>
                <Box sx={{ width: '50%' }}>
                  <FormControl fullWidth variant="outlined">
                    <InputLabel>套餐类型</InputLabel>
                    <Select
                      value={formData.plan_type || ''}
                      onChange={(e) => setFormData({ ...formData, plan_type: e.target.value })}
                      disabled={dialogMode === 'view'}
                      label="套餐类型"
                      required
                    >
                      {getPlanTypeOptions().map(({ value, label }) => (
                        <MenuItem key={value} value={value}>
                          {label}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Box>
                <Box sx={{ width: '50%', display: 'flex', alignItems: 'center', gap: 2, mt: 1 }}>
                  <Typography variant="body2" sx={{ color: 'text.secondary', minWidth: 80 }}>
                    激活状态：
                  </Typography>
                  <Switch
                    checked={formData.is_active}
                    onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
                    disabled={dialogMode === 'view'}
                    color="primary"
                  />
                  <Chip
                    label={formData.is_active ? '已激活' : '未激活'}
                    color={formData.is_active ? 'success' : 'default'}
                    size="small"
                  />
                </Box>
              </Box>
            </Box>

            {/* 价格设置区域 */}
            <Box sx={{ p: 3, bgcolor: 'grey.50', borderTop: 1, borderBottom: 1, borderColor: 'divider' }}>
              <Typography variant="h6" sx={{ mb: 2, color: 'primary.main', display: 'flex', alignItems: 'center', gap: 1 }}>
                💰 价格设置
              </Typography>
              <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                {/* 月付价格 */}
                <Box sx={{ flex: 1, minWidth: 200 }}>
                  <Paper sx={{ p: 1.5, textAlign: 'center', bgcolor: 'white', height: '100%' }}>
                    <Typography variant="body2" color="primary" sx={{ mb: 1, fontWeight: 600 }}>
                      月付价格
                    </Typography>
                    <TextField
                      size="small"
                      type="number"
                      value={formData.pricing.monthly}
                      onChange={(e) => setFormData({
                        ...formData,
                        pricing: { ...formData.pricing, monthly: e.target.value }
                      })}
                      disabled={dialogMode === 'view'}
                      required
                      InputProps={{
                        startAdornment: <InputAdornment position="start">¥</InputAdornment>,
                        endAdornment: <InputAdornment position="end">/月</InputAdornment>,
                      }}
                      sx={{ width: '100%' }}
                    />
                    <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5, display: 'block' }}>
                      基础价格
                    </Typography>
                  </Paper>
                </Box>

                {/* 季度价格 */}
                <Box sx={{ flex: 1, minWidth: 200 }}>
                  <Paper sx={{ p: 1.5, textAlign: 'center', bgcolor: 'white', height: '100%' }}>
                    <Typography variant="body2" color="primary" sx={{ mb: 1, fontWeight: 600 }}>
                      季度价格
                    </Typography>
                    <TextField
                      size="small"
                      type="number"
                      value={formData.pricing.quarterly}
                      onChange={(e) => setFormData({
                        ...formData,
                        pricing: { ...formData.pricing, quarterly: e.target.value }
                      })}
                      disabled={dialogMode === 'view'}
                      InputProps={{
                        startAdornment: <InputAdornment position="start">¥</InputAdornment>,
                        endAdornment: <InputAdornment position="end">/季</InputAdornment>,
                      }}
                      sx={{ width: '100%' }}
                    />
                    {formData.pricing.quarterly && formData.pricing.monthly && (
                      <Chip
                        label={`省${((1 - formData.pricing.quarterly / (formData.pricing.monthly * 3)) * 100).toFixed(0)}%`}
                        color="success"
                        size="small"
                        sx={{ mt: 0.5, fontSize: '0.7rem' }}
                      />
                    )}
                  </Paper>
                </Box>

                {/* 半年价格 */}
                <Box sx={{ flex: 1, minWidth: 200 }}>
                  <Paper sx={{ p: 1.5, textAlign: 'center', bgcolor: 'white', height: '100%' }}>
                    <Typography variant="body2" color="primary" sx={{ mb: 1, fontWeight: 600 }}>
                      半年价格
                    </Typography>
                    <TextField
                      size="small"
                      type="number"
                      value={formData.pricing.semiannual}
                      onChange={(e) => setFormData({
                        ...formData,
                        pricing: { ...formData.pricing, semiannual: e.target.value }
                      })}
                      disabled={dialogMode === 'view'}
                      InputProps={{
                        startAdornment: <InputAdornment position="start">¥</InputAdornment>,
                        endAdornment: <InputAdornment position="end">/半年</InputAdornment>,
                      }}
                      sx={{ width: '100%' }}
                    />
                    {formData.pricing.semiannual && formData.pricing.monthly && (
                      <Chip
                        label={`省${((1 - formData.pricing.semiannual / (formData.pricing.monthly * 6)) * 100).toFixed(0)}%`}
                        color="success"
                        size="small"
                        sx={{ mt: 0.5, fontSize: '0.7rem' }}
                      />
                    )}
                  </Paper>
                </Box>

                {/* 年付价格 */}
                <Box sx={{ flex: 1, minWidth: 200 }}>
                  <Paper sx={{ p: 1.5, textAlign: 'center', bgcolor: 'white', height: '100%' }}>
                    <Typography variant="body2" color="primary" sx={{ mb: 1, fontWeight: 600 }}>
                      年付价格
                    </Typography>
                    <TextField
                      size="small"
                      type="number"
                      value={formData.pricing.annual}
                      onChange={(e) => setFormData({
                        ...formData,
                        pricing: { ...formData.pricing, annual: e.target.value }
                      })}
                      disabled={dialogMode === 'view'}
                      InputProps={{
                        startAdornment: <InputAdornment position="start">¥</InputAdornment>,
                        endAdornment: <InputAdornment position="end">/年</InputAdornment>,
                      }}
                      sx={{ width: '100%' }}
                    />
                    {formData.pricing.annual && formData.pricing.monthly && (
                      <Chip
                        label={`省${((1 - formData.pricing.annual / (formData.pricing.monthly * 12)) * 100).toFixed(0)}%`}
                        color="success"
                        size="small"
                        sx={{ mt: 0.5, fontSize: '0.7rem' }}
                      />
                    )}
                  </Paper>
                </Box>
              </Box>
            </Box>

            {/* 目标用户类型选择区域 */}
            <Box sx={{ p: 3, bgcolor: 'white' }}>
              <Typography variant="h6" sx={{ mb: 3, color: 'primary.main', display: 'flex', alignItems: 'center', gap: 1 }}>
                👥 目标用户类型
              </Typography>
              <FormControl fullWidth disabled={dialogMode === 'view'}>
                <InputLabel>选择目标用户类型</InputLabel>
                <Select
                  value={formData.target_user_type || formData.target_roles[0] || ''}
                  onChange={(e) => setFormData({
                    ...formData,
                    target_user_type: e.target.value,
                    target_roles: e.target.value ? [e.target.value] : []
                  })}
                  label="选择目标用户类型"
                >
                  <MenuItem value="">请选择</MenuItem>
                  {roleOptions.map(role => (
                    <MenuItem key={role.value} value={role.value}>
                      {role.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              {/* 显示顺序 */}
              <Box sx={{ mt: 3 }}>
                <TextField
                  fullWidth
                  label="显示顺序"
                  type="number"
                  value={formData.display_order || 0}
                  onChange={(e) => setFormData({ ...formData, display_order: parseInt(e.target.value) || 0 })}
                  disabled={dialogMode === 'view'}
                  variant="outlined"
                  helperText="数字越小显示越靠前"
                  inputProps={{ min: 0 }}
                />
              </Box>
            </Box>

            {/* 功能特性区域 */}
            <Box sx={{ p: 3, bgcolor: 'grey.50', borderTop: 1, borderColor: 'divider' }}>
              <Typography variant="h6" sx={{ mb: 3, color: 'primary.main', display: 'flex', alignItems: 'center', gap: 1 }}>
                ⚡ 功能特性
              </Typography>

              {/* 功能特性列表 */}
              <Box sx={{ mb: 3 }}>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  产品包含的功能特性
                </Typography>
                {formData.features.length > 0 ? (
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                    {formData.features.map((feature, index) => (
                      <Chip
                        key={index}
                        label={feature}
                        onDelete={dialogMode !== 'view' ? () => {
                          setFormData({
                            ...formData,
                            features: formData.features.filter(f => f !== feature)
                          });
                        } : undefined}
                        variant="outlined"
                        sx={{
                          bgcolor: 'rgba(25, 118, 210, 0.08)',
                          color: '#1976d2',
                          borderColor: '#1976d2',
                          fontWeight: 500,
                          fontSize: '0.875rem',
                          '&:hover': {
                            bgcolor: 'rgba(25, 118, 210, 0.12)'
                          },
                          '& .MuiChip-deleteIcon': {
                            color: 'error.main',
                            '&:hover': {
                              color: 'error.dark'
                            }
                          }
                        }}
                      />
                    ))}
                  </Box>
                ) : (
                  <Paper sx={{ p: 3, textAlign: 'center', bgcolor: 'white' }}>
                    <Typography variant="body2" color="text.secondary" sx={{ fontStyle: 'italic' }}>
                      暂无功能特性，请添加产品功能
                    </Typography>
                  </Paper>
                )}
              </Box>

              {/* 添加功能特性 */}
              {dialogMode !== 'view' && (
                <Paper sx={{ p: 3, bgcolor: 'white' }}>
                  <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600 }}>
                    添加新功能
                  </Typography>
                  <Stack direction="row" spacing={2} alignItems="center">
                    <TextField
                      placeholder="输入功能特性名称"
                      value={newFeature}
                      onChange={(e) => setNewFeature(e.target.value)}
                      onKeyPress={(e) => {
                        if (e.key === 'Enter') {
                          handleAddFeature();
                        }
                      }}
                      sx={{ flex: 1 }}
                      variant="outlined"
                    />
                    <Button
                      variant="contained"
                      onClick={handleAddFeature}
                      disabled={!newFeature.trim()}
                      startIcon={<AddIcon />}
                      sx={{ minWidth: 100 }}
                    >
                      添加
                    </Button>
                  </Stack>
                </Paper>
              )}
            </Box>

            {/* 配额信息区域 */}
            <Box sx={{ p: 3, bgcolor: 'white', borderTop: 1, borderColor: 'divider' }}>
              <Typography variant="h6" sx={{ mb: 3, color: 'primary.main', display: 'flex', alignItems: 'center', gap: 1 }}>
                📊 配额限制
              </Typography>

              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 4 }}>
                {/* 企业用户配额 */}
                <Box>
                  <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600, color: 'text.primary' }}>
                    企业用户配额
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      {dialogMode === 'view' ? (
                        <Box sx={{
                          p: 2.5,
                          bgcolor: 'rgba(25, 118, 210, 0.04)',
                          borderRadius: 2,
                          border: '1px solid rgba(25, 118, 210, 0.12)',
                          height: '100%'
                        }}>
                          <Typography variant="caption" sx={{
                            color: '#666',
                            fontSize: '0.75rem',
                            display: 'block',
                            mb: 0.5
                          }}>
                            内容请求配额
                          </Typography>
                          <Typography variant="h6" sx={{
                            color: '#1976d2',
                            fontWeight: 700,
                            fontSize: '1.25rem'
                          }}>
                            {formData.quotas?.max_content_requests || 0} 次/月
                          </Typography>
                        </Box>
                      ) : (
                        <TextField
                          fullWidth
                          label="内容请求配额"
                          type="number"
                          value={formData.quotas?.max_content_requests || 0}
                          onChange={(e) => setFormData({
                            ...formData,
                            quotas: {
                              ...formData.quotas,
                              max_content_requests: parseInt(e.target.value) || 0
                            }
                          })}
                          variant="outlined"
                          helperText="每月可请求的内容生成次数"
                          inputProps={{ min: 0 }}
                        />
                      )}
                    </Grid>
                    <Grid item xs={6}>
                      {dialogMode === 'view' ? (
                        <Box sx={{
                          p: 2.5,
                          bgcolor: 'rgba(25, 118, 210, 0.04)',
                          borderRadius: 2,
                          border: '1px solid rgba(25, 118, 210, 0.12)',
                          height: '100%'
                        }}>
                          <Typography variant="caption" sx={{
                            color: '#666',
                            fontSize: '0.75rem',
                            display: 'block',
                            mb: 0.5
                          }}>
                            监控项目配额
                          </Typography>
                          <Typography variant="h6" sx={{
                            color: '#1976d2',
                            fontWeight: 700,
                            fontSize: '1.25rem'
                          }}>
                            {formData.quotas?.max_monitoring_projects || 0} 个
                          </Typography>
                        </Box>
                      ) : (
                        <TextField
                          fullWidth
                          label="监控项目配额"
                          type="number"
                          value={formData.quotas?.max_monitoring_projects || 0}
                          onChange={(e) => setFormData({
                            ...formData,
                            quotas: {
                              ...formData.quotas,
                              max_monitoring_projects: parseInt(e.target.value) || 0
                            }
                          })}
                          variant="outlined"
                          helperText="可创建的监控项目数量"
                          inputProps={{ min: 0 }}
                        />
                      )}
                    </Grid>
                    <Grid item xs={6}>
                      {dialogMode === 'view' ? (
                        <Box sx={{
                          p: 2.5,
                          bgcolor: 'rgba(25, 118, 210, 0.04)',
                          borderRadius: 2,
                          border: '1px solid rgba(25, 118, 210, 0.12)',
                          height: '100%'
                        }}>
                          <Typography variant="caption" sx={{
                            color: '#666',
                            fontSize: '0.75rem',
                            display: 'block',
                            mb: 0.5
                          }}>
                            API调用配额
                          </Typography>
                          <Typography variant="h6" sx={{
                            color: '#1976d2',
                            fontWeight: 700,
                            fontSize: '1.25rem'
                          }}>
                            {formData.quotas?.max_api_calls || 0} 次/月
                          </Typography>
                        </Box>
                      ) : (
                        <TextField
                          fullWidth
                          label="API调用配额"
                          type="number"
                          value={formData.quotas?.max_api_calls || 0}
                          onChange={(e) => setFormData({
                            ...formData,
                            quotas: {
                              ...formData.quotas,
                              max_api_calls: parseInt(e.target.value) || 0
                            }
                          })}
                          variant="outlined"
                          helperText="每月可调用API的次数"
                          inputProps={{ min: 0 }}
                        />
                      )}
                    </Grid>
                    <Grid item xs={6}>
                      {dialogMode === 'view' ? (
                        <Box sx={{
                          p: 2.5,
                          bgcolor: 'rgba(25, 118, 210, 0.04)',
                          borderRadius: 2,
                          border: '1px solid rgba(25, 118, 210, 0.12)',
                          height: '100%'
                        }}>
                          <Typography variant="caption" sx={{
                            color: '#666',
                            fontSize: '0.75rem',
                            display: 'block',
                            mb: 0.5
                          }}>
                            团队成员配额
                          </Typography>
                          <Typography variant="h6" sx={{
                            color: '#1976d2',
                            fontWeight: 700,
                            fontSize: '1.25rem'
                          }}>
                            {formData.quotas?.max_team_members || 1} 人
                          </Typography>
                        </Box>
                      ) : (
                        <TextField
                          fullWidth
                          label="团队成员配额"
                          type="number"
                          value={formData.quotas?.max_team_members || 1}
                          onChange={(e) => setFormData({
                            ...formData,
                            quotas: {
                              ...formData.quotas,
                              max_team_members: parseInt(e.target.value) || 1
                            }
                          })}
                          variant="outlined"
                          helperText="可邀请的团队成员数量"
                          inputProps={{ min: 1 }}
                        />
                      )}
                    </Grid>
                  </Grid>
                </Box>

                {/* 服务商配额 */}
                <Box>
                  <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600, color: 'text.primary' }}>
                    服务商配额
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={4}>
                      {dialogMode === 'view' ? (
                        <Box sx={{
                          p: 2.5,
                          bgcolor: 'rgba(156, 39, 176, 0.04)',
                          borderRadius: 2,
                          border: '1px solid rgba(156, 39, 176, 0.12)',
                          height: '100%'
                        }}>
                          <Typography variant="caption" sx={{
                            color: '#666',
                            fontSize: '0.75rem',
                            display: 'block',
                            mb: 0.5
                          }}>
                            服务订单配额
                          </Typography>
                          <Typography variant="h6" sx={{
                            color: '#9c27b0',
                            fontWeight: 700,
                            fontSize: '1.25rem'
                          }}>
                            {formData.quotas?.max_service_orders || 0} 个/月
                          </Typography>
                        </Box>
                      ) : (
                        <TextField
                          fullWidth
                          label="服务订单配额"
                          type="number"
                          value={formData.quotas?.max_service_orders || 0}
                          onChange={(e) => setFormData({
                            ...formData,
                            quotas: {
                              ...formData.quotas,
                              max_service_orders: parseInt(e.target.value) || 0
                            }
                          })}
                          variant="outlined"
                          helperText="每月可接受的服务订单数量"
                          inputProps={{ min: 0 }}
                        />
                      )}
                    </Grid>
                    <Grid item xs={4}>
                      {dialogMode === 'view' ? (
                        <Box sx={{
                          p: 2.5,
                          bgcolor: 'rgba(156, 39, 176, 0.04)',
                          borderRadius: 2,
                          border: '1px solid rgba(156, 39, 176, 0.12)',
                          height: '100%'
                        }}>
                          <Typography variant="caption" sx={{
                            color: '#666',
                            fontSize: '0.75rem',
                            display: 'block',
                            mb: 0.5
                          }}>
                            渠道配额
                          </Typography>
                          <Typography variant="h6" sx={{
                            color: '#9c27b0',
                            fontWeight: 700,
                            fontSize: '1.25rem'
                          }}>
                            {formData.quotas?.max_channels || 1} 个
                          </Typography>
                        </Box>
                      ) : (
                        <TextField
                          fullWidth
                          label="渠道配额"
                          type="number"
                          value={formData.quotas?.max_channels || 1}
                          onChange={(e) => setFormData({
                            ...formData,
                            quotas: {
                              ...formData.quotas,
                              max_channels: parseInt(e.target.value) || 1
                            }
                          })}
                          variant="outlined"
                          helperText="可创建的渠道数量"
                          inputProps={{ min: 1 }}
                        />
                      )}
                    </Grid>
                    <Grid item xs={4}>
                      {dialogMode === 'view' ? (
                        <Box sx={{
                          p: 2.5,
                          bgcolor: 'rgba(156, 39, 176, 0.04)',
                          borderRadius: 2,
                          border: '1px solid rgba(156, 39, 176, 0.12)',
                          height: '100%'
                        }}>
                          <Typography variant="caption" sx={{
                            color: '#666',
                            fontSize: '0.75rem',
                            display: 'block',
                            mb: 0.5
                          }}>
                            佣金比例
                          </Typography>
                          <Typography variant="h6" sx={{
                            color: formData.quotas?.commission_rate ? '#4caf50' : '#9e9e9e',
                            fontWeight: 700,
                            fontSize: '1.25rem'
                          }}>
                            {formData.quotas?.commission_rate ? `${formData.quotas.commission_rate}%` : '未设置'}
                          </Typography>
                        </Box>
                      ) : (
                        <TextField
                          fullWidth
                          label="佣金比例"
                          type="number"
                          value={formData.quotas?.commission_rate || ''}
                          onChange={(e) => setFormData({
                            ...formData,
                            quotas: {
                              ...formData.quotas,
                              commission_rate: e.target.value ? parseFloat(e.target.value) : null
                            }
                          })}
                          variant="outlined"
                          helperText="佣金比例（%），可为空"
                          inputProps={{ min: 0, max: 100, step: 0.1 }}
                        />
                      )}
                    </Grid>
                  </Grid>
                </Box>
              </Box>
            </Box>

            {/* 套餐基本信息区域 */}
            {dialogMode === 'view' && (
              <Box sx={{ p: 3, bgcolor: 'grey.50', borderTop: 1, borderColor: 'divider' }}>
                <Typography variant="h6" sx={{ mb: 3, color: 'primary.main', display: 'flex', alignItems: 'center', gap: 1 }}>
                  ℹ️ 套餐信息
                </Typography>

                <Grid container spacing={2}>
                  <Grid item xs={3}>
                    <Box sx={{
                      p: 2.5,
                      bgcolor: 'rgba(76, 175, 80, 0.04)',
                      borderRadius: 2,
                      border: '1px solid rgba(76, 175, 80, 0.12)',
                      height: '100%'
                    }}>
                      <Typography variant="caption" sx={{
                        color: '#666',
                        fontSize: '0.75rem',
                        display: 'block',
                        mb: 0.5
                      }}>
                        套餐代码
                      </Typography>
                      <Typography variant="h6" sx={{
                        color: '#2e7d32',
                        fontWeight: 700,
                        fontSize: '1.25rem'
                      }}>
                        {formData.plan_code || '未设置'}
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={3}>
                    <Box sx={{
                      p: 2.5,
                      bgcolor: 'rgba(76, 175, 80, 0.04)',
                      borderRadius: 2,
                      border: '1px solid rgba(76, 175, 80, 0.12)',
                      height: '100%'
                    }}>
                      <Typography variant="caption" sx={{
                        color: '#666',
                        fontSize: '0.75rem',
                        display: 'block',
                        mb: 0.5
                      }}>
                        显示顺序
                      </Typography>
                      <Typography variant="h6" sx={{
                        color: '#2e7d32',
                        fontWeight: 700,
                        fontSize: '1.25rem'
                      }}>
                        {formData.display_order || 0}
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={3}>
                    <Box sx={{
                      p: 2.5,
                      bgcolor: 'rgba(76, 175, 80, 0.04)',
                      borderRadius: 2,
                      border: '1px solid rgba(76, 175, 80, 0.12)',
                      height: '100%'
                    }}>
                      <Typography variant="caption" sx={{
                        color: '#666',
                        fontSize: '0.75rem',
                        display: 'block',
                        mb: 0.5
                      }}>
                        创建时间
                      </Typography>
                      <Typography variant="body1" sx={{
                        color: '#2e7d32',
                        fontWeight: 600,
                        fontSize: '1rem'
                      }}>
                        {formData.created_at ? new Date(formData.created_at).toLocaleString('zh-CN') : '未知'}
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={3}>
                    <Box sx={{
                      p: 2.5,
                      bgcolor: 'rgba(76, 175, 80, 0.04)',
                      borderRadius: 2,
                      border: '1px solid rgba(76, 175, 80, 0.12)',
                      height: '100%'
                    }}>
                      <Typography variant="caption" sx={{
                        color: '#666',
                        fontSize: '0.75rem',
                        display: 'block',
                        mb: 0.5
                      }}>
                        更新时间
                      </Typography>
                      <Typography variant="body1" sx={{
                        color: '#2e7d32',
                        fontWeight: 600,
                        fontSize: '1rem'
                      }}>
                        {formData.updated_at ? new Date(formData.updated_at).toLocaleString('zh-CN') : '未知'}
                      </Typography>
                    </Box>
                  </Grid>
                </Grid>
              </Box>
            )}
          </Box>
        </DialogContent>
        <DialogActions sx={{ p: 3, bgcolor: 'grey.50', borderTop: 1, borderColor: 'divider' }}>
          <Stack direction="row" spacing={2} sx={{ width: '100%', justifyContent: 'flex-end' }}>
            <Button
              onClick={handleCloseDialog}
              variant="outlined"
              size="large"
              sx={{ minWidth: 100 }}
            >
              {dialogMode === 'view' ? '关闭' : '取消'}
            </Button>
            {dialogMode !== 'view' && (
              <Button
                onClick={handleSubmit}
                variant="contained"
                size="large"
                sx={{
                  minWidth: 120,
                  bgcolor: dialogMode === 'edit' ? 'warning.main' : 'primary.main',
                  '&:hover': {
                    bgcolor: dialogMode === 'edit' ? 'warning.dark' : 'primary.dark'
                  }
                }}
                startIcon={dialogMode === 'edit' ? <EditIcon /> : <AddIcon />}
              >
                {dialogMode === 'edit' ? '保存修改' : '创建套餐'}
              </Button>
            )}
          </Stack>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ProductManagement;