import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  CardActions,
  Paper,
  Button,
  IconButton,
  Badge,
  Tabs,
  Tab,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Checkbox,
  Toolbar,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  CircularProgress,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemAvatar,
  ListItemSecondaryAction,
  Divider,
  InputAdornment,
  Stack,
  Switch,
  FormControlLabel,
  LinearProgress,
  Avatar,
  AvatarGroup,
  Rating,
  Stepper,
  Step,
  StepLabel,
  StepContent,

  Accordion,
  AccordionSummary,
  AccordionDetails,
  SpeedDial,
  SpeedDialAction,
  Drawer,
  FormGroup,
  FormHelperText,
} from '@mui/material';
import {
  Assignment,
  AssignmentTurnedIn,
  AssignmentLate,
  AssignmentInd,
  Inventory,
  LocalShipping,
  RateReview,
  TrendingUp,
  AccessTime,
  CheckCircle,
  Cancel,
  Warning,
  Error,
  Info,
  Refresh,
  Search,
  FilterList,
  Download,
  Upload,
  Add,
  Edit,
  Delete,
  Visibility,
  Close,
  Send,
  AttachFile,
  Person,
  Group,
  Star,
  StarBorder,
  ThumbUp,
  ThumbDown,
  ExpandMore,
  ChevronRight,
  AssignmentReturn,
  AssignmentReturned,
  ContentPaste,
  Create,
  Publish,
  Description,
  Article,
  WorkHistory,
  Payment,
  MonetizationOn,
  PendingActions,
  Loop,
  Done,
  Block,
  Schedule,
  CalendarToday,
  BarChart,
  ShowChart,
  Speed,
  HighQuality,
  Verified,
  VerifiedUser,
  ManageAccounts,
  SupportAgent,
  Engineering,
  BatchPrediction,
  FileDownload,
  FileUpload,
  Assessment,
  Analytics,
} from '@mui/icons-material';
import {
  PieChart, Pie, Cell, ResponsiveContainer, Tooltip as RechartsTooltip, Legend,
  LineChart, Line, XAxis, YAxis, CartesianGrid,
  BarChart as RechartsBarChart, Bar,
  RadialBarChart, RadialBar,
} from 'recharts';
import { format, formatDistanceToNow, parseISO, addDays, isAfter, isBefore } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { useSnackbar } from 'notistack';
import { contentService } from '../../services/contentService';

// Tab panel component
function TabPanel({ children, value, index, ...other }) {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`content-tabpanel-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

// Status chip component
const StatusChip = ({ status, size = 'small' }) => {
  const statusConfig = {
    pending: { label: '待处理', color: 'warning', icon: <PendingActions /> },
    accepted: { label: '已接受', color: 'info', icon: <AssignmentInd /> },
    in_progress: { label: '进行中', color: 'primary', icon: <Loop /> },
    delivered: { label: '已交付', color: 'secondary', icon: <LocalShipping /> },
    reviewing: { label: '审核中', color: 'warning', icon: <RateReview /> },
    completed: { label: '已完成', color: 'success', icon: <CheckCircle /> },
    rejected: { label: '已拒绝', color: 'error', icon: <Cancel /> },
    cancelled: { label: '已取消', color: 'default', icon: <Block /> },
  };

  const config = statusConfig[status] || { label: status, color: 'default', icon: <Info /> };

  return (
    <Chip
      icon={config.icon}
      label={config.label}
      color={config.color}
      size={size}
      variant="outlined"
    />
  );
};

// Progress indicator component
const ProgressIndicator = ({ progress }) => {
  const getColor = () => {
    if (progress >= 80) return 'success';
    if (progress >= 50) return 'primary';
    if (progress >= 30) return 'warning';
    return 'error';
  };

  return (
    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
      <Box sx={{ flex: 1 }}>
        <LinearProgress
          variant="determinate"
          value={progress}
          color={getColor()}
          sx={{ height: 8, borderRadius: 4 }}
        />
      </Box>
      <Typography variant="body2" color="text.secondary">
        {progress}%
      </Typography>
    </Box>
  );
};

const ContentServiceManagement = () => {
  const { enqueueSnackbar } = useSnackbar();

  // State management
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState(0);
  const [statistics, setStatistics] = useState(null);
  const [requirements, setRequirements] = useState([]);
  const [tasks, setTasks] = useState([]);
  const [selectedItems, setSelectedItems] = useState([]);
  const [detailPanelOpen, setDetailPanelOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  // Filter states
  const [requirementFilters, setRequirementFilters] = useState({
    status: '',
    type: '',
    priority: '',
    client: '',
    tags: [],
    date_from: null,
    date_to: null,
  });

  const [taskFilters, setTaskFilters] = useState({
    status: '',
    priority: '',
    assignee: '',
    progress: '',
  });

  // Pagination
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);

  // Dialog states
  const [createDialog, setCreateDialog] = useState(false);
  const [batchDeleteDialog, setBatchDeleteDialog] = useState(false);
  const [exportDialog, setExportDialog] = useState(false);

  // Form states
  const [editFormData, setEditFormData] = useState({
    id: '',
    title: '',
    description: '',
    type: '',
    priority: 'medium',
    status: 'pending',
    client: '',
    budget: '',
    deadline: null,
    tags: [],
    assignee: '',
    progress: 0,
  });

  // Load initial data
  useEffect(() => {
    loadStatistics();
    loadRequirements();
    loadTasks();
  }, []);

  // Auto-refresh
  useEffect(() => {
    const interval = setInterval(() => {
      if (activeTab === 0) {
        loadRequirements();
      } else if (activeTab === 1) {
        loadTasks();
      }
    }, 30000); // Refresh every 30 seconds

    return () => clearInterval(interval);
  }, [activeTab]);

  // Data loading functions
  const loadStatistics = async () => {
    try {
      // 模拟统计数据
      const mockStats = {
        total_requirements: 156,
        pending_requirements: 23,
        in_progress_tasks: 45,
        completed_tasks: 88,
        monthly_total: 156,
        completion_rate: 0.85,
        avg_delivery_time: '3.2天',
        acceptance_rate: 0.92,
      };
      setStatistics(mockStats);
    } catch (error) {
      console.error('Failed to load statistics:', error);
    }
  };

  const loadRequirements = async () => {
    setLoading(true);
    try {
      // 模拟需求数据 - 还没进入任务流程的需求
      const mockRequirements = [
        {
          id: 1,
          title: 'AI技术在金融领域的应用分析',
          description: '需要深度分析AI技术在金融科技领域的应用现状和发展趋势',
          type: 'research',
          priority: 'high',
          status: 'pending',
          client: '金融科技公司',
          budget: 3000,
          deadline: '2024-02-15',
          created_at: '2024-01-20T10:30:00Z',
          tags: ['AI', '金融', '技术分析'],
          word_count: 5000,
        },
        {
          id: 2,
          title: '企业数字化转型白皮书',
          description: '编写企业数字化转型的完整指南和最佳实践',
          type: 'content_creation',
          priority: 'medium',
          status: 'draft',
          client: '咨询公司',
          budget: 2500,
          deadline: '2024-02-20',
          created_at: '2024-01-21T14:20:00Z',
          tags: ['数字化', '转型', '企业'],
          word_count: 8000,
        },
        {
          id: 3,
          title: '区块链技术发展报告',
          description: '分析区块链技术的最新发展动态和市场应用',
          type: 'research',
          priority: 'low',
          status: 'pending',
          client: '区块链公司',
          budget: 2000,
          deadline: '2024-02-25',
          created_at: '2024-01-22T09:15:00Z',
          tags: ['区块链', '技术', '报告'],
          word_count: 4000,
        },
      ];
      setRequirements(mockRequirements);
      setTotalCount(mockRequirements.length);
    } catch (error) {
      enqueueSnackbar('加载需求失败', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const loadTasks = async () => {
    setLoading(true);
    try {
      // 模拟任务数据 - 已经进入任务流程的
      const mockTasks = [
        {
          id: 4,
          title: 'SEO优化策略指南',
          description: '编写完整的SEO优化策略和实施指南',
          type: 'content_creation',
          priority: 'high',
          status: 'in_progress',
          client: '数字营销公司',
          budget: 1800,
          deadline: '2024-02-10',
          created_at: '2024-01-15T11:00:00Z',
          started_at: '2024-01-18T09:00:00Z',
          assignee: '张三',
          assignee_id: 'user_001',
          progress: 75,
          tags: ['SEO', '营销', '策略'],
          word_count: 3500,
        },
        {
          id: 5,
          title: '人工智能发展趋势报告',
          description: '分析2024年人工智能技术发展趋势和应用前景',
          type: 'research',
          priority: 'medium',
          status: 'completed',
          client: 'AI科技公司',
          budget: 2200,
          deadline: '2024-01-30',
          created_at: '2024-01-10T14:30:00Z',
          started_at: '2024-01-12T10:00:00Z',
          completed_at: '2024-01-28T16:30:00Z',
          assignee: '李四',
          assignee_id: 'user_002',
          progress: 100,
          tags: ['AI', '趋势', '报告'],
          word_count: 6000,
        },
        {
          id: 6,
          title: '电商平台运营手册',
          description: '编写电商平台的完整运营指南和最佳实践',
          type: 'content_creation',
          priority: 'medium',
          status: 'review',
          client: '电商公司',
          budget: 2800,
          deadline: '2024-02-05',
          created_at: '2024-01-12T16:20:00Z',
          started_at: '2024-01-15T09:30:00Z',
          delivered_at: '2024-02-03T14:00:00Z',
          assignee: '王五',
          assignee_id: 'user_003',
          progress: 100,
          tags: ['电商', '运营', '手册'],
          word_count: 4500,
        },
      ];
      setTasks(mockTasks);
    } catch (error) {
      enqueueSnackbar('加载任务失败', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  // Action handlers
  const handleCreateItem = () => {
    setEditFormData({
      id: '',
      title: '',
      description: '',
      type: activeTab === 0 ? 'research' : 'content_creation',
      priority: 'medium',
      status: activeTab === 0 ? 'pending' : 'in_progress',
      client: '',
      budget: '',
      deadline: null,
      tags: [],
      assignee: '',
      progress: 0,
    });
    setCreateDialog(true);
  };

  const handleEditItem = (item) => {
    setEditFormData({
      id: item.id,
      title: item.title,
      description: item.description,
      type: item.type,
      priority: item.priority,
      status: item.status,
      client: item.client,
      budget: item.budget,
      deadline: item.deadline,
      tags: item.tags || [],
      assignee: item.assignee || '',
      progress: item.progress || 0,
    });
    setEditDialogOpen(true);
  };

  const handleSaveItem = async () => {
    try {
      if (editFormData.id) {
        // 更新现有项目
        if (activeTab === 0) {
          setRequirements(prev => prev.map(req =>
            req.id === editFormData.id ? { ...req, ...editFormData } : req
          ));
        } else {
          setTasks(prev => prev.map(task =>
            task.id === editFormData.id ? { ...task, ...editFormData } : task
          ));
        }
        enqueueSnackbar('更新成功', { variant: 'success' });
      } else {
        // 创建新项目
        const newItem = {
          ...editFormData,
          id: Date.now(),
          created_at: new Date().toISOString(),
        };
        if (activeTab === 0) {
          setRequirements(prev => [newItem, ...prev]);
        } else {
          setTasks(prev => [newItem, ...prev]);
        }
        enqueueSnackbar('创建成功', { variant: 'success' });
      }
      setEditDialogOpen(false);
      setCreateDialog(false);
    } catch (error) {
      enqueueSnackbar('保存失败', { variant: 'error' });
    }
  };

  const handleDeleteItem = async (itemId) => {
    try {
      if (activeTab === 0) {
        setRequirements(prev => prev.filter(req => req.id !== itemId));
      } else {
        setTasks(prev => prev.filter(task => task.id !== itemId));
      }
      enqueueSnackbar('删除成功', { variant: 'success' });
      setDeleteDialogOpen(false);
    } catch (error) {
      enqueueSnackbar('删除失败', { variant: 'error' });
    }
  };

  const handleBatchDelete = async () => {
    try {
      if (activeTab === 0) {
        setRequirements(prev => prev.filter(req => !selectedItems.includes(req.id)));
      } else {
        setTasks(prev => prev.filter(task => !selectedItems.includes(task.id)));
      }
      enqueueSnackbar(`成功删除 ${selectedItems.length} 项`, { variant: 'success' });
      setSelectedItems([]);
      setBatchDeleteDialog(false);
    } catch (error) {
      enqueueSnackbar('批量删除失败', { variant: 'error' });
    }
  };

  const handleExportReport = () => {
    enqueueSnackbar('报告导出功能开发中', { variant: 'info' });
  };

  const handleSelectAll = (event) => {
    if (event.target.checked) {
      const currentData = activeTab === 0 ? requirements : tasks;
      const allIds = currentData.map(item => item.id);
      setSelectedItems(allIds);
    } else {
      setSelectedItems([]);
    }
  };

  const handleSelectItem = (itemId) => {
    setSelectedItems(prev => {
      if (prev.includes(itemId)) {
        return prev.filter(id => id !== itemId);
      }
      return [...prev, itemId];
    });
  };

  // Calculate metrics
  const calculateMetrics = useMemo(() => {
    if (!statistics) return {};

    const total = statistics.monthly_total || 0;
    const completed = statistics.completed_tasks || 0;
    const completionRate = total > 0 ? (completed / total * 100).toFixed(1) : 0;

    return {
      completionRate,
      avgDeliveryTime: statistics.avg_delivery_time || '3.2天',
      acceptanceRate: (statistics.acceptance_rate * 100).toFixed(1) || 92,
    };
  }, [statistics]);

  // Render statistics cards
  const renderStatisticsCards = () => (
    <Grid container spacing={3} sx={{ mb: 3 }}>
      <Grid item xs={12} md={3}>
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Box>
                <Typography color="text.secondary" gutterBottom>
                  总需求
                </Typography>
                <Typography variant="h4">
                  {statistics?.total_requirements || 0}
                </Typography>
                <Typography variant="body2" color="info.main">
                  待处理 {statistics?.pending_requirements || 0}
                </Typography>
              </Box>
              <Avatar sx={{ bgcolor: 'info.light' }}>
                <Assignment />
              </Avatar>
            </Box>
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12} md={3}>
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Box>
                <Typography color="text.secondary" gutterBottom>
                  进行中任务
                </Typography>
                <Typography variant="h4">
                  {statistics?.in_progress_tasks || 0}
                </Typography>
                <Typography variant="body2" color="primary.main">
                  平均进度 65%
                </Typography>
              </Box>
              <Avatar sx={{ bgcolor: 'primary.light' }}>
                <Loop />
              </Avatar>
            </Box>
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12} md={3}>
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Box>
                <Typography color="text.secondary" gutterBottom>
                  已完成任务
                </Typography>
                <Typography variant="h4">
                  {statistics?.completed_tasks || 0}
                </Typography>
                <Typography variant="body2" color="success.main">
                  完成率 {calculateMetrics.completionRate}%
                </Typography>
              </Box>
              <Avatar sx={{ bgcolor: 'success.light' }}>
                <CheckCircle />
              </Avatar>
            </Box>
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12} md={3}>
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Box>
                <Typography color="text.secondary" gutterBottom>
                  平均交付
                </Typography>
                <Typography variant="h4">
                  {calculateMetrics.avgDeliveryTime}
                </Typography>
                <Typography variant="body2" color="warning.main">
                  验收率 {calculateMetrics.acceptanceRate}%
                </Typography>
              </Box>
              <Avatar sx={{ bgcolor: 'warning.light' }}>
                <AccessTime />
              </Avatar>
            </Box>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );

  // Render requirement management tab
  const renderRequirementManagement = () => (
    <Box>
      {/* Filters and Actions */}
      <Paper sx={{ p: 2, mb: 2 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={2}>
            <FormControl fullWidth size="small">
              <InputLabel>状态</InputLabel>
              <Select
                value={requirementFilters.status}
                onChange={(e) => setRequirementFilters(prev => ({ ...prev, status: e.target.value }))}
                label="状态"
              >
                <MenuItem value="">全部</MenuItem>
                <MenuItem value="pending">待处理</MenuItem>
                <MenuItem value="draft">草稿</MenuItem>
                <MenuItem value="review">审核中</MenuItem>
                <MenuItem value="approved">已批准</MenuItem>
                <MenuItem value="rejected">已拒绝</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={2}>
            <FormControl fullWidth size="small">
              <InputLabel>类型</InputLabel>
              <Select
                value={requirementFilters.type}
                onChange={(e) => setRequirementFilters(prev => ({ ...prev, type: e.target.value }))}
                label="类型"
              >
                <MenuItem value="">全部</MenuItem>
                <MenuItem value="research">研究分析</MenuItem>
                <MenuItem value="content_creation">内容创作</MenuItem>
                <MenuItem value="translation">翻译服务</MenuItem>
                <MenuItem value="editing">编辑校对</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={2}>
            <FormControl fullWidth size="small">
              <InputLabel>优先级</InputLabel>
              <Select
                value={requirementFilters.priority}
                onChange={(e) => setRequirementFilters(prev => ({ ...prev, priority: e.target.value }))}
                label="优先级"
              >
                <MenuItem value="">全部</MenuItem>
                <MenuItem value="high">高优先级</MenuItem>
                <MenuItem value="medium">中优先级</MenuItem>
                <MenuItem value="low">低优先级</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={3}>
            <TextField
              fullWidth
              size="small"
              placeholder="搜索标题、客户..."
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Search />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>
          <Grid item xs={12} md={3}>
            <Stack direction="row" spacing={1}>
              <Button
                variant="contained"
                startIcon={<Add />}
                onClick={handleCreateItem}
              >
                新建需求
              </Button>
              <Button
                variant="outlined"
                startIcon={<Delete />}
                onClick={() => setBatchDeleteDialog(true)}
                disabled={selectedItems.length === 0}
                color="error"
              >
                批量删除
              </Button>
            </Stack>
          </Grid>
        </Grid>
      </Paper>

      {/* Requirements list */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell padding="checkbox">
                <Checkbox
                  indeterminate={selectedItems.length > 0 && selectedItems.length < requirements.length}
                  checked={requirements.length > 0 && selectedItems.length === requirements.length}
                  onChange={handleSelectAll}
                />
              </TableCell>
              <TableCell>需求标题</TableCell>
              <TableCell>客户</TableCell>
              <TableCell>类型</TableCell>
              <TableCell>优先级</TableCell>
              <TableCell>状态</TableCell>
              <TableCell>预算</TableCell>
              <TableCell>截止日期</TableCell>
              <TableCell align="center">操作</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {requirements.map((requirement) => (
              <TableRow
                key={requirement.id}
                hover
                selected={selectedItem?.id === requirement.id}
              >
                <TableCell padding="checkbox">
                  <Checkbox
                    checked={selectedItems.includes(requirement.id)}
                    onChange={() => handleSelectItem(requirement.id)}
                  />
                </TableCell>
                <TableCell>
                  <Box>
                    <Typography variant="body2" sx={{ fontWeight: 500 }}>
                      {requirement.title}
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 0.5, mt: 0.5 }}>
                      {requirement.tags?.slice(0, 3).map((tag, index) => (
                        <Chip key={index} label={tag} size="small" variant="outlined" />
                      ))}
                    </Box>
                    <Typography variant="caption" color="text.secondary">
                      {requirement.word_count} 字
                    </Typography>
                  </Box>
                </TableCell>
                <TableCell>
                  <Typography variant="body2" sx={{ fontWeight: 500 }}>
                    {requirement.client}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Chip
                    icon={requirement.type === 'research' ? <Assessment /> : <Create />}
                    label={requirement.type === 'research' ? '研究分析' : '内容创作'}
                    size="small"
                    variant="outlined"
                  />
                </TableCell>
                <TableCell>
                  <Chip
                    label={requirement.priority === 'high' ? '高' : requirement.priority === 'medium' ? '中' : '低'}
                    color={requirement.priority === 'high' ? 'error' : requirement.priority === 'medium' ? 'warning' : 'info'}
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  <StatusChip status={requirement.status} />
                </TableCell>
                <TableCell>
                  <Typography variant="body2" sx={{ fontWeight: 600, color: 'success.main' }}>
                    ¥{requirement.budget}
                  </Typography>
                </TableCell>
                <TableCell>
                  {requirement.deadline ? (
                    <Box>
                      <Typography variant="body2">
                        {format(parseISO(requirement.deadline), 'MM-dd')}
                      </Typography>
                      <Typography variant="caption" color={
                        isAfter(new Date(), parseISO(requirement.deadline)) ? 'error' : 'text.secondary'
                      }>
                        {formatDistanceToNow(parseISO(requirement.deadline), {
                          addSuffix: true,
                          locale: zhCN
                        })}
                      </Typography>
                    </Box>
                  ) : '-'}
                </TableCell>
                <TableCell align="center">
                  <Stack direction="row" spacing={1}>
                    <IconButton
                      size="small"
                      onClick={() => {
                        setSelectedItem(requirement);
                        setDetailPanelOpen(true);
                      }}
                    >
                      <Visibility />
                    </IconButton>
                    <IconButton
                      size="small"
                      onClick={() => handleEditItem(requirement)}
                    >
                      <Edit />
                    </IconButton>
                    <IconButton
                      size="small"
                      color="error"
                      onClick={() => {
                        setSelectedItem(requirement);
                        setDeleteDialogOpen(true);
                      }}
                    >
                      <Delete />
                    </IconButton>
                  </Stack>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
        <TablePagination
          rowsPerPageOptions={[5, 10, 25]}
          component="div"
          count={requirements.length}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={(e, newPage) => setPage(newPage)}
          onRowsPerPageChange={(e) => {
            setRowsPerPage(parseInt(e.target.value, 10));
            setPage(0);
          }}
        />
      </TableContainer>


    </Box>
  );

  // Render task management tab (已进入任务流程的)
  const renderTaskManagement = () => (
    <Box>
      {/* Filters and Actions */}
      <Paper sx={{ p: 2, mb: 2 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={2}>
            <FormControl fullWidth size="small">
              <InputLabel>状态</InputLabel>
              <Select
                value={taskFilters.status}
                onChange={(e) => setTaskFilters(prev => ({ ...prev, status: e.target.value }))}
                label="状态"
              >
                <MenuItem value="">全部</MenuItem>
                <MenuItem value="in_progress">进行中</MenuItem>
                <MenuItem value="review">待审核</MenuItem>
                <MenuItem value="completed">已完成</MenuItem>
                <MenuItem value="cancelled">已取消</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={2}>
            <FormControl fullWidth size="small">
              <InputLabel>优先级</InputLabel>
              <Select
                value={taskFilters.priority}
                onChange={(e) => setTaskFilters(prev => ({ ...prev, priority: e.target.value }))}
                label="优先级"
              >
                <MenuItem value="">全部</MenuItem>
                <MenuItem value="high">高优先级</MenuItem>
                <MenuItem value="medium">中优先级</MenuItem>
                <MenuItem value="low">低优先级</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={2}>
            <FormControl fullWidth size="small">
              <InputLabel>执行人</InputLabel>
              <Select
                value={taskFilters.assignee}
                onChange={(e) => setTaskFilters(prev => ({ ...prev, assignee: e.target.value }))}
                label="执行人"
              >
                <MenuItem value="">全部</MenuItem>
                <MenuItem value="张三">张三</MenuItem>
                <MenuItem value="李四">李四</MenuItem>
                <MenuItem value="王五">王五</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={3}>
            <TextField
              fullWidth
              size="small"
              placeholder="搜索任务标题..."
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Search />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>
          <Grid item xs={12} md={3}>
            <Stack direction="row" spacing={1}>
              <Button
                variant="contained"
                startIcon={<Add />}
                onClick={handleCreateItem}
              >
                新建任务
              </Button>
              <Button
                variant="outlined"
                startIcon={<Delete />}
                onClick={() => setBatchDeleteDialog(true)}
                disabled={selectedItems.length === 0}
                color="error"
              >
                批量删除
              </Button>
            </Stack>
          </Grid>
        </Grid>
      </Paper>

      {/* Tasks list */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell padding="checkbox">
                <Checkbox
                  indeterminate={selectedItems.length > 0 && selectedItems.length < tasks.length}
                  checked={tasks.length > 0 && selectedItems.length === tasks.length}
                  onChange={handleSelectAll}
                />
              </TableCell>
              <TableCell>任务标题</TableCell>
              <TableCell>客户</TableCell>
              <TableCell>执行人</TableCell>
              <TableCell>优先级</TableCell>
              <TableCell>状态</TableCell>
              <TableCell>进度</TableCell>
              <TableCell>预算</TableCell>
              <TableCell>截止日期</TableCell>
              <TableCell align="center">操作</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {tasks.map((task) => (
              <TableRow
                key={task.id}
                hover
                selected={selectedItem?.id === task.id}
              >
                <TableCell padding="checkbox">
                  <Checkbox
                    checked={selectedItems.includes(task.id)}
                    onChange={() => handleSelectItem(task.id)}
                  />
                </TableCell>
                <TableCell>
                  <Box>
                    <Typography variant="body2" sx={{ fontWeight: 500 }}>
                      {task.title}
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 0.5, mt: 0.5 }}>
                      {task.tags?.slice(0, 3).map((tag, index) => (
                        <Chip key={index} label={tag} size="small" variant="outlined" />
                      ))}
                    </Box>
                    <Typography variant="caption" color="text.secondary">
                      {task.word_count} 字
                    </Typography>
                  </Box>
                </TableCell>
                <TableCell>
                  <Typography variant="body2" sx={{ fontWeight: 500 }}>
                    {task.client}
                  </Typography>
                </TableCell>
                <TableCell>
                  {task.assignee ? (
                    <Chip
                      avatar={<Avatar>{task.assignee[0]}</Avatar>}
                      label={task.assignee}
                      size="small"
                    />
                  ) : (
                    <Typography variant="body2" color="text.secondary">
                      未分配
                    </Typography>
                  )}
                </TableCell>
                <TableCell>
                  <Chip
                    label={task.priority === 'high' ? '高' : task.priority === 'medium' ? '中' : '低'}
                    color={task.priority === 'high' ? 'error' : task.priority === 'medium' ? 'warning' : 'info'}
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  <StatusChip status={task.status} />
                </TableCell>
                <TableCell>
                  <ProgressIndicator progress={task.progress || 0} />
                </TableCell>
                <TableCell>
                  <Typography variant="body2" sx={{ fontWeight: 600, color: 'success.main' }}>
                    ¥{task.budget}
                  </Typography>
                </TableCell>
                <TableCell>
                  {task.deadline ? (
                    <Box>
                      <Typography variant="body2">
                        {format(parseISO(task.deadline), 'MM-dd')}
                      </Typography>
                      <Typography variant="caption" color={
                        isAfter(new Date(), parseISO(task.deadline)) ? 'error' : 'text.secondary'
                      }>
                        {formatDistanceToNow(parseISO(task.deadline), {
                          addSuffix: true,
                          locale: zhCN
                        })}
                      </Typography>
                    </Box>
                  ) : '-'}
                </TableCell>
                <TableCell align="center">
                  <Stack direction="row" spacing={1}>
                    <IconButton
                      size="small"
                      onClick={() => {
                        setSelectedItem(task);
                        setDetailPanelOpen(true);
                      }}
                    >
                      <Visibility />
                    </IconButton>
                    <IconButton
                      size="small"
                      onClick={() => handleEditItem(task)}
                    >
                      <Edit />
                    </IconButton>
                    <IconButton
                      size="small"
                      color="error"
                      onClick={() => {
                        setSelectedItem(task);
                        setDeleteDialogOpen(true);
                      }}
                    >
                      <Delete />
                    </IconButton>
                  </Stack>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
        <TablePagination
          rowsPerPageOptions={[5, 10, 25]}
          component="div"
          count={tasks.length}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={(e, newPage) => setPage(newPage)}
          onRowsPerPageChange={(e) => {
            setRowsPerPage(parseInt(e.target.value, 10));
            setPage(0);
          }}
        />
      </TableContainer>

    </Box>
  );



  return (
    <Container maxWidth={false} sx={{ py: 3 }}>
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" gutterBottom>
          内容服务管理
        </Typography>
        <Typography variant="body1" color="text.secondary">
          管理所有内容需求和任务流程，包括需求创建、编辑、删除和任务跟踪
        </Typography>
      </Box>

      {/* Statistics cards */}
      {renderStatisticsCards()}

      {/* Main content */}
      <Paper sx={{ mb: 2 }}>
        <Tabs
          value={activeTab}
          onChange={(e, newValue) => setActiveTab(newValue)}
          variant="scrollable"
          scrollButtons="auto"
        >
          <Tab icon={<Assignment />} label={`需求管理 (${requirements.length})`} />
          <Tab icon={<LocalShipping />} label={`交付管理 (${tasks.length})`} />
        </Tabs>
      </Paper>

      <Paper>
        <TabPanel value={activeTab} index={0}>
          {renderRequirementManagement()}
        </TabPanel>
        <TabPanel value={activeTab} index={1}>
          {renderTaskManagement()}
        </TabPanel>
      </Paper>

      {/* Batch operations toolbar */}
      {selectedItems.length > 0 && (
        <Paper
          sx={{
            position: 'fixed',
            bottom: 16,
            left: '50%',
            transform: 'translateX(-50%)',
            p: 2,
            zIndex: 1000,
          }}
          elevation={8}
        >
          <Stack direction="row" spacing={2} alignItems="center">
            <Typography variant="body2">
              已选择 {selectedItems.length} 项
            </Typography>
            <Button
              variant="contained"
              color="error"
              startIcon={<Delete />}
              onClick={() => setBatchDeleteDialog(true)}
            >
              批量删除
            </Button>
            <Button
              variant="outlined"
              startIcon={<Cancel />}
              onClick={() => setSelectedItems([])}
            >
              取消选择
            </Button>
          </Stack>
        </Paper>
      )}

      {/* Create/Edit dialog */}
      <Dialog
        open={createDialog || editDialogOpen}
        onClose={() => {
          setCreateDialog(false);
          setEditDialogOpen(false);
        }}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          {editFormData.id ? '编辑' : '新建'}{activeTab === 0 ? '需求' : '任务'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="标题"
                value={editFormData.title}
                onChange={(e) => setEditFormData(prev => ({ ...prev, title: e.target.value }))}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={3}
                label="描述"
                value={editFormData.description}
                onChange={(e) => setEditFormData(prev => ({ ...prev, description: e.target.value }))}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>类型</InputLabel>
                <Select
                  value={editFormData.type}
                  onChange={(e) => setEditFormData(prev => ({ ...prev, type: e.target.value }))}
                  label="类型"
                >
                  <MenuItem value="research">研究分析</MenuItem>
                  <MenuItem value="content_creation">内容创作</MenuItem>
                  <MenuItem value="translation">翻译服务</MenuItem>
                  <MenuItem value="editing">编辑校对</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>优先级</InputLabel>
                <Select
                  value={editFormData.priority}
                  onChange={(e) => setEditFormData(prev => ({ ...prev, priority: e.target.value }))}
                  label="优先级"
                >
                  <MenuItem value="high">高优先级</MenuItem>
                  <MenuItem value="medium">中优先级</MenuItem>
                  <MenuItem value="low">低优先级</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="客户"
                value={editFormData.client}
                onChange={(e) => setEditFormData(prev => ({ ...prev, client: e.target.value }))}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="预算"
                type="number"
                value={editFormData.budget}
                onChange={(e) => setEditFormData(prev => ({ ...prev, budget: e.target.value }))}
              />
            </Grid>
            {activeTab === 1 && (
              <>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="执行人"
                    value={editFormData.assignee}
                    onChange={(e) => setEditFormData(prev => ({ ...prev, assignee: e.target.value }))}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="进度"
                    type="number"
                    inputProps={{ min: 0, max: 100 }}
                    value={editFormData.progress}
                    onChange={(e) => setEditFormData(prev => ({ ...prev, progress: parseInt(e.target.value) }))}
                  />
                </Grid>
              </>
            )}
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => {
            setCreateDialog(false);
            setEditDialogOpen(false);
          }}>
            取消
          </Button>
          <Button onClick={handleSaveItem} variant="contained">
            保存
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete confirmation dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>确认删除</DialogTitle>
        <DialogContent>
          <Typography>
            确定要删除"{selectedItem?.title}"吗？此操作不可撤销。
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>取消</Button>
          <Button
            onClick={() => handleDeleteItem(selectedItem?.id)}
            variant="contained"
            color="error"
          >
            删除
          </Button>
        </DialogActions>
      </Dialog>

      {/* Batch delete confirmation dialog */}
      <Dialog
        open={batchDeleteDialog}
        onClose={() => setBatchDeleteDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>批量删除确认</DialogTitle>
        <DialogContent>
          <Typography>
            确定要删除选中的 {selectedItems.length} 项吗？此操作不可撤销。
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setBatchDeleteDialog(false)}>取消</Button>
          <Button
            onClick={handleBatchDelete}
            variant="contained"
            color="error"
          >
            批量删除
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default ContentServiceManagement;