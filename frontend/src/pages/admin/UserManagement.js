import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Avatar,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Button,
  IconButton,
  TextField,
  InputAdornment,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tabs,
  Tab,
  CircularProgress,
  Alert,
  Snackbar,
  Pagination,
  Stack,
} from '@mui/material';
import {
  Search,
  FilterList,
  Edit,
  Delete,
  Visibility,
  CheckCircle,
  Cancel,
  People,
  MoreVert,
  Add,
  Person,
  Group,
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import apiService from '../../services/api';

function UserManagement() {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [users, setUsers] = useState([]);
  const [filteredUsers, setFilteredUsers] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [selectedUser, setSelectedUser] = useState(null);
  const [userDetailOpen, setUserDetailOpen] = useState(false);
  const [tabValue, setTabValue] = useState(0);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [newUser, setNewUser] = useState({
    full_name: '',
    email: '',
    phone: '',
    password: '',
    role: 'regular_user',
    status: 'active',
    send_notification: true,
    force_password_reset: true
  });
  const [editUser, setEditUser] = useState(null);
  const [alert, setAlert] = useState({ open: false, message: '', severity: 'success' });
  const [isLoadingUsers, setIsLoadingUsers] = useState(false);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [userToDelete, setUserToDelete] = useState(null);

  // 分页状态
  const [currentPage, setCurrentPage] = useState(1);
  const [usersPerPage] = useState(5);
  const [paginatedUsers, setPaginatedUsers] = useState([]);
  const [totalPages, setTotalPages] = useState(1);

  // 加载用户列表
  const loadUsers = async () => {
    // 防止重复请求
    if (isLoadingUsers) {
      console.log('正在加载用户列表，跳过重复请求');
      return;
    }

    try {
      setIsLoadingUsers(true);
      setLoading(true);
      const response = await apiService.get('/users');
      if (response && response.success) {
        // 后端返回的数据结构是 { users: [...], total: ..., page: ..., size: ..., pages: ... }
        const usersData = Array.isArray(response.data?.users) ? response.data.users : [];
        setUsers(usersData);
        setFilteredUsers(usersData);

      } else {

        setUsers([]);
        setFilteredUsers([]);
      }
    } catch (error) {

      setAlert({
        open: true,
        message: '加载用户列表失败，请重试',
        severity: 'error'
      });
      setUsers([]);
      setFilteredUsers([]);
    } finally {
      setLoading(false);
      setIsLoadingUsers(false);
    }
  };

  useEffect(() => {
    loadUsers();
  }, []);

  // 自动关闭提示
  useEffect(() => {
    if (alert.open) {
      const timer = setTimeout(() => {
        setAlert({ ...alert, open: false });
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [alert.open]);

  // 编辑用户
  const handleEditUser = async () => {
    try {
      const updateData = {
        full_name: editUser.full_name?.trim() || null,
        phone: editUser.phone?.trim() || null
      };

      // 如果手机号为空字符串，设置为null
      if (updateData.phone === '') {
        updateData.phone = null;
      }

      console.log('发送更新数据:', updateData);

      const response = await apiService.put(`/users/${editUser.id}`, updateData);
      if (response && response.success) {
        await loadUsers();
        setEditDialogOpen(false);
        setEditUser(null);
        setAlert({
          open: true,
          message: '用户信息更新成功！',
          severity: 'success'
        });
      } else {
        setAlert({
          open: true,
          message: response?.message || '更新失败，请重试',
          severity: 'error'
        });
      }
    } catch (error) {
      console.error('更新用户失败:', error);
      setAlert({
        open: true,
        message: '更新失败，请检查网络连接或联系管理员',
        severity: 'error'
      });
    }
  };

  // 创建用户
  const handleCreateUser = async () => {
    try {
      // 清理和验证数据
      const createData = {
        email: newUser.email?.trim(),
        full_name: newUser.full_name?.trim(),
        phone: newUser.phone?.trim() || null,
        password: newUser.password?.trim() || null,
        role: newUser.role,
        status: newUser.status,
        send_notification: newUser.send_notification,
        force_password_reset: newUser.force_password_reset
      };

      // 移除空字符串，设置为null
      if (createData.phone === '') createData.phone = null;
      if (createData.password === '') createData.password = null;

      console.log('发送创建用户数据:', createData);

      const response = await apiService.post('/users/create', createData);
      if (response && response.success) {
        await loadUsers();
        setCreateDialogOpen(false);
        setNewUser({
          full_name: '',
          email: '',
          phone: '',
          password: '',
          role: 'regular_user',
          status: 'active',
          send_notification: true,
          force_password_reset: true
        });
        setAlert({
          open: true,
          message: '用户创建成功！',
          severity: 'success'
        });
      } else {
        setAlert({
          open: true,
          message: response?.message || '创建失败，请重试',
          severity: 'error'
        });
      }
    } catch (error) {
      console.error('创建用户失败:', error);
      setAlert({
        open: true,
        message: '创建失败，请检查网络连接或联系管理员',
        severity: 'error'
      });
    }
  };

  // 更新用户状态
  const handleUpdateUserStatus = async (userId, newStatus) => {
    try {
      const response = await apiService.put(`/users/${userId}/status`, { status: newStatus });
      if (response && response.success) {
        await loadUsers();
        setAlert({
          open: true,
          message: `用户状态已更新为${newStatus === 'active' ? '激活' : '禁用'}！`,
          severity: 'success'
        });
      } else {
        setAlert({
          open: true,
          message: response?.message || '状态更新失败，请重试',
          severity: 'error'
        });
      }
    } catch (error) {
      console.error('更新用户状态失败:', error);
      setAlert({
        open: true,
        message: '状态更新失败，请检查网络连接或联系管理员',
        severity: 'error'
      });
    }
  };

  // 删除用户 - 显示确认对话框
  const handleDeleteUser = (userId, userName) => {
    const user = users.find(u => u.id === userId);
    setUserToDelete(user);
    setDeleteConfirmOpen(true);
  };

  // 确认删除用户
  const confirmDeleteUser = async () => {
    if (!userToDelete) return;

    try {
      const response = await apiService.delete(`/users/${userToDelete.id}`);
      if (response && response.success) {
        await loadUsers();
        setAlert({
          open: true,
          message: `用户"${userToDelete.full_name || userToDelete.email}"删除成功！`,
          severity: 'success'
        });
      } else {
        setAlert({
          open: true,
          message: response?.message || '删除失败，请重试',
          severity: 'error'
        });
      }
    } catch (error) {
      console.error('删除用户失败:', error);
      setAlert({
        open: true,
        message: '删除失败，请检查网络连接或联系管理员',
        severity: 'error'
      });
    } finally {
      setDeleteConfirmOpen(false);
      setUserToDelete(null);
    }
  };

  // 获取用户详情
  const handleViewUser = async (userId) => {
    try {
      const response = await apiService.get(`/users/${userId}`);
      if (response && response.success) {
        setSelectedUser(response.data);
        setUserDetailOpen(true);
      } else {
        setAlert({
          open: true,
          message: '获取用户详情失败',
          severity: 'error'
        });
      }
    } catch (error) {
      console.error('获取用户详情失败:', error);
      setAlert({
        open: true,
        message: '获取用户详情失败，请重试',
        severity: 'error'
      });
    }
  };




  // 过滤用户
  useEffect(() => {
    let filtered = users;

    if (searchTerm) {
      filtered = filtered.filter(user =>
        (user.full_name || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
        (user.email || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
        (user.phone || '').toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (roleFilter !== 'all') {
      // 根据用户的角色数组进行筛选
      filtered = filtered.filter(user =>
        user.roles && user.roles.some(role => role.role === roleFilter)
      );
    }

    if (statusFilter !== 'all') {
      filtered = filtered.filter(user => user.status === statusFilter);
    }

    setFilteredUsers(filtered);
    // 重置到第一页
    setCurrentPage(1);
  }, [users, searchTerm, roleFilter, statusFilter]);

  // 分页逻辑
  useEffect(() => {
    const startIndex = (currentPage - 1) * usersPerPage;
    const endIndex = startIndex + usersPerPage;
    const paginated = filteredUsers.slice(startIndex, endIndex);
    setPaginatedUsers(paginated);
    setTotalPages(Math.ceil(filteredUsers.length / usersPerPage));
  }, [filteredUsers, currentPage, usersPerPage]);

  // 处理分页变化
  const handlePageChange = (event, page) => {
    setCurrentPage(page);
  };

  const getRoleLabel = (role) => {
    const roleMap = {
      regular_user: '普通用户',
      enterprise_user: '企业用户',
      agent_user: '代理商',
      channel_user: '渠道商',
      admin: '管理员',
      super_admin: '超级管理员',
    };
    return roleMap[role] || role;
  };

  // 获取用户的主要角色
  const getUserMainRole = (user) => {
    if (!user.roles || user.roles.length === 0) {
      return '无角色';
    }
    // 返回第一个激活的角色
    const activeRole = user.roles.find(role => role.is_active);
    return activeRole ? getRoleLabel(activeRole.role) : '无角色';
  };

  // 安全获取用户名首字母
  const getUserInitial = (user) => {
    const name = user?.full_name || user?.email || 'U';
    return String(name).charAt(0).toUpperCase();
  };

  const getRoleColor = (role) => {
    const colorMap = {
      user: 'default',
      enterprise: 'primary',
      agent: 'success',
      channel: 'warning',
      admin: 'error',
    };
    return colorMap[role] || 'default';
  };

  const getStatusColor = (status) => {
    const statusMap = {
      active: 'success',
      pending: 'warning',
      inactive: 'error',
    };
    return statusMap[status] || 'default';
  };

  const getStatusLabel = (status) => {
    const statusMap = {
      active: '活跃',
      pending: '待审核',
      inactive: '已停用',
    };
    return statusMap[status] || status;
  };

  const handleUserAction = async (action, userId) => {
    const user = users.find(u => u.id === userId);
    if (!user) return;

    switch (action) {
      case 'edit':
        // 编辑用户功能
        setEditUser({
          id: user.id,
          full_name: user.full_name || '',
          email: user.email || '',
          phone: user.phone || '',
          status: user.status || 'active'
        });
        setEditDialogOpen(true);
        break;
      case 'approve':
        await handleUpdateUserStatus(userId, 'active');
        break;
      case 'reject':
        await handleUpdateUserStatus(userId, 'inactive');
        break;
      case 'delete':
        handleDeleteUser(userId, user.full_name || user.email);
        break;
      default:
        console.log(`Unknown action: ${action}`);
    }
  };



  if (loading) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 400 }}>
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  return (
    <Box sx={{ width: '100%', p: 3, backgroundColor: '#f5f5f5', minHeight: '100vh' }}>
      {/* 页面标题和操作按钮区域 */}
      <Box sx={{
        backgroundColor: '#fff',
        borderRadius: 3,
        border: '1px solid #e0e0e0',
        p: 4,
        mb: 3,
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
      }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <People sx={{ fontSize: 32, color: '#1976d2' }} />
            <Box>
              <Typography variant="h4" sx={{ fontWeight: 700, color: '#1a1a1a', mb: 0.5 }}>
                用户管理
              </Typography>
              <Typography variant="body1" sx={{ color: '#666' }}>
                管理平台用户，审核角色申请，设置用户权限
              </Typography>
            </Box>
          </Box>
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={() => setCreateDialogOpen(true)}
            sx={{
              borderRadius: 2,
              fontWeight: 600,
              px: 3,
              py: 1.5,
              background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',
              boxShadow: '0 4px 12px rgba(25, 118, 210, 0.3)',
              '&:hover': {
                background: 'linear-gradient(135deg, #1565c0 0%, #0d47a1 100%)',
                boxShadow: '0 6px 16px rgba(25, 118, 210, 0.4)',
              }
            }}
          >
            创建用户
          </Button>
        </Box>
      </Box>



      {/* Filters */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                placeholder="搜索用户..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Search />
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>角色筛选</InputLabel>
                <Select
                  value={roleFilter}
                  label="角色筛选"
                  onChange={(e) => setRoleFilter(e.target.value)}
                >
                  <MenuItem value="all">全部角色</MenuItem>
                  <MenuItem value="regular_user">普通用户</MenuItem>
                  <MenuItem value="enterprise_user">企业用户</MenuItem>
                  <MenuItem value="agent_user">代理商</MenuItem>
                  <MenuItem value="channel_user">渠道商</MenuItem>
                  <MenuItem value="admin">管理员</MenuItem>
                  <MenuItem value="super_admin">超级管理员</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>状态筛选</InputLabel>
                <Select
                  value={statusFilter}
                  label="状态筛选"
                  onChange={(e) => setStatusFilter(e.target.value)}
                >
                  <MenuItem value="all">全部状态</MenuItem>
                  <MenuItem value="active">活跃</MenuItem>
                  <MenuItem value="pending">待审核</MenuItem>
                  <MenuItem value="inactive">已停用</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={2}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<FilterList />}
                onClick={() => {
                  setSearchTerm('');
                  setRoleFilter('all');
                  setStatusFilter('all');
                }}
              >
                重置
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Users Table */}
      <Card>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant="h6" sx={{ fontWeight: 600 }}>
              用户列表 (共 {filteredUsers.length} 个用户，第 {currentPage} 页，共 {totalPages} 页)
            </Typography>
          </Box>
          <TableContainer component={Paper} variant="outlined">
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>用户</TableCell>
                  <TableCell>角色</TableCell>
                  <TableCell>状态</TableCell>
                  <TableCell>手机号</TableCell>
                  <TableCell>订单数</TableCell>
                  <TableCell>最后登录</TableCell>
                  <TableCell>操作</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {paginatedUsers.map((user) => (
                  <TableRow key={user.id} hover>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Avatar sx={{ width: 40, height: 40, mr: 2 }}>
                          {getUserInitial(user)}
                        </Avatar>
                        <Box>
                          <Typography variant="body2" sx={{ fontWeight: 500 }}>
                            {user.full_name || '未设置姓名'}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {user.email}
                          </Typography>
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={getUserMainRole(user)}
                        color="primary"
                        size="small"
                        variant="outlined"
                      />
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={getStatusLabel(user.status)}
                        color={getStatusColor(user.status)}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {user.phone || '-'}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {user.statistics?.total_orders || 0}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" color="text.secondary">
                        {user.last_login_at ? new Date(user.last_login_at).toLocaleString('zh-CN') : '从未登录'}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', gap: 1 }}>
                        <IconButton
                          size="small"
                          onClick={() => handleViewUser(user.id)}
                          color="primary"
                        >
                          <Visibility fontSize="small" />
                        </IconButton>
                        <IconButton
                          size="small"
                          onClick={() => handleUserAction('edit', user.id)}
                          color="info"
                        >
                          <Edit fontSize="small" />
                        </IconButton>
                        <IconButton
                          size="small"
                          onClick={() => handleUserAction('delete', user.id)}
                          color="error"
                          sx={{
                            '&:hover': {
                              backgroundColor: 'rgba(211, 47, 47, 0.08)',
                            }
                          }}
                        >
                          <Delete fontSize="small" />
                        </IconButton>
                        {user.status === 'pending' && (
                          <>
                            <IconButton
                              size="small"
                              onClick={() => handleUserAction('approve', user.id)}
                              color="success"
                            >
                              <CheckCircle fontSize="small" />
                            </IconButton>
                            <IconButton
                              size="small"
                              onClick={() => handleUserAction('reject', user.id)}
                              color="error"
                            >
                              <Cancel fontSize="small" />
                            </IconButton>
                          </>
                        )}
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>

          {/* 分页组件 */}
          {totalPages > 1 && (
            <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
              <Stack spacing={2}>
                <Pagination
                  count={totalPages}
                  page={currentPage}
                  onChange={handlePageChange}
                  color="primary"
                  size="large"
                  showFirstButton
                  showLastButton
                  sx={{
                    '& .MuiPaginationItem-root': {
                      fontSize: '1rem',
                      fontWeight: 500,
                      minWidth: '40px',
                      height: '40px',
                      borderRadius: '8px',
                      margin: '0 2px',
                      '&:hover': {
                        backgroundColor: 'rgba(25, 118, 210, 0.08)',
                      },
                      '&.Mui-selected': {
                        backgroundColor: '#1976d2',
                        color: 'white',
                        fontWeight: 600,
                        '&:hover': {
                          backgroundColor: '#1565c0',
                        },
                      },
                    },
                  }}
                />
                <Typography
                  variant="body2"
                  color="text.secondary"
                  sx={{ textAlign: 'center', mt: 1 }}
                >
                  显示第 {((currentPage - 1) * usersPerPage) + 1} - {Math.min(currentPage * usersPerPage, filteredUsers.length)} 条，共 {filteredUsers.length} 条记录
                </Typography>
              </Stack>
            </Box>
          )}
        </CardContent>
      </Card>

      {/* User Detail Dialog */}
      <Dialog
        open={userDetailOpen}
        onClose={() => setUserDetailOpen(false)}
        maxWidth="lg"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 3,
            boxShadow: '0 8px 32px rgba(0,0,0,0.12)',
          }
        }}
      >
        <DialogTitle sx={{
          background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',
          color: 'white',
          borderRadius: '12px 12px 0 0'
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <People sx={{ fontSize: 28 }} />
            <Typography variant="h5" sx={{ fontWeight: 600 }}>
              用户详情
            </Typography>
          </Box>
        </DialogTitle>
        <DialogContent sx={{ p: 0 }}>
          {selectedUser && (
            <Box>
              {/* 用户头部信息卡片 */}
              <Box sx={{
                background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
                p: 4,
                borderBottom: '1px solid #e0e0e0'
              }}>
                <Grid container spacing={3} alignItems="center">
                  <Grid item xs={12} md={3}>
                    <Box sx={{ textAlign: 'center' }}>
                      <Avatar
                        sx={{
                          width: 100,
                          height: 100,
                          mx: 'auto',
                          mb: 2,
                          bgcolor: 'primary.main',
                          fontSize: '2.5rem',
                          boxShadow: '0 4px 20px rgba(25, 118, 210, 0.3)',
                        }}
                      >
                        {getUserInitial(selectedUser)}
                      </Avatar>
                      <Typography variant="h5" sx={{ fontWeight: 700, mb: 1, color: '#1a1a1a' }}>
                        {selectedUser.full_name || '未设置姓名'}
                      </Typography>
                      <Typography variant="body1" sx={{ color: '#666', mb: 2 }}>
                        {selectedUser.email}
                      </Typography>
                      <Box sx={{ display: 'flex', gap: 1, justifyContent: 'center', flexWrap: 'wrap' }}>
                        <Chip
                          label={getUserMainRole(selectedUser)}
                          color="primary"
                          variant="filled"
                          sx={{ fontWeight: 600 }}
                        />
                        <Chip
                          label={getStatusLabel(selectedUser.status)}
                          color={getStatusColor(selectedUser.status)}
                          variant="outlined"
                          size="small"
                        />
                      </Box>
                    </Box>
                  </Grid>
                  <Grid item xs={12} md={9}>
                    <Grid container spacing={3}>
                      <Grid item xs={12} sm={6} md={3}>
                        <Box sx={{ textAlign: 'center', p: 2, bgcolor: 'white', borderRadius: 2, boxShadow: '0 2px 8px rgba(0,0,0,0.1)' }}>
                          <Typography variant="h4" sx={{ fontWeight: 700, color: '#1976d2', mb: 1 }}>
                            {selectedUser.statistics?.total_orders || 0}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            总订单数
                          </Typography>
                        </Box>
                      </Grid>
                      <Grid item xs={12} sm={6} md={3}>
                        <Box sx={{ textAlign: 'center', p: 2, bgcolor: 'white', borderRadius: 2, boxShadow: '0 2px 8px rgba(0,0,0,0.1)' }}>
                          <Typography variant="h4" sx={{ fontWeight: 700, color: '#2e7d32', mb: 1 }}>
                            ¥{selectedUser.statistics?.total_spent || '0.00'}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            总消费金额
                          </Typography>
                        </Box>
                      </Grid>
                      <Grid item xs={12} sm={6} md={3}>
                        <Box sx={{ textAlign: 'center', p: 2, bgcolor: 'white', borderRadius: 2, boxShadow: '0 2px 8px rgba(0,0,0,0.1)' }}>
                          <Typography variant="h4" sx={{ fontWeight: 700, color: '#ed6c02', mb: 1 }}>
                            {selectedUser.statistics?.active_projects || 0}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            活跃项目
                          </Typography>
                        </Box>
                      </Grid>
                      <Grid item xs={12} sm={6} md={3}>
                        <Box sx={{ textAlign: 'center', p: 2, bgcolor: 'white', borderRadius: 2, boxShadow: '0 2px 8px rgba(0,0,0,0.1)' }}>
                          <Typography variant="h4" sx={{ fontWeight: 700, color: '#666666', mb: 1 }}>
                            {selectedUser.statistics?.referral_count || 0}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            推荐用户
                          </Typography>
                        </Box>
                      </Grid>
                    </Grid>
                  </Grid>
                </Grid>
              </Box>
              {/* 详细信息区域 */}
              <Box sx={{ p: 4 }}>
                <Grid container spacing={4}>
                  {/* 基本信息 */}
                  <Grid item xs={12} md={6}>
                    <Card sx={{ height: '100%', boxShadow: '0 2px 12px rgba(0,0,0,0.08)', borderRadius: 3 }}>
                      <CardContent sx={{ p: 3 }}>
                        <Typography variant="h6" sx={{ fontWeight: 700, mb: 3, color: '#1976d2', display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Person sx={{ fontSize: 24 }} />
                          基本信息
                        </Typography>
                        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2.5 }}>
                          <Box>
                            <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 600, mb: 0.5 }}>
                              用户ID
                            </Typography>
                            <Typography variant="body1" sx={{ fontFamily: 'monospace', fontSize: '0.9rem', color: '#666' }}>
                              {selectedUser.id}
                            </Typography>
                          </Box>
                          <Box>
                            <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 600, mb: 0.5 }}>
                              邮箱地址
                            </Typography>
                            <Typography variant="body1" sx={{ fontWeight: 500 }}>
                              {selectedUser.email}
                            </Typography>
                          </Box>
                          <Box>
                            <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 600, mb: 0.5 }}>
                              手机号码
                            </Typography>
                            <Typography variant="body1" sx={{ fontWeight: 500 }}>
                              {selectedUser.phone || '未设置'}
                            </Typography>
                          </Box>
                          <Box>
                            <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 600, mb: 0.5 }}>
                              推荐码
                            </Typography>
                            <Typography variant="body1" sx={{ fontFamily: 'monospace', fontWeight: 600, color: '#1976d2' }}>
                              {selectedUser.referral_code || '无'}
                            </Typography>
                          </Box>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>

                  {/* 账户状态 */}
                  <Grid item xs={12} md={6}>
                    <Card sx={{ height: '100%', boxShadow: '0 2px 12px rgba(0,0,0,0.08)', borderRadius: 3 }}>
                      <CardContent sx={{ p: 3 }}>
                        <Typography variant="h6" sx={{ fontWeight: 700, mb: 3, color: '#1976d2', display: 'flex', alignItems: 'center', gap: 1 }}>
                          <CheckCircle sx={{ fontSize: 24 }} />
                          账户状态
                        </Typography>
                        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2.5 }}>
                          <Box>
                            <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 600, mb: 0.5 }}>
                              注册时间
                            </Typography>
                            <Typography variant="body1" sx={{ fontWeight: 500 }}>
                              {selectedUser.created_at ? new Date(selectedUser.created_at).toLocaleString('zh-CN') : '未知'}
                            </Typography>
                          </Box>
                          <Box>
                            <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 600, mb: 0.5 }}>
                              最后登录
                            </Typography>
                            <Typography variant="body1" sx={{ fontWeight: 500 }}>
                              {selectedUser.last_login_at ? new Date(selectedUser.last_login_at).toLocaleString('zh-CN') : '从未登录'}
                            </Typography>
                          </Box>
                          <Box>
                            <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 600, mb: 0.5 }}>
                              邮箱验证
                            </Typography>
                            <Chip
                              label={selectedUser.email_verified ? '已验证' : '未验证'}
                              color={selectedUser.email_verified ? 'success' : 'warning'}
                              size="small"
                              variant="outlined"
                            />
                          </Box>
                          <Box>
                            <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 600, mb: 0.5 }}>
                              手机验证
                            </Typography>
                            <Chip
                              label={selectedUser.phone_verified ? '已验证' : '未验证'}
                              color={selectedUser.phone_verified ? 'success' : 'warning'}
                              size="small"
                              variant="outlined"
                            />
                          </Box>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>

                  {/* 用户角色 */}
                  {selectedUser.roles && selectedUser.roles.length > 0 && (
                    <Grid item xs={12}>
                      <Card sx={{ boxShadow: '0 2px 12px rgba(0,0,0,0.08)', borderRadius: 3 }}>
                        <CardContent sx={{ p: 3 }}>
                          <Typography variant="h6" sx={{ fontWeight: 700, mb: 3, color: '#1976d2', display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Group sx={{ fontSize: 24 }} />
                            用户角色
                          </Typography>
                          <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                            {selectedUser.roles.map((role, index) => (
                              <Chip
                                key={index}
                                label={getRoleLabel(role.role)}
                                color={role.is_active ? 'primary' : 'default'}
                                variant={role.is_active ? 'filled' : 'outlined'}
                                sx={{
                                  fontWeight: 600,
                                  '& .MuiChip-label': {
                                    px: 2
                                  }
                                }}
                              />
                            ))}
                          </Box>
                        </CardContent>
                      </Card>
                    </Grid>
                  )}
                </Grid>
              </Box>
            </Box>
          )}
        </DialogContent>
        <DialogActions sx={{
          p: 3,
          borderTop: '1px solid #e0e0e0',
          background: '#fafafa',
          gap: 2
        }}>
          <Button
            onClick={() => setUserDetailOpen(false)}
            variant="outlined"
            sx={{
              borderRadius: 2,
              px: 3,
              py: 1,
              fontWeight: 600
            }}
          >
            关闭
          </Button>
          {selectedUser && (
            <Button
              onClick={() => {
                setEditUser({
                  id: selectedUser.id,
                  full_name: selectedUser.full_name || '',
                  email: selectedUser.email || '',
                  phone: selectedUser.phone || '',
                  status: selectedUser.status || 'active'
                });
                setEditDialogOpen(true);
                setUserDetailOpen(false);
              }}
              variant="contained"
              startIcon={<Edit />}
              sx={{
                borderRadius: 2,
                px: 3,
                py: 1,
                fontWeight: 600,
                background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',
                '&:hover': {
                  background: 'linear-gradient(135deg, #1565c0 0%, #0d47a1 100%)',
                }
              }}
            >
              编辑用户
            </Button>
          )}
        </DialogActions>
      </Dialog>

      {/* 创建用户对话框 */}
      <Dialog
        open={createDialogOpen}
        onClose={() => setCreateDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>创建新用户</DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="姓名"
                  value={newUser.full_name}
                  onChange={(e) => setNewUser({ ...newUser, full_name: e.target.value })}
                  variant="outlined"
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="邮箱"
                  type="email"
                  value={newUser.email}
                  onChange={(e) => setNewUser({ ...newUser, email: e.target.value })}
                  variant="outlined"
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="手机号"
                  value={newUser.phone}
                  onChange={(e) => setNewUser({ ...newUser, phone: e.target.value })}
                  variant="outlined"
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="密码（可选，留空自动生成）"
                  type="password"
                  value={newUser.password}
                  onChange={(e) => setNewUser({ ...newUser, password: e.target.value })}
                  variant="outlined"
                  placeholder="留空将自动生成密码"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>角色</InputLabel>
                  <Select
                    value={newUser.role}
                    label="角色"
                    onChange={(e) => setNewUser({ ...newUser, role: e.target.value })}
                  >
                    <MenuItem value="regular_user">普通用户</MenuItem>
                    <MenuItem value="admin">管理员</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>状态</InputLabel>
                  <Select
                    value={newUser.status}
                    label="状态"
                    onChange={(e) => setNewUser({ ...newUser, status: e.target.value })}
                  >
                    <MenuItem value="active">激活</MenuItem>
                    <MenuItem value="inactive">禁用</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCreateDialogOpen(false)}>取消</Button>
          <Button onClick={handleCreateUser} variant="contained">创建</Button>
        </DialogActions>
      </Dialog>

      {/* 编辑用户对话框 */}
      <Dialog
        open={editDialogOpen}
        onClose={() => setEditDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>编辑用户信息</DialogTitle>
        <DialogContent>
          {editUser && (
            <Box sx={{ pt: 2 }}>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="姓名"
                    value={editUser.full_name}
                    onChange={(e) => setEditUser({ ...editUser, full_name: e.target.value })}
                    variant="outlined"
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="邮箱"
                    type="email"
                    value={editUser.email}
                    variant="outlined"
                    disabled
                    helperText="邮箱地址不可修改"
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="手机号"
                    value={editUser.phone}
                    onChange={(e) => setEditUser({ ...editUser, phone: e.target.value })}
                    variant="outlined"
                  />
                </Grid>
              </Grid>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditDialogOpen(false)}>取消</Button>
          <Button onClick={handleEditUser} variant="contained">保存</Button>
        </DialogActions>
      </Dialog>

      {/* 删除确认对话框 */}
      <Dialog
        open={deleteConfirmOpen}
        onClose={() => setDeleteConfirmOpen(false)}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 3,
            boxShadow: '0 8px 32px rgba(0,0,0,0.12)',
          }
        }}
      >
        <DialogTitle sx={{
          display: 'flex',
          alignItems: 'center',
          gap: 2,
          color: '#d32f2f',
          borderBottom: '1px solid #ffebee'
        }}>
          <Delete sx={{ fontSize: 28 }} />
          <Typography variant="h6" sx={{ fontWeight: 600 }}>
            确认删除用户
          </Typography>
        </DialogTitle>
        <DialogContent sx={{ pt: 3 }}>
          {userToDelete && (
            <Box>
              <Alert severity="warning" sx={{ mb: 3 }}>
                <Typography variant="body1" sx={{ fontWeight: 600, mb: 1 }}>
                  ⚠️ 危险操作警告
                </Typography>
                <Typography variant="body2">
                  删除用户是不可逆的操作，将会：
                </Typography>
                <Box component="ul" sx={{ mt: 1, pl: 2 }}>
                  <li>永久删除用户账户和所有相关数据</li>
                  <li>清除用户的所有角色和权限</li>
                  <li>删除用户的订单历史和统计信息</li>
                </Box>
              </Alert>

              <Box sx={{
                p: 3,
                bgcolor: '#f5f5f5',
                borderRadius: 2,
                border: '1px solid #e0e0e0'
              }}>
                <Typography variant="body1" sx={{ mb: 2, fontWeight: 600 }}>
                  即将删除的用户信息：
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                      <Avatar sx={{ bgcolor: 'error.main' }}>
                        {getUserInitial(userToDelete)}
                      </Avatar>
                      <Box>
                        <Typography variant="h6" sx={{ fontWeight: 600 }}>
                          {userToDelete.full_name || '未设置姓名'}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {userToDelete.email}
                        </Typography>
                      </Box>
                    </Box>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">角色</Typography>
                    <Typography variant="body1">{getUserMainRole(userToDelete)}</Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">注册时间</Typography>
                    <Typography variant="body1">
                      {userToDelete.created_at ? new Date(userToDelete.created_at).toLocaleDateString('zh-CN') : '未知'}
                    </Typography>
                  </Grid>
                </Grid>
              </Box>
            </Box>
          )}
        </DialogContent>
        <DialogActions sx={{
          p: 3,
          borderTop: '1px solid #e0e0e0',
          gap: 2
        }}>
          <Button
            onClick={() => setDeleteConfirmOpen(false)}
            variant="outlined"
            sx={{
              borderRadius: 2,
              px: 3,
              py: 1,
              fontWeight: 600
            }}
          >
            取消
          </Button>
          <Button
            onClick={confirmDeleteUser}
            variant="contained"
            color="error"
            startIcon={<Delete />}
            sx={{
              borderRadius: 2,
              px: 3,
              py: 1,
              fontWeight: 600,
              background: 'linear-gradient(135deg, #d32f2f 0%, #c62828 100%)',
              '&:hover': {
                background: 'linear-gradient(135deg, #c62828 0%, #b71c1c 100%)',
              }
            }}
          >
            确认删除
          </Button>
        </DialogActions>
      </Dialog>

      {/* 提示消息 */}
      <Snackbar
        open={alert.open}
        autoHideDuration={3000}
        onClose={() => setAlert({ ...alert, open: false })}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert
          onClose={() => setAlert({ ...alert, open: false })}
          severity={alert.severity}
          sx={{ width: '100%' }}
        >
          {alert.message}
        </Alert>
      </Snackbar>
    </Box>
  );
}

export default UserManagement;
