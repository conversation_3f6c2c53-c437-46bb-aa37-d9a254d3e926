import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Avatar,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Button,
  IconButton,
  TextField,
  InputAdornment,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Alert,
  Tabs,
  Tab,
  Divider,
  Grid,
  LinearProgress,
  Stack,
} from '@mui/material';
import {
  Search,
  FilterList,
  Visibility,
  CheckCircle,
  Cancel,
  Schedule,
  Article,
  Edit,
  Upload,
  Assignment,
  PlayArrow,
  Pause,
  AttachMoney,
  AccessTime,
  Person,
  Description,
  CloudUpload,
  Send,
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';

function TaskManagement() {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [tasks, setTasks] = useState([]);
  const [filteredTasks, setFilteredTasks] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [priorityFilter, setPriorityFilter] = useState('all');
  const [selectedTask, setSelectedTask] = useState(null);
  const [taskDialogOpen, setTaskDialogOpen] = useState(false);
  const [deliveryForm, setDeliveryForm] = useState({
    title: '',
    content: '',
    note: '',
    attachments: [],
  });
  const [tabValue, setTabValue] = useState(0);

  // 模拟数据加载
  useEffect(() => {
    const loadTasks = async () => {
      // 模拟API调用
      setTimeout(() => {
        const mockTasks = [
          {
            id: 1,
            title: 'AI技术在教育领域的革新应用',
            client: '科技教育公司',
            type: 'direct',
            status: 'in_progress',
            acceptTime: '2024-01-20 15:45',
            deadline: '2024-01-25 18:00',
            wordCount: 2500,
            priority: 'high',
            budget: 1500,
            progress: 65,
            requirements: '需要专业的技术分析，包含实际案例',
            tags: ['AI', '教育', '技术'],
            channels: ['微信公众号', '知乎', '今日头条'],
          },
          {
            id: 2,
            title: '2024年SEO优化最佳实践指南',
            client: '数字营销公司',
            type: 'direct',
            status: 'completed',
            acceptTime: '2024-01-20 15:45',
            deadline: '2024-01-28 12:00',
            completedTime: '2024-01-27 10:30',
            wordCount: 3200,
            priority: 'medium',
            budget: 2000,
            progress: 100,
            requirements: '需要包含最新的SEO策略和工具推荐',
            tags: ['SEO', '优化', '营销'],
            channels: ['官方博客', 'LinkedIn'],
          },
          {
            id: 3,
            title: '企业数字化转型案例分析',
            client: '咨询公司',
            type: 'creative',
            status: 'in_progress',
            acceptTime: '2024-01-21 14:30',
            deadline: '2024-01-30 17:00',
            wordCount: 2000,
            priority: 'medium',
            budget: 1800,
            progress: 30,
            requirements: '需要分析3-5个成功的数字化转型案例',
            tags: ['数字化', '转型', '案例分析'],
            targetAudience: '企业管理者',
          },
        ];
        setTasks(mockTasks);
        setFilteredTasks(mockTasks);
        setLoading(false);
      }, 1000);
    };

    loadTasks();
  }, []);

  // 过滤任务
  useEffect(() => {
    let filtered = tasks;

    // 根据标签页过滤
    if (tabValue === 0) {
      filtered = filtered.filter(task => task.status === 'in_progress');
    } else if (tabValue === 1) {
      filtered = filtered.filter(task => task.status === 'completed');
    }

    if (searchTerm) {
      filtered = filtered.filter(task =>
        task.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        task.client.toLowerCase().includes(searchTerm.toLowerCase()) ||
        task.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    if (statusFilter !== 'all') {
      filtered = filtered.filter(task => task.status === statusFilter);
    }

    if (priorityFilter !== 'all') {
      filtered = filtered.filter(task => task.priority === priorityFilter);
    }

    setFilteredTasks(filtered);
  }, [tasks, tabValue, searchTerm, statusFilter, priorityFilter]);

  const getStatusColor = (status) => {
    const statusMap = {
      in_progress: 'primary',
      completed: 'success',
      cancelled: 'error',
      pending_review: 'warning',
    };
    return statusMap[status] || 'default';
  };

  const getStatusLabel = (status) => {
    const statusMap = {
      in_progress: '进行中',
      completed: '已完成',
      cancelled: '已取消',
      pending_review: '待审核',
    };
    return statusMap[status] || status;
  };

  const getPriorityColor = (priority) => {
    const priorityMap = {
      high: 'error',
      medium: 'warning',
      low: 'info',
    };
    return priorityMap[priority] || 'default';
  };

  const getPriorityLabel = (priority) => {
    const priorityMap = {
      high: '高优先级',
      medium: '中优先级',
      low: '低优先级',
    };
    return priorityMap[priority] || priority;
  };

  const handleViewTask = (task) => {
    setSelectedTask(task);
    setTaskDialogOpen(true);
    setDeliveryForm({
      title: '',
      content: '',
      note: '',
      attachments: [],
    });
  };

  const handleDeliverTask = async () => {
    try {
      // 这里调用API提交任务
      console.log('提交任务:', selectedTask.id, deliveryForm);
      
      // 更新本地状态
      setTasks(prev => prev.map(task => 
        task.id === selectedTask.id 
          ? { ...task, status: 'pending_review', progress: 100 }
          : task
      ));
      
      setTaskDialogOpen(false);
    } catch (error) {
      console.error('提交任务失败:', error);
    }
  };

  const stats = [
    {
      title: '进行中',
      value: tasks.filter(t => t.status === 'in_progress').length,
      color: 'primary',
      icon: <PlayArrow />,
    },
    {
      title: '已完成',
      value: tasks.filter(t => t.status === 'completed').length,
      color: 'success',
      icon: <CheckCircle />,
    },
    {
      title: '待审核',
      value: tasks.filter(t => t.status === 'pending_review').length,
      color: 'warning',
      icon: <Schedule />,
    },
    {
      title: '总收益',
      value: `¥${tasks.filter(t => t.status === 'completed').reduce((sum, t) => sum + (t.budget || 0), 0)}`,
      color: 'info',
      icon: <AttachMoney />,
    },
  ];

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 400 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ width: '100%', p: 3, backgroundColor: '#f5f5f5', minHeight: '100vh' }}>
      {/* 页面标题 */}
      <Box sx={{
        backgroundColor: '#fff',
        borderRadius: 3,
        border: '1px solid #e0e0e0',
        p: 4,
        mb: 3,
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
      }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Assignment sx={{ fontSize: 32, color: '#1976d2' }} />
            <Box>
              <Typography variant="h4" sx={{ fontWeight: 700, color: '#1a1a1a', mb: 0.5 }}>
                稿件任务
              </Typography>
              <Typography variant="body1" sx={{ color: '#666' }}>
                管理已接单的任务，跟踪进度和提交稿件
              </Typography>
            </Box>
          </Box>
        </Box>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {stats.map((stat, index) => (
          <Grid item xs={12} sm={6} md={3} key={index}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Avatar
                    sx={{
                      bgcolor: `${stat.color}.light`,
                      color: `${stat.color}.main`,
                      width: 48,
                      height: 48,
                      mr: 2,
                    }}
                  >
                    {stat.icon}
                  </Avatar>
                  <Box>
                    <Typography variant="h4" sx={{ fontWeight: 600 }}>
                      {stat.value}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {stat.title}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* 标签页 */}
      <Card sx={{ mb: 3 }}>
        <Tabs
          value={tabValue}
          onChange={(e, newValue) => setTabValue(newValue)}
          sx={{ borderBottom: 1, borderColor: 'divider' }}
        >
          <Tab
            icon={<PlayArrow />}
            label={`进行中 (${tasks.filter(t => t.status === 'in_progress').length})`}
            iconPosition="start"
          />
          <Tab
            icon={<CheckCircle />}
            label={`已完成 (${tasks.filter(t => t.status === 'completed').length})`}
            iconPosition="start"
          />
        </Tabs>
      </Card>

      {/* Filters */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                placeholder="搜索任务..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Search />
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>优先级筛选</InputLabel>
                <Select
                  value={priorityFilter}
                  label="优先级筛选"
                  onChange={(e) => setPriorityFilter(e.target.value)}
                >
                  <MenuItem value="all">全部优先级</MenuItem>
                  <MenuItem value="high">高优先级</MenuItem>
                  <MenuItem value="medium">中优先级</MenuItem>
                  <MenuItem value="low">低优先级</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={3}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<FilterList />}
                onClick={() => {
                  setSearchTerm('');
                  setPriorityFilter('all');
                }}
              >
                重置
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Task Table */}
      <Card>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant="h6" sx={{ fontWeight: 600 }}>
              {tabValue === 0 ? '进行中的任务' : '已完成的任务'} ({filteredTasks.length})
            </Typography>
          </Box>
          <TableContainer component={Paper} variant="outlined">
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>任务标题</TableCell>
                  <TableCell>客户</TableCell>
                  <TableCell>预算</TableCell>
                  <TableCell>进度</TableCell>
                  <TableCell>优先级</TableCell>
                  <TableCell>截止时间</TableCell>
                  <TableCell>操作</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredTasks.map((task) => (
                  <TableRow key={task.id} hover>
                    <TableCell>
                      <Box>
                        <Typography variant="body2" sx={{ fontWeight: 500, mb: 0.5 }}>
                          {task.title}
                        </Typography>
                        <Box sx={{ display: 'flex', gap: 0.5, mb: 0.5 }}>
                          {task.tags.slice(0, 3).map((tag, index) => (
                            <Chip
                              key={index}
                              label={tag}
                              size="small"
                              variant="outlined"
                              sx={{ fontSize: '0.7rem', height: 20 }}
                            />
                          ))}
                        </Box>
                        <Typography variant="caption" color="text.secondary">
                          {task.wordCount} 字 • 接单时间: {task.acceptTime}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" sx={{ fontWeight: 500 }}>
                        {task.client}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <AttachMoney sx={{ fontSize: 16, color: 'success.main', mr: 0.5 }} />
                        <Typography variant="body2" sx={{ fontWeight: 600, color: 'success.main' }}>
                          {task.budget}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ width: '100%' }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                          <Typography variant="body2" sx={{ fontWeight: 500, mr: 1 }}>
                            {task.progress}%
                          </Typography>
                          <Chip
                            label={getStatusLabel(task.status)}
                            color={getStatusColor(task.status)}
                            size="small"
                          />
                        </Box>
                        <LinearProgress
                          variant="determinate"
                          value={task.progress}
                          sx={{ height: 6, borderRadius: 3 }}
                        />
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={getPriorityLabel(task.priority)}
                        color={getPriorityColor(task.priority)}
                        size="small"
                        variant="outlined"
                      />
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <AccessTime sx={{ fontSize: 16, color: 'text.secondary', mr: 0.5 }} />
                        <Typography variant="body2" color="text.secondary">
                          {new Date(task.deadline).toLocaleDateString()}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', gap: 1 }}>
                        <IconButton
                          size="small"
                          onClick={() => handleViewTask(task)}
                          color="primary"
                        >
                          <Visibility fontSize="small" />
                        </IconButton>
                        {task.status === 'in_progress' && (
                          <Button
                            size="small"
                            variant="contained"
                            onClick={() => handleViewTask(task)}
                            startIcon={<Upload />}
                            sx={{ minWidth: 80 }}
                          >
                            提交
                          </Button>
                        )}
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* Task Detail Dialog */}
      <Dialog
        open={taskDialogOpen}
        onClose={() => setTaskDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Typography variant="h5" sx={{ fontWeight: 600 }}>
            {selectedTask?.status === 'in_progress' ? '提交稿件' : '任务详情'}
          </Typography>
        </DialogTitle>
        <DialogContent>
          {selectedTask && (
            <Box sx={{ mt: 2 }}>
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                    {selectedTask.title}
                  </Typography>
                  <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
                    <Chip
                      label={selectedTask.type === 'direct' ? '直发稿件' : '创作需求'}
                      size="small"
                      color="primary"
                    />
                    <Chip label={selectedTask.client} size="small" variant="outlined" />
                    <Chip
                      label={getPriorityLabel(selectedTask.priority)}
                      color={getPriorityColor(selectedTask.priority)}
                      size="small"
                    />
                    <Chip
                      label={`¥${selectedTask.budget}`}
                      color="success"
                      size="small"
                    />
                  </Box>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" color="text.secondary" sx={{ mb: 1 }}>
                    任务要求
                  </Typography>
                  <Box sx={{
                    p: 2,
                    bgcolor: 'grey.50',
                    borderRadius: 1,
                    maxHeight: 200,
                    overflow: 'auto'
                  }}>
                    <Typography variant="body2">
                      {selectedTask.requirements}
                    </Typography>
                  </Box>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" color="text.secondary" sx={{ mb: 1 }}>
                    任务信息
                  </Typography>
                  <Box sx={{ p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                      <strong>字数要求：</strong>{selectedTask.wordCount}
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                      <strong>预算：</strong>¥{selectedTask.budget}
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                      <strong>截止时间：</strong>{new Date(selectedTask.deadline).toLocaleString()}
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                      <strong>接单时间：</strong>{selectedTask.acceptTime}
                    </Typography>
                    <Typography variant="body2">
                      <strong>标签：</strong>{selectedTask.tags.join(', ')}
                    </Typography>
                    {selectedTask.channels && (
                      <Typography variant="body2" sx={{ mt: 1 }}>
                        <strong>发布渠道：</strong>{selectedTask.channels.join(', ')}
                      </Typography>
                    )}
                  </Box>
                </Grid>

                {selectedTask.status === 'in_progress' && (
                  <>
                    <Grid item xs={12}>
                      <Divider sx={{ my: 2 }} />
                      <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
                        提交稿件
                      </Typography>
                    </Grid>

                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="稿件标题"
                        value={deliveryForm.title}
                        onChange={(e) => setDeliveryForm(prev => ({ ...prev, title: e.target.value }))}
                        sx={{ mb: 2 }}
                      />
                    </Grid>

                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        multiline
                        rows={8}
                        label="稿件内容"
                        value={deliveryForm.content}
                        onChange={(e) => setDeliveryForm(prev => ({ ...prev, content: e.target.value }))}
                        placeholder="请输入稿件正文内容..."
                        sx={{ mb: 2 }}
                      />
                    </Grid>

                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        multiline
                        rows={3}
                        label="交付说明"
                        value={deliveryForm.note}
                        onChange={(e) => setDeliveryForm(prev => ({ ...prev, note: e.target.value }))}
                        placeholder="向客户说明稿件的特点和亮点..."
                      />
                    </Grid>

                    <Grid item xs={12}>
                      <Box sx={{
                        border: '2px dashed #ccc',
                        borderRadius: 1,
                        p: 3,
                        textAlign: 'center',
                        cursor: 'pointer',
                        '&:hover': { borderColor: 'primary.main' }
                      }}>
                        <CloudUpload sx={{ fontSize: 48, color: 'text.secondary', mb: 1 }} />
                        <Typography variant="body2" color="text.secondary">
                          点击上传附件（图片、文档等）
                        </Typography>
                      </Box>
                    </Grid>
                  </>
                )}
              </Grid>
            </Box>
          )}
        </DialogContent>
        <DialogActions sx={{ p: 3 }}>
          <Button onClick={() => setTaskDialogOpen(false)}>
            取消
          </Button>
          {selectedTask && selectedTask.status === 'in_progress' && (
            <Button
              onClick={handleDeliverTask}
              variant="contained"
              startIcon={<Send />}
              disabled={!deliveryForm.title.trim() || !deliveryForm.content.trim()}
            >
              提交稿件
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </Box>
  );
}

export default TaskManagement;
