import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  Button,
  IconButton,
  TextField,
  InputAdornment,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  MenuItem,
  CircularProgress,
  Alert,
  Chip,
  Tooltip,
  Menu,
  ListItemIcon,
  ListItemText,
  Snackbar,
  Avatar,
  Tabs,
  Tab,
  FormControl,
  InputLabel,
  Select,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
} from '@mui/material';
import {
  Search,
  Add,
  Edit,
  Delete,
  MoreVert,
  Category,
  CheckCircle,
  Warning,
  ToggleOn,
  ToggleOff,
  AccountTree,
  Folder,
  FolderOpen,
} from '@mui/icons-material';
import apiService from '../../services/api';

function ChannelCategoryManagement() {
  const [loading, setLoading] = useState(true);
  const [categories, setCategories] = useState([]);
  const [filteredCategories, setFilteredCategories] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [categoryDialogOpen, setCategoryDialogOpen] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [anchorEl, setAnchorEl] = useState(null);
  const [actionCategory, setActionCategory] = useState(null);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });

  // 标签页状态
  const [currentTab, setCurrentTab] = useState(0); // 0=主分类, 1=子分类
  const [mainCategories, setMainCategories] = useState([]);
  const [subCategories, setSubCategories] = useState([]);

  const [newCategory, setNewCategory] = useState({
    name: '',
    description: '',
    parent_id: '', // 新增父分类ID字段
    category_level: 1, // 新增分类层级字段
  });

  const loadCategories = useCallback(async () => {
    try {
      setLoading(true);
      const response = await apiService.getChannelCategories();
      if (response && response.success) {
        const categoriesData = Array.isArray(response.data.categories) ? response.data.categories : [];
        setCategories(categoriesData);

        // 分离主分类和子分类
        const mainCats = categoriesData.filter(cat => cat.category_level === 1 || !cat.parent_id);
        const subCats = categoriesData.filter(cat => cat.category_level === 2 || cat.parent_id);

        setMainCategories(mainCats);
        setSubCategories(subCats);
        setFilteredCategories(currentTab === 0 ? mainCats : subCats);
      } else {
        setCategories([]);
        setMainCategories([]);
        setSubCategories([]);
        setFilteredCategories([]);
        const errorMessage = response?.message || '获取分类数据失败';
        showSnackbar(errorMessage, 'error');
      }
    } catch (error) {
      console.error('加载分类数据失败:', error);
      setCategories([]);
      setMainCategories([]);
      setSubCategories([]);
      setFilteredCategories([]);

      let errorMessage = '加载分类数据失败';
      if (error.response) {
        if (error.response.status === 403) {
          errorMessage = '权限不足，无法获取分类数据';
        } else if (error.response.status >= 500) {
          errorMessage = '服务器错误，请稍后重试';
        }
      } else if (error.request) {
        errorMessage = '网络连接失败，请检查网络';
      }

      showSnackbar(errorMessage, 'error');
    } finally {
      setLoading(false);
    }
  }, [currentTab]);

  // 加载渠道分类数据
  useEffect(() => {
    loadCategories();
  }, [loadCategories]);

  // 标签页切换处理
  const handleTabChange = (event, newValue) => {
    setCurrentTab(newValue);
    setSearchTerm(''); // 切换标签页时清空搜索
  };

  // 根据当前标签页更新显示的分类
  useEffect(() => {
    const currentCategories = currentTab === 0 ? mainCategories : subCategories;
    let filtered = currentCategories;

    if (searchTerm) {
      filtered = filtered.filter(category =>
        category.category_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (category.category_description && category.category_description.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }
    setFilteredCategories(filtered);
  }, [mainCategories, subCategories, currentTab, searchTerm]);

  const showSnackbar = (message, severity = 'success') => {
    setSnackbar({ open: true, message, severity });
  };

  const handleSnackbarClose = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  // 打开创建/编辑对话框
  const handleOpenDialog = (category = null) => {
    if (category) {
      setEditMode(true);
      setSelectedCategory(category);
      setNewCategory({
        name: category.category_name,
        description: category.category_description,
        parent_id: category.parent_id || '',
        category_level: category.category_level || 1,
      });
    } else {
      setEditMode(false);
      setSelectedCategory(null);
      setNewCategory({
        name: '',
        description: '',
        parent_id: currentTab === 1 ? '' : '', // 如果在子分类标签页，需要选择父分类
        category_level: currentTab === 0 ? 1 : 2, // 根据当前标签页设置层级
      });
    }
    setCategoryDialogOpen(true);
  };

  // 关闭对话框
  const handleCloseDialog = () => {
    setCategoryDialogOpen(false);
    setEditMode(false);
    setSelectedCategory(null);
    setNewCategory({
      name: '',
      description: '',
      parent_id: '',
      category_level: 1,
    });
  };

  // 生成分类代码的辅助函数
  const generateCategoryCode = (name) => {
    if (!name) return '';

    // 对于中文字符，使用拼音或者时间戳
    const timestamp = Date.now().toString().slice(-6); // 取时间戳后6位
    let code = name.toLowerCase()
      .replace(/\s+/g, '_') // 空格替换为下划线
      .replace(/[^\w\u4e00-\u9fa5]/g, ''); // 保留字母、数字、下划线和中文字符

    // 如果包含中文字符，添加时间戳确保唯一性
    if (/[\u4e00-\u9fa5]/.test(code)) {
      code = `category_${timestamp}`;
    }

    // 如果代码为空或太短，使用时间戳
    if (!code || code.length < 2) {
      code = `category_${timestamp}`;
    }

    return code;
  };

  // 创建或更新分类
  const handleSaveCategory = async () => {
    try {
      // 转换前端字段名到后端期望的字段名
      const apiData = {
        category_name: newCategory.name,
        category_code: generateCategoryCode(newCategory.name), // 使用改进的代码生成逻辑
        category_description: newCategory.description,
        parent_id: newCategory.parent_id || null,
        category_level: newCategory.category_level,
        sort_order: 0, // 默认排序
        is_active: true // 确保包含is_active字段
      };

      console.log('发送的API数据:', apiData); // 调试日志

      if (editMode && selectedCategory) {
        const response = await apiService.updateChannelCategory(selectedCategory.id, apiData);
        if (response && response.success) {
          showSnackbar('分类更新成功');
          await loadCategories();
          handleCloseDialog();
        } else {
          const errorMessage = response?.message || '更新失败，请重试';
          showSnackbar(`分类更新失败: ${errorMessage}`, 'error');
        }
      } else {
        const response = await apiService.createChannelCategory(apiData);
        if (response && response.success) {
          showSnackbar('分类创建成功');
          await loadCategories();
          handleCloseDialog();
        } else {
          const errorMessage = response?.message || '创建失败，请重试';
          showSnackbar(`分类创建失败: ${errorMessage}`, 'error');
        }
      }
    } catch (error) {
      console.error('保存分类错误:', error); // 调试日志

      let errorMessage = editMode ? '更新分类失败' : '创建分类失败';

      // 处理不同类型的错误
      if (error.response) {
        if (error.response.data && error.response.data.detail) {
          // 处理 FastAPI 验证错误
          if (Array.isArray(error.response.data.detail)) {
            const fieldMap = {
              'category_name': '分类名称',
              'category_code': '分类代码',
              'category_description': '分类描述',
              'category_level': '分类层级',
              'sort_order': '排序顺序',
              'is_active': '启用状态'
            };
            const validationErrors = error.response.data.detail.map(err => {
              const fieldName = fieldMap[err.loc[err.loc.length - 1]] || err.loc.join('.');
              return `${fieldName}: ${err.msg}`;
            }).join('; ');
            errorMessage = `${editMode ? '更新' : '创建'}分类失败: ${validationErrors}`;
          } else {
            errorMessage = `${editMode ? '更新' : '创建'}分类失败: ${error.response.data.detail}`;
          }
        } else if (error.response.status === 400) {
          errorMessage = '请求参数错误，请检查输入信息';
        } else if (error.response.status === 403) {
          errorMessage = '权限不足';
        } else if (error.response.status === 422) {
          errorMessage = '数据验证失败，请检查输入信息';
        } else if (error.response.status >= 500) {
          errorMessage = '服务器错误，请稍后重试';
        }
      } else if (error.request) {
        errorMessage = '网络连接失败，请检查网络';
      }

      showSnackbar(errorMessage, 'error');
    }
  };

  // 删除分类
  const handleDeleteCategory = async (categoryId) => {
    if (window.confirm('确定要删除这个分类吗？删除后无法恢复。')) {
      try {
        const response = await apiService.deleteChannelCategory(categoryId);
        if (response && response.success) {
          showSnackbar('分类删除成功');
          await loadCategories();
        } else {
          const errorMessage = response?.message || '删除失败，请重试';
          showSnackbar(`删除分类失败: ${errorMessage}`, 'error');
        }
      } catch (error) {
        console.error('删除分类失败:', error);
        let errorMessage = '删除分类失败';

        // 处理不同类型的错误
        if (error.response) {
          if (error.response.data && error.response.data.detail) {
            errorMessage = `删除分类失败: ${error.response.data.detail}`;
          } else if (error.response.status === 404) {
            errorMessage = '分类不存在';
          } else if (error.response.status === 403) {
            errorMessage = '权限不足';
          } else if (error.response.status >= 500) {
            errorMessage = '服务器错误，请稍后重试';
          }
        } else if (error.request) {
          errorMessage = '网络连接失败，请检查网络';
        }

        showSnackbar(errorMessage, 'error');
      }
    }
    handleMenuClose();
  };

  // 切换分类状态
  const handleToggleCategoryStatus = async (category) => {
    const newStatus = !category.is_active;
    const actionText = newStatus ? '启用' : '禁用';

    try {
      const response = await apiService.updateChannelCategory(category.id, { is_active: newStatus });
      if (response && response.success) {
        showSnackbar(`分类"${category.category_name}"已${actionText}`, 'success');
        await loadCategories();
      } else {
        const errorMessage = response?.message || '操作失败，请重试';
        showSnackbar(`${actionText}分类失败: ${errorMessage}`, 'error');
      }
    } catch (error) {

      let errorMessage = `${actionText}分类失败`;

      // 处理不同类型的错误
      if (error.response) {
        // 服务器返回错误响应
        if (error.response.data && error.response.data.detail) {
          errorMessage = `${actionText}分类失败: ${error.response.data.detail}`;
        } else if (error.response.status === 404) {
          errorMessage = '分类不存在';
        } else if (error.response.status === 403) {
          errorMessage = '权限不足';
        } else if (error.response.status >= 500) {
          errorMessage = '服务器错误，请稍后重试';
        }
      } else if (error.request) {
        // 网络错误
        errorMessage = '网络连接失败，请检查网络';
      }

      showSnackbar(errorMessage, 'error');
    }
    handleMenuClose();
  };

  const handleMenuClick = (event, category) => {
    setAnchorEl(event.currentTarget);
    setActionCategory(category);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setActionCategory(null);
  };



  // 格式化日期
  const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ width: '100%', p: 3, backgroundColor: '#ffffff', minHeight: '100vh' }}>
      {/* 页面标题和操作按钮区域 */}
      <Box sx={{
        backgroundColor: '#fff',
        borderRadius: 3,
        border: '1px solid #e0e0e0',
        p: 4,
        mb: 3,
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
      }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Category sx={{ fontSize: 32, color: '#1976d2' }} />
            <Box>
              <Typography variant="h4" sx={{ fontWeight: 700, color: '#1a1a1a', mb: 0.5 }}>
                渠道分类管理
              </Typography>
              <Typography variant="body1" sx={{ color: '#666' }}>
                管理渠道分类，创建、编辑和删除分类
              </Typography>
            </Box>
          </Box>
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={() => handleOpenDialog()}
            sx={{ borderRadius: 2 }}
          >
            添加分类
          </Button>
        </Box>
      </Box>

      {/* 标签页区域 */}
      <Card sx={{ borderRadius: 3, boxShadow: '0 2px 8px rgba(0,0,0,0.1)', mb: 3 }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs
            value={currentTab}
            onChange={handleTabChange}
            sx={{ px: 3, pt: 2 }}
          >
            <Tab
              icon={<Folder />}
              label={`主分类 (${mainCategories.length})`}
              sx={{
                minHeight: 48,
                '& .MuiTab-iconWrapper': { mb: 0.5 }
              }}
            />
            <Tab
              icon={<FolderOpen />}
              label={`子分类 (${subCategories.length})`}
              sx={{
                minHeight: 48,
                '& .MuiTab-iconWrapper': { mb: 0.5 }
              }}
            />
          </Tabs>
        </Box>

        {/* 搜索区域 */}
        <CardContent>
          <Grid container spacing={3} alignItems="center">
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                placeholder="搜索分类名称或描述..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Search />
                    </InputAdornment>
                  ),
                }}
                sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
              />
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* 分类列表 */}
      <Card sx={{ borderRadius: 3, boxShadow: '0 2px 8px rgba(0,0,0,0.1)' }}>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant="h6" sx={{ fontWeight: 600 }}>
              分类列表 ({filteredCategories.length})
            </Typography>
          </Box>

          {/* 分类表格布局 */}
          <TableContainer component={Paper} sx={{ borderRadius: 2, border: '1px solid #e0e0e0' }}>
            <Table sx={{ minWidth: 650 }} aria-label="分类列表">
              <TableHead>
                <TableRow sx={{ backgroundColor: '#f5f5f5' }}>
                  <TableCell sx={{ fontWeight: 600, fontSize: '0.875rem' }}>分类信息</TableCell>
                  <TableCell sx={{ fontWeight: 600, fontSize: '0.875rem' }}>分类代码</TableCell>
                  <TableCell sx={{ fontWeight: 600, fontSize: '0.875rem' }}>描述</TableCell>
                  <TableCell sx={{ fontWeight: 600, fontSize: '0.875rem' }}>状态</TableCell>
                  <TableCell sx={{ fontWeight: 600, fontSize: '0.875rem' }}>关联渠道</TableCell>
                  <TableCell sx={{ fontWeight: 600, fontSize: '0.875rem' }}>创建时间</TableCell>
                  <TableCell sx={{ fontWeight: 600, fontSize: '0.875rem' }}>更新时间</TableCell>
                  <TableCell sx={{ fontWeight: 600, fontSize: '0.875rem', textAlign: 'center' }}>操作</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredCategories.map((category) => (
                  <TableRow
                    key={category.id}
                    sx={{
                      '&:hover': { backgroundColor: '#f9f9f9' },
                      '&:last-child td, &:last-child th': { border: 0 }
                    }}
                  >
                    {/* 分类信息列 */}
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                        <Avatar sx={{
                          bgcolor: category.category_level === 2 ? '#ff9800' : '#1976d2',
                          width: 40,
                          height: 40
                        }}>
                          {category.category_level === 2 ? <FolderOpen /> : <Folder />}
                        </Avatar>
                        <Box>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
                            <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                              {category.category_name}
                            </Typography>
                            <Chip
                              label={category.category_level === 2 ? '子分类' : '主分类'}
                              size="small"
                              color={category.category_level === 2 ? 'warning' : 'primary'}
                              variant="outlined"
                            />
                          </Box>
                          {/* 显示父分类信息 */}
                          {category.category_level === 2 && category.parent_id && (
                            <Typography variant="caption" color="text.secondary">
                              父分类: {mainCategories.find(p => p.id === category.parent_id)?.category_name || '未知'}
                            </Typography>
                          )}
                        </Box>
                      </Box>
                    </TableCell>

                    {/* 分类代码列 */}
                    <TableCell>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', color: '#666' }}>
                        {category.category_code}
                      </Typography>
                    </TableCell>

                    {/* 描述列 */}
                    <TableCell sx={{ maxWidth: 300 }}>
                      <Typography variant="body2" color="text.secondary" sx={{
                        display: '-webkit-box',
                        WebkitLineClamp: 2,
                        WebkitBoxOrient: 'vertical',
                        overflow: 'hidden',
                        lineHeight: 1.4
                      }}>
                        {category.category_description || '暂无描述'}
                      </Typography>
                    </TableCell>

                    {/* 状态列 */}
                    <TableCell>
                      <Chip
                        label={category.is_active ? '启用' : '禁用'}
                        color={category.is_active ? 'success' : 'error'}
                        size="small"
                        icon={category.is_active ? <CheckCircle /> : <Warning />}
                      />
                    </TableCell>

                    {/* 关联渠道列 */}
                    <TableCell>
                      <Typography variant="body2">
                        {category.provider_count || 0}
                      </Typography>
                    </TableCell>

                    {/* 创建时间列 */}
                    <TableCell>
                      <Typography variant="body2" color="text.secondary">
                        {formatDate(category.created_at)}
                      </Typography>
                    </TableCell>

                    {/* 更新时间列 */}
                    <TableCell>
                      <Typography variant="body2" color="text.secondary">
                        {formatDate(category.updated_at)}
                      </Typography>
                    </TableCell>

                    {/* 操作列 */}
                    <TableCell>
                      <Box sx={{ display: 'flex', justifyContent: 'center', gap: 1 }}>
                        <Tooltip title="编辑">
                          <IconButton
                            size="small"
                            onClick={() => handleOpenDialog(category)}
                            sx={{
                              bgcolor: 'primary.main',
                              color: 'white',
                              '&:hover': { bgcolor: 'primary.dark' }
                            }}
                          >
                            <Edit fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="更多操作">
                          <IconButton
                            size="small"
                            onClick={(e) => handleMenuClick(e, category)}
                            sx={{
                              bgcolor: 'grey.100',
                              '&:hover': { bgcolor: 'grey.200' }
                            }}
                          >
                            <MoreVert fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>

          {/* 空状态 */}
          {filteredCategories.length === 0 && !loading && (
            <Box sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              py: 8
            }}>
              <Category sx={{ fontSize: 64, color: 'grey.300', mb: 2 }} />
              <Typography variant="h6" color="text.secondary" sx={{ mb: 1 }}>
                暂无分类数据
              </Typography>
              <Typography variant="body2" color="text.secondary">
                点击上方"添加分类"按钮创建第一个分类
              </Typography>
            </Box>
          )}
        </CardContent>
      </Card>

      {/* 操作菜单 */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => handleOpenDialog(actionCategory)}>
          <ListItemIcon>
            <Edit fontSize="small" />
          </ListItemIcon>
          <ListItemText>编辑分类</ListItemText>
        </MenuItem>
        <MenuItem onClick={() => handleToggleCategoryStatus(actionCategory)}>
          <ListItemIcon>
            {actionCategory?.is_active ? (
              <ToggleOff fontSize="small" color="warning" />
            ) : (
              <ToggleOn fontSize="small" color="success" />
            )}
          </ListItemIcon>
          <ListItemText>
            {actionCategory?.is_active ? '禁用分类' : '启用分类'}
          </ListItemText>
        </MenuItem>
        <MenuItem
          onClick={() => handleDeleteCategory(actionCategory?.id)}
          sx={{ color: 'error.main' }}
        >
          <ListItemIcon>
            <Delete fontSize="small" color="error" />
          </ListItemIcon>
          <ListItemText>删除分类</ListItemText>
        </MenuItem>
      </Menu>

      {/* 创建/编辑分类对话框 */}
      <Dialog
        open={categoryDialogOpen}
        onClose={handleCloseDialog}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          {editMode ? '编辑分类' : '创建分类'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <TextField
              fullWidth
              label="分类名称"
              value={newCategory.name}
              onChange={(e) => setNewCategory({ ...newCategory, name: e.target.value })}
              sx={{ mb: 2 }}
              required
            />

            {/* 父分类选择 - 只在创建子分类时显示 */}
            {newCategory.category_level === 2 && (
              <FormControl fullWidth sx={{ mb: 2 }}>
                <InputLabel>父分类</InputLabel>
                <Select
                  value={newCategory.parent_id}
                  label="父分类"
                  onChange={(e) => setNewCategory({ ...newCategory, parent_id: e.target.value })}
                  required
                >
                  <MenuItem value="">
                    <em>请选择父分类</em>
                  </MenuItem>
                  {mainCategories.map((category) => (
                    <MenuItem key={category.id} value={category.id}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Folder fontSize="small" />
                        {category.category_name}
                      </Box>
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            )}

            <TextField
              fullWidth
              label="分类描述"
              multiline
              rows={3}
              value={newCategory.description}
              onChange={(e) => setNewCategory({ ...newCategory, description: e.target.value })}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>取消</Button>
          <Button
            onClick={handleSaveCategory}
            variant="contained"
            disabled={!newCategory.name.trim()}
          >
            {editMode ? '更新' : '创建'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* 消息提示 */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert
          onClose={handleSnackbarClose}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
}

export default ChannelCategoryManagement;
