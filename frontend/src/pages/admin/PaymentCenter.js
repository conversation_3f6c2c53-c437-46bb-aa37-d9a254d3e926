import React, { useState, useEffect, use<PERSON><PERSON>back } from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Paper,
  Button,
  IconButton,
  Badge,
  Tabs,
  Tab,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Checkbox,
  Toolbar,
  Tooltip,
  Alert,
  AlertTitle,
  CircularProgress,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction,
  Divider,
  InputAdornment,
  Stack,
  Avatar,
  LinearProgress,
  Collapse,
  FormControlLabel,
  Switch,
  Radio,
  RadioGroup,
} from '@mui/material';
import {
  Payment,
  CreditCard,
  AccountBalance,
  AttachMoney,
  TrendingUp,
  TrendingDown,
  CheckCircle,
  Cancel,
  Warning,
  Error,
  Refresh,
  Download,
  Search,
  FilterList,
  MoreVert,
  Receipt,
  Assignment,
  Timeline,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Paid,
  MoneyOff,
  AccountBalanceWallet,
  QrCode,
  Smartphone,
  Computer,
  Close,
  ThumbUp,
  ThumbDown,
  History,
  Assessment,
  DateRange,
  Today,
  LocalAtm,
  Check,
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { format, startOfDay, endOfDay, startOfMonth, endOfMonth } from 'date-fns';
import { useSnackbar } from 'notistack';

import paymentService from '../../services/paymentService';



// Tab panel component
function TabPanel({ children, value, index, ...other }) {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`payment-tabpanel-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

// Payment status color mapping
const getPaymentStatusColor = (status) => {
  const statusColors = {
    pending: 'warning',
    processing: 'info',
    success: 'success',
    failed: 'error',
    cancelled: 'default',
    refunded: 'secondary',
  };
  return statusColors[status] || 'default';
};

// Payment method icon mapping
const getPaymentMethodIcon = (method) => {
  const methodIcons = {
    credit_card: <CreditCard />,
    debit_card: <CreditCard />,
    bank_transfer: <AccountBalance />,
    wallet: <AccountBalanceWallet />,
    cash: <LocalAtm />,
    qr_code: <QrCode />,
    mobile: <Smartphone />,
    online: <Computer />,
  };
  return methodIcons[method] || <Payment />;
};

// Refund status color mapping
const getRefundStatusColor = (status) => {
  const statusColors = {
    pending: 'warning',
    approved: 'info',
    processing: 'info',
    completed: 'success',
    rejected: 'error',
    cancelled: 'default',
  };
  return statusColors[status] || 'default';
};

function PaymentCenter() {
  const { enqueueSnackbar } = useSnackbar();
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(false);
  
  // Payment records state
  const [payments, setPayments] = useState([]);
  const [selectedPayments, setSelectedPayments] = useState([]);
  const [paymentPage, setPaymentPage] = useState(0);
  const [paymentRowsPerPage, setPaymentRowsPerPage] = useState(10);
  const [totalPayments, setTotalPayments] = useState(0);
  
  // Refund state
  const [refunds, setRefunds] = useState([]);
  const [selectedRefunds, setSelectedRefunds] = useState([]);
  const [refundPage, setRefundPage] = useState(0);
  const [refundRowsPerPage, setRefundRowsPerPage] = useState(10);
  const [totalRefunds, setTotalRefunds] = useState(0);
  

  
  // Filters
  const [paymentFilters, setPaymentFilters] = useState({
    dateFrom: startOfMonth(new Date()),
    dateTo: endOfMonth(new Date()),
    method: 'all',
    status: 'all',
    search: '',
  });
  
  const [refundFilters, setRefundFilters] = useState({
    status: 'pending',
    dateFrom: null,
    dateTo: null,
  });
  
  // Dialogs
  const [paymentDetailsDialog, setPaymentDetailsDialog] = useState(false);
  const [selectedPaymentDetails, setSelectedPaymentDetails] = useState(null);
  const [refundDialog, setRefundDialog] = useState(false);
  const [refundFormData, setRefundFormData] = useState({
    payment_id: '',
    amount: 0,
    reason: '',
  });

  
  // Statistics
  const [statistics, setStatistics] = useState({
    todayRevenue: 0,
    pendingRefunds: 0,
    completedRefunds: 0,
    successRate: 0,
    totalTransactions: 0,
    averageAmount: 0,
  });
  


  // Load payment records
  const loadPayments = useCallback(async () => {
    setLoading(true);
    try {
      const params = {
        page: paymentPage + 1,
        limit: paymentRowsPerPage,
        ...paymentFilters,
      };
      
      const response = await paymentService.getPayments(params);
      setPayments(response.data || []);
      setTotalPayments(response.total || 0);
    } catch (error) {
      enqueueSnackbar('Failed to load payment records', { variant: 'error' });
      console.error('Error loading payments:', error);
    } finally {
      setLoading(false);
    }
  }, [paymentPage, paymentRowsPerPage, paymentFilters, enqueueSnackbar]);

  // Load refunds
  const loadRefunds = useCallback(async () => {
    setLoading(true);
    try {
      const params = {
        page: refundPage + 1,
        limit: refundRowsPerPage,
        ...refundFilters,
      };
      
      const response = await paymentService.getRefunds(params);
      setRefunds(response.data || []);
      setTotalRefunds(response.total || 0);
    } catch (error) {
      enqueueSnackbar('Failed to load refunds', { variant: 'error' });
      console.error('Error loading refunds:', error);
    } finally {
      setLoading(false);
    }
  }, [refundPage, refundRowsPerPage, refundFilters, enqueueSnackbar]);



  // Load statistics
  const loadStatistics = async () => {
    try {
      const stats = await paymentService.getPaymentStatistics({
        from: startOfDay(new Date()),
        to: endOfDay(new Date()),
      });
      setStatistics(stats);
    } catch (error) {
      console.error('Error loading statistics:', error);
    }
  };



  // Initial load based on tab
  useEffect(() => {
    switch (tabValue) {
      case 0: // Payment Records
        loadPayments();
        break;
      case 1: // Refund Management
        loadRefunds();
        break;
    }
  }, [tabValue, loadPayments, loadRefunds]);

  // Load statistics on mount
  useEffect(() => {
    loadStatistics();
  }, []);

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
    setSelectedPayments([]);
    setSelectedRefunds([]);
  };

  // View payment details
  const handleViewPaymentDetails = async (payment) => {
    try {
      const details = await paymentService.getPaymentById(payment.id);
      setSelectedPaymentDetails(details);
      setPaymentDetailsDialog(true);
    } catch (error) {
      enqueueSnackbar('Failed to load payment details', { variant: 'error' });
    }
  };

  // Process refund
  const handleCreateRefund = async () => {
    try {
      await paymentService.createRefund(refundFormData);
      enqueueSnackbar('Refund request created successfully', { variant: 'success' });
      setRefundDialog(false);
      setRefundFormData({ payment_id: '', amount: 0, reason: '' });
      loadRefunds();
    } catch (error) {
      enqueueSnackbar('Failed to create refund', { variant: 'error' });
    }
  };

  // Approve refund
  const handleApproveRefund = async (refundId) => {
    try {
      await paymentService.approveRefund(refundId);
      enqueueSnackbar('Refund approved successfully', { variant: 'success' });
      loadRefunds();
    } catch (error) {
      enqueueSnackbar('Failed to approve refund', { variant: 'error' });
    }
  };

  // Reject refund
  const handleRejectRefund = async (refundId, reason) => {
    try {
      await paymentService.rejectRefund(refundId, reason);
      enqueueSnackbar('Refund rejected', { variant: 'info' });
      loadRefunds();
    } catch (error) {
      enqueueSnackbar('Failed to reject refund', { variant: 'error' });
    }
  };

  // Batch approve refunds
  const handleBatchApprove = async () => {
    if (selectedRefunds.length === 0) {
      enqueueSnackbar('Please select refunds to approve', { variant: 'warning' });
      return;
    }
    
    try {
      await paymentService.batchApproveRefunds(selectedRefunds);
      enqueueSnackbar(`${selectedRefunds.length} refunds approved`, { variant: 'success' });
      setSelectedRefunds([]);
      loadRefunds();
    } catch (error) {
      enqueueSnackbar('Failed to approve refunds', { variant: 'error' });
    }
  };



  // Export payments
  const handleExportPayments = async () => {
    try {
      const blob = await paymentService.exportPayments(paymentFilters);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `payments_${format(new Date(), 'yyyy-MM-dd')}.csv`;
      a.click();
      window.URL.revokeObjectURL(url);
      enqueueSnackbar('Payments exported successfully', { variant: 'success' });
    } catch (error) {
      enqueueSnackbar('Failed to export payments', { variant: 'error' });
    }
  };

  // Reconcile payments
  const handleReconcile = async () => {
    try {
      const result = await paymentService.reconcilePayments({
        from: paymentFilters.dateFrom,
        to: paymentFilters.dateTo,
      });
      enqueueSnackbar(`Reconciliation completed: ${result.matched} matched, ${result.mismatched} mismatched`, 
        { variant: result.mismatched > 0 ? 'warning' : 'success' });
    } catch (error) {
      enqueueSnackbar('Reconciliation failed', { variant: 'error' });
    }
  };

  // Statistics Card Component
  const StatCard = ({ title, value, icon, color, subtitle, trend }) => (
    <Card sx={{ height: '100%' }}>
      <CardContent>
        <Box display="flex" justifyContent="space-between" alignItems="flex-start">
          <Box>
            <Typography color="textSecondary" gutterBottom variant="subtitle2">
              {title}
            </Typography>
            <Typography variant="h4" component="div">
              {value}
            </Typography>
            {subtitle && (
              <Typography variant="body2" color="textSecondary" mt={1}>
                {subtitle}
              </Typography>
            )}
            {trend !== undefined && (
              <Box display="flex" alignItems="center" mt={1}>
                {trend > 0 ? (
                  <TrendingUp color="success" fontSize="small" />
                ) : (
                  <TrendingDown color="error" fontSize="small" />
                )}
                <Typography
                  variant="body2"
                  color={trend > 0 ? 'success.main' : 'error.main'}
                  ml={0.5}
                >
                  {Math.abs(trend)}%
                </Typography>
              </Box>
            )}
          </Box>
          <Avatar sx={{ bgcolor: color, width: 48, height: 48 }}>
            {icon}
          </Avatar>
        </Box>
      </CardContent>
    </Card>
  );



  return (
    <Container maxWidth={false} sx={{ mt: 3, mb: 3 }}>
      {/* Page Header */}
      <Box mb={3}>
        <Typography variant="h4" gutterBottom>
          支付管理中心
        </Typography>
        <Typography variant="body2" color="textSecondary">
          全面的支付处理和退款管理
        </Typography>
      </Box>

      {/* Statistics Cards */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="今日收入"
            value={`$${statistics.todayRevenue.toFixed(2)}`}
            icon={<AttachMoney />}
            color="success.main"
            trend={15}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="待处理退款"
            value={statistics.pendingRefunds}
            icon={<MoneyOff />}
            color="warning.main"
            subtitle="等待审批"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="已完成退款"
            value={statistics.completedRefunds}
            icon={<CheckCircle />}
            color="info.main"
            subtitle="本月"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="成功率"
            value={`${statistics.successRate}%`}
            icon={<TrendingUp />}
            color="primary.main"
            trend={3}
          />
        </Grid>
      </Grid>

      {/* Main Content */}
      <Paper sx={{ width: '100%' }}>
        {/* Tabs */}
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={handleTabChange}>
            <Tab
              label="支付记录"
              icon={<Payment />}
              iconPosition="start"
            />
            <Tab
              label="退款管理"
              icon={
                <Badge badgeContent={statistics.pendingRefunds} color="warning">
                  <MoneyOff />
                </Badge>
              }
              iconPosition="start"
            />
          </Tabs>
        </Box>

        {/* Tab Panel 0: Payment Records */}
        <TabPanel value={tabValue} index={0}>
          {/* Filters */}
          <Paper elevation={0} sx={{ p: 2, mb: 2, bgcolor: 'grey.50' }}>
            <Grid container spacing={2} alignItems="center">
              <Grid item xs={12} md={2}>
                <LocalizationProvider dateAdapter={AdapterDateFns}>
                  <DatePicker
                    label="开始日期"
                    value={paymentFilters.dateFrom}
                    onChange={(date) => setPaymentFilters({ ...paymentFilters, dateFrom: date })}
                    renderInput={(params) => <TextField {...params} size="small" fullWidth />}
                  />
                </LocalizationProvider>
              </Grid>
              <Grid item xs={12} md={2}>
                <LocalizationProvider dateAdapter={AdapterDateFns}>
                  <DatePicker
                    label="结束日期"
                    value={paymentFilters.dateTo}
                    onChange={(date) => setPaymentFilters({ ...paymentFilters, dateTo: date })}
                    renderInput={(params) => <TextField {...params} size="small" fullWidth />}
                  />
                </LocalizationProvider>
              </Grid>
              <Grid item xs={12} md={2}>
                <FormControl fullWidth size="small">
                  <InputLabel>支付方式</InputLabel>
                  <Select
                    value={paymentFilters.method}
                    label="支付方式"
                    onChange={(e) => setPaymentFilters({ ...paymentFilters, method: e.target.value })}
                  >
                    <MenuItem value="all">全部方式</MenuItem>
                    <MenuItem value="credit_card">信用卡</MenuItem>
                    <MenuItem value="debit_card">借记卡</MenuItem>
                    <MenuItem value="bank_transfer">银行转账</MenuItem>
                    <MenuItem value="wallet">电子钱包</MenuItem>
                    <MenuItem value="cash">现金</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={2}>
                <FormControl fullWidth size="small">
                  <InputLabel>状态</InputLabel>
                  <Select
                    value={paymentFilters.status}
                    label="状态"
                    onChange={(e) => setPaymentFilters({ ...paymentFilters, status: e.target.value })}
                  >
                    <MenuItem value="all">全部状态</MenuItem>
                    <MenuItem value="pending">待处理</MenuItem>
                    <MenuItem value="processing">处理中</MenuItem>
                    <MenuItem value="success">成功</MenuItem>
                    <MenuItem value="failed">失败</MenuItem>
                    <MenuItem value="refunded">已退款</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={2}>
                <TextField
                  fullWidth
                  size="small"
                  placeholder="搜索..."
                  value={paymentFilters.search}
                  onChange={(e) => setPaymentFilters({ ...paymentFilters, search: e.target.value })}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <Search />
                      </InputAdornment>
                    ),
                  }}
                />
              </Grid>
              <Grid item xs={12} md={2}>
                <Stack direction="row" spacing={1}>
                  <Button
                    variant="contained"
                    startIcon={<Search />}
                    onClick={loadPayments}
                  >
                    搜索
                  </Button>
                  <IconButton onClick={handleExportPayments} color="primary">
                    <Download />
                  </IconButton>
                </Stack>
              </Grid>
            </Grid>
          </Paper>

          {/* Payment Records Table */}
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>交易ID</TableCell>
                  <TableCell>订单ID</TableCell>
                  <TableCell>支付方式</TableCell>
                  <TableCell>金额</TableCell>
                  <TableCell>状态</TableCell>
                  <TableCell>客户</TableCell>
                  <TableCell>日期</TableCell>
                  <TableCell align="right">操作</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={8} align="center">
                      <CircularProgress />
                    </TableCell>
                  </TableRow>
                ) : payments.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={8} align="center">
                      <Typography color="textSecondary">没有找到支付记录</Typography>
                    </TableCell>
                  </TableRow>
                ) : (
                  payments.map((payment) => (
                    <TableRow key={payment.id} hover>
                      <TableCell>
                        <Typography variant="body2" fontFamily="monospace">
                          {payment.transaction_id}
                        </Typography>
                      </TableCell>
                      <TableCell>#{payment.order_id}</TableCell>
                      <TableCell>
                        <Box display="flex" alignItems="center" gap={1}>
                          {getPaymentMethodIcon(payment.method)}
                          <Typography variant="body2">
                            {payment.method_name}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" fontWeight="medium">
                          ${payment.amount?.toFixed(2)}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={payment.status}
                          size="small"
                          color={getPaymentStatusColor(payment.status)}
                        />
                      </TableCell>
                      <TableCell>{payment.customer_name}</TableCell>
                      <TableCell>
                        {format(new Date(payment.created_at), 'MMM dd, HH:mm')}
                      </TableCell>
                      <TableCell align="right">
                        <Stack direction="row" spacing={1} justifyContent="flex-end">
                          <IconButton
                            size="small"
                            onClick={() => handleViewPaymentDetails(payment)}
                          >
                            <Receipt />
                          </IconButton>
                          {payment.status === 'success' && !payment.refunded && (
                            <IconButton
                              size="small"
                              color="warning"
                              onClick={() => {
                                setRefundFormData({
                                  payment_id: payment.id,
                                  amount: payment.amount,
                                  reason: '',
                                });
                                setRefundDialog(true);
                              }}
                            >
                              <MoneyOff />
                            </IconButton>
                          )}
                        </Stack>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>

          {/* Pagination */}
          <TablePagination
            component="div"
            count={totalPayments}
            page={paymentPage}
            onPageChange={(e, newPage) => setPaymentPage(newPage)}
            rowsPerPage={paymentRowsPerPage}
            onRowsPerPageChange={(e) => {
              setPaymentRowsPerPage(parseInt(e.target.value, 10));
              setPaymentPage(0);
            }}
          />

          {/* Export and Reconciliation Tools */}
          <Box mt={2} p={2} bgcolor="grey.50" display="flex" justifyContent="space-between">
            <Button
              variant="outlined"
              startIcon={<Download />}
              onClick={handleExportPayments}
            >
              导出报告
            </Button>
            <Button
              variant="contained"
              startIcon={<Assignment />}
              onClick={handleReconcile}
            >
              对账
            </Button>
          </Box>
        </TabPanel>

        {/* Tab Panel 1: Refund Management */}
        <TabPanel value={tabValue} index={1}>
          {/* Refund Filters */}
          <Paper elevation={0} sx={{ p: 2, mb: 2, bgcolor: 'grey.50' }}>
            <Grid container spacing={2} alignItems="center">
              <Grid item xs={12} md={3}>
                <FormControl fullWidth size="small">
                  <InputLabel>状态筛选</InputLabel>
                  <Select
                    value={refundFilters.status}
                    label="状态筛选"
                    onChange={(e) => setRefundFilters({ ...refundFilters, status: e.target.value })}
                  >
                    <MenuItem value="all">全部退款</MenuItem>
                    <MenuItem value="pending">待审批</MenuItem>
                    <MenuItem value="approved">已批准</MenuItem>
                    <MenuItem value="processing">处理中</MenuItem>
                    <MenuItem value="completed">已完成</MenuItem>
                    <MenuItem value="rejected">已拒绝</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={3}>
                <LocalizationProvider dateAdapter={AdapterDateFns}>
                  <DatePicker
                    label="From Date"
                    value={refundFilters.dateFrom}
                    onChange={(date) => setRefundFilters({ ...refundFilters, dateFrom: date })}
                    renderInput={(params) => <TextField {...params} size="small" fullWidth />}
                  />
                </LocalizationProvider>
              </Grid>
              <Grid item xs={12} md={3}>
                <LocalizationProvider dateAdapter={AdapterDateFns}>
                  <DatePicker
                    label="To Date"
                    value={refundFilters.dateTo}
                    onChange={(date) => setRefundFilters({ ...refundFilters, dateTo: date })}
                    renderInput={(params) => <TextField {...params} size="small" fullWidth />}
                  />
                </LocalizationProvider>
              </Grid>
              <Grid item xs={12} md={3}>
                <Button
                  fullWidth
                  variant="contained"
                  startIcon={<Search />}
                  onClick={loadRefunds}
                >
                  搜索退款
                </Button>
              </Grid>
            </Grid>
          </Paper>

          {/* Batch Operations Toolbar */}
          {selectedRefunds.length > 0 && (
            <Toolbar sx={{ bgcolor: 'primary.light', mb: 2 }}>
              <Typography variant="subtitle1" component="div" sx={{ flex: '1 1 100%' }}>
                已选择 {selectedRefunds.length} 项
              </Typography>
              <Stack direction="row" spacing={2}>
                <Button
                  variant="contained"
                  color="success"
                  startIcon={<ThumbUp />}
                  onClick={handleBatchApprove}
                >
                  批准所选
                </Button>
                <Button
                  variant="contained"
                  color="error"
                  startIcon={<ThumbDown />}
                  onClick={() => {
                    // Handle batch reject
                  }}
                >
                  拒绝所选
                </Button>
              </Stack>
            </Toolbar>
          )}

          {/* Refund List */}
          <Grid container spacing={2}>
            {refunds.map((refund) => (
              <Grid item xs={12} key={refund.id}>
                <Card>
                  <CardContent>
                    <Grid container spacing={2} alignItems="center">
                      <Grid item xs={12} md={1}>
                        <Checkbox
                          checked={selectedRefunds.includes(refund.id)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setSelectedRefunds([...selectedRefunds, refund.id]);
                            } else {
                              setSelectedRefunds(selectedRefunds.filter(id => id !== refund.id));
                            }
                          }}
                        />
                      </Grid>
                      <Grid item xs={12} md={3}>
                        <Typography variant="subtitle1">
                          退款 #{refund.id}
                        </Typography>
                        <Typography variant="body2" color="textSecondary">
                          支付: {refund.payment_id}
                        </Typography>
                      </Grid>
                      <Grid item xs={12} md={2}>
                        <Typography variant="h6" color="error">
                          ${refund.amount?.toFixed(2)}
                        </Typography>
                        <Typography variant="body2" color="textSecondary">
                          原始金额: ${refund.original_amount?.toFixed(2)}
                        </Typography>
                      </Grid>
                      <Grid item xs={12} md={2}>
                        <Chip
                          label={refund.status}
                          color={getRefundStatusColor(refund.status)}
                        />
                      </Grid>
                      <Grid item xs={12} md={2}>
                        <Typography variant="body2">
                          {format(new Date(refund.created_at), 'MMM dd, yyyy')}
                        </Typography>
                        <Typography variant="body2" color="textSecondary">
                          {refund.customer_name}
                        </Typography>
                      </Grid>
                      <Grid item xs={12} md={2}>
                        {refund.status === 'pending' && (
                          <Stack direction="row" spacing={1}>
                            <Button
                              size="small"
                              variant="contained"
                              color="success"
                              startIcon={<Check />}
                              onClick={() => handleApproveRefund(refund.id)}
                            >
                              批准
                            </Button>
                            <Button
                              size="small"
                              variant="outlined"
                              color="error"
                              startIcon={<Close />}
                              onClick={() => handleRejectRefund(refund.id, 'Admin rejected')}
                            >
                              拒绝
                            </Button>
                          </Stack>
                        )}
                        {refund.status === 'approved' && (
                          <Button
                            size="small"
                            variant="contained"
                            startIcon={<Payment />}
                            onClick={() => paymentService.processRefund(refund.id)}
                          >
                            处理
                          </Button>
                        )}
                      </Grid>
                    </Grid>
                    {refund.reason && (
                      <Box mt={2} p={2} bgcolor="grey.50" borderRadius={1}>
                        <Typography variant="body2">
                          <strong>原因：</strong> {refund.reason}
                        </Typography>
                      </Box>
                    )}
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>

          {/* Refund Pagination */}
          <TablePagination
            component="div"
            count={totalRefunds}
            page={refundPage}
            onPageChange={(e, newPage) => setRefundPage(newPage)}
            rowsPerPage={refundRowsPerPage}
            onRowsPerPageChange={(e) => {
              setRefundRowsPerPage(parseInt(e.target.value, 10));
              setRefundPage(0);
            }}
          />
        </TabPanel>




      </Paper>

      {/* Payment Details Dialog */}
      <Dialog
        open={paymentDetailsDialog}
        onClose={() => setPaymentDetailsDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          支付详情
          <IconButton
            onClick={() => setPaymentDetailsDialog(false)}
            sx={{ position: 'absolute', right: 8, top: 8 }}
          >
            <Close />
          </IconButton>
        </DialogTitle>
        <DialogContent dividers>
          {selectedPaymentDetails && (
            <Grid container spacing={2}>
              <Grid item xs={6}>
                <Typography variant="body2" color="textSecondary">
                  交易ID
                </Typography>
                <Typography variant="body1" fontFamily="monospace">
                  {selectedPaymentDetails.transaction_id}
                </Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="body2" color="textSecondary">
                  状态
                </Typography>
                <Chip
                  label={selectedPaymentDetails.status}
                  color={getPaymentStatusColor(selectedPaymentDetails.status)}
                  size="small"
                />
              </Grid>
              <Grid item xs={6}>
                <Typography variant="body2" color="textSecondary">
                  金额
                </Typography>
                <Typography variant="h6">
                  ${selectedPaymentDetails.amount?.toFixed(2)}
                </Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="body2" color="textSecondary">
                  支付方式
                </Typography>
                <Typography variant="body1">
                  {selectedPaymentDetails.method_name}
                </Typography>
              </Grid>
              <Grid item xs={12}>
                <Typography variant="body2" color="textSecondary">
                  客户
                </Typography>
                <Typography variant="body1">
                  {selectedPaymentDetails.customer_name} ({selectedPaymentDetails.customer_email})
                </Typography>
              </Grid>
              <Grid item xs={12}>
                <Typography variant="body2" color="textSecondary">
                  创建时间
                </Typography>
                <Typography variant="body1">
                  {format(new Date(selectedPaymentDetails.created_at), 'PPpp')}
                </Typography>
              </Grid>
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPaymentDetailsDialog(false)}>
            关闭
          </Button>
        </DialogActions>
      </Dialog>

      {/* Refund Dialog */}
      <Dialog
        open={refundDialog}
        onClose={() => setRefundDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>创建退款申请</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="退款金额"
                type="number"
                value={refundFormData.amount}
                onChange={(e) => setRefundFormData({ ...refundFormData, amount: parseFloat(e.target.value) })}
                InputProps={{
                  startAdornment: <InputAdornment position="start">$</InputAdornment>,
                }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="退款原因"
                multiline
                rows={3}
                value={refundFormData.reason}
                onChange={(e) => setRefundFormData({ ...refundFormData, reason: e.target.value })}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setRefundDialog(false)}>取消</Button>
          <Button onClick={handleCreateRefund} variant="contained" color="warning">
            创建退款
          </Button>
        </DialogActions>
      </Dialog>


    </Container>
  );
}

export default PaymentCenter;