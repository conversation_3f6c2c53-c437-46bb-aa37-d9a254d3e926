import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Chip,
  Tab,
  Tabs,
  InputAdornment,
  Switch,
  FormControlLabel,
  Grid,
  LinearProgress,
  CircularProgress,
} from '@mui/material';
import {
  Add,
  Edit,
  Delete,
  Search,
  Visibility,
} from '@mui/icons-material';
import axios from 'axios';
import { useSnackbar } from 'notistack';
import { ApiConfig } from '../../config/api-config';
import { AppConfig } from '../../config/app-config';
import HelpDocumentEditor from '../../components/HelpDocumentEditor';
import MarkdownRenderer from '../../components/MarkdownRenderer';

// 创建axios实例
const axiosInstance = axios.create({
  baseURL: ApiConfig.baseURL,
  timeout: ApiConfig.timeout,
  headers: ApiConfig.defaultHeaders,
});

// 请求拦截器 - 添加token
axiosInstance.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem(`${AppConfig.storage.prefix}${AppConfig.storage.tokenKey}`);
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
axiosInstance.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token过期，跳转到登录页
      window.location.href = '/#/auth/login';
    }
    return Promise.reject(error);
  }
);

function DocumentationManagement() {
  const { enqueueSnackbar } = useSnackbar();
  const [tabValue, setTabValue] = useState(0);
  
  // 分类管理状态
  const [categories, setCategories] = useState([]);
  const [categoriesLoading, setCategoriesLoading] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [categoryDialog, setCategoryDialog] = useState(false);
  const [categoryLoading, setCategoryLoading] = useState(false);
  const [categoryForm, setCategoryForm] = useState({
    name: '',
    description: '',
    icon: '',
    sort_order: 1,
    is_active: true,
  });
  
  // 文档管理状态
  const [documents, setDocuments] = useState([]);
  const [documentsLoading, setDocumentsLoading] = useState(false);
  const [documentDialog, setDocumentDialog] = useState(false);
  const [documentForm, setDocumentForm] = useState({
    category_id: '',
    title: '',
    content: '',
    sort_order: 1,
    is_published: true,
  });
  const [selectedDocument, setSelectedDocument] = useState(null);
  const [previewDialog, setPreviewDialog] = useState(false);
  const [previewLoading, setPreviewLoading] = useState(false);
  const [documentEditLoading, setDocumentEditLoading] = useState(false);
  
  // 搜索和分页
  const [searchKeyword, setSearchKeyword] = useState('');
  const [debouncedSearchKeyword, setDebouncedSearchKeyword] = useState('');
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalDocuments, setTotalDocuments] = useState(0);
  


  // 防抖搜索
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchKeyword(searchKeyword);
      // 搜索时重置到第一页
      if (searchKeyword !== debouncedSearchKeyword) {
        setPage(0);
      }
    }, 500); // 500ms 防抖延迟

    return () => clearTimeout(timer);
  }, [searchKeyword, debouncedSearchKeyword]);

  useEffect(() => {
    loadCategories();
    loadDocuments();
  }, []);

  useEffect(() => {
    loadDocuments();
  }, [page, rowsPerPage, debouncedSearchKeyword, selectedCategory]);

  // 加载分类列表
  const loadCategories = async () => {
    try {
      setCategoriesLoading(true);
      const response = await axiosInstance.get(
        `/api/${ApiConfig.version}${ApiConfig.endpoints.documentation.categories.list}`
      );
      if (response.data.success) {
        setCategories(response.data.data || []);
      } else {
        console.error('Load categories failed:', response.data);
        enqueueSnackbar(response.data.message || '加载分类失败', { variant: 'error' });
      }
    } catch (error) {
      console.error('Failed to load categories:', error);
      enqueueSnackbar(error.response?.data?.detail || error.response?.data?.message || '加载分类失败', { variant: 'error' });
    } finally {
      setCategoriesLoading(false);
    }
  };

  // 加载文档列表
  const loadDocuments = async () => {
    try {
      setDocumentsLoading(true);
      const params = {
        page: page + 1,
        page_size: rowsPerPage,
        keyword: debouncedSearchKeyword || undefined,
        category_id: selectedCategory || undefined,
      };

      const response = await axiosInstance.get(
        `/api/${ApiConfig.version}${ApiConfig.endpoints.documentation.documents.list}`,
        { params }
      );

      if (response.data.success) {
        const data = response.data.data;
        setDocuments(data.items || []);
        setTotalDocuments(data.pagination?.total || 0);
      } else {
        enqueueSnackbar(response.data.message || '加载文档失败', { variant: 'error' });
      }
    } catch (error) {
      console.error('Failed to load documents:', error);
      enqueueSnackbar(error.response?.data?.message || '加载文档失败', { variant: 'error' });
    } finally {
      setDocumentsLoading(false);
    }
  };



  // 处理分类操作
  const handleSaveCategory = async () => {
    if (categoryLoading) return; // 防止重复提交

    setCategoryLoading(true);
    try {
      let response;
      if (categoryForm.id) {
        // 更新分类
        response = await axiosInstance.put(
          `/api/${ApiConfig.version}${ApiConfig.endpoints.documentation.categories.update}/${categoryForm.id}`,
          categoryForm
        );
      } else {
        // 创建分类
        response = await axiosInstance.post(
          `/api/${ApiConfig.version}${ApiConfig.endpoints.documentation.categories.create}`,
          categoryForm
        );
      }

      if (response.data.success) {
        enqueueSnackbar(categoryForm.id ? '分类更新成功' : '分类创建成功', { variant: 'success' });
        setCategoryDialog(false);
        resetCategoryForm();
        await loadCategories(); // 等待加载完成
      } else {
        enqueueSnackbar(response.data.message || '保存分类失败', { variant: 'error' });
      }
    } catch (error) {
      console.error('Failed to save category:', error);
      enqueueSnackbar(error.response?.data?.message || '保存分类失败', { variant: 'error' });
    } finally {
      setCategoryLoading(false); // 确保加载状态被重置
    }
  };

  const handleDeleteCategory = async (categoryId) => {
    if (window.confirm('确定要删除这个分类吗？')) {
      try {
        const response = await axiosInstance.delete(
          `/api/${ApiConfig.version}${ApiConfig.endpoints.documentation.categories.delete}/${categoryId}`
        );
        if (response.data.success) {
          enqueueSnackbar('分类删除成功', { variant: 'success' });
          loadCategories();
        } else {
          enqueueSnackbar(response.data.message || '删除分类失败', { variant: 'error' });
        }
      } catch (error) {
        console.error('Failed to delete category:', error);
        enqueueSnackbar(error.response?.data?.message || '删除分类失败', { variant: 'error' });
      }
    }
  };

  // 处理文档操作
  const handleSaveDocument = async () => {
    try {
      let response;
      if (documentForm.id) {
        // 更新文档
        response = await axiosInstance.put(
          `/api/${ApiConfig.version}${ApiConfig.endpoints.documentation.documents.update}/${documentForm.id}`,
          documentForm
        );
      } else {
        // 创建文档
        response = await axiosInstance.post(
          `/api/${ApiConfig.version}${ApiConfig.endpoints.documentation.documents.create}`,
          documentForm
        );
      }

      if (response.data.success) {
        const isUpdate = !!documentForm.id;
        enqueueSnackbar(isUpdate ? '文档更新成功' : '文档创建成功', { variant: 'success' });

        // 如果是新创建的文档，更新documentForm的id，这样用户可以继续编辑和上传文件
        if (!isUpdate && response.data.data && response.data.data.id) {
          setDocumentForm(prev => ({ ...prev, id: response.data.data.id }));
        } else {
          // 如果是更新操作，关闭对话框
          setDocumentDialog(false);
          resetDocumentForm();
        }

        loadDocuments();
      } else {
        enqueueSnackbar(response.data.message || '保存文档失败', { variant: 'error' });
      }
    } catch (error) {
      console.error('Failed to save document:', error);
      enqueueSnackbar(error.response?.data?.message || '保存文档失败', { variant: 'error' });
    }
  };

  const handleDeleteDocument = async (docId) => {
    if (window.confirm('确定要删除这个文档吗？')) {
      try {
        const response = await axiosInstance.delete(
          `/api/${ApiConfig.version}${ApiConfig.endpoints.documentation.documents.delete}/${docId}`
        );
        if (response.data.success) {
          enqueueSnackbar('文档删除成功', { variant: 'success' });
          loadDocuments();
        } else {
          enqueueSnackbar(response.data.message || '删除文档失败', { variant: 'error' });
        }
      } catch (error) {
        console.error('Failed to delete document:', error);
        enqueueSnackbar(error.response?.data?.message || '删除文档失败', { variant: 'error' });
      }
    }
  };

  // 重置表单
  const resetCategoryForm = () => {
    setCategoryForm({
      name: '',
      description: '',
      icon: '',
      sort_order: 1,
      is_active: true,
    });
  };

  const resetDocumentForm = () => {
    setDocumentForm({
      category_id: '',
      title: '',
      content: '',
      sort_order: 1,
      is_published: true,
    });
  };

  // 编辑分类
  const handleEditCategory = (category) => {
    setCategoryForm(category);
    setCategoryDialog(true);
  };

  // 编辑文档
  const handleEditDocument = async (document) => {
    if (documentEditLoading) return; // 防止重复点击

    try {
      setDocumentEditLoading(true);

      // 先获取完整的文档详情（包含content）
      const response = await axiosInstance.get(
        `/api/${ApiConfig.version}${ApiConfig.endpoints.documentation.documents.get}/${document.id}`
      );

      if (response.data.success) {
        const fullDocument = response.data.data;
        setDocumentForm(fullDocument);
        setDocumentDialog(true);
      } else {
        enqueueSnackbar(response.data.message || '获取文档详情失败', { variant: 'error' });
      }
    } catch (error) {
      console.error('Failed to load document for editing:', error);
      enqueueSnackbar(error.response?.data?.message || '获取文档详情失败', { variant: 'error' });
    } finally {
      setDocumentEditLoading(false);
    }
  };

  // 预览文档
  const handlePreviewDocument = async (docId) => {
    try {
      setPreviewLoading(true);
      setPreviewDialog(true); // 先打开对话框显示加载状态

      const response = await axiosInstance.get(
        `/api/${ApiConfig.version}${ApiConfig.endpoints.documentation.documents.get}/${docId}`,
        { params: { increment_view: false } }
      );
      if (response.data.success) {
        setSelectedDocument(response.data.data);
      } else {
        enqueueSnackbar(response.data.message || '加载文档失败', { variant: 'error' });
        setPreviewDialog(false);
      }
    } catch (error) {
      console.error('Failed to load document:', error);
      enqueueSnackbar(error.response?.data?.message || '加载文档失败', { variant: 'error' });
      setPreviewDialog(false);
    } finally {
      setPreviewLoading(false);
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom sx={{ mb: 3, fontWeight: 600 }}>
        帮助文档管理
      </Typography>



      {/* 标签页 */}
      <Paper sx={{ mb: 3 }}>
        <Tabs value={tabValue} onChange={(e, v) => setTabValue(v)}>
          <Tab label="文档管理" />
          <Tab label="分类管理" />
        </Tabs>
      </Paper>

      {/* 文档管理标签页 */}
      {tabValue === 0 && (
        <Paper sx={{ p: 2 }}>
          <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
              <TextField
                size="small"
                placeholder="搜索文档..."
                value={searchKeyword}
                onChange={(e) => setSearchKeyword(e.target.value)}
                slotProps={{
                  input: {
                    startAdornment: (
                      <InputAdornment position="start">
                        <Search />
                      </InputAdornment>
                    ),
                  },
                }}
              />
              <FormControl size="small" sx={{ minWidth: 180 }}>
                <InputLabel>分类筛选</InputLabel>
                <Select
                  value={selectedCategory || ''}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  label="分类筛选"
                  MenuProps={{
                    PaperProps: {
                      sx: {
                        maxHeight: 300,
                        minWidth: 180,
                      }
                    }
                  }}
                >
                  <MenuItem value="">全部</MenuItem>
                  {categories.map((cat) => (
                    <MenuItem key={cat.id} value={cat.id} sx={{ minWidth: 160 }}>
                      {cat.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Box>
            <Button
              variant="contained"
              startIcon={<Add />}
              onClick={() => {
                resetDocumentForm();
                setDocumentDialog(true);
              }}
            >
              新增文档
            </Button>
          </Box>

          {documentsLoading ? (
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                height: '400px',
                backgroundColor: '#fafbfc'
              }}
            >
              <LinearProgress sx={{ width: '200px', mb: 2 }} />
              <Typography variant="body2" color="text.secondary">
                正在加载文档列表...
              </Typography>
            </Box>
          ) : (
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>文档ID</TableCell>
                    <TableCell>标题</TableCell>
                    <TableCell>分类</TableCell>
                    <TableCell>排序</TableCell>
                    <TableCell>浏览量</TableCell>
                    <TableCell>状态</TableCell>
                    <TableCell>创建者</TableCell>
                    <TableCell>更新时间</TableCell>
                    <TableCell align="center">操作</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {documents.length > 0 ? (
                    documents.map((doc) => (
                      <TableRow
                        key={doc.id}
                        sx={{
                          '&:not(:last-child)': {
                            borderBottom: '2px solid #f5f5f5'
                          },
                          '& td': {
                            py: 2
                          }
                        }}
                      >
                        <TableCell>{doc.id}</TableCell>
                        <TableCell>{doc.title}</TableCell>
                        <TableCell>{doc.category_name || '-'}</TableCell>
                        <TableCell>{doc.sort_order}</TableCell>
                        <TableCell>{doc.view_count || 0}</TableCell>
                        <TableCell>
                          <Chip
                            size="small"
                            label={doc.is_published ? '已发布' : '未发布'}
                            color={doc.is_published ? 'success' : 'default'}
                          />
                        </TableCell>
                        <TableCell>{doc.creator_name || '-'}</TableCell>
                        <TableCell>
                          {new Date(doc.updated_at || doc.created_at).toLocaleDateString()}
                        </TableCell>
                        <TableCell align="center">
                          <IconButton
                            size="small"
                            onClick={() => handlePreviewDocument(doc.id)}
                          >
                            <Visibility />
                          </IconButton>
                          <IconButton
                            size="small"
                            onClick={() => handleEditDocument(doc)}
                            disabled={documentEditLoading}
                          >
                            {documentEditLoading ? <CircularProgress size={16} /> : <Edit />}
                          </IconButton>
                          <IconButton
                            size="small"
                            color="error"
                            onClick={() => handleDeleteDocument(doc.id)}
                          >
                            <Delete />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={9} align="center" sx={{ py: 8 }}>
                        <Box sx={{ textAlign: 'center' }}>
                          <Typography variant="h6" sx={{ color: '#9ca3af', mb: 1 }}>
                            📝 暂无文档
                          </Typography>
                          <Typography variant="body2" sx={{ color: '#6b7280', mb: 2 }}>
                            {debouncedSearchKeyword ? '没有找到匹配的文档' : '点击上方"新增文档"按钮创建第一个文档'}
                          </Typography>
                          {!debouncedSearchKeyword && (
                            <Button
                              variant="outlined"
                              startIcon={<Add />}
                              onClick={() => {
                                resetDocumentForm();
                                setDocumentDialog(true);
                              }}
                              sx={{ mt: 1 }}
                            >
                              创建文档
                            </Button>
                          )}
                        </Box>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          )}

          <Box sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            p: 2,
            borderTop: '1px solid #e0e0e0',
            backgroundColor: '#fafafa'
          }}>
            <Typography variant="body2" sx={{ color: '#666' }}>
              共 {totalDocuments} 条记录
            </Typography>
            <TablePagination
              component="div"
              count={totalDocuments}
              page={page}
              onPageChange={(e, newPage) => setPage(newPage)}
              rowsPerPage={rowsPerPage}
              onRowsPerPageChange={(e) => {
                setRowsPerPage(parseInt(e.target.value, 10));
                setPage(0);
              }}
              labelRowsPerPage="每页显示:"
              labelDisplayedRows={({ from, to, count }) =>
                `第 ${from}-${to} 条，共 ${count !== -1 ? count : `超过 ${to}`} 条`
              }
              rowsPerPageOptions={[10, 25, 50, 100]}
              sx={{
                '& .MuiTablePagination-toolbar': {
                  minHeight: '52px'
                },
                '& .MuiTablePagination-selectLabel': {
                  fontSize: '0.875rem',
                  color: '#666'
                },
                '& .MuiTablePagination-displayedRows': {
                  fontSize: '0.875rem',
                  color: '#666'
                },
                '& .MuiTablePagination-select': {
                  fontSize: '0.875rem'
                },
                '& .MuiIconButton-root': {
                  padding: '8px',
                  '&:hover': {
                    backgroundColor: '#e3f2fd'
                  }
                }
              }}
            />
          </Box>
        </Paper>
      )}

      {/* 分类管理标签页 */}
      {tabValue === 1 && (
        <Paper sx={{ p: 2 }}>
          <Box sx={{ mb: 2, display: 'flex', justifyContent: 'flex-end' }}>
            <Button
              variant="contained"
              startIcon={<Add />}
              onClick={() => {
                resetCategoryForm();
                setCategoryDialog(true);
              }}
            >
              新增分类
            </Button>
          </Box>

          {categoriesLoading ? (
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                height: '300px',
                backgroundColor: '#fafbfc'
              }}
            >
              <LinearProgress sx={{ width: '200px', mb: 2 }} />
              <Typography variant="body2" color="text.secondary">
                正在加载分类列表...
              </Typography>
            </Box>
          ) : (
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>分类名称</TableCell>
                    <TableCell>描述</TableCell>
                    <TableCell>图标</TableCell>
                    <TableCell>排序</TableCell>
                    <TableCell>文档数量</TableCell>
                    <TableCell>状态</TableCell>
                    <TableCell align="center">操作</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {categories.length > 0 ? (
                    categories.map((category, index) => (
                      <TableRow
                        key={category.id}
                        sx={{
                          '&:not(:last-child)': {
                            borderBottom: '2px solid #f5f5f5'
                          },
                          '& td': {
                            py: 2
                          }
                        }}
                      >
                        <TableCell>{category.name}</TableCell>
                        <TableCell>{category.description || '-'}</TableCell>
                        <TableCell>{category.icon || '-'}</TableCell>
                        <TableCell>{category.sort_order}</TableCell>
                        <TableCell>{category.document_count || 0}</TableCell>
                        <TableCell>
                          <Chip
                            size="small"
                            label={category.is_active ? '启用' : '禁用'}
                            color={category.is_active ? 'success' : 'default'}
                          />
                        </TableCell>
                        <TableCell align="center">
                          <IconButton
                            size="small"
                            onClick={() => handleEditCategory(category)}
                          >
                            <Edit />
                          </IconButton>
                          <IconButton
                            size="small"
                            color="error"
                            onClick={() => handleDeleteCategory(category.id)}
                          >
                            <Delete />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={7} align="center" sx={{ py: 8 }}>
                        <Box sx={{ textAlign: 'center' }}>
                          <Typography variant="h6" sx={{ color: '#9ca3af', mb: 1 }}>
                            📁 暂无分类
                          </Typography>
                          <Typography variant="body2" sx={{ color: '#6b7280', mb: 2 }}>
                            点击上方"新增分类"按钮创建第一个分类
                          </Typography>
                          <Button
                            variant="outlined"
                            startIcon={<Add />}
                            onClick={() => {
                              resetCategoryForm();
                              setCategoryDialog(true);
                            }}
                            sx={{ mt: 1 }}
                          >
                            创建分类
                          </Button>
                        </Box>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </Paper>
      )}



      {/* 分类编辑对话框 */}
      <Dialog open={categoryDialog} onClose={() => setCategoryDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          {categoryForm.id ? '编辑分类' : '新增分类'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 3, display: 'flex', flexDirection: 'column', gap: 3 }}>
            <TextField
              label="分类名称"
              value={categoryForm.name}
              onChange={(e) => setCategoryForm({ ...categoryForm, name: e.target.value })}
              fullWidth
              required
              size="small"
              placeholder="常见问题"
            />
            <TextField
              label="分类描述"
              value={categoryForm.description}
              onChange={(e) => setCategoryForm({ ...categoryForm, description: e.target.value })}
              fullWidth
              size="small"
              placeholder="分类的详细描述"
              multiline
              rows={2}
            />
            <TextField
              label="图标名称"
              value={categoryForm.icon}
              onChange={(e) => setCategoryForm({ ...categoryForm, icon: e.target.value })}
              fullWidth
              size="small"
              placeholder="Category"
            />
            <TextField
              label="排序权重"
              type="number"
              value={categoryForm.sort_order}
              onChange={(e) => setCategoryForm({ ...categoryForm, sort_order: parseInt(e.target.value) || 1 })}
              fullWidth
              size="small"
              placeholder="1"
              slotProps={{ htmlInput: { min: 1, max: 999 } }}
            />
            <FormControlLabel
              control={
                <Switch
                  checked={categoryForm.is_active}
                  onChange={(e) => setCategoryForm({ ...categoryForm, is_active: e.target.checked })}
                />
              }
              label="启用状态"
              labelPlacement="start"
              sx={{
                display: 'flex',
                justifyContent: 'space-between',
                marginLeft: 0,
                marginRight: 0,
                width: '100%'
              }}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => setCategoryDialog(false)}
            disabled={categoryLoading}
          >
            取消
          </Button>
          <Button
            onClick={handleSaveCategory}
            variant="contained"
            disabled={categoryLoading}
          >
            {categoryLoading ? '保存中...' : '保存'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* 文档编辑对话框 */}
      <Dialog open={documentDialog} onClose={() => setDocumentDialog(false)} maxWidth="xl" fullWidth>
        <DialogTitle sx={{
          borderBottom: '1px solid #e0e0e0',
          backgroundColor: '#f8f9fa',
          display: 'flex',
          alignItems: 'center',
          gap: 1
        }}>
          <Typography variant="h6" component="span">
            {documentForm.id ? '编辑文档' : '新增文档'}
          </Typography>
        </DialogTitle>
        <DialogContent sx={{ p: 0 }}>
          <Box sx={{ p: 3 }}>
            {/* 基本信息区域 */}
            <Paper sx={{ p: 3, mb: 3, backgroundColor: '#fafafa' }}>
              <Typography variant="h6" sx={{ mb: 2, color: 'primary.main', fontWeight: 600 }}>
                📝 基本信息
              </Typography>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <FormControl fullWidth required size="small" sx={{ minWidth: 200 }}>
                    <InputLabel>所属分类</InputLabel>
                    <Select
                      value={documentForm.category_id}
                      onChange={(e) => setDocumentForm({ ...documentForm, category_id: e.target.value })}
                      label="所属分类"
                      MenuProps={{
                        PaperProps: {
                          sx: {
                            maxHeight: 300,
                            minWidth: 200,
                          }
                        }
                      }}
                    >
                      {categories.map((cat) => (
                        <MenuItem key={cat.id} value={cat.id} sx={{ minWidth: 180 }}>
                          {cat.name}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    label="文档标题"
                    value={documentForm.title}
                    onChange={(e) => setDocumentForm({ ...documentForm, title: e.target.value })}
                    fullWidth
                    required
                    size="small"
                    placeholder="请输入文档标题"
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    label="排序权重"
                    type="number"
                    value={documentForm.sort_order}
                    onChange={(e) => setDocumentForm({ ...documentForm, sort_order: parseInt(e.target.value) || 1 })}
                    fullWidth
                    size="small"
                    placeholder="1"
                    slotProps={{ htmlInput: { min: 1, max: 999 } }}
                  />
                </Grid>
              </Grid>
            </Paper>



            {/* 发布设置区域 */}
            <Paper sx={{ p: 3, mb: 3, backgroundColor: '#fafafa' }}>
              <Typography variant="h6" sx={{ mb: 2, color: 'primary.main', fontWeight: 600 }}>
                ⚙️ 发布设置
              </Typography>
              <Grid container spacing={3} alignItems="center">
                <Grid item xs={12} md={6}>
                  <Box sx={{ display: 'flex', alignItems: 'center', height: '100%' }}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={documentForm.is_published}
                          onChange={(e) => setDocumentForm({ ...documentForm, is_published: e.target.checked })}
                          color="primary"
                        />
                      }
                      label="发布状态"
                      sx={{ m: 0 }}
                    />
                  </Box>
                </Grid>
              </Grid>
            </Paper>

            {/* 内容编辑区域 */}
            <Paper sx={{ p: 3, backgroundColor: '#fafafa' }}>
              <Typography variant="h6" sx={{ mb: 2, color: 'primary.main', fontWeight: 600 }}>
                📄 文档内容
              </Typography>
              <HelpDocumentEditor
                value={documentForm.content || ''}
                onChange={(content) => setDocumentForm({ ...documentForm, content })}
                documentId={documentForm.id}
                placeholder="请输入文档内容（支持Markdown格式）"
                height={400}
                axiosInstance={axiosInstance}
              />
            </Paper>
          </Box>
        </DialogContent>
        <DialogActions sx={{
          p: 3,
          borderTop: '1px solid #e0e0e0',
          backgroundColor: '#f8f9fa',
          gap: 2
        }}>
          <Button
            onClick={() => setDocumentDialog(false)}
            variant="outlined"
            size="large"
            sx={{ minWidth: 100 }}
          >
            取消
          </Button>
          <Button
            onClick={handleSaveDocument}
            variant="contained"
            size="large"
            sx={{ minWidth: 120 }}
          >
            {documentForm.id ? '更新文档' : '创建文档'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* 文档预览对话框 */}
      <Dialog
        open={previewDialog}
        onClose={() => setPreviewDialog(false)}
        maxWidth="xl"
        fullWidth
      >
        <DialogTitle sx={{
          borderBottom: '1px solid #e0e0e0',
          backgroundColor: '#f8f9fa',
          display: 'flex',
          alignItems: 'center',
          gap: 1
        }}>
          <Typography variant="h6" component="span">
            查看文档
          </Typography>
        </DialogTitle>

        <DialogContent sx={{ p: 0 }}>
          {previewLoading ? (
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                height: '400px'
              }}
            >
              <CircularProgress size={40} sx={{ mb: 2 }} />
              <Typography variant="body2" color="text.secondary">
                正在加载文档详情...
              </Typography>
            </Box>
          ) : selectedDocument ? (
            <Box sx={{ p: 3 }}>
              {/* 基本信息区域 */}
              <Paper sx={{ p: 3, mb: 3, backgroundColor: '#fafafa' }}>
                <Typography variant="h6" sx={{ mb: 2, color: 'primary.main', fontWeight: 600 }}>
                  📝 基本信息
                </Typography>
                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <TextField
                      label="所属分类"
                      value={selectedDocument.category_name || '未分类'}
                      fullWidth
                      size="small"
                      slotProps={{
                        input: {
                          readOnly: true,
                        },
                      }}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      label="文档标题"
                      value={selectedDocument.title}
                      fullWidth
                      size="small"
                      slotProps={{
                        input: {
                          readOnly: true,
                        },
                      }}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      label="排序权重"
                      value={selectedDocument.sort_order}
                      fullWidth
                      size="small"
                      slotProps={{
                        input: {
                          readOnly: true,
                        },
                      }}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      label="浏览量"
                      value={selectedDocument.view_count || 0}
                      fullWidth
                      size="small"
                      slotProps={{
                        input: {
                          readOnly: true,
                        },
                      }}
                    />
                  </Grid>
                </Grid>
              </Paper>

              {/* 发布设置区域 */}
              <Paper sx={{ p: 3, mb: 3, backgroundColor: '#fafafa' }}>
                <Typography variant="h6" sx={{ mb: 2, color: 'primary.main', fontWeight: 600 }}>
                  ⚙️ 发布设置
                </Typography>
                <Grid container spacing={3} alignItems="center">
                  <Grid item xs={12} md={6}>
                    <Box sx={{ display: 'flex', alignItems: 'center', height: '100%' }}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={selectedDocument.is_published}
                            disabled
                            color="primary"
                          />
                        }
                        label="发布状态"
                        sx={{ m: 0 }}
                      />
                    </Box>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      label="创建者"
                      value={selectedDocument.creator_name || '未知'}
                      fullWidth
                      size="small"
                      slotProps={{
                        input: {
                          readOnly: true,
                        },
                      }}
                    />
                  </Grid>
                </Grid>
              </Paper>

              {/* 内容显示区域 */}
              <Paper sx={{ p: 3, backgroundColor: '#fafafa' }}>
                <Typography variant="h6" sx={{ mb: 2, color: 'primary.main', fontWeight: 600 }}>
                  📄 文档内容
                </Typography>
                <Box sx={{
                  border: '1px solid #e0e0e0',
                  borderRadius: 1,
                  p: 3,
                  backgroundColor: '#fff',
                  minHeight: 400,
                  maxHeight: 600,
                  overflow: 'auto'
                }}>
                  {selectedDocument.content ? (
                    <MarkdownRenderer content={selectedDocument.content} />
                  ) : (
                    <Typography variant="body1" color="text.secondary" sx={{ textAlign: 'center', py: 4 }}>
                      暂无内容
                    </Typography>
                  )}
                </Box>
              </Paper>
            </Box>
          ) : (
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                height: '400px'
              }}
            >
              <Typography variant="h6" sx={{ color: '#9ca3af', mb: 1 }}>
                📄 文档不存在
              </Typography>
              <Typography variant="body2" sx={{ color: '#6b7280' }}>
                无法加载文档信息
              </Typography>
            </Box>
          )}
        </DialogContent>
        <DialogActions sx={{
          p: 3,
          borderTop: '1px solid #e0e0e0',
          backgroundColor: '#f8f9fa',
          gap: 2
        }}>
          <Button
            onClick={() => setPreviewDialog(false)}
            variant="outlined"
            size="large"
            sx={{ minWidth: 100 }}
          >
            关闭
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}

export default DocumentationManagement;