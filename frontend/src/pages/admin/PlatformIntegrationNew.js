import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  CardActions,
  Paper,
  Button,
  IconButton,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Toolbar,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  CircularProgress,
  Divider,
  InputAdornment,
  Stack,
  Switch,
  FormControlLabel,
  Avatar,
} from '@mui/material';
import {
  Add,
  Edit,
  Delete,
  Visibility,
  Search,
  Refresh,
  Settings,
  CheckCircle,
  Error as ErrorIcon,
  Warning,
  CloudQueue,
  Link,
  Api,
} from '@mui/icons-material';
import { useSnackbar } from 'notistack';
import platformService from '../../services/platformService';

// 平台类型配置
const PLATFORM_TYPES = [
  { value: 'content_publish', label: '内容发布平台', color: '#1976d2' },
  { value: 'media_resource', label: '媒体资源平台', color: '#f57c00' },
  { value: 'ai_service', label: 'AI服务平台', color: '#9c27b0' },
  { value: 'data_analysis', label: '数据分析平台', color: '#388e3c' },
];

// 认证类型配置
const AUTH_TYPES = [
  { value: 'api_key', label: 'API Key', description: '使用API密钥进行认证', fields: ['api_key', 'api_secret'] },
  { value: 'username_password', label: '用户名密码', description: '使用用户名和密码进行认证', fields: ['username', 'password'] },
  { value: 'bearer_token', label: 'Bearer Token', description: '使用Bearer令牌', fields: ['access_token'] },
  { value: 'oauth2', label: 'OAuth 2.0', description: '使用OAuth 2.0授权码流程', fields: ['api_key', 'api_secret', 'access_token'] },
];

// 连接状态配置
const CONNECTION_STATUS = {
  connected: { label: '已连接', color: 'success', icon: <CheckCircle /> },
  disconnected: { label: '未连接', color: 'error', icon: <ErrorIcon /> },
  connecting: { label: '连接中', color: 'warning', icon: <CloudQueue /> },
  error: { label: '连接错误', color: 'error', icon: <Warning /> },
};

const PlatformIntegration = () => {
  const { enqueueSnackbar } = useSnackbar();

  // 基础状态
  const [loading, setLoading] = useState(false);
  const [platforms, setPlatforms] = useState([]);
  const [connectionStatuses, setConnectionStatuses] = useState({});

  // 表格状态
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');

  // 对话框状态
  const [addDialog, setAddDialog] = useState(false);
  const [editDialog, setEditDialog] = useState(false);
  const [deleteDialog, setDeleteDialog] = useState(false);
  const [viewDialog, setViewDialog] = useState(false);
  const [selectedPlatform, setSelectedPlatform] = useState(null);

  // 表单数据
  const [formData, setFormData] = useState({
    platform_code: '',
    platform_name: '',
    platform_type: 'content_publish',
    api_base_url: '',
    auth_type: 'api_key', // 认证类型
    api_key: '',
    api_secret: '',
    username: '',
    password: '',
    access_token: '',
    description: '',
    is_active: true,
    rate_limits: {
      requests_per_minute: 60,
      requests_per_hour: 1000
    }
  });

  // 加载平台列表
  const loadPlatforms = async () => {
    setLoading(true);
    try {
      const response = await platformService.getPlatformConfigs();
      setPlatforms(response.data || []);
      
      // 加载连接状态
      const statuses = {};
      for (const platform of response.data || []) {
        try {
          const status = await platformService.testConnection(platform.platform_code);
          statuses[platform.platform_code] = status.connected ? 'connected' : 'disconnected';
        } catch (error) {
          statuses[platform.platform_code] = 'error';
        }
      }
      setConnectionStatuses(statuses);
    } catch (error) {
      enqueueSnackbar('加载平台配置失败', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  // 测试连接
  const testConnection = async (platformCode) => {
    try {
      setConnectionStatuses(prev => ({ ...prev, [platformCode]: 'connecting' }));
      const result = await platformService.testConnection(platformCode);
      setConnectionStatuses(prev => ({ 
        ...prev, 
        [platformCode]: result.connected ? 'connected' : 'disconnected' 
      }));
      enqueueSnackbar(
        result.connected ? '连接测试成功' : '连接测试失败', 
        { variant: result.connected ? 'success' : 'error' }
      );
    } catch (error) {
      setConnectionStatuses(prev => ({ ...prev, [platformCode]: 'error' }));
      enqueueSnackbar('连接测试失败', { variant: 'error' });
    }
  };

  // 添加平台
  const handleAddPlatform = async () => {
    try {
      await platformService.createPlatformConfig(formData);
      enqueueSnackbar('平台添加成功', { variant: 'success' });
      setAddDialog(false);
      resetForm();
      loadPlatforms();
    } catch (error) {
      enqueueSnackbar('平台添加失败', { variant: 'error' });
    }
  };

  // 更新平台
  const handleUpdatePlatform = async () => {
    try {
      await platformService.updatePlatformConfig(selectedPlatform.platform_code, formData);
      enqueueSnackbar('平台更新成功', { variant: 'success' });
      setEditDialog(false);
      resetForm();
      loadPlatforms();
    } catch (error) {
      enqueueSnackbar('平台更新失败', { variant: 'error' });
    }
  };

  // 删除平台
  const handleDeletePlatform = async () => {
    try {
      await platformService.deletePlatformConfig(selectedPlatform.platform_code);
      enqueueSnackbar('平台删除成功', { variant: 'success' });
      setDeleteDialog(false);
      setSelectedPlatform(null);
      loadPlatforms();
    } catch (error) {
      enqueueSnackbar('平台删除失败', { variant: 'error' });
    }
  };

  // 重置表单
  const resetForm = () => {
    setFormData({
      platform_code: '',
      platform_name: '',
      platform_type: 'content_publish',
      api_base_url: '',
      auth_type: 'api_key',
      api_key: '',
      api_secret: '',
      username: '',
      password: '',
      access_token: '',
      description: '',
      is_active: true,
      rate_limits: {
        requests_per_minute: 60,
        requests_per_hour: 1000
      }
    });
  };

  // 打开编辑对话框
  const handleEditClick = (platform) => {
    setSelectedPlatform(platform);
    setFormData({
      platform_code: platform.platform_code,
      platform_name: platform.platform_name,
      platform_type: platform.platform_type || 'content_publish',
      api_base_url: platform.api_base_url || '',
      auth_type: platform.auth_type || 'api_key',
      api_key: platform.api_key || '',
      api_secret: platform.api_secret || '',
      username: platform.username || '',
      password: platform.password || '',
      access_token: platform.access_token || '',
      description: platform.description || '',
      is_active: platform.is_active,
      rate_limits: platform.rate_limits || { requests_per_minute: 60, requests_per_hour: 1000 }
    });
    setEditDialog(true);
  };

  // 打开查看对话框
  const handleViewClick = (platform) => {
    setSelectedPlatform(platform);
    setViewDialog(true);
  };

  // 打开删除对话框
  const handleDeleteClick = (platform) => {
    setSelectedPlatform(platform);
    setDeleteDialog(true);
  };

  // 过滤平台列表
  const filteredPlatforms = platforms.filter(platform => {
    const matchesSearch = platform.platform_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         platform.platform_code.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = filterType === 'all' || platform.platform_type === filterType;
    const matchesStatus = filterStatus === 'all' || 
                         (filterStatus === 'active' && platform.is_active) ||
                         (filterStatus === 'inactive' && !platform.is_active);
    return matchesSearch && matchesType && matchesStatus;
  });

  // 分页数据
  const paginatedPlatforms = filteredPlatforms.slice(
    page * rowsPerPage,
    page * rowsPerPage + rowsPerPage
  );

  // 获取平台类型信息
  const getPlatformTypeInfo = (type) => {
    return PLATFORM_TYPES.find(t => t.value === type) || PLATFORM_TYPES[0];
  };

  // 获取连接状态信息
  const getConnectionStatusInfo = (status) => {
    return CONNECTION_STATUS[status] || CONNECTION_STATUS.disconnected;
  };

  useEffect(() => {
    loadPlatforms();
  }, []);

  return (
    <Container maxWidth={false} sx={{ py: 3 }}>
      {/* 页面标题 */}
      <Box sx={{
        mb: 4,
        p: 3,
        bgcolor: 'white',
        borderRadius: 2,
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        border: '1px solid rgba(0,0,0,0.05)'
      }}>
        <Typography variant="h4" gutterBottom sx={{ fontWeight: 'bold', color: 'text.primary' }}>
          🔗 软文平台配置管理
        </Typography>
        <Typography variant="body1" color="text.secondary">
          管理各个对接的软文平台配置信息，包括API配置、认证信息和连接状态
        </Typography>
        <Box sx={{ mt: 2, display: 'flex', gap: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <CheckCircle sx={{ fontSize: 20, color: 'success.main' }} />
            <Typography variant="body2" color="text.secondary">
              已连接: {Object.values(connectionStatuses).filter(s => s === 'connected').length}
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Settings sx={{ fontSize: 20, color: 'primary.main' }} />
            <Typography variant="body2" color="text.secondary">
              总平台: {platforms.length}
            </Typography>
          </Box>
        </Box>
      </Box>

      {/* 工具栏 */}
      <Paper sx={{
        p: 3,
        mb: 3,
        borderRadius: 2,
        boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
        border: '1px solid rgba(0,0,0,0.05)'
      }}>
        <Grid container spacing={3} alignItems="center">
          <Grid item xs={12} md={3}>
            <TextField
              fullWidth
              size="small"
              placeholder="🔍 搜索平台名称或代码..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Search color="action" />
                  </InputAdornment>
                ),
              }}
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: 2,
                  '&:hover fieldset': {
                    borderColor: 'primary.main',
                  },
                }
              }}
            />
          </Grid>
          <Grid item xs={12} md={2}>
            <FormControl fullWidth size="small">
              <InputLabel>📂 平台类型</InputLabel>
              <Select
                value={filterType}
                onChange={(e) => setFilterType(e.target.value)}
                label="📂 平台类型"
                sx={{ borderRadius: 2 }}
              >
                <MenuItem value="all">全部类型</MenuItem>
                {PLATFORM_TYPES.map(type => (
                  <MenuItem key={type.value} value={type.value}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Box sx={{
                        width: 8,
                        height: 8,
                        borderRadius: '50%',
                        bgcolor: type.color
                      }} />
                      {type.label}
                    </Box>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={2}>
            <FormControl fullWidth size="small">
              <InputLabel>⚡ 状态</InputLabel>
              <Select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                label="⚡ 状态"
                sx={{ borderRadius: 2 }}
              >
                <MenuItem value="all">全部状态</MenuItem>
                <MenuItem value="active">✅ 启用</MenuItem>
                <MenuItem value="inactive">❌ 禁用</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={3}>
            <Stack direction="row" spacing={2}>
              <Button
                variant="contained"
                startIcon={<Add />}
                onClick={() => setAddDialog(true)}
                sx={{
                  borderRadius: 2,
                  px: 3,
                  bgcolor: 'primary.main',
                  '&:hover': {
                    bgcolor: 'primary.dark',
                  }
                }}
              >
                添加平台
              </Button>
              <Button
                variant="outlined"
                startIcon={<Refresh />}
                onClick={loadPlatforms}
                disabled={loading}
                sx={{
                  borderRadius: 2,
                  px: 2,
                  borderColor: 'primary.main',
                  color: 'primary.main',
                  '&:hover': {
                    borderColor: 'primary.dark',
                    bgcolor: 'primary.50',
                  }
                }}
              >
                刷新
              </Button>
            </Stack>
          </Grid>
          <Grid item xs={12} md={2}>
            <Box sx={{ textAlign: 'right' }}>
              <Typography variant="caption" color="text.secondary">
                共 {filteredPlatforms.length} 个平台
              </Typography>
            </Box>
          </Grid>
        </Grid>
      </Paper>

      {/* 平台列表表格 */}
      <Paper sx={{
        borderRadius: 2,
        boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
        overflow: 'hidden'
      }}>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow sx={{
                bgcolor: 'grey.50',
                '& .MuiTableCell-head': {
                  fontWeight: 'bold',
                  color: 'text.primary',
                  borderBottom: '2px solid',
                  borderColor: 'divider'
                }
              }}>
                <TableCell>🏢 平台信息</TableCell>
                <TableCell>📂 类型</TableCell>
                <TableCell>🔗 API地址</TableCell>
                <TableCell>📡 连接状态</TableCell>
                <TableCell>⚡ 状态</TableCell>
                <TableCell align="center">🛠️ 操作</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={6} align="center">
                    <CircularProgress />
                  </TableCell>
                </TableRow>
              ) : paginatedPlatforms.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} align="center">
                    <Typography color="text.secondary">暂无平台配置</Typography>
                  </TableCell>
                </TableRow>
              ) : (
                paginatedPlatforms.map((platform) => {
                  const typeInfo = getPlatformTypeInfo(platform.platform_type);
                  const statusInfo = getConnectionStatusInfo(connectionStatuses[platform.platform_code]);

                  return (
                    <TableRow
                      key={platform.platform_code}
                      hover
                      sx={{
                        '&:hover': {
                          bgcolor: 'action.hover',
                          transform: 'scale(1.001)',
                          transition: 'all 0.2s ease-in-out'
                        },
                        '& .MuiTableCell-root': {
                          borderBottom: '1px solid rgba(224, 224, 224, 0.5)'
                        }
                      }}
                    >
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                          <Avatar sx={{
                            bgcolor: typeInfo.color,
                            width: 48,
                            height: 48,
                            fontSize: '1.2rem',
                            fontWeight: 'bold',
                            boxShadow: '0 2px 8px rgba(0,0,0,0.15)'
                          }}>
                            {platform.platform_name[0]}
                          </Avatar>
                          <Box>
                            <Typography variant="subtitle1" sx={{ fontWeight: 'medium', mb: 0.5 }}>
                              {platform.platform_name}
                            </Typography>
                            <Typography variant="caption" color="text.secondary" sx={{
                              bgcolor: 'grey.100',
                              px: 1,
                              py: 0.25,
                              borderRadius: 1,
                              fontFamily: 'monospace'
                            }}>
                              {platform.platform_code}
                            </Typography>
                          </Box>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={typeInfo.label}
                          size="small"
                          sx={{
                            bgcolor: typeInfo.color,
                            color: 'white',
                            fontWeight: 'medium',
                            boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
                          }}
                        />
                      </TableCell>
                      <TableCell>
                        <Box sx={{ maxWidth: 200 }}>
                          <Typography
                            variant="body2"
                            sx={{
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              whiteSpace: 'nowrap',
                              color: platform.api_base_url ? 'text.primary' : 'text.secondary',
                              fontFamily: platform.api_base_url ? 'monospace' : 'inherit',
                              fontSize: platform.api_base_url ? '0.8rem' : '0.875rem'
                            }}
                          >
                            {platform.api_base_url || '⚠️ 未配置'}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Chip
                          icon={statusInfo.icon}
                          label={statusInfo.label}
                          color={statusInfo.color}
                          size="small"
                          variant="outlined"
                          sx={{
                            fontWeight: 'medium',
                            '& .MuiChip-icon': {
                              fontSize: '1rem'
                            }
                          }}
                        />
                      </TableCell>
                      <TableCell>
                        <Switch
                          checked={platform.is_active}
                          size="small"
                          disabled
                          sx={{
                            '& .MuiSwitch-thumb': {
                              boxShadow: '0 2px 4px rgba(0,0,0,0.2)'
                            }
                          }}
                        />
                      </TableCell>
                      <TableCell align="center">
                        <Stack direction="row" spacing={0.5} justifyContent="center">
                          <IconButton
                            size="small"
                            onClick={() => handleViewClick(platform)}
                            sx={{
                              color: 'info.main',
                              '&:hover': {
                                bgcolor: 'info.50',
                                transform: 'scale(1.1)'
                              }
                            }}
                          >
                            <Visibility />
                          </IconButton>
                          <IconButton
                            size="small"
                            onClick={() => handleEditClick(platform)}
                            sx={{
                              color: 'primary.main',
                              '&:hover': {
                                bgcolor: 'primary.50',
                                transform: 'scale(1.1)'
                              }
                            }}
                          >
                            <Edit />
                          </IconButton>
                          <IconButton
                            size="small"
                            onClick={() => testConnection(platform.platform_code)}
                            sx={{
                              color: 'success.main',
                              '&:hover': {
                                bgcolor: 'success.50',
                                transform: 'scale(1.1)'
                              }
                            }}
                          >
                            <Link />
                          </IconButton>
                          <IconButton
                            size="small"
                            onClick={() => handleDeleteClick(platform)}
                            sx={{
                              color: 'error.main',
                              '&:hover': {
                                bgcolor: 'error.50',
                                transform: 'scale(1.1)'
                              }
                            }}
                          >
                            <Delete />
                          </IconButton>
                        </Stack>
                      </TableCell>
                    </TableRow>
                  );
                })
              )}
            </TableBody>
          </Table>
        </TableContainer>

        <Box sx={{
          borderTop: '1px solid',
          borderColor: 'divider',
          bgcolor: 'grey.25'
        }}>
          <TablePagination
            rowsPerPageOptions={[5, 10, 25]}
            component="div"
            count={filteredPlatforms.length}
            rowsPerPage={rowsPerPage}
            page={page}
            onPageChange={(e, newPage) => setPage(newPage)}
            onRowsPerPageChange={(e) => {
              setRowsPerPage(parseInt(e.target.value, 10));
              setPage(0);
            }}
            labelRowsPerPage="每页显示:"
            labelDisplayedRows={({ from, to, count }) =>
              `第 ${from}-${to} 条，共 ${count} 条`
            }
            sx={{
              '& .MuiTablePagination-toolbar': {
                px: 3,
                py: 2
              },
              '& .MuiTablePagination-selectLabel, & .MuiTablePagination-displayedRows': {
                fontSize: '0.875rem',
                color: 'text.secondary'
              }
            }}
          />
        </Box>
      </Paper>

      {/* 添加平台对话框 */}
      <Dialog
        open={addDialog}
        onClose={() => setAddDialog(false)}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 3,
            boxShadow: '0 8px 32px rgba(0,0,0,0.12)'
          }
        }}
      >
        <DialogTitle sx={{
          bgcolor: 'grey.50',
          color: 'text.primary',
          display: 'flex',
          alignItems: 'center',
          gap: 1,
          fontWeight: 'bold',
          borderBottom: '1px solid',
          borderColor: 'divider'
        }}>
          <Add />
          添加新平台
        </DialogTitle>
        <DialogContent sx={{ p: 3 }}>
          <Grid container spacing={3} sx={{ mt: 0.5 }}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="🏷️ 平台代码"
                value={formData.platform_code}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  platform_code: e.target.value
                }))}
                helperText="唯一标识符，如：ruanwenjie、xinmeiti"
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                  }
                }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="平台名称"
                value={formData.platform_name}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  platform_name: e.target.value
                }))}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>平台类型</InputLabel>
                <Select
                  value={formData.platform_type}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    platform_type: e.target.value
                  }))}
                  label="平台类型"
                >
                  {PLATFORM_TYPES.map(type => (
                    <MenuItem key={type.value} value={type.value}>
                      {type.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="🔗 API基础URL"
                value={formData.api_base_url}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  api_base_url: e.target.value
                }))}
                helperText="如：https://api.example.com"
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                  }
                }}
              />
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>🔐 认证类型</InputLabel>
                <Select
                  value={formData.auth_type}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    auth_type: e.target.value
                  }))}
                  label="🔐 认证类型"
                  sx={{ borderRadius: 2 }}
                >
                  {AUTH_TYPES.map(type => (
                    <MenuItem key={type.value} value={type.value}>
                      <Box>
                        <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                          {type.label}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {type.description}
                        </Typography>
                      </Box>
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            {/* 根据认证类型显示不同的字段 */}
            {formData.auth_type === 'api_key' && (
              <>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="🔑 API Key"
                    type="password"
                    value={formData.api_key}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      api_key: e.target.value
                    }))}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 2,
                      }
                    }}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="🔐 API Secret"
                    type="password"
                    value={formData.api_secret}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      api_secret: e.target.value
                    }))}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 2,
                      }
                    }}
                  />
                </Grid>
              </>
            )}

            {formData.auth_type === 'username_password' && (
              <>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="👤 用户名"
                    value={formData.username}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      username: e.target.value
                    }))}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 2,
                      }
                    }}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="🔒 密码"
                    type="password"
                    value={formData.password}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      password: e.target.value
                    }))}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 2,
                      }
                    }}
                  />
                </Grid>
              </>
            )}

            {formData.auth_type === 'bearer_token' && (
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="🎫 Access Token"
                  type="password"
                  value={formData.access_token}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    access_token: e.target.value
                  }))}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 2,
                    }
                  }}
                />
              </Grid>
            )}

            {formData.auth_type === 'oauth2' && (
              <>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="🔑 Client ID"
                    value={formData.api_key}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      api_key: e.target.value
                    }))}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 2,
                      }
                    }}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="🔐 Client Secret"
                    type="password"
                    value={formData.api_secret}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      api_secret: e.target.value
                    }))}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 2,
                      }
                    }}
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="🎫 Access Token (可选)"
                    type="password"
                    value={formData.access_token}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      access_token: e.target.value
                    }))}
                    helperText="如果已有访问令牌可填写，否则将通过OAuth流程获取"
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 2,
                      }
                    }}
                  />
                </Grid>
              </>
            )}
            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={3}
                label="📝 描述"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  description: e.target.value
                }))}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                  }
                }}
              />
            </Grid>
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.is_active}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      is_active: e.target.checked
                    }))}
                  />
                }
                label="启用平台"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions sx={{ p: 3, gap: 2 }}>
          <Button
            onClick={() => setAddDialog(false)}
            variant="outlined"
            sx={{
              borderRadius: 2,
              px: 3,
              borderColor: 'grey.300',
              color: 'text.secondary',
              '&:hover': {
                borderColor: 'grey.400',
                bgcolor: 'grey.50'
              }
            }}
          >
            取消
          </Button>
          <Button
            onClick={handleAddPlatform}
            variant="contained"
            sx={{
              borderRadius: 2,
              px: 4,
              bgcolor: 'primary.main',
              '&:hover': {
                bgcolor: 'primary.dark',
              }
            }}
          >
            添加平台
          </Button>
        </DialogActions>
      </Dialog>

      {/* 编辑平台对话框 */}
      <Dialog
        open={editDialog}
        onClose={() => setEditDialog(false)}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 3,
            boxShadow: '0 8px 32px rgba(0,0,0,0.12)'
          }
        }}
      >
        <DialogTitle sx={{
          bgcolor: 'grey.50',
          color: 'text.primary',
          display: 'flex',
          alignItems: 'center',
          gap: 1,
          fontWeight: 'bold',
          borderBottom: '1px solid',
          borderColor: 'divider'
        }}>
          <Edit />
          编辑平台配置
        </DialogTitle>
        <DialogContent sx={{ p: 3 }}>
          <Grid container spacing={3} sx={{ mt: 0.5 }}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="🏷️ 平台代码"
                value={formData.platform_code}
                disabled
                helperText="平台代码不可修改"
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                  }
                }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="🏢 平台名称"
                value={formData.platform_name}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  platform_name: e.target.value
                }))}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                  }
                }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>📂 平台类型</InputLabel>
                <Select
                  value={formData.platform_type}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    platform_type: e.target.value
                  }))}
                  label="📂 平台类型"
                  sx={{ borderRadius: 2 }}
                >
                  {PLATFORM_TYPES.map(type => (
                    <MenuItem key={type.value} value={type.value}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Box sx={{
                          width: 8,
                          height: 8,
                          borderRadius: '50%',
                          bgcolor: type.color
                        }} />
                        {type.label}
                      </Box>
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="🔗 API基础URL"
                value={formData.api_base_url}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  api_base_url: e.target.value
                }))}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                  }
                }}
              />
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>🔐 认证类型</InputLabel>
                <Select
                  value={formData.auth_type}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    auth_type: e.target.value
                  }))}
                  label="🔐 认证类型"
                  sx={{ borderRadius: 2 }}
                >
                  {AUTH_TYPES.map(type => (
                    <MenuItem key={type.value} value={type.value}>
                      <Box>
                        <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                          {type.label}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {type.description}
                        </Typography>
                      </Box>
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            {/* 根据认证类型显示不同的字段 */}
            {formData.auth_type === 'api_key' && (
              <>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="🔑 API Key"
                    type="password"
                    value={formData.api_key}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      api_key: e.target.value
                    }))}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 2,
                      }
                    }}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="🔐 API Secret"
                    type="password"
                    value={formData.api_secret}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      api_secret: e.target.value
                    }))}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 2,
                      }
                    }}
                  />
                </Grid>
              </>
            )}

            {formData.auth_type === 'username_password' && (
              <>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="👤 用户名"
                    value={formData.username}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      username: e.target.value
                    }))}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 2,
                      }
                    }}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="🔒 密码"
                    type="password"
                    value={formData.password}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      password: e.target.value
                    }))}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 2,
                      }
                    }}
                  />
                </Grid>
              </>
            )}

            {formData.auth_type === 'bearer_token' && (
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="🎫 Access Token"
                  type="password"
                  value={formData.access_token}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    access_token: e.target.value
                  }))}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 2,
                    }
                  }}
                />
              </Grid>
            )}

            {formData.auth_type === 'oauth2' && (
              <>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="🔑 Client ID"
                    value={formData.api_key}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      api_key: e.target.value
                    }))}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 2,
                      }
                    }}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="🔐 Client Secret"
                    type="password"
                    value={formData.api_secret}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      api_secret: e.target.value
                    }))}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 2,
                      }
                    }}
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="🎫 Access Token (可选)"
                    type="password"
                    value={formData.access_token}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      access_token: e.target.value
                    }))}
                    helperText="如果已有访问令牌可填写，否则将通过OAuth流程获取"
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 2,
                      }
                    }}
                  />
                </Grid>
              </>
            )}
            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={3}
                label="📝 描述"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  description: e.target.value
                }))}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                  }
                }}
              />
            </Grid>
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.is_active}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      is_active: e.target.checked
                    }))}
                  />
                }
                label="⚡ 启用平台"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions sx={{ p: 3, gap: 2 }}>
          <Button
            onClick={() => setEditDialog(false)}
            variant="outlined"
            sx={{
              borderRadius: 2,
              px: 3,
              borderColor: 'grey.300',
              color: 'text.secondary',
              '&:hover': {
                borderColor: 'grey.400',
                bgcolor: 'grey.50'
              }
            }}
          >
            取消
          </Button>
          <Button
            onClick={handleUpdatePlatform}
            variant="contained"
            sx={{
              borderRadius: 2,
              px: 4,
              bgcolor: 'primary.main',
              '&:hover': {
                bgcolor: 'primary.dark',
              }
            }}
          >
            保存更改
          </Button>
        </DialogActions>
      </Dialog>

      {/* 查看平台对话框 */}
      <Dialog
        open={viewDialog}
        onClose={() => setViewDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>平台详情</DialogTitle>
        <DialogContent>
          {selectedPlatform && (
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12} md={6}>
                <Typography variant="body2" color="text.secondary">平台代码</Typography>
                <Typography variant="body1" sx={{ mb: 2 }}>
                  {selectedPlatform.platform_code}
                </Typography>
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography variant="body2" color="text.secondary">平台名称</Typography>
                <Typography variant="body1" sx={{ mb: 2 }}>
                  {selectedPlatform.platform_name}
                </Typography>
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography variant="body2" color="text.secondary">平台类型</Typography>
                <Chip
                  label={getPlatformTypeInfo(selectedPlatform.platform_type).label}
                  size="small"
                  sx={{ mb: 2 }}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography variant="body2" color="text.secondary">连接状态</Typography>
                <Chip
                  icon={getConnectionStatusInfo(connectionStatuses[selectedPlatform.platform_code]).icon}
                  label={getConnectionStatusInfo(connectionStatuses[selectedPlatform.platform_code]).label}
                  color={getConnectionStatusInfo(connectionStatuses[selectedPlatform.platform_code]).color}
                  size="small"
                  sx={{ mb: 2 }}
                />
              </Grid>
              <Grid item xs={12}>
                <Typography variant="body2" color="text.secondary">API基础URL</Typography>
                <Typography variant="body1" sx={{ mb: 2 }}>
                  {selectedPlatform.api_base_url || '未配置'}
                </Typography>
              </Grid>
              <Grid item xs={12}>
                <Typography variant="body2" color="text.secondary">描述</Typography>
                <Typography variant="body1">
                  {selectedPlatform.description || '暂无描述'}
                </Typography>
              </Grid>
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setViewDialog(false)}>关闭</Button>
        </DialogActions>
      </Dialog>

      {/* 删除确认对话框 */}
      <Dialog
        open={deleteDialog}
        onClose={() => setDeleteDialog(false)}
        maxWidth="sm"
      >
        <DialogTitle>确认删除</DialogTitle>
        <DialogContent>
          <Typography>
            确定要删除平台 "{selectedPlatform?.platform_name}" 吗？此操作不可撤销。
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialog(false)}>取消</Button>
          <Button onClick={handleDeletePlatform} color="error" variant="contained">
            删除
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default PlatformIntegration;
