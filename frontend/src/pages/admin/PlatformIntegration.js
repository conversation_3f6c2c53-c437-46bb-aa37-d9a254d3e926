import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  CardActions,
  Paper,
  Button,
  IconButton,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Toolbar,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  CircularProgress,
  Divider,
  InputAdornment,
  Stack,
  Switch,
  FormControlLabel,
  Avatar,
} from '@mui/material';
import {
  Add,
  Edit,
  Delete,
  Visibility,
  VisibilityOff,
  Save,
  Cancel,
  Search,
  FilterList,
  CloudQueue,
  CloudOff,
  CloudDone,
  Link,
  LinkOff,
  Settings,
  Security,
  CheckCircle,
  Error as ErrorIcon,
  Warning,
  ExpandMore,
  ChevronRight,
  Api,
  Security,
  NetworkCheck,
  Sync,
  SyncDisabled,
  SyncProblem,
  AttachMoney,
  CompareArrows,
  Article,
  ShoppingCart,
  Inventory,
  PriceCheck,
  WifiTethering,
  WifiTetheringOff,
  Settings,
  PowerSettingsNew,
  AccessTime,
  Timer,
  ErrorOutline,
  CheckCircle,
  Cancel,
  Pending,
  Loop,
  ArrowForward,
  ArrowBack,
} from '@mui/icons-material';
import { format, formatDistanceToNow, parseISO } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { useSnackbar } from 'notistack';
import platformService from '../../services/platformService';

// Platform status indicator component
const PlatformStatusIndicator = ({ status, size = 'small' }) => {
  const getStatusColor = () => {
    switch (status) {
      case 'connected':
        return 'success';
      case 'disconnected':
        return 'error';
      case 'connecting':
        return 'warning';
      case 'error':
        return 'error';
      default:
        return 'default';
    }
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'connected':
        return <WifiTethering />;
      case 'disconnected':
        return <WifiTetheringOff />;
      case 'connecting':
        return <Sync />;
      case 'error':
        return <SyncProblem />;
      default:
        return <Info />;
    }
  };

  return (
    <Chip
      icon={getStatusIcon()}
      label={status}
      color={getStatusColor()}
      size={size}
      variant="outlined"
    />
  );
};

// Tab panel component
function TabPanel({ children, value, index, ...other }) {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`platform-tabpanel-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const PlatformIntegration = () => {
  const { enqueueSnackbar } = useSnackbar();
  
  // State management
  const [loading, setLoading] = useState(false);
  const [selectedPlatform, setSelectedPlatform] = useState(null);
  const [activeTab, setActiveTab] = useState(0);
  const [platforms, setPlatforms] = useState([]);
  const [credentials, setCredentials] = useState([]);
  const [routes, setRoutes] = useState([]);
  const [tokens, setTokens] = useState({});
  const [connectionStatuses, setConnectionStatuses] = useState({});
  
  // Dialog states
  const [addPlatformDialog, setAddPlatformDialog] = useState(false);
  const [editConfigDialog, setEditConfigDialog] = useState(false);
  const [credentialDialog, setCredentialDialog] = useState(false);
  const [testConnectionDialog, setTestConnectionDialog] = useState(false);
  const [routeDetailDialog, setRouteDetailDialog] = useState(false);
  const [selectedRoute, setSelectedRoute] = useState(null);
  
  // Filter states
  const [platformFilter, setPlatformFilter] = useState('all');
  const [routeFilter, setRouteFilter] = useState({
    status: 'all',
    type: 'all',
    platform: 'all'
  });
  
  // Pagination states
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  
  // Right sidebar state
  const [rightDrawerOpen, setRightDrawerOpen] = useState(true);
  
  // Form states
  const [platformFormData, setPlatformFormData] = useState({
    platform_code: '',
    platform_name: '',
    platform_type: 'content_publish',
    api_base_url: '',
    description: '',
    is_active: true,
    features: [],
    rate_limit: {
      requests_per_minute: 60,
      requests_per_hour: 1000
    }
  });
  
  const [credentialFormData, setCredentialFormData] = useState({
    api_key: '',
    api_secret: '',
    access_token: '',
    refresh_token: '',
    webhook_url: '',
    additional_config: {}
  });

  // Ruanwenjie specific states
  const [ruanwenjieData, setRuanwenjieData] = useState({
    mediaResources: [],
    orderSyncStatus: null,
    priceComparison: []
  });

  // Load initial data
  useEffect(() => {
    loadPlatforms();
    loadCredentials();
    loadRoutes();
  }, []);

  // Auto-refresh connection status
  useEffect(() => {
    const interval = setInterval(() => {
      if (selectedPlatform) {
        checkConnectionStatus(selectedPlatform.platform_code);
      }
    }, 30000); // Check every 30 seconds

    return () => clearInterval(interval);
  }, [selectedPlatform]);

  // Data loading functions
  const loadPlatforms = async () => {
    setLoading(true);
    try {
      const response = await platformService.getPlatformConfigs();
      setPlatforms(response.data || []);
      
      // Load connection status for each platform
      const statuses = {};
      for (const platform of response.data || []) {
        statuses[platform.platform_code] = await checkConnectionStatus(platform.platform_code);
      }
      setConnectionStatuses(statuses);
    } catch (error) {
      enqueueSnackbar('加载平台配置失败', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const loadCredentials = async () => {
    try {
      const response = await platformService.getCredentialsList();
      setCredentials(response);
    } catch (error) {
      console.error('Failed to load credentials:', error);
    }
  };

  const loadRoutes = async () => {
    try {
      const response = await platformService.getRoutes({
        page: page + 1,
        size: rowsPerPage
      });
      setRoutes(response.data || []);
    } catch (error) {
      console.error('Failed to load routes:', error);
    }
  };

  const checkConnectionStatus = async (platformCode) => {
    try {
      const response = await platformService.testConnection(platformCode);
      return response.is_connected ? 'connected' : 'disconnected';
    } catch (error) {
      return 'error';
    }
  };

  // Platform configuration handlers
  const handleAddPlatform = async () => {
    try {
      await platformService.createPlatformConfig(platformFormData);
      enqueueSnackbar('平台添加成功', { variant: 'success' });
      setAddPlatformDialog(false);
      loadPlatforms();
    } catch (error) {
      enqueueSnackbar('平台添加失败', { variant: 'error' });
    }
  };

  const handleUpdatePlatform = async () => {
    try {
      await platformService.updatePlatformConfig(
        selectedPlatform.platform_code,
        platformFormData
      );
      enqueueSnackbar('平台配置更新成功', { variant: 'success' });
      setEditConfigDialog(false);
      loadPlatforms();
    } catch (error) {
      enqueueSnackbar('平台配置更新失败', { variant: 'error' });
    }
  };

  const handleTogglePlatform = async (platform) => {
    try {
      await platformService.updatePlatformConfig(platform.platform_code, {
        is_active: !platform.is_active
      });
      enqueueSnackbar(
        platform.is_active ? '平台已禁用' : '平台已启用',
        { variant: 'success' }
      );
      loadPlatforms();
    } catch (error) {
      enqueueSnackbar('操作失败', { variant: 'error' });
    }
  };

  // Credential management handlers
  const handleSaveCredential = async () => {
    try {
      await platformService.setCredential(
        selectedPlatform.platform_code,
        credentialFormData
      );
      enqueueSnackbar('凭证保存成功', { variant: 'success' });
      setCredentialDialog(false);
      loadCredentials();
    } catch (error) {
      enqueueSnackbar('凭证保存失败', { variant: 'error' });
    }
  };

  const handleTestConnection = async () => {
    setTestConnectionDialog(true);
    try {
      const response = await platformService.testConnection(
        selectedPlatform.platform_code
      );
      if (response.is_connected) {
        enqueueSnackbar('连接测试成功', { variant: 'success' });
      } else {
        enqueueSnackbar('连接测试失败', { variant: 'error' });
      }
    } catch (error) {
      enqueueSnackbar('连接测试错误', { variant: 'error' });
    } finally {
      setTestConnectionDialog(false);
    }
  };

  // Token management handlers
  const handleRefreshToken = async (platformCode, forceRefresh = false) => {
    try {
      const response = await platformService.refreshToken(platformCode, forceRefresh);
      enqueueSnackbar('令牌刷新成功', { variant: 'success' });
      setTokens(prev => ({
        ...prev,
        [platformCode]: response
      }));
    } catch (error) {
      enqueueSnackbar('令牌刷新失败', { variant: 'error' });
    }
  };

  const handleRevokeToken = async (platformCode) => {
    try {
      await platformService.revokeToken(platformCode, 'Manual revocation');
      enqueueSnackbar('令牌已撤销', { variant: 'success' });
      setTokens(prev => ({
        ...prev,
        [platformCode]: null
      }));
    } catch (error) {
      enqueueSnackbar('令牌撤销失败', { variant: 'error' });
    }
  };

  // Route management handlers
  const handleRetryRoute = async (routeId) => {
    try {
      await platformService.retryRoute(routeId);
      enqueueSnackbar('路由重试已启动', { variant: 'success' });
      loadRoutes();
    } catch (error) {
      enqueueSnackbar('路由重试失败', { variant: 'error' });
    }
  };

  const handleViewRouteDetail = async (route) => {
    try {
      const detail = await platformService.getRouteDetail(route.id);
      setSelectedRoute(detail);
      setRouteDetailDialog(true);
    } catch (error) {
      enqueueSnackbar('加载路由详情失败', { variant: 'error' });
    }
  };

  // Ruanwenjie specific handlers
  const loadRuanwenjieMedia = async () => {
    try {
      const response = await platformService.getRuanwenjieMediaResources();
      setRuanwenjieData(prev => ({
        ...prev,
        mediaResources: response.data || []
      }));
    } catch (error) {
      enqueueSnackbar('加载媒体资源失败', { variant: 'error' });
    }
  };

  const handleSyncRuanwenjieOrders = async () => {
    try {
      const response = await platformService.syncRuanwenjieOrders();
      enqueueSnackbar('订单同步成功', { variant: 'success' });
      setRuanwenjieData(prev => ({
        ...prev,
        orderSyncStatus: response
      }));
    } catch (error) {
      enqueueSnackbar('订单同步失败', { variant: 'error' });
    }
  };

  const handleComparePrices = async (mediaIds) => {
    try {
      const response = await platformService.compareRuanwenjiePrices(mediaIds);
      setRuanwenjieData(prev => ({
        ...prev,
        priceComparison: response.data || []
      }));
    } catch (error) {
      enqueueSnackbar('价格比较失败', { variant: 'error' });
    }
  };

  // Filter functions
  const filteredPlatforms = useMemo(() => {
    if (platformFilter === 'all') return platforms;
    if (platformFilter === 'active') return platforms.filter(p => p.is_active);
    if (platformFilter === 'inactive') return platforms.filter(p => !p.is_active);
    return platforms.filter(p => p.platform_type === platformFilter);
  }, [platforms, platformFilter]);

  const filteredRoutes = useMemo(() => {
    let filtered = routes;
    if (routeFilter.status !== 'all') {
      filtered = filtered.filter(r => r.route_status === routeFilter.status);
    }
    if (routeFilter.type !== 'all') {
      filtered = filtered.filter(r => r.route_type === routeFilter.type);
    }
    if (routeFilter.platform !== 'all') {
      filtered = filtered.filter(r => r.platform_code === routeFilter.platform);
    }
    return filtered;
  }, [routes, routeFilter]);

  // Render platform list sidebar
  const renderPlatformList = () => (
    <Paper sx={{ p: 2, height: '100%' }}>
      <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h6">平台列表</Typography>
        <IconButton color="primary" onClick={() => setAddPlatformDialog(true)}>
          <Add />
        </IconButton>
      </Box>
      
      <FormControl fullWidth size="small" sx={{ mb: 2 }}>
        <Select
          value={platformFilter}
          onChange={(e) => setPlatformFilter(e.target.value)}
        >
          <MenuItem value="all">全部平台</MenuItem>
          <MenuItem value="active">已激活</MenuItem>
          <MenuItem value="inactive">未激活</MenuItem>
          <MenuItem value="ai_search">AI搜索引擎</MenuItem>
          <MenuItem value="content_publish">内容发布</MenuItem>
          <MenuItem value="media_resource">媒体资源</MenuItem>
        </Select>
      </FormControl>

      <List>
        {filteredPlatforms.map((platform) => (
          <React.Fragment key={platform.platform_code}>
            <ListItemButton
              selected={selectedPlatform?.platform_code === platform.platform_code}
              onClick={() => setSelectedPlatform(platform)}
            >
              <ListItemIcon>
                <Avatar sx={{ bgcolor: platform.is_active ? 'primary.main' : 'grey.500' }}>
                  {platform.platform_name[0]}
                </Avatar>
              </ListItemIcon>
              <ListItemText
                primary={platform.platform_name}
                secondary={
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <PlatformStatusIndicator
                      status={connectionStatuses[platform.platform_code] || 'disconnected'}
                      size="small"
                    />
                  </Box>
                }
              />
              <ListItemSecondaryAction>
                <Switch
                  edge="end"
                  checked={platform.is_active}
                  onChange={() => handleTogglePlatform(platform)}
                />
              </ListItemSecondaryAction>
            </ListItemButton>
            <Divider />
          </React.Fragment>
        ))}
      </List>

      {filteredPlatforms.length === 0 && (
        <Box sx={{ textAlign: 'center', py: 4 }}>
          <Typography color="text.secondary">暂无平台</Typography>
        </Box>
      )}
    </Paper>
  );

  // Render platform configuration tab
  const renderConfigurationTab = () => (
    <Box>
      {selectedPlatform ? (
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
                  <Typography variant="h6">基本信息</Typography>
                  <Button
                    startIcon={<Edit />}
                    onClick={() => {
                      setPlatformFormData(selectedPlatform);
                      setEditConfigDialog(true);
                    }}
                  >
                    编辑配置
                  </Button>
                </Box>
                
                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <Typography variant="body2" color="text.secondary">平台代码</Typography>
                    <Typography variant="body1" sx={{ mb: 2 }}>
                      {selectedPlatform.platform_code}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Typography variant="body2" color="text.secondary">平台名称</Typography>
                    <Typography variant="body1" sx={{ mb: 2 }}>
                      {selectedPlatform.platform_name}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Typography variant="body2" color="text.secondary">平台类型</Typography>
                    <Chip label={selectedPlatform.platform_type} color="primary" size="small" />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Typography variant="body2" color="text.secondary">状态</Typography>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={selectedPlatform.is_active}
                          onChange={() => handleTogglePlatform(selectedPlatform)}
                        />
                      }
                      label={selectedPlatform.is_active ? '已启用' : '已禁用'}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <Typography variant="body2" color="text.secondary">API基础URL</Typography>
                    <Typography variant="body1" sx={{ mb: 2 }}>
                      {selectedPlatform.api_base_url || '未配置'}
                    </Typography>
                  </Grid>
                  <Grid item xs={12}>
                    <Typography variant="body2" color="text.secondary">描述</Typography>
                    <Typography variant="body1">
                      {selectedPlatform.description || '暂无描述'}
                    </Typography>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 2 }}>功能特性</Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                  {selectedPlatform.features?.map((feature) => (
                    <Chip key={feature} label={feature} variant="outlined" />
                  )) || <Typography color="text.secondary">未配置功能</Typography>}
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 2 }}>速率限制</Typography>
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">每分钟请求</Typography>
                    <Typography variant="h4">
                      {selectedPlatform.rate_limit?.requests_per_minute || 60}
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">每小时请求</Typography>
                    <Typography variant="h4">
                      {selectedPlatform.rate_limit?.requests_per_hour || 1000}
                    </Typography>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      ) : (
        <Box sx={{ textAlign: 'center', py: 8 }}>
          <SettingsApplications sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
          <Typography variant="h6" color="text.secondary">
            请从左侧选择一个平台查看配置
          </Typography>
        </Box>
      )}
    </Box>
  );

  // Render authentication management tab
  const renderAuthenticationTab = () => (
    <Box>
      {selectedPlatform ? (
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
                  <Typography variant="h6">凭证配置</Typography>
                  <Box>
                    <Button
                      startIcon={<VpnKey />}
                      onClick={() => setCredentialDialog(true)}
                      sx={{ mr: 1 }}
                    >
                      配置凭证
                    </Button>
                    <Button
                      startIcon={<NetworkCheck />}
                      onClick={handleTestConnection}
                      variant="outlined"
                    >
                      测试连接
                    </Button>
                  </Box>
                </Box>

                {credentials.find(c => c.platform_code === selectedPlatform.platform_code) ? (
                  <Grid container spacing={2}>
                    <Grid item xs={12} md={6}>
                      <Typography variant="body2" color="text.secondary">API Key</Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="body1">
                          ****{credentials.find(c => c.platform_code === selectedPlatform.platform_code)?.api_key_hint}
                        </Typography>
                        <IconButton size="small">
                          <VisibilityOff />
                        </IconButton>
                      </Box>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography variant="body2" color="text.secondary">验证状态</Typography>
                      <Chip
                        label={credentials.find(c => c.platform_code === selectedPlatform.platform_code)?.is_valid ? '有效' : '无效'}
                        color={credentials.find(c => c.platform_code === selectedPlatform.platform_code)?.is_valid ? 'success' : 'error'}
                        size="small"
                      />
                    </Grid>
                    <Grid item xs={12}>
                      <Typography variant="body2" color="text.secondary">最后验证时间</Typography>
                      <Typography variant="body1">
                        {credentials.find(c => c.platform_code === selectedPlatform.platform_code)?.last_validated_at ?
                          format(parseISO(credentials.find(c => c.platform_code === selectedPlatform.platform_code).last_validated_at), 'yyyy-MM-dd HH:mm:ss') :
                          '从未验证'
                        }
                      </Typography>
                    </Grid>
                  </Grid>
                ) : (
                  <Alert severity="warning">
                    该平台尚未配置凭证，请点击"配置凭证"按钮进行设置
                  </Alert>
                )}
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 2 }}>令牌状态</Typography>
                {tokens[selectedPlatform.platform_code] ? (
                  <Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                      <Typography variant="body2" color="text.secondary">当前令牌</Typography>
                      <Chip
                        label="活跃"
                        color="success"
                        size="small"
                        icon={<CheckCircle />}
                      />
                    </Box>
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="body2" color="text.secondary">过期时间</Typography>
                      <Typography variant="body1">
                        {tokens[selectedPlatform.platform_code].expires_at ?
                          formatDistanceToNow(parseISO(tokens[selectedPlatform.platform_code].expires_at), {
                            addSuffix: true,
                            locale: zhCN
                          }) :
                          '永不过期'
                        }
                      </Typography>
                    </Box>
                    <Stack direction="row" spacing={1}>
                      <Button
                        size="small"
                        startIcon={<Refresh />}
                        onClick={() => handleRefreshToken(selectedPlatform.platform_code)}
                      >
                        刷新令牌
                      </Button>
                      <Button
                        size="small"
                        startIcon={<Clear />}
                        color="error"
                        onClick={() => handleRevokeToken(selectedPlatform.platform_code)}
                      >
                        撤销令牌
                      </Button>
                    </Stack>
                  </Box>
                ) : (
                  <Alert severity="info">暂无活跃令牌</Alert>
                )}
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 2 }}>连接监控</Typography>
                <Box sx={{ mb: 2 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="body2">连接状态</Typography>
                    <PlatformStatusIndicator
                      status={connectionStatuses[selectedPlatform.platform_code] || 'disconnected'}
                    />
                  </Box>
                  <LinearProgress
                    variant="determinate"
                    value={connectionStatuses[selectedPlatform.platform_code] === 'connected' ? 100 : 0}
                    color={connectionStatuses[selectedPlatform.platform_code] === 'connected' ? 'success' : 'error'}
                  />
                </Box>
                <Button
                  fullWidth
                  variant="outlined"
                  startIcon={<NetworkCheck />}
                  onClick={() => checkConnectionStatus(selectedPlatform.platform_code)}
                >
                  重新检查连接
                </Button>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      ) : (
        <Box sx={{ textAlign: 'center', py: 8 }}>
          <Security sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
          <Typography variant="h6" color="text.secondary">
            请从左侧选择一个平台管理认证
          </Typography>
        </Box>
      )}
    </Box>
  );

  // Render routing management tab
  const renderRoutingTab = () => (
    <Box>
      <Box sx={{ mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={3}>
            <FormControl fullWidth size="small">
              <InputLabel>状态筛选</InputLabel>
              <Select
                value={routeFilter.status}
                onChange={(e) => setRouteFilter(prev => ({ ...prev, status: e.target.value }))}
                label="状态筛选"
              >
                <MenuItem value="all">全部状态</MenuItem>
                <MenuItem value="pending">待处理</MenuItem>
                <MenuItem value="processing">处理中</MenuItem>
                <MenuItem value="success">成功</MenuItem>
                <MenuItem value="failed">失败</MenuItem>
                <MenuItem value="retrying">重试中</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={3}>
            <FormControl fullWidth size="small">
              <InputLabel>平台筛选</InputLabel>
              <Select
                value={routeFilter.platform}
                onChange={(e) => setRouteFilter(prev => ({ ...prev, platform: e.target.value }))}
                label="平台筛选"
              >
                <MenuItem value="all">全部平台</MenuItem>
                {platforms.map(p => (
                  <MenuItem key={p.platform_code} value={p.platform_code}>
                    {p.platform_name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={3}>
            <TextField
              fullWidth
              size="small"
              placeholder="搜索路由..."
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Search />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>
          <Grid item xs={12} md={3}>
            <Button
              fullWidth
              variant="contained"
              startIcon={<Add />}
              onClick={() => {/* Handle create route */}}
            >
              创建路由
            </Button>
          </Grid>
        </Grid>
      </Box>

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>路由ID</TableCell>
              <TableCell>平台</TableCell>
              <TableCell>类型</TableCell>
              <TableCell>状态</TableCell>
              <TableCell>创建时间</TableCell>
              <TableCell>响应时间</TableCell>
              <TableCell align="center">操作</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredRoutes.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage).map((route) => (
              <TableRow key={route.id}>
                <TableCell>
                  <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
                    {route.id.slice(0, 8)}...
                  </Typography>
                </TableCell>
                <TableCell>{route.platform_code}</TableCell>
                <TableCell>
                  <Chip label={route.route_type} size="small" />
                </TableCell>
                <TableCell>
                  <Chip
                    label={route.route_status}
                    color={
                      route.route_status === 'success' ? 'success' :
                      route.route_status === 'failed' ? 'error' :
                      route.route_status === 'processing' ? 'info' :
                      'default'
                    }
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  {format(parseISO(route.created_at), 'MM-dd HH:mm')}
                </TableCell>
                <TableCell>
                  {route.response_time ? `${route.response_time}ms` : '-'}
                </TableCell>
                <TableCell align="center">
                  <IconButton
                    size="small"
                    onClick={() => handleViewRouteDetail(route)}
                  >
                    <Visibility />
                  </IconButton>
                  {route.route_status === 'failed' && (
                    <IconButton
                      size="small"
                      onClick={() => handleRetryRoute(route.id)}
                      color="warning"
                    >
                      <Refresh />
                    </IconButton>
                  )}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
        <TablePagination
          rowsPerPageOptions={[5, 10, 25]}
          component="div"
          count={filteredRoutes.length}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={(e, newPage) => setPage(newPage)}
          onRowsPerPageChange={(e) => {
            setRowsPerPage(parseInt(e.target.value, 10));
            setPage(0);
          }}
        />
      </TableContainer>

      {filteredRoutes.length === 0 && (
        <Box sx={{ textAlign: 'center', py: 8 }}>
          <Route sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
          <Typography variant="h6" color="text.secondary">
            暂无路由记录
          </Typography>
        </Box>
      )}
    </Box>
  );

  // Render Ruanwenjie special features tab
  const renderRuanwenjieTab = () => (
    <Box>
      {selectedPlatform?.platform_code === 'ruanwenjie' ? (
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
                  <Typography variant="h6">媒体资源浏览</Typography>
                  <Button
                    startIcon={<Refresh />}
                    onClick={loadRuanwenjieMedia}
                  >
                    刷新资源
                  </Button>
                </Box>
                
                <Grid container spacing={2}>
                  {ruanwenjieData.mediaResources.map((media) => (
                    <Grid item xs={12} md={4} key={media.id}>
                      <Card variant="outlined">
                        <CardContent>
                          <Typography variant="subtitle1" gutterBottom>
                            {media.name}
                          </Typography>
                          <Typography variant="body2" color="text.secondary" gutterBottom>
                            {media.category} | {media.type}
                          </Typography>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 2 }}>
                            <Typography variant="h6" color="primary">
                              ¥{media.price}
                            </Typography>
                            <Chip
                              label={media.status}
                              color={media.status === 'available' ? 'success' : 'default'}
                              size="small"
                            />
                          </Box>
                        </CardContent>
                      </Card>
                    </Grid>
                  ))}
                </Grid>

                {ruanwenjieData.mediaResources.length === 0 && (
                  <Alert severity="info">暂无媒体资源数据</Alert>
                )}
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
                  <Typography variant="h6">订单同步状态</Typography>
                  <Button
                    startIcon={<Sync />}
                    onClick={handleSyncRuanwenjieOrders}
                    variant="outlined"
                  >
                    同步订单
                  </Button>
                </Box>
                
                {ruanwenjieData.orderSyncStatus ? (
                  <Box>
                    <Grid container spacing={2}>
                      <Grid item xs={6}>
                        <Typography variant="body2" color="text.secondary">同步时间</Typography>
                        <Typography variant="body1">
                          {format(parseISO(ruanwenjieData.orderSyncStatus.sync_time), 'yyyy-MM-dd HH:mm:ss')}
                        </Typography>
                      </Grid>
                      <Grid item xs={6}>
                        <Typography variant="body2" color="text.secondary">同步数量</Typography>
                        <Typography variant="h4">
                          {ruanwenjieData.orderSyncStatus.synced_count}
                        </Typography>
                      </Grid>
                      <Grid item xs={12}>
                        <LinearProgress
                          variant="determinate"
                          value={ruanwenjieData.orderSyncStatus.sync_progress || 0}
                        />
                      </Grid>
                    </Grid>
                  </Box>
                ) : (
                  <Alert severity="info">点击"同步订单"开始同步</Alert>
                )}
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 3 }}>价格对比表</Typography>
                
                {ruanwenjieData.priceComparison.length > 0 ? (
                  <TableContainer>
                    <Table size="small">
                      <TableHead>
                        <TableRow>
                          <TableCell>媒体</TableCell>
                          <TableCell align="right">原价</TableCell>
                          <TableCell align="right">优惠价</TableCell>
                          <TableCell align="right">折扣</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {ruanwenjieData.priceComparison.map((item) => (
                          <TableRow key={item.media_id}>
                            <TableCell>{item.media_name}</TableCell>
                            <TableCell align="right">¥{item.original_price}</TableCell>
                            <TableCell align="right">¥{item.discount_price}</TableCell>
                            <TableCell align="right">
                              <Chip
                                label={`${item.discount_rate}%`}
                                color="secondary"
                                size="small"
                              />
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                ) : (
                  <Alert severity="info">选择媒体资源进行价格比较</Alert>
                )}
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      ) : (
        <Box sx={{ textAlign: 'center', py: 8 }}>
          <Store sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
          <Typography variant="h6" color="text.secondary">
            请选择软文街平台查看专项功能
          </Typography>
        </Box>
      )}
    </Box>
  );

  // Render connection status sidebar
  const renderConnectionStatus = () => (
    <Paper sx={{ p: 2, height: '100%' }}>
      <Typography variant="h6" sx={{ mb: 2 }}>连接状态</Typography>
      
      {selectedPlatform && (
        <Box sx={{ mb: 3 }}>
          <Card variant="outlined">
            <CardContent>
              <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                当前平台
              </Typography>
              <Typography variant="h6" gutterBottom>
                {selectedPlatform.platform_name}
              </Typography>
              <PlatformStatusIndicator
                status={connectionStatuses[selectedPlatform.platform_code] || 'disconnected'}
              />
            </CardContent>
          </Card>
        </Box>
      )}

      <Typography variant="subtitle2" sx={{ mb: 2 }}>最近活动</Typography>
      
      <Timeline position="left">
        {routes.slice(0, 5).map((route, index) => (
          <TimelineItem key={route.id}>
            <TimelineOppositeContent sx={{ m: 'auto 0' }}>
              <Typography variant="caption" color="text.secondary">
                {format(parseISO(route.created_at), 'HH:mm')}
              </Typography>
            </TimelineOppositeContent>
            <TimelineSeparator>
              <TimelineConnector sx={{ bgcolor: index === 0 ? 'primary.main' : 'grey.300' }} />
              <TimelineDot color={
                route.route_status === 'success' ? 'success' :
                route.route_status === 'failed' ? 'error' :
                'grey'
              }>
                {route.route_status === 'success' ? <Check /> :
                 route.route_status === 'failed' ? <Clear /> :
                 <Loop />}
              </TimelineDot>
              <TimelineConnector sx={{ bgcolor: 'grey.300' }} />
            </TimelineSeparator>
            <TimelineContent sx={{ py: '12px', px: 2 }}>
              <Typography variant="body2">
                {route.route_type}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {route.platform_code}
              </Typography>
            </TimelineContent>
          </TimelineItem>
        ))}
      </Timeline>

      <Divider sx={{ my: 2 }} />
      
      <Typography variant="subtitle2" sx={{ mb: 2 }}>平台统计</Typography>
      
      <Grid container spacing={1}>
        <Grid item xs={6}>
          <Card variant="outlined">
            <CardContent sx={{ p: 1.5 }}>
              <Typography variant="caption" color="text.secondary">
                已连接
              </Typography>
              <Typography variant="h6">
                {Object.values(connectionStatuses).filter(s => s === 'connected').length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={6}>
          <Card variant="outlined">
            <CardContent sx={{ p: 1.5 }}>
              <Typography variant="caption" color="text.secondary">
                断开连接
              </Typography>
              <Typography variant="h6">
                {Object.values(connectionStatuses).filter(s => s !== 'connected').length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Paper>
  );

  return (
    <Container maxWidth={false} sx={{ py: 3 }}>
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" gutterBottom>
          平台集成管理
        </Typography>
        <Typography variant="body1" color="text.secondary">
          管理和监控所有第三方平台集成
        </Typography>
      </Box>

      <Grid container spacing={3}>
        {/* Left sidebar - Platform list */}
        <Grid item xs={12} md={2.5}>
          {renderPlatformList()}
        </Grid>

        {/* Main content area */}
        <Grid item xs={12} md={rightDrawerOpen ? 7 : 9.5}>
          <Paper sx={{ mb: 2 }}>
            <Tabs
              value={activeTab}
              onChange={(e, newValue) => setActiveTab(newValue)}
              variant="scrollable"
              scrollButtons="auto"
            >
              <Tab icon={<Settings />} label="平台配置" />
              <Tab icon={<Security />} label="认证管理" />
              <Tab icon={<Route />} label="路由管理" />
              <Tab icon={<Store />} label="软文街专项" />
            </Tabs>
          </Paper>

          <Paper>
            <TabPanel value={activeTab} index={0}>
              {renderConfigurationTab()}
            </TabPanel>
            <TabPanel value={activeTab} index={1}>
              {renderAuthenticationTab()}
            </TabPanel>
            <TabPanel value={activeTab} index={2}>
              {renderRoutingTab()}
            </TabPanel>
            <TabPanel value={activeTab} index={3}>
              {renderRuanwenjieTab()}
            </TabPanel>
          </Paper>
        </Grid>

        {/* Right sidebar - Connection status */}
        {rightDrawerOpen && (
          <Grid item xs={12} md={2.5}>
            {renderConnectionStatus()}
          </Grid>
        )}
      </Grid>

      {/* Add Platform Dialog */}
      <Dialog
        open={addPlatformDialog}
        onClose={() => setAddPlatformDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>添加新平台</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="平台代码"
                value={platformFormData.platform_code}
                onChange={(e) => setPlatformFormData(prev => ({
                  ...prev,
                  platform_code: e.target.value
                }))}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="平台名称"
                value={platformFormData.platform_name}
                onChange={(e) => setPlatformFormData(prev => ({
                  ...prev,
                  platform_name: e.target.value
                }))}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>平台类型</InputLabel>
                <Select
                  value={platformFormData.platform_type}
                  onChange={(e) => setPlatformFormData(prev => ({
                    ...prev,
                    platform_type: e.target.value
                  }))}
                  label="平台类型"
                >
                  {platformService.getPlatformTypes().map(type => (
                    <MenuItem key={type.value} value={type.value}>
                      {type.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="API基础URL"
                value={platformFormData.api_base_url}
                onChange={(e) => setPlatformFormData(prev => ({
                  ...prev,
                  api_base_url: e.target.value
                }))}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={3}
                label="描述"
                value={platformFormData.description}
                onChange={(e) => setPlatformFormData(prev => ({
                  ...prev,
                  description: e.target.value
                }))}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setAddPlatformDialog(false)}>取消</Button>
          <Button onClick={handleAddPlatform} variant="contained">添加</Button>
        </DialogActions>
      </Dialog>

      {/* Edit Configuration Dialog */}
      <Dialog
        open={editConfigDialog}
        onClose={() => setEditConfigDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>编辑平台配置</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="平台名称"
                value={platformFormData.platform_name}
                onChange={(e) => setPlatformFormData(prev => ({
                  ...prev,
                  platform_name: e.target.value
                }))}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="API基础URL"
                value={platformFormData.api_base_url}
                onChange={(e) => setPlatformFormData(prev => ({
                  ...prev,
                  api_base_url: e.target.value
                }))}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={3}
                label="描述"
                value={platformFormData.description}
                onChange={(e) => setPlatformFormData(prev => ({
                  ...prev,
                  description: e.target.value
                }))}
              />
            </Grid>
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={platformFormData.is_active}
                    onChange={(e) => setPlatformFormData(prev => ({
                      ...prev,
                      is_active: e.target.checked
                    }))}
                  />
                }
                label="启用平台"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditConfigDialog(false)}>取消</Button>
          <Button onClick={handleUpdatePlatform} variant="contained">保存</Button>
        </DialogActions>
      </Dialog>

      {/* Credential Configuration Dialog */}
      <Dialog
        open={credentialDialog}
        onClose={() => setCredentialDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>配置平台凭证</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="API Key"
                type="password"
                value={credentialFormData.api_key}
                onChange={(e) => setCredentialFormData(prev => ({
                  ...prev,
                  api_key: e.target.value
                }))}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="API Secret"
                type="password"
                value={credentialFormData.api_secret}
                onChange={(e) => setCredentialFormData(prev => ({
                  ...prev,
                  api_secret: e.target.value
                }))}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Access Token (可选)"
                type="password"
                value={credentialFormData.access_token}
                onChange={(e) => setCredentialFormData(prev => ({
                  ...prev,
                  access_token: e.target.value
                }))}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Webhook URL (可选)"
                value={credentialFormData.webhook_url}
                onChange={(e) => setCredentialFormData(prev => ({
                  ...prev,
                  webhook_url: e.target.value
                }))}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCredentialDialog(false)}>取消</Button>
          <Button onClick={handleSaveCredential} variant="contained">保存凭证</Button>
        </DialogActions>
      </Dialog>

      {/* Test Connection Dialog */}
      <Dialog
        open={testConnectionDialog}
        onClose={() => setTestConnectionDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogContent>
          <Box sx={{ textAlign: 'center', py: 3 }}>
            <CircularProgress size={60} />
            <Typography variant="h6" sx={{ mt: 2 }}>
              正在测试连接...
            </Typography>
          </Box>
        </DialogContent>
      </Dialog>

      {/* Route Detail Dialog */}
      <Dialog
        open={routeDetailDialog}
        onClose={() => setRouteDetailDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>路由详情</DialogTitle>
        <DialogContent>
          {selectedRoute && (
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12} md={6}>
                <Typography variant="body2" color="text.secondary">路由ID</Typography>
                <Typography variant="body1" sx={{ mb: 2 }}>
                  {selectedRoute.id}
                </Typography>
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography variant="body2" color="text.secondary">平台</Typography>
                <Typography variant="body1" sx={{ mb: 2 }}>
                  {selectedRoute.platform_code}
                </Typography>
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography variant="body2" color="text.secondary">路由类型</Typography>
                <Chip label={selectedRoute.route_type} />
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography variant="body2" color="text.secondary">状态</Typography>
                <Chip
                  label={selectedRoute.route_status}
                  color={
                    selectedRoute.route_status === 'success' ? 'success' :
                    selectedRoute.route_status === 'failed' ? 'error' :
                    'default'
                  }
                />
              </Grid>
              <Grid item xs={12}>
                <Typography variant="body2" color="text.secondary">请求数据</Typography>
                <Paper variant="outlined" sx={{ p: 2, bgcolor: 'grey.50' }}>
                  <pre style={{ margin: 0, overflow: 'auto' }}>
                    {JSON.stringify(selectedRoute.request_data, null, 2)}
                  </pre>
                </Paper>
              </Grid>
              <Grid item xs={12}>
                <Typography variant="body2" color="text.secondary">响应数据</Typography>
                <Paper variant="outlined" sx={{ p: 2, bgcolor: 'grey.50' }}>
                  <pre style={{ margin: 0, overflow: 'auto' }}>
                    {JSON.stringify(selectedRoute.response_data, null, 2)}
                  </pre>
                </Paper>
              </Grid>
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setRouteDetailDialog(false)}>关闭</Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default PlatformIntegration;