import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Button,
  IconButton,
  TextField,
  InputAdornment,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Alert,
  Snackbar,
  Tooltip,
  Avatar,
  TablePagination,
} from '@mui/material';
import {
  Search,
  Edit,
  Delete,
  Visibility,
  CheckCircle,
  Block,
  Add,
  Settings,
  Warning,
  Link,
  Person,
} from '@mui/icons-material';
import apiService from '../../services/api';

function ServiceManagement() {
  const [loading, setLoading] = useState(true);
  const [services, setServices] = useState([]);
  const [categories, setCategories] = useState([]);
  const [mainCategories, setMainCategories] = useState([]);
  const [subCategories, setSubCategories] = useState([]);
  const [filteredServices, setFilteredServices] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [mainCategoryFilter, setMainCategoryFilter] = useState('all');
  const [subCategoryFilter, setSubCategoryFilter] = useState('all');

  // 分页相关状态
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  const [selectedService, setSelectedService] = useState(null);
  const [serviceDetailOpen, setServiceDetailOpen] = useState(false);
  const [serviceDialogOpen, setServiceDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);

  const [editService, setEditService] = useState(null);

  // 关联渠道商相关状态
  const [linkProviderDialogOpen, setLinkProviderDialogOpen] = useState(false);
  const [selectedServiceForLink, setSelectedServiceForLink] = useState(null);
  const [providers, setProviders] = useState([]);
  const [providerSearchTerm, setProviderSearchTerm] = useState('');
  const [filteredProviders, setFilteredProviders] = useState([]);
  const [selectedProvider, setSelectedProvider] = useState(null);
  const [linkingLoading, setLinkingLoading] = useState(false);

  // 标签分类枚举值（从API获取）
  const [channelTypeOptions, setChannelTypeOptions] = useState([
    '不限', '新闻资讯', '汽车网站', 'IT科技', '生活消费', '财经商业', '游戏电竞',
    '房产家居', '女性时尚', '娱乐休闲', '健康医疗', '教育培训', '频道类型',
    '酒店旅游', '音乐餐饮', '体育运动', '公益三农', '文化艺术', '亲子包邮',
    '优惠套餐', '能源环保'
  ]);

  // 严格按照后端 ChannelServiceCreate schema 定义状态
  const [newService, setNewService] = useState({
    // 必填字段
    service_name: '',
    service_code: '',      // 实体账户ID
    service_description: '',
    category_id: '',
    base_price: '',
    channel_type: '',      // 标签分类

    // 前端辅助字段
    main_category_id: '',  // 用于层级选择的主分类ID

    // 可选字段 - 核心必要字段
    price_unit: '次',      // str (默认"次")
    is_active: true,       // bool (默认True)
  });
  
  const [alert, setAlert] = useState({ open: false, message: '', severity: 'success' });



  // 获取频道类型枚举值
  const fetchChannelTypes = async () => {
    try {
      const response = await fetch('http://localhost:8000/api/v1/public/channel-types');
      const result = await response.json();
      if (result.code === 200) {
        setChannelTypeOptions(result.data);
      }
    } catch (error) {
      console.error('获取频道类型失败:', error);
      // 使用默认值，不显示错误
    }
  };

  // 加载数据
  useEffect(() => {
    loadServices();
    loadCategories();
    fetchChannelTypes();
  }, []);

  // 自动关闭提示
  useEffect(() => {
    if (alert.open) {
      const timer = setTimeout(() => {
        setAlert(prev => ({ ...prev, open: false }));
      }, 4000);
      return () => clearTimeout(timer);
    }
  }, [alert.open]);

  const loadServices = async () => {
    try {
      setLoading(true);

      // 使用真实的API调用
      const response = await apiService.get('/channel-services');

      if (response && response.success) {
        // 后端返回的数据结构是 { items: [...], pagination: {...} }
        const servicesData = Array.isArray(response.data?.items) ? response.data.items : [];

        // 直接使用后端返回的真实数据，包含 bound_providers 信息
        setServices(servicesData);
        setFilteredServices(servicesData);

        if (servicesData.length === 0) {
          setAlert({
            open: true,
            message: '暂无服务数据，请先创建一些服务',
            severity: 'info'
          });
        }
      } else {
        setAlert({
          open: true,
          message: '获取服务列表失败，请重试',
          severity: 'error'
        });
      }
    } catch (error) {
      console.error('加载服务失败:', error);
      setAlert({
        open: true,
        message: '加载服务失败，请重试',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  const loadCategories = async () => {
    try {
      const response = await apiService.getChannelCategories();
      if (response.success) {
        // 根据API响应结构，数据在 response.data.categories 中
        const categoriesData = Array.isArray(response.data.categories) ? response.data.categories : [];
        setCategories(categoriesData);

        // 分离主分类和子分类
        const mainCats = categoriesData.filter(cat => cat.category_level === 1);
        const subCats = categoriesData.filter(cat => cat.category_level === 2);

        setMainCategories(mainCats);
        setSubCategories(subCats);
      } else {
        setCategories([]);
        setMainCategories([]);
        setSubCategories([]);
      }
    } catch (error) {
      setCategories([]);
      setMainCategories([]);
      setSubCategories([]);
    }
  };

  // 关闭对话框并重置表单 - 严格按照后端schema
  const handleCloseDialog = () => {
    setServiceDialogOpen(false);
    setNewService({
      // 必填字段
      service_name: '',
      service_code: '',      // 实体账户ID
      service_description: '',
      category_id: '',
      base_price: '',
      channel_type: '',      // 标签分类

      // 前端辅助字段
      main_category_id: '',  // 用于层级选择的主分类ID

      // 可选字段 - 核心必要字段
      price_unit: '次',      // str (默认"次")
      is_active: true,       // bool (默认True)
    });
  };

  // 创建服务
  const handleCreateService = async () => {
    try {
      // 验证必填字段
      if (!newService.service_name.trim()) {
        setAlert({
          open: true,
          message: '请输入服务名称',
          severity: 'error'
        });
        return;
      }

      if (!newService.service_code.trim()) {
        setAlert({
          open: true,
          message: '请输入实体账户ID',
          severity: 'error'
        });
        return;
      }

      // portal_type 在后端 schema 中是可选字段，所以移除必填验证

      if (!newService.category_id || !newService.category_id.trim()) {
        setAlert({
          open: true,
          message: '请选择服务分类',
          severity: 'error'
        });
        return;
      }

      // 严格按照后端 ChannelServiceCreate schema 构造数据
      const apiData = {
        // 必填字段
        service_name: newService.service_name.trim(),
        service_code: newService.service_code.trim(),
        service_description: newService.service_description.trim(),
        category_id: newService.category_id.trim(),
        base_price: parseFloat(newService.base_price) || 0,

        // 可选字段 - 严格匹配后端类型
        price_unit: newService.price_unit || '次',
        channel_type: newService.channel_type || null,
        portal_type: newService.portal_type || null,
        platform_specs: newService.platform_specs || null,
        coverage_area: Array.isArray(newService.coverage_area) ? newService.coverage_area : [],
        is_active: Boolean(newService.is_active)
      };

      const response = await apiService.post('/channel-services', apiData);
      if (response && response.success) {
        await loadServices();
        handleCloseDialog();
        setAlert({
          open: true,
          message: `渠道服务"${newService.service_name}"创建成功！`,
          severity: 'success'
        });
      } else {
        setAlert({
          open: true,
          message: response?.message || '创建失败，请重试',
          severity: 'error'
        });
      }
    } catch (error) {
      let errorMessage = '创建失败，请检查网络连接或联系管理员';

      if (error.response?.data) {
        const errorData = error.response.data;

        if (errorData.detail) {
          if (Array.isArray(errorData.detail)) {
            // Pydantic validation errors
            const validationErrors = errorData.detail.map(err =>
              `${err.loc?.join('.')} - ${err.msg}`
            ).join('; ');
            errorMessage = `数据验证失败: ${validationErrors}`;
          } else if (typeof errorData.detail === 'string') {
            errorMessage = errorData.detail;
          }
        } else if (errorData.message) {
          errorMessage = errorData.message;
        }
      }

      setAlert({
        open: true,
        message: errorMessage,
        severity: 'error'
      });
    }
  };

  // 编辑服务
  const handleEditService = async () => {
    try {
      // 严格按照后端 ChannelServiceUpdate schema 构造数据
      const apiData = {
        // 所有字段都是可选的，只发送有值的字段
        service_name: editService.service_name?.trim() || null,
        service_code: editService.service_code?.trim() || null,
        service_description: editService.service_description?.trim() || null,
        category_id: editService.category_id || null,
        base_price: editService.base_price ? parseFloat(editService.base_price) : null,
        price_unit: editService.price_unit || null,
        channel_type: editService.channel_type || null,
        service_features: Array.isArray(editService.service_features) ? editService.service_features : null,
        is_active: editService.is_active !== undefined ? Boolean(editService.is_active) : null
      };

      const response = await apiService.put(`/channel-services/${editService.id}`, apiData);
      if (response && response.success) {
        await loadServices();
        handleCloseEditDialog();
        setAlert({
          open: true,
          message: `渠道服务"${editService.service_name}"更新成功！`,
          severity: 'success'
        });
      } else {
        setAlert({
          open: true,
          message: response?.message || '更新失败，请重试',
          severity: 'error'
        });
      }
    } catch (error) {
      setAlert({
        open: true,
        message: '更新失败，请检查网络连接或联系管理员',
        severity: 'error'
      });
    }
  };

  // 关闭编辑对话框
  const handleCloseEditDialog = () => {
    setEditDialogOpen(false);
    setEditService(null);
  };

  // 搜索和过滤
  useEffect(() => {
    let filtered = services || [];

    // 搜索过滤
    if (searchTerm && filtered.length > 0) {
      filtered = filtered.filter(service =>
        service.service_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        service.service_code.toLowerCase().includes(searchTerm.toLowerCase()) ||
        service.service_description.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // 状态过滤
    if (statusFilter !== 'all' && filtered.length > 0) {
      if (statusFilter === 'active') {
        filtered = filtered.filter(service => service.is_active === true);
      } else if (statusFilter === 'inactive') {
        filtered = filtered.filter(service => service.is_active === false);
      }
    }

    // 层级分类过滤
    if (subCategoryFilter !== 'all' && filtered.length > 0) {
      // 如果选择了子分类，直接按子分类筛选
      filtered = filtered.filter(service => service.category_id === subCategoryFilter);
    } else if (mainCategoryFilter !== 'all' && filtered.length > 0) {
      // 如果只选择了主分类，筛选该主分类下的所有子分类的服务
      const mainCategorySubCategories = subCategories.filter(subCat => subCat.parent_id === mainCategoryFilter);
      const subCategoryIds = mainCategorySubCategories.map(subCat => subCat.id);
      filtered = filtered.filter(service => subCategoryIds.includes(service.category_id));
    }

    setFilteredServices(Array.isArray(filtered) ? filtered : []);
  }, [services, searchTerm, statusFilter, mainCategoryFilter, subCategoryFilter, subCategories]);

  // 处理主分类变化
  const handleMainCategoryChange = (value) => {
    setMainCategoryFilter(value);
    setSubCategoryFilter('all'); // 重置子分类选择
  };

  // 获取当前主分类下的子分类
  const getSubCategoriesForMainCategory = () => {
    if (mainCategoryFilter === 'all') {
      return subCategories;
    }
    return subCategories.filter(subCat => subCat.parent_id === mainCategoryFilter);
  };

  // 获取渠道商列表
  const loadProviders = async () => {
    try {
      const response = await apiService.get('/channels', {
        params: {
          page: 1,
          size: 100,  // 获取更多数据
          verification_status: 'approved'  // 只获取已审核通过的渠道商
        }
      });

      console.log('渠道商API响应:', response);

      if (response && response.success && response.data) {
        // 处理不同的数据结构
        let providersData = [];

        if (Array.isArray(response.data.items)) {
          providersData = response.data.items;
        } else if (Array.isArray(response.data.data)) {
          providersData = response.data.data;
        } else if (Array.isArray(response.data)) {
          providersData = response.data;
        }

        console.log('解析的渠道商数据:', providersData);
        setProviders(providersData);
        setFilteredProviders(providersData);
      } else {
        // 使用模拟数据
        const mockProviders = [
          {
            id: 'mock-provider-1',
            provider_name: '科技媒体工作室',
            provider_type: 'studio',
            real_name: '张三',
            company_name: '科技媒体工作室',
            contact_email: '<EMAIL>',
            contact_phone: '13800138001',
            verification_status: 'approved',
            created_at: new Date().toISOString()
          },
          {
            id: 'mock-provider-2',
            provider_name: '个人自媒体博主',
            provider_type: 'individual',
            real_name: '李四',
            contact_email: '<EMAIL>',
            contact_phone: '13800138002',
            verification_status: 'approved',
            created_at: new Date().toISOString()
          },
          {
            id: 'mock-provider-3',
            provider_name: '新媒体传播公司',
            provider_type: 'company',
            company_name: '新媒体传播有限公司',
            contact_email: '<EMAIL>',
            contact_phone: '13800138003',
            verification_status: 'approved',
            created_at: new Date().toISOString()
          }
        ];
        setProviders(mockProviders);
        setFilteredProviders(mockProviders);
      }
    } catch (error) {
      console.error('获取渠道商列表失败:', error);
      // 使用模拟数据
      const mockProviders = [
        {
          id: 'mock-provider-1',
          provider_name: '科技媒体工作室',
          provider_type: 'studio',
          real_name: '张三',
          company_name: '科技媒体工作室',
          contact_email: '<EMAIL>',
          contact_phone: '13800138001',
          verification_status: 'approved',
          created_at: new Date().toISOString()
        },
        {
          id: 'mock-provider-2',
          provider_name: '个人自媒体博主',
          provider_type: 'individual',
          real_name: '李四',
          contact_email: '<EMAIL>',
          contact_phone: '13800138002',
          verification_status: 'approved',
          created_at: new Date().toISOString()
        }
      ];
      setProviders(mockProviders);
      setFilteredProviders(mockProviders);
    }
  };

  // 搜索渠道商
  useEffect(() => {
    if (!providerSearchTerm.trim()) {
      setFilteredProviders(providers);
    } else {
      const filtered = providers.filter(provider =>
        provider.provider_name?.toLowerCase().includes(providerSearchTerm.toLowerCase()) ||
        provider.contact_email?.toLowerCase().includes(providerSearchTerm.toLowerCase()) ||
        provider.real_name?.toLowerCase().includes(providerSearchTerm.toLowerCase()) ||
        provider.company_name?.toLowerCase().includes(providerSearchTerm.toLowerCase())
      );
      setFilteredProviders(filtered);
    }
  }, [providers, providerSearchTerm]);

  // 打开关联渠道商对话框
  const handleOpenLinkProviderDialog = (service) => {
    setSelectedServiceForLink(service);
    setLinkProviderDialogOpen(true);
    setSelectedProvider(null);
    setProviderSearchTerm('');
    loadProviders();
  };

  // 关闭关联渠道商对话框
  const handleCloseLinkProviderDialog = () => {
    setLinkProviderDialogOpen(false);
    setSelectedServiceForLink(null);
    setSelectedProvider(null);
    setProviderSearchTerm('');
    setFilteredProviders([]);
  };

  // 关联渠道商到服务
  const handleLinkProvider = async () => {
    if (!selectedProvider || !selectedServiceForLink) {
      setAlert({
        open: true,
        message: '请选择要关联的渠道商',
        severity: 'error'
      });
      return;
    }

    try {
      setLinkingLoading(true);
      const response = await apiService.post(`/channel-category-mappings/admin/link?service_id=${selectedServiceForLink.id}&provider_id=${selectedProvider.id}`);

      if (response && response.success) {
        setAlert({
          open: true,
          message: `成功将渠道商"${selectedProvider.provider_name}"关联到服务"${selectedServiceForLink.service_name}"`,
          severity: 'success'
        });
        handleCloseLinkProviderDialog();
        await loadServices(); // 重新加载服务列表
      } else {
        setAlert({
          open: true,
          message: response?.message || '关联失败，请重试',
          severity: 'error'
        });
      }
    } catch (error) {
      console.error('关联渠道商失败:', error);
      let errorMessage = '关联失败，请检查网络连接或联系管理员';

      // 处理特定的错误信息
      if (error.response?.data?.detail) {
        errorMessage = error.response.data.detail;
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      setAlert({
        open: true,
        message: errorMessage,
        severity: 'error'
      });
    } finally {
      setLinkingLoading(false);
    }
  };



  // 获取服务详情
  const getServiceDetail = async (serviceId) => {
    try {
      const response = await apiService.get(`/channel-services/${serviceId}`);
      if (response && response.success) {
        return response.data;
      } else {
        throw new Error(response?.message || '获取服务详情失败');
      }
    } catch (error) {
      setAlert({
        open: true,
        message: '获取服务详情失败，请重试',
        severity: 'error'
      });
      return null;
    }
  };

  const handleServiceAction = async (action, service = null) => {
    const targetService = service;
    if (!targetService) return;

    try {
      switch (action) {
        case 'view':
          // 查看详情逻辑 - 获取完整的服务详情
          const serviceDetail = await getServiceDetail(targetService.id);
          if (serviceDetail) {
            setSelectedService(serviceDetail);
            setServiceDetailOpen(true);
          }
          break;
        case 'edit':
          // 编辑逻辑 - 获取最新的服务详情数据
          const editServiceDetail = await getServiceDetail(targetService.id);
          if (editServiceDetail) {
            const platformSpecs = editServiceDetail.platform_specs || {};
            setEditService({
              id: editServiceDetail.id,
              service_name: editServiceDetail.service_name,
              service_code: editServiceDetail.service_code,
              service_description: editServiceDetail.service_description,
              service_features: editServiceDetail.service_features || [],  // 服务特色/亮点
              category_id: editServiceDetail.category_id || '',
              base_price: editServiceDetail.base_price ? editServiceDetail.base_price.toString() : '',
              price_unit: editServiceDetail.price_unit || '',
              channel_type: editServiceDetail.channel_type || '',  // 标签分类
              is_active: editServiceDetail.is_active,
            });
            setEditDialogOpen(true);
          }
          break;
        case 'delete':
          if (window.confirm(`确定要删除渠道服务"${targetService.service_name}"吗？此操作不可撤销。`)) {
            try {
              const response = await apiService.delete(`/channel-services/${targetService.id}`);
              if (response && response.success) {
                setAlert({
                  open: true,
                  message: `渠道服务"${targetService.service_name}"删除成功！`,
                  severity: 'success'
                });
                await loadServices();
              } else {
                setAlert({
                  open: true,
                  message: response?.message || '删除失败，请重试',
                  severity: 'error'
                });
              }
            } catch (error) {
              setAlert({
                open: true,
                message: '删除失败，请检查网络连接或联系管理员',
                severity: 'error'
              });
            }
          }
          break;
        case 'toggle_status':
          try {
            const newStatus = !targetService.is_active;
            const response = await apiService.put(`/channel-services/${targetService.id}`, { is_active: newStatus });
            if (response && response.success) {
              setAlert({
                open: true,
                message: `渠道服务"${targetService.service_name}"已${newStatus ? '启用' : '禁用'}！`,
                severity: 'success'
              });
              await loadServices();
            } else {
              setAlert({
                open: true,
                message: response?.message || '操作失败，请重试',
                severity: 'error'
              });
            }
          } catch (error) {
            setAlert({
              open: true,
              message: '操作失败，请检查网络连接或联系管理员',
              severity: 'error'
            });
          }
          break;
        default:
          break;
      }
    } catch (error) {
      setAlert({
        open: true,
        message: '操作失败，请检查网络连接或联系管理员',
        severity: 'error'
      });
    }
  };

  const getCategoryName = (service) => {
    // 优先使用服务对象中的分类信息
    if (service.category && service.category.category_name) {
      return service.category.category_name;
    }

    // 如果没有分类信息，尝试从categories数组中查找
    if (service.category_id) {
      const category = categories.find(cat => cat.id === service.category_id);
      if (category) {
        return category.category_name;
      }
    }

    return '未分类';
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ bgcolor: '#ffffff', minHeight: '100vh' }}>
      <Container maxWidth="xl" sx={{ py: 4 }}>
      {/* 页面标题 */}
      <Card sx={{
        mb: 4,
        borderRadius: 3,
        border: '1px solid #e0e0e0',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
      }}>
        <CardContent sx={{ p: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
            <Box>
              <Typography variant="h4" sx={{
                fontWeight: 700,
                color: '#1976d2',
                mb: 1,
                display: 'flex',
                alignItems: 'center',
                gap: 2
              }}>
                <Settings sx={{ fontSize: 32 }} />
                渠道实体管理
              </Typography>
            </Box>
            <Button
              variant="contained"
              startIcon={<Add />}
              onClick={() => setServiceDialogOpen(true)}
              sx={{
                borderRadius: 2,
                fontWeight: 600,
                px: 3,
                py: 1.5,
                background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',
                boxShadow: '0 4px 12px rgba(25, 118, 210, 0.3)',
                '&:hover': {
                  background: 'linear-gradient(135deg, #1565c0 0%, #0d47a1 100%)',
                  boxShadow: '0 6px 16px rgba(25, 118, 210, 0.4)',
                }
              }}
            >
              添加服务
            </Button>
          </Box>
        </CardContent>
      </Card>

      {/* 搜索过滤和服务列表 */}
      <Card sx={{ borderRadius: 3, boxShadow: '0 2px 8px rgba(0,0,0,0.1)' }}>
        <CardContent>
          {/* 搜索和过滤区域 */}
          <Grid container spacing={3} alignItems="center" sx={{ mb: 3 }}>
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                placeholder="搜索服务信息..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Search />
                    </InputAdornment>
                  ),
                }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                    minHeight: 56,
                    backgroundColor: '#ffffff',
                  },
                  '& .MuiInputBase-input': {
                    fontSize: '0.95rem',
                  },
                  '& .MuiInputBase-input::placeholder': {
                    fontSize: '0.9rem',
                    opacity: 0.7,
                  }
                }}
              />
            </Grid>
            <Grid item xs={12} md={2}>
              <FormControl fullWidth>
                <InputLabel>状态筛选</InputLabel>
                <Select
                  value={statusFilter}
                  label="状态筛选"
                  onChange={(e) => setStatusFilter(e.target.value)}
                  sx={{
                    borderRadius: 2,
                    backgroundColor: '#ffffff',
                  }}
                >
                  <MenuItem value="all">全部状态</MenuItem>
                  <MenuItem value="active">启用</MenuItem>
                  <MenuItem value="inactive">禁用</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>主分类筛选</InputLabel>
                <Select
                  value={mainCategoryFilter}
                  label="主分类筛选"
                  onChange={(e) => handleMainCategoryChange(e.target.value)}
                  sx={{
                    borderRadius: 2,
                    backgroundColor: '#ffffff',
                  }}
                >
                  <MenuItem value="all">全部主分类</MenuItem>
                  {Array.isArray(mainCategories) ? mainCategories
                    .map((category) => (
                      <MenuItem key={category.id} value={category.id}>
                        {category.category_name}
                      </MenuItem>
                    )) : null}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>子分类筛选</InputLabel>
                <Select
                  value={subCategoryFilter}
                  label="子分类筛选"
                  onChange={(e) => setSubCategoryFilter(e.target.value)}
                  disabled={mainCategoryFilter === 'all'}
                  sx={{
                    borderRadius: 2,
                    backgroundColor: '#ffffff',
                  }}
                >
                  <MenuItem value="all">全部子分类</MenuItem>
                  {getSubCategoriesForMainCategory().map((category) => (
                    <MenuItem key={category.id} value={category.id}>
                      {category.category_name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
          </Grid>

          {/* 服务列表 */}
          <Box sx={{ border: '1px solid #e0e0e0', borderRadius: 2 }}>
            <Box sx={{
              p: 3,
              borderBottom: '1px solid #e0e0e0',
              background: 'linear-gradient(135deg, #f5f5f5 0%, #e0e0e0 100%)',
              borderRadius: '8px 8px 0 0'
            }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Settings sx={{ color: '#333333', fontSize: 24 }} />
                <Typography variant="h6" sx={{ fontWeight: 600, color: '#333333' }}>
                  服务列表 ({Array.isArray(filteredServices) ? filteredServices.length : 0})
                </Typography>
              </Box>
            </Box>
            <TableContainer component={Paper} sx={{ boxShadow: 'none' }}>
              <Table sx={{ minWidth: 650 }}>
                <TableHead>
                  <TableRow sx={{ backgroundColor: '#f8fafc' }}>
                    <TableCell sx={{ fontWeight: 600, color: '#374151', py: 2 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Settings sx={{ fontSize: 18 }} />
                        服务名称
                      </Box>
                    </TableCell>
                    <TableCell sx={{ fontWeight: 600, color: '#374151', py: 2 }}>描述</TableCell>
                    <TableCell sx={{ fontWeight: 600, color: '#374151', py: 2 }}>分类</TableCell>
                    <TableCell sx={{ fontWeight: 600, color: '#374151', py: 2 }}>价格</TableCell>
                    <TableCell sx={{ fontWeight: 600, color: '#374151', py: 2 }}>时长</TableCell>
                    <TableCell sx={{ fontWeight: 600, color: '#374151', py: 2 }}>绑定渠道商</TableCell>
                    <TableCell sx={{ fontWeight: 600, color: '#374151', py: 2 }}>状态</TableCell>
                    <TableCell sx={{ fontWeight: 600, color: '#374151', py: 2, textAlign: 'center' }}>操作</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {filteredServices
                    .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                    .map((service) => (
                    <TableRow
                      key={service.id}
                      sx={{
                        '&:hover': {
                          backgroundColor: '#f8fafc',
                          '& .action-buttons': {
                            opacity: 1
                          }
                        },
                        transition: 'all 0.2s ease'
                      }}
                    >
                      <TableCell sx={{ py: 3 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                          <Avatar sx={{
                            bgcolor: '#667eea',
                            width: 40,
                            height: 40,
                            boxShadow: '0 2px 8px rgba(102, 126, 234, 0.3)'
                          }}>
                            <Settings />
                          </Avatar>
                          <Box>
                            <Typography variant="subtitle1" sx={{ fontWeight: 600, color: '#1e293b' }}>
                              {service.service_name}
                            </Typography>
                          </Box>
                        </Box>
                      </TableCell>
                      <TableCell sx={{ py: 3, maxWidth: 250 }}>
                        <Typography
                          variant="body2"
                          color="text.secondary"
                          sx={{
                            display: '-webkit-box',
                            WebkitLineClamp: 2,
                            WebkitBoxOrient: 'vertical',
                            overflow: 'hidden',
                            lineHeight: 1.4
                          }}
                        >
                          {service.service_description || '暂无描述'}
                        </Typography>
                      </TableCell>
                    <TableCell sx={{ py: 3 }}>
                      <Chip
                        label={getCategoryName(service)}
                        variant="outlined"
                        size="small"
                        sx={{
                          backgroundColor: '#f1f5f9',
                          borderColor: '#cbd5e1',
                          fontWeight: 500
                        }}
                      />
                    </TableCell>
                    <TableCell sx={{ py: 3 }}>
                      <Typography variant="body2" sx={{ fontWeight: 600, color: '#059669' }}>
                        ¥{service.base_price}
                        {service.price_unit && (
                          <Typography component="span" variant="caption" color="text.secondary" sx={{ ml: 0.5 }}>
                            /{service.price_unit}
                          </Typography>
                        )}
                      </Typography>
                    </TableCell>
                    <TableCell sx={{ py: 3 }}>
                      {service.delivery_time ? (
                        <Typography variant="body2" sx={{ fontWeight: 600, color: '#374151' }}>
                          {service.delivery_time}小时
                        </Typography>
                      ) : (
                        <Typography variant="body2" color="text.secondary">
                          不限
                        </Typography>
                      )}
                    </TableCell>
                    <TableCell sx={{ py: 3 }}>
                      {service.bound_providers && service.bound_providers.length > 0 ? (
                        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
                          {service.bound_providers.slice(0, 2).map((provider, index) => (
                            <Chip
                              key={provider.id}
                              label={provider.provider_name}
                              variant="outlined"
                              size="small"
                              icon={<Person />}
                              sx={{
                                backgroundColor: '#e8f5e8',
                                borderColor: '#4caf50',
                                color: '#2e7d32',
                                fontWeight: 500,
                                fontSize: '0.75rem'
                              }}
                            />
                          ))}
                          {service.bound_providers.length > 2 && (
                            <Typography variant="caption" color="text.secondary">
                              +{service.bound_providers.length - 2} 更多
                            </Typography>
                          )}
                        </Box>
                      ) : (
                        <Chip
                          label="未绑定"
                          variant="outlined"
                          size="small"
                          sx={{
                            backgroundColor: '#f5f5f5',
                            borderColor: '#d0d0d0',
                            color: '#666',
                            fontWeight: 500,
                            fontSize: '0.75rem'
                          }}
                        />
                      )}
                    </TableCell>
                    <TableCell sx={{ py: 3 }}>
                      <Chip
                        label={service.is_active ? '启用' : '禁用'}
                        color={service.is_active ? 'success' : 'error'}
                        size="small"
                        icon={service.is_active ? <CheckCircle /> : <Warning />}
                        sx={{ fontWeight: 500 }}
                      />
                    </TableCell>

                    <TableCell sx={{ py: 3, textAlign: 'center' }}>
                      <Box
                        className="action-buttons"
                        sx={{
                          display: 'flex',
                          justifyContent: 'center',
                          gap: 1,
                          opacity: 0.7,
                          transition: 'opacity 0.2s ease'
                        }}
                      >
                        <Tooltip title="查看详情">
                          <IconButton
                            size="small"
                            onClick={() => {
                              setSelectedService(service);
                              setServiceDetailOpen(true);
                            }}
                            sx={{
                              bgcolor: '#3b82f6',
                              color: 'white',
                              '&:hover': { bgcolor: '#2563eb' },
                              width: 32,
                              height: 32
                            }}
                          >
                            <Visibility fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="编辑服务">
                          <IconButton
                            size="small"
                            onClick={() => handleServiceAction('edit', service)}
                            sx={{
                              bgcolor: '#ff9800',
                              color: 'white',
                              '&:hover': { bgcolor: '#f57c00' },
                              width: 32,
                              height: 32
                            }}
                          >
                            <Edit fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title={service.is_active ? '禁用服务' : '启用服务'}>
                          <IconButton
                            size="small"
                            onClick={() => handleServiceAction('toggle_status', service)}
                            sx={{
                              bgcolor: service.is_active ? '#f59e0b' : '#10b981',
                              color: 'white',
                              '&:hover': {
                                bgcolor: service.is_active ? '#d97706' : '#059669'
                              },
                              width: 32,
                              height: 32
                            }}
                          >
                            {service.is_active ? <Block fontSize="small" /> : <CheckCircle fontSize="small" />}
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="关联渠道商">
                          <IconButton
                            size="small"
                            onClick={() => handleOpenLinkProviderDialog(service)}
                            sx={{
                              bgcolor: '#6b7280',
                              color: 'white',
                              '&:hover': { bgcolor: '#4b5563' },
                              width: 32,
                              height: 32
                            }}
                          >
                            <Link fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="删除服务">
                          <IconButton
                            size="small"
                            onClick={() => handleServiceAction('delete', service)}
                            sx={{
                              bgcolor: '#ef4444',
                              color: 'white',
                              '&:hover': { bgcolor: '#dc2626' },
                              width: 32,
                              height: 32
                            }}
                          >
                            <Delete fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
                {filteredServices.length === 0 && (
                  <TableRow>
                    <TableCell colSpan={8} align="center" sx={{ py: 4 }}>
                      <Typography variant="body2" color="text.secondary">
                        暂无服务数据
                      </Typography>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>

          {/* 自定义分页组件 */}
          <Box sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            p: 2,
            borderTop: '1px solid #e0e0e0',
            bgcolor: 'white'
          }}>
            <Typography variant="body2" color="text.secondary">
              显示 {Math.min(page * rowsPerPage + 1, filteredServices.length)} - {Math.min((page + 1) * rowsPerPage, filteredServices.length)} 条，共 {filteredServices.length} 条
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Button
                size="small"
                disabled={page === 0}
                onClick={() => setPage(page - 1)}
                sx={{ minWidth: 'auto', px: 1 }}
              >
                上一页
              </Button>
              {Array.from({ length: Math.ceil(filteredServices.length / rowsPerPage) }, (_, index) => (
                <Button
                  key={index}
                  size="small"
                  variant={page === index ? 'contained' : 'outlined'}
                  onClick={() => setPage(index)}
                  sx={{
                    minWidth: 32,
                    height: 32,
                    bgcolor: page === index ? '#6b7280' : 'transparent',
                    '&:hover': {
                      bgcolor: page === index ? '#4b5563' : '#f5f5f5'
                    }
                  }}
                >
                  {index + 1}
                </Button>
              ))}
              <Button
                size="small"
                disabled={page >= Math.ceil(filteredServices.length / rowsPerPage) - 1}
                onClick={() => setPage(page + 1)}
                sx={{ minWidth: 'auto', px: 1 }}
              >
                下一页
              </Button>
              <FormControl size="small" sx={{ ml: 2, minWidth: 80 }}>
                <Select
                  value={rowsPerPage}
                  onChange={(e) => {
                    setRowsPerPage(parseInt(e.target.value, 10));
                    setPage(0);
                  }}
                  displayEmpty
                >
                  <MenuItem value={5}>5条/页</MenuItem>
                  <MenuItem value={10}>10条/页</MenuItem>
                  <MenuItem value={25}>25条/页</MenuItem>
                  <MenuItem value={50}>50条/页</MenuItem>
                </Select>
              </FormControl>
            </Box>
          </Box>
          </Box>
        </CardContent>
      </Card>



      {/* 添加服务对话框 */}
      <Dialog
        open={serviceDialogOpen}
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
        scroll="paper"
        PaperProps={{
          sx: {
            borderRadius: 3,
            maxHeight: '95vh',
            margin: 1,
          }
        }}
      >
        <DialogTitle sx={{
          pb: 2,
          borderBottom: '1px solid #e0e0e0',
          display: 'flex',
          alignItems: 'center',
          gap: 2
        }}>
          <Box sx={{
            width: 40,
            height: 40,
            borderRadius: 2,
            bgcolor: '#1976d2',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            <Add sx={{ color: 'white' }} />
          </Box>
          <Box>
            <Typography variant="h6" sx={{ fontWeight: 600 }}>
              添加服务
            </Typography>
            <Typography variant="body2" color="text.secondary">
              创建新的服务项目
            </Typography>
          </Box>
        </DialogTitle>
        <DialogContent sx={{ pt: 3, pb: 2 }}>
          <Box>
            {/* 基本信息 */}
            <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2, color: '#1976d2' }}>
              基本信息
            </Typography>
            <Grid container spacing={2} sx={{ mb: 2 }}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="服务名称"
                  value={newService.service_name}
                  onChange={(e) => setNewService({ ...newService, service_name: e.target.value })}
                  required
                  variant="outlined"
                  placeholder="请输入服务的名称"
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 2,
                      '&:hover fieldset': {
                        borderColor: '#1976d2',
                      }
                    }
                  }}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="实体账户ID"
                  value={newService.service_code}
                  onChange={(e) => setNewService({ ...newService, service_code: e.target.value })}
                  required
                  variant="outlined"
                  placeholder="请输入实体账户ID"
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 2,
                      '&:hover fieldset': {
                        borderColor: '#1976d2',
                      }
                    }
                  }}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth variant="outlined" required>
                  <InputLabel shrink>主分类</InputLabel>
                  <Select
                    value={newService.main_category_id || ''}
                    label="主分类"
                    onChange={(e) => {
                      const mainCategoryId = e.target.value;
                      setNewService({
                        ...newService,
                        main_category_id: mainCategoryId,
                        category_id: '' // 重置子分类选择
                      });
                    }}
                    displayEmpty
                    notched
                    sx={{
                      borderRadius: 2,
                      minHeight: 56,
                      minWidth: 200,
                      backgroundColor: '#ffffff',
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#1976d2',
                      }
                    }}
                  >
                    <MenuItem value="">请选择主分类</MenuItem>
                    {Array.isArray(mainCategories) ? mainCategories
                      .filter(category => category.is_active)
                      .map((category) => (
                        <MenuItem key={category.id} value={category.id}>
                          {category.category_name}
                        </MenuItem>
                      )) : null}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth variant="outlined" required>
                  <InputLabel shrink>子分类</InputLabel>
                  <Select
                    value={newService.category_id}
                    label="子分类"
                    onChange={(e) => setNewService({ ...newService, category_id: e.target.value })}
                    disabled={!newService.main_category_id}
                    displayEmpty
                    notched
                    sx={{
                      borderRadius: 2,
                      minHeight: 56,
                      minWidth: 200,
                      backgroundColor: '#ffffff',
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#1976d2',
                      }
                    }}
                  >
                    <MenuItem value="">请选择子分类</MenuItem>
                    {Array.isArray(subCategories) ? subCategories
                      .filter(category =>
                        category.is_active &&
                        category.parent_id === newService.main_category_id
                      )
                      .map((category) => (
                        <MenuItem key={category.id} value={category.id}>
                          {category.category_name}
                        </MenuItem>
                      )) : null}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth variant="outlined" required>
                  <InputLabel shrink>标签分类</InputLabel>
                  <Select
                    value={newService.channel_type}
                    label="标签分类"
                    onChange={(e) => setNewService({ ...newService, channel_type: e.target.value })}
                    displayEmpty
                    notched
                    sx={{
                      borderRadius: 2,
                      minHeight: 56,
                      minWidth: 200,
                      backgroundColor: '#ffffff',
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#1976d2',
                      }
                    }}
                  >
                    <MenuItem value="">请选择标签分类</MenuItem>
                    {channelTypeOptions.map((option) => (
                      <MenuItem key={option} value={option}>
                        {option}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
            </Grid>

            {/* 服务描述独占一行 */}
            <Box sx={{ mb: 3 }}>
              <TextField
                fullWidth
                label="服务描述"
                multiline
                rows={4}
                value={newService.service_description}
                onChange={(e) => setNewService({ ...newService, service_description: e.target.value })}
                variant="outlined"
                placeholder="请输入服务的详细描述"
                sx={{
                  width: '100%',
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                    '&:hover fieldset': {
                      borderColor: '#1976d2',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: '#1976d2',
                    }
                  },
                  '& .MuiInputLabel-root.Mui-focused': {
                    color: '#1976d2',
                  }
                }}
              />
            </Box>

            {/* 价格信息 */}
            <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2, color: '#1976d2' }}>
              价格信息
            </Typography>
            <Grid container spacing={2} sx={{ mb: 3 }}>
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  label="基础价格"
                  type="number"
                  value={newService.base_price}
                  onChange={(e) => setNewService({ ...newService, base_price: e.target.value })}
                  inputProps={{ min: 0, step: 0.01 }}
                  variant="outlined"
                  placeholder="请输入服务的基础价格"
                  InputProps={{
                    startAdornment: <Typography variant="body2" color="text.secondary">¥</Typography>
                  }}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 2,
                      '&:hover fieldset': {
                        borderColor: '#1976d2',
                      }
                    }
                  }}
                />
              </Grid>

              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  label="计费单位"
                  value={newService.price_unit}
                  onChange={(e) => setNewService({ ...newService, price_unit: e.target.value })}
                  variant="outlined"
                  placeholder="如：次、小时、天"
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 2,
                      '&:hover fieldset': {
                        borderColor: '#1976d2',
                      }
                    }
                  }}
                />
              </Grid>
            </Grid>













            {/* 状态设置 */}
            <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2, color: '#1976d2' }}>
              状态设置
            </Typography>
            <Grid container spacing={2} sx={{ mb: 4 }}>
              <Grid item xs={6}>
                <FormControl fullWidth variant="outlined">
                  <InputLabel>状态</InputLabel>
                  <Select
                    value={newService.is_active}
                    label="状态"
                    onChange={(e) => setNewService({ ...newService, is_active: e.target.value })}
                    displayEmpty
                    MenuProps={{
                      PaperProps: {
                        sx: {
                          maxHeight: 150,
                          borderRadius: 2,
                          boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
                          mt: 0.5,
                        }
                      },
                      anchorOrigin: {
                        vertical: 'bottom',
                        horizontal: 'left',
                      },
                      transformOrigin: {
                        vertical: 'top',
                        horizontal: 'left',
                      },
                      disablePortal: true,
                    }}
                    sx={{
                      borderRadius: 2,
                      minHeight: 56,
                      minWidth: 120,
                      '& .MuiSelect-select': {
                        display: 'flex',
                        alignItems: 'center',
                        minHeight: '1.4375em',
                      },
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#1976d2',
                      }
                    }}
                  >
                    <MenuItem value={true}>启用</MenuItem>
                    <MenuItem value={false}>禁用</MenuItem>
                  </Select>
                </FormControl>
              </Grid>

            </Grid>
          </Box>
        </DialogContent>
        <DialogActions sx={{
          p: 3,
          borderTop: '1px solid #e0e0e0',
          gap: 2
        }}>
          <Button
            onClick={handleCloseDialog}
            variant="outlined"
            sx={{
              borderRadius: 2,
              px: 3,
              py: 1,
              borderColor: '#e0e0e0',
              color: '#666',
              '&:hover': {
                borderColor: '#1976d2',
                backgroundColor: '#f5f5f5'
              }
            }}
          >
            取消
          </Button>
          <Button
            onClick={handleCreateService}
            variant="contained"
            disabled={!newService.service_name.trim() || !newService.service_code.trim() || !newService.category_id.trim()}
            sx={{
              borderRadius: 2,
              px: 4,
              py: 1,
              background: 'linear-gradient(45deg, #1976d2 30%, #42a5f5 90%)',
              boxShadow: '0 3px 5px 2px rgba(25, 118, 210, .3)',
              '&:hover': {
                background: 'linear-gradient(45deg, #1565c0 30%, #1976d2 90%)',
              },
              '&:disabled': {
                background: '#e0e0e0',
                color: '#999'
              }
            }}
          >
            创建服务
          </Button>
        </DialogActions>
      </Dialog>

      {/* 编辑服务对话框 */}
      <Dialog
        open={editDialogOpen}
        onClose={handleCloseEditDialog}
        maxWidth="md"
        fullWidth
        scroll="paper"
        PaperProps={{
          sx: {
            borderRadius: 3,
            maxHeight: '95vh',
            margin: 1,
          }
        }}
      >
        <DialogTitle sx={{
          pb: 2,
          borderBottom: '1px solid #e0e0e0',
          display: 'flex',
          alignItems: 'center',
          gap: 2
        }}>
          <Box sx={{
            width: 40,
            height: 40,
            borderRadius: 2,
            bgcolor: '#ff9800',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            <Edit sx={{ color: 'white' }} />
          </Box>
          <Box>
            <Typography variant="h6" sx={{ fontWeight: 600 }}>
              编辑服务
            </Typography>
            <Typography variant="body2" color="text.secondary">
              修改服务信息和参数
            </Typography>
          </Box>
        </DialogTitle>
        <DialogContent sx={{ pt: 3, pb: 2 }}>
          {editService && (
            <Box>
              {/* 基本信息 */}
              <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2, color: '#1976d2' }}>
                基本信息
              </Typography>
              <Grid container spacing={2} sx={{ mb: 2 }}>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="服务名称"
                    value={editService.service_name}
                    onChange={(e) => setEditService({ ...editService, service_name: e.target.value })}
                    required
                    variant="outlined"
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 2,
                        '&:hover fieldset': {
                          borderColor: '#1976d2',
                        }
                      }
                    }}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="实体账户ID"
                    value={editService.service_code}
                    onChange={(e) => setEditService({ ...editService, service_code: e.target.value })}
                    required
                    variant="outlined"
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 2,
                        '&:hover fieldset': {
                          borderColor: '#1976d2',
                        }
                      }
                    }}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <FormControl fullWidth variant="outlined">
                    <InputLabel shrink>服务分类</InputLabel>
                    <Select
                      value={editService.category_id}
                      label="服务分类"
                      onChange={(e) => setEditService({ ...editService, category_id: e.target.value })}
                      displayEmpty
                      notched
                      sx={{
                        borderRadius: 2,
                        minHeight: 56,
                        minWidth: 200,
                        '&:hover .MuiOutlinedInput-notchedOutline': {
                          borderColor: '#1976d2',
                        }
                      }}
                    >
                      {Array.isArray(categories) ? categories
                        .map((category) => (
                          <MenuItem key={category.id} value={category.id}>
                            {category.category_name}
                          </MenuItem>
                        )) : null}
                    </Select>
                  </FormControl>
                </Grid>
              </Grid>

              {/* 服务描述独占一行 */}
              <Box sx={{ mb: 3 }}>
                <TextField
                  fullWidth
                  label="服务描述"
                  placeholder="请输入服务的详细描述"
                  multiline
                  rows={4}
                  value={editService.service_description}
                  onChange={(e) => setEditService({ ...editService, service_description: e.target.value })}
                  variant="outlined"
                  sx={{
                    width: '100%',
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 2,
                      '&:hover fieldset': {
                        borderColor: '#1976d2',
                      },
                      '&.Mui-focused fieldset': {
                        borderColor: '#1976d2',
                      }
                    },
                    '& .MuiInputLabel-root.Mui-focused': {
                      color: '#1976d2',
                    }
                  }}
                />
              </Box>

              {/* 服务特色 */}
              <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2, color: '#1976d2' }}>
                服务特色
              </Typography>
              <Grid container spacing={2} sx={{ mb: 3 }}>
                <Grid item xs={12}>
                  {/* 特色标签显示 */}
                  <Box sx={{ mb: 2 }}>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                      {Array.isArray(editService.service_features) && editService.service_features.map((feature, index) => (
                        <Chip
                          key={index}
                          label={feature}
                          onDelete={() => {
                            const newFeatures = editService.service_features.filter((_, i) => i !== index);
                            setEditService({ ...editService, service_features: newFeatures });
                          }}
                          color="primary"
                          variant="outlined"
                          sx={{ borderRadius: '16px' }}
                        />
                      ))}
                    </Box>

                    {/* 添加特色输入框 */}
                    <TextField
                      fullWidth
                      label="添加服务特色"
                      placeholder="输入特色后按回车添加"
                      variant="outlined"
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          e.preventDefault();
                          const value = e.target.value.trim();
                          if (value && !editService.service_features.includes(value)) {
                            const newFeatures = [...(editService.service_features || []), value];
                            setEditService({ ...editService, service_features: newFeatures });
                            e.target.value = '';
                          }
                        }
                      }}
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          borderRadius: 2,
                          '&:hover fieldset': {
                            borderColor: '#1976d2',
                          }
                        }
                      }}
                    />
                  </Box>
                </Grid>
              </Grid>

              {/* 价格信息 */}
              <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2, color: '#1976d2' }}>
                价格信息
              </Typography>
              <Grid container spacing={2} sx={{ mb: 3 }}>
                <Grid item xs={12} md={4}>
                  <TextField
                    fullWidth
                    label="基础价格"
                    type="number"
                    value={editService.base_price}
                    onChange={(e) => setEditService({ ...editService, base_price: e.target.value })}
                    inputProps={{ min: 0, step: 0.01 }}
                    variant="outlined"
                    InputProps={{
                      startAdornment: <Typography variant="body2" color="text.secondary">¥</Typography>
                    }}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 2,
                        '&:hover fieldset': {
                          borderColor: '#1976d2',
                        }
                      }
                    }}
                  />
                </Grid>

                <Grid item xs={12} md={4}>
                  <TextField
                    fullWidth
                    label="计费单位"
                    placeholder="如：次、小时、天"
                    value={editService.price_unit}
                    onChange={(e) => setEditService({ ...editService, price_unit: e.target.value })}
                    variant="outlined"
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 2,
                        '&:hover fieldset': {
                          borderColor: '#1976d2',
                        }
                      }
                    }}
                  />
                </Grid>
              </Grid>

              {/* 服务设置 */}
              <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2, color: '#1976d2' }}>
                服务设置
              </Typography>
              <Grid container spacing={2} sx={{ mb: 3 }}>


              </Grid>

              {/* 平台设置 */}
              <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2, color: '#1976d2' }}>
                平台设置
              </Typography>
              <Grid container spacing={2} sx={{ mb: 3 }}>
                <Grid item xs={12} md={6}>
                  <FormControl fullWidth variant="outlined" required>
                    <InputLabel shrink>标签分类</InputLabel>
                    <Select
                      value={editService.channel_type}
                      label="标签分类"
                      onChange={(e) => setEditService({ ...editService, channel_type: e.target.value })}
                      displayEmpty
                      notched
                      sx={{
                        borderRadius: 2,
                        minHeight: 56,
                        minWidth: 200,
                        backgroundColor: '#ffffff',
                        '&:hover .MuiOutlinedInput-notchedOutline': {
                          borderColor: '#1976d2',
                        }
                      }}
                    >
                      <MenuItem value="">请选择标签分类</MenuItem>
                      {channelTypeOptions.map((option) => (
                        <MenuItem key={option} value={option}>
                          {option}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
              </Grid>



              {/* 状态设置 */}
              <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2, color: '#1976d2' }}>
                状态设置
              </Typography>
              <Grid container spacing={2} sx={{ mb: 4 }}>
                <Grid item xs={6}>
                  <FormControl fullWidth variant="outlined">
                    <InputLabel>状态</InputLabel>
                    <Select
                      value={editService.is_active}
                      label="状态"
                      onChange={(e) => setEditService({ ...editService, is_active: e.target.value })}
                      displayEmpty
                      MenuProps={{
                        PaperProps: {
                          sx: {
                            maxHeight: 150,
                            borderRadius: 2,
                            boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
                            mt: 0.5,
                          }
                        },
                        anchorOrigin: {
                          vertical: 'bottom',
                          horizontal: 'left',
                        },
                        transformOrigin: {
                          vertical: 'top',
                          horizontal: 'left',
                        },
                        disablePortal: true,
                      }}
                      sx={{
                        borderRadius: 2,
                        minHeight: 56,
                        minWidth: 120,
                        '& .MuiSelect-select': {
                          display: 'flex',
                          alignItems: 'center',
                          minHeight: '1.4375em',
                        },
                        '&:hover .MuiOutlinedInput-notchedOutline': {
                          borderColor: '#1976d2',
                        }
                      }}
                    >
                      <MenuItem value={true}>启用</MenuItem>
                      <MenuItem value={false}>禁用</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
              </Grid>
            </Box>
          )}
        </DialogContent>
        <DialogActions sx={{
          p: 3,
          borderTop: '1px solid #e0e0e0',
          gap: 2
        }}>
          <Button
            onClick={handleCloseEditDialog}
            variant="outlined"
            sx={{
              borderRadius: 2,
              px: 3,
              py: 1,
              borderColor: '#e0e0e0',
              color: '#666',
              '&:hover': {
                borderColor: '#1976d2',
                backgroundColor: '#f5f5f5'
              }
            }}
          >
            取消
          </Button>
          <Button
            onClick={handleEditService}
            variant="contained"
            disabled={!editService?.service_name?.trim() || !editService?.service_code?.trim()}
            sx={{
              borderRadius: 2,
              px: 4,
              py: 1,
              background: 'linear-gradient(45deg, #ff9800 30%, #ffb74d 90%)',
              boxShadow: '0 3px 5px 2px rgba(255, 152, 0, .3)',
              '&:hover': {
                background: 'linear-gradient(45deg, #f57c00 30%, #ff9800 90%)',
              },
              '&:disabled': {
                background: '#e0e0e0',
                color: '#999'
              }
            }}
          >
            更新服务
          </Button>
        </DialogActions>
      </Dialog>

      {/* 服务详情对话框 */}
      <Dialog
        open={serviceDetailOpen}
        onClose={() => setServiceDetailOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle sx={{
          pb: 2,
          borderBottom: '1px solid #e0e0e0',
          display: 'flex',
          alignItems: 'center',
          gap: 2
        }}>
          <Box sx={{
            width: 40,
            height: 40,
            borderRadius: 2,
            bgcolor: '#1976d2',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            <Settings sx={{ color: 'white' }} />
          </Box>
          <Box>
            <Typography variant="h6" sx={{ fontWeight: 600 }}>
              服务详情
            </Typography>
            <Typography variant="body2" color="text.secondary">
              查看服务的详细信息
            </Typography>
          </Box>
        </DialogTitle>
        <DialogContent>
          {selectedService && (
            <Box sx={{ pt: 3 }}>
              {/* 基本信息 - 一排显示 */}
              <Box sx={{ mb: 4 }}>
                <Typography variant="h6" sx={{ fontWeight: 600, mb: 3, color: '#1976d2', display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Settings sx={{ fontSize: 20 }} />
                  基本信息
                </Typography>
                <Grid container spacing={3}>
                  <Grid item xs={12} md={2.4}>
                    <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#f8f9fa', borderRadius: 2 }}>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>服务名称</Typography>
                      <Typography variant="body1" sx={{ fontWeight: 600 }}>{selectedService.service_name}</Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} md={2.4}>
                    <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#f8f9fa', borderRadius: 2 }}>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>服务分类</Typography>
                      <Typography variant="body1" sx={{ fontWeight: 600 }}>{getCategoryName(selectedService.category_id)}</Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} md={2.4}>
                    <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#f8f9fa', borderRadius: 2 }}>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>服务价格</Typography>
                      <Typography variant="body1" sx={{ fontWeight: 600, color: '#4caf50' }}>
                        ¥{selectedService.base_price}
                        {selectedService.price_unit && `/${selectedService.price_unit}`}
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} md={2.4}>
                    <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#f8f9fa', borderRadius: 2 }}>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>门户类型</Typography>
                      <Typography variant="body1" sx={{ fontWeight: 600 }}>{selectedService.portal_type || '未设置'}</Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} md={2.4}>
                    <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#f8f9fa', borderRadius: 2 }}>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>状态</Typography>
                      <Chip
                        label={selectedService.is_active ? '启用' : '禁用'}
                        color={selectedService.is_active ? 'success' : 'error'}
                        size="small"
                        sx={{ fontWeight: 600 }}
                      />
                    </Box>
                  </Grid>
                </Grid>

                {/* 第二行 - 频道类型等 */}
                {(selectedService.channel_type || selectedService.coverage_area?.length > 0) && (
                  <Grid container spacing={3} sx={{ mt: 2 }}>
                    {selectedService.channel_type && (
                      <Grid item xs={12} md={2.4}>
                        <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#f8f9fa', borderRadius: 2 }}>
                          <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>频道类型</Typography>
                          <Typography variant="body1" sx={{ fontWeight: 600 }}>{selectedService.channel_type}</Typography>
                        </Box>
                      </Grid>
                    )}
                    {selectedService.coverage_area?.length > 0 && (
                      <Grid item xs={12} md={2.4}>
                        <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#f8f9fa', borderRadius: 2 }}>
                          <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>覆盖区域</Typography>
                          <Typography variant="body1" sx={{ fontWeight: 600 }}>
                            {selectedService.coverage_area.join(', ')}
                          </Typography>
                        </Box>
                      </Grid>
                    )}
                  </Grid>
                )}
              </Box>

              {/* 服务详情 - 一排显示 */}
              <Box sx={{ mb: 4 }}>
                <Typography variant="h6" sx={{ fontWeight: 600, mb: 3, color: '#ff9800', display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Visibility sx={{ fontSize: 20 }} />
                  服务详情
                </Typography>
                <Grid container spacing={3}>
                  <Grid item xs={12} md={3}>
                    <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#f8f9fa', borderRadius: 2 }}>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>交付时长</Typography>
                      <Typography variant="body1" sx={{ fontWeight: 600 }}>
                        {selectedService.delivery_time ? `${selectedService.delivery_time}小时` : '不限'}
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} md={3}>
                    <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#f8f9fa', borderRadius: 2 }}>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>修改次数</Typography>
                      <Typography variant="body1" sx={{ fontWeight: 600 }}>
                        {selectedService.revision_count || 0}次
                      </Typography>
                    </Box>
                  </Grid>


                </Grid>
              </Box>

              {/* 服务描述 */}
              {selectedService.service_description && (
                <Box sx={{ mb: 2 }}>
                  <Typography variant="h6" sx={{ fontWeight: 600, mb: 3, color: '#4caf50', display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Edit sx={{ fontSize: 20 }} />
                    服务描述
                  </Typography>
                  <Box sx={{ p: 3, bgcolor: '#f8f9fa', borderRadius: 2, border: '1px solid #e0e0e0' }}>
                    <Typography variant="body1" sx={{ lineHeight: 1.8, color: '#333' }}>
                      {selectedService.service_description}
                    </Typography>
                  </Box>
                </Box>
              )}

              {/* 服务特色 */}
              {selectedService.service_features && selectedService.service_features.length > 0 && (
                <Box sx={{ mb: 2 }}>
                  <Typography variant="h6" sx={{ fontWeight: 600, mb: 3, color: '#2196f3', display: 'flex', alignItems: 'center', gap: 1 }}>
                    <CheckCircle sx={{ fontSize: 20 }} />
                    服务特色
                  </Typography>
                  <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                    {selectedService.service_features.map((feature, index) => (
                      <Chip
                        key={index}
                        label={feature}
                        variant="filled"
                        color="primary"
                        size="small"
                        sx={{
                          borderRadius: '16px',
                          fontWeight: 500,
                          '& .MuiChip-label': {
                            px: 2
                          }
                        }}
                      />
                    ))}
                  </Box>
                </Box>
              )}

              {/* 服务标签 */}
              {selectedService.tags && selectedService.tags.length > 0 && (
                <Box sx={{ mb: 2 }}>
                  <Typography variant="h6" sx={{ fontWeight: 600, mb: 3, color: '#6b7280', display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Settings sx={{ fontSize: 20 }} />
                    服务标签
                  </Typography>
                  <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                    {selectedService.tags.map((tag, index) => (
                      <Chip
                        key={index}
                        label={tag}
                        variant="outlined"
                        size="small"
                        sx={{
                          borderRadius: 2,
                          fontSize: '0.75rem',
                          fontWeight: 500
                        }}
                      />
                    ))}
                  </Box>
                </Box>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions sx={{ p: 3, borderTop: '1px solid #e0e0e0', gap: 2 }}>
          <Button
            onClick={() => setServiceDetailOpen(false)}
            variant="outlined"
            sx={{
              borderRadius: 2,
              px: 3,
              borderColor: '#e0e0e0',
              color: '#666',
              '&:hover': {
                borderColor: '#1976d2',
                backgroundColor: '#f5f5f5'
              }
            }}
          >
            关闭
          </Button>
        </DialogActions>
      </Dialog>

      {/* 关联渠道商对话框 */}
      <Dialog
        open={linkProviderDialogOpen}
        onClose={handleCloseLinkProviderDialog}
        maxWidth="md"
        fullWidth
        scroll="paper"
        PaperProps={{
          sx: {
            borderRadius: 3,
            maxHeight: '90vh',
            margin: 1,
          }
        }}
      >
        <DialogTitle sx={{
          pb: 2,
          borderBottom: '1px solid #e0e0e0',
          display: 'flex',
          alignItems: 'center',
          gap: 2
        }}>
          <Box sx={{
            width: 40,
            height: 40,
            borderRadius: 2,
            bgcolor: '#6b7280',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            <Link sx={{ color: 'white' }} />
          </Box>
          <Box>
            <Typography variant="h6" sx={{ fontWeight: 600 }}>
              关联渠道商
            </Typography>
            <Typography variant="body2" color="text.secondary">
              为服务"{selectedServiceForLink?.service_name}"选择要关联的渠道商
            </Typography>
            <Typography variant="body2" color="warning.main" sx={{ mt: 1, fontWeight: 500 }}>
              注意：每个服务最多只能关联一个内容提供商
            </Typography>
          </Box>
        </DialogTitle>
        <DialogContent sx={{ p: 3 }}>
          {/* 搜索框 */}
          <TextField
            fullWidth
            placeholder="搜索渠道商（名称、邮箱、真实姓名、公司名称）"
            value={providerSearchTerm}
            onChange={(e) => setProviderSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Search />
                </InputAdornment>
              ),
            }}
            sx={{ mb: 3 }}
          />

          {/* 渠道商列表 */}
          <Box sx={{ maxHeight: '400px', overflowY: 'auto' }}>
            {filteredProviders.length === 0 ? (
              <Box sx={{ textAlign: 'center', py: 4 }}>
                <Typography variant="body2" color="text.secondary">
                  {providerSearchTerm ? '未找到匹配的渠道商' : '暂无渠道商数据'}
                </Typography>
              </Box>
            ) : (
              filteredProviders.map((provider) => (
                <Card
                  key={provider.id}
                  sx={{
                    mb: 2,
                    cursor: 'pointer',
                    border: selectedProvider?.id === provider.id ? '2px solid #6b7280' : '1px solid #e0e0e0',
                    '&:hover': {
                      boxShadow: 2,
                      borderColor: '#6b7280'
                    }
                  }}
                  onClick={() => setSelectedProvider(provider)}
                >
                  <CardContent sx={{ p: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                      <Avatar sx={{ bgcolor: '#6b7280' }}>
                        <Person />
                      </Avatar>
                      <Box sx={{ flex: 1 }}>
                        <Typography variant="h6" sx={{ fontWeight: 600, mb: 0.5 }}>
                          {provider.provider_name}
                        </Typography>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 0.5 }}>
                          {provider.provider_type === 'individual' ? '个人' :
                           provider.provider_type === 'company' ? '公司' :
                           provider.provider_type === 'studio' ? '工作室' :
                           provider.provider_type === 'mcn' ? 'MCN机构' : '未知类型'}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          邮箱: {provider.contact_email}
                        </Typography>
                        {provider.real_name && (
                          <Typography variant="body2" color="text.secondary">
                            真实姓名: {provider.real_name}
                          </Typography>
                        )}
                        {provider.company_name && (
                          <Typography variant="body2" color="text.secondary">
                            公司名称: {provider.company_name}
                          </Typography>
                        )}
                      </Box>
                      {selectedProvider?.id === provider.id && (
                        <CheckCircle sx={{ color: '#6b7280' }} />
                      )}
                    </Box>
                  </CardContent>
                </Card>
              ))
            )}
          </Box>
        </DialogContent>
        <DialogActions sx={{ p: 3, borderTop: '1px solid #e0e0e0', gap: 2 }}>
          <Button
            onClick={handleCloseLinkProviderDialog}
            variant="outlined"
            sx={{
              borderRadius: 2,
              px: 3,
              borderColor: '#e0e0e0',
              color: '#666',
              '&:hover': {
                borderColor: '#6b7280',
                backgroundColor: '#f5f5f5'
              }
            }}
          >
            取消
          </Button>
          <Button
            onClick={handleLinkProvider}
            variant="contained"
            disabled={!selectedProvider || linkingLoading}
            sx={{
              borderRadius: 2,
              px: 3,
              bgcolor: '#6b7280',
              '&:hover': { bgcolor: '#4b5563' }
            }}
          >
            {linkingLoading ? <CircularProgress size={20} color="inherit" /> : '确认关联'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* 提示消息 */}
      <Snackbar
        open={alert.open}
        autoHideDuration={4000}
        onClose={() => setAlert({ ...alert, open: false })}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert
          onClose={() => setAlert({ ...alert, open: false })}
          severity={alert.severity}
          sx={{ width: '100%' }}
        >
          {alert.message}
        </Alert>
      </Snackbar>
      </Container>
    </Box>
  );
}

export default ServiceManagement;
