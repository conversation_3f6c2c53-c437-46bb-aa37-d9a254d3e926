import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Container,
  Typo<PERSON>,
  Card,
  CardContent,
  Paper,
  Button,
  IconButton,
  Badge,
  Tabs,
  Tab,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Checkbox,
  Toolbar,
  Tooltip,
  Alert,
  CircularProgress,
  Collapse,
  InputAdornment,
  Stack,
  Avatar,
} from '@mui/material';

import {
  ShoppingCart,
  Payment,
  CheckCircle,
  Cancel,
  Search,
  FilterList,
  Download,
  AttachMoney,
  Assignment,
  Refresh,
  TrendingUp,
  TrendingDown,
  PendingActions,
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { format, subDays, startOfDay, endOfDay } from 'date-fns';
import { useSnackbar } from 'notistack';
import orderService from '../../services/orderService';

// Tab panel component
function TabPanel({ children, value, index, ...other }) {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`order-tabpanel-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

// Order status color mapping
const getStatusColor = (status) => {
  const statusColors = {
    pending: 'warning',
    processing: 'info',
    completed: 'success',
    cancelled: 'error',
    refunded: 'default',
    failed: 'error',
    pending_payment: 'warning',
  };
  return statusColors[status] || 'default';
};

// Order type color mapping
const getOrderTypeColor = (type) => {
  const typeColors = {
    subscription: 'primary',
    one_time: 'secondary',
    renewal: 'info',
    upgrade: 'success',
  };
  return typeColors[type] || 'default';
};

function OrderCenter() {
  const { enqueueSnackbar } = useSnackbar();
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(false);
  const [orders, setOrders] = useState([]);
  const [selectedOrders, setSelectedOrders] = useState([]);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  
  // Filter states
  const [filters, setFilters] = useState({
    search: '',
    status: 'all',
    type: 'all',
    dateFrom: null,
    dateTo: null,
    minAmount: '',
    maxAmount: '',
  });
  
  // Statistics
  const [statistics, setStatistics] = useState({
    todayOrders: 0,
    pendingPayment: 0,
    completed: 0,
    refunding: 0,
    todayRevenue: 0,
    growthRate: 0,
  });

  // Load orders based on current tab and filters
  const loadOrders = useCallback(async () => {
    setLoading(true);
    try {
      let response;
      const params = {
        page: page + 1,
        limit: rowsPerPage,
        ...filters,
      };

      switch (tabValue) {
        case 0: // All orders
          response = await orderService.getOrders(params);
          break;
        case 1: // Pending payment
          response = await orderService.getPendingPaymentOrders(params);
          break;
        default:
          response = await orderService.getOrders(params);
      }

      setOrders(response.data || []);
      setTotalCount(response.total || 0);
    } catch (error) {
      enqueueSnackbar('Failed to load orders', { variant: 'error' });
      console.error('Error loading orders:', error);
    } finally {
      setLoading(false);
    }
  }, [tabValue, page, rowsPerPage, filters, enqueueSnackbar]);

  // Load statistics
  const loadStatistics = async () => {
    try {
      const stats = await orderService.getOrderStatistics({
        from: startOfDay(new Date()),
        to: endOfDay(new Date()),
      });
      setStatistics(stats);
    } catch (error) {
      console.error('Error loading statistics:', error);
    }
  };



  // Initial load
  useEffect(() => {
    loadOrders();
    loadStatistics();
  }, [loadOrders]);

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
    setSelectedOrders([]);
  };

  // Handle filter change
  const handleFilterChange = (field, value) => {
    setFilters(prev => ({ ...prev, [field]: value }));
    setPage(0);
  };

  // Handle search
  const handleSearch = async () => {
    if (filters.search) {
      setLoading(true);
      try {
        const response = await orderService.searchOrders({
          query: filters.search,
          ...filters,
        });
        setOrders(response.data || []);
        setTotalCount(response.total || 0);
      } catch (error) {
        enqueueSnackbar('Search failed', { variant: 'error' });
      } finally {
        setLoading(false);
      }
    } else {
      loadOrders();
    }
  };

  // Handle order selection
  const handleSelectOrder = (orderId) => {
    setSelectedOrders(prev =>
      prev.includes(orderId)
        ? prev.filter(id => id !== orderId)
        : [...prev, orderId]
    );
  };

  // Handle select all
  const handleSelectAll = (event) => {
    if (event.target.checked) {
      setSelectedOrders(orders.map(order => order.id));
    } else {
      setSelectedOrders([]);
    }
  };



  // Cancel order
  const handleCancelOrder = async (orderId) => {
    try {
      await orderService.cancelOrder(orderId);
      enqueueSnackbar('Order cancelled successfully', { variant: 'success' });
      loadOrders();
    } catch (error) {
      enqueueSnackbar('Failed to cancel order', { variant: 'error' });
    }
  };

  // Process payment
  const handleProcessPayment = async (orderId) => {
    try {
      await orderService.processPayment(orderId, {
        payment_method: 'online',
      });
      enqueueSnackbar('Payment processed successfully', { variant: 'success' });
      loadOrders();
    } catch (error) {
      enqueueSnackbar('Failed to process payment', { variant: 'error' });
    }
  };

  // Batch cancel
  const handleBatchCancel = async () => {
    if (selectedOrders.length === 0) {
      enqueueSnackbar('Please select orders to cancel', { variant: 'warning' });
      return;
    }
    
    try {
      await orderService.batchCancelOrders(selectedOrders, 'Batch cancellation');
      enqueueSnackbar(`${selectedOrders.length} orders cancelled`, { variant: 'success' });
      setSelectedOrders([]);
      loadOrders();
    } catch (error) {
      enqueueSnackbar('Failed to cancel orders', { variant: 'error' });
    }
  };

  // Export orders
  const handleExport = async () => {
    try {
      const blob = await orderService.exportOrders(filters);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `orders_${format(new Date(), 'yyyy-MM-dd')}.csv`;
      a.click();
      window.URL.revokeObjectURL(url);
      enqueueSnackbar('Orders exported successfully', { variant: 'success' });
    } catch (error) {
      enqueueSnackbar('Failed to export orders', { variant: 'error' });
    }
  };



  // Statistics Cards
  const StatCard = ({ title, value, icon, color, trend }) => (
    <Card sx={{ height: '100%' }}>
      <CardContent>
        <Box display="flex" justifyContent="space-between" alignItems="flex-start">
          <Box>
            <Typography color="textSecondary" gutterBottom variant="subtitle2">
              {title}
            </Typography>
            <Typography variant="h4" component="div">
              {value}
            </Typography>
            {trend !== undefined && (
              <Box display="flex" alignItems="center" mt={1}>
                {trend > 0 ? (
                  <TrendingUp color="success" fontSize="small" />
                ) : (
                  <TrendingDown color="error" fontSize="small" />
                )}
                <Typography
                  variant="body2"
                  color={trend > 0 ? 'success.main' : 'error.main'}
                  ml={0.5}
                >
                  {Math.abs(trend)}%
                </Typography>
              </Box>
            )}
          </Box>
          <Avatar sx={{ bgcolor: color, width: 48, height: 48 }}>
            {icon}
          </Avatar>
        </Box>
      </CardContent>
    </Card>
  );

  return (
    <Container maxWidth={false} sx={{ mt: 3, mb: 3 }}>
      {/* Page Header */}
      <Box mb={3}>
        <Typography variant="h4" gutterBottom>
          订单管理中心
        </Typography>
        <Typography variant="body2" color="textSecondary">
          全面的订单管理和实时跟踪
        </Typography>
      </Box>

      {/* Statistics Cards */}
      <Box sx={{ display: 'flex', gap: 3, mb: 3, flexWrap: 'wrap' }}>
        <Box sx={{ flex: '1 1 250px', minWidth: 250 }}>
          <StatCard
            title="今日订单"
            value={statistics.todayOrders}
            icon={<ShoppingCart />}
            color="primary.main"
            trend={statistics.growthRate}
          />
        </Box>
        <Box sx={{ flex: '1 1 250px', minWidth: 250 }}>
          <StatCard
            title="待支付"
            value={statistics.pendingPayment}
            icon={<PendingActions />}
            color="warning.main"
          />
        </Box>
        <Box sx={{ flex: '1 1 250px', minWidth: 250 }}>
          <StatCard
            title="已完成"
            value={statistics.completed}
            icon={<CheckCircle />}
            color="success.main"
          />
        </Box>
        <Box sx={{ flex: '1 1 250px', minWidth: 250 }}>
          <StatCard
            title="今日收入"
            value={`$${statistics.todayRevenue.toFixed(2)}`}
            icon={<AttachMoney />}
            color="info.main"
            trend={12}
          />
        </Box>
      </Box>

      <Paper sx={{ width: '100%' }}>
        {/* Tabs */}
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={handleTabChange}>
            <Tab label="订单列表" icon={<Assignment />} iconPosition="start" />
            <Tab
              label="待处理订单"
              icon={
                <Badge badgeContent={statistics.pendingPayment} color="warning">
                  <PendingActions />
                </Badge>
              }
              iconPosition="start"
            />
          </Tabs>
        </Box>

            {/* Tab Panel 0: Order List */}
            <TabPanel value={tabValue} index={0}>
              {/* Filters */}
              <Paper elevation={0} sx={{ p: 2, mb: 2, bgcolor: 'grey.50' }}>
                <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', alignItems: 'end' }}>
                  <Box sx={{ flex: '1 1 300px', minWidth: 300 }}>
                    <TextField
                      fullWidth
                      size="small"
                      placeholder="搜索订单..."
                      value={filters.search}
                      onChange={(e) => handleFilterChange('search', e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Search />
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Box>
                  <Box sx={{ flex: '0 0 150px', minWidth: 150 }}>
                    <FormControl fullWidth size="small">
                      <InputLabel>类型</InputLabel>
                      <Select
                        value={filters.type}
                        label="类型"
                        onChange={(e) => handleFilterChange('type', e.target.value)}
                      >
                        <MenuItem value="all">全部类型</MenuItem>
                        <MenuItem value="subscription">订阅</MenuItem>
                        <MenuItem value="one_time">一次性</MenuItem>
                        <MenuItem value="renewal">续订</MenuItem>
                        <MenuItem value="upgrade">升级</MenuItem>
                      </Select>
                    </FormControl>
                  </Box>
                  <Box sx={{ flex: '0 0 150px', minWidth: 150 }}>
                    <FormControl fullWidth size="small">
                      <InputLabel>状态</InputLabel>
                      <Select
                        value={filters.status}
                        label="状态"
                        onChange={(e) => handleFilterChange('status', e.target.value)}
                      >
                        <MenuItem value="all">全部状态</MenuItem>
                        <MenuItem value="pending">待处理</MenuItem>
                        <MenuItem value="pending_payment">待支付</MenuItem>
                        <MenuItem value="processing">处理中</MenuItem>
                        <MenuItem value="completed">已完成</MenuItem>
                        <MenuItem value="cancelled">已取消</MenuItem>
                        <MenuItem value="refunded">已退款</MenuItem>
                      </Select>
                    </FormControl>
                  </Box>
                  <Box sx={{ flex: '0 0 150px', minWidth: 150 }}>
                    <LocalizationProvider dateAdapter={AdapterDateFns}>
                      <DatePicker
                        label="开始日期"
                        value={filters.dateFrom}
                        onChange={(date) => handleFilterChange('dateFrom', date)}
                        renderInput={(params) => <TextField {...params} size="small" fullWidth />}
                      />
                    </LocalizationProvider>
                  </Box>
                  <Box sx={{ flex: '0 0 150px', minWidth: 150 }}>
                    <LocalizationProvider dateAdapter={AdapterDateFns}>
                      <DatePicker
                        label="结束日期"
                        value={filters.dateTo}
                        onChange={(date) => handleFilterChange('dateTo', date)}
                        renderInput={(params) => <TextField {...params} size="small" fullWidth />}
                      />
                    </LocalizationProvider>
                  </Box>
                  <Box sx={{ flex: '0 0 100px', minWidth: 100 }}>
                    <Button
                      fullWidth
                      variant="contained"
                      startIcon={<Search />}
                      onClick={handleSearch}
                    >
                      搜索
                    </Button>
                  </Box>
                </Box>
              </Paper>

              {/* Toolbar for batch operations */}
              {selectedOrders.length > 0 && (
                <Toolbar sx={{ bgcolor: 'primary.light', mb: 2 }}>
                  <Typography variant="subtitle1" component="div" sx={{ flex: '1 1 100%' }}>
                    已选择 {selectedOrders.length} 项
                  </Typography>
                  <Tooltip title="取消所选">
                    <IconButton onClick={handleBatchCancel}>
                      <Cancel />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="导出所选">
                    <IconButton onClick={handleExport}>
                      <Download />
                    </IconButton>
                  </Tooltip>
                </Toolbar>
              )}

              {/* Orders Table */}
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell padding="checkbox">
                        <Checkbox
                          indeterminate={selectedOrders.length > 0 && selectedOrders.length < orders.length}
                          checked={orders.length > 0 && selectedOrders.length === orders.length}
                          onChange={handleSelectAll}
                        />
                      </TableCell>
                      <TableCell>订单ID</TableCell>
                      <TableCell>类型</TableCell>
                      <TableCell>客户</TableCell>
                      <TableCell>金额</TableCell>
                      <TableCell>状态</TableCell>
                      <TableCell>日期</TableCell>
                      <TableCell align="right">操作</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {loading ? (
                      <TableRow>
                        <TableCell colSpan={8} align="center">
                          <CircularProgress />
                        </TableCell>
                      </TableRow>
                    ) : orders.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={8} align="center">
                          <Typography color="textSecondary">没有找到订单</Typography>
                        </TableCell>
                      </TableRow>
                    ) : (
                      orders.map((order) => (
                        <TableRow
                          key={order.id}
                          hover
                          selected={selectedOrders.includes(order.id)}
                        >
                          <TableCell padding="checkbox">
                            <Checkbox
                              checked={selectedOrders.includes(order.id)}
                              onChange={() => handleSelectOrder(order.id)}
                            />
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2" fontWeight="medium">
                              #{order.id}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={order.type}
                              size="small"
                              color={getOrderTypeColor(order.type)}
                            />
                          </TableCell>
                          <TableCell>{order.customer_name || 'N/A'}</TableCell>
                          <TableCell>
                            <Typography variant="body2" fontWeight="medium">
                              ${order.amount?.toFixed(2) || '0.00'}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={order.status}
                              size="small"
                              color={getStatusColor(order.status)}
                            />
                          </TableCell>
                          <TableCell>
                            {format(new Date(order.created_at), 'MMM dd, yyyy')}
                          </TableCell>
                          <TableCell align="right">
                            {order.status === 'pending_payment' && (
                              <IconButton
                                size="small"
                                color="primary"
                                onClick={() => handleProcessPayment(order.id)}
                              >
                                <Payment />
                              </IconButton>
                            )}
                            {['pending', 'pending_payment'].includes(order.status) && (
                              <IconButton
                                size="small"
                                color="error"
                                onClick={() => handleCancelOrder(order.id)}
                              >
                                <Cancel />
                              </IconButton>
                            )}
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </TableContainer>

              {/* Pagination */}
              <TablePagination
                component="div"
                count={totalCount}
                page={page}
                onPageChange={(e, newPage) => setPage(newPage)}
                rowsPerPage={rowsPerPage}
                onRowsPerPageChange={(e) => {
                  setRowsPerPage(parseInt(e.target.value, 10));
                  setPage(0);
                }}
              />
            </TabPanel>

            {/* Tab Panel 1: Pending Orders */}
            <TabPanel value={tabValue} index={1}>
              <Alert severity="info" sx={{ mb: 2 }}>
                {statistics.pendingPayment} 个订单等待支付
              </Alert>
              
              {/* Batch operations toolbar */}
              <Box display="flex" justifyContent="space-between" mb={2}>
                <Stack direction="row" spacing={2}>
                  <Button
                    variant="outlined"
                    startIcon={<Refresh />}
                    onClick={loadOrders}
                  >
                    刷新
                  </Button>
                  <Button
                    variant="outlined"
                    color="error"
                    startIcon={<Cancel />}
                    onClick={handleBatchCancel}
                    disabled={selectedOrders.length === 0}
                  >
                    取消所选 ({selectedOrders.length})
                  </Button>
                </Stack>
                <Button
                  variant="contained"
                  startIcon={<Download />}
                  onClick={handleExport}
                >
                  导出
                </Button>
              </Box>

              {/* Pending orders list */}
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
                {orders.map((order) => (
                  <Card key={order.id} sx={{ minHeight: 120 }}>
                    <CardContent sx={{ p: 3 }}>
                      <Box display="flex" justifyContent="space-between" alignItems="flex-start">
                        <Box sx={{ flex: 1 }}>
                          <Typography variant="h5" sx={{ mb: 1 }}>
                            订单 #{order.id}
                          </Typography>
                          <Typography variant="h6" color="textSecondary" gutterBottom>
                            {order.customer_name} • {format(new Date(order.created_at), 'MMM dd, yyyy HH:mm')}
                          </Typography>
                          <Box mt={2}>
                            <Chip
                              label={order.type}
                              size="medium"
                              color={getOrderTypeColor(order.type)}
                              sx={{ mr: 2, fontSize: '0.875rem', height: 32 }}
                            />
                            <Chip
                              label="待支付"
                              size="medium"
                              color="warning"
                              sx={{ fontSize: '0.875rem', height: 32 }}
                            />
                          </Box>
                        </Box>
                        <Box textAlign="right" sx={{ minWidth: 200 }}>
                          <Typography variant="h4" color="primary" sx={{ mb: 2 }}>
                            ${order.amount?.toFixed(2)}
                          </Typography>
                          <Stack direction="row" spacing={2} justifyContent="flex-end">
                            <Button
                              size="large"
                              variant="contained"
                              color="primary"
                              startIcon={<Payment />}
                              onClick={() => handleProcessPayment(order.id)}
                              sx={{ minWidth: 120 }}
                            >
                              处理支付
                            </Button>
                            <Button
                              size="large"
                              variant="outlined"
                              color="error"
                              startIcon={<Cancel />}
                              onClick={() => handleCancelOrder(order.id)}
                              sx={{ minWidth: 100 }}
                            >
                              取消
                            </Button>
                          </Stack>
                        </Box>
                      </Box>
                    </CardContent>
                  </Card>
                ))}
              </Box>
            </TabPanel>
          </Paper>


    </Container>
  );
}

export default OrderCenter;