import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Button,
  IconButton,
  TextField,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Stack,
  Typography,
  Alert,
  Grid,
  InputAdornment,
  Tooltip,
  Avatar,
  Tab,
  Tabs
} from '@mui/material';
import {
  Search as SearchIcon,
  Visibility as ViewIcon,
  Edit as EditIcon,
  Refresh as RefreshIcon,
  PlayArrow as ActivateIcon,
  Pause as DeactivateIcon,
  Save as SaveIcon,
  RestartAlt as ResetIcon
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { zhCN } from 'date-fns/locale';
import { format } from 'date-fns';
import axios from 'axios';
import { ApiConfig } from '../../config/api-config';
import subscriptionService from '../../services/subscriptionService';
import userService from '../../services/userService';

// 创建axios实例
const axiosInstance = axios.create({
  baseURL: ApiConfig.baseURL,
  timeout: ApiConfig.timeout,
  headers: ApiConfig.defaultHeaders,
});

// 请求拦截器
axiosInstance.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem(`${ApiConfig.storage.prefix}${ApiConfig.storage.tokenKey}`);
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

const SubscriptionList = () => {
  const [subscriptions, setSubscriptions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [page, setPage] = useState(0);
  const rowsPerPage = 10; // 固定每页显示10条
  const [total, setTotal] = useState(0);
  const [selectedSubscription, setSelectedSubscription] = useState(null);
  const [openDetailDialog, setOpenDetailDialog] = useState(false);
  const [openEditDialog, setOpenEditDialog] = useState(false);
  const [editingSubscription, setEditingSubscription] = useState(null);

  const [usersInfo, setUsersInfo] = useState({}); // 存储用户信息
  const [tabValue, setTabValue] = useState(0);
  
  // 筛选条件
  const [filters, setFilters] = useState({
    keyword: '',
    status: '',
    product_id: '',
    user_type: '',
    start_date: null,
    end_date: null,
    min_amount: '',
    max_amount: ''
  });



  // 订阅状态选项
  const statusOptions = [
    { value: 'active', label: '生效中', color: 'success' },
    { value: 'expired', label: '已过期', color: 'warning' },
    { value: 'cancelled', label: '已取消', color: 'error' },
    { value: 'pending', label: '待支付', color: 'info' }
  ];

  // 用户类型选项
  const userTypeOptions = [
    { value: 'regular_user', label: '普通用户', color: 'default' },
    { value: 'enterprise_user', label: '企业用户', color: 'primary' },
    { value: 'channel_user', label: '渠道商', color: 'warning' },
    { value: 'agent_user', label: '代理商', color: 'success' }
  ];



  // 订阅周期选项
  const billingCycles = [
    { value: 'monthly', label: '月付' },
    { value: 'quarterly', label: '季度' },
    { value: 'semiannual', label: '半年' },
    { value: 'annual', label: '年付' }
  ];

  useEffect(() => {
    fetchSubscriptions();
  }, [page, rowsPerPage, filters, tabValue]); // eslint-disable-line react-hooks/exhaustive-deps

  // 获取用户信息
  const fetchUsersInfo = async (userIds) => {
    try {
      const uniqueUserIds = [...new Set(userIds)];
      const users = {};

      // 逐个获取用户信息
      for (const userId of uniqueUserIds) {
        try {
          const response = await userService.getUserById(userId);
          users[userId] = response.data || response;
        } catch (error) {
          console.warn(`获取用户 ${userId} 信息失败:`, error);
          // 提供默认用户信息
          users[userId] = {
            id: userId,
            email: `user_${userId.substring(0, 8)}@unknown.com`,
            full_name: `用户_${userId.substring(0, 8)}`,
            status: 'unknown'
          };
        }
      }

      setUsersInfo(prev => ({ ...prev, ...users }));
    } catch (error) {
      console.error('获取用户信息失败:', error);
    }
  };

  // 获取订阅列表
  const fetchSubscriptions = async () => {
    setLoading(true);
    setError('');
    try {
      // 根据tab筛选状态，转换为后端期望的is_active参数
      let isActiveFilter = null;
      if (tabValue === 1) isActiveFilter = true;  // 生效中
      else if (tabValue === 2) isActiveFilter = false; // 已过期
      else if (tabValue === 3) isActiveFilter = false; // 已取消

      const params = {
        page: page + 1,
        size: rowsPerPage,
        is_active: isActiveFilter,
        ...Object.fromEntries(
          Object.entries(filters).filter(([k, v]) => v !== '' && v !== null && k !== 'status')
        )
      };

      // 调用真实API获取订阅数据
      const response = await subscriptionService.getSubscriptions(params);

      // 处理API响应数据
      const subscriptionData = {
        items: response.items || response || [],
        total: response.total || response.length || 0
      };

      setSubscriptions(subscriptionData.items);
      setTotal(subscriptionData.total);

      // 获取用户信息
      if (subscriptionData.items.length > 0) {
        const userIds = subscriptionData.items.map(sub => sub.user_id).filter(Boolean);
        if (userIds.length > 0) {
          await fetchUsersInfo(userIds);
        }
      }

    } catch (error) {
      console.error('获取订阅列表失败:', error);
      setError('获取订阅列表失败');
    } finally {
      setLoading(false);
    }
  };



  // 查看订阅详情
  const handleViewDetails = (subscription) => {
    setSelectedSubscription(subscription);
    setOpenDetailDialog(true);
  };



  // 编辑订阅
  const handleEditSubscription = (subscription) => {
    setEditingSubscription({
      ...subscription,
      new_end_date: subscription.end_date,
      new_start_date: subscription.start_date,
      new_status: subscription.is_active ? 'active' : 'expired',
      new_auto_renewal: subscription.auto_renewal,
      new_content_used: subscription.content_used || 0,
      new_monitoring_used: subscription.monitoring_used || 0,
      new_ai_used: subscription.ai_used || 0,
      edit_reason: ''
    });
    setOpenEditDialog(true);
  };

  // 保存编辑
  const handleSaveEdit = async () => {
    if (!editingSubscription) return;

    setLoading(true);
    try {
      // 构建更新数据
      const updateData = {};

      // 检查哪些字段被修改了
      if (editingSubscription.new_end_date !== editingSubscription.end_date) {
        updateData.end_date = editingSubscription.new_end_date;
      }

      if (editingSubscription.new_start_date !== editingSubscription.start_date) {
        updateData.start_date = editingSubscription.new_start_date;
      }

      // 状态转换
      const currentStatus = editingSubscription.is_active ? 'active' : 'expired';
      if (editingSubscription.new_status !== currentStatus) {
        updateData.status = editingSubscription.new_status;
      }

      if (editingSubscription.new_auto_renewal !== editingSubscription.auto_renewal) {
        updateData.auto_renewal = editingSubscription.new_auto_renewal;
      }

      // 配额使用量
      if (editingSubscription.new_content_used !== (editingSubscription.content_used || 0)) {
        updateData.content_used = editingSubscription.new_content_used;
      }

      if (editingSubscription.new_monitoring_used !== (editingSubscription.monitoring_used || 0)) {
        updateData.monitoring_used = editingSubscription.new_monitoring_used;
      }

      if (editingSubscription.new_ai_used !== (editingSubscription.ai_used || 0)) {
        updateData.ai_used = editingSubscription.new_ai_used;
      }

      // 添加修改原因
      if (editingSubscription.edit_reason) {
        updateData.reason = editingSubscription.edit_reason;
      }

      // 如果没有任何修改，提示用户
      if (Object.keys(updateData).length === 0 || (Object.keys(updateData).length === 1 && updateData.reason)) {
        setError('没有检测到任何修改');
        return;
      }

      // 调用API更新订阅信息
      const updatedSubscription = await subscriptionService.adminEditSubscription(
        editingSubscription.id,
        updateData
      );

      // 更新本地数据
      setSubscriptions(prev => prev.map(sub =>
        sub.id === editingSubscription.id ? updatedSubscription : sub
      ));

      setSuccess('订阅信息更新成功');
      setOpenEditDialog(false);
      setEditingSubscription(null);

      // 刷新列表以获取最新数据
      await fetchSubscriptions();

    } catch (error) {
      console.error('更新订阅失败:', error);
      setError(error.response?.data?.detail || '更新失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 激活订阅
  const handleActivateSubscription = async (subscription) => {
    setLoading(true);
    try {
      await subscriptionService.adminActivateSubscription(
        subscription.id,
        '管理员手动激活订阅'
      );

      setSuccess(`订阅 ${subscription.order_id || subscription.id} 已激活`);

      // 刷新列表
      await fetchSubscriptions();

    } catch (error) {
      console.error('激活订阅失败:', error);
      setError(error.response?.data?.detail || '激活失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 停用订阅
  const handleDeactivateSubscription = async (subscription) => {
    setLoading(true);
    try {
      await subscriptionService.adminDeactivateSubscription(
        subscription.id,
        '管理员手动停用订阅'
      );

      setSuccess(`订阅 ${subscription.order_id || subscription.id} 已停用`);

      // 刷新列表
      await fetchSubscriptions();

    } catch (error) {
      console.error('停用订阅失败:', error);
      setError(error.response?.data?.detail || '停用失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 重置配额使用量
  const handleResetQuotaUsage = async (subscription) => {
    setLoading(true);
    try {
      await subscriptionService.adminResetQuotaUsage(
        subscription.id,
        '管理员重置配额使用量'
      );

      setSuccess(`订阅 ${subscription.order_id || subscription.id} 的配额使用量已重置`);

      // 刷新列表
      await fetchSubscriptions();

    } catch (error) {
      console.error('重置配额失败:', error);
      setError(error.response?.data?.detail || '重置配额失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };



  // 分页处理
  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };



  // 筛选处理
  const handleFilterChange = (field, value) => {
    setFilters(prev => ({
      ...prev,
      [field]: value
    }));
    setPage(0);
  };



  // 获取状态标签
  const getStatusChip = (status) => {
    const statusConfig = statusOptions.find(s => s.value === status);
    return statusConfig ? (
      <Chip 
        label={statusConfig.label} 
        size="small" 
        color={statusConfig.color}
        icon={statusConfig.icon}
      />
    ) : null;
  };

  // 获取用户类型标签
  const getUserTypeChip = (type) => {
    const typeConfig = userTypeOptions.find(t => t.value === type);
    return typeConfig ? (
      <Chip 
        label={typeConfig.label} 
        size="small" 
        color={typeConfig.color}
        variant="outlined"
      />
    ) : null;
  };



  // 获取订阅周期标签
  const getBillingCycleLabel = (cycle) => {
    const cycleConfig = billingCycles.find(c => c.value === cycle);
    return cycleConfig ? cycleConfig.label : cycle;
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        订阅列表
      </Typography>
      
      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        查看和管理所有用户的订阅订单，包括生效中、已过期和已取消的订阅
      </Typography>

      {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}
      {success && <Alert severity="success" sx={{ mb: 2 }}>{success}</Alert>}

      {/* 操作栏 */}
      <Paper sx={{ mb: 2 }}>
        {/* Tab切换 */}
        <Tabs value={tabValue} onChange={(e, v) => setTabValue(v)} sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tab label="全部" />
          <Tab label="生效中" />
          <Tab label="已过期" />
          <Tab label="已取消" />
        </Tabs>

        <Box sx={{ p: 2 }}>
          {/* 筛选区域 */}
          <Grid container spacing={2} alignItems="center" justifyContent="space-between">
            <Grid item xs={12} md={9}>
              <Grid container spacing={2} alignItems="center">
                <Grid item xs={12} sm={6} md={4}>
                  <TextField
                    fullWidth
                    size="small"
                    label="搜索订单号/用户"
                    value={filters.keyword}
                    onChange={(e) => handleFilterChange('keyword', e.target.value)}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <SearchIcon />
                        </InputAdornment>
                      ),
                    }}
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <FormControl fullWidth size="small" sx={{ minWidth: 120 }}>
                    <InputLabel>用户类型</InputLabel>
                    <Select
                      value={filters.user_type}
                      onChange={(e) => handleFilterChange('user_type', e.target.value)}
                      label="用户类型"
                    >
                      <MenuItem value="">全部</MenuItem>
                      {userTypeOptions.map(type => (
                        <MenuItem key={type.value} value={type.value}>
                          {type.label}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={6} md={2.5}>
                  <LocalizationProvider dateAdapter={AdapterDateFns} locale={zhCN}>
                    <DatePicker
                      label="开始日期"
                      value={filters.start_date}
                      onChange={(value) => handleFilterChange('start_date', value)}
                      slotProps={{
                        textField: {
                          size: 'small',
                          fullWidth: true
                        }
                      }}
                    />
                  </LocalizationProvider>
                </Grid>
                <Grid item xs={12} sm={6} md={2.5}>
                  <LocalizationProvider dateAdapter={AdapterDateFns} locale={zhCN}>
                    <DatePicker
                      label="结束日期"
                      value={filters.end_date}
                      onChange={(value) => handleFilterChange('end_date', value)}
                      slotProps={{
                        textField: {
                          size: 'small',
                          fullWidth: true
                        }
                      }}
                    />
                  </LocalizationProvider>
                </Grid>
              </Grid>
            </Grid>
            <Grid item xs={12} md={3}>
              <Stack direction="row" spacing={1} justifyContent="flex-end">
                <Button
                  variant="outlined"
                  startIcon={<RefreshIcon />}
                  onClick={fetchSubscriptions}
                >
                  刷新
                </Button>
              </Stack>
            </Grid>
          </Grid>
        </Box>
      </Paper>

      {/* 订阅列表 */}
      <TableContainer component={Paper}>
        <Table sx={{ '& .MuiTableRow-root': { borderBottom: '1px solid #e0e0e0' } }}>
          <TableHead>
            <TableRow sx={{ backgroundColor: '#f5f5f5' }}>
              <TableCell sx={{ fontWeight: 'bold', borderBottom: '2px solid #d0d0d0' }}>订阅ID</TableCell>
              <TableCell sx={{ fontWeight: 'bold', borderBottom: '2px solid #d0d0d0' }}>用户信息</TableCell>
              <TableCell sx={{ fontWeight: 'bold', borderBottom: '2px solid #d0d0d0' }}>套餐信息</TableCell>
              <TableCell sx={{ fontWeight: 'bold', borderBottom: '2px solid #d0d0d0' }}>配额使用</TableCell>
              <TableCell sx={{ fontWeight: 'bold', borderBottom: '2px solid #d0d0d0' }}>有效期</TableCell>
              <TableCell sx={{ fontWeight: 'bold', borderBottom: '2px solid #d0d0d0' }}>状态</TableCell>
              <TableCell sx={{ fontWeight: 'bold', borderBottom: '2px solid #d0d0d0' }}>续费设置</TableCell>
              <TableCell sx={{ fontWeight: 'bold', borderBottom: '2px solid #d0d0d0' }}>操作</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={8} align="center" sx={{ py: 8 }}>
                  <Typography variant="body1" color="text.secondary">
                    加载中...
                  </Typography>
                </TableCell>
              </TableRow>
            ) : subscriptions.length === 0 ? (
              <TableRow>
                <TableCell colSpan={8} align="center" sx={{ py: 8 }}>
                  <Typography variant="body1" color="text.secondary">
                    暂无订阅数据
                  </Typography>
                </TableCell>
              </TableRow>
            ) : (
              subscriptions.map((subscription, index) => {
                const userInfo = usersInfo[subscription.user_id];
                return (
                  <TableRow
                    key={subscription.id}
                    sx={{
                      backgroundColor: index % 2 === 0 ? '#fafafa' : '#ffffff',
                      '&:hover': {
                        backgroundColor: '#f0f0f0'
                      },
                      borderBottom: '1px solid #e0e0e0'
                    }}
                  >
                    <TableCell sx={{ padding: '16px', borderRight: '1px solid #f0f0f0' }}>
                      <Typography variant="body2" fontWeight="bold" sx={{
                        fontFamily: 'monospace',
                        fontSize: '0.8rem'
                      }}>
                        {subscription.id.substring(0, 12)}...
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {format(new Date(subscription.created_at), 'yyyy-MM-dd HH:mm')}
                      </Typography>
                    </TableCell>
                    <TableCell sx={{ padding: '16px', borderRight: '1px solid #f0f0f0' }}>
                      <Stack direction="row" spacing={1} alignItems="center">
                        <Avatar sx={{ width: 32, height: 32 }}>
                          {userInfo?.full_name ? userInfo.full_name[0].toUpperCase() :
                           userInfo?.email ? userInfo.email[0].toUpperCase() : 'U'}
                        </Avatar>
                        <Box>
                          <Typography variant="body2">
                            {userInfo?.full_name || userInfo?.email || '未知用户'}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {userInfo?.email || `ID: ${subscription.user_id.substring(0, 8)}...`}
                          </Typography>
                        </Box>
                      </Stack>
                    </TableCell>
                    <TableCell sx={{ padding: '16px', borderRight: '1px solid #f0f0f0' }}>
                      <Typography variant="body2" fontWeight="bold">
                        {subscription.plan_name || '未知套餐'}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {subscription.plan_type} • ¥{subscription.renewal_price || '0.00'}/月
                      </Typography>
                    </TableCell>
                    <TableCell sx={{ padding: '16px', borderRight: '1px solid #f0f0f0' }}>
                      <Stack spacing={0.5}>
                        <Typography variant="caption" color="text.secondary">
                          内容: {subscription.content_used || 0}/{subscription.content_quota || 0}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          监控: {subscription.monitoring_used || 0}/{subscription.monitoring_quota || 0}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          AI: {subscription.ai_used || 0}/{subscription.ai_quota || 0}
                        </Typography>
                      </Stack>
                    </TableCell>
                    <TableCell sx={{ padding: '16px', borderRight: '1px solid #f0f0f0' }}>
                      <Typography variant="body2">
                        {subscription.start_date?.split('T')[0]}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        至 {subscription.end_date?.split('T')[0]}
                      </Typography>
                      {subscription.days_remaining !== undefined && (
                        <Typography variant="caption" color={subscription.days_remaining > 7 ? 'success.main' : 'warning.main'}>
                          剩余 {subscription.days_remaining} 天
                        </Typography>
                      )}
                    </TableCell>
                    <TableCell sx={{ padding: '16px', borderRight: '1px solid #f0f0f0' }}>
                      <Stack direction="row" spacing={1} alignItems="center">
                        <Box
                          sx={{
                            width: 8,
                            height: 8,
                            borderRadius: '50%',
                            backgroundColor: subscription.is_active ? '#4caf50' : '#ff9800'
                          }}
                        />
                        <Chip
                          label={subscription.is_active ? '生效中' : '已过期'}
                          size="small"
                          color={subscription.is_active ? 'success' : 'warning'}
                          variant={subscription.is_active ? 'filled' : 'outlined'}
                          sx={{
                            fontWeight: 'bold',
                            '& .MuiChip-label': {
                              fontSize: '0.75rem'
                            }
                          }}
                        />
                      </Stack>
                    </TableCell>
                    <TableCell sx={{ padding: '16px', borderRight: '1px solid #f0f0f0' }}>
                      <Stack spacing={0.5}>
                        <Stack direction="row" spacing={1} alignItems="center">
                          <Box
                            sx={{
                              width: 8,
                              height: 8,
                              borderRadius: '50%',
                              backgroundColor: subscription.auto_renewal ? '#2196f3' : '#9e9e9e'
                            }}
                          />
                          <Chip
                            label={subscription.auto_renewal ? '自动续费' : '手动续费'}
                            size="small"
                            color={subscription.auto_renewal ? 'primary' : 'default'}
                            variant={subscription.auto_renewal ? 'filled' : 'outlined'}
                            sx={{
                              fontWeight: 'bold',
                              '& .MuiChip-label': {
                                fontSize: '0.75rem'
                              }
                            }}
                          />
                        </Stack>
                        {subscription.auto_renewal && (
                          <Typography variant="caption" color="primary.main" sx={{ fontWeight: 'bold', ml: 2 }}>
                            ¥{subscription.renewal_price}/月
                          </Typography>
                        )}
                      </Stack>
                    </TableCell>
                    <TableCell sx={{ padding: '16px' }}>
                      <Stack direction="row" spacing={1}>
                        <Tooltip title="查看详情">
                          <IconButton size="small" onClick={() => handleViewDetails(subscription)}>
                            <ViewIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="编辑订阅">
                          <IconButton size="small" onClick={() => handleEditSubscription(subscription)}>
                            <EditIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        {subscription.is_active && (
                          <Tooltip title="停用订阅">
                            <IconButton
                              size="small"
                              onClick={() => handleDeactivateSubscription(subscription)}
                              color="error"
                            >
                              <DeactivateIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        )}
                        {!subscription.is_active && (
                          <Tooltip title="激活订阅">
                            <IconButton
                              size="small"
                              onClick={() => handleActivateSubscription(subscription)}
                              color="success"
                            >
                              <ActivateIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        )}
                        <Tooltip title="重置配额使用量">
                          <IconButton
                            size="small"
                            onClick={() => handleResetQuotaUsage(subscription)}
                            color="warning"
                          >
                            <ResetIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </Stack>
                    </TableCell>
                </TableRow>
                );
              })
            )}
          </TableBody>
        </Table>
        <Box sx={{
          borderTop: '1px solid #e0e0e0',
          backgroundColor: '#fafafa',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          padding: '12px 16px',
          minHeight: '56px'
        }}>
          {/* 左侧：数据统计信息 */}
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Typography variant="body2" color="text.secondary">
              共 <strong>{total}</strong> 条订阅记录
            </Typography>
            <Typography variant="body2" color="text.secondary">
              当前显示第 <strong>{page * rowsPerPage + 1}</strong> - <strong>{Math.min((page + 1) * rowsPerPage, total)}</strong> 条
            </Typography>
          </Box>

          {/* 右侧：分页控件 */}
          <TablePagination
            component="div"
            count={total}
            page={page}
            onPageChange={handleChangePage}
            rowsPerPage={10}
            rowsPerPageOptions={[]}
            labelDisplayedRows={() => ``}
            sx={{
              '& .MuiTablePagination-toolbar': {
                minHeight: '40px',
                paddingLeft: 0,
                paddingRight: 0
              },
              '& .MuiTablePagination-selectLabel': {
                display: 'none'
              },
              '& .MuiTablePagination-select': {
                display: 'none'
              },
              '& .MuiTablePagination-actions': {
                marginLeft: '16px'
              },
              '& .MuiIconButton-root': {
                padding: '4px',
                '&:hover': {
                  backgroundColor: 'rgba(0, 0, 0, 0.04)'
                }
              }
            }}
          />
        </Box>
      </TableContainer>

      {/* 订阅详情对话框 */}
      <Dialog open={openDetailDialog} onClose={() => setOpenDetailDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          <Box display="flex" alignItems="center" justifyContent="space-between">
            <Typography variant="h6">订阅详情</Typography>
            {selectedSubscription && (
              <Typography variant="body2" color="text.secondary" sx={{ fontFamily: 'monospace', fontSize: '0.875rem' }}>
                订单号: {selectedSubscription.order_no || selectedSubscription.order_id}
              </Typography>
            )}
          </Box>
        </DialogTitle>
        <DialogContent>
          {selectedSubscription && (
            <Box sx={{ mt: 2 }}>
              <Stack spacing={3}>
                {/* 用户信息 */}
                <Box>
                  <Typography variant="subtitle2" gutterBottom color="text.secondary" sx={{ mb: 1.5 }}>
                    用户信息
                  </Typography>
                  <Paper variant="outlined" sx={{ p: 3 }}>
                    <Stack direction="row" spacing={4} alignItems="center" flexWrap="wrap" useFlexGap>
                      <Box sx={{ minWidth: 150 }}>
                        <Typography variant="caption" color="text.secondary" display="block">用户名</Typography>
                        <Typography variant="body2" fontWeight="bold">
                          {usersInfo[selectedSubscription.user_id]?.full_name ||
                           usersInfo[selectedSubscription.user_id]?.email ||
                           '未知用户'}
                        </Typography>
                      </Box>
                      <Box sx={{ minWidth: 180 }}>
                        <Typography variant="caption" color="text.secondary" display="block">邮箱</Typography>
                        <Typography variant="body2">
                          {usersInfo[selectedSubscription.user_id]?.email || '未知'}
                        </Typography>
                      </Box>
                      <Box sx={{ minWidth: 200 }}>
                        <Typography variant="caption" color="text.secondary" display="block">用户ID</Typography>
                        <Typography variant="body2" sx={{ fontSize: '0.75rem', fontFamily: 'monospace' }}>
                          {selectedSubscription.user_id}
                        </Typography>
                      </Box>
                      {selectedSubscription.company_id && (
                        <Box sx={{ minWidth: 200 }}>
                          <Typography variant="caption" color="text.secondary" display="block">公司ID</Typography>
                          <Typography variant="body2" sx={{ fontSize: '0.75rem', fontFamily: 'monospace' }}>
                            {selectedSubscription.company_id}
                          </Typography>
                        </Box>
                      )}
                    </Stack>
                  </Paper>
                </Box>

                {/* 产品信息 */}
                <Box>
                  <Typography variant="subtitle2" gutterBottom color="text.secondary" sx={{ mb: 1.5 }}>
                    产品信息
                  </Typography>
                  <Paper variant="outlined" sx={{ p: 3 }}>
                    <Stack direction="row" spacing={4} alignItems="center" flexWrap="wrap" useFlexGap>
                      <Box sx={{ minWidth: 150 }}>
                        <Typography variant="caption" color="text.secondary" display="block">套餐名称</Typography>
                        <Typography variant="body2" fontWeight="bold">
                          {selectedSubscription.plan_name || '未知套餐'}
                        </Typography>
                      </Box>
                      <Box sx={{ minWidth: 120 }}>
                        <Typography variant="caption" color="text.secondary" display="block">套餐类型</Typography>
                        <Typography variant="body2">
                          {selectedSubscription.plan_type}
                        </Typography>
                      </Box>
                      <Box sx={{ minWidth: 200 }}>
                        <Typography variant="caption" color="text.secondary" display="block">套餐ID</Typography>
                        <Typography variant="body2" sx={{ fontSize: '0.75rem', fontFamily: 'monospace' }}>
                          {selectedSubscription.plan_id}
                        </Typography>
                      </Box>
                      <Box sx={{ minWidth: 120 }}>
                        <Typography variant="caption" color="text.secondary" display="block">续费价格</Typography>
                        <Typography variant="body2" color="primary.main" fontWeight="bold" sx={{ fontSize: '1rem' }}>
                          ¥{selectedSubscription.renewal_price}
                        </Typography>
                      </Box>
                    </Stack>
                  </Paper>
                </Box>

                {/* 订阅状态 */}
                <Box>
                  <Typography variant="subtitle2" gutterBottom color="text.secondary" sx={{ mb: 1.5 }}>
                    订阅状态
                  </Typography>
                  <Paper variant="outlined" sx={{ p: 3 }}>
                    <Stack direction="row" spacing={4} alignItems="center" flexWrap="wrap" useFlexGap>
                      <Box sx={{ minWidth: 120 }}>
                        <Typography variant="caption" color="text.secondary" display="block" sx={{ mb: 0.5 }}>状态</Typography>
                        <Box display="flex" alignItems="center" gap={1}>
                          <Box
                            sx={{
                              width: 8,
                              height: 8,
                              borderRadius: '50%',
                              backgroundColor: selectedSubscription.is_active ? '#4caf50' : '#ff9800',
                              boxShadow: selectedSubscription.is_active
                                ? '0 0 0 2px rgba(76, 175, 80, 0.2)'
                                : '0 0 0 2px rgba(255, 152, 0, 0.2)'
                            }}
                          />
                          <Chip
                            label={selectedSubscription.is_active ? '生效中' : '已过期'}
                            size="small"
                            color={selectedSubscription.is_active ? 'success' : 'warning'}
                            variant="filled"
                            sx={{
                              fontWeight: 'bold',
                              fontSize: '0.75rem',
                              height: 24,
                              '& .MuiChip-label': {
                                px: 1.5
                              }
                            }}
                          />
                        </Box>
                      </Box>
                      <Box sx={{ minWidth: 300 }}>
                        <Typography variant="caption" color="text.secondary" display="block">有效期</Typography>
                        <Typography variant="body2" sx={{ lineHeight: 1.4 }}>
                          {selectedSubscription.start_date} 至 {selectedSubscription.end_date}
                        </Typography>
                        {selectedSubscription.days_remaining !== undefined && (
                          <Typography
                            variant="caption"
                            color={selectedSubscription.days_remaining > 7 ? 'success.main' : 'warning.main'}
                            sx={{ fontWeight: 'bold' }}
                          >
                            剩余 {selectedSubscription.days_remaining} 天
                          </Typography>
                        )}
                      </Box>
                      <Box sx={{ minWidth: 100 }}>
                        <Typography variant="caption" color="text.secondary" display="block">自动续费</Typography>
                        <Typography variant="body2" color={selectedSubscription.auto_renewal ? 'success.main' : 'text.secondary'}>
                          {selectedSubscription.auto_renewal ? '已开启' : '已关闭'}
                        </Typography>
                      </Box>
                    </Stack>
                  </Paper>
                </Box>

                {/* 支付信息 */}
                <Box>
                  <Typography variant="subtitle2" gutterBottom color="text.secondary" sx={{ mb: 1.5 }}>
                    支付信息
                  </Typography>
                  <Paper variant="outlined" sx={{ p: 3 }}>
                    <Stack direction="row" spacing={4} alignItems="center" flexWrap="wrap" useFlexGap>
                      <Box sx={{ minWidth: 120 }}>
                        <Typography variant="caption" color="text.secondary" display="block">支付方式</Typography>
                        <Typography variant="body2">
                          在线支付
                        </Typography>
                      </Box>
                      <Box sx={{ minWidth: 100 }}>
                        <Typography variant="caption" color="text.secondary" display="block">支付状态</Typography>
                        <Typography variant="body2" color="success.main" fontWeight="bold">
                          已支付
                        </Typography>
                      </Box>
                      <Box sx={{ minWidth: 120 }}>
                        <Typography variant="caption" color="text.secondary" display="block">续费价格</Typography>
                        <Typography variant="body2" color="primary.main" fontWeight="bold" sx={{ fontSize: '1rem' }}>
                          ¥{selectedSubscription.renewal_price}
                        </Typography>
                      </Box>
                      <Box sx={{ minWidth: 120 }}>
                        <Typography variant="caption" color="text.secondary" display="block">发票状态</Typography>
                        <Typography variant="body2">
                          {selectedSubscription.invoice_status === 'issued' ? '已开具' :
                           selectedSubscription.invoice_status === 'pending' ? '待开具' : '已取消'}
                        </Typography>
                      </Box>
                    </Stack>
                  </Paper>
                </Box>

                {/* 时间记录 */}
                <Box>
                  <Typography variant="subtitle2" gutterBottom color="text.secondary" sx={{ mb: 1.5 }}>
                    时间记录
                  </Typography>
                  <Paper variant="outlined" sx={{ p: 3 }}>
                    <Stack direction="row" spacing={4} alignItems="center" flexWrap="wrap" useFlexGap>
                      <Box sx={{ minWidth: 180 }}>
                        <Typography variant="caption" color="text.secondary" display="block">创建时间</Typography>
                        <Typography variant="body2">
                          {selectedSubscription.created_at}
                        </Typography>
                      </Box>
                      <Box sx={{ minWidth: 180 }}>
                        <Typography variant="caption" color="text.secondary" display="block">更新时间</Typography>
                        <Typography variant="body2">
                          {selectedSubscription.updated_at || selectedSubscription.created_at}
                        </Typography>
                      </Box>
                    </Stack>
                  </Paper>
                </Box>

                {/* 取消信息（如果已取消） */}
                {selectedSubscription.status === 'cancelled' && (
                  <Box>
                    <Typography variant="subtitle2" gutterBottom color="text.secondary" sx={{ mb: 1.5 }}>
                      取消信息
                    </Typography>
                    <Paper variant="outlined" sx={{ p: 3, bgcolor: 'error.50' }}>
                      <Stack direction="row" spacing={4} alignItems="center" flexWrap="wrap" useFlexGap>
                        <Box sx={{ minWidth: 180 }}>
                          <Typography variant="caption" color="text.secondary" display="block">取消时间</Typography>
                          <Typography variant="body2">
                            {selectedSubscription.cancelled_at}
                          </Typography>
                        </Box>
                        {selectedSubscription.cancellation_reason && (
                          <Box sx={{ minWidth: 300, flex: 1 }}>
                            <Typography variant="caption" color="text.secondary" display="block">取消原因</Typography>
                            <Typography variant="body2">
                              {selectedSubscription.cancellation_reason}
                            </Typography>
                          </Box>
                        )}
                      </Stack>
                    </Paper>
                  </Box>
                )}
              </Stack>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDetailDialog(false)}>关闭</Button>
        </DialogActions>
      </Dialog>

      {/* 编辑订阅对话框 */}
      <Dialog open={openEditDialog} onClose={() => setOpenEditDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          <Box display="flex" alignItems="center" justifyContent="space-between">
            <Typography variant="h6">编辑订阅</Typography>
            {editingSubscription && (
              <Typography variant="body2" color="text.secondary" sx={{ fontFamily: 'monospace', fontSize: '0.875rem' }}>
                订单号: {editingSubscription.order_no || editingSubscription.order_id}
              </Typography>
            )}
          </Box>
        </DialogTitle>
        <DialogContent>
          {editingSubscription && (
            <Box sx={{ mt: 2 }}>
              <Stack spacing={3}>
                {/* 用户信息 */}
                <Box>
                  <Typography variant="subtitle2" gutterBottom color="text.secondary" sx={{ mb: 1.5 }}>
                    用户信息
                  </Typography>
                  <Paper variant="outlined" sx={{ p: 3 }}>
                    <Stack direction="row" spacing={4} alignItems="center" flexWrap="wrap" useFlexGap>
                      <Box sx={{ minWidth: 150 }}>
                        <Typography variant="caption" color="text.secondary" display="block">用户名</Typography>
                        <Typography variant="body2" fontWeight="bold">
                          {usersInfo[editingSubscription.user_id]?.full_name ||
                           usersInfo[editingSubscription.user_id]?.email ||
                           '未知用户'}
                        </Typography>
                      </Box>
                      <Box sx={{ minWidth: 180 }}>
                        <Typography variant="caption" color="text.secondary" display="block">邮箱</Typography>
                        <Typography variant="body2">
                          {usersInfo[editingSubscription.user_id]?.email || '未知'}
                        </Typography>
                      </Box>
                      <Box sx={{ minWidth: 200 }}>
                        <Typography variant="caption" color="text.secondary" display="block">用户ID</Typography>
                        <Typography variant="body2" sx={{ fontSize: '0.75rem', fontFamily: 'monospace' }}>
                          {editingSubscription.user_id}
                        </Typography>
                      </Box>
                      {editingSubscription.company_id && (
                        <Box sx={{ minWidth: 200 }}>
                          <Typography variant="caption" color="text.secondary" display="block">公司ID</Typography>
                          <Typography variant="body2" sx={{ fontSize: '0.75rem', fontFamily: 'monospace' }}>
                            {editingSubscription.company_id}
                          </Typography>
                        </Box>
                      )}
                    </Stack>
                  </Paper>
                </Box>

                {/* 产品信息 */}
                <Box>
                  <Typography variant="subtitle2" gutterBottom color="text.secondary" sx={{ mb: 1.5 }}>
                    产品信息
                  </Typography>
                  <Paper variant="outlined" sx={{ p: 3 }}>
                    <Stack direction="row" spacing={4} alignItems="center" flexWrap="wrap" useFlexGap>
                      <Box sx={{ minWidth: 150 }}>
                        <Typography variant="caption" color="text.secondary" display="block">套餐名称</Typography>
                        <Typography variant="body2" fontWeight="bold">
                          {editingSubscription.plan_name || '未知套餐'}
                        </Typography>
                      </Box>
                      <Box sx={{ minWidth: 120 }}>
                        <Typography variant="caption" color="text.secondary" display="block">套餐类型</Typography>
                        <Typography variant="body2">
                          {editingSubscription.plan_type}
                        </Typography>
                      </Box>
                      <Box sx={{ minWidth: 200 }}>
                        <Typography variant="caption" color="text.secondary" display="block">套餐ID</Typography>
                        <Typography variant="body2" sx={{ fontSize: '0.75rem', fontFamily: 'monospace' }}>
                          {editingSubscription.plan_id}
                        </Typography>
                      </Box>
                      <Box sx={{ minWidth: 120 }}>
                        <Typography variant="caption" color="text.secondary" display="block">续费价格</Typography>
                        <Typography variant="body2" color="primary.main" fontWeight="bold" sx={{ fontSize: '1rem' }}>
                          ¥{editingSubscription.renewal_price}
                        </Typography>
                      </Box>
                    </Stack>
                  </Paper>
                </Box>

                {/* 当前状态 */}
                <Box>
                  <Typography variant="subtitle2" gutterBottom color="text.secondary" sx={{ mb: 1.5 }}>
                    当前状态
                  </Typography>
                  <Paper variant="outlined" sx={{ p: 3 }}>
                    <Stack direction="row" spacing={4} alignItems="center" flexWrap="wrap" useFlexGap>
                      <Box sx={{ minWidth: 120 }}>
                        <Typography variant="caption" color="text.secondary" display="block" sx={{ mb: 0.5 }}>状态</Typography>
                        <Box display="flex" alignItems="center" gap={1}>
                          <Box
                            sx={{
                              width: 8,
                              height: 8,
                              borderRadius: '50%',
                              backgroundColor: editingSubscription.is_active ? '#4caf50' : '#ff9800',
                              boxShadow: editingSubscription.is_active
                                ? '0 0 0 2px rgba(76, 175, 80, 0.2)'
                                : '0 0 0 2px rgba(255, 152, 0, 0.2)'
                            }}
                          />
                          <Chip
                            label={editingSubscription.is_active ? '生效中' : '已过期'}
                            size="small"
                            color={editingSubscription.is_active ? 'success' : 'warning'}
                            variant="filled"
                            sx={{
                              fontWeight: 'bold',
                              fontSize: '0.75rem',
                              height: 24,
                              '& .MuiChip-label': {
                                px: 1.5
                              }
                            }}
                          />
                        </Box>
                      </Box>
                      <Box sx={{ minWidth: 300 }}>
                        <Typography variant="caption" color="text.secondary" display="block">当前有效期</Typography>
                        <Typography variant="body2" sx={{ lineHeight: 1.4 }}>
                          {editingSubscription.start_date} 至 {editingSubscription.end_date}
                        </Typography>
                        {editingSubscription.days_remaining !== undefined && (
                          <Typography
                            variant="caption"
                            color={editingSubscription.days_remaining > 7 ? 'success.main' : 'warning.main'}
                            sx={{ fontWeight: 'bold' }}
                          >
                            剩余 {editingSubscription.days_remaining} 天
                          </Typography>
                        )}
                      </Box>
                      <Box sx={{ minWidth: 100 }}>
                        <Typography variant="caption" color="text.secondary" display="block">自动续费</Typography>
                        <Typography variant="body2" color={editingSubscription.auto_renewal ? 'success.main' : 'text.secondary'}>
                          {editingSubscription.auto_renewal ? '已开启' : '已关闭'}
                        </Typography>
                      </Box>
                    </Stack>
                  </Paper>
                </Box>

                {/* 编辑有效期 */}
                <Box>
                  <Typography variant="subtitle2" gutterBottom color="text.secondary" sx={{ mb: 1.5 }}>
                    修改有效期
                  </Typography>
                  <Paper variant="outlined" sx={{ p: 3, bgcolor: 'primary.50' }}>
                    <Grid container spacing={3}>
                      <Grid item xs={12} sm={6}>
                        <LocalizationProvider dateAdapter={AdapterDateFns} locale={zhCN}>
                          <DatePicker
                            label="开始日期"
                            value={new Date(editingSubscription.new_start_date)}
                            onChange={(value) => {
                              if (value) {
                                setEditingSubscription(prev => ({
                                  ...prev,
                                  new_start_date: format(value, 'yyyy-MM-dd')
                                }));
                              }
                            }}
                            slotProps={{
                              textField: {
                                size: 'small',
                                fullWidth: true,
                                sx: {
                                  '& .MuiOutlinedInput-root': {
                                    backgroundColor: 'white',
                                  }
                                }
                              }
                            }}
                          />
                        </LocalizationProvider>
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <LocalizationProvider dateAdapter={AdapterDateFns} locale={zhCN}>
                          <DatePicker
                            label="结束日期"
                            value={new Date(editingSubscription.new_end_date)}
                            onChange={(value) => {
                              if (value) {
                                setEditingSubscription(prev => ({
                                  ...prev,
                                  new_end_date: format(value, 'yyyy-MM-dd')
                                }));
                              }
                            }}
                            slotProps={{
                              textField: {
                                size: 'small',
                                fullWidth: true,
                                sx: {
                                  '& .MuiOutlinedInput-root': {
                                    backgroundColor: 'white',
                                  }
                                }
                              }
                            }}
                          />
                        </LocalizationProvider>
                      </Grid>
                      {(editingSubscription.new_end_date !== editingSubscription.end_date ||
                        editingSubscription.new_start_date !== editingSubscription.start_date) && (
                        <Grid item xs={12}>
                          <Box sx={{
                            p: 2,
                            bgcolor: 'info.50',
                            borderRadius: 1,
                            border: '1px solid',
                            borderColor: 'info.200'
                          }}>
                            <Typography variant="body2" color="info.main" fontWeight="bold" sx={{ mb: 1 }}>
                              时间变更预览
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              原有效期：{editingSubscription.start_date} 至 {editingSubscription.end_date}
                            </Typography>
                            <Typography variant="body2" color="primary.main" fontWeight="bold">
                              新有效期：{editingSubscription.new_start_date} 至 {editingSubscription.new_end_date}
                            </Typography>
                          </Box>
                        </Grid>
                      )}
                    </Grid>
                  </Paper>
                </Box>

                {/* 编辑状态和设置 */}
                <Box>
                  <Typography variant="subtitle2" gutterBottom color="text.secondary" sx={{ mb: 1.5 }}>
                    状态和设置
                  </Typography>
                  <Paper variant="outlined" sx={{ p: 3, bgcolor: 'warning.50' }}>
                    <Grid container spacing={3}>
                      <Grid item xs={12} sm={6}>
                        <FormControl fullWidth size="small">
                          <InputLabel>订阅状态</InputLabel>
                          <Select
                            value={editingSubscription.new_status}
                            label="订阅状态"
                            onChange={(e) => {
                              setEditingSubscription(prev => ({
                                ...prev,
                                new_status: e.target.value
                              }));
                            }}
                            sx={{ backgroundColor: 'white' }}
                          >
                            <MenuItem value="active">
                              <Box display="flex" alignItems="center" gap={1}>
                                <Box sx={{ width: 8, height: 8, borderRadius: '50%', bgcolor: 'success.main' }} />
                                生效中
                              </Box>
                            </MenuItem>
                            <MenuItem value="expired">
                              <Box display="flex" alignItems="center" gap={1}>
                                <Box sx={{ width: 8, height: 8, borderRadius: '50%', bgcolor: 'warning.main' }} />
                                已过期
                              </Box>
                            </MenuItem>
                            <MenuItem value="cancelled">
                              <Box display="flex" alignItems="center" gap={1}>
                                <Box sx={{ width: 8, height: 8, borderRadius: '50%', bgcolor: 'error.main' }} />
                                已取消
                              </Box>
                            </MenuItem>
                            <MenuItem value="suspended">
                              <Box display="flex" alignItems="center" gap={1}>
                                <Box sx={{ width: 8, height: 8, borderRadius: '50%', bgcolor: 'grey.500' }} />
                                已暂停
                              </Box>
                            </MenuItem>
                          </Select>
                        </FormControl>
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <FormControl fullWidth size="small">
                          <InputLabel>自动续费</InputLabel>
                          <Select
                            value={editingSubscription.new_auto_renewal}
                            label="自动续费"
                            onChange={(e) => {
                              setEditingSubscription(prev => ({
                                ...prev,
                                new_auto_renewal: e.target.value
                              }));
                            }}
                            sx={{ backgroundColor: 'white' }}
                          >
                            <MenuItem value={true}>
                              <Box display="flex" alignItems="center" gap={1}>
                                <Box sx={{ width: 8, height: 8, borderRadius: '50%', bgcolor: 'success.main' }} />
                                已开启
                              </Box>
                            </MenuItem>
                            <MenuItem value={false}>
                              <Box display="flex" alignItems="center" gap={1}>
                                <Box sx={{ width: 8, height: 8, borderRadius: '50%', bgcolor: 'grey.500' }} />
                                已关闭
                              </Box>
                            </MenuItem>
                          </Select>
                        </FormControl>
                      </Grid>
                    </Grid>
                  </Paper>
                </Box>

                {/* 编辑配额使用量 */}
                <Box>
                  <Typography variant="subtitle2" gutterBottom color="text.secondary" sx={{ mb: 1.5 }}>
                    配额使用量调整
                  </Typography>
                  <Paper variant="outlined" sx={{ p: 3, bgcolor: 'success.50' }}>
                    <Grid container spacing={3}>
                      <Grid item xs={12} sm={4}>
                        <TextField
                          fullWidth
                          label="内容请求使用量"
                          type="number"
                          size="small"
                          value={editingSubscription.new_content_used}
                          onChange={(e) => {
                            const value = Math.max(0, parseInt(e.target.value) || 0);
                            setEditingSubscription(prev => ({
                              ...prev,
                              new_content_used: value
                            }));
                          }}
                          InputProps={{
                            endAdornment: (
                              <InputAdornment position="end">
                                / {editingSubscription.content_quota || 0}
                              </InputAdornment>
                            ),
                            sx: { backgroundColor: 'white' }
                          }}
                        />
                      </Grid>
                      <Grid item xs={12} sm={4}>
                        <TextField
                          fullWidth
                          label="监控项目使用量"
                          type="number"
                          size="small"
                          value={editingSubscription.new_monitoring_used}
                          onChange={(e) => {
                            const value = Math.max(0, parseInt(e.target.value) || 0);
                            setEditingSubscription(prev => ({
                              ...prev,
                              new_monitoring_used: value
                            }));
                          }}
                          InputProps={{
                            endAdornment: (
                              <InputAdornment position="end">
                                / {editingSubscription.monitoring_quota || 0}
                              </InputAdornment>
                            ),
                            sx: { backgroundColor: 'white' }
                          }}
                        />
                      </Grid>
                      <Grid item xs={12} sm={4}>
                        <TextField
                          fullWidth
                          label="AI调用使用量"
                          type="number"
                          size="small"
                          value={editingSubscription.new_ai_used}
                          onChange={(e) => {
                            const value = Math.max(0, parseInt(e.target.value) || 0);
                            setEditingSubscription(prev => ({
                              ...prev,
                              new_ai_used: value
                            }));
                          }}
                          InputProps={{
                            endAdornment: (
                              <InputAdornment position="end">
                                / {editingSubscription.ai_quota || 0}
                              </InputAdornment>
                            ),
                            sx: { backgroundColor: 'white' }
                          }}
                        />
                      </Grid>
                    </Grid>
                  </Paper>
                </Box>

                {/* 修改原因 */}
                <Box>
                  <Typography variant="subtitle2" gutterBottom color="text.secondary" sx={{ mb: 1.5 }}>
                    修改原因
                  </Typography>
                  <TextField
                    fullWidth
                    label="请输入修改原因（必填）"
                    multiline
                    rows={3}
                    value={editingSubscription.edit_reason}
                    onChange={(e) => {
                      setEditingSubscription(prev => ({
                        ...prev,
                        edit_reason: e.target.value
                      }));
                    }}
                    placeholder="请详细说明此次修改的原因，这将记录在操作日志中..."
                    required
                  />
                </Box>


              </Stack>
            </Box>
          )}
        </DialogContent>
        <DialogActions sx={{ px: 3, py: 2, bgcolor: 'grey.50' }}>
          <Button
            onClick={() => setOpenEditDialog(false)}
            variant="outlined"
            sx={{ minWidth: 100 }}
          >
            取消
          </Button>
          <Button
            onClick={handleSaveEdit}
            variant="contained"
            startIcon={<SaveIcon />}
            disabled={!editingSubscription ||
              (!editingSubscription.edit_reason?.trim()) ||
              (editingSubscription.new_end_date === editingSubscription.end_date &&
               editingSubscription.new_start_date === editingSubscription.start_date &&
               editingSubscription.new_status === (editingSubscription.is_active ? 'active' : 'expired') &&
               editingSubscription.new_auto_renewal === editingSubscription.auto_renewal &&
               editingSubscription.new_content_used === (editingSubscription.content_used || 0) &&
               editingSubscription.new_monitoring_used === (editingSubscription.monitoring_used || 0) &&
               editingSubscription.new_ai_used === (editingSubscription.ai_used || 0))
            }
            sx={{ minWidth: 120 }}
          >
            保存修改
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default SubscriptionList;