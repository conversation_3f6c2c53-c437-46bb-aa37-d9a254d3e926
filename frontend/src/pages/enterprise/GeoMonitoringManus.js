import React, { useState, useRef, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  TextField,
  IconButton,
  Button,
  Card,
  Grid,
  Avatar,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemButton,
  ListItemIcon,
  ListItemAvatar,
  Divider,
  InputAdornment,
  Menu,
  MenuItem,
  Tooltip,
  Badge,
  Stack,
  ToggleButton,
  ToggleButtonGroup,
  Tabs,
  Tab,
  Drawer,
  AppBar,
  Toolbar,
  CircularProgress,
  LinearProgress,
  FormControl,
  Select,
  InputLabel,
} from '@mui/material';
import {
  Add,
  Search,
  MoreHoriz,
  PlayArrow,
  Stop,
  Settings,
  Code,
  Storage,
  Api,
  Timeline,
  AccountTree,
  Hub,
  Insights,
  AutoAwesome,
  ContentCopy,
  Delete,
  Edit,
  Share,
  FolderOpen,
  History,
  Refresh,
  Download,
  Upload,
  FilterList,
  Sort,
  ViewModule,
  ViewList,
  Dashboard,
  Analytics,
  TrendingUp,
  Assessment,
  BarChart,
  PieChart,
  ShowChart,
  Public,
  Speed,
  Psychology,
  SmartToy,
  DataObject,
  Terminal,
  BubbleChart,
  ScatterPlot,
  QueryStats,
  Schema,
  Dataset,
  TableChart,
  GridOn,
  Functions,
  Calculate,
  Transform,
  Merge,
  CompareArrows,
  Sync,
  CloudSync,
  SaveAlt,
  PublishRounded,
  PlayCircleOutline,
  PauseCircleOutline,
  StopCircle,
  CheckCircle,
  ErrorOutline,
  Warning,
  Info,
  HelpOutline,
  Lightbulb,
  Build,
  Extension,
  Widgets,
  Apps,
  Layers,
  DeviceHub,
  RouterOutlined,
  Cable,
  Input,
  Output,
  Link,
  LinkOff,
  Circle,
  Square,
  Hexagon,
  Category,
  LocalOffer,
  Style,
  Palette,
  Brush,
  FormatPaint,
  ColorLens,
  Gradient,
  AutoFixHigh,
  AutoGraph,
  ModelTraining,
  Webhook,
  IntegrationInstructions,
  DataThresholding,
  QueryBuilder,
  ManageSearch,
  TroubleshootOutlined,
  BugReport,
  Science,
  Biotech,
  Explore,
  RocketLaunch,
  FlashOn,
  Bolt,
  ElectricBolt,
  Memory,
  Cpu,
  DeveloperBoard,
  SettingsInputComponent,
  SettingsEthernet,
  Tune,
  FilterAlt,
  FilterAltOff,
  PivotTableChart,
  StackedBarChart,
  Leaderboard,
  TipsAndUpdates,
  Task,
  ChecklistRtl,
  AssignmentTurnedIn,
  PendingActions,
  Schedule,
  Timer,
  AccessTime,
  Update,
  Cached,
  Loop,
  Replay,
  FastForward,
  FastRewind,
  SkipNext,
  SkipPrevious,
  KeyboardArrowLeft,
  KeyboardArrowRight,
  KeyboardArrowUp,
  KeyboardArrowDown,
  ExpandMore,
  ExpandLess,
  ChevronRight,
  ChevronLeft,
  UnfoldMore,
  UnfoldLess,
  MoreVert,
  Menu as MenuIcon,
  Close,
  Minimize,
  Maximize,
  Fullscreen,
  FullscreenExit,
  OpenInNew,
  OpenInFull,
  CloseFullscreen,
  ZoomIn,
  ZoomOut,
  ZoomOutMap,
  CenterFocusStrong,
  CenterFocusWeak,
  PanTool,
  TouchApp,
  Mouse,
  NearMe,
} from '@mui/icons-material';
import { styled, alpha } from '@mui/material/styles';

// 样式组件定义
const MainContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  height: '100vh',
  backgroundColor: '#fafafa',
  overflow: 'hidden',
}));

const SidebarContainer = styled(Box)(({ theme }) => ({
  width: 320,
  backgroundColor: '#ffffff',
  borderRight: '1px solid #e5e7eb',
  display: 'flex',
  flexDirection: 'column',
  height: '100%',
}));

const SidebarHeader = styled(Box)(({ theme }) => ({
  padding: theme.spacing(2),
  borderBottom: '1px solid #e5e7eb',
  backgroundColor: '#ffffff',
}));

const ConversationList = styled(Box)(({ theme }) => ({
  flex: 1,
  overflowY: 'auto',
  '&::-webkit-scrollbar': {
    width: '6px',
  },
  '&::-webkit-scrollbar-thumb': {
    backgroundColor: '#d1d5db',
    borderRadius: '3px',
  },
}));

const ConversationItem = styled(ListItemButton)(({ theme, selected }) => ({
  padding: theme.spacing(1.5, 2),
  borderBottom: '1px solid #f3f4f6',
  backgroundColor: selected ? '#f0f9ff' : 'transparent',
  '&:hover': {
    backgroundColor: selected ? '#f0f9ff' : '#f9fafb',
  },
  borderLeft: selected ? '3px solid #3b82f6' : '3px solid transparent',
}));

const WorkspaceContainer = styled(Box)(({ theme }) => ({
  flex: 1,
  display: 'flex',
  flexDirection: 'column',
  backgroundColor: '#ffffff',
  height: '100%',
}));

const WorkspaceHeader = styled(Box)(({ theme }) => ({
  padding: theme.spacing(1.5, 3),
  borderBottom: '1px solid #e5e7eb',
  backgroundColor: '#ffffff',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  minHeight: 60,
}));

const CanvasContainer = styled(Box)(({ theme }) => ({
  flex: 1,
  position: 'relative',
  backgroundColor: '#f9fafb',
  backgroundImage: `
    linear-gradient(rgba(229, 231, 235, 0.3) 1px, transparent 1px),
    linear-gradient(90deg, rgba(229, 231, 235, 0.3) 1px, transparent 1px)
  `,
  backgroundSize: '20px 20px',
  overflow: 'auto',
}));

const NodeCard = styled(Paper)(({ theme, nodeType }) => ({
  position: 'absolute',
  padding: theme.spacing(1.5),
  borderRadius: theme.spacing(1),
  minWidth: 200,
  backgroundColor: '#ffffff',
  border: `1px solid ${nodeType === 'input' ? '#10b981' : nodeType === 'output' ? '#8b5cf6' : '#3b82f6'}`,
  boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
  cursor: 'move',
  transition: 'all 0.2s ease',
  '&:hover': {
    boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
    transform: 'translateY(-1px)',
  },
}));

const ConnectionLine = styled('svg')(({ theme }) => ({
  position: 'absolute',
  pointerEvents: 'none',
  zIndex: 0,
}));

const StyledTextField = styled(TextField)(({ theme }) => ({
  '& .MuiOutlinedInput-root': {
    backgroundColor: '#f9fafb',
    borderRadius: theme.spacing(1),
    '& fieldset': {
      borderColor: '#e5e7eb',
    },
    '&:hover fieldset': {
      borderColor: '#d1d5db',
    },
    '&.Mui-focused fieldset': {
      borderColor: '#3b82f6',
    },
  },
}));

const ToolButton = styled(IconButton)(({ theme }) => ({
  borderRadius: theme.spacing(1),
  padding: theme.spacing(1),
  '&:hover': {
    backgroundColor: '#f3f4f6',
  },
}));

const StatusChip = styled(Chip)(({ theme, status }) => ({
  fontSize: '0.75rem',
  height: 24,
  backgroundColor: 
    status === 'running' ? '#dcfce7' :
    status === 'success' ? '#d1fae5' :
    status === 'error' ? '#fee2e2' :
    status === 'warning' ? '#fef3c7' :
    '#f3f4f6',
  color:
    status === 'running' ? '#166534' :
    status === 'success' ? '#065f46' :
    status === 'error' ? '#991b1b' :
    status === 'warning' ? '#92400e' :
    '#4b5563',
  '& .MuiChip-icon': {
    fontSize: '1rem',
    color: 'inherit',
  },
}));

// 节点类型定义
const nodeTypes = [
  { id: 'input', label: '数据输入', icon: <Input />, color: '#10b981' },
  { id: 'filter', label: '数据筛选', icon: <FilterAlt />, color: '#3b82f6' },
  { id: 'transform', label: '数据转换', icon: <Transform />, color: '#f59e0b' },
  { id: 'aggregate', label: '数据聚合', icon: <Functions />, color: '#8b5cf6' },
  { id: 'model', label: 'AI模型', icon: <Psychology />, color: '#ec4899' },
  { id: 'chart', label: '图表生成', icon: <BarChart />, color: '#06b6d4' },
  { id: 'output', label: '结果输出', icon: <Output />, color: '#10b981' },
];

// 预设工作流模板
const workflowTemplates = [
  {
    id: 'traffic-analysis',
    name: '流量分析工作流',
    description: '实时流量数据采集、处理和可视化',
    icon: <TrendingUp />,
    nodes: [
      { id: 'n1', type: 'input', label: '流量数据源', x: 100, y: 200 },
      { id: 'n2', type: 'filter', label: '异常过滤', x: 300, y: 200 },
      { id: 'n3', type: 'aggregate', label: '时段聚合', x: 500, y: 200 },
      { id: 'n4', type: 'chart', label: '趋势图表', x: 700, y: 200 },
    ],
  },
  {
    id: 'seo-monitor',
    name: 'SEO监控工作流',
    description: '关键词排名跟踪和竞争分析',
    icon: <Search />,
    nodes: [
      { id: 'n1', type: 'input', label: '关键词数据', x: 100, y: 150 },
      { id: 'n2', type: 'input', label: '竞品数据', x: 100, y: 250 },
      { id: 'n3', type: 'model', label: '排名分析', x: 350, y: 200 },
      { id: 'n4', type: 'chart', label: '对比图表', x: 600, y: 200 },
    ],
  },
  {
    id: 'ai-insights',
    name: 'AI洞察工作流',
    description: '智能数据分析和预测',
    icon: <AutoAwesome />,
    nodes: [
      { id: 'n1', type: 'input', label: '历史数据', x: 100, y: 200 },
      { id: 'n2', type: 'model', label: 'AI预测模型', x: 350, y: 200 },
      { id: 'n3', type: 'output', label: '预测结果', x: 600, y: 200 },
    ],
  },
];

function GeoMonitoringManus() {
  const [selectedConversation, setSelectedConversation] = useState(0);
  const [conversations, setConversations] = useState([
    { id: 0, name: '流量分析报告', time: '刚刚', status: 'running', type: 'traffic' },
    { id: 1, name: 'SEO排名监控', time: '5分钟前', status: 'success', type: 'seo' },
    { id: 2, name: '竞品对比分析', time: '1小时前', status: 'success', type: 'competitor' },
    { id: 3, name: 'AI性能诊断', time: '昨天', status: 'warning', type: 'ai' },
    { id: 4, name: '月度数据汇总', time: '3天前', status: 'success', type: 'report' },
  ]);
  const [searchQuery, setSearchQuery] = useState('');
  const [currentTab, setCurrentTab] = useState(0);
  const [nodes, setNodes] = useState([]);
  const [connections, setConnections] = useState([]);
  const [selectedNode, setSelectedNode] = useState(null);
  const [draggingNode, setDraggingNode] = useState(null);
  const [workflowStatus, setWorkflowStatus] = useState('idle');
  const [sidePanel, setSidePanel] = useState('nodes'); // nodes, properties, history
  const canvasRef = useRef(null);

  // 创建新对话
  const handleNewConversation = () => {
    const newConv = {
      id: conversations.length,
      name: `新建工作流 ${conversations.length + 1}`,
      time: '刚刚',
      status: 'idle',
      type: 'custom',
    };
    setConversations([newConv, ...conversations]);
    setSelectedConversation(newConv.id);
    setNodes([]);
    setConnections([]);
  };

  // 加载模板
  const handleLoadTemplate = (template) => {
    setNodes(template.nodes);
    // 设置连接关系
    const newConnections = [];
    for (let i = 0; i < template.nodes.length - 1; i++) {
      newConnections.push({
        from: template.nodes[i].id,
        to: template.nodes[i + 1].id,
      });
    }
    setConnections(newConnections);
  };

  // 添加节点到画布
  const handleAddNode = (nodeType) => {
    const newNode = {
      id: `node-${Date.now()}`,
      type: nodeType.id,
      label: nodeType.label,
      icon: nodeType.icon,
      color: nodeType.color,
      x: 100 + nodes.length * 50,
      y: 200,
      config: {},
    };
    setNodes([...nodes, newNode]);
  };

  // 拖拽节点
  const handleNodeDrag = (nodeId, newX, newY) => {
    setNodes(nodes.map(node => 
      node.id === nodeId ? { ...node, x: newX, y: newY } : node
    ));
  };

  // 运行工作流
  const handleRunWorkflow = () => {
    setWorkflowStatus('running');
    // 更新对话状态
    setConversations(conversations.map(conv =>
      conv.id === selectedConversation
        ? { ...conv, status: 'running', time: '运行中' }
        : conv
    ));

    // 模拟运行完成
    setTimeout(() => {
      setWorkflowStatus('success');
      setConversations(conversations.map(conv =>
        conv.id === selectedConversation
          ? { ...conv, status: 'success', time: '刚刚' }
          : conv
      ));
    }, 3000);
  };

  // 渲染连接线
  const renderConnections = () => {
    return connections.map((conn, index) => {
      const fromNode = nodes.find(n => n.id === conn.from);
      const toNode = nodes.find(n => n.id === conn.to);
      if (!fromNode || !toNode) return null;

      const path = `M ${fromNode.x + 100} ${fromNode.y + 30} 
                    C ${fromNode.x + 200} ${fromNode.y + 30}, 
                      ${toNode.x - 50} ${toNode.y + 30}, 
                      ${toNode.x} ${toNode.y + 30}`;

      return (
        <ConnectionLine
          key={index}
          width="100%"
          height="100%"
          style={{ top: 0, left: 0 }}
        >
          <path
            d={path}
            stroke="#3b82f6"
            strokeWidth="2"
            fill="none"
            strokeDasharray={workflowStatus === 'running' ? '5,5' : '0'}
          >
            {workflowStatus === 'running' && (
              <animate
                attributeName="stroke-dashoffset"
                values="10;0"
                dur="0.5s"
                repeatCount="indefinite"
              />
            )}
          </path>
        </ConnectionLine>
      );
    });
  };

  return (
    <MainContainer>
      {/* 左侧对话列表 */}
      <SidebarContainer>
        <SidebarHeader>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
            <Typography variant="h6" fontWeight="600">
              监控工作流
            </Typography>
            <Button
              variant="contained"
              size="small"
              startIcon={<Add />}
              onClick={handleNewConversation}
              sx={{
                textTransform: 'none',
                borderRadius: 2,
                backgroundColor: '#3b82f6',
                '&:hover': {
                  backgroundColor: '#2563eb',
                },
              }}
            >
              新建
            </Button>
          </Box>
          <StyledTextField
            fullWidth
            size="small"
            placeholder="搜索工作流..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Search fontSize="small" />
                </InputAdornment>
              ),
            }}
          />
        </SidebarHeader>

        <ConversationList>
          {conversations.map((conv) => (
            <ConversationItem
              key={conv.id}
              selected={selectedConversation === conv.id}
              onClick={() => setSelectedConversation(conv.id)}
            >
              <ListItemAvatar>
                <Avatar sx={{ 
                  width: 36, 
                  height: 36,
                  backgroundColor: conv.status === 'running' ? '#3b82f6' :
                                 conv.status === 'success' ? '#10b981' :
                                 conv.status === 'warning' ? '#f59e0b' :
                                 '#6b7280',
                }}>
                  {conv.type === 'traffic' ? <TrendingUp /> :
                   conv.type === 'seo' ? <Search /> :
                   conv.type === 'competitor' ? <CompareArrows /> :
                   conv.type === 'ai' ? <Psychology /> :
                   <Dashboard />}
                </Avatar>
              </ListItemAvatar>
              <ListItemText
                primary={
                  <Typography variant="body2" fontWeight="500">
                    {conv.name}
                  </Typography>
                }
                secondary={
                  <Box display="flex" alignItems="center" gap={1}>
                    <Typography variant="caption" color="text.secondary">
                      {conv.time}
                    </Typography>
                    {conv.status !== 'idle' && (
                      <StatusChip
                        size="small"
                        status={conv.status}
                        icon={
                          conv.status === 'running' ? <Loop /> :
                          conv.status === 'success' ? <CheckCircle /> :
                          conv.status === 'warning' ? <Warning /> :
                          <Info />
                        }
                        label={
                          conv.status === 'running' ? '运行中' :
                          conv.status === 'success' ? '成功' :
                          conv.status === 'warning' ? '警告' :
                          '待运行'
                        }
                      />
                    )}
                  </Box>
                }
              />
              <IconButton size="small" onClick={(e) => e.stopPropagation()}>
                <MoreHoriz fontSize="small" />
              </IconButton>
            </ConversationItem>
          ))}
        </ConversationList>

        {/* 模板快速访问 */}
        <Box sx={{ p: 2, borderTop: '1px solid #e5e7eb' }}>
          <Typography variant="caption" color="text.secondary" gutterBottom>
            快速开始
          </Typography>
          <Stack spacing={1} mt={1}>
            {workflowTemplates.map((template) => (
              <Button
                key={template.id}
                size="small"
                startIcon={template.icon}
                onClick={() => handleLoadTemplate(template)}
                sx={{
                  justifyContent: 'flex-start',
                  textTransform: 'none',
                  color: '#4b5563',
                  '&:hover': {
                    backgroundColor: '#f3f4f6',
                  },
                }}
              >
                {template.name}
              </Button>
            ))}
          </Stack>
        </Box>
      </SidebarContainer>

      {/* 右侧工作区 */}
      <WorkspaceContainer>
        <WorkspaceHeader>
          <Box display="flex" alignItems="center" gap={2}>
            <Typography variant="h6" fontWeight="600">
              {conversations.find(c => c.id === selectedConversation)?.name || '新建工作流'}
            </Typography>
            {workflowStatus === 'running' && (
              <CircularProgress size={20} />
            )}
          </Box>

          <Box display="flex" alignItems="center" gap={1}>
            <ToggleButtonGroup
              value={currentTab}
              exclusive
              onChange={(e, newTab) => setCurrentTab(newTab)}
              size="small"
            >
              <ToggleButton value={0}>
                <AccountTree fontSize="small" sx={{ mr: 0.5 }} />
                画布
              </ToggleButton>
              <ToggleButton value={1}>
                <Code fontSize="small" sx={{ mr: 0.5 }} />
                代码
              </ToggleButton>
              <ToggleButton value={2}>
                <Timeline fontSize="small" sx={{ mr: 0.5 }} />
                监控
              </ToggleButton>
            </ToggleButtonGroup>

            <Divider orientation="vertical" flexItem />

            <ToolButton onClick={handleRunWorkflow} color="primary">
              {workflowStatus === 'running' ? <Stop /> : <PlayArrow />}
            </ToolButton>
            <ToolButton>
              <SaveAlt />
            </ToolButton>
            <ToolButton>
              <Share />
            </ToolButton>
            <ToolButton>
              <Settings />
            </ToolButton>
          </Box>
        </WorkspaceHeader>

        {/* 主画布区域 */}
        {currentTab === 0 && (
          <Box sx={{ display: 'flex', flex: 1, position: 'relative' }}>
            {/* 节点工具栏 */}
            <Box sx={{ 
              width: 240,
              backgroundColor: '#ffffff',
              borderRight: '1px solid #e5e7eb',
              p: 2,
              overflowY: 'auto',
            }}>
              <Typography variant="subtitle2" fontWeight="600" gutterBottom>
                节点组件
              </Typography>
              <Stack spacing={1} mt={2}>
                {nodeTypes.map((nodeType) => (
                  <Button
                    key={nodeType.id}
                    fullWidth
                    variant="outlined"
                    startIcon={nodeType.icon}
                    onClick={() => handleAddNode(nodeType)}
                    sx={{
                      justifyContent: 'flex-start',
                      textTransform: 'none',
                      borderColor: '#e5e7eb',
                      color: '#4b5563',
                      '&:hover': {
                        borderColor: nodeType.color,
                        backgroundColor: alpha(nodeType.color, 0.05),
                      },
                    }}
                  >
                    {nodeType.label}
                  </Button>
                ))}
              </Stack>

              <Divider sx={{ my: 2 }} />

              <Typography variant="subtitle2" fontWeight="600" gutterBottom>
                节点属性
              </Typography>
              {selectedNode ? (
                <Box mt={2}>
                  <TextField
                    fullWidth
                    size="small"
                    label="节点名称"
                    value={selectedNode.label}
                    sx={{ mb: 2 }}
                  />
                  <FormControl fullWidth size="small" sx={{ mb: 2 }}>
                    <InputLabel>数据源</InputLabel>
                    <Select label="数据源">
                      <MenuItem value="api">API接口</MenuItem>
                      <MenuItem value="database">数据库</MenuItem>
                      <MenuItem value="file">文件上传</MenuItem>
                    </Select>
                  </FormControl>
                  <Button 
                    fullWidth 
                    variant="outlined" 
                    color="error"
                    startIcon={<Delete />}
                    size="small"
                  >
                    删除节点
                  </Button>
                </Box>
              ) : (
                <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
                  选择一个节点查看属性
                </Typography>
              )}
            </Box>

            {/* 画布 */}
            <CanvasContainer ref={canvasRef}>
              {renderConnections()}
              {nodes.map((node) => (
                <NodeCard
                  key={node.id}
                  nodeType={node.type}
                  style={{ left: node.x, top: node.y }}
                  onClick={() => setSelectedNode(node)}
                  onMouseDown={(e) => {
                    setDraggingNode(node.id);
                    e.preventDefault();
                  }}
                >
                  <Box display="flex" alignItems="center" gap={1}>
                    <Avatar sx={{ 
                      width: 28, 
                      height: 28,
                      backgroundColor: alpha(node.color, 0.1),
                      color: node.color,
                    }}>
                      {node.icon}
                    </Avatar>
                    <Typography variant="body2" fontWeight="500">
                      {node.label}
                    </Typography>
                  </Box>
                  <Box sx={{ 
                    position: 'absolute',
                    top: '50%',
                    left: -8,
                    transform: 'translateY(-50%)',
                    width: 16,
                    height: 16,
                    borderRadius: '50%',
                    backgroundColor: '#ffffff',
                    border: '2px solid ' + node.color,
                  }} />
                  <Box sx={{ 
                    position: 'absolute',
                    top: '50%',
                    right: -8,
                    transform: 'translateY(-50%)',
                    width: 16,
                    height: 16,
                    borderRadius: '50%',
                    backgroundColor: '#ffffff',
                    border: '2px solid ' + node.color,
                  }} />
                </NodeCard>
              ))}

              {/* 空状态 */}
              {nodes.length === 0 && (
                <Box sx={{
                  position: 'absolute',
                  top: '50%',
                  left: '50%',
                  transform: 'translate(-50%, -50%)',
                  textAlign: 'center',
                }}>
                  <BubbleChart sx={{ fontSize: 64, color: '#d1d5db', mb: 2 }} />
                  <Typography variant="h6" color="text.secondary" gutterBottom>
                    开始构建您的监控工作流
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                    从左侧拖拽节点到画布，或选择一个模板快速开始
                  </Typography>
                  <Stack direction="row" spacing={2} justifyContent="center">
                    {workflowTemplates.slice(0, 2).map((template) => (
                      <Button
                        key={template.id}
                        variant="outlined"
                        startIcon={template.icon}
                        onClick={() => handleLoadTemplate(template)}
                      >
                        {template.name}
                      </Button>
                    ))}
                  </Stack>
                </Box>
              )}
            </CanvasContainer>
          </Box>
        )}

        {/* 代码视图 */}
        {currentTab === 1 && (
          <Box sx={{ flex: 1, p: 3, backgroundColor: '#1e1e1e', color: '#ffffff' }}>
            <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
              {JSON.stringify({ nodes, connections }, null, 2)}
            </Typography>
          </Box>
        )}

        {/* 监控视图 */}
        {currentTab === 2 && (
          <Box sx={{ flex: 1, p: 3 }}>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Card sx={{ p: 2 }}>
                  <Typography variant="h6" gutterBottom>
                    实时指标
                  </Typography>
                  <Box sx={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                    <Typography color="text.secondary">监控图表区域</Typography>
                  </Box>
                </Card>
              </Grid>
              <Grid item xs={12} md={6}>
                <Card sx={{ p: 2 }}>
                  <Typography variant="h6" gutterBottom>
                    运行日志
                  </Typography>
                  <Box sx={{ height: 300, overflowY: 'auto', backgroundColor: '#f9fafb', p: 2, borderRadius: 1 }}>
                    <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
                      [2024-01-10 10:30:00] 工作流开始执行...
                      <br />[2024-01-10 10:30:01] 数据输入节点: 成功加载数据
                      <br />[2024-01-10 10:30:02] 数据筛选节点: 应用过滤条件
                      <br />[2024-01-10 10:30:03] 数据聚合节点: 完成聚合计算
                      <br />[2024-01-10 10:30:04] 图表生成节点: 生成可视化
                      <br />[2024-01-10 10:30:05] 工作流执行完成
                    </Typography>
                  </Box>
                </Card>
              </Grid>
            </Grid>
          </Box>
        )}
      </WorkspaceContainer>
    </MainContainer>
  );
}

export default GeoMonitoringManus;