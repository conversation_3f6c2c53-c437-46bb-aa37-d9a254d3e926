import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Typography,
  Paper,
  IconButton,
  TextField,
  Button,
  Divider,
  Grid,
  Card,
  CardContent,
  Tabs,
  Tab,
  Chip,
  Alert,
  Snackbar,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Tooltip
} from '@mui/material';
import {
  Send,
  AutoAwesome,
  Settings,
  SmartToy,
  ContentCopy,
  Download,
  Save,
  Refresh,
  Psychology,
  LibraryBooks,
  Chat,
  Add,
  Tune,
  Storage
} from '@mui/icons-material';

import ConversationManager from '../../components/conversation/ConversationManager';
import KnowledgeManager from '../../components/knowledge/KnowledgeManager';
import ApiService from '../../services/api';

function AIContentCreationV2() {
  // 核心状态
  const [currentConversationId, setCurrentConversationId] = useState(null);
  const [inputMessage, setInputMessage] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedContent, setGeneratedContent] = useState('');
  
  // 界面状态
  const [activeTab, setActiveTab] = useState(0);
  const [showSettings, setShowSettings] = useState(false);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });
  
  // 知识库状态
  const [selectedKnowledgeBases, setSelectedKnowledgeBases] = useState([]);
  const [knowledgeEnabled, setKnowledgeEnabled] = useState(false);
  
  // 模板状态
  const [selectedTemplate, setSelectedTemplate] = useState('');
  const [templateParameters, setTemplateParameters] = useState({});
  
  // AI设置
  const [aiSettings, setAiSettings] = useState({
    model: 'doubao',
    temperature: 0.7,
    maxTokens: 2000,
    tone: 'professional',
    length: 'medium'
  });
  
  const messagesEndRef = useRef(null);

  // 处理对话选择
  const handleConversationSelect = (conversationId) => {
    setCurrentConversationId(conversationId);
    setGeneratedContent('');
    
    if (conversationId) {
      loadConversationHistory(conversationId);
    }
  };

  // 加载对话历史
  const loadConversationHistory = async (conversationId) => {
    try {
      const response = await ApiService.get(`/api/v1/conversations/${conversationId}`);
      const conversation = response.data;
      
      // 设置知识库
      if (conversation.knowledge_bases) {
        setSelectedKnowledgeBases(conversation.knowledge_bases);
        setKnowledgeEnabled(conversation.knowledge_bases.length > 0);
      }
      
      // 设置模板参数
      if (conversation.template_parameters) {
        setTemplateParameters(conversation.template_parameters);
      }
      
      // 显示最后的AI回复作为生成内容
      const lastAssistantMessage = conversation.messages
        .filter(msg => msg.message_type === 'ASSISTANT')
        .pop();
      
      if (lastAssistantMessage) {
        setGeneratedContent(lastAssistantMessage.content);
      }
      
    } catch (error) {
      console.error('加载对话历史失败:', error);
      setSnackbar({ 
        open: true, 
        message: '加载对话历史失败', 
        severity: 'error' 
      });
    }
  };

  // 创建新对话
  const handleNewConversation = async () => {
    try {
      const response = await ApiService.post('/api/v1/conversations', {
        title: `新对话 ${new Date().toLocaleString()}`,
        template_id: selectedTemplate || null,
        template_parameters: templateParameters,
        knowledge_bases: knowledgeEnabled ? selectedKnowledgeBases : []
      });
      
      const newConversation = response.data;
      setCurrentConversationId(newConversation.id);
      setGeneratedContent('');
      
      setSnackbar({ 
        open: true, 
        message: '新对话创建成功', 
        severity: 'success' 
      });
      
    } catch (error) {
      console.error('创建对话失败:', error);
      setSnackbar({ 
        open: true, 
        message: '创建对话失败', 
        severity: 'error' 
      });
    }
  };

  // 发送消息并生成内容
  const handleSendMessage = async () => {
    if (!inputMessage.trim() || isGenerating) return;
    
    // 如果没有当前对话，先创建一个
    let conversationId = currentConversationId;
    if (!conversationId) {
      try {
        const response = await ApiService.post('/api/v1/conversations', {
          title: inputMessage.slice(0, 50) + '...',
          template_id: selectedTemplate || null,
          template_parameters: templateParameters,
          knowledge_bases: knowledgeEnabled ? selectedKnowledgeBases : []
        });
        conversationId = response.data.id;
        setCurrentConversationId(conversationId);
      } catch (error) {
        setSnackbar({ 
          open: true, 
          message: '创建对话失败', 
          severity: 'error' 
        });
        return;
      }
    }
    
    setIsGenerating(true);
    setGeneratedContent('');
    
    try {
      // 使用V2版本的API
      const response = await fetch('/api/v1/ai/content/generate/v2', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('ai_seo_auth_token')}`
        },
        body: JSON.stringify({
          content_type: 'article',
          topic: inputMessage,
          keywords: inputMessage.split(/\s+/).filter(word => word.length > 0).slice(0, 5).concat(['内容', 'AI']).slice(0, 3),
          conversation_id: conversationId,
          template_id: selectedTemplate || null,
          template_parameters: templateParameters,
          knowledge_bases: knowledgeEnabled ? selectedKnowledgeBases : [],
          is_first_request: !currentConversationId,
          ai_model: aiSettings.model,
          tone: aiSettings.tone,
          length: aiSettings.length
        })
      });
      
      if (!response.ok) {
        throw new Error('API调用失败');
      }
      
      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = '';
      
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        
        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';
        
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6));
              
              if (data.type === 'content') {
                setGeneratedContent(prev => prev + data.content);
              } else if (data.type === 'final') {
                setGeneratedContent(data.generated_content || '');
              } else if (data.type === 'error') {
                throw new Error(data.error || '生成失败');
              }
            } catch (e) {
              console.error('解析SSE数据失败:', e);
            }
          }
        }
      }
      
      setInputMessage('');
      
    } catch (error) {
      console.error('生成内容失败:', error);
      setSnackbar({ 
        open: true, 
        message: '生成内容失败，请稍后重试', 
        severity: 'error' 
      });
    } finally {
      setIsGenerating(false);
    }
  };

  // 复制内容
  const handleCopyContent = () => {
    navigator.clipboard.writeText(generatedContent);
    setSnackbar({ 
      open: true, 
      message: '内容已复制到剪贴板', 
      severity: 'success' 
    });
  };

  // 保存内容
  const handleSaveContent = () => {
    const blob = new Blob([generatedContent], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `AI生成内容_${new Date().toISOString().slice(0, 10)}.txt`;
    a.click();
    URL.revokeObjectURL(url);
    
    setSnackbar({ 
      open: true, 
      message: '内容已保存', 
      severity: 'success' 
    });
  };

  return (
    <Box sx={{ height: '100vh', display: 'flex', flexDirection: 'column', p: 2 }}>
      {/* 头部 */}
      <Paper elevation={1} sx={{ p: 2, mb: 2 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h5" sx={{ fontWeight: 600, display: 'flex', alignItems: 'center', gap: 1 }}>
            <Psychology color="primary" />
            AI智能写作助手 V2
          </Typography>
          
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Button
              startIcon={<Add />}
              variant="outlined"
              onClick={handleNewConversation}
              size="small"
            >
              新对话
            </Button>
            <IconButton onClick={() => setShowSettings(true)}>
              <Settings />
            </IconButton>
          </Box>
        </Box>
      </Paper>

      {/* 主体内容 */}
      <Box sx={{ flex: 1, display: 'flex', gap: 2, overflow: 'hidden' }}>
        {/* 左侧：对话管理 */}
        <ConversationManager
          onConversationSelect={handleConversationSelect}
          currentConversationId={currentConversationId}
          onNewConversation={(conversation) => {
            setCurrentConversationId(conversation.id);
          }}
        />

        {/* 中间：写作区域 */}
        <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column', gap: 2 }}>
          {/* 输入区域 */}
          <Paper elevation={1} sx={{ p: 2 }}>
            <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
              <TextField
                fullWidth
                multiline
                rows={3}
                placeholder="请描述您想要生成的内容..."
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                onKeyPress={(e) => {
                  if (e.key === 'Enter' && e.ctrlKey) {
                    handleSendMessage();
                  }
                }}
              />
              <Button
                variant="contained"
                onClick={handleSendMessage}
                disabled={!inputMessage.trim() || isGenerating}
                sx={{ minWidth: 120, height: 'fit-content' }}
                startIcon={isGenerating ? <CircularProgress size={16} /> : <Send />}
              >
                {isGenerating ? '生成中' : '生成'}
              </Button>
            </Box>
            
            {/* 快捷设置 */}
            <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
              <FormControlLabel
                control={
                  <Switch
                    checked={knowledgeEnabled}
                    onChange={(e) => setKnowledgeEnabled(e.target.checked)}
                  />
                }
                label="启用知识库"
              />
              
              {knowledgeEnabled && (
                <Chip
                  icon={<LibraryBooks />}
                  label={`${selectedKnowledgeBases.length}个知识库`}
                  variant="outlined"
                  size="small"
                />
              )}
              
              <Chip
                icon={<SmartToy />}
                label={aiSettings.model}
                variant="outlined"
                size="small"
              />
            </Box>
          </Paper>

          {/* 生成内容区域 */}
          <Paper elevation={1} sx={{ flex: 1, p: 2, display: 'flex', flexDirection: 'column' }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">生成内容</Typography>
              
              {generatedContent && (
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Tooltip title="复制内容">
                    <IconButton onClick={handleCopyContent} size="small">
                      <ContentCopy />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="保存内容">
                    <IconButton onClick={handleSaveContent} size="small">
                      <Download />
                    </IconButton>
                  </Tooltip>
                </Box>
              )}
            </Box>
            
            <Box sx={{ 
              flex: 1, 
              border: 1, 
              borderColor: 'divider', 
              borderRadius: 1, 
              p: 2,
              overflow: 'auto',
              bgcolor: 'background.paper'
            }}>
              {isGenerating ? (
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <CircularProgress size={20} />
                  <Typography color="text.secondary">AI正在生成内容...</Typography>
                </Box>
              ) : generatedContent ? (
                <Typography sx={{ whiteSpace: 'pre-wrap', lineHeight: 1.8 }}>
                  {generatedContent}
                </Typography>
              ) : (
                <Typography color="text.secondary" sx={{ fontStyle: 'italic' }}>
                  生成的内容将在这里显示...
                </Typography>
              )}
            </Box>
          </Paper>
        </Box>

        {/* 右侧：知识库管理 */}
        <Box sx={{ width: 350 }}>
          <Paper elevation={1} sx={{ height: '100%', p: 2 }}>
            <Tabs value={activeTab} onChange={(e, v) => setActiveTab(v)} sx={{ mb: 2 }}>
              <Tab label="知识库" icon={<LibraryBooks />} />
              <Tab label="设置" icon={<Tune />} />
            </Tabs>
            
            {activeTab === 0 && (
              <KnowledgeManager
                selectedKnowledgeBases={selectedKnowledgeBases}
                onSelectionChange={setSelectedKnowledgeBases}
                mode="selection"
              />
            )}
            
            {activeTab === 1 && (
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <FormControl fullWidth size="small">
                  <InputLabel>AI模型</InputLabel>
                  <Select
                    value={aiSettings.model}
                    onChange={(e) => setAiSettings(prev => ({ ...prev, model: e.target.value }))}
                  >
                    <MenuItem value="doubao">豆包</MenuItem>
                    <MenuItem value="gpt-4">GPT-4</MenuItem>
                    <MenuItem value="claude">Claude</MenuItem>
                  </Select>
                </FormControl>
                
                <FormControl fullWidth size="small">
                  <InputLabel>语调风格</InputLabel>
                  <Select
                    value={aiSettings.tone}
                    onChange={(e) => setAiSettings(prev => ({ ...prev, tone: e.target.value }))}
                  >
                    <MenuItem value="professional">专业</MenuItem>
                    <MenuItem value="casual">轻松</MenuItem>
                    <MenuItem value="friendly">友好</MenuItem>
                    <MenuItem value="formal">正式</MenuItem>
                  </Select>
                </FormControl>
                
                <FormControl fullWidth size="small">
                  <InputLabel>内容长度</InputLabel>
                  <Select
                    value={aiSettings.length}
                    onChange={(e) => setAiSettings(prev => ({ ...prev, length: e.target.value }))}
                  >
                    <MenuItem value="short">简短</MenuItem>
                    <MenuItem value="medium">中等</MenuItem>
                    <MenuItem value="long">详细</MenuItem>
                  </Select>
                </FormControl>
              </Box>
            )}
          </Paper>
        </Box>
      </Box>

      {/* 消息提示 */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={3000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert severity={snackbar.severity} onClose={() => setSnackbar({ ...snackbar, open: false })}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
}

export default AIContentCreationV2;
