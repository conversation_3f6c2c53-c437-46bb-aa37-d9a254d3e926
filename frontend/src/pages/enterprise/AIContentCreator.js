import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Typography,
  Paper,
  IconButton,
  TextField,
  Button,
  Avatar,
  Divider,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemButton,
  Chip,
  Tooltip,
  CircularProgress,
  Alert,
  Snackbar,
  Menu,
  Tab,
  Tabs,
  Autocomplete,
  ToggleButton,
  ToggleButtonGroup,
  Fade,
  Collapse,
  Card,
  CardContent,
} from '@mui/material';
import {
  Send,
  AutoAwesome,
  History,
  ContentCopy,
  Download,
  Save,
  Add,
  Article,
  Refresh,
  Delete,
  Edit,
  Undo,
  Redo,
  FormatBold,
  FormatItalic,
  FormatUnderlined,
  FormatListBulleted,
  FormatListNumbered,
  Code,
  Image,
  Link,
  Settings,
  Folder,
  FolderOpen,
  Psychology,
  Lightbulb,
  School,
  Business,
  Campaign,
  TrendingUp,
  AttachFile,
  Share,
  CloudDownload,
  SaveAlt,
  RestartAlt,
  <PERSON><PERSON><PERSON><PERSON><PERSON>Outline,
  SmartToy,
  Publish,
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';
import ApiService from '../../services/api';

const StyledContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  height: 'calc(100vh - 120px)',
  gap: 0,
  background: theme.palette.background.default,
  position: 'relative',
  overflow: 'hidden',
}));

const ChatPanel = styled(Paper)(({ theme }) => ({
  flex: '0 0 520px',
  display: 'flex',
  flexDirection: 'column',
  background: `linear-gradient(135deg, ${theme.palette.background.paper} 0%, ${theme.palette.background.default} 100%)`,
  borderRadius: 0,
  borderRight: `1px solid ${theme.palette.divider}`,
  overflow: 'hidden',
  boxShadow: '2px 0 8px rgba(0,0,0,0.05)',
}));

const ContentPanel = styled(Paper)(({ theme }) => ({
  flex: 1,
  display: 'flex',
  flexDirection: 'column',
  background: theme.palette.background.paper,
  borderRadius: 0,
  overflow: 'hidden',
  position: 'relative',
}));

const ChatHeader = styled(Box)(({ theme }) => ({
  padding: theme.spacing(2),
  background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
  color: 'white',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
}));

const ChatMessages = styled(Box)(({ theme }) => ({
  flex: 1,
  overflowY: 'auto',
  padding: theme.spacing(2),
  display: 'flex',
  flexDirection: 'column',
  gap: theme.spacing(2),
  '&::-webkit-scrollbar': {
    width: '6px',
  },
  '&::-webkit-scrollbar-track': {
    background: 'transparent',
  },
  '&::-webkit-scrollbar-thumb': {
    background: theme.palette.divider,
    borderRadius: '3px',
  },
}));

const MessageBubble = styled(Box)(({ theme, isUser }) => ({
  maxWidth: '85%',
  alignSelf: isUser ? 'flex-end' : 'flex-start',
  padding: theme.spacing(1.5),
  borderRadius: isUser ? '18px 18px 4px 18px' : '18px 18px 18px 4px',
  background: isUser 
    ? `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`
    : theme.palette.grey[100],
  color: isUser ? 'white' : theme.palette.text.primary,
  boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
  animation: 'fadeInUp 0.3s ease',
  '@keyframes fadeInUp': {
    from: {
      opacity: 0,
      transform: 'translateY(10px)',
    },
    to: {
      opacity: 1,
      transform: 'translateY(0)',
    },
  },
}));

const ContentHeader = styled(Box)(({ theme }) => ({
  padding: theme.spacing(2, 3),
  borderBottom: `1px solid ${theme.palette.divider}`,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  background: theme.palette.background.default,
}));

const ContentBody = styled(Box)(({ theme }) => ({
  flex: 1,
  padding: theme.spacing(3),
  overflowY: 'auto',
  background: 'white',
  '&::-webkit-scrollbar': {
    width: '8px',
  },
  '&::-webkit-scrollbar-track': {
    background: '#f1f1f1',
  },
  '&::-webkit-scrollbar-thumb': {
    background: theme.palette.divider,
    borderRadius: '4px',
  },
}));

const StyledArticle = styled(Box)(({ theme }) => ({
  maxWidth: '800px',
  margin: '0 auto',
  padding: theme.spacing(3),
  fontFamily: '"Segoe UI", Tahoma, Geneva, Verdana, sans-serif',
  lineHeight: 1.8,
  color: theme.palette.text.primary,
  backgroundColor: theme.palette.background.paper,
  borderRadius: theme.spacing(1),
  boxShadow: '0 2px 8px rgba(0,0,0,0.1)',

  // Markdown样式
  '& .markdown-content': {
    width: '100%',
  },

  // 段落间距
  '& .paragraph-spacing': {
    height: theme.spacing(2),
  },

  // 标题样式
  '& .markdown-h1, & h1': {
    fontSize: '2.5rem',
    fontWeight: 700,
    marginBottom: theme.spacing(3),
    marginTop: theme.spacing(2),
    color: theme.palette.primary.dark,
    borderBottom: `3px solid ${theme.palette.primary.main}`,
    paddingBottom: theme.spacing(1),
    lineHeight: 1.2,
  },
  '& .markdown-h2, & h2': {
    fontSize: '2rem',
    fontWeight: 600,
    marginTop: theme.spacing(4),
    marginBottom: theme.spacing(2),
    color: theme.palette.primary.main,
    lineHeight: 1.3,
  },
  '& .markdown-h3, & h3': {
    fontSize: '1.5rem',
    fontWeight: 600,
    marginTop: theme.spacing(3),
    marginBottom: theme.spacing(1.5),
    color: theme.palette.text.primary,
    lineHeight: 1.4,
  },
  '& .markdown-h4, & h4': {
    fontSize: '1.25rem',
    fontWeight: 600,
    marginTop: theme.spacing(2.5),
    marginBottom: theme.spacing(1),
    color: theme.palette.text.primary,
  },
  '& .markdown-h5, & h5': {
    fontSize: '1.1rem',
    fontWeight: 600,
    marginTop: theme.spacing(2),
    marginBottom: theme.spacing(1),
    color: theme.palette.text.primary,
  },
  '& .markdown-h6, & h6': {
    fontSize: '1rem',
    fontWeight: 600,
    marginTop: theme.spacing(2),
    marginBottom: theme.spacing(1),
    color: theme.palette.text.secondary,
  },

  // 段落样式
  '& .markdown-paragraph, & p': {
    fontSize: '1.1rem',
    marginBottom: theme.spacing(2),
    textAlign: 'justify',
    lineHeight: 1.8,
    textIndent: '2em', // 段落首行缩进
  },

  // 列表样式
  '& .markdown-list, & ul': {
    marginBottom: theme.spacing(2),
    marginTop: theme.spacing(1),
    paddingLeft: theme.spacing(4),
    listStyleType: 'disc',
  },
  '& .markdown-ordered-list, & ol': {
    marginBottom: theme.spacing(2),
    marginTop: theme.spacing(1),
    paddingLeft: theme.spacing(4),
    listStyleType: 'decimal',
  },
  '& .markdown-list-item, & .markdown-ordered-item, & li': {
    marginBottom: theme.spacing(0.5),
    fontSize: '1.1rem',
    lineHeight: 1.6,
  },

  // 引用样式
  '& .markdown-quote, & blockquote': {
    borderLeft: `4px solid ${theme.palette.primary.main}`,
    paddingLeft: theme.spacing(2),
    paddingRight: theme.spacing(2),
    paddingTop: theme.spacing(1),
    paddingBottom: theme.spacing(1),
    marginLeft: 0,
    marginRight: 0,
    marginTop: theme.spacing(2),
    marginBottom: theme.spacing(2),
    fontStyle: 'italic',
    color: theme.palette.text.secondary,
    background: theme.palette.grey[50],
    borderRadius: '4px',
    fontSize: '1.05rem',
  },

  // 代码样式
  '& .markdown-inline-code, & code': {
    background: theme.palette.grey[100],
    color: theme.palette.error.dark,
    padding: '2px 6px',
    borderRadius: '4px',
    fontFamily: '"Consolas", "Monaco", "Courier New", monospace',
    fontSize: '0.9rem',
    border: `1px solid ${theme.palette.grey[300]}`,
  },
  '& .markdown-code-block, & pre': {
    background: theme.palette.grey[900],
    color: 'white',
    padding: theme.spacing(2),
    borderRadius: '8px',
    overflowX: 'auto',
    marginTop: theme.spacing(2),
    marginBottom: theme.spacing(2),
    fontSize: '0.9rem',
    lineHeight: 1.5,
    '& code': {
      background: 'transparent',
      color: 'inherit',
      padding: 0,
      border: 'none',
      fontSize: 'inherit',
    },
  },

  // 文本格式
  '& strong': {
    fontWeight: 700,
    color: theme.palette.text.primary,
  },
  '& em': {
    fontStyle: 'italic',
    color: theme.palette.text.secondary,
  },

  // 分隔线
  '& hr': {
    border: 'none',
    borderTop: `2px solid ${theme.palette.divider}`,
    margin: theme.spacing(3, 0),
  },

  // 表格样式
  '& table': {
    width: '100%',
    borderCollapse: 'collapse',
    marginTop: theme.spacing(2),
    marginBottom: theme.spacing(2),
    '& th, & td': {
      border: `1px solid ${theme.palette.divider}`,
      padding: theme.spacing(1),
      textAlign: 'left',
    },
    '& th': {
      backgroundColor: theme.palette.grey[100],
      fontWeight: 600,
    },
  },
}));

const styleTemplates = [
  { id: 'professional', name: '专业商务', icon: <Business />, prompt: '使用专业、正式的商务语言风格' },
  { id: 'creative', name: '创意营销', icon: <Campaign />, prompt: '使用富有创意和吸引力的营销语言' },
  { id: 'technical', name: '技术文档', icon: <Code />, prompt: '使用准确、详细的技术文档风格' },
  { id: 'educational', name: '教育培训', icon: <School />, prompt: '使用易懂、循序渐进的教学风格' },
  { id: 'seo', name: 'SEO优化', icon: <TrendingUp />, prompt: '优化关键词密度和搜索引擎友好性' },
  { id: 'storytelling', name: '故事叙述', icon: <AutoAwesome />, prompt: '使用引人入胜的故事叙述方式' },
];

// 简单的内容格式化组件
const ContentRenderer = ({ content }) => {
  if (!content) return null;

  const formatContent = (text) => {
    // 清理文本
    text = text.replace(/\r\n/g, '\n').replace(/\r/g, '\n').trim();

    // 处理标题 - # ## ### 等
    text = text.replace(/^(#{1,6})\s+(.+)$/gm, (match, hashes, title) => {
      const level = hashes.length;
      return `<h${level} style="margin-top: 2em; margin-bottom: 1em; font-weight: bold; color: #1976d2;">${title.trim()}</h${level}>`;
    });

    // 处理粗体 **text**
    text = text.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

    // 处理斜体 *text*
    text = text.replace(/\*([^*\n]+)\*/g, '<em>$1</em>');

    // 处理代码块 ```code```
    text = text.replace(/```([\s\S]*?)```/g, '<pre style="background: #f5f5f5; padding: 1em; border-radius: 4px; overflow-x: auto; margin: 1em 0;"><code>$1</code></pre>');

    // 处理行内代码 `code`
    text = text.replace(/`([^`\n]+)`/g, '<code style="background: #f0f0f0; padding: 2px 4px; border-radius: 3px; font-family: monospace;">$1</code>');

    // 处理引用 > text
    text = text.replace(/^>\s+(.+)$/gm, '<blockquote style="border-left: 4px solid #1976d2; padding-left: 1em; margin: 1em 0; font-style: italic; color: #666;">$1</blockquote>');

    // 处理无序列表 - item
    text = text.replace(/^[-*]\s+(.+)$/gm, '<li style="margin-bottom: 0.5em;">$1</li>');

    // 处理有序列表 1. item
    text = text.replace(/^\d+\.\s+(.+)$/gm, '<li style="margin-bottom: 0.5em;">$1</li>');

    // 包装列表项
    text = text.replace(/(<li[^>]*>.*?<\/li>)(\s*<li[^>]*>.*?<\/li>)*/gs, (match) => {
      return `<ul style="margin: 1em 0; padding-left: 2em;">${match}</ul>`;
    });

    // 处理段落 - 将单独的行转换为段落
    const lines = text.split('\n');
    const processedLines = [];

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();

      if (!line) {
        // 空行
        processedLines.push('<br>');
      } else if (line.match(/^<(h[1-6]|ul|ol|li|blockquote|pre|code)/)) {
        // 已经是HTML标签的行
        processedLines.push(line);
      } else {
        // 普通文本行，包装为段落
        processedLines.push(`<p style="margin-bottom: 1em; line-height: 1.6; text-align: justify;">${line}</p>`);
      }
    }

    return processedLines.join('\n');
  };

  return (
    <div
      style={{
        maxWidth: '800px',
        margin: '0 auto',
        padding: '2em',
        fontFamily: '"Segoe UI", Tahoma, Geneva, Verdana, sans-serif',
        fontSize: '16px',
        lineHeight: '1.6',
        color: '#333'
      }}
      dangerouslySetInnerHTML={{ __html: formatContent(content) }}
    />
  );
};

function AIContentCreator({ projectId, onClose }) {
  const [messages, setMessages] = useState([
    { id: 1, text: '您好！我是AI内容创作助手。请告诉我您想创建什么类型的内容？', isUser: false }
  ]);
  const [inputMessage, setInputMessage] = useState('');
  const [selectedTemplate, setSelectedTemplate] = useState('professional');
  const [articleContent, setArticleContent] = useState('');
  const [articleTitle, setArticleTitle] = useState('未命名文章');
  const [isGenerating, setIsGenerating] = useState(false);
  const [conversations, setConversations] = useState([]);
  const [currentConversationId, setCurrentConversationId] = useState(null);
  const [articleVersions, setArticleVersions] = useState([]);
  const [currentVersionIndex, setCurrentVersionIndex] = useState(-1);
  const [projects, setProjects] = useState([]);
  const [selectedProject, setSelectedProject] = useState(projectId || null);
  const [showNewConversationDialog, setShowNewConversationDialog] = useState(false);
  const [showVersionHistory, setShowVersionHistory] = useState(false);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });
  const [showProjectSelector, setShowProjectSelector] = useState(false);
  const messagesEndRef = useRef(null);

  useEffect(() => {
    loadProjects();
    loadConversations();
  }, []);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const loadProjects = async () => {
    try {
      const response = await ApiService.get('/projects');
      setProjects(response.data);
    } catch (error) {
      console.error('Failed to load projects:', error);
    }
  };

  const loadConversations = async () => {
    try {
      const response = await ApiService.get('/ai/conversations');
      setConversations(response.data);
      if (response.data.length > 0 && !currentConversationId) {
        setCurrentConversationId(response.data[0].id);
        loadConversation(response.data[0].id);
      }
    } catch (error) {
      console.error('Failed to load conversations:', error);
    }
  };

  const loadConversation = async (conversationId) => {
    try {
      const response = await ApiService.get(`/ai/conversations/${conversationId}`);
      setMessages(response.data.messages);
      setArticleContent(response.data.current_content);
      setArticleTitle(response.data.title);
      setArticleVersions(response.data.versions);
      setCurrentVersionIndex(response.data.versions.length - 1);
    } catch (error) {
      console.error('Failed to load conversation:', error);
    }
  };

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || isGenerating) return;

    const userMessage = {
      id: Date.now(),
      text: inputMessage,
      isUser: true,
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsGenerating(true);

    // 创建AI消息占位符
    const aiMessageId = Date.now() + 1;
    const aiMessage = {
      id: aiMessageId,
      text: '正在生成内容...',
      isUser: false,
      isStreaming: true,
    };
    setMessages(prev => [...prev, aiMessage]);

    try {
      const template = styleTemplates.find(t => t.id === selectedTemplate);

      // 调用流式API
      const token = localStorage.getItem('ai_seo_auth_token');
      const response = await fetch('http://127.0.0.1:8000/api/v1/ai/content/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          content_type: 'article',
          topic: inputMessage,
          keywords: inputMessage.split(/\s+/).filter(word => word.length > 0).slice(0, 5).concat(['内容', 'AI']).slice(0, 3),
          target_audience: '通用受众',
          tone: 'professional',
          length: 'medium',
          language: 'zh-CN',
          ai_model: 'doubao'
        }),
      });

      if (!response.ok) {
        throw new Error('网络请求失败');
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let fullContent = '';
      let currentProgress = 0;

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6));

              if (data.type === 'content') {
                fullContent += data.content;
                // 更新AI消息内容
                setMessages(prev => prev.map(msg =>
                  msg.id === aiMessageId
                    ? { ...msg, text: fullContent, isStreaming: true }
                    : msg
                ));
              } else if (data.type === 'progress') {
                currentProgress = data.progress;
                // 可以在这里更新进度条
              } else if (data.type === 'final') {
                // 生成完成
                setMessages(prev => prev.map(msg =>
                  msg.id === aiMessageId
                    ? { ...msg, text: data.generated_content, isStreaming: false }
                    : msg
                ));
                setArticleContent(data.generated_content);
                setArticleTitle('AI生成的文章');
                saveVersion(data.generated_content, 'AI生成的文章');
              } else if (data.type === 'error') {
                throw new Error(data.message);
              }
            } catch (parseError) {
              console.warn('解析流数据失败:', parseError);
            }
          }
        }
      }
    } catch (error) {
      console.error('Failed to generate content:', error);
      setSnackbar({ open: true, message: '内容生成失败，请重试', severity: 'error' });
      // 移除失败的AI消息
      setMessages(prev => prev.filter(msg => msg.id !== aiMessageId));
    } finally {
      setIsGenerating(false);
    }
  };

  const saveVersion = (content, title) => {
    const newVersion = {
      id: Date.now(),
      content,
      title,
      timestamp: new Date().toISOString(),
      version: articleVersions.length + 1,
    };
    setArticleVersions(prev => [...prev, newVersion]);
    setCurrentVersionIndex(articleVersions.length);
  };

  const rollbackToVersion = (versionIndex) => {
    if (versionIndex >= 0 && versionIndex < articleVersions.length) {
      const version = articleVersions[versionIndex];
      setArticleContent(version.content);
      setArticleTitle(version.title);
      setCurrentVersionIndex(versionIndex);
      setShowVersionHistory(false);
      setSnackbar({ open: true, message: `已恢复到版本 ${version.version}`, severity: 'success' });
    }
  };

  const handleCopyContent = () => {
    navigator.clipboard.writeText(articleContent);
    setSnackbar({ open: true, message: '内容已复制到剪贴板', severity: 'success' });
  };

  const handleDownloadContent = () => {
    const blob = new Blob([articleContent], { type: 'text/plain;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${articleTitle}.txt`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
    setSnackbar({ open: true, message: '文件下载成功', severity: 'success' });
  };

  const handleSaveContent = async () => {
    if (!selectedProject) {
      setShowProjectSelector(true);
      return;
    }

    try {
      await ApiService.post('/ai/content/save', {
        title: articleTitle,
        content: articleContent,
        project_id: selectedProject,
        conversation_id: currentConversationId,
      });
      setSnackbar({ open: true, message: '内容保存成功', severity: 'success' });
    } catch (error) {
      console.error('Failed to save content:', error);
      setSnackbar({ open: true, message: '保存失败，请重试', severity: 'error' });
    }
  };

  const handleNewConversation = async (title) => {
    try {
      const response = await ApiService.post('/ai/conversations', {
        title,
        project_id: selectedProject,
      });
      setCurrentConversationId(response.data.id);
      setMessages([{ id: 1, text: '您好！我是AI内容创作助手。请告诉我您想创建什么类型的内容？', isUser: false }]);
      setArticleContent('');
      setArticleTitle('未命名文章');
      setArticleVersions([]);
      setCurrentVersionIndex(-1);
      loadConversations();
      setShowNewConversationDialog(false);
    } catch (error) {
      console.error('Failed to create conversation:', error);
      setSnackbar({ open: true, message: '创建对话失败', severity: 'error' });
    }
  };

  return (
    <StyledContainer>
      <ChatPanel elevation={0}>
        <ChatHeader>
          <Box display="flex" alignItems="center" gap={1}>
            <SmartToy />
            <Typography variant="h6" fontWeight="bold">
              AI创作助手
            </Typography>
          </Box>
          <Box display="flex" gap={1}>
            <Tooltip title="新建对话">
              <IconButton 
                size="small" 
                sx={{ color: 'white' }}
                onClick={() => setShowNewConversationDialog(true)}
              >
                <Add />
              </IconButton>
            </Tooltip>
            <Tooltip title="对话历史">
              <IconButton 
                size="small" 
                sx={{ color: 'white' }}
                onClick={() => {}}
              >
                <History />
              </IconButton>
            </Tooltip>
          </Box>
        </ChatHeader>

        <Box p={2} bgcolor="background.default">
          <FormControl fullWidth size="small" variant="outlined">
            <InputLabel>风格模板</InputLabel>
            <Select
              value={selectedTemplate}
              onChange={(e) => setSelectedTemplate(e.target.value)}
              label="风格模板"
              startAdornment={
                <Box display="flex" alignItems="center" ml={1}>
                  {styleTemplates.find(t => t.id === selectedTemplate)?.icon}
                </Box>
              }
            >
              {styleTemplates.map(template => (
                <MenuItem key={template.id} value={template.id}>
                  <Box display="flex" alignItems="center" gap={1}>
                    {template.icon}
                    <Typography>{template.name}</Typography>
                  </Box>
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Box>

        <Divider />

        <ChatMessages>
          {messages.map((message) => (
            <Fade in key={message.id}>
              <MessageBubble isUser={message.isUser}>
                {!message.isUser && (
                  <Box display="flex" alignItems="center" gap={1} mb={1}>
                    <Avatar sx={{ width: 24, height: 24, bgcolor: 'primary.main' }}>
                      <SmartToy sx={{ fontSize: 16 }} />
                    </Avatar>
                    <Typography variant="caption" fontWeight="bold">
                      AI助手
                    </Typography>
                  </Box>
                )}
                <Typography variant="body2">
                  {message.text}
                </Typography>
              </MessageBubble>
            </Fade>
          ))}
          {isGenerating && (
            <Box display="flex" alignItems="center" gap={1} ml={1}>
              <CircularProgress size={20} />
              <Typography variant="body2" color="text.secondary">
                AI正在生成内容...
              </Typography>
            </Box>
          )}
          <div ref={messagesEndRef} />
        </ChatMessages>

        <Divider />

        <Box p={2} bgcolor="background.paper">
          <TextField
            fullWidth
            multiline
            maxRows={3}
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            onKeyPress={(e) => {
              if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                handleSendMessage();
              }
            }}
            placeholder="输入您的需求，例如：帮我写一篇关于数字化转型的文章..."
            variant="outlined"
            size="small"
            disabled={isGenerating}
            InputProps={{
              endAdornment: (
                <IconButton 
                  onClick={handleSendMessage} 
                  disabled={!inputMessage.trim() || isGenerating}
                  color="primary"
                >
                  <Send />
                </IconButton>
              ),
            }}
          />
        </Box>
      </ChatPanel>

      <ContentPanel elevation={0}>
        <ContentHeader>
          <Box display="flex" alignItems="center" gap={2}>
            <Article color="primary" />
            <TextField
              value={articleTitle}
              onChange={(e) => setArticleTitle(e.target.value)}
              variant="standard"
              sx={{ 
                fontSize: '1.25rem',
                fontWeight: 600,
                '& .MuiInput-underline:before': {
                  borderBottom: 'none',
                },
                '& .MuiInput-underline:hover:not(.Mui-disabled):before': {
                  borderBottom: '1px solid rgba(0, 0, 0, 0.42)',
                },
              }}
            />
            {selectedProject && (
              <Chip
                icon={<FolderOpen />}
                label={projects.find(p => p.id === selectedProject)?.name || '项目'}
                color="primary"
                variant="outlined"
                size="small"
              />
            )}
          </Box>
          
          <Box display="flex" alignItems="center" gap={1}>
            <Tooltip title="版本历史">
              <IconButton onClick={() => setShowVersionHistory(true)} disabled={articleVersions.length === 0}>
                <History />
              </IconButton>
            </Tooltip>
            <Tooltip title="关联项目">
              <IconButton onClick={() => setShowProjectSelector(true)}>
                <AttachFile />
              </IconButton>
            </Tooltip>
            <Divider orientation="vertical" flexItem sx={{ mx: 1 }} />
            <Tooltip title="复制内容">
              <IconButton onClick={handleCopyContent} disabled={!articleContent}>
                <ContentCopy />
              </IconButton>
            </Tooltip>
            <Tooltip title="下载文档">
              <IconButton onClick={handleDownloadContent} disabled={!articleContent}>
                <Download />
              </IconButton>
            </Tooltip>
            <Tooltip title="保存到项目">
              <IconButton onClick={handleSaveContent} disabled={!articleContent}>
                <Save />
              </IconButton>
            </Tooltip>
            <Tooltip title="发布文章">
              <IconButton color="primary" disabled={!articleContent}>
                <Publish />
              </IconButton>
            </Tooltip>
          </Box>
        </ContentHeader>

        <ContentBody>
          {articleContent ? (
            <ContentRenderer content={articleContent} />
          ) : (
            <Box
              display="flex"
              flexDirection="column"
              alignItems="center"
              justifyContent="center"
              height="100%"
              color="text.secondary"
            >
              <AutoAwesome sx={{ fontSize: 64, mb: 2, opacity: 0.3 }} />
              <Typography variant="h6" gutterBottom>
                开始创作您的内容
              </Typography>
              <Typography variant="body2" color="text.secondary">
                在左侧对话框中输入您的需求，AI将为您生成内容
              </Typography>
            </Box>
          )}
        </ContentBody>
      </ContentPanel>

      {/* 新建对话对话框 */}
      <Dialog 
        open={showNewConversationDialog} 
        onClose={() => setShowNewConversationDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>新建对话</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="对话标题"
            fullWidth
            variant="outlined"
            defaultValue={`新对话 ${new Date().toLocaleDateString()}`}
            onKeyPress={(e) => {
              if (e.key === 'Enter') {
                handleNewConversation(e.target.value);
              }
            }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowNewConversationDialog(false)}>取消</Button>
          <Button 
            onClick={() => {
              const input = document.querySelector('input[type="text"]');
              handleNewConversation(input.value);
            }} 
            variant="contained"
          >
            创建
          </Button>
        </DialogActions>
      </Dialog>

      {/* 版本历史对话框 */}
      <Dialog
        open={showVersionHistory}
        onClose={() => setShowVersionHistory(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Box display="flex" alignItems="center" gap={1}>
            <History />
            版本历史
          </Box>
        </DialogTitle>
        <DialogContent>
          <List>
            {articleVersions.map((version, index) => (
              <ListItem key={version.id}>
                <ListItemIcon>
                  <Article />
                </ListItemIcon>
                <ListItemText
                  primary={`版本 ${version.version} - ${version.title}`}
                  secondary={new Date(version.timestamp).toLocaleString()}
                />
                <Button
                  variant="outlined"
                  size="small"
                  onClick={() => rollbackToVersion(index)}
                  disabled={index === currentVersionIndex}
                >
                  {index === currentVersionIndex ? '当前版本' : '恢复'}
                </Button>
              </ListItem>
            ))}
          </List>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowVersionHistory(false)}>关闭</Button>
        </DialogActions>
      </Dialog>

      {/* 项目选择对话框 */}
      <Dialog
        open={showProjectSelector}
        onClose={() => setShowProjectSelector(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>选择关联项目</DialogTitle>
        <DialogContent>
          <FormControl fullWidth margin="dense">
            <InputLabel>项目</InputLabel>
            <Select
              value={selectedProject || ''}
              onChange={(e) => setSelectedProject(e.target.value)}
              label="项目"
            >
              {projects.map(project => (
                <MenuItem key={project.id} value={project.id}>
                  <Box display="flex" alignItems="center" gap={1}>
                    <Folder />
                    {project.name}
                  </Box>
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowProjectSelector(false)}>取消</Button>
          <Button
            onClick={() => {
              setShowProjectSelector(false);
              if (selectedProject) {
                handleSaveContent();
              }
            }}
            variant="contained"
            disabled={!selectedProject}
          >
            确认
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar 通知 */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={3000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert 
          onClose={() => setSnackbar({ ...snackbar, open: false })} 
          severity={snackbar.severity}
          variant="filled"
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </StyledContainer>
  );
}

export default AIContentCreator;