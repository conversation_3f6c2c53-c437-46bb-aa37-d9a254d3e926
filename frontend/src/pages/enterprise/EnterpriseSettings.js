import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Tabs,
  Tab,
  TextField,
  Button,
  Grid,
  Card,
  CardContent,
  Switch,
  FormControlLabel,
  Divider,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  InputAdornment,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  ListItemIcon,
  Paper,
  CircularProgress,
  Skeleton,
} from '@mui/material';
import {
  Business,
  Api,
  Notifications,
  Save,
  Edit,
  ContentCopy,
  Visibility,
  VisibilityOff,
  Add,
  Delete,
  Security,
  Email,
  Phone,
  LocationOn,
  Description,
  CheckCircle,
  Warning,
  Info,
  Refresh,
  Key,
  Settings,
  Domain,
  CalendarToday,
  Person,
  Work,
  Group,
  CreditCard,
} from '@mui/icons-material';
import companyService from '../../services/companyService';

function EnterpriseSettings() {
  // Component state
  const [currentTab, setCurrentTab] = useState(0);
  const [showApiKey, setShowApiKey] = useState(false);
  const [openApiDialog, setOpenApiDialog] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // 企业信息数据（只读）
  const [companyInfo, setCompanyInfo] = useState(null);

  // 获取企业信息
  const fetchCompanyInfo = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await companyService.getCompanyInfo();

      // 处理不同的响应格式
      let data = null;
      if (response && response.success && response.data) {
        data = response.data;
      } else if (response && response.data) {
        data = response.data;
      } else if (response && response.success !== false) {
        data = response;
      }

      if (data) {
        // 格式化企业信息数据
        const formattedInfo = {
          name: data.company_name || data.name || '-',
          type: data.company_type || data.type || '-',
          creditCode: data.unified_social_credit_code || data.creditCode || '-',
          industry: data.industry || '-',
          scale: data.company_size || data.scale || '-',
          address: data.headquarters_location || data.address || '-',
          phone: data.contact_phone || data.phone || '-',
          email: data.contact_email || data.email || '-',
          website: data.official_website || data.website || '-',
          description: data.company_description || data.description || '-',
          foundDate: data.establishment_date || data.foundDate || '-',
          legalPerson: data.legal_representative || data.legalPerson || '-',
          businessScope: data.business_scope || '-',
          registeredCapital: data.registered_capital || '-',
          verificationStatus: data.verification_status || 'pending',
          createdAt: data.created_at || '-',
          updatedAt: data.updated_at || '-'
        };

        setCompanyInfo(formattedInfo);
      } else {
        setError('无法获取企业信息');
      }
    } catch (error) {
      console.error('获取企业信息失败:', error);
      setError('获取企业信息失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 组件挂载时获取企业信息
  useEffect(() => {
    fetchCompanyInfo();
  }, []);

  // API密钥数据
  const [apiKey] = useState({
    id: 1,
    name: 'API密钥',
    key: 'sk_live_1234567890abcdef',
    status: 'active',
    createDate: '2024-01-01',
    lastUsed: '2024-01-15 14:30',
    requests: 15234
  });

  // 通知设置数据
  const [notificationSettings, setNotificationSettings] = useState({
    email: {
      systemNotice: true,
      contentUpdate: true,
      apiAlert: true,
      monthlyReport: false,
      promotion: false,
    },
    sms: {
      systemNotice: false,
      contentUpdate: false,
      apiAlert: true,
      monthlyReport: false,
      promotion: false,
    }
  });

  // 新API密钥表单
  const [newApiKey, setNewApiKey] = useState({
    name: '',
    description: '',
    environment: 'production',
  });

  // API密钥列表
  const [apiKeys, setApiKeys] = useState([
    {
      id: 1,
      name: '生产环境密钥',
      key: 'sk_live_1234567890abcdef',
      createdAt: '2024-01-01',
      lastUsed: '2024-01-15',
      status: 'active',
      permissions: 'full'
    },
    {
      id: 2,
      name: '测试环境密钥',
      key: 'sk_test_abcdef1234567890',
      createdAt: '2024-01-10',
      lastUsed: '从未使用',
      status: 'active',
      permissions: 'read'
    }
  ]);

  // API密钥表单
  const [apiKeyForm, setApiKeyForm] = useState({
    name: '',
    permissions: 'read'
  });

  const handleTabChange = (event, newValue) => {
    setCurrentTab(newValue);
  };

  const handleCopyApiKey = (key) => {
    navigator.clipboard.writeText(key);
    // 这里可以添加提示消息
  };


  // 刷新企业信息
  const handleRefreshCompanyInfo = () => {
    fetchCompanyInfo();
  };

  const handleNotificationChange = (channel, type) => {
    setNotificationSettings(prev => ({
      ...prev,
      [channel]: {
        ...prev[channel],
        [type]: !prev[channel][type]
      }
    }));
  };

  const handleCreateApiKey = () => {
    // 生成新的API密钥
    const newKey = {
      id: Date.now(),
      name: apiKeyForm.name || '新密钥',
      key: `sk_${Math.random().toString(36).substring(2)}${Math.random().toString(36).substring(2)}`,
      createdAt: new Date().toISOString().split('T')[0],
      lastUsed: '从未使用',
      status: 'active',
      permissions: apiKeyForm.permissions || 'read'
    };
    
    setApiKeys(prevKeys => [...prevKeys, newKey]);
    setOpenApiDialog(false);
    setApiKeyForm({ name: '', permissions: 'read' });
    
    // 显示成功提示
    alert(`API密钥创建成功！请复制并保存：${newKey.key}`);
  };

  // 渲染企业信息标签页
  const renderCompanyInfo = () => {
    if (loading) {
      return (
        <Box>
          <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="h6" sx={{ fontWeight: 600, color: '#1a1a1a' }}>
              企业基本信息
            </Typography>
            <Skeleton variant="rectangular" width={100} height={36} />
          </Box>
          <Card variant="outlined" sx={{ borderRadius: 2 }}>
            <CardContent sx={{ p: 3 }}>
              <Grid container spacing={3}>
                {Array.from({ length: 8 }).map((_, index) => (
                  <Grid item xs={12} md={6} key={index}>
                    <Skeleton variant="rectangular" height={56} />
                  </Grid>
                ))}
                <Grid item xs={12}>
                  <Skeleton variant="rectangular" height={120} />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Box>
      );
    }

    if (error) {
      return (
        <Box>
          <Box sx={{ mb: 3 }}>
            <Typography variant="h6" sx={{ fontWeight: 600, color: '#1a1a1a' }}>
              企业基本信息
            </Typography>
          </Box>
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        </Box>
      );
    }

    if (!companyInfo) {
      return (
        <Box>
          <Alert severity="info">
            暂无企业信息
          </Alert>
        </Box>
      );
    }

    return (
      <Box>
        <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h6" sx={{ fontWeight: 600, color: '#1a1a1a' }}>
            企业基本信息
          </Typography>
          <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
            {companyInfo.verificationStatus && companyInfo.verificationStatus !== 'unknown' && (
              <Chip
                label={
                  companyInfo.verificationStatus === 'approved' ? '已认证' :
                  companyInfo.verificationStatus === 'pending' ? '待审核' :
                  companyInfo.verificationStatus === 'rejected' ? '审核未通过' : ''
                }
                color={
                  companyInfo.verificationStatus === 'approved' ? 'success' :
                  companyInfo.verificationStatus === 'pending' ? 'warning' :
                  companyInfo.verificationStatus === 'rejected' ? 'error' : 'default'
                }
                size="small"
                icon={
                  companyInfo.verificationStatus === 'approved' ? <CheckCircle /> :
                  companyInfo.verificationStatus === 'pending' ? <Info /> :
                  companyInfo.verificationStatus === 'rejected' ? <Warning /> : null
                }
              />
            )}
          </Box>
        </Box>

      <Card sx={{ borderRadius: 2, border: '1px solid #e5e7eb', boxShadow: 'none' }}>
        <CardContent sx={{ p: 4 }}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="企业名称"
                value={companyInfo.name || '-'}
                InputProps={{
                  readOnly: true,
                  startAdornment: (
                    <InputAdornment position="start">
                      <Business sx={{ color: '#9ca3af' }} />
                    </InputAdornment>
                  ),
                }}
                variant="filled"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="企业类型"
                value={companyInfo.type || '-'}
                InputProps={{
                  readOnly: true,
                  startAdornment: (
                    <InputAdornment position="start">
                      <Work sx={{ color: '#9ca3af' }} />
                    </InputAdornment>
                  ),
                }}
                variant="filled"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="统一社会信用代码"
                value={companyInfo.creditCode || '-'}
                InputProps={{
                  readOnly: true,
                  startAdornment: (
                    <InputAdornment position="start">
                      <CreditCard sx={{ color: '#9ca3af' }} />
                    </InputAdornment>
                  ),
                }}
                variant="filled"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="所属行业"
                value={companyInfo.industry || '-'}
                InputProps={{
                  readOnly: true,
                  startAdornment: (
                    <InputAdornment position="start">
                      <Work sx={{ color: '#9ca3af' }} />
                    </InputAdornment>
                  ),
                }}
                variant="filled"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="企业规模"
                value={companyInfo.scale || '-'}
                InputProps={{
                  readOnly: true,
                  startAdornment: (
                    <InputAdornment position="start">
                      <Group sx={{ color: '#9ca3af' }} />
                    </InputAdornment>
                  ),
                }}
                variant="filled"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="成立日期"
                value={companyInfo.foundDate || '-'}
                InputProps={{
                  readOnly: true,
                  startAdornment: (
                    <InputAdornment position="start">
                      <CalendarToday sx={{ color: '#9ca3af' }} />
                    </InputAdornment>
                  ),
                }}
                variant="filled"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="法定代表人"
                value={companyInfo.legalPerson || '-'}
                InputProps={{
                  readOnly: true,
                  startAdornment: (
                    <InputAdornment position="start">
                      <Person sx={{ color: '#9ca3af' }} />
                    </InputAdornment>
                  ),
                }}
                variant="filled"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="注册资本"
                value={companyInfo.registeredCapital || '-'}
                InputProps={{
                  readOnly: true,
                  startAdornment: (
                    <InputAdornment position="start">
                      <CreditCard sx={{ color: '#9ca3af' }} />
                    </InputAdornment>
                  ),
                }}
                variant="filled"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="企业地址"
                value={companyInfo.address || '-'}
                InputProps={{
                  readOnly: true,
                  startAdornment: (
                    <InputAdornment position="start">
                      <LocationOn sx={{ color: '#9ca3af' }} />
                    </InputAdornment>
                  ),
                }}
                variant="filled"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="联系电话"
                value={companyInfo.phone || '-'}
                InputProps={{
                  readOnly: true,
                  startAdornment: (
                    <InputAdornment position="start">
                      <Phone sx={{ color: '#9ca3af' }} />
                    </InputAdornment>
                  ),
                }}
                variant="filled"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="企业邮箱"
                value={companyInfo.email || '-'}
                InputProps={{
                  readOnly: true,
                  startAdornment: (
                    <InputAdornment position="start">
                      <Email sx={{ color: '#9ca3af' }} />
                    </InputAdornment>
                  ),
                }}
                variant="filled"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="官方网站"
                value={companyInfo.website || '-'}
                InputProps={{
                  readOnly: true,
                  startAdornment: (
                    <InputAdornment position="start">
                      <Domain sx={{ color: '#9ca3af' }} />
                    </InputAdornment>
                  ),
                }}
                variant="filled"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="经营范围"
                value={companyInfo.businessScope || '-'}
                InputProps={{
                  readOnly: true,
                  startAdornment: (
                    <InputAdornment position="start" sx={{ alignSelf: 'flex-start', mt: 1 }}>
                      <Description sx={{ color: '#9ca3af' }} />
                    </InputAdornment>
                  ),
                }}
                multiline
                rows={3}
                variant="filled"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="企业简介"
                value={companyInfo.description || '-'}
                InputProps={{
                  readOnly: true,
                  startAdornment: (
                    <InputAdornment position="start" sx={{ alignSelf: 'flex-start', mt: 1 }}>
                      <Description sx={{ color: '#9ca3af' }} />
                    </InputAdornment>
                  ),
                }}
                multiline
                rows={4}
                variant="filled"
              />
            </Grid>
          </Grid>

          {/* 更新信息 */}
          <Box sx={{ mt: 4, p: 2, backgroundColor: '#f9fafb', borderRadius: 1 }}>
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <Typography variant="caption" sx={{ color: '#6b7280' }}>
                  <strong>创建时间：</strong>
                  {companyInfo.createdAt ? new Date(companyInfo.createdAt).toLocaleString('zh-CN') : '-'}
                </Typography>
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography variant="caption" sx={{ color: '#6b7280' }}>
                  <strong>最后更新：</strong>
                  {companyInfo.updatedAt ? new Date(companyInfo.updatedAt).toLocaleString('zh-CN') : '-'}
                </Typography>
              </Grid>
            </Grid>
          </Box>
        </CardContent>
      </Card>
    </Box>
    );
  };

  // 渲染API管理标签页
  const renderApiManagement = () => (
    <Box>
      <Box sx={{ mb: 3 }}>
        <Typography variant="h6" sx={{ fontWeight: 600, color: '#1a1a1a', mb: 0.5 }}>
          API密钥管理
        </Typography>
        <Typography variant="body2" sx={{ color: '#6b7280' }}>
          使用API密钥调用平台接口服务
        </Typography>
      </Box>

      <Alert severity="info" sx={{ mb: 3 }}>
        <Typography variant="body2">
          请妥善保管您的API密钥，不要在公开场合暴露。如果密钥泄露，请立即重新生成。
        </Typography>
      </Alert>

      <Card sx={{ borderRadius: 2, border: '1px solid #e5e7eb', boxShadow: 'none', p: 3 }}>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Key sx={{ fontSize: 20, color: '#6b7280' }} />
                <Typography variant="subtitle1" sx={{ fontWeight: 600, color: '#1a1a1a' }}>
                  {apiKey.name}
                </Typography>
                <Chip
                  label="启用"
                  size="small"
                  sx={{
                    backgroundColor: '#dcfce7',
                    color: '#166534',
                    fontWeight: 500
                  }}
                />
              </Box>
              <Button
                variant="outlined"
                startIcon={<Refresh />}
                size="small"
                onClick={() => {}}
                sx={{ 
                  borderColor: '#e5e7eb',
                  color: '#6b7280',
                  '&:hover': {
                    borderColor: '#d1d5db',
                    backgroundColor: '#f9fafb'
                  }
                }}
              >
                重新生成
              </Button>
            </Box>
            
            <Box sx={{ backgroundColor: '#f9fafb', p: 2, borderRadius: 1, mb: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                <Typography variant="body2" sx={{ color: '#6b7280', fontWeight: 500 }}>
                  密钥值：
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Typography
                  variant="body1"
                  sx={{
                    fontFamily: 'monospace',
                    fontSize: '0.95rem',
                    color: '#374151',
                    flex: 1
                  }}
                >
                  {showApiKey ? apiKey.key : '••••••••••••••••••••••••••••••••'}
                </Typography>
                <IconButton
                  size="small"
                  onClick={() => setShowApiKey(!showApiKey)}
                  sx={{ color: '#6b7280' }}
                >
                  {showApiKey ? <VisibilityOff fontSize="small" /> : <Visibility fontSize="small" />}
                </IconButton>
                <IconButton
                  size="small"
                  onClick={() => handleCopyApiKey(apiKey.key)}
                  sx={{ color: '#6b7280' }}
                >
                  <ContentCopy fontSize="small" />
                </IconButton>
              </Box>
            </Box>

            <Grid container spacing={2}>
              <Grid item xs={12} sm={4}>
                <Typography variant="body2" sx={{ color: '#6b7280', mb: 0.5 }}>
                  创建时间
                </Typography>
                <Typography variant="body1" sx={{ color: '#374151', fontWeight: 500 }}>
                  {apiKey.createDate}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={4}>
                <Typography variant="body2" sx={{ color: '#6b7280', mb: 0.5 }}>
                  最后使用
                </Typography>
                <Typography variant="body1" sx={{ color: '#374151', fontWeight: 500 }}>
                  {apiKey.lastUsed}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={4}>
                <Typography variant="body2" sx={{ color: '#6b7280', mb: 0.5 }}>
                  调用次数
                </Typography>
                <Typography variant="body1" sx={{ color: '#374151', fontWeight: 500 }}>
                  {apiKey.requests.toLocaleString()} 次
                </Typography>
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      </Card>
    </Box>
  );

  // 渲染通知设置标签页
  const renderNotificationSettings = () => (
    <Box>
      <Box sx={{ mb: 3 }}>
        <Typography variant="h6" sx={{ fontWeight: 600, color: '#1a1a1a', mb: 0.5 }}>
          通知设置
        </Typography>
        <Typography variant="body2" sx={{ color: '#6b7280' }}>
          管理您接收系统通知的方式和类型
        </Typography>
      </Box>

      <Grid container spacing={3}>
        {/* 左侧：通知设置卡片 */}
        <Grid item xs={12} md={8}>
          <Grid container spacing={3}>
            {/* 邮件通知 */}
            <Grid item xs={12}>
              <Card sx={{ borderRadius: 2, border: '1px solid #e5e7eb', boxShadow: 'none', height: '100%' }}>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 3 }}>
                    <Email sx={{ color: '#3b82f6' }} />
                    <Typography variant="h6" sx={{ fontWeight: 600, color: '#1a1a1a' }}>
                      邮件通知
                    </Typography>
                  </Box>
                  <List>
                    <ListItem sx={{ px: 0 }}>
                      <ListItemText
                        primary="系统通知"
                        secondary="系统维护、功能更新等重要通知"
                        primaryTypographyProps={{ variant: 'body2', fontWeight: 500 }}
                        secondaryTypographyProps={{ variant: 'caption', color: '#6b7280' }}
                      />
                      <ListItemSecondaryAction>
                        <Switch
                          checked={notificationSettings.email.systemNotice}
                          onChange={() => handleNotificationChange('email', 'systemNotice')}
                          color="primary"
                        />
                      </ListItemSecondaryAction>
                    </ListItem>
                    <ListItem sx={{ px: 0 }}>
                      <ListItemText
                        primary="内容更新"
                        secondary="内容发布、审核状态变更通知"
                        primaryTypographyProps={{ variant: 'body2', fontWeight: 500 }}
                        secondaryTypographyProps={{ variant: 'caption', color: '#6b7280' }}
                      />
                      <ListItemSecondaryAction>
                        <Switch
                          checked={notificationSettings.email.contentUpdate}
                          onChange={() => handleNotificationChange('email', 'contentUpdate')}
                          color="primary"
                        />
                      </ListItemSecondaryAction>
                    </ListItem>
                    <ListItem sx={{ px: 0 }}>
                      <ListItemText
                        primary="API告警"
                        secondary="API调用异常、配额告警"
                        primaryTypographyProps={{ variant: 'body2', fontWeight: 500 }}
                        secondaryTypographyProps={{ variant: 'caption', color: '#6b7280' }}
                      />
                      <ListItemSecondaryAction>
                        <Switch
                          checked={notificationSettings.email.apiAlert}
                          onChange={() => handleNotificationChange('email', 'apiAlert')}
                          color="primary"
                        />
                      </ListItemSecondaryAction>
                    </ListItem>
                    <ListItem sx={{ px: 0 }}>
                      <ListItemText
                        primary="月度报告"
                        secondary="每月数据分析报告"
                        primaryTypographyProps={{ variant: 'body2', fontWeight: 500 }}
                        secondaryTypographyProps={{ variant: 'caption', color: '#6b7280' }}
                      />
                      <ListItemSecondaryAction>
                        <Switch
                          checked={notificationSettings.email.monthlyReport}
                          onChange={() => handleNotificationChange('email', 'monthlyReport')}
                          color="primary"
                        />
                      </ListItemSecondaryAction>
                    </ListItem>
                    <ListItem sx={{ px: 0 }}>
                      <ListItemText
                        primary="营销推广"
                        secondary="产品优惠、活动信息"
                        primaryTypographyProps={{ variant: 'body2', fontWeight: 500 }}
                        secondaryTypographyProps={{ variant: 'caption', color: '#6b7280' }}
                      />
                      <ListItemSecondaryAction>
                        <Switch
                          checked={notificationSettings.email.promotion}
                          onChange={() => handleNotificationChange('email', 'promotion')}
                          color="primary"
                        />
                      </ListItemSecondaryAction>
                    </ListItem>
                  </List>
                </CardContent>
              </Card>
            </Grid>

            {/* 短信通知 */}
            <Grid item xs={12}>
              <Card sx={{ borderRadius: 2, border: '1px solid #e5e7eb', boxShadow: 'none', height: '100%' }}>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 3 }}>
                    <Phone sx={{ color: '#10b981' }} />
                    <Typography variant="h6" sx={{ fontWeight: 600, color: '#1a1a1a' }}>
                      短信通知
                    </Typography>
                  </Box>
                  <List>
                    <ListItem sx={{ px: 0 }}>
                      <ListItemText
                        primary="系统通知"
                        secondary="系统维护、功能更新等重要通知"
                        primaryTypographyProps={{ variant: 'body2', fontWeight: 500 }}
                        secondaryTypographyProps={{ variant: 'caption', color: '#6b7280' }}
                      />
                      <ListItemSecondaryAction>
                        <Switch
                          checked={notificationSettings.sms.systemNotice}
                          onChange={() => handleNotificationChange('sms', 'systemNotice')}
                          color="primary"
                        />
                      </ListItemSecondaryAction>
                    </ListItem>
                    <ListItem sx={{ px: 0 }}>
                      <ListItemText
                        primary="内容更新"
                        secondary="内容发布、审核状态变更通知"
                        primaryTypographyProps={{ variant: 'body2', fontWeight: 500 }}
                        secondaryTypographyProps={{ variant: 'caption', color: '#6b7280' }}
                      />
                      <ListItemSecondaryAction>
                        <Switch
                          checked={notificationSettings.sms.contentUpdate}
                          onChange={() => handleNotificationChange('sms', 'contentUpdate')}
                          color="primary"
                        />
                      </ListItemSecondaryAction>
                    </ListItem>
                    <ListItem sx={{ px: 0 }}>
                      <ListItemText
                        primary="API告警"
                        secondary="API调用异常、配额告警"
                        primaryTypographyProps={{ variant: 'body2', fontWeight: 500 }}
                        secondaryTypographyProps={{ variant: 'caption', color: '#6b7280' }}
                      />
                      <ListItemSecondaryAction>
                        <Switch
                          checked={notificationSettings.sms.apiAlert}
                          onChange={() => handleNotificationChange('sms', 'apiAlert')}
                          color="primary"
                        />
                      </ListItemSecondaryAction>
                    </ListItem>
                    <ListItem sx={{ px: 0 }}>
                      <ListItemText
                        primary="月度报告"
                        secondary="每月数据分析报告"
                        primaryTypographyProps={{ variant: 'body2', fontWeight: 500 }}
                        secondaryTypographyProps={{ variant: 'caption', color: '#6b7280' }}
                      />
                      <ListItemSecondaryAction>
                        <Switch
                          checked={notificationSettings.sms.monthlyReport}
                          onChange={() => handleNotificationChange('sms', 'monthlyReport')}
                          color="primary"
                        />
                      </ListItemSecondaryAction>
                    </ListItem>
                    <ListItem sx={{ px: 0 }}>
                      <ListItemText
                        primary="营销推广"
                        secondary="产品优惠、活动信息"
                        primaryTypographyProps={{ variant: 'body2', fontWeight: 500 }}
                        secondaryTypographyProps={{ variant: 'caption', color: '#6b7280' }}
                      />
                      <ListItemSecondaryAction>
                        <Switch
                          checked={notificationSettings.sms.promotion}
                          onChange={() => handleNotificationChange('sms', 'promotion')}
                          color="primary"
                        />
                      </ListItemSecondaryAction>
                    </ListItem>
                  </List>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Grid>

        {/* 右侧：通知说明 */}
        <Grid item xs={12} md={4}>
          <Card sx={{ borderRadius: 2, border: '1px solid #e5e7eb', boxShadow: 'none', backgroundColor: '#f9fafb' }}>
            <CardContent>
              <Typography variant="h6" sx={{ fontWeight: 600, color: '#1a1a1a', mb: 3 }}>
                通知说明
              </Typography>
              
              <Box sx={{ mb: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1.5, mb: 2 }}>
                  <Email sx={{ fontSize: 24, color: '#3b82f6', mt: 0.5 }} />
                  <Box>
                    <Typography variant="subtitle2" sx={{ fontWeight: 600, color: '#374151', mb: 1 }}>
                      邮件通知
                    </Typography>
                    <Typography variant="body2" sx={{ color: '#6b7280', lineHeight: 1.6 }}>
                      通过您的企业邮箱接收通知，支持详细内容和附件。适合接收详细报告和非紧急通知。
                    </Typography>
                  </Box>
                </Box>
              </Box>

              <Box sx={{ mb: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1.5, mb: 2 }}>
                  <Phone sx={{ fontSize: 24, color: '#10b981', mt: 0.5 }} />
                  <Box>
                    <Typography variant="subtitle2" sx={{ fontWeight: 600, color: '#374151', mb: 1 }}>
                      短信通知
                    </Typography>
                    <Typography variant="body2" sx={{ color: '#6b7280', lineHeight: 1.6 }}>
                      紧急重要通知实时推送，确保及时获得关键信息。适合系统告警和紧急通知。
                    </Typography>
                  </Box>
                </Box>
              </Box>

              <Divider sx={{ my: 3 }} />

              <Box>
                <Typography variant="subtitle2" sx={{ fontWeight: 600, color: '#374151', mb: 2 }}>
                  注意事项
                </Typography>
                <List dense>
                  <ListItem sx={{ px: 0, py: 0.5 }}>
                    <ListItemIcon sx={{ minWidth: 28 }}>
                      <CheckCircle sx={{ fontSize: 16, color: '#10b981' }} />
                    </ListItemIcon>
                    <ListItemText 
                      primary="通知设置实时生效"
                      primaryTypographyProps={{ variant: 'caption', color: '#6b7280' }}
                    />
                  </ListItem>
                  <ListItem sx={{ px: 0, py: 0.5 }}>
                    <ListItemIcon sx={{ minWidth: 28 }}>
                      <CheckCircle sx={{ fontSize: 16, color: '#10b981' }} />
                    </ListItemIcon>
                    <ListItemText 
                      primary="短信通知可能产生额外费用"
                      primaryTypographyProps={{ variant: 'caption', color: '#6b7280' }}
                    />
                  </ListItem>
                  <ListItem sx={{ px: 0, py: 0.5 }}>
                    <ListItemIcon sx={{ minWidth: 28 }}>
                      <CheckCircle sx={{ fontSize: 16, color: '#10b981' }} />
                    </ListItemIcon>
                    <ListItemText 
                      primary="确保联系方式正确有效"
                      primaryTypographyProps={{ variant: 'caption', color: '#6b7280' }}
                    />
                  </ListItem>
                </List>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>
        <Button
          variant="contained"
          startIcon={<Save />}
          sx={{ backgroundColor: '#3b82f6', '&:hover': { backgroundColor: '#2563eb' } }}
        >
          保存通知设置
        </Button>
      </Box>
    </Box>
  );

  return (
    <Box sx={{ width: '100%', backgroundColor: 'white', minHeight: '100vh' }}>
      {/* 顶部标题区域 */}
      <Box sx={{
        borderBottom: '1px solid #f0f0f0',
        backgroundColor: 'white'
      }}>
        <Container maxWidth="xl">
          <Box sx={{ py: 5 }}>
            <Typography variant="h3" sx={{
              fontWeight: 700,
              color: '#1a1a1a',
              mb: 1,
              fontSize: { xs: '2rem', md: '2.5rem' }
            }}>
              企业设置
            </Typography>
            <Typography variant="body1" sx={{
              color: '#6b7280',
              fontSize: '1.125rem'
            }}>
              管理企业信息、API密钥和通知偏好
            </Typography>
          </Box>
        </Container>
      </Box>

      {/* 标签导航 */}
      <Box sx={{
        borderBottom: '1px solid #e5e7eb',
        backgroundColor: 'white'
      }}>
        <Container maxWidth="xl">
          <Tabs
            value={currentTab}
            onChange={handleTabChange}
            sx={{
              '& .MuiTab-root': {
                textTransform: 'none',
                fontWeight: 500,
                fontSize: '0.95rem',
                color: '#6b7280',
                py: 2,
                px: 3,
                '&.Mui-selected': {
                  color: '#3b82f6',
                },
              },
              '& .MuiTabs-indicator': {
                backgroundColor: '#3b82f6',
                height: 3,
              },
            }}
          >
            <Tab icon={<Business sx={{ fontSize: 20 }} />} iconPosition="start" label="企业信息" />
            <Tab icon={<Api sx={{ fontSize: 20 }} />} iconPosition="start" label="API管理" />
            <Tab icon={<Notifications sx={{ fontSize: 20 }} />} iconPosition="start" label="通知设置" />
          </Tabs>
        </Container>
      </Box>

      {/* 内容区域 */}
      <Box sx={{ backgroundColor: 'white' }}>
        <Container maxWidth="xl">
          <Box sx={{ py: 4 }}>
            {currentTab === 0 && renderCompanyInfo()}
            {currentTab === 1 && renderApiManagement()}
            {currentTab === 2 && renderNotificationSettings()}
          </Box>
        </Container>
      </Box>

      {/* 创建API密钥对话框 */}
      <Dialog
        open={openApiDialog}
        onClose={() => setOpenApiDialog(false)}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 2,
            boxShadow: '0 25px 50px -12px rgb(0 0 0 / 0.25)'
          }
        }}
      >
        <DialogTitle sx={{ borderBottom: '1px solid #e5e7eb', pb: 2 }}>
          <Typography variant="h6" sx={{ fontWeight: 600, color: '#1f2937' }}>
            创建新的API密钥
          </Typography>
        </DialogTitle>
        <DialogContent sx={{ pt: 3 }}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="密钥名称"
                value={newApiKey.name}
                onChange={(e) => setNewApiKey({ ...newApiKey, name: e.target.value })}
                placeholder="例如：生产环境密钥"
                required
              />
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>环境类型</InputLabel>
                <Select
                  value={newApiKey.environment}
                  label="环境类型"
                  onChange={(e) => setNewApiKey({ ...newApiKey, environment: e.target.value })}
                >
                  <MenuItem value="production">生产环境</MenuItem>
                  <MenuItem value="test">测试环境</MenuItem>
                  <MenuItem value="development">开发环境</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="描述"
                value={newApiKey.description}
                onChange={(e) => setNewApiKey({ ...newApiKey, description: e.target.value })}
                multiline
                rows={3}
                placeholder="描述这个API密钥的用途"
              />
            </Grid>
          </Grid>
          <Alert severity="warning" sx={{ mt: 3 }}>
            <Typography variant="body2">
              创建后请立即复制并妥善保存密钥，系统不会再次显示完整密钥。
            </Typography>
          </Alert>
        </DialogContent>
        <DialogActions sx={{ borderTop: '1px solid #e5e7eb', px: 3, py: 2 }}>
          <Button onClick={() => setOpenApiDialog(false)} sx={{ color: '#6b7280' }}>
            取消
          </Button>
          <Button
            onClick={handleCreateApiKey}
            variant="contained"
            sx={{
              backgroundColor: '#3b82f6',
              '&:hover': { backgroundColor: '#2563eb' }
            }}
          >
            创建密钥
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}

export default EnterpriseSettings;