import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import subscriptionService from '../../services/subscriptionService';
import orderService from '../../services/orderService';
import paymentService from '../../services/paymentService';
import {
  Box,
  Typography,
  Card,
  CardContent,
  CardActions,
  Grid,
  Avatar,
  AvatarGroup,
  Chip,
  Button,
  LinearProgress,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  Switch,
  Alert,
  Fab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  Radio,
  RadioGroup,
  FormControlLabel,
  Slider,
  Drawer,
  ListItemButton,
  Collapse,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  InputAdornment,
  IconButton,
  Checkbox,
  CircularProgress,
  Tabs,
  Tab,
} from '@mui/material';
import {
  Business,
  Dashboard,
  Analytics,
  People,
  Settings,
  Notifications,
  Assessment,
  TrendingUp,
  ExpandLess,
  ExpandMore,
  Menu,
  Close,
  Search,
  Add,
  Edit,
  Delete,
  Visibility,
  FileDownload,
  BarChart,
  AccountTree,
  Security,
  Group,
  Campaign,
  MonetizationOn,
  Storage,
  Api,
  Assignment,
  Support,
  CloudDownload,
  Star,
  PlayArrow,
  FileCopy,
  MenuBook,
  CloudUpload,
  Description,
  Schedule,
  MoreVert,
  Article,
  Refresh,
  Person,
  AutoAwesome,
  History,
  Share,
  AttachFile,
  Send,
  Save,
  Download,
  Publish,
  Upgrade,
  Diamond,
  AccountCircle,
  CalendarMonth,
  WorkspacePremium,
  CheckCircle,
  AccessTime,
  Folder,
  Logout,
  StarBorder,
  Rocket,
  Announcement,
  MenuOpen,
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '@mui/material/styles';
import ApiService from '../../services/api';
import AIContentCreation from './AIContentCreation';
// import GeoMonitoring from './GeoMonitoring';
import WritingTemplates from './WritingTemplates';
import EnterpriseSettings from './EnterpriseSettings';
import EnterpriseDashboard from './EnterpriseDashboard';
import EnterpriseContentManagement from './EnterpriseContentManagement';
import KnowledgeBaseManagement from './KnowledgeBaseManagement';
import EnterpriseAnnouncements from './EnterpriseAnnouncements';
import { lazyWithRetry } from '../../utils/lazyWithRetry';

// 懒加载组件以避免循环依赖和导入错误
const WorkspaceManager = lazyWithRetry(() => import('./WorkspaceManager'));
const GeoMonitoring = lazyWithRetry(() => import('./GeoMonitoringAI'));

function EnterpriseControlCenter() {
  const { user, logout } = useAuth();
  const theme = useTheme();
  const navigate = useNavigate();
  const [stats, setStats] = useState({
    totalProjects: 12,
    activeUsers: 156,
    monthlyRevenue: 45680,
    systemHealth: 98.5,
    apiCalls: 125430,
    storageUsed: 75,
  });
  const [notifications, setNotifications] = useState(true);
  const [settingsOpen, setSettingsOpen] = useState(false);
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [currentPage, setCurrentPage] = useState('dashboard');
  const [expandedMenus, setExpandedMenus] = useState({});
  const [showUpgradeDialog, setShowUpgradeDialog] = useState(false);
  const [showPaymentDialog, setShowPaymentDialog] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState('basic');
  const [selectedDuration, setSelectedDuration] = useState('monthly');
  const [paymentMethod, setPaymentMethod] = useState('alipay');
  const [isProcessingPayment, setIsProcessingPayment] = useState(false);
  const [availablePlans, setAvailablePlans] = useState([]);
  const [loadingPlans, setLoadingPlans] = useState(false);
  const [currentSubscription, setCurrentSubscription] = useState(null);

  // 组件加载时获取数据
  useEffect(() => {
    fetchAvailablePlans();
    fetchCurrentSubscription();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  // 当付费周期改变时重新获取套餐数据
  useEffect(() => {
    if (showUpgradeDialog) {
      fetchAvailablePlans(selectedDuration);
    }
  }, [selectedDuration, showUpgradeDialog]); // eslint-disable-line react-hooks/exhaustive-deps

  // 当付费周期改变时（包括自动切换），重新获取套餐数据
  useEffect(() => {
    // 只有在组件已经初始化后才重新获取套餐数据
    if (currentSubscription !== undefined) {
      fetchAvailablePlans(selectedDuration);
    }
  }, [selectedDuration]); // eslint-disable-line react-hooks/exhaustive-deps

  // 获取可用订阅计划
  const fetchAvailablePlans = async (billingCycle = null, forceRefreshSubscription = false) => {
    try {
      setLoadingPlans(true);

      // 如果需要强制刷新订阅状态（升级后）
      let latestSubscription = currentSubscription;
      if (forceRefreshSubscription) {
        try {
          const subscriptionResponse = await subscriptionService.getCurrentSubscription('enterprise');
          setCurrentSubscription(subscriptionResponse);
          latestSubscription = subscriptionResponse;
        } catch (error) {
          latestSubscription = null;
        }
      }

      const params = {
        target_user_type: 'enterprise',
        active_only: true
      };

      // 如果指定了付费周期，添加到参数中
      if (billingCycle) {
        params.billing_cycle = billingCycle;
      }

      const response = await subscriptionService.getAvailablePlans(params);



      // 处理不同的响应格式
      let plansData = [];
      if (Array.isArray(response)) {
        plansData = response;
      } else if (response.success && response.data) {
        plansData = response.data;
      } else if (response.data) {
        plansData = response.data;
      }

      if (plansData && plansData.length > 0) {
        // 转换API数据格式为组件需要的格式
        const formattedPlans = plansData.map(plan => {
          // 根据计划类型设置图标和颜色
          let icon, color;
          if (plan.plan_name.includes('企业') || plan.plan_type === 'enterprise') {
            icon = <WorkspacePremium sx={{ fontSize: 28 }} />;
            color = '#f59e0b';
          } else if (plan.plan_name.includes('专业') || plan.plan_type === 'pro') {
            icon = <Diamond sx={{ fontSize: 28 }} />;
            color = '#8b5cf6';
          } else {
            icon = <Rocket sx={{ fontSize: 28 }} />;
            color = '#3b82f6';
          }

          // 构建功能列表
          const features = [];
          if (plan.max_content_requests) {
            features.push(`${plan.max_content_requests}个内容请求`);
          }
          if (plan.max_monitoring_projects) {
            features.push(`${plan.max_monitoring_projects}个监控项目`);
          }
          if (plan.max_api_calls) {
            features.push(`${plan.max_api_calls}次API调用`);
          }
          if (plan.max_team_members) {
            features.push(`${plan.max_team_members}个团队成员`);
          }

          // 添加功能特性
          if (plan.features) {
            if (plan.features.support) {
              const supportMap = {
                'email': '邮件支持',
                'priority': '优先技术支持',
                'dedicated': '专属客服支持'
              };
              features.push(supportMap[plan.features.support] || '客服支持');
            }
            if (plan.features.analytics) {
              const analyticsMap = {
                'basic': '基础数据分析',
                'advanced': '高级数据分析',
                'enterprise': '企业级数据分析'
              };
              features.push(analyticsMap[plan.features.analytics] || '数据分析');
            }
            if (plan.features.api) {
              features.push('API接入权限');
            }
            if (plan.features.custom) {
              features.push('自定义品牌');
            }
          }

          return {
            id: plan.id, // 使用实际的UUID作为id
            plan_code: plan.plan_code, // 保留plan_code用于显示
            name: plan.plan_name,
            icon: icon,
            features: features,
            pricing: {
              monthly: parseFloat(plan.monthly_price) || 0,
              quarterly: parseFloat(plan.quarterly_price) || 0,
              yearly: parseFloat(plan.yearly_price) || 0
            },
            color: color,
            popular: plan.display_order === 2, // 第二个计划设为热门
            description: `${plan.plan_name} - 适合${plan.target_user_type === 'enterprise' ? '企业' : '个人'}用户`,
            is_purchased: plan.is_purchased || false, // 是否已购买
            current_subscription_id: plan.current_subscription_id || null // 当前订阅ID
          };
        });


        setAvailablePlans(formattedPlans);

        // 检查当前选中的套餐是否应该被禁用，如果是则重新选择
        if (formattedPlans.length > 0) {
          const currentSelectedPlan = formattedPlans.find(plan => String(plan.id) === String(selectedPlan));
          
          if (currentSelectedPlan) {
            // 检查是否是当前用户正在使用的套餐且付费周期相同
            const isCurrentPlan = latestSubscription &&
              (String(latestSubscription.plan_id) === String(currentSelectedPlan.id)) &&
              (latestSubscription.billing_cycle === selectedDuration);
            const shouldDisable = currentSelectedPlan.is_purchased || isCurrentPlan;
            
            if (shouldDisable) {
              // 当前选中的套餐应该被禁用，选择第一个未禁用的套餐
              const firstAvailablePlan = formattedPlans.find(plan => {
                // 检查是否是当前用户正在使用的套餐且付费周期相同
                const isCurrentPlan = latestSubscription &&
                  (String(latestSubscription.plan_id) === String(plan.id)) &&
                  (latestSubscription.billing_cycle === selectedDuration);
                const shouldDisable = plan.is_purchased || isCurrentPlan;
                return !shouldDisable;
              });
              
              if (firstAvailablePlan) {
                setSelectedPlan(firstAvailablePlan.id);
              }
            }
          } else {
            // 当前选中的套餐不在新列表中，选择第一个未禁用的套餐
            const firstAvailablePlan = formattedPlans.find(plan => {
              // 检查是否是当前用户正在使用的套餐且付费周期相同
              const isCurrentPlan = latestSubscription &&
                (String(latestSubscription.plan_id) === String(plan.id)) &&
                (latestSubscription.billing_cycle === selectedDuration);
              const shouldDisable = plan.is_purchased || isCurrentPlan;
              return !shouldDisable;
            });
            
            if (firstAvailablePlan) {
              setSelectedPlan(firstAvailablePlan.id);
            }
          }
        }
      }
    } catch (error) {

      // 如果API调用失败，使用默认的静态数据
      setAvailablePlans(subscriptionPlans);
    } finally {
      setLoadingPlans(false);
    }
  };

  // 获取当前用户订阅状态
  const fetchCurrentSubscription = async () => {
    try {
      const response = await subscriptionService.getCurrentSubscription('enterprise');
      setCurrentSubscription(response);

      // 如果用户有订阅，自动切换到用户当前的付费周期
      if (response && response.billing_cycle && response.billing_cycle !== selectedDuration) {
        setSelectedDuration(response.billing_cycle);
      }

      return response;
    } catch (error) {
      // 用户可能没有订阅，这是正常的
      setCurrentSubscription(null);
      return null;
    }
  };

  // 升级后刷新状态
  const refreshAfterUpgrade = async (newBillingCycle) => {
    // 等待一小段时间确保数据库更改已生效
    await new Promise(resolve => setTimeout(resolve, 500));

    // 先获取最新的订阅状态
    const latestSubscription = await fetchCurrentSubscription();

    // 再等待一小段时间
    await new Promise(resolve => setTimeout(resolve, 200));

    // 然后刷新套餐列表
    await fetchAvailablePlans(newBillingCycle);
    return latestSubscription;
  };

  // 处理订阅
  const handleSubscribe = async () => {
    try {
      setIsProcessingPayment(true);

      // 找到选中的套餐
      const selectedPlanData = availablePlans.find(plan => plan.id === selectedPlan);
      if (!selectedPlanData) {
        alert('请选择一个有效的套餐');
        return;
      }

      // 判断是升级还是购买新套餐
      const isSamePlan = currentSubscription && (currentSubscription.plan_id === selectedPlan);
      const isDifferentBillingCycle = currentSubscription && (currentSubscription.billing_cycle !== selectedDuration);

      if (currentSubscription && isSamePlan && isDifferentBillingCycle) {
        // 相同套餐不同付费周期 - 走升级流程
        const upgradeData = {
          target_plan_id: selectedPlan,
          billing_cycle: selectedDuration,
          effective_immediately: true,
          prorate: true
        };
        const response = await subscriptionService.upgradeSubscription(upgradeData);

        // 检查响应格式 - 后端直接返回 SubscriptionChangeResponse
        if (response && response.status === 'success') {
          alert(response.message || '升级成功！');
          setShowPaymentDialog(false);
          setShowUpgradeDialog(false);
          // 刷新订阅状态和套餐数据
          await refreshAfterUpgrade(selectedDuration);
        } else {
          throw new Error('升级失败');
        }
      } else {
        // 新用户购买 或 不同套餐购买 - 走完整购买流程（创建订单）

        // 新用户购买流程：订单 → 支付 → 订阅

        // 1. 创建订单
        const serviceMonths = selectedDuration === 'monthly' ? 1 :
                             selectedDuration === 'quarterly' ? 3 :
                             selectedDuration === 'yearly' ? 12 : 12;

        const orderData = {
          plan_id: selectedPlan,
          service_months: serviceMonths,
          customer_note: '企业用户购买套餐',
          auto_renewal: false
        };

        const orderResponse = await orderService.createSubscriptionOrder(orderData);

        // 检查响应格式 - 后端返回 { success: true, data: {...}, message: "..." }
        if (!orderResponse || !orderResponse.success || !orderResponse.data || !orderResponse.data.id) {

          throw new Error('订单创建失败');
        }

        // 后端已经自动完成支付，直接处理成功逻辑

        
        // 检查订单状态是否为已支付
        if (orderResponse.data.order_status === 'paid') {
          alert(orderResponse.message || '订阅成功！');
          setShowPaymentDialog(false);
          setShowUpgradeDialog(false);

          // 重新获取当前订阅状态和套餐数据
          await fetchCurrentSubscription();
          await fetchAvailablePlans(selectedDuration);
        } else {

          throw new Error('订单支付未完成');
        }
      }
    } catch (error) {
      let errorMessage = currentSubscription ? '升级失败：' : '订阅失败：';

      if (error.response && error.response.data && error.response.data.detail) {
        const detail = error.response.data.detail;
        if (detail.includes('already has an active subscription')) {
          errorMessage = '您已经有一个活跃的订阅，无法重复订阅。如需升级，请使用升级功能。';
        } else {
          errorMessage += detail;
        }
      } else {
        errorMessage += (error.message || '未知错误');
      }

      alert(errorMessage);
    } finally {
      setIsProcessingPayment(false);
    }
  };

  // 处理续费
  const handleRenewalClick = async () => {
    if (!currentSubscription) {
      alert('没有找到当前订阅信息');
      return;
    }

    try {
      const renewalData = {
        subscription_id: currentSubscription.id,
        billing_cycle: currentSubscription.billing_cycle,
        payment_method: 'default',
        apply_discount: true
      };

      const response = await subscriptionService.renewSubscription(renewalData);

      if (response) {
        alert('续费成功！');
        // 重新获取订阅信息
        await fetchCurrentSubscription();
        await fetchAvailablePlans(selectedDuration);
      }
    } catch (error) {
      console.error('续费失败:', error);
      alert('续费失败：' + (error.response?.data?.detail || error.message));
    }
  };

  // AI内容创作相关状态
  const [selectedTemplate, setSelectedTemplate] = useState('专业正式');
  const [inputText, setInputText] = useState('');
  const [generatedContent, setGeneratedContent] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [publishDialogOpen, setPublishDialogOpen] = useState(false);
  const [selectedChannels, setSelectedChannels] = useState([]);
  const [chatMessages, setChatMessages] = useState([
    {
      id: 1,
      type: 'system',
      content: '您好！我是AI内容创作助手。请告诉我您想要创作什么类型的内容，我会为您生成高质量的文章。',
      timestamp: new Date()
    }
  ]);
  
  // 项目管理分页状态
  const [currentProjectPage, setCurrentProjectPage] = useState(1);
  
  // 项目列表
  const projectList = [
    '豆包SEO优化',
    'Kimi内容优化', 
    'ChatGPT排名提升',
    '文心一言优化',
    '通义千问推广',
    '讯飞星火项目',
    'Claude优化方案',
    'Gemini推广计划'
  ];

  // AI优化策略相关状态
  const [strategyList, setStrategyList] = useState([
    {
      id: 1,
      name: '豆包SEO优化策略',
      status: 'active',
      projects: ['豆包SEO优化', 'Kimi内容优化'],
      config: {
        modelPreference: 'quality',
        keywordDensity: 'medium',
        contentStyle: 'professional',
        monitoringFrequency: 'daily',
        autoOptimization: true,
        // 添加模板参数配置
        templateParameters: [
          { key: 'keyword', label: '关键词', required: true, placeholder: '请输入目标关键词' },
          { key: 'title', label: '标题', required: true, placeholder: '请输入文章标题' },
          { key: 'intro', label: '引言', required: false, placeholder: '请输入引言内容' },
          { key: 'conclusion', label: '结论', required: false, placeholder: '请输入结论内容' }
        ]
      },
      createdAt: '2024-01-15',
      updatedAt: '2024-01-16'
    },
    {
      id: 2,
      name: '通用内容策略',
      status: 'active',
      projects: ['ChatGPT排名提升'],
      config: {
        modelPreference: 'balanced',
        keywordDensity: 'low',
        contentStyle: 'casual',
        monitoringFrequency: 'hourly',
        autoOptimization: false,
        templateParameters: [
          { key: 'topic', label: '主题', required: true, placeholder: '请输入文章主题' },
          { key: 'audience', label: '目标受众', required: false, placeholder: '请输入目标受众' },
          { key: 'tone', label: '语调风格', required: false, placeholder: '请输入语调风格' }
        ]
      },
      createdAt: '2024-01-10',
      updatedAt: '2024-01-14'
    },
    {
      id: 3,
      name: '高频更新策略',
      status: 'inactive',
      projects: ['文心一言优化', '通义千问推广'],
      config: {
        modelPreference: 'speed',
        keywordDensity: 'high',
        contentStyle: 'creative',
        monitoringFrequency: 'realtime',
        autoOptimization: true,
        templateParameters: [
          { key: 'product', label: '产品名称', required: true, placeholder: '请输入产品名称' },
          { key: 'feature', label: '核心功能', required: true, placeholder: '请输入核心功能' },
          { key: 'benefit', label: '用户收益', required: false, placeholder: '请输入用户收益' },
          { key: 'cta', label: '行动号召', required: false, placeholder: '请输入行动号召' }
        ]
      },
      createdAt: '2024-01-05',
      updatedAt: '2024-01-12'
    },
    {
      id: 4,
      name: '精准营销策略',
      status: 'active',
      projects: ['讯飞星火项目'],
      config: {
        modelPreference: 'quality',
        keywordDensity: 'high',
        contentStyle: 'professional',
        monitoringFrequency: 'hourly',
        autoOptimization: true,
        templateParameters: [
          { key: 'brand', label: '品牌名称', required: true, placeholder: '请输入品牌名称' },
          { key: 'campaign', label: '营销活动', required: true, placeholder: '请输入营销活动名称' },
          { key: 'target', label: '目标群体', required: false, placeholder: '请输入目标群体' },
          { key: 'offer', label: '优惠信息', required: false, placeholder: '请输入优惠信息' }
        ]
      },
      createdAt: '2024-01-08',
      updatedAt: '2024-01-15'
    },
    {
      id: 5,
      name: '长尾关键词策略',
      status: 'active',
      projects: ['Claude优化方案', 'Gemini推广计划'],
      config: {
        modelPreference: 'balanced',
        keywordDensity: 'medium',
        contentStyle: 'creative',
        monitoringFrequency: 'daily',
        autoOptimization: false,
      },
      createdAt: '2024-01-06',
      updatedAt: '2024-01-13'
    },
    {
      id: 6,
      name: '品牌建设策略',
      status: 'active',
      projects: ['豆包SEO优化'],
      config: {
        modelPreference: 'quality',
        keywordDensity: 'low',
        contentStyle: 'professional',
        monitoringFrequency: 'weekly',
        autoOptimization: true,
      },
      createdAt: '2024-01-04',
      updatedAt: '2024-01-11'
    },
    {
      id: 7,
      name: '快速响应策略',
      status: 'inactive',
      projects: [],
      config: {
        modelPreference: 'speed',
        keywordDensity: 'low',
        contentStyle: 'casual',
        monitoringFrequency: 'realtime',
        autoOptimization: true,
      },
      createdAt: '2024-01-03',
      updatedAt: '2024-01-10'
    },
    {
      id: 8,
      name: '深度内容策略',
      status: 'active',
      projects: ['ChatGPT排名提升', '文心一言优化'],
      config: {
        modelPreference: 'quality',
        keywordDensity: 'medium',
        contentStyle: 'professional',
        monitoringFrequency: 'daily',
        autoOptimization: false,
      },
      createdAt: '2024-01-02',
      updatedAt: '2024-01-09'
    }
  ]);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [selectedStrategy, setSelectedStrategy] = useState(null);
  const [newStrategyDialogOpen, setNewStrategyDialogOpen] = useState(false);
  const [selectedProject, setSelectedProject] = useState('all'); // 选中的项目过滤
  const [dialogTabValue, setDialogTabValue] = useState(0); // 对话框标签页
  const [strategyPage, setStrategyPage] = useState(1); // 策略分页

  // 模板参数管理状态
  const [templateTextFieldRef, setTemplateTextFieldRef] = useState(null);
  const [selectedParameter, setSelectedParameter] = useState('');
  const [newParameter, setNewParameter] = useState({
    key: '',
    label: '',
    required: false,
    placeholder: ''
  });

  // 企业设置相关状态
  // 导航菜单配置
  const navigationItems = [
    {
      id: 'dashboard',
      title: '企业概览',
      icon: <Dashboard />,
      path: 'dashboard'
    },
    {
      id: 'workspace-group',
      title: '工作空间',
      icon: <AccountTree />,
      children: [
        {
          id: 'project-management',
          title: '项目管理',
          icon: <Assignment />,
          path: 'workspace'
        },
        {
          id: 'geo-monitoring',
          title: 'GEO监控',
          icon: <Assessment />,
          path: 'geo-monitoring'
        },
        {
          id: 'ai-optimization',
          title: 'AI优化策略',
          icon: <AutoAwesome />,
          path: 'ai-optimization'
        }
      ]
    },
    {
      id: 'content-group',
      title: '内容管理',
      icon: <Article />,
      children: [
        {
          id: 'ai-content-creation',
          title: 'AI创作',
          icon: <AutoAwesome />,
          path: 'ai-content-creation'
        },
        {
          id: 'content-list',
          title: '内容列表',
          icon: <Description />,
          path: 'content-management'
        },
        {
          id: 'writing-templates',
          title: '写作模板',
          icon: <Assignment />,
          path: 'writing-templates'
        },
        {
          id: 'knowledge-base',
          title: '知识库管理',
          icon: <MenuBook />,
          path: 'knowledge-base'
        }
      ]
    },
    {
      id: 'announcements',
      title: '最新公告',
      icon: <Campaign />,
      path: 'announcements'
    },
    {
      id: 'settings',
      title: '企业设置',
      icon: <Settings />,
      path: 'settings'
    }
  ];

  const handleMenuClick = (item) => {
    if (item.children) {
      setExpandedMenus(prev => ({
        ...prev,
        [item.id]: !prev[item.id]
      }));
    } else {
      setCurrentPage(item.path);
    }
  };

  const handleSubMenuClick = (path) => {
    setCurrentPage(path);
  };

  // 获取页面标题
  const getPageTitle = () => {
    const pageTitles = {
      'dashboard': '企业概览',
      'workspace': '项目管理',
      'geo-monitoring': 'GEO监控',
      'ai-optimization': 'AI优化策略',
      'ai-content-creation': 'AI创作',
      'content-management': '内容列表',
      'writing-templates': '写作模板',
      'knowledge-base': '知识库管理',
      'announcements': '最新公告',
      'settings': '企业设置'
    };
    return pageTitles[currentPage] || '企业控制中心';
  };

  // 获取页面描述
  const getPageDescription = () => {
    const pageDescriptions = {
      'dashboard': '查看企业整体运营状况和关键指标',
      'workspace': '管理项目和协作的统一工作空间',
      'geo-monitoring': '智能对话式GEO监控和数据分析',
      'ai-optimization': '优化AI内容生成策略，提升内容质量和效率',
      'ai-content-creation': '使用AI技术快速创作高质量内容',
      'content-management': '管理和组织所有内容资源',
      'writing-templates': '自定义AI写作模板和内容模板',
      'knowledge-base': '管理企业知识库，为AI内容生成提供专业资料',
      'announcements': '查看系统公告和重要通知信息',
      'settings': '配置企业基本信息和安全设置'
    };
    return pageDescriptions[currentPage] || '管理您的企业业务';
  };

  // 渲染工作空间页面
  const renderWorkspaceContent = () => (
    <div data-testid="workspace-manager">
      <WorkspaceManager />
    </div>
  );
  
  // 处理策略编辑
  const handleEditStrategy = (strategy) => {
    setSelectedStrategy(strategy);
    setEditDialogOpen(true);
  };

  // 插入参数到模板文本框
  const insertParameterToTemplate = () => {
    if (!selectedParameter || !templateTextFieldRef) return;

    const textField = templateTextFieldRef;
    const cursorPosition = textField.selectionStart || 0;
    const currentValue = selectedStrategy.config?.writingTemplate || '';
    const parameterText = `{${selectedParameter}}`;

    const newValue = currentValue.slice(0, cursorPosition) + parameterText + currentValue.slice(cursorPosition);

    setSelectedStrategy({
      ...selectedStrategy,
      config: { ...selectedStrategy.config, writingTemplate: newValue }
    });

    // 重新聚焦并设置光标位置
    setTimeout(() => {
      textField.focus();
      textField.setSelectionRange(cursorPosition + parameterText.length, cursorPosition + parameterText.length);
    }, 0);
  };

  // 添加新参数
  const addNewParameter = () => {
    if (!newParameter.key || !newParameter.label) return;

    // 检查参数名是否已存在
    const existingParams = selectedStrategy.config?.templateParameters || [];
    if (existingParams.some(param => param.key === newParameter.key)) {
      alert('参数名已存在，请使用不同的参数名');
      return;
    }

    const updatedParams = [...existingParams, { ...newParameter }];
    setSelectedStrategy({
      ...selectedStrategy,
      config: { ...selectedStrategy.config, templateParameters: updatedParams }
    });

    // 重置表单
    setNewParameter({
      key: '',
      label: '',
      required: false,
      placeholder: ''
    });
  };

  // 处理新建策略
  const handleCreateStrategy = () => {
    setSelectedStrategy({
      name: '',
      status: 'active',
      projects: [],
      config: {
        modelPreference: 'balanced',
        keywordDensity: 'medium',
        contentStyle: 'professional',
        monitoringFrequency: 'daily',
        autoOptimization: false,
      }
    });
    setNewStrategyDialogOpen(true);
  };

  // 处理保存策略
  const handleSaveStrategy = () => {
    if (newStrategyDialogOpen) {
      // 添加新策略
      const newStrategy = {
        ...selectedStrategy,
        id: strategyList.length + 1,
        createdAt: new Date().toLocaleDateString(),
        updatedAt: new Date().toLocaleDateString()
      };
      setStrategyList([...strategyList, newStrategy]);
      setNewStrategyDialogOpen(false);
    } else {
      // 更新现有策略
      setStrategyList(strategyList.map(s => 
        s.id === selectedStrategy.id ? selectedStrategy : s
      ));
      setEditDialogOpen(false);
    }
    setSelectedStrategy(null);
  };

  // 渲染AI优化策略页面
  const renderAIOptimizationContent = () => {
    return (
    <>
    <Box sx={{ 
      width: '100%', 
      minHeight: '100vh',
      backgroundColor: '#f8f9fa' 
    }}>
      {/* 顶部标题区域 */}
      <Box sx={{
        borderBottom: '1px solid #e5e7eb',
        backgroundColor: 'white'
      }}>
        <Box sx={{ px: 4, py: 4 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Box>
              <Typography variant="h4" sx={{ 
                fontWeight: 700, 
                color: '#1a1a1a', 
                mb: 0.5,
                fontSize: { xs: '1.75rem', md: '2rem' }
              }}>
                AI优化策略管理
              </Typography>
              <Typography variant="body1" sx={{ 
                color: '#6b7280',
                fontSize: '1rem' 
              }}>
                管理和配置AI优化策略，绑定到不同项目
              </Typography>
            </Box>
            <Button
              variant="contained"
              startIcon={<Add />}
              onClick={handleCreateStrategy}
              sx={{
                backgroundColor: '#3b82f6',
                '&:hover': { backgroundColor: '#2563eb' }
              }}
            >
              新建策略
            </Button>
          </Box>
        </Box>
      </Box>

      {/* 策略列表内容区域 */}
      <Box sx={{ px: 4, py: 4 }}>
        {/* 项目选择和分页控制栏 */}
        <Box sx={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center',
          mb: 3,
          pb: 2,
          borderBottom: '1px solid #e5e7eb'
        }}>
          {/* 左侧：项目选择 */}
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Typography variant="body2" sx={{ color: '#6b7280', fontWeight: 500 }}>
              选择项目：
            </Typography>
            <Select
              value={selectedProject}
              onChange={(e) => {
                setSelectedProject(e.target.value);
                setStrategyPage(1); // 切换项目时重置到第一页
              }}
              size="small"
              sx={{ 
                minWidth: 200,
                '& .MuiOutlinedInput-notchedOutline': {
                  borderColor: '#e5e7eb'
                }
              }}
            >
              <MenuItem value="all">所有项目</MenuItem>
              {projectList.map((project) => (
                <MenuItem key={project} value={project}>{project}</MenuItem>
              ))}
            </Select>
            {selectedProject !== 'all' && (
              <Chip
                label={`${(() => {
                  const filteredStrategies = strategyList.filter(s => 
                    s.projects.includes(selectedProject)
                  );
                  return filteredStrategies.length;
                })()} 个策略`}
                size="small"
                sx={{
                  backgroundColor: '#eff6ff',
                  color: '#3b82f6',
                  fontWeight: 500
                }}
              />
            )}
          </Box>

          {/* 右侧：分页控件 */}
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Button
              size="small"
              variant="outlined"
              onClick={() => setStrategyPage(prev => Math.max(1, prev - 1))}
              disabled={strategyPage === 1}
              sx={{
                minWidth: 'auto',
                px: 2,
                borderColor: '#e5e7eb',
                color: '#6b7280',
                '&:hover': {
                  borderColor: '#9ca3af',
                  backgroundColor: '#f9fafb'
                },
                '&.Mui-disabled': {
                  borderColor: '#f3f4f6',
                  color: '#d1d5db'
                }
              }}
            >
              上一页
            </Button>

            {/* 页码按钮 */}
            <Box sx={{ display: 'flex', gap: 0.5 }}>
              {(() => {
                const filteredStrategies = selectedProject === 'all' 
                  ? strategyList 
                  : strategyList.filter(s => s.projects.includes(selectedProject));
                const totalPages = Math.ceil(filteredStrategies.length / 6);
                const pages = [];
                
                for (let i = 1; i <= Math.min(totalPages, 5); i++) {
                  pages.push(
                    <Button
                      key={i}
                      size="small"
                      variant={strategyPage === i ? 'contained' : 'text'}
                      onClick={() => setStrategyPage(i)}
                      sx={{
                        minWidth: 32,
                        height: 32,
                        p: 0,
                        borderRadius: 1,
                        backgroundColor: strategyPage === i ? '#3b82f6' : 'transparent',
                        color: strategyPage === i ? 'white' : '#6b7280',
                        '&:hover': {
                          backgroundColor: strategyPage === i ? '#2563eb' : '#f3f4f6'
                        }
                      }}
                    >
                      {i}
                    </Button>
                  );
                }
                
                if (totalPages > 5 && strategyPage < totalPages) {
                  pages.push(
                    <Typography key="dots" sx={{ px: 1, color: '#9ca3af' }}>...</Typography>
                  );
                  pages.push(
                    <Button
                      key={totalPages}
                      size="small"
                      variant={strategyPage === totalPages ? 'contained' : 'text'}
                      onClick={() => setStrategyPage(totalPages)}
                      sx={{
                        minWidth: 32,
                        height: 32,
                        p: 0,
                        borderRadius: 1,
                        backgroundColor: strategyPage === totalPages ? '#3b82f6' : 'transparent',
                        color: strategyPage === totalPages ? 'white' : '#6b7280',
                        '&:hover': {
                          backgroundColor: strategyPage === totalPages ? '#2563eb' : '#f3f4f6'
                        }
                      }}
                    >
                      {totalPages}
                    </Button>
                  );
                }
                
                return pages;
              })()}
            </Box>

            <Button
              size="small"
              variant="outlined"
              onClick={() => {
                const filteredStrategies = selectedProject === 'all' 
                  ? strategyList 
                  : strategyList.filter(s => s.projects.includes(selectedProject));
                const totalPages = Math.ceil(filteredStrategies.length / 6);
                setStrategyPage(prev => Math.min(totalPages, prev + 1));
              }}
              disabled={(() => {
                const filteredStrategies = selectedProject === 'all' 
                  ? strategyList 
                  : strategyList.filter(s => s.projects.includes(selectedProject));
                const totalPages = Math.ceil(filteredStrategies.length / 6);
                return strategyPage === totalPages;
              })()}
              sx={{
                minWidth: 'auto',
                px: 2,
                borderColor: '#e5e7eb',
                color: '#6b7280',
                '&:hover': {
                  borderColor: '#9ca3af',
                  backgroundColor: '#f9fafb'
                },
                '&.Mui-disabled': {
                  borderColor: '#f3f4f6',
                  color: '#d1d5db'
                }
              }}
            >
              下一页
            </Button>
          </Box>
        </Box>

        {/* 策略卡片网格 */}
        <Grid container spacing={2}>
          {/* 策略卡片列表 */}
          {(() => {
            // 根据选中项目过滤策略
            const filteredStrategies = selectedProject === 'all' 
              ? strategyList 
              : strategyList.filter(strategy => strategy.projects.includes(selectedProject));
            
            // 分页处理
            const strategiesPerPage = 6;
            const startIndex = (strategyPage - 1) * strategiesPerPage;
            const endIndex = startIndex + strategiesPerPage;
            const paginatedStrategies = filteredStrategies.slice(startIndex, endIndex);

            return paginatedStrategies.map((strategy) => (
              <Grid item xs={12} md={6} lg={4} key={strategy.id}>
              <Card sx={{
                borderRadius: 1.5,
                border: '1px solid #e5e7eb',
                boxShadow: 'none',
                height: '100%',
                transition: 'all 0.2s',
                '&:hover': {
                  boxShadow: '0 2px 4px -1px rgb(0 0 0 / 0.06)',
                  borderColor: '#d1d5db',
                  transform: 'translateY(-1px)'
                }
              }}>
                <CardContent sx={{ p: 1.5 }}>
                  <Box sx={{ mb: 1 }}>
                    <Typography variant="subtitle2" sx={{ 
                      fontWeight: 600, 
                      color: '#1a1a1a',
                      mb: 0.25,
                      fontSize: '0.875rem'
                    }}>
                      {strategy.name}
                    </Typography>
                    <Chip 
                      label={strategy.status === 'active' ? '运行中' : '已停用'} 
                      size="small" 
                      sx={{ 
                        backgroundColor: strategy.status === 'active' ? '#dcfce7' : '#fee2e2',
                        color: strategy.status === 'active' ? '#15803d' : '#991b1b',
                        fontWeight: 600,
                        fontSize: '0.65rem',
                        height: 18,
                        '& .MuiChip-label': { px: 0.75 }
                      }} 
                    />
                  </Box>

                  <Divider sx={{ my: 1 }} />

                  {/* 配置摘要 */}
                  <Box sx={{ mb: 1 }}>
                    <Typography variant="caption" sx={{ color: '#6b7280', fontWeight: 600, fontSize: '0.65rem' }}>
                      策略配置
                    </Typography>
                    <Box sx={{ mt: 0.25, display: 'flex', flexDirection: 'column', gap: 0.15 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                        <Typography variant="caption" sx={{ color: '#9ca3af', fontSize: '0.65rem' }}>模型:</Typography>
                        <Typography variant="caption" sx={{ color: '#374151', fontWeight: 500, fontSize: '0.65rem' }}>
                          {strategy.config.modelPreference === 'speed' ? '速度' : 
                           strategy.config.modelPreference === 'balanced' ? '平衡' : '质量'}
                        </Typography>
                      </Box>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                        <Typography variant="caption" sx={{ color: '#9ca3af', fontSize: '0.65rem' }}>关键词:</Typography>
                        <Typography variant="caption" sx={{ color: '#374151', fontWeight: 500, fontSize: '0.65rem' }}>
                          {strategy.config.keywordDensity === 'low' ? '低' : 
                           strategy.config.keywordDensity === 'medium' ? '中' : '高'}密度
                        </Typography>
                      </Box>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                        <Typography variant="caption" sx={{ color: '#9ca3af', fontSize: '0.65rem' }}>监控:</Typography>
                        <Typography variant="caption" sx={{ color: '#374151', fontWeight: 500, fontSize: '0.65rem' }}>
                          {strategy.config.monitoringFrequency === 'realtime' ? '实时' :
                           strategy.config.monitoringFrequency === 'hourly' ? '每小时' :
                           strategy.config.monitoringFrequency === 'daily' ? '每日' : '每周'}
                        </Typography>
                      </Box>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                        <Typography variant="caption" sx={{ color: '#9ca3af', fontSize: '0.65rem' }}>自动:</Typography>
                        <Typography variant="caption" sx={{ 
                          color: strategy.config.autoOptimization ? '#10b981' : '#ef4444', 
                          fontWeight: 500,
                          fontSize: '0.65rem'
                        }}>
                          {strategy.config.autoOptimization ? '开启' : '关闭'}
                        </Typography>
                      </Box>
                    </Box>
                  </Box>


                  <Box sx={{ mt: 1, pt: 0.75, borderTop: '1px solid #f3f4f6' }}>
                    <Typography variant="caption" sx={{ color: '#9ca3af', fontSize: '0.6rem', display: 'block', mb: 0.5 }}>
                      更新: {strategy.updatedAt}
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 0.25 }}>
                      <Button
                        size="small"
                        variant="outlined"
                        startIcon={<Edit sx={{ fontSize: 12 }} />}
                        sx={{
                          flex: 1,
                          borderColor: '#e5e7eb',
                          color: '#374151',
                          fontSize: '0.65rem',
                          py: 0.25,
                          px: 0.5,
                          minHeight: 24,
                          textTransform: 'none',
                          '&:hover': {
                            borderColor: '#3b82f6',
                            backgroundColor: '#eff6ff',
                            color: '#3b82f6'
                          }
                        }}
                        onClick={() => handleEditStrategy(strategy)}
                      >
                        编辑
                      </Button>
                      <Button
                        size="small"
                        variant="outlined"
                        sx={{
                          flex: 1,
                          borderColor: strategy.status === 'active' ? '#fbbf24' : '#10b981',
                          color: strategy.status === 'active' ? '#92400e' : '#065f46',
                          backgroundColor: strategy.status === 'active' ? '#fef3c7' : '#d1fae5',
                          fontSize: '0.65rem',
                          py: 0.25,
                          px: 0.5,
                          minHeight: 24,
                          textTransform: 'none',
                          '&:hover': {
                            borderColor: strategy.status === 'active' ? '#f59e0b' : '#059669',
                            backgroundColor: strategy.status === 'active' ? '#fde68a' : '#a7f3d0'
                          }
                        }}
                        onClick={() => {
                          const updatedStrategy = { ...strategy, status: strategy.status === 'active' ? 'inactive' : 'active' };
                          setStrategyList(strategyList.map(s => s.id === strategy.id ? updatedStrategy : s));
                        }}
                      >
                        {strategy.status === 'active' ? '停用' : '启用'}
                      </Button>
                      <Button
                        size="small"
                        variant="outlined"
                        sx={{
                          flex: 1,
                          borderColor: '#fca5a5',
                          color: '#991b1b',
                          backgroundColor: '#fee2e2',
                          fontSize: '0.65rem',
                          py: 0.25,
                          px: 0.5,
                          minHeight: 24,
                          textTransform: 'none',
                          '&:hover': {
                            borderColor: '#ef4444',
                            backgroundColor: '#fecaca',
                            color: '#7f1d1d'
                          }
                        }}
                        onClick={() => {
                          if (window.confirm(`确定要删除策略"${strategy.name}"吗？`)) {
                            setStrategyList(strategyList.filter(s => s.id !== strategy.id));
                          }
                        }}
                      >
                        删除
                      </Button>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ));
          })()}
        </Grid>
      </Box>
    </Box>

    {/* 策略编辑对话框 */}
    <Dialog
      open={editDialogOpen || newStrategyDialogOpen}
      onClose={() => {
        setEditDialogOpen(false);
        setNewStrategyDialogOpen(false);
        setSelectedStrategy(null);
        setDialogTabValue(0);
      }}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          maxHeight: '90vh'
        }
      }}
    >
      <DialogTitle sx={{ 
        p: 2.5,
        backgroundColor: '#f0f7ff',
        borderBottom: '1px solid #dbeafe'
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
            <Settings sx={{ color: '#3b82f6', fontSize: 28 }} />
            <Typography variant="h5" sx={{ 
              fontWeight: 600,
              color: '#1e40af'
            }}>
              {newStrategyDialogOpen ? '创建优化策略' : '配置优化策略'}
            </Typography>
          </Box>
          <IconButton 
            onClick={() => {
              setEditDialogOpen(false);
              setNewStrategyDialogOpen(false);
              setSelectedStrategy(null);
              setDialogTabValue(0);
            }} 
            size="small"
          >
            <Close />
          </IconButton>
        </Box>
      </DialogTitle>
      <DialogContent sx={{ backgroundColor: '#fafbfc', py: 2, px: 2 }}>
        {selectedStrategy && (
          <Box>
            {/* 策略名称输入区 */}
            <Box sx={{ 
              mb: 3
            }}>
              <Grid container spacing={2} alignItems="center">
                <Grid item xs={12} md={8}>
                  <TextField
                    fullWidth
                    label="策略名称"
                    value={selectedStrategy.name || ''}
                    onChange={(e) => setSelectedStrategy({...selectedStrategy, name: e.target.value})}
                    size="small"
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Typography sx={{ fontSize: '1.2rem' }}>📝</Typography>
                        </InputAdornment>
                      ),
                    }}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        backgroundColor: 'white',
                        '&:hover fieldset': {
                          borderColor: '#3b82f6',
                        },
                        '&.Mui-focused fieldset': {
                          borderColor: '#3b82f6',
                        },
                      },
                    }}
                  />
                </Grid>
                <Grid item xs={12} md={4}>
                  <Chip 
                    label={selectedStrategy.status === 'active' ? '✅ 已启用' : '⏸️ 已停用'}
                    color={selectedStrategy.status === 'active' ? 'success' : 'default'}
                    size="small"
                    sx={{ ml: 1 }}
                  />
                  <Chip 
                    label={`📅 ${selectedStrategy.updatedAt || '今天'}`}
                    variant="outlined"
                    size="small"
                    sx={{ ml: 1 }}
                  />
                </Grid>
              </Grid>
            </Box>
            
            {/* 标签栏 */}
            <Box sx={{ backgroundColor: '#f0f7ff', borderRadius: 2, p: 0.5, mb: 3 }}>
              <Tabs
                value={dialogTabValue}
                onChange={(e, newValue) => setDialogTabValue(newValue)}
                variant="fullWidth"
                sx={{ 
                  '& .MuiTab-root': {
                    minHeight: 56,
                    fontSize: '0.875rem',
                    fontWeight: 500,
                    textTransform: 'none',
                    color: '#6b7280',
                    '&.Mui-selected': {
                      color: 'white',
                      backgroundColor: '#3b82f6',
                      borderRadius: 1.5,
                      fontWeight: 600,
                    },
                  },
                  '& .MuiTabs-indicator': {
                    display: 'none',
                  },
                }}
              >
                <Tab label="基础配置" icon={<Dashboard sx={{ fontSize: 18 }} />} iconPosition="start" />
                <Tab label="关键词优化" icon={<Search sx={{ fontSize: 18 }} />} iconPosition="start" />
                <Tab label="AI平台" icon={<AutoAwesome sx={{ fontSize: 18 }} />} iconPosition="start" />
                <Tab label="内容创作" icon={<Edit sx={{ fontSize: 18 }} />} iconPosition="start" />
                <Tab label="参数管理" icon={<Settings sx={{ fontSize: 18 }} />} iconPosition="start" />
                <Tab label="监控告警" icon={<Analytics sx={{ fontSize: 18 }} />} iconPosition="start" />
              </Tabs>
            </Box>

            {/* 基础配置 Tab - 合并原知识库和基础设置 */}
            {dialogTabValue === 0 && (
              <Box>
                <Box sx={{ mb: 3 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Box sx={{
                      width: 40,
                      height: 40,
                      borderRadius: 2,
                      backgroundColor: '#eff6ff',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      mr: 2
                    }}>
                      <Typography sx={{ fontSize: '1.5rem' }}>📚</Typography>
                    </Box>
                    <Box>
                      <Typography variant="h6" sx={{ fontWeight: 600, color: '#1a1a1a' }}>
                        基础配置
                      </Typography>
                      <Typography variant="body2" sx={{ color: '#6b7280' }}>
                        设置策略的核心参数和知识库绑定
                      </Typography>
                    </Box>
                  </Box>
                </Box>
                
                <Grid container spacing={3}>
                  {/* 知识库配置卡片 */}
                  <Grid item xs={12} md={6}>
                    <Card variant="outlined" sx={{ 
                      borderRadius: 2, 
                      border: '1px solid #e5e7eb',
                      '&:hover': {
                        borderColor: '#3b82f6',
                        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                      }
                    }}>
                      <CardContent>
                        <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2, color: '#374151' }}>
                          📖 知识库设置
                        </Typography>
                        <FormControl fullWidth size="small" sx={{ mb: 2 }}>
                          <InputLabel>选择知识库（可多选）</InputLabel>
                          <Select
                            multiple
                            value={selectedStrategy.config?.knowledgeBase || []}
                            label="选择知识库（可多选）"
                            onChange={(e) => setSelectedStrategy({
                              ...selectedStrategy,
                              config: {...selectedStrategy.config, knowledgeBase: e.target.value}
                            })}
                            renderValue={(selected) => (
                              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                                {selected.map((value) => (
                                  <Chip key={value} label={
                                    value === 'industry' ? '🏭 行业知识库' :
                                    value === 'product' ? '📦 产品知识库' :
                                    value === 'competitor' ? '🎯 竞品知识库' :
                                    '✨ 自定义知识库'
                                  } size="small" />
                                ))}
                              </Box>
                            )}
                          >
                            <MenuItem value="industry">
                              <Checkbox checked={(selectedStrategy.config?.knowledgeBase || []).indexOf('industry') > -1} />
                              <ListItemText primary="🏭 行业知识库" />
                            </MenuItem>
                            <MenuItem value="product">
                              <Checkbox checked={(selectedStrategy.config?.knowledgeBase || []).indexOf('product') > -1} />
                              <ListItemText primary="📦 产品知识库" />
                            </MenuItem>
                            <MenuItem value="competitor">
                              <Checkbox checked={(selectedStrategy.config?.knowledgeBase || []).indexOf('competitor') > -1} />
                              <ListItemText primary="🎯 竞品知识库" />
                            </MenuItem>
                            <MenuItem value="custom">
                              <Checkbox checked={(selectedStrategy.config?.knowledgeBase || []).indexOf('custom') > -1} />
                              <ListItemText primary="✨ 自定义知识库" />
                            </MenuItem>
                          </Select>
                        </FormControl>
                        <Alert severity="info" sx={{ mt: 2 }}>
                          <Typography variant="body2" sx={{ fontWeight: 500 }}>
                            知识库说明
                          </Typography>
                          <Typography variant="caption" sx={{ display: 'block', mt: 0.5 }}>
                            知识库是关键词挖掘和文字创作的重要依据。系统将基于您选择的知识库：
                          </Typography>
                          <Box component="ul" sx={{ m: 0, pl: 2, mt: 1 }}>
                            <Typography component="li" variant="caption">智能挖掘相关关键词和长尾词</Typography>
                            <Typography component="li" variant="caption">生成符合行业特征的内容模板</Typography>
                            <Typography component="li" variant="caption">优化内容的专业性和相关性</Typography>
                          </Box>
                          <Typography variant="caption" sx={{ display: 'block', mt: 1, fontWeight: 500 }}>
                            请认真选择与您业务相关的知识库，以获得最佳优化效果。
                          </Typography>
                        </Alert>
                      </CardContent>
                    </Card>
                  </Grid>
                  
                  {/* 优化模式卡片 */}
                  <Grid item xs={12} md={6}>
                    <Card variant="outlined" sx={{ 
                      borderRadius: 2, 
                      border: '1px solid #e5e7eb',
                      '&:hover': {
                        borderColor: '#3b82f6',
                        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                      }
                    }}>
                      <CardContent>
                        <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2, color: '#374151' }}>
                          ⚙️ 优化模式
                        </Typography>
                        <FormControl fullWidth size="small" sx={{ mb: 2 }}>
                          <InputLabel>模型倾向</InputLabel>
                          <Select
                            value={selectedStrategy.config?.modelPreference || 'balanced'}
                            label="模型倾向"
                            onChange={(e) => setSelectedStrategy({
                              ...selectedStrategy,
                              config: {...selectedStrategy.config, modelPreference: e.target.value}
                            })}
                          >
                            <MenuItem value="speed">🚀 速度优先</MenuItem>
                            <MenuItem value="balanced">⚖️ 平衡模式</MenuItem>
                            <MenuItem value="quality">💎 质量优先</MenuItem>
                          </Select>
                        </FormControl>
                        <FormControlLabel
                          control={
                            <Switch
                              checked={selectedStrategy.config?.autoOptimization || false}
                              onChange={(e) => setSelectedStrategy({
                                ...selectedStrategy,
                                config: {...selectedStrategy.config, autoOptimization: e.target.checked}
                              })}
                              color="primary"
                            />
                          }
                          label={
                            <Box>
                              <Typography variant="body2" sx={{ fontWeight: 500 }}>
                                智能自动优化
                              </Typography>
                              <Typography variant="caption" sx={{ color: '#6b7280' }}>
                                根据效果自动调整策略
                              </Typography>
                            </Box>
                          }
                        />
                      </CardContent>
                    </Card>
                  </Grid>
                </Grid>
              </Box>
            )}

            {/* 关键词优化 Tab */}
            {dialogTabValue === 1 && (
              <Box>
                <Box sx={{ mb: 3 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Box sx={{
                      width: 40,
                      height: 40,
                      borderRadius: 2,
                      backgroundColor: '#fef3c7',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      mr: 2
                    }}>
                      <Typography sx={{ fontSize: '1.5rem' }}>🔍</Typography>
                    </Box>
                    <Box>
                      <Typography variant="h6" sx={{ fontWeight: 600, color: '#1a1a1a' }}>
                        关键词优化
                      </Typography>
                      <Typography variant="body2" sx={{ color: '#6b7280' }}>
                        配置核心关键词和SEO优化策略
                      </Typography>
                    </Box>
                  </Box>
                </Box>
                
                <Grid container spacing={3}>
                  {/* 需求输入区域 */}
                  <Grid item xs={12}>
                    <Card variant="outlined" sx={{ 
                      borderRadius: 2,
                      backgroundColor: '#f0f7ff',
                      border: '1px solid #93c5fd'
                    }}>
                      <CardContent>
                        <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2, color: '#1e40af' }}>
                          📝 需求描述
                        </Typography>
                        <TextField
                          fullWidth
                          size="small"
                          multiline
                          rows={3}
                          placeholder="请描述您的业务需求和目标，例如：
我们是一家AI技术公司，主要提供智能内容生成和SEO优化服务，目标客户是中小企业..."
                          value={selectedStrategy.config?.businessRequirement || ''}
                          onChange={(e) => setSelectedStrategy({
                            ...selectedStrategy,
                            config: {...selectedStrategy.config, businessRequirement: e.target.value}
                          })}
                          sx={{
                            '& .MuiOutlinedInput-root': {
                              backgroundColor: 'white',
                            }
                          }}
                        />
                        <Typography variant="caption" sx={{ color: '#64748b', mt: 1, display: 'block' }}>
                          详细的需求描述将帮助AI更准确地挖掘相关关键词
                        </Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                  
                  {/* AI挖掘的关键词列表 */}
                  <Grid item xs={12} md={6}>
                    <Card variant="outlined" sx={{ 
                      borderRadius: 2,
                      backgroundColor: '#fefce8',
                      border: '1px solid #fde047'
                    }}>
                      <CardContent>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                          <Typography variant="subtitle1" sx={{ fontWeight: 600, color: '#713f12' }}>
                            🤖 AI挖掘推荐
                          </Typography>
                          <Button
                            size="small"
                            variant="contained"
                            startIcon={<AutoAwesome />}
                            onClick={() => {
                              // 基于需求和知识库挖掘关键词
                              const requirement = selectedStrategy.config?.businessRequirement || '';
                              const knowledgeBase = selectedStrategy.config?.knowledgeBase || [];
                              
                              // 生成推荐关键词列表
                              const suggestedList = [
                                '基础关键词',
                                'AI内容优化',
                                'SEO智能分析',
                                '搜索排名提升',
                                '关键词研究工具',
                                '内容营销策略',
                                '长尾关键词',
                                'AI写作助手SEO',
                                '智能关键词挖掘',
                                '搜索意图分析',
                                '竞品关键词分析',
                                '高转化关键词',
                                '地域关键词',
                                requirement.includes('AI') ? 'AI技术服务' : '',
                                requirement.includes('企业') ? '企业级解决方案' : '',
                                knowledgeBase.includes('industry') ? '行业趋势分析' : '',
                                knowledgeBase.includes('product') ? '产品功能优化' : '',
                                knowledgeBase.includes('competitor') ? '竞品对比优势' : ''
                              ].filter(k => k);
                              
                              setSelectedStrategy({
                                ...selectedStrategy,
                                config: {
                                  ...selectedStrategy.config,
                                  suggestedKeywords: suggestedList
                                }
                              });
                              alert('AI关键词挖掘完成！已生成' + suggestedList.length + '个推荐关键词');
                            }}
                            sx={{ 
                              backgroundColor: '#fbbf24',
                              color: '#713f12',
                              '&:hover': {
                                backgroundColor: '#f59e0b'
                              }
                            }}
                          >
                            开始挖掘
                          </Button>
                        </Box>
                        <Box sx={{ 
                          maxHeight: 300, 
                          overflowY: 'auto',
                          p: 1,
                          backgroundColor: 'white',
                          borderRadius: 1
                        }}>
                          {(selectedStrategy.config?.suggestedKeywords || [
                            'AI智能优化',
                            'SEO排名算法',
                            '内容质量评分',
                            '关键词密度分析',
                            '搜索意图匹配'
                          ]).map((keyword, index) => (
                            <Box key={index} sx={{ 
                              display: 'flex', 
                              justifyContent: 'space-between',
                              alignItems: 'center',
                              p: 1,
                              mb: 0.5,
                              backgroundColor: '#fffbeb',
                              borderRadius: 1,
                              '&:hover': {
                                backgroundColor: '#fef3c7'
                              }
                            }}>
                              <Typography variant="body2">{keyword}</Typography>
                              <IconButton 
                                size="small"
                                onClick={() => {
                                  const currentKeywords = selectedStrategy.config?.coreKeywords || '';
                                  setSelectedStrategy({
                                    ...selectedStrategy,
                                    config: {
                                      ...selectedStrategy.config,
                                      coreKeywords: currentKeywords ? `${currentKeywords}\n${keyword}` : keyword
                                    }
                                  });
                                }}
                                sx={{ color: '#10b981' }}
                              >
                                <Add />
                              </IconButton>
                            </Box>
                          ))}
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                  
                  {/* 已选择的核心关键词 */}
                  <Grid item xs={12} md={6}>
                    <Card variant="outlined" sx={{ 
                      borderRadius: 2,
                      backgroundColor: '#f0fdf4',
                      border: '1px solid #86efac'
                    }}>
                      <CardContent>
                        <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2, color: '#14532d' }}>
                          ✅ 已选择关键词
                        </Typography>
                        <TextField
                          fullWidth
                          size="small"
                          multiline
                          rows={10}
                          placeholder="每行输入一个关键词，或从左侧推荐列表中添加"
                          value={selectedStrategy.config?.coreKeywords || ''}
                          onChange={(e) => setSelectedStrategy({
                            ...selectedStrategy,
                            config: {...selectedStrategy.config, coreKeywords: e.target.value}
                          })}
                          sx={{
                            '& .MuiOutlinedInput-root': {
                              backgroundColor: 'white',
                            }
                          }}
                        />
                        <Box sx={{ mt: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <Typography variant="caption" sx={{ color: '#64748b' }}>
                            已选择 {(selectedStrategy.config?.coreKeywords?.split('\n').filter(k => k.trim()).length || 0)} 个关键词
                          </Typography>
                          <Button
                            size="small"
                            variant="text"
                            onClick={() => setSelectedStrategy({
                              ...selectedStrategy,
                              config: {...selectedStrategy.config, coreKeywords: ''}
                            })}
                            sx={{ color: '#ef4444' }}
                          >
                            清空列表
                          </Button>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                </Grid>
              </Box>
            )}

            {/* AI平台 Tab */}
            {dialogTabValue === 2 && (
              <Box>
                <Box sx={{ mb: 3 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Box sx={{
                      width: 40,
                      height: 40,
                      borderRadius: 2,
                      backgroundColor: '#e0e7ff',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      mr: 2
                    }}>
                      <Typography sx={{ fontSize: '1.5rem' }}>🤖</Typography>
                    </Box>
                    <Box>
                      <Typography variant="h6" sx={{ fontWeight: 600, color: '#1a1a1a' }}>
                        AI平台配置
                      </Typography>
                      <Typography variant="body2" sx={{ color: '#6b7280' }}>
                        选择要优化的AI平台，并设置优先级权重
                      </Typography>
                    </Box>
                  </Box>
                </Box>
                
                <Grid container spacing={2}>
                  {[
                    { name: '豆包', id: 'doubao', icon: '🤖', color: '#4f46e5' },
                    { name: 'ChatGPT', id: 'chatgpt', icon: '💬', color: '#10b981' },
                    { name: 'Kimi', id: 'kimi', icon: '🎭', color: '#8b5cf6' },
                    { name: '文心一言', id: 'wenxin', icon: '📝', color: '#3b82f6' },
                    { name: '通义千问', id: 'tongyi', icon: '🔮', color: '#ec4899' },
                    { name: 'Claude', id: 'claude', icon: '🎨', color: '#f59e0b' },
                    { name: '讯飞星火', id: 'xunfei', icon: '✨', color: '#ef4444' },
                    { name: 'Gemini', id: 'gemini', icon: '💎', color: '#06b6d4' }
                  ].map((platform, index) => (
                    <Grid item xs={12} sm={6} md={4} lg={3} key={platform.id}>
                      <Card 
                        variant="outlined" 
                        sx={{ 
                          borderRadius: 2,
                          overflow: 'visible',
                          transition: 'all 0.2s',
                          border: selectedStrategy.config?.aiPlatforms?.[platform.id]?.enabled ? 
                            `2px solid ${platform.color}` : '1px solid #e5e7eb',
                          '&:hover': {
                            transform: 'translateY(-2px)',
                            boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
                          }
                        }}
                      >
                        <CardContent sx={{ p: 2 }}>
                          <Box sx={{ textAlign: 'center' }}>
                            <Typography sx={{ fontSize: '2rem', mb: 1 }}>
                              {platform.icon}
                            </Typography>
                            <FormControlLabel
                              control={
                                <Switch
                                  checked={selectedStrategy.config?.aiPlatforms?.[platform.id]?.enabled || false}
                                  onChange={(e) => setSelectedStrategy({
                                    ...selectedStrategy,
                                    config: {
                                      ...selectedStrategy.config,
                                      aiPlatforms: {
                                        ...selectedStrategy.config?.aiPlatforms,
                                        [platform.id]: {
                                          ...selectedStrategy.config?.aiPlatforms?.[platform.id],
                                          enabled: e.target.checked,
                                          weight: selectedStrategy.config?.aiPlatforms?.[platform.id]?.weight || (8 - index)
                                        }
                                      }
                                    }
                                  })}
                                  size="small"
                                  sx={{
                                    '& .MuiSwitch-switchBase.Mui-checked': {
                                      color: platform.color,
                                    },
                                    '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                                      backgroundColor: platform.color,
                                    },
                                  }}
                                />
                              }
                              label={
                                <Typography variant="body2" sx={{ fontWeight: 600 }}>
                                  {platform.name}
                                </Typography>
                              }
                              labelPlacement="bottom"
                              sx={{ m: 0 }}
                            />
                            {selectedStrategy.config?.aiPlatforms?.[platform.id]?.enabled && (
                              <Box sx={{ mt: 2 }}>
                                <Typography variant="caption" sx={{ color: '#6b7280', display: 'block', mb: 0.5 }}>
                                  优先级权重
                                </Typography>
                                <Slider
                                  value={selectedStrategy.config?.aiPlatforms?.[platform.id]?.weight || (8 - index)}
                                  onChange={(e, newValue) => setSelectedStrategy({
                                    ...selectedStrategy,
                                    config: {
                                      ...selectedStrategy.config,
                                      aiPlatforms: {
                                        ...selectedStrategy.config?.aiPlatforms,
                                        [platform.id]: {
                                          ...selectedStrategy.config?.aiPlatforms?.[platform.id],
                                          weight: newValue
                                        }
                                      }
                                    }
                                  })}
                                  min={1}
                                  max={10}
                                  marks
                                  valueLabelDisplay="auto"
                                  sx={{
                                    color: platform.color,
                                    '& .MuiSlider-thumb': {
                                      backgroundColor: platform.color,
                                    },
                                    '& .MuiSlider-track': {
                                      backgroundColor: platform.color,
                                    },
                                  }}
                                />
                              </Box>
                            )}
                          </Box>
                        </CardContent>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              </Box>
            )}

            {/* 内容创作 Tab */}
            {dialogTabValue === 3 && (
              <Box>
                <Box sx={{ mb: 3 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Box sx={{
                      width: 40,
                      height: 40,
                      borderRadius: 2,
                      backgroundColor: '#fce7f3',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      mr: 2
                    }}>
                      <Typography sx={{ fontSize: '1.5rem' }}>✍️</Typography>
                    </Box>
                    <Box>
                      <Typography variant="h6" sx={{ fontWeight: 600, color: '#1a1a1a' }}>
                        内容创作偏好
                      </Typography>
                      <Typography variant="body2" sx={{ color: '#6b7280' }}>
                        定制内容风格和写作模板
                      </Typography>
                    </Box>
                  </Box>
                </Box>
                
                <Grid container spacing={3}>
                  {/* 风格设置 */}
                  <Grid item xs={12}>
                    <Card variant="outlined" sx={{ borderRadius: 2, backgroundColor: '#f9fafb' }}>
                      <CardContent>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                          <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                            🎨 风格与格式
                          </Typography>
                          <Button
                            size="small"
                            variant="outlined"
                            startIcon={<AutoAwesome />}
                            onClick={() => {
                              // 基于关键词和知识库生成写作模板
                              const keywords = selectedStrategy.config?.coreKeywords || '';
                              const knowledgeBase = selectedStrategy.config?.knowledgeBase || [];
                              const template = `基于关键词：${keywords.split('\n').slice(0, 3).join('、')}
知识库：${knowledgeBase.join('、')}

生成的AI写作模板：
1. 标题：融入核心关键词，吸引眼球
2. 开头：提出问题或痛点，引起共鸣
3. 正文：
   - 第一段：介绍背景和重要性
   - 第二段：详细说明解决方案
   - 第三段：案例分析和数据支撑
4. 结尾：总结要点，呼吁行动

SEO优化要点：
- 关键词密度：2-3%
- 标题包含主关键词
- 段落开头包含相关关键词
- 使用语义相关词汇`;
                              alert('AI写作模板已生成！\n\n' + template);
                            }}
                            sx={{ 
                              borderColor: '#3b82f6',
                              color: '#3b82f6',
                              '&:hover': {
                                borderColor: '#2563eb',
                                backgroundColor: 'rgba(59, 130, 246, 0.04)'
                              }
                            }}
                          >
                            AI生成模板
                          </Button>
                        </Box>
                        <Grid container spacing={2}>
                          <Grid item xs={12} sm={6} md={3}>
                            <FormControl fullWidth size="small">
                              <InputLabel>写作风格</InputLabel>
                              <Select
                                value={selectedStrategy.config?.writingStyle || 'professional'}
                                label="写作风格"
                                onChange={(e) => setSelectedStrategy({
                                  ...selectedStrategy,
                                  config: {...selectedStrategy.config, writingStyle: e.target.value}
                                })}
                              >
                                <MenuItem value="professional">💼 专业严谨</MenuItem>
                                <MenuItem value="casual">😊 轻松活泼</MenuItem>
                                <MenuItem value="storytelling">📚 故事叙述</MenuItem>
                                <MenuItem value="technical">🔧 技术详细</MenuItem>
                                <MenuItem value="persuasive">💪 说服力强</MenuItem>
                              </Select>
                            </FormControl>
                          </Grid>
                          <Grid item xs={12} sm={6} md={3}>
                            <FormControl fullWidth size="small">
                              <InputLabel>内容长度</InputLabel>
                              <Select
                                value={selectedStrategy.config?.contentLength || 'medium'}
                                label="内容长度"
                                onChange={(e) => setSelectedStrategy({
                                  ...selectedStrategy,
                                  config: {...selectedStrategy.config, contentLength: e.target.value}
                                })}
                              >
                                <MenuItem value="short">📄 简短 (300-500字)</MenuItem>
                                <MenuItem value="medium">📃 中等 (500-1000字)</MenuItem>
                                <MenuItem value="long">📑 长篇 (1000-2000字)</MenuItem>
                                <MenuItem value="extra_long">📚 超长 (2000字+)</MenuItem>
                              </Select>
                            </FormControl>
                          </Grid>
                          <Grid item xs={12} sm={6} md={3}>
                            <FormControl fullWidth size="small">
                              <InputLabel>语言选择</InputLabel>
                              <Select
                                value={selectedStrategy.config?.language || 'zh-CN'}
                                label="语言选择"
                                onChange={(e) => setSelectedStrategy({
                                  ...selectedStrategy,
                                  config: {...selectedStrategy.config, language: e.target.value}
                                })}
                              >
                                <MenuItem value="zh-CN">🇨🇳 简体中文</MenuItem>
                                <MenuItem value="zh-TW">🇹🇼 繁体中文</MenuItem>
                                <MenuItem value="en-US">🇺🇸 英文</MenuItem>
                                <MenuItem value="mixed">🌏 中英混合</MenuItem>
                              </Select>
                            </FormControl>
                          </Grid>
                          <Grid item xs={12} sm={6} md={3}>
                            <FormControl fullWidth size="small">
                              <InputLabel>段落结构</InputLabel>
                              <Select
                                value={selectedStrategy.config?.paragraphStructure || 'standard'}
                                label="段落结构"
                                onChange={(e) => setSelectedStrategy({
                                  ...selectedStrategy,
                                  config: {...selectedStrategy.config, paragraphStructure: e.target.value}
                                })}
                              >
                                <MenuItem value="standard">📝 标准段落</MenuItem>
                                <MenuItem value="bullet">• 要点列表</MenuItem>
                                <MenuItem value="numbered">1. 编号列表</MenuItem>
                                <MenuItem value="mixed">🔀 混合格式</MenuItem>
                              </Select>
                            </FormControl>
                          </Grid>
                        </Grid>
                      </CardContent>
                    </Card>
                  </Grid>
                  
                  {/* 模板设置 */}
                  <Grid item xs={12}>
                    <Card variant="outlined" sx={{ borderRadius: 2 }}>
                      <CardContent>
                        <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2 }}>
                          📋 内容模板
                        </Typography>

                        {/* 参数选择和插入工具 */}
                        <Box sx={{ mb: 2, p: 2, backgroundColor: '#f8f9fa', borderRadius: 1, border: '1px solid #e9ecef' }}>
                          <Typography variant="body2" sx={{ mb: 1, fontWeight: 500, color: '#495057' }}>
                            快速插入参数
                          </Typography>
                          <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
                            <FormControl size="small" sx={{ minWidth: 200 }}>
                              <InputLabel>选择参数</InputLabel>
                              <Select
                                value={selectedParameter}
                                label="选择参数"
                                onChange={(e) => setSelectedParameter(e.target.value)}
                              >
                                {(selectedStrategy.config?.templateParameters || []).map((param) => (
                                  <MenuItem key={param.key} value={param.key}>
                                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                      <Chip
                                        label={param.key}
                                        size="small"
                                        color="primary"
                                        variant="outlined"
                                        sx={{ fontSize: '0.75rem' }}
                                      />
                                      <Typography variant="body2" sx={{ color: '#666' }}>
                                        {param.label}
                                      </Typography>
                                      {param.required && (
                                        <Typography variant="caption" sx={{ color: '#d32f2f' }}>
                                          *必填
                                        </Typography>
                                      )}
                                    </Box>
                                  </MenuItem>
                                ))}
                              </Select>
                            </FormControl>
                            <Button
                              variant="contained"
                              size="small"
                              onClick={insertParameterToTemplate}
                              disabled={!selectedParameter}
                              sx={{
                                backgroundColor: '#3b82f6',
                                '&:hover': { backgroundColor: '#2563eb' }
                              }}
                            >
                              插入参数
                            </Button>
                          </Box>
                        </Box>

                        <TextField
                          fullWidth
                          size="small"
                          multiline
                          rows={6}
                          placeholder="输入自定义写作模板，使用变量占位符：
{keyword} - 关键词
{title} - 标题
{intro} - 引言
{conclusion} - 结论

示例模板：
标题：{title}
引言：介绍{keyword}的重要性...
主体：详细说明{keyword}的特点...
结论：总结{keyword}的价值..."
                          value={selectedStrategy.config?.writingTemplate || ''}
                          onChange={(e) => setSelectedStrategy({
                            ...selectedStrategy,
                            config: {...selectedStrategy.config, writingTemplate: e.target.value}
                          })}
                          inputRef={(ref) => setTemplateTextFieldRef(ref)}
                          sx={{
                            '& .MuiOutlinedInput-root': {
                              fontFamily: 'monospace',
                              fontSize: '0.875rem',
                            }
                          }}
                        />
                        <Box sx={{ mt: 2, display: 'flex', gap: 1 }}>
                          <Button size="small" variant="outlined" startIcon={<Typography>📝</Typography>}>
                            加载模板
                          </Button>
                          <Button size="small" variant="outlined" startIcon={<Typography>💾</Typography>}>
                            保存模板
                          </Button>
                          <Button size="small" variant="outlined" startIcon={<Typography>👁️</Typography>}>
                            预览效果
                          </Button>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                </Grid>
              </Box>
            )}

            {/* 参数管理 Tab */}
            {dialogTabValue === 4 && (
              <Box>
                <Box sx={{ mb: 3 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Typography variant="h6" sx={{ fontWeight: 600, color: '#1f2937' }}>
                      模板参数管理
                    </Typography>
                  </Box>
                  <Typography variant="body2" sx={{ color: '#6b7280' }}>
                    管理写作模板中使用的参数变量，设置参数的显示名称、是否必填等属性
                  </Typography>
                </Box>

                <Grid container spacing={3}>
                  {/* 参数列表 */}
                  <Grid item xs={12}>
                    <Card variant="outlined" sx={{ borderRadius: 2 }}>
                      <CardContent>
                        <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2 }}>
                          📝 当前参数列表
                        </Typography>

                        {(selectedStrategy.config?.templateParameters || []).length === 0 ? (
                          <Box sx={{
                            textAlign: 'center',
                            py: 4,
                            color: '#6b7280',
                            backgroundColor: '#f9fafb',
                            borderRadius: 1,
                            border: '2px dashed #d1d5db'
                          }}>
                            <Typography variant="body2">
                              暂无参数，点击下方按钮添加参数
                            </Typography>
                          </Box>
                        ) : (
                          <Grid container spacing={2}>
                            {(selectedStrategy.config?.templateParameters || []).map((param, index) => (
                              <Grid item xs={12} sm={6} key={index}>
                                <Card sx={{
                                  border: '1px solid #e5e7eb',
                                  borderRadius: 1,
                                  '&:hover': { borderColor: '#3b82f6' }
                                }}>
                                  <CardContent sx={{ p: 2 }}>
                                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                                      <Chip
                                        label={`{${param.key}}`}
                                        size="small"
                                        color="primary"
                                        variant="outlined"
                                        sx={{ fontFamily: 'monospace' }}
                                      />
                                      <IconButton
                                        size="small"
                                        sx={{ color: '#ef4444' }}
                                        onClick={() => {
                                          const newParams = selectedStrategy.config.templateParameters.filter((_, i) => i !== index);
                                          setSelectedStrategy({
                                            ...selectedStrategy,
                                            config: { ...selectedStrategy.config, templateParameters: newParams }
                                          });
                                        }}
                                      >
                                        <Delete fontSize="small" />
                                      </IconButton>
                                    </Box>
                                    <Typography variant="body2" sx={{ fontWeight: 500, mb: 0.5 }}>
                                      {param.label}
                                    </Typography>
                                    <Typography variant="caption" sx={{ color: '#6b7280', display: 'block', mb: 1 }}>
                                      {param.placeholder || '无提示信息'}
                                    </Typography>
                                    <Box sx={{ display: 'flex', gap: 1 }}>
                                      <Chip
                                        label={param.required ? '必填' : '可选'}
                                        size="small"
                                        color={param.required ? 'error' : 'default'}
                                        variant="outlined"
                                        sx={{ fontSize: '0.7rem' }}
                                      />
                                    </Box>
                                  </CardContent>
                                </Card>
                              </Grid>
                            ))}
                          </Grid>
                        )}
                      </CardContent>
                    </Card>
                  </Grid>

                  {/* 添加参数表单 */}
                  <Grid item xs={12}>
                    <Card variant="outlined" sx={{ borderRadius: 2, backgroundColor: '#f8f9fa' }}>
                      <CardContent>
                        <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2 }}>
                          ➕ 添加新参数
                        </Typography>

                        <Grid container spacing={2}>
                          <Grid item xs={12} sm={3}>
                            <TextField
                              fullWidth
                              size="small"
                              label="参数名"
                              placeholder="例如：topic"
                              value={newParameter.key}
                              onChange={(e) => setNewParameter(prev => ({ ...prev, key: e.target.value }))}
                              helperText="用于模板中的变量名"
                              sx={{
                                '& .MuiOutlinedInput-root': {
                                  backgroundColor: '#fff',
                                  fontFamily: 'monospace'
                                }
                              }}
                            />
                          </Grid>
                          <Grid item xs={12} sm={3}>
                            <TextField
                              fullWidth
                              size="small"
                              label="显示标签"
                              placeholder="例如：文章主题"
                              value={newParameter.label}
                              onChange={(e) => setNewParameter(prev => ({ ...prev, label: e.target.value }))}
                              helperText="用户界面显示的名称"
                              sx={{
                                '& .MuiOutlinedInput-root': {
                                  backgroundColor: '#fff'
                                }
                              }}
                            />
                          </Grid>
                          <Grid item xs={12} sm={4}>
                            <TextField
                              fullWidth
                              size="small"
                              label="提示信息"
                              placeholder="例如：请输入文章主题"
                              value={newParameter.placeholder}
                              onChange={(e) => setNewParameter(prev => ({ ...prev, placeholder: e.target.value }))}
                              helperText="输入框的提示文字"
                              sx={{
                                '& .MuiOutlinedInput-root': {
                                  backgroundColor: '#fff'
                                }
                              }}
                            />
                          </Grid>
                          <Grid item xs={12} sm={2}>
                            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                              <FormControlLabel
                                control={
                                  <Checkbox
                                    checked={newParameter.required}
                                    onChange={(e) => setNewParameter(prev => ({ ...prev, required: e.target.checked }))}
                                    size="small"
                                  />
                                }
                                label="必填"
                                sx={{ fontSize: '0.875rem' }}
                              />
                              <Button
                                variant="contained"
                                size="small"
                                onClick={addNewParameter}
                                disabled={!newParameter.key || !newParameter.label}
                                sx={{
                                  backgroundColor: '#10b981',
                                  '&:hover': { backgroundColor: '#059669' }
                                }}
                              >
                                添加参数
                              </Button>
                            </Box>
                          </Grid>
                        </Grid>
                      </CardContent>
                    </Card>
                  </Grid>
                </Grid>
              </Box>
            )}

            {/* 监控告警 Tab */}
            {dialogTabValue === 5 && (
              <Box>
                <Box sx={{ mb: 3 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Box sx={{
                      width: 40,
                      height: 40,
                      borderRadius: 2,
                      backgroundColor: '#dcfce7',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      mr: 2
                    }}>
                      <Typography sx={{ fontSize: '1.5rem' }}>📊</Typography>
                    </Box>
                    <Box>
                      <Typography variant="h6" sx={{ fontWeight: 600, color: '#1a1a1a' }}>
                        监控与告警
                      </Typography>
                      <Typography variant="body2" sx={{ color: '#6b7280' }}>
                        配置效果监控和异常告警机制
                      </Typography>
                    </Box>
                  </Box>
                </Box>
                
                <Grid container spacing={3}>
                  {/* 监控设置 */}
                  <Grid item xs={12} md={6}>
                    <Card variant="outlined" sx={{ borderRadius: 2, height: '100%' }}>
                      <CardContent>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                          <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                            ⏰ 监控设置
                          </Typography>
                          <Button
                            size="small"
                            variant="contained"
                            startIcon={<Rocket />}
                            onClick={() => {
                              // 一键配置GEO监控
                              setSelectedStrategy({
                                ...selectedStrategy,
                                config: {
                                  ...selectedStrategy.config,
                                  monitoringFrequency: 'realtime',
                                  monitoringTemplate: 'geo_optimized',
                                  geoMonitoringParams: {
                                    regions: ['北京', '上海', '广州', '深圳', '杭州'],
                                    competitors: ['豆包', 'Kimi', 'ChatGPT', '文心一言'],
                                    keywords: selectedStrategy.config?.coreKeywords?.split('\n') || [],
                                    alertThreshold: {
                                      rankingDrop: 3,
                                      trafficDrop: 20,
                                      qualityScore: 85
                                    },
                                    reportFrequency: 'daily',
                                    autoOptimize: true
                                  },
                                  monitoringMetrics: {
                                    '排名变化': true,
                                    '流量统计': true,
                                    '关键词覆盖': true,
                                    '内容质量': true,
                                    '用户互动': true,
                                    '转化率': true,
                                    '地域分布': true,
                                    '竞品对比': true
                                  }
                                }
                              });
                              alert('GEO监控模板已配置！\n\n已启用实时监控，覆盖主要城市和竞品分析。');
                            }}
                            sx={{ 
                              backgroundColor: '#3b82f6',
                              '&:hover': {
                                backgroundColor: '#2563eb'
                              }
                            }}
                          >
                            一键配置GEO监控
                          </Button>
                        </Box>
                        <FormControl fullWidth size="small" sx={{ mb: 2 }}>
                          <InputLabel>监控频率</InputLabel>
                          <Select
                            value={selectedStrategy.config?.monitoringFrequency || 'daily'}
                            label="监控频率"
                            onChange={(e) => setSelectedStrategy({
                              ...selectedStrategy,
                              config: {...selectedStrategy.config, monitoringFrequency: e.target.value}
                            })}
                          >
                            <MenuItem value="realtime">⚡ 实时监控</MenuItem>
                            <MenuItem value="hourly">⏱️ 每小时</MenuItem>
                            <MenuItem value="daily">📅 每日</MenuItem>
                            <MenuItem value="weekly">📆 每周</MenuItem>
                          </Select>
                        </FormControl>
                        <FormControl fullWidth size="small">
                          <InputLabel>监控模板</InputLabel>
                          <Select
                            value={selectedStrategy.config?.monitoringTemplate || 'comprehensive'}
                            label="监控模板"
                            onChange={(e) => setSelectedStrategy({
                              ...selectedStrategy,
                              config: {...selectedStrategy.config, monitoringTemplate: e.target.value}
                            })}
                          >
                            <MenuItem value="basic">📊 基础监控</MenuItem>
                            <MenuItem value="comprehensive">📈 全面监控</MenuItem>
                            <MenuItem value="performance">🚀 性能监控</MenuItem>
                            <MenuItem value="competitor">🎯 竞品监控</MenuItem>
                            <MenuItem value="geo_optimized">🌍 GEO优化监控</MenuItem>
                            <MenuItem value="custom">⚙️ 自定义监控</MenuItem>
                          </Select>
                        </FormControl>
                      </CardContent>
                    </Card>
                  </Grid>
                  
                  {/* 监控指标 */}
                  <Grid item xs={12} md={6}>
                    <Card variant="outlined" sx={{ borderRadius: 2, height: '100%' }}>
                      <CardContent>
                        <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2 }}>
                          📍 监控指标
                        </Typography>
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                          {[
                            { name: '排名变化', icon: '📈' },
                            { name: '流量统计', icon: '👥' },
                            { name: '关键词覆盖', icon: '🔑' },
                            { name: '内容质量', icon: '⭐' },
                            { name: '用户互动', icon: '💬' },
                            { name: '转化率', icon: '🎯' }
                          ].map((metric) => (
                            <Chip
                              key={metric.name}
                              label={`${metric.icon} ${metric.name}`}
                              onClick={() => {
                                const currentValue = selectedStrategy.config?.monitoringMetrics?.[metric.name] || false;
                                setSelectedStrategy({
                                  ...selectedStrategy,
                                  config: {
                                    ...selectedStrategy.config,
                                    monitoringMetrics: {
                                      ...selectedStrategy.config?.monitoringMetrics,
                                      [metric.name]: !currentValue
                                    }
                                  }
                                });
                              }}
                              color={selectedStrategy.config?.monitoringMetrics?.[metric.name] ? 'primary' : 'default'}
                              variant={selectedStrategy.config?.monitoringMetrics?.[metric.name] ? 'filled' : 'outlined'}
                              size="small"
                              sx={{ cursor: 'pointer' }}
                            />
                          ))}
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                  {/* GEO监控参数 */}
                  {selectedStrategy.config?.monitoringTemplate === 'geo_optimized' && (
                    <Grid item xs={12}>
                      <Card variant="outlined" sx={{ 
                        borderRadius: 2,
                        backgroundColor: '#e0f2fe',
                        border: '1px solid #0ea5e9'
                      }}>
                        <CardContent>
                          <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2, color: '#0369a1' }}>
                            🌍 GEO监控参数（重要调整指标）
                          </Typography>
                          <Grid container spacing={2}>
                            <Grid item xs={12} md={6}>
                              <Typography variant="caption" sx={{ color: '#64748b', fontWeight: 500 }}>
                                监控地域
                              </Typography>
                              <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', mt: 1 }}>
                                {(selectedStrategy.config?.geoMonitoringParams?.regions || []).map((region) => (
                                  <Chip key={region} label={region} size="small" color="primary" />
                                ))}
                              </Box>
                            </Grid>
                            <Grid item xs={12} md={6}>
                              <Typography variant="caption" sx={{ color: '#64748b', fontWeight: 500 }}>
                                竞品分析
                              </Typography>
                              <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', mt: 1 }}>
                                {(selectedStrategy.config?.geoMonitoringParams?.competitors || []).map((comp) => (
                                  <Chip key={comp} label={comp} size="small" variant="outlined" />
                                ))}
                              </Box>
                            </Grid>
                            <Grid item xs={12}>
                              <Typography variant="caption" sx={{ color: '#64748b', fontWeight: 500 }}>
                                关键性能指标（KPI）
                              </Typography>
                              <Box sx={{ display: 'flex', gap: 2, mt: 1 }}>
                                <Chip 
                                  icon={<TrendingUp />}
                                  label={`排名波动阈值: ${selectedStrategy.config?.geoMonitoringParams?.alertThreshold?.rankingDrop || 3}位`}
                                  size="small"
                                  color="success"
                                />
                                <Chip 
                                  icon={<Analytics />}
                                  label={`流量下降阈值: ${selectedStrategy.config?.geoMonitoringParams?.alertThreshold?.trafficDrop || 20}%`}
                                  size="small"
                                  color="warning"
                                />
                                <Chip 
                                  icon={<Star />}
                                  label={`质量分数线: ${selectedStrategy.config?.geoMonitoringParams?.alertThreshold?.qualityScore || 85}分`}
                                  size="small"
                                  color="info"
                                />
                              </Box>
                            </Grid>
                            <Grid item xs={12}>
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                <CheckCircle sx={{ color: '#10b981', fontSize: 18 }} />
                                <Typography variant="body2" sx={{ color: '#059669' }}>
                                  自动优化: {selectedStrategy.config?.geoMonitoringParams?.autoOptimize ? '已启用' : '未启用'}
                                </Typography>
                                <Divider orientation="vertical" flexItem sx={{ mx: 1 }} />
                                <Schedule sx={{ color: '#3b82f6', fontSize: 18 }} />
                                <Typography variant="body2" sx={{ color: '#2563eb' }}>
                                  报告频率: {selectedStrategy.config?.geoMonitoringParams?.reportFrequency === 'daily' ? '每日' : '实时'}
                                </Typography>
                              </Box>
                            </Grid>
                          </Grid>
                        </CardContent>
                      </Card>
                    </Grid>
                  )}
                  
                  {/* 告警配置 */}
                  <Grid item xs={12}>
                    <Card variant="outlined" sx={{ 
                      borderRadius: 2,
                      backgroundColor: selectedStrategy.config?.enableAlerts ? '#fef2f2' : '#f9fafb'
                    }}>
                      <CardContent>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                          <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                            🚨 告警配置
                          </Typography>
                          <FormControlLabel
                            control={
                              <Switch
                                checked={selectedStrategy.config?.enableAlerts || false}
                                onChange={(e) => setSelectedStrategy({
                                  ...selectedStrategy,
                                  config: {...selectedStrategy.config, enableAlerts: e.target.checked}
                                })}
                                color="error"
                              />
                            }
                            label="启用告警"
                          />
                        </Box>
                        
                        <Grid container spacing={2}>
                          <Grid item xs={12} sm={6} md={3}>
                            <TextField
                              fullWidth
                              size="small"
                              label="🔻 排名下降阈值"
                              type="number"
                              disabled={!selectedStrategy.config?.enableAlerts}
                              value={selectedStrategy.config?.alertThreshold?.rankDrop || 5}
                              onChange={(e) => setSelectedStrategy({
                                ...selectedStrategy,
                                config: {
                                  ...selectedStrategy.config,
                                  alertThreshold: {
                                    ...selectedStrategy.config?.alertThreshold,
                                    rankDrop: parseInt(e.target.value)
                                  }
                                }
                              })}
                              InputProps={{
                                endAdornment: <InputAdornment position="end">位</InputAdornment>,
                              }}
                            />
                          </Grid>
                          <Grid item xs={12} sm={6} md={3}>
                            <TextField
                              fullWidth
                              size="small"
                              label="📉 流量下降阈值"
                              type="number"
                              disabled={!selectedStrategy.config?.enableAlerts}
                              value={selectedStrategy.config?.alertThreshold?.trafficDrop || 20}
                              onChange={(e) => setSelectedStrategy({
                                ...selectedStrategy,
                                config: {
                                  ...selectedStrategy.config,
                                  alertThreshold: {
                                    ...selectedStrategy.config?.alertThreshold,
                                    trafficDrop: parseInt(e.target.value)
                                  }
                                }
                              })}
                              InputProps={{
                                endAdornment: <InputAdornment position="end">%</InputAdornment>,
                              }}
                            />
                          </Grid>
                          <Grid item xs={12} sm={6} md={3}>
                            <TextField
                              fullWidth
                              size="small"
                              label="⚠️ 质量分阈值"
                              type="number"
                              disabled={!selectedStrategy.config?.enableAlerts}
                              value={selectedStrategy.config?.alertThreshold?.qualityScore || 60}
                              onChange={(e) => setSelectedStrategy({
                                ...selectedStrategy,
                                config: {
                                  ...selectedStrategy.config,
                                  alertThreshold: {
                                    ...selectedStrategy.config?.alertThreshold,
                                    qualityScore: parseInt(e.target.value)
                                  }
                                }
                              })}
                              InputProps={{
                                endAdornment: <InputAdornment position="end">分</InputAdornment>,
                              }}
                            />
                          </Grid>
                          <Grid item xs={12} sm={6} md={3}>
                            <FormControl fullWidth size="small" disabled={!selectedStrategy.config?.enableAlerts}>
                              <InputLabel>通知方式</InputLabel>
                              <Select
                                value={selectedStrategy.config?.alertMethod || 'email'}
                                label="通知方式"
                                onChange={(e) => setSelectedStrategy({
                                  ...selectedStrategy,
                                  config: {...selectedStrategy.config, alertMethod: e.target.value}
                                })}
                              >
                                <MenuItem value="email">📧 邮件</MenuItem>
                                <MenuItem value="sms">📱 短信</MenuItem>
                                <MenuItem value="wechat">💬 微信</MenuItem>
                                <MenuItem value="all">📢 全部</MenuItem>
                              </Select>
                            </FormControl>
                          </Grid>
                        </Grid>
                        
                        {selectedStrategy.config?.enableAlerts && (
                          <Alert severity="info" sx={{ mt: 2 }}>
                            <Typography variant="caption">
                              当监控指标达到阈值时，系统将通过{' '}
                              {selectedStrategy.config?.alertMethod === 'email' ? '邮件' :
                               selectedStrategy.config?.alertMethod === 'sms' ? '短信' :
                               selectedStrategy.config?.alertMethod === 'wechat' ? '微信' : '所有渠道'}
                              {' '}向您发送告警通知
                            </Typography>
                          </Alert>
                        )}
                      </CardContent>
                    </Card>
                  </Grid>
                </Grid>
              </Box>
            )}
          </Box>
        )}
      </DialogContent>
      <DialogActions sx={{ p: 3, backgroundColor: '#f0f7ff' }}>
        <Button 
          onClick={() => {
            setEditDialogOpen(false);
            setNewStrategyDialogOpen(false);
            setSelectedStrategy(null);
            setDialogTabValue(0);
          }}
          sx={{ color: '#64748b' }}
        >
          取消
        </Button>
        <Button 
          onClick={handleSaveStrategy}
          variant="contained"
          sx={{
            backgroundColor: '#3b82f6',
            '&:hover': {
              backgroundColor: '#2563eb'
            }
          }}
        >
          保存策略
        </Button>
      </DialogActions>
    </Dialog>
    </>
  );
  };  
  // 旧的工作空间内容（保留供参考）
  const renderWorkspaceContentOld = () => {
    const projectsPerPage = 6;
    
    // 扩展的项目数据
    const allProjects = [
      { id: 1, name: '豆包SEO优化', status: '运行中', strategy: '智能推荐策略', platform: '豆包' },
      { id: 2, name: 'Kimi内容优化', status: '已暂停', strategy: '关键词密度策略', platform: 'Kimi' },
      { id: 3, name: 'ChatGPT排名提升', status: '运行中', strategy: '语义相关性策略', platform: 'ChatGPT' },
      { id: 4, name: '文心一言优化', status: '待配置', strategy: '未配置', platform: '文心一言' },
      { id: 5, name: '通义千问推广', status: '运行中', strategy: '内容质量策略', platform: '通义千问' },
      { id: 6, name: '讯飞星火项目', status: '运行中', strategy: '综合优化策略', platform: '讯飞星火' },
      { id: 7, name: 'Claude优化方案', status: '已暂停', strategy: '深度内容策略', platform: 'Claude' },
      { id: 8, name: 'Gemini推广计划', status: '运行中', strategy: '多维度策略', platform: 'Gemini' },
      { id: 9, name: '百川智能项目', status: '待配置', strategy: '未配置', platform: '百川' },
      { id: 10, name: 'GLM优化项目', status: '运行中', strategy: '精准匹配策略', platform: 'GLM' },
      { id: 11, name: 'Moonshot项目', status: '已暂停', strategy: '长文本策略', platform: 'Moonshot' },
      { id: 12, name: '零一万物推广', status: '运行中', strategy: '智能推荐策略', platform: '零一万物' },
    ];
    
    // 分页逻辑
    const indexOfLastProject = currentProjectPage * projectsPerPage;
    const indexOfFirstProject = indexOfLastProject - projectsPerPage;
    const currentProjects = allProjects.slice(indexOfFirstProject, indexOfLastProject);
    const totalPages = Math.ceil(allProjects.length / projectsPerPage);
    
    const handlePageChange = (page) => {
      setCurrentProjectPage(page);
    };

    return (
    <Box sx={{ 
      width: '100%', 
      minHeight: '100vh',
      backgroundColor: 'white' 
    }}>
      {/* 顶部标题区域 */}
      <Box sx={{
        borderBottom: '1px solid #f0f0f0',
        backgroundColor: 'white'
      }}>
        <Box sx={{ px: 4, py: 4 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Box>
              <Typography variant="h4" sx={{ 
                fontWeight: 700, 
                color: '#1a1a1a', 
                mb: 0.5,
                fontSize: { xs: '1.75rem', md: '2rem' }
              }}>
                项目管理
              </Typography>
              <Typography variant="body1" sx={{ 
                color: '#6b7280',
                fontSize: '1rem' 
              }}>
                管理项目和协作的统一平台
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', gap: 2 }}>
              <Button
                variant="outlined"
                startIcon={<Settings />}
                sx={{
                  borderColor: '#e5e7eb',
                  color: '#6b7280',
                  '&:hover': {
                    borderColor: '#d1d5db',
                    backgroundColor: '#f9fafb'
                  }
                }}
              >
                配置设置
              </Button>
              <Button
                variant="contained"
                startIcon={<Add />}
                sx={{
                  backgroundColor: '#1976d2',
                  '&:hover': { backgroundColor: '#1565c0' }
                }}
              >
                新建项目
              </Button>
            </Box>
          </Box>
        </Box>
      </Box>

      {/* 项目管理主区域 */}
      <Box sx={{ backgroundColor: 'white' }}>
        <Box sx={{ px: 4, py: 4 }}>
          <Grid container spacing={3}>
            {/* 左侧项目列表 */}
            <Grid item xs={12} lg={8}>
              {/* 标题和搜索栏 */}
              <Box sx={{ 
                display: 'flex', 
                justifyContent: 'space-between', 
                alignItems: 'center',
                mb: 3
              }}>
                <Box>
                  <Typography variant="h5" sx={{
                    fontWeight: 700,
                    color: '#1a1a1a',
                    mb: 0.5
                  }}>
                    项目列表
                  </Typography>
                  <Typography variant="body2" sx={{
                    color: '#6b7280'
                  }}>
                    管理所有进行中的项目
                  </Typography>
                </Box>
                <TextField
                  placeholder="搜索项目..."
                  size="small"
                  sx={{ 
                    width: 250,
                    '& .MuiOutlinedInput-root': {
                      backgroundColor: '#f9fafb',
                      '&:hover': {
                        backgroundColor: '#f3f4f6'
                      },
                      '&.Mui-focused': {
                        backgroundColor: 'white'
                      }
                    }
                  }}
                  InputProps={{
                    startAdornment: <Search sx={{ color: '#9ca3af', mr: 1 }} />
                  }}
                />
              </Box>

              {/* 项目网格 */}
              <Grid container spacing={2}>
                {currentProjects.map((project) => (
                  <Grid item xs={12} md={6} lg={4} key={project.id}>
                    <Card sx={{
                      border: '1px solid #e5e7eb',
                      boxShadow: 'none',
                      height: '100%',
                      minHeight: '200px', // 设置最小高度确保卡片一致性
                      display: 'flex',
                      flexDirection: 'column',
                      '&:hover': {
                        boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1)',
                      }
                    }}>
                      <CardContent sx={{
                        p: 2,
                        flex: 1,
                        display: 'flex',
                        flexDirection: 'column'
                      }}>
                        {/* 内容区域 */}
                        <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
                          <Box sx={{ mb: 2 }}>
                            <Typography variant="subtitle2" sx={{
                              fontWeight: 600,
                              mb: 1,
                              fontSize: '0.875rem',
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              whiteSpace: 'nowrap',
                              lineHeight: 1.2
                            }}>
                              {project.name}
                            </Typography>
                            <Chip
                              label={project.status}
                              size="small"
                              sx={{
                                backgroundColor: project.status === '运行中' ? '#dcfce7' :
                                               project.status === '已暂停' ? '#fee2e2' : '#f3f4f6',
                                color: project.status === '运行中' ? '#15803d' :
                                       project.status === '已暂停' ? '#991b1b' : '#6b7280',
                                fontWeight: 600,
                                fontSize: '0.75rem'
                              }}
                            />
                          </Box>

                          <Box sx={{ mb: 2 }}>
                            <Typography variant="caption" sx={{
                              color: '#6b7280',
                              display: 'block',
                              mb: 0.5
                            }}>
                              配置平台
                            </Typography>
                            <Typography variant="body2" sx={{
                              fontWeight: 500,
                              fontSize: '0.813rem',
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              whiteSpace: 'nowrap'
                            }}>
                              {project.platform}
                            </Typography>
                          </Box>

                          <Box sx={{ mb: 2, flex: 1 }}>
                            <Typography variant="caption" sx={{
                              color: '#6b7280',
                              display: 'block',
                              mb: 0.5
                            }}>
                              当前策略
                            </Typography>
                            <Typography variant="body2" sx={{
                              fontWeight: 500,
                              fontSize: '0.813rem',
                              color: project.strategy === '未配置' ? '#ef4444' : '#1a1a1a',
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              whiteSpace: 'nowrap'
                            }}>
                              {project.strategy}
                            </Typography>
                          </Box>
                        </Box>

                        {/* 操作按钮 - 固定在底部 */}
                        <Button
                          fullWidth
                          variant={project.status === '运行中' ? 'contained' : 'outlined'}
                          size="small"
                          startIcon={<AutoAwesome sx={{ fontSize: 16 }} />}
                          sx={{ 
                            textTransform: 'none',
                            fontSize: '0.813rem',
                            py: 0.75,
                            backgroundColor: project.status === '运行中' ? '#3b82f6' : 'transparent',
                            borderColor: '#e5e7eb',
                            '&:hover': {
                              backgroundColor: project.status === '运行中' ? '#2563eb' : '#f9fafb',
                            }
                          }}
                        >
                          {project.status === '运行中' ? 'AI优化中' : 
                           project.status === '已暂停' ? '恢复优化' : '配置优化'}
                        </Button>
                      </CardContent>
                    </Card>

                  </Grid>
                ))}
              </Grid>
              
              {/* 分页控件 */}
              <Box sx={{ 
                display: 'flex', 
                justifyContent: 'center', 
                alignItems: 'center',
                gap: 1,
                mt: 3 
              }}>
                <Button
                  size="small"
                  variant="outlined"
                  onClick={() => handlePageChange(currentProjectPage - 1)}
                  disabled={currentProjectPage === 1}
                  sx={{
                    minWidth: 'auto',
                    px: 2,
                    borderColor: '#e5e7eb',
                    color: '#6b7280',
                    '&:hover': {
                      borderColor: '#9ca3af',
                      backgroundColor: '#f9fafb'
                    },
                    '&.Mui-disabled': {
                      borderColor: '#f3f4f6',
                      color: '#d1d5db'
                    }
                  }}
                >
                  上一页
                </Button>
                
                <Box sx={{ display: 'flex', gap: 0.5 }}>
                  {[...Array(totalPages)].map((_, index) => (
                    <Button
                      key={index + 1}
                      size="small"
                      variant={currentProjectPage === index + 1 ? 'contained' : 'text'}
                      onClick={() => handlePageChange(index + 1)}
                      sx={{
                        minWidth: 32,
                        height: 32,
                        p: 0,
                        borderRadius: 1,
                        backgroundColor: currentProjectPage === index + 1 ? '#3b82f6' : 'transparent',
                        color: currentProjectPage === index + 1 ? 'white' : '#6b7280',
                        '&:hover': {
                          backgroundColor: currentProjectPage === index + 1 ? '#2563eb' : '#f3f4f6'
                        }
                      }}
                    >
                      {index + 1}
                    </Button>
                  ))}
                </Box>
                
                <Button
                  size="small"
                  variant="outlined"
                  onClick={() => handlePageChange(currentProjectPage + 1)}
                  disabled={currentProjectPage === totalPages}
                  sx={{
                    minWidth: 'auto',
                    px: 2,
                    borderColor: '#e5e7eb',
                    color: '#6b7280',
                    '&:hover': {
                      borderColor: '#9ca3af',
                      backgroundColor: '#f9fafb'
                    },
                    '&.Mui-disabled': {
                      borderColor: '#f3f4f6',
                      color: '#d1d5db'
                    }
                  }}
                >
                  下一页
                </Button>
              </Box>
            </Grid>

            {/* 右侧AI优化提示 */}
            <Grid item xs={12} lg={4}>
              <Box sx={{ 
                position: 'sticky',
                top: 20
              }}>
                <Box sx={{ 
                  backgroundColor: '#fff7ed',
                  borderRadius: 2,
                  p: 2.5,
                  border: '1px solid #fed7aa'
                }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5, mb: 2 }}>
                    <AutoAwesome sx={{ color: '#f59e0b', fontSize: 24 }} />
                    <Typography variant="subtitle1" sx={{ 
                      fontWeight: 600, 
                      color: '#1a1a1a'
                    }}>
                      AI自动优化
                    </Typography>
                  </Box>
                  
                  <Typography variant="body2" sx={{ 
                    color: '#92400e',
                    mb: 2,
                    fontSize: '0.813rem'
                  }}>
                    启用AI自动优化前，请完成以下步骤：
                  </Typography>

                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1.5 }}>
                    <Box sx={{ 
                      display: 'flex', 
                      alignItems: 'flex-start', 
                      gap: 1.5,
                      p: 1.5,
                      backgroundColor: 'white',
                      borderRadius: 1,
                      border: '1px solid #fed7aa'
                    }}>
                      <Box sx={{
                        width: 28,
                        height: 28,
                        borderRadius: '6px',
                        backgroundColor: '#fed7aa',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        flexShrink: 0
                      }}>
                        <Typography sx={{ color: '#ea580c', fontWeight: 700, fontSize: '0.75rem' }}>1</Typography>
                      </Box>
                      <Box>
                        <Typography variant="body2" sx={{ 
                          fontWeight: 600,
                          color: '#1e293b',
                          fontSize: '0.813rem',
                          mb: 0.5
                        }}>
                          配置策略
                        </Typography>
                        <Typography variant="caption" sx={{ 
                          color: '#64748b',
                          fontSize: '0.75rem'
                        }}>
                          设定优化目标和参数
                        </Typography>
                      </Box>
                    </Box>

                    <Box sx={{ 
                      display: 'flex', 
                      alignItems: 'flex-start', 
                      gap: 1.5,
                      p: 1.5,
                      backgroundColor: 'white',
                      borderRadius: 1,
                      border: '1px solid #fed7aa'
                    }}>
                      <Box sx={{
                        width: 28,
                        height: 28,
                        borderRadius: '6px',
                        backgroundColor: '#fbbf24',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        flexShrink: 0
                      }}>
                        <Typography sx={{ color: '#ffffff', fontWeight: 700, fontSize: '0.75rem' }}>2</Typography>
                      </Box>
                      <Box>
                        <Typography variant="body2" sx={{ 
                          fontWeight: 600,
                          color: '#1e293b',
                          fontSize: '0.813rem',
                          mb: 0.5
                        }}>
                          设定额度
                        </Typography>
                        <Typography variant="caption" sx={{ 
                          color: '#64748b',
                          fontSize: '0.75rem'
                        }}>
                          充值并设置消费限制
                        </Typography>
                      </Box>
                    </Box>

                    <Box sx={{ 
                      display: 'flex', 
                      alignItems: 'flex-start', 
                      gap: 1.5,
                      p: 1.5,
                      backgroundColor: 'white',
                      borderRadius: 1,
                      border: '1px solid #fed7aa'
                    }}>
                      <Box sx={{
                        width: 28,
                        height: 28,
                        borderRadius: '6px',
                        backgroundColor: '#84cc16',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        flexShrink: 0
                      }}>
                        <Typography sx={{ color: '#ffffff', fontWeight: 700, fontSize: '0.75rem' }}>3</Typography>
                      </Box>
                      <Box>
                        <Typography variant="body2" sx={{ 
                          fontWeight: 600,
                          color: '#1e293b',
                          fontSize: '0.813rem',
                          mb: 0.5
                        }}>
                          启动服务
                        </Typography>
                        <Typography variant="caption" sx={{ 
                          color: '#64748b',
                          fontSize: '0.75rem'
                        }}>
                          一键启动自动优化
                        </Typography>
                      </Box>
                    </Box>

                    <Box sx={{ 
                      display: 'flex', 
                      alignItems: 'flex-start', 
                      gap: 1.5,
                      p: 1.5,
                      backgroundColor: 'white',
                      borderRadius: 1,
                      border: '1px solid #fed7aa'
                    }}>
                      <Box sx={{
                        width: 28,
                        height: 28,
                        borderRadius: '6px',
                        backgroundColor: '#3b82f6',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        flexShrink: 0
                      }}>
                        <Typography sx={{ color: '#ffffff', fontWeight: 700, fontSize: '0.75rem' }}>4</Typography>
                      </Box>
                      <Box>
                        <Typography variant="body2" sx={{ 
                          fontWeight: 600,
                          color: '#1e293b',
                          fontSize: '0.813rem',
                          mb: 0.5
                        }}>
                          查看记录
                        </Typography>
                        <Typography variant="caption" sx={{ 
                          color: '#64748b',
                          fontSize: '0.75rem'
                        }}>
                          AI优化历史和效果分析
                        </Typography>
                      </Box>
                    </Box>
                  </Box>

                  <Button
                    variant="contained"
                    fullWidth
                    sx={{ 
                      mt: 2,
                      backgroundColor: '#f59e0b',
                      '&:hover': {
                        backgroundColor: '#d97706'
                      }
                    }}
                    startIcon={<Settings />}
                  >
                    配置AI优化
                  </Button>

                  <Typography variant="caption" sx={{ 
                    display: 'block',
                    mt: 2,
                    color: '#92400e',
                    fontSize: '0.75rem',
                    textAlign: 'center'
                  }}>
                    需配置策略并充值额度后方可使用
                  </Typography>
                </Box>
              </Box>
            </Grid>
          </Grid>
        </Box>
      </Box>
    </Box>
  );
  };

  // 渲染页面内容
  // 订阅套餐配置
  const subscriptionPlans = [
    {
      id: 'basic',
      name: '基础版',
      icon: <Rocket sx={{ fontSize: 28 }} />,
      features: [
        '10个项目管理',
        '5个团队成员',
        '基础数据分析',
        '标准客服支持'
      ],
      pricing: {
        monthly: 299,
        quarterly: 807,
        yearly: 2870
      },
      color: '#3b82f6',
      popular: true
    },
    {
      id: 'professional',
      name: '专业版',
      icon: <Diamond sx={{ fontSize: 28 }} />,
      features: [
        '无限项目管理',
        '无限团队成员',
        '高级数据分析',
        'API接入权限',
        '优先技术支持',
        '自定义品牌'
      ],
      pricing: {
        monthly: 599,
        quarterly: 1617,
        yearly: 5750
      },
      color: '#8b5cf6',
      popular: false
    }
  ];

  const durationOptions = [
    { id: 'monthly', label: '月付', discount: 0 },
    { id: 'quarterly', label: '季付', discount: 10 },
    { id: 'yearly', label: '年付', discount: 20 }
  ];

  const renderPageContent = () => {
    if (currentPage === 'dashboard') {
      return renderDashboardContent();
    } else if (currentPage === 'workspace') {
      return renderWorkspaceContent();
    } else if (currentPage === 'geo-monitoring') {
      return renderGeoMonitoringContent();
    } else if (currentPage === 'ai-optimization') {
      return renderAIOptimizationContent();
    } else if (currentPage === 'ai-content-creation') {
      return renderAIContentCreationContent();
    } else if (currentPage === 'content-management') {
      return renderContentManagementContent();
    } else if (currentPage === 'writing-templates') {
      return renderWritingTemplatesContent();
    } else if (currentPage === 'knowledge-base') {
      return renderKnowledgeBaseContent();
    } else if (currentPage === 'announcements') {
      return renderAnnouncementsContent();
    } else if (currentPage === 'settings' || currentPage.startsWith('settings/')) {
      return renderSettingsContent();
    }
    return renderDashboardContent(); // 默认显示企业概览
  };

  // 渲染企业概览页面
  const renderDashboardContent = () => {
    return <EnterpriseDashboard onNavigate={setCurrentPage} />;
  };

  // Legacy dashboard content (keeping for reference)
  const renderDashboardContentOld = () => (
    <Box sx={{ width: '100%', p: 3, backgroundColor: '#f5f5f5' }}>
      {/* 页面标题和操作按钮区域 */}
      <Box sx={{
        backgroundColor: '#fff',
        borderRadius: 3,
        border: '1px solid #e0e0e0',
        p: 4,
        mb: 3,
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
      }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Business sx={{ fontSize: 32, color: '#1976d2' }} />
            <Box>
              <Typography variant="h4" sx={{ fontWeight: 700, color: '#1a1a1a', mb: 0.5 }}>
                企业概览
              </Typography>
              <Typography variant="body1" sx={{ color: '#666' }}>
                查看企业整体运营状况和关键指标
              </Typography>
            </Box>
          </Box>
        </Box>
      </Box>

      {/* 快速操作 */}
      <Card sx={{
        borderRadius: 3,
        mb: 4,
        backgroundColor: '#fff',
        border: '1px solid rgba(0,0,0,0.08)',
        boxShadow: '0 2px 8px rgba(0,0,0,0.06)'
      }}>
        <CardContent sx={{ p: 3 }}>
          <Typography variant="h6" sx={{ fontWeight: 600, mb: 3, color: '#1a1a1a' }}>
            快速操作
          </Typography>
          <Grid container spacing={2}>
            {quickActions.map((action) => (
              <Grid item xs={6} sm={3} key={action.id}>
                <Card
                  sx={{
                    background: action.gradient,
                    color: 'white',
                    cursor: 'pointer',
                    borderRadius: 2,
                    height: '100px',
                    transition: 'all 0.3s ease',
                    border: 'none',
                    boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
                    '&:hover': {
                      transform: 'translateY(-4px) scale(1.02)',
                      boxShadow: '0 8px 25px rgba(0,0,0,0.2)',
                    }
                  }}
                  onClick={action.action}
                >
                  <CardContent sx={{
                    p: 2,
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                    textAlign: 'center',
                    '&:last-child': { pb: 2 }
                  }}>
                    <Box sx={{ mb: 1.5 }}>
                      {React.cloneElement(action.icon, { sx: { fontSize: 28 } })}
                    </Box>
                    <Typography variant="body2" sx={{
                      fontWeight: 600,
                      fontSize: '0.875rem',
                      lineHeight: 1.2
                    }}>
                      {action.title}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </CardContent>
      </Card>

      {/* 统计卡片 */}
      <Box sx={{
        display: 'flex',
        gap: 3,
        mb: 4,
        '& > *': { flex: 1 } // 让所有子元素平均分配宽度
      }}>
        <Card sx={{
          borderRadius: 3,
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
          backgroundColor: '#fff'
        }}>
          <CardContent sx={{ textAlign: 'center', p: 3 }}>
            <Business sx={{ fontSize: 40, color: '#1976d2', mb: 2 }} />
            <Typography variant="h3" sx={{ fontWeight: 700, mb: 1, color: '#1976d2' }}>
              12
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
              监控项目
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
              <TrendingUp sx={{ fontSize: 14, color: '#4caf50', mr: 0.5 }} />
              <Typography variant="caption" sx={{ color: '#4caf50', fontWeight: 500 }}>
                +3 本月新增
              </Typography>
            </Box>
          </CardContent>
        </Card>

        <Card sx={{
          borderRadius: 3,
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
          backgroundColor: '#fff'
        }}>
          <CardContent sx={{ textAlign: 'center', p: 3 }}>
            <Assessment sx={{ fontSize: 40, color: '#4caf50', mb: 2 }} />
            <Typography variant="h3" sx={{ fontWeight: 700, mb: 1, color: '#4caf50' }}>
              8.5
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
              平均排名
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
              <TrendingUp sx={{ fontSize: 14, color: '#4caf50', mr: 0.5 }} />
              <Typography variant="caption" sx={{ color: '#4caf50', fontWeight: 500 }}>
                提升 15%
              </Typography>
            </Box>
          </CardContent>
        </Card>

        <Card sx={{
          borderRadius: 3,
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
          backgroundColor: '#fff'
        }}>
          <CardContent sx={{ textAlign: 'center', p: 3 }}>
            <Security sx={{ fontSize: 40, color: '#ff9800', mb: 2 }} />
            <Typography variant="h3" sx={{ fontWeight: 700, mb: 1, color: '#ff9800' }}>
              5
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
              违规内容数
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
              <Typography variant="caption" sx={{ color: '#666', fontWeight: 500 }}>
                — 无变化
              </Typography>
            </Box>
          </CardContent>
        </Card>

        <Card sx={{
          borderRadius: 3,
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
          backgroundColor: '#fff'
        }}>
          <CardContent sx={{ textAlign: 'center', p: 3 }}>
            <Api sx={{ fontSize: 40, color: '#9c27b0', mb: 2 }} />
            <Typography variant="h3" sx={{ fontWeight: 700, mb: 1, color: '#9c27b0' }}>
              256
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
              API调用次数
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
              <TrendingUp sx={{ fontSize: 14, color: '#4caf50', mr: 0.5 }} />
              <Typography variant="caption" sx={{ color: '#4caf50', fontWeight: 500 }}>
                +28% 本月
              </Typography>
            </Box>
          </CardContent>
        </Card>
      </Box>

      {/* 排名趋势图表 */}
      <Card sx={{ borderRadius: 3, boxShadow: '0 2px 8px rgba(0,0,0,0.1)', mb: 4 }}>
        <CardContent sx={{ p: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant="h6" sx={{ fontWeight: 600 }}>
              排名趋势
            </Typography>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Button size="small" variant="outlined">7天</Button>
              <Button size="small" variant="contained">30天</Button>
              <Button size="small" variant="outlined">90天</Button>
            </Box>
          </Box>

          {/* 趋势图表示 */}
          <Box sx={{ height: 200, display: 'flex', alignItems: 'end', justifyContent: 'space-between', px: 2 }}>
            {[
              { value: 6.2, height: 80, color: '#ffecb3', date: '1/15' },
              { value: 6.8, height: 95, color: '#fff3e0', date: '1/16' },
              { value: 7.2, height: 110, color: '#e3f2fd', date: '1/17' },
              { value: 7.8, height: 125, color: '#e8f5e8', date: '1/18' },
              { value: 8.1, height: 135, color: '#e8f5e8', date: '1/19' },
              { value: 7.5, height: 118, color: '#e3f2fd', date: '1/20' },
              { value: 8.3, height: 140, color: '#e8f5e8', date: '1/21' },
              { value: 8.7, height: 150, color: '#e8f5e8', date: '1/22' },
              { value: 8.2, height: 138, color: '#e8f5e8', date: '1/23' },
              { value: 9.1, height: 165, color: '#e8f5e8', date: '1/24' },
              { value: 8.9, height: 158, color: '#e8f5e8', date: '1/25' },
              { value: 9.2, height: 170, color: '#e8f5e8', date: '1/26' },
              { value: 9.5, height: 180, color: '#e8f5e8', date: '1/27' },
              { value: 9.8, height: 190, color: '#c8e6c9', date: '1/28' },
              { value: 9.6, height: 185, color: '#e8f5e8', date: '1/29' },
              { value: 9.3, height: 175, color: '#e8f5e8', date: '1/30' },
              { value: 8.8, height: 155, color: '#e8f5e8', date: '1/31' },
              { value: 9.0, height: 160, color: '#e8f5e8', date: '2/1' },
              { value: 9.4, height: 178, color: '#e8f5e8', date: '2/2' },
              { value: 9.7, height: 188, color: '#c8e6c9', date: '2/3' }
            ].map((item, index) => (
              <Box key={index} sx={{
                width: 24,
                height: item.height,
                backgroundColor: item.color,
                borderRadius: 1,
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'space-between',
                py: 0.5,
                position: 'relative',
                cursor: 'pointer',
                transition: 'all 0.2s ease',
                '&:hover': {
                  transform: 'scale(1.1)',
                  boxShadow: '0 2px 8px rgba(0,0,0,0.2)',
                  zIndex: 1
                }
              }}>
                <Typography variant="caption" sx={{
                  fontSize: '10px',
                  fontWeight: 600,
                  color: '#333',
                  writingMode: 'vertical-rl',
                  textOrientation: 'mixed'
                }}>
                  {item.value}
                </Typography>
                <Typography variant="caption" sx={{
                  fontSize: '8px',
                  color: '#666',
                  position: 'absolute',
                  bottom: -20,
                  transform: 'rotate(-45deg)',
                  transformOrigin: 'center'
                }}>
                  {item.date}
                </Typography>
              </Box>
            ))}
          </Box>

          {/* 图表说明 */}
          <Box sx={{ mt: 4, display: 'flex', justifyContent: 'center', gap: 3 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Box sx={{ width: 12, height: 12, backgroundColor: '#c8e6c9', borderRadius: 0.5 }} />
              <Typography variant="caption" color="text.secondary">优秀 (9.5+)</Typography>
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Box sx={{ width: 12, height: 12, backgroundColor: '#e8f5e8', borderRadius: 0.5 }} />
              <Typography variant="caption" color="text.secondary">良好 (7.0-9.4)</Typography>
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Box sx={{ width: 12, height: 12, backgroundColor: '#fff3e0', borderRadius: 0.5 }} />
              <Typography variant="caption" color="text.secondary">一般 (5.0-6.9)</Typography>
            </Box>
          </Box>
        </CardContent>
      </Card>

      {/* 最近发布的内容 */}
      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h6" sx={{ fontWeight: 600 }}>
          最近发布的内容
        </Typography>
        <Button variant="text" size="small">
          查看全部
        </Button>
      </Box>

      <Grid container spacing={3}>
        {[].map((item, index) => (
          <Grid item xs={12} md={6} key={index}>
            <Card sx={{
              borderRadius: 3,
              boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
              height: '100%',
              display: 'flex',
              flexDirection: 'column'
            }}>
              <CardContent sx={{ p: 3, flex: 1, display: 'flex', flexDirection: 'column' }}>
                {/* 标题和状态 */}
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                    <Checkbox size="small" />
                    <Typography variant="h6" sx={{ fontWeight: 600, flex: 1 }}>
                      {item.title}
                    </Typography>
                  </Box>
                  <Chip
                    label={item.status}
                    size="small"
                    sx={{
                      backgroundColor: item.statusColor,
                      color: '#fff',
                      fontWeight: 500,
                      minWidth: 60
                    }}
                  />
                </Box>

                {/* 作者和发布信息 */}
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                  <Typography variant="body2" color="text.secondary">
                    {item.author}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {item.publishTime}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {item.readCount}
                  </Typography>
                </Box>

                {/* 描述 */}
                <Typography variant="body2" sx={{ color: '#666', mb: 2, flex: 1 }}>
                  {item.description}
                </Typography>

                {/* 标签 */}
                <Box sx={{ display: 'flex', gap: 1, mb: 2, flexWrap: 'wrap' }}>
                  {item.tags.map((tag, tagIndex) => (
                    <Chip
                      key={tagIndex}
                      label={tag}
                      size="small"
                      variant="outlined"
                      sx={{ fontSize: '0.75rem' }}
                    />
                  ))}
                </Box>

                {/* 操作按钮 */}
                <Box sx={{ display: 'flex', gap: 1, mt: 'auto' }}>
                  <Button size="small" variant="outlined" startIcon={<Visibility />}>
                    查看
                  </Button>
                  <Button size="small" variant="outlined" startIcon={<Edit />}>
                    编辑
                  </Button>
                  <Button size="small" variant="outlined" startIcon={<Assessment />}>
                    数据
                  </Button>
                  {item.status === '草稿' && (
                    <Button size="small" variant="contained" sx={{ ml: 'auto' }}>
                      发布
                    </Button>
                  )}
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    </Box>
  );

  const quickActions = [
    {
      id: 1,
      title: '创建项目',
      description: '创建新的企业项目',
      icon: <Add />,
      action: () => setCurrentPage('projects/create'),
      gradient: 'linear-gradient(135deg, #3b82f6 0%, #764ba2 100%)'
    },
    {
      id: 2,
      title: '用户管理',
      description: '管理企业用户',
      icon: <People />,
      action: () => setCurrentPage('users/list'),
      gradient: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)'
    },
    {
      id: 3,
      title: '数据分析',
      description: '查看数据报表',
      icon: <Analytics />,
      action: () => setCurrentPage('analytics/overview'),
      gradient: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)'
    },
    {
      id: 4,
      title: '系统设置',
      description: '配置企业设置',
      icon: <Settings />,
      action: () => setCurrentPage('settings/general'),
      gradient: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)'
    }
  ];

  // 渲染其他页面内容的占位符函数
  const renderProjectsContent = () => (
    <Box sx={{ p: 3 }}>
      <Typography variant="h5">项目管理功能开发中...</Typography>
    </Box>
  );

  const renderUsersContent = () => (
    <Box sx={{ p: 3 }}>
      <Typography variant="h5">用户管理功能开发中...</Typography>
    </Box>
  );

  const renderAnalyticsContent = () => (
    <Box sx={{ p: 3 }}>
      <Typography variant="h5">数据分析功能开发中...</Typography>
    </Box>
  );

  const renderBillingContent = () => (
    <Box sx={{ p: 3 }}>
      <Typography variant="h5">财务管理功能开发中...</Typography>
    </Box>
  );

  const renderApiContent = () => (
    <Box sx={{ p: 3 }}>
      <Typography variant="h5">API管理功能开发中...</Typography>
    </Box>
  );

  const renderSettingsContent = () => {
    return <EnterpriseSettings />;
  };

  // 渲染公告页面
  const renderAnnouncementsContent = () => {
    return <EnterpriseAnnouncements />;
  };

  // 渲染GEO监控页面
  const renderGeoMonitoringContent = () => (
    <React.Suspense fallback={
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <CircularProgress />
      </Box>
    }>
      <GeoMonitoring />
    </React.Suspense>
  );

  // 旧的GEO监控页面代码（已被新组件替代）
  const renderOldGeoMonitoringContent = () => (
    <Box sx={{ width: '100%', p: 3, backgroundColor: '#f5f5f5' }}>
      {/* 页面标题 */}
      <Box sx={{
        backgroundColor: '#fff',
        borderRadius: 3,
        border: '1px solid #e0e0e0',
        p: 3,
        mb: 3,
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
      }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Box>
            <Typography variant="h4" sx={{ fontWeight: 700, color: '#1a1a1a', mb: 1 }}>
              GEO监控项目
            </Typography>
            <Typography variant="body1" sx={{ color: '#666' }}>
              管理和监控GEO优化项目
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', gap: 2 }}>
            <Button
              variant="contained"
              startIcon={<Add />}
              sx={{
                backgroundColor: '#1976d2',
                '&:hover': { backgroundColor: '#1565c0' }
              }}
            >
              新建项目
            </Button>
          </Box>
        </Box>

        {/* 统计信息 */}
        <Box sx={{ display: 'flex', gap: 3, mt: 3 }}>
          <Box sx={{ textAlign: 'center' }}>
            <Typography variant="h3" sx={{ fontWeight: 700, color: '#1976d2' }}>5</Typography>
            <Typography variant="body2" sx={{ color: '#666' }}>总项目</Typography>
          </Box>
          <Box sx={{ textAlign: 'center' }}>
            <Typography variant="h3" sx={{ fontWeight: 700, color: '#4caf50' }}>3</Typography>
            <Typography variant="body2" sx={{ color: '#666' }}>运行中</Typography>
          </Box>
        </Box>
      </Box>

      {/* 项目列表 */}
      <Grid container spacing={3}>
        {/* 项目卡片示例 */}
        <Grid item xs={12} md={6} lg={4}>
          <Card sx={{
            borderRadius: 3,
            boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
            '&:hover': { boxShadow: '0 4px 16px rgba(0,0,0,0.15)' }
          }}>
            <CardContent sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                <Typography variant="h6" sx={{ fontWeight: 600 }}>
                  首页GEO优化项目
                </Typography>
                <Chip label="运行中" color="success" size="small" />
              </Box>
              <Typography variant="body2" sx={{ color: '#666', mb: 2 }}>
                https://example.com
              </Typography>
              <Typography variant="body2" sx={{ color: '#666', mb: 3 }}>
                针对公司首页进行全面的SEO优化，提升搜索引擎排名。
              </Typography>

              {/* 指标 */}
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h6" sx={{ fontWeight: 600, color: '#1976d2' }}>45</Typography>
                  <Typography variant="caption" sx={{ color: '#666' }}>关键词</Typography>
                </Box>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h6" sx={{ fontWeight: 600, color: '#4caf50' }}>12.5</Typography>
                  <Typography variant="caption" sx={{ color: '#666' }}>平均排名</Typography>
                </Box>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h6" sx={{ fontWeight: 600, color: '#ff9800' }}>78%</Typography>
                  <Typography variant="caption" sx={{ color: '#666' }}>覆盖率</Typography>
                </Box>
              </Box>

              <Box sx={{ display: 'flex', gap: 1 }}>
                <Button size="small" variant="outlined">查看详情</Button>
                <Button size="small" variant="outlined">编辑</Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* 更多项目卡片... */}
        <Grid item xs={12} md={6} lg={4}>
          <Card sx={{
            borderRadius: 3,
            boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
            '&:hover': { boxShadow: '0 4px 16px rgba(0,0,0,0.15)' }
          }}>
            <CardContent sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                <Typography variant="h6" sx={{ fontWeight: 600 }}>
                  产品页面优化
                </Typography>
                <Chip label="暂停中" color="warning" size="small" />
              </Box>
              <Typography variant="body2" sx={{ color: '#666', mb: 2 }}>
                https://products.example.com
              </Typography>
              <Typography variant="body2" sx={{ color: '#666', mb: 3 }}>
                优化产品页面的SEO表现，提升产品搜索可见性。
              </Typography>

              {/* 指标 */}
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h6" sx={{ fontWeight: 600, color: '#1976d2' }}>28</Typography>
                  <Typography variant="caption" sx={{ color: '#666' }}>关键词</Typography>
                </Box>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h6" sx={{ fontWeight: 600, color: '#4caf50' }}>8.3</Typography>
                  <Typography variant="caption" sx={{ color: '#666' }}>平均排名</Typography>
                </Box>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h6" sx={{ fontWeight: 600, color: '#ff9800' }}>82%</Typography>
                  <Typography variant="caption" sx={{ color: '#666' }}>覆盖率</Typography>
                </Box>
              </Box>

              <Box sx={{ display: 'flex', gap: 1 }}>
                <Button size="small" variant="outlined">查看详情</Button>
                <Button size="small" variant="outlined">编辑</Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );

  // 渲染写作模板页面
  const renderWritingTemplatesContent = () => {
    return <WritingTemplates />;
  };

  // 原来的写作模板页面（已被新页面替代）
  const renderOldWritingTemplatesContent = () => (
    <Box sx={{ width: '100%', p: 3, backgroundColor: '#f5f5f5' }}>
      {/* 页面标题 */}
      <Box sx={{
        backgroundColor: '#fff',
        borderRadius: 3,
        border: '1px solid #e0e0e0',
        p: 3,
        mb: 3,
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
      }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Box>
            <Typography variant="h4" sx={{ fontWeight: 700, color: '#1a1a1a', mb: 1 }}>
              写作模板
            </Typography>
            <Typography variant="body1" sx={{ color: '#666' }}>
              自定义AI写作模板和内容模板
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', gap: 2 }}>
            <Button
              variant="outlined"
              startIcon={<CloudDownload />}
            >
              导入模板
            </Button>
            <Button
              variant="contained"
              startIcon={<Add />}
              sx={{
                backgroundColor: '#1976d2',
                '&:hover': { backgroundColor: '#1565c0' }
              }}
            >
              创建模板
            </Button>
          </Box>
        </Box>
      </Box>

      {/* 模板分类 */}
      <Box sx={{ mb: 3 }}>
        <Grid container spacing={2}>
          {/* 分类数据将从后端API获取 */}
          {[
            { name: '全部模板', count: 0, active: true }
          ].map((category, index) => (
            <Grid item key={index}>
              <Chip
                label={`${category.name} (${category.count})`}
                variant={category.active ? "filled" : "outlined"}
                color={category.active ? "primary" : "default"}
                sx={{
                  height: 36,
                  '&:hover': { backgroundColor: category.active ? undefined : 'rgba(25, 118, 210, 0.04)' }
                }}
              />
            </Grid>
          ))}
        </Grid>
      </Box>

      {/* 我的模板 */}
      <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>我的模板</Typography>
      <Grid container spacing={3}>
        {/* 模板数据将从后端API获取 */}
        {[].map((template, index) => (
          <Grid item xs={12} md={6} lg={4} key={index}>
            <Card sx={{
              borderRadius: 3,
              boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
              '&:hover': { boxShadow: '0 4px 16px rgba(0,0,0,0.15)' }
            }}>
              <CardContent sx={{ p: 3 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    {template.title}
                  </Typography>
                  <IconButton size="small" sx={{ color: template.favorite ? '#ff9800' : '#ccc' }}>
                    <Star />
                  </IconButton>
                </Box>
                <Chip label={template.category} size="small" sx={{ mb: 2 }} />
                <Typography variant="body2" sx={{ color: '#666', mb: 3 }}>
                  {template.description}
                </Typography>

                {/* 使用统计 */}
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2, fontSize: '0.875rem', color: '#666' }}>
                  <span>使用次数: {template.usage}</span>
                  <span>最后使用: {template.lastUsed}</span>
                </Box>

                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Button size="small" variant="contained" startIcon={<PlayArrow />}>
                    使用
                  </Button>
                  <Button size="small" variant="outlined" startIcon={<Edit />}>
                    编辑
                  </Button>
                  <Button size="small" variant="outlined" startIcon={<FileCopy />}>
                    复制
                  </Button>
                  <Button size="small" variant="outlined" startIcon={<Delete />}>
                    删除
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    </Box>
  );

  // 渲染知识库管理页面
  const renderKnowledgeBaseContent = () => {
    return <KnowledgeBaseManagement />;
  };

  // Legacy knowledge base content (keeping for reference)
  const renderKnowledgeBaseContentOld = () => (
    <Box sx={{ width: '100%', p: 2, backgroundColor: '#fafafa' }}>
      {/* 页面标题 - 扁平化设计 */}
      <Box sx={{
        backgroundColor: '#fff',
        borderRadius: 0,
        borderBottom: '1px solid #e0e0e0',
        p: 3,
        mb: 3
      }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Box>
            <Typography variant="h5" sx={{ fontWeight: 600, color: '#333', mb: 0.5 }}>
              知识库管理
            </Typography>
            <Typography variant="body2" sx={{ color: '#999' }}>
              管理企业知识库，为AI内容生成提供专业资料
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', gap: 1.5 }}>
            <Button
              variant="text"
              startIcon={<CloudUpload />}
              sx={{ 
                color: '#666',
                '&:hover': { backgroundColor: '#f5f5f5' }
              }}
            >
              上传文档
            </Button>
            <Button
              variant="outlined"
              startIcon={<Add />}
              sx={{
                borderColor: '#1976d2',
                color: '#1976d2',
                borderRadius: 0.5,
                '&:hover': { 
                  borderColor: '#1565c0',
                  backgroundColor: 'rgba(25, 118, 210, 0.04)'
                }
              }}
            >
              创建知识库
            </Button>
          </Box>
        </Box>

        {/* 统计信息 - 扁平化样式 */}
        <Box sx={{ 
          display: 'flex', 
          gap: 6,
          pt: 2,
          borderTop: '1px solid #f0f0f0'
        }}>
          <Box sx={{ display: 'flex', alignItems: 'baseline', gap: 1 }}>
            <Typography variant="h4" sx={{ fontWeight: 500, color: '#333' }}>4</Typography>
            <Typography variant="body2" sx={{ color: '#999' }}>知识库总数</Typography>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'baseline', gap: 1 }}>
            <Typography variant="h4" sx={{ fontWeight: 500, color: '#333' }}>156</Typography>
            <Typography variant="body2" sx={{ color: '#999' }}>文档总数</Typography>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'baseline', gap: 1 }}>
            <Typography variant="h4" sx={{ fontWeight: 500, color: '#333' }}>432</Typography>
            <Typography variant="body2" sx={{ color: '#999' }}>引用次数</Typography>
          </Box>
        </Box>
      </Box>

      {/* 我的知识库 - 扁平化标题 */}
      <Box sx={{ 
        display: 'flex', 
        alignItems: 'center', 
        mb: 2,
        pb: 1,
        borderBottom: '1px solid #e0e0e0'
      }}>
        <Typography variant="body1" sx={{ fontWeight: 500, color: '#333' }}>
          我的知识库
        </Typography>
      </Box>
      
      <Grid container spacing={2}>
        {/* 知识库卡片 - 扁平化设计 */}
        {[
          {
            title: '产品技术文档',
            description: '包含所有产品的技术文档、API文档和使用说明',
            docCount: 23,
            size: '52MB',
            lastUpdate: '12小时前',
            tags: ['产品', '技术', 'API'],
            status: 'active'
          },
          {
            title: '公司介绍资料',
            description: '公司简介、发展历程、企业文化等相关资料',
            docCount: 15,
            size: '18MB',
            lastUpdate: '1天前',
            tags: ['公司', '介绍', '文化'],
            status: 'active'
          },
          {
            title: '行业研究报告',
            description: 'AI行业发展趋势、市场分析、竞品研究等资料',
            docCount: 34,
            size: '67MB',
            lastUpdate: '3天前',
            tags: ['行业', '研究', '分析'],
            status: 'active'
          },
          {
            title: '营销案例库',
            description: '成功的营销案例、推广策略和客户反馈资料',
            docCount: 28,
            size: '41MB',
            lastUpdate: '5天前',
            tags: ['营销', '案例', '推广'],
            status: 'inactive'
          }
        ].map((kb, index) => (
          <Grid item xs={12} md={6} lg={6} key={index}>
            <Box sx={{
              backgroundColor: '#fff',
              border: '1px solid #e5e5e5',
              borderRadius: 0,
              p: 2.5,
              height: '100%',
              display: 'flex',
              flexDirection: 'column',
              transition: 'all 0.2s ease',
              '&:hover': { 
                borderColor: '#d0d0d0',
                backgroundColor: '#fafafa'
              }
            }}>
              {/* 标题和状态 */}
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1.5 }}>
                <Typography variant="subtitle1" sx={{ fontWeight: 500, color: '#333', flex: 1 }}>
                  {kb.title}
                </Typography>
                <Typography 
                  variant="caption" 
                  sx={{ 
                    color: kb.status === 'active' ? '#52c41a' : '#999',
                    fontSize: '12px',
                    px: 1,
                    py: 0.25,
                    border: `1px solid ${kb.status === 'active' ? '#d9f7be' : '#f0f0f0'}`,
                    backgroundColor: kb.status === 'active' ? '#f6ffed' : '#fafafa',
                    borderRadius: 0.5
                  }}
                >
                  {kb.status === 'active' ? '启用中' : '已暂停'}
                </Typography>
              </Box>

              {/* 描述 */}
              <Typography variant="body2" sx={{ color: '#666', mb: 2, flex: 1, lineHeight: 1.6 }}>
                {kb.description}
              </Typography>

              {/* 标签 - 简化样式 */}
              <Box sx={{ mb: 2 }}>
                {kb.tags.map((tag, tagIndex) => (
                  <Typography
                    key={tagIndex}
                    component="span"
                    sx={{ 
                      display: 'inline-block',
                      fontSize: '12px',
                      color: '#999',
                      backgroundColor: '#f5f5f5',
                      px: 1,
                      py: 0.25,
                      mr: 0.75,
                      mb: 0.5,
                      borderRadius: 0
                    }}
                  >
                    {tag}
                  </Typography>
                ))}
              </Box>

              {/* 统计信息 - 更简洁 */}
              <Box sx={{ 
                display: 'flex', 
                gap: 2,
                mb: 2,
                pt: 1.5,
                borderTop: '1px solid #f0f0f0',
                fontSize: '12px',
                color: '#999'
              }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                  <Description sx={{ fontSize: 14, color: '#bbb' }} />
                  <span>{kb.docCount} 文档</span>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                  <Storage sx={{ fontSize: 14, color: '#bbb' }} />
                  <span>{kb.size}</span>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                  <AccessTime sx={{ fontSize: 14, color: '#bbb' }} />
                  <span>{kb.lastUpdate}</span>
                </Box>
              </Box>

              {/* 操作按钮 - text样式 */}
              <Box sx={{ display: 'flex', gap: 1, mt: 'auto' }}>
                <Button 
                  size="small" 
                  variant="text"
                  sx={{ 
                    color: '#1976d2',
                    fontSize: '13px',
                    '&:hover': { backgroundColor: '#f5f5f5' }
                  }}
                >
                  查看详情
                </Button>
                <Button 
                  size="small" 
                  variant="text"
                  sx={{ 
                    color: '#666',
                    fontSize: '13px',
                    '&:hover': { backgroundColor: '#f5f5f5' }
                  }}
                >
                  编辑
                </Button>
                <Box sx={{ flex: 1 }} />
                <IconButton 
                  size="small"
                  sx={{ 
                    color: '#999',
                    '&:hover': { backgroundColor: '#f5f5f5' }
                  }}
                >
                  <MoreVert fontSize="small" />
                </IconButton>
              </Box>
            </Box>
          </Grid>
        ))}
      </Grid>
    </Box>
  );

  // 渲染内容管理页面
  const renderContentManagementContent = () => {
    return <EnterpriseContentManagement />;
  };

  // Legacy content management (keeping for reference)
  const renderContentManagementContentOld = () => (
    <Box sx={{ width: '100%', p: 3, backgroundColor: '#f5f5f5' }}>
      {/* 页面标题 */}
      <Box sx={{
        backgroundColor: '#fff',
        borderRadius: 3,
        border: '1px solid #e0e0e0',
        p: 3,
        mb: 3,
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
      }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Box>
            <Typography variant="h4" sx={{ fontWeight: 700, color: '#1a1a1a', mb: 1 }}>
              内容管理
            </Typography>
            <Typography variant="body1" sx={{ color: '#666' }}>
              管理和组织所有内容资源
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', gap: 2 }}>
            <Button
              variant="outlined"
              startIcon={<Refresh />}
            >
              刷新
            </Button>
            <Button
              variant="contained"
              startIcon={<Add />}
              sx={{
                backgroundColor: '#1976d2',
                '&:hover': { backgroundColor: '#1565c0' }
              }}
            >
              发布内容
            </Button>
          </Box>
        </Box>

        {/* 统计信息 */}
        <Box sx={{ display: 'flex', gap: 4, mt: 3 }}>
          <Box sx={{ textAlign: 'center' }}>
            <Typography variant="h3" sx={{ fontWeight: 700, color: '#2196f3' }}>4</Typography>
            <Typography variant="body2" sx={{ color: '#666' }}>总数量</Typography>
          </Box>
          <Box sx={{ textAlign: 'center' }}>
            <Typography variant="h3" sx={{ fontWeight: 700, color: '#ff9800' }}>2</Typography>
            <Typography variant="body2" sx={{ color: '#666' }}>进行中</Typography>
          </Box>
          <Box sx={{ textAlign: 'center' }}>
            <Typography variant="h3" sx={{ fontWeight: 700, color: '#4caf50' }}>1</Typography>
            <Typography variant="body2" sx={{ color: '#666' }}>已完成</Typography>
          </Box>
        </Box>
      </Box>

      {/* 搜索和筛选 */}
      <Box sx={{ mb: 3, display: 'flex', gap: 2, alignItems: 'center' }}>
        <TextField
          placeholder="搜索内容标题或关键词"
          size="small"
          sx={{ flex: 1, maxWidth: 400, backgroundColor: '#fff' }}
          InputProps={{
            startAdornment: <Search sx={{ color: '#666', mr: 1 }} />
          }}
        />
        <FormControl size="small" sx={{ minWidth: 120, backgroundColor: '#fff' }}>
          <InputLabel>全部项目</InputLabel>
          <Select defaultValue="" displayEmpty>
            <MenuItem value="">全部项目</MenuItem>
            <MenuItem value="ai-content">AI内容营销</MenuItem>
            <MenuItem value="seo">SEO优化</MenuItem>
            <MenuItem value="social">社交媒体</MenuItem>
          </Select>
        </FormControl>
        <FormControl size="small" sx={{ minWidth: 120, backgroundColor: '#fff' }}>
          <InputLabel>全部状态</InputLabel>
          <Select defaultValue="" displayEmpty>
            <MenuItem value="">全部状态</MenuItem>
            <MenuItem value="draft">草稿</MenuItem>
            <MenuItem value="review">审核中</MenuItem>
            <MenuItem value="published">已发布</MenuItem>
          </Select>
        </FormControl>
        <FormControl size="small" sx={{ minWidth: 120, backgroundColor: '#fff' }}>
          <InputLabel>全部类型</InputLabel>
          <Select defaultValue="" displayEmpty>
            <MenuItem value="">全部类型</MenuItem>
            <MenuItem value="article">文章</MenuItem>
            <MenuItem value="video">视频</MenuItem>
            <MenuItem value="image">图片</MenuItem>
          </Select>
        </FormControl>
        <FormControl size="small" sx={{ minWidth: 150, backgroundColor: '#fff' }}>
          <InputLabel>创建时间（降序排列）</InputLabel>
          <Select defaultValue="desc" displayEmpty>
            <MenuItem value="desc">创建时间（降序排列）</MenuItem>
            <MenuItem value="asc">创建时间（升序排列）</MenuItem>
            <MenuItem value="title">标题排序</MenuItem>
          </Select>
        </FormControl>
      </Box>

      {/* 内容列表 */}
      <TableContainer component={Card} sx={{ borderRadius: 2, boxShadow: '0 2px 8px rgba(0,0,0,0.1)' }}>
        <Table>
          <TableHead>
            <TableRow sx={{ backgroundColor: '#f5f5f5' }}>
              <TableCell padding="checkbox">
                <Checkbox />
              </TableCell>
              <TableCell sx={{ fontWeight: 600 }}>标题</TableCell>
              <TableCell sx={{ fontWeight: 600, width: 100 }}>类型</TableCell>
              <TableCell sx={{ fontWeight: 600, width: 120 }}>作者</TableCell>
              <TableCell sx={{ fontWeight: 600, width: 120 }}>创建时间</TableCell>
              <TableCell sx={{ fontWeight: 600, width: 100 }}>阅读量</TableCell>
              <TableCell sx={{ fontWeight: 600, width: 100 }}>状态</TableCell>
              <TableCell sx={{ fontWeight: 600, width: 200 }}>操作</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {[].map((content, index) => (
              <TableRow key={index} hover sx={{ '&:hover': { backgroundColor: '#f9f9f9' } }}>
                <TableCell padding="checkbox">
                  <Checkbox />
                </TableCell>
                <TableCell>
                  <Box>
                    <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 0.5 }}>
                      {content.title}
                    </Typography>
                    <Typography
                      variant="body2"
                      color="text.secondary"
                      sx={{
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        display: '-webkit-box',
                        WebkitLineClamp: 2,
                        WebkitBoxOrient: 'vertical',
                        lineHeight: 1.3,
                        maxHeight: '2.6em'
                      }}
                    >
                      {content.description}
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 0.5, mt: 1, flexWrap: 'wrap' }}>
                      {content.tags.slice(0, 3).map((tag, tagIndex) => (
                        <Chip
                          key={tagIndex}
                          label={tag}
                          size="small"
                          variant="outlined"
                          sx={{ fontSize: '0.7rem', height: 20 }}
                        />
                      ))}
                    </Box>
                  </Box>
                </TableCell>
                <TableCell>
                  <Chip label={content.type} size="small" variant="outlined" />
                </TableCell>
                <TableCell>
                  <Typography variant="body2">{content.author}</Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="body2" color="text.secondary">
                    {content.createTime}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="body2" color="text.secondary">
                    {content.readCount}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Chip
                    label={content.statusText}
                    size="small"
                    sx={{
                      backgroundColor: content.statusColor,
                      color: '#fff',
                      fontWeight: 500
                    }}
                  />
                </TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', gap: 0.5 }}>
                    <IconButton size="small" color="primary" title="查看">
                      <Visibility fontSize="small" />
                    </IconButton>
                    <IconButton size="small" color="primary" title="编辑">
                      <Edit fontSize="small" />
                    </IconButton>
                    <IconButton size="small" color="primary" title="数据">
                      <Assessment fontSize="small" />
                    </IconButton>
                    {content.status === 'draft' && (
                      <Button size="small" variant="contained" sx={{ ml: 1 }}>
                        发布
                      </Button>
                    )}
                  </Box>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );

  // 渲染AI内容创作页面
  // 渠道数据结构 - 将从后端API获取
  const channelData = {
    // 渠道数据将通过API动态加载
  };

  // 发送消息函数
  const handleSendMessage = () => {
    if (!inputText.trim()) return;

    const userMessage = {
      id: Date.now(),
      type: 'user',
      content: inputText,
      timestamp: new Date()
    };

    setChatMessages(prev => [...prev, userMessage]);
    setIsGenerating(true);
    setInputText('');

    // 模拟AI生成回复
    setTimeout(() => {
      const aiMessage = {
        id: Date.now() + 1,
        type: 'assistant',
        content: `以下是根据您的需求生成的内容：

# ${userMessage.content}

这是一篇关于"${userMessage.content}"的专业分析文章。通过深入研究和数据分析，我们发现了许多有价值的洞察...

## 主要观点

1. **关键要点一**：详细分析和解释...
2. **关键要点二**：数据支撑和证据...
3. **关键要点三**：未来趋势和建议...

## 结论

综上所述，这个主题具有重要的意义和价值...

---

*这篇文章已经完成，您可以点击"发布"按钮选择发布渠道。*`,
        timestamp: new Date(),
        canPublish: true
      };
      setChatMessages(prev => [...prev, aiMessage]);
      setIsGenerating(false);
    }, 2000);
  };

  const renderAIContentCreationContent = () => {
    return <AIContentCreation />;
  };

  return (
    <Box sx={{ display: 'flex', height: '100vh', backgroundColor: '#ffffff' }}>
      {/* 侧边栏 */}
      <Drawer
        variant="persistent"
        anchor="left"
        open={sidebarOpen}
        sx={{
          width: sidebarOpen ? (sidebarCollapsed ? 72 : 280) : 0,
          flexShrink: 0,
          transition: 'width 0.3s ease',
          '& .MuiDrawer-paper': {
            width: sidebarCollapsed ? 72 : 280,
            boxSizing: 'border-box',
            backgroundColor: '#fff',
            borderRight: '1px solid #e0e0e0',
            boxShadow: '2px 0 8px rgba(0,0,0,0.1)',
            height: '100vh', // 固定高度为视口高度
            overflow: 'hidden', // 防止整个侧边栏滚动
            display: 'flex',
            flexDirection: 'column',
            transition: 'width 0.3s ease'
          },
        }}
      >
        {/* 侧边栏头部 */}
        <Box sx={{
          p: sidebarCollapsed ? 1 : 3,
          borderBottom: '1px solid #e0e0e0',
          transition: 'padding 0.3s ease'
        }}>
          {!sidebarCollapsed ? (
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Business sx={{ fontSize: 28, color: '#1976d2' }} />
              <Typography variant="h6" sx={{ fontWeight: 700, color: '#1a1a1a', flex: 1 }}>
                企业控制中心
              </Typography>
              <IconButton
                onClick={() => setSidebarCollapsed(true)}
                size="small"
                sx={{ color: '#666' }}
              >
                <MenuOpen />
              </IconButton>
            </Box>
          ) : (
            <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 1 }}>
              <Business sx={{ fontSize: 24, color: '#1976d2' }} />
              <IconButton
                onClick={() => setSidebarCollapsed(false)}
                size="small"
                sx={{ color: '#666' }}
              >
                <Menu />
              </IconButton>
            </Box>
          )}
        </Box>

        {/* 用户信息区域 */}
        <Box sx={{
          p: sidebarCollapsed ? 1 : 2,
          borderBottom: '1px solid #e0e0e0',
          backgroundColor: '#f8f9fa',
          transition: 'padding 0.3s ease'
        }}>
          {!sidebarCollapsed ? (
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Avatar
                sx={{
                  width: 48,
                  height: 48,
                  bgcolor: '#1976d2',
                  fontSize: '1.2rem',
                  fontWeight: 600
                }}
              >
                {user && user.name ? user.name[0] : 'U'}
              </Avatar>
              <Box sx={{ flex: 1 }}>
                <Typography variant="subtitle1" sx={{ fontWeight: 600, color: '#1a1a1a' }}>
                  {user && user.name ? user.name : '用户名称'}
                </Typography>
                <Typography variant="body2" sx={{ color: '#666' }}>
                  {user && user.email ? user.email : '<EMAIL>'}
                </Typography>
              </Box>
            </Box>
          ) : (
            <Box sx={{ display: 'flex', justifyContent: 'center' }}>
              <Avatar
                sx={{
                  width: 32,
                  height: 32,
                  bgcolor: '#1976d2',
                  fontSize: '1rem',
                  fontWeight: 600
                }}
              >
                {user && user.name ? user.name[0] : 'U'}
              </Avatar>
            </Box>
          )}
        </Box>



        {/* 导航菜单 */}
        <List sx={{
          flexGrow: 1,
          py: 2,
          overflow: 'auto', // 允许导航菜单滚动
          height: 0 // 强制让flex: 1生效
        }}>
          {navigationItems.map((item) => (
            <Box key={item.id}>
              <ListItemButton
                onClick={() => handleMenuClick(item)}
                sx={{
                  mx: sidebarCollapsed ? 0.5 : 2,
                  mb: 1,
                  borderRadius: 2,
                  backgroundColor: currentPage === item.path ? 'rgba(25, 118, 210, 0.1)' : 'transparent',
                  '&:hover': {
                    backgroundColor: 'rgba(25, 118, 210, 0.05)',
                  },
                  justifyContent: sidebarCollapsed ? 'center' : 'flex-start',
                  px: sidebarCollapsed ? 1 : 2
                }}
              >
                <ListItemIcon sx={{
                  color: currentPage === item.path ? '#1976d2' : 'inherit',
                  minWidth: sidebarCollapsed ? 'auto' : 40,
                  justifyContent: 'center'
                }}>
                  {item.icon}
                </ListItemIcon>
                {!sidebarCollapsed && (
                  <>
                    <ListItemText
                      primary={item.title}
                      sx={{
                        '& .MuiListItemText-primary': {
                          fontWeight: currentPage === item.path ? 600 : 400,
                          color: currentPage === item.path ? '#1976d2' : 'inherit'
                        }
                      }}
                    />
                    {item.children && (
                      expandedMenus[item.id] ? <ExpandLess /> : <ExpandMore />
                    )}
                  </>
                )}
              </ListItemButton>

              {item.children && !sidebarCollapsed && (
                <Collapse in={expandedMenus[item.id]} timeout="auto" unmountOnExit>
                  <List component="div" disablePadding>
                    {item.children.map((child) => (
                      <ListItemButton
                        key={child.id}
                        onClick={() => handleSubMenuClick(child.path)}
                        data-testid={child.id === 'project-management' ? 'workspace-manager-menu' : `${child.id}-menu`}
                        sx={{
                          pl: 6,
                          mx: 2,
                          mb: 0.5,
                          borderRadius: 2,
                          backgroundColor: currentPage === child.path ? 'rgba(25, 118, 210, 0.1)' : 'transparent',
                          '&:hover': {
                            backgroundColor: 'rgba(25, 118, 210, 0.05)',
                          }
                        }}
                      >
                        <ListItemText
                          primary={child.title}
                          sx={{
                            '& .MuiListItemText-primary': {
                              fontSize: '0.875rem',
                              fontWeight: currentPage === child.path ? 600 : 400,
                              color: currentPage === child.path ? '#1976d2' : 'text.secondary'
                            }
                          }}
                        />
                      </ListItemButton>
                    ))}
                  </List>
                </Collapse>
              )}
            </Box>
          ))}
        </List>

        {/* 订阅组件 - 只在侧边栏展开时显示 */}
        {!sidebarCollapsed && (
          <Box sx={{
            p: 2,
            borderTop: '1px solid #e0e0e0'
          }}>
            <Card sx={{
              backgroundColor: '#f0f7ff',
              border: '1px solid #dbeafe',
              borderRadius: 2,
              p: 2,
              boxShadow: 'none'
            }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1.5 }}>
                <WorkspacePremium sx={{ fontSize: 20, color: '#3b82f6' }} />
                <Typography variant="subtitle2" sx={{
                  fontWeight: 600,
                  fontSize: '0.9rem',
                  color: '#1e40af'
                }}>
                  订阅状态
                </Typography>
              </Box>

              <Box sx={{ mb: 1.5 }}>
                <Chip
                  label={currentSubscription ? currentSubscription.plan_name : "免费版"}
                  size="small"
                  sx={{
                    backgroundColor: currentSubscription ? '#dcfce7' : '#dbeafe',
                    color: currentSubscription ? '#16a34a' : '#1e40af',
                    fontWeight: 500,
                    fontSize: '0.7rem',
                    height: 20
                  }}
                />
                {currentSubscription ? (
                  <Box sx={{ mt: 0.75 }}>
                    <Typography variant="caption" sx={{
                      display: 'block',
                      color: '#64748b',
                      fontSize: '0.65rem'
                    }}>
                      付费周期: {currentSubscription.billing_cycle === 'monthly' ? '月付' :
                                currentSubscription.billing_cycle === 'quarterly' ? '季付' :
                                currentSubscription.billing_cycle === 'yearly' ? '年付' : currentSubscription.billing_cycle}
                    </Typography>
                    <Typography variant="caption" sx={{
                      display: 'block',
                      color: '#64748b',
                      fontSize: '0.65rem'
                    }}>
                      到期时间: {new Date(currentSubscription.end_date).toLocaleDateString('zh-CN')}
                    </Typography>
                    <Typography variant="caption" sx={{
                      display: 'block',
                      color: currentSubscription.days_remaining <= 7 ? '#dc2626' : '#64748b',
                      fontSize: '0.65rem'
                    }}>
                      剩余天数: {currentSubscription.days_remaining}天
                    </Typography>
                    <Box sx={{ mt: 1, display: 'flex', gap: 0.5 }}>
                      <Button
                        size="small"
                        variant="outlined"
                        onClick={() => handleRenewalClick()}
                        sx={{
                          fontSize: '0.6rem',
                          height: 20,
                          minWidth: 'auto',
                          px: 1,
                          borderColor: '#16a34a',
                          color: '#16a34a',
                          '&:hover': {
                            borderColor: '#15803d',
                            backgroundColor: 'rgba(22, 163, 74, 0.04)'
                          }
                        }}
                      >
                        续费
                      </Button>
                      <Button
                        size="small"
                        variant="text"
                        onClick={() => setShowUpgradeDialog(true)}
                        sx={{
                          fontSize: '0.6rem',
                          height: 20,
                          minWidth: 'auto',
                          px: 1,
                          color: '#3b82f6'
                        }}
                      >
                        升级
                      </Button>
                    </Box>
                  </Box>
                ) : (
                  <Typography variant="caption" sx={{
                    display: 'block',
                    mt: 0.75,
                    color: '#64748b',
                    fontSize: '0.7rem'
                  }}>
                    升级解锁更多功能
                  </Typography>
                )}
              </Box>

              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5, mb: 1.5 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.75 }}>
                  <StarBorder sx={{ fontSize: 12, color: '#93c5fd' }} />
                  <Typography variant="caption" sx={{ fontSize: '0.65rem', color: '#475569' }}>
                    无限项目创建
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.75 }}>
                  <StarBorder sx={{ fontSize: 12, color: '#93c5fd' }} />
                  <Typography variant="caption" sx={{ fontSize: '0.65rem', color: '#475569' }}>
                    高级数据分析
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.75 }}>
                  <StarBorder sx={{ fontSize: 12, color: '#93c5fd' }} />
                  <Typography variant="caption" sx={{ fontSize: '0.65rem', color: '#475569' }}>
                    团队协作功能
                  </Typography>
                </Box>
              </Box>

              <Button
                fullWidth
                variant="outlined"
                size="small"
                onClick={() => {
                  setShowUpgradeDialog(true);
                  fetchAvailablePlans(selectedDuration);
                }}
                sx={{
                  borderColor: '#3b82f6',
                  color: '#3b82f6',
                  fontSize: '0.75rem',
                  py: 0.5,
                  textTransform: 'none',
                  '&:hover': {
                    backgroundColor: '#eff6ff',
                    borderColor: '#2563eb'
                  }
                }}
              >
                升级专业版
              </Button>
            </Card>
          </Box>
        )}

        {/* 退出按钮 */}
        <Box sx={{
          p: sidebarCollapsed ? 1 : 2,
          borderTop: '1px solid #e0e0e0',
          mt: 'auto',
          transition: 'padding 0.3s ease'
        }}>
          <ListItemButton
            onClick={() => {
              logout();
              navigate('/auth/login');
            }}
            sx={{
              borderRadius: 2,
              color: '#dc2626',
              transition: 'all 0.2s',
              justifyContent: sidebarCollapsed ? 'center' : 'flex-start',
              px: sidebarCollapsed ? 1 : 2,
              '&:hover': {
                backgroundColor: 'rgba(220, 38, 38, 0.05)',
                color: '#b91c1c',
              }
            }}
          >
            <ListItemIcon sx={{
              color: 'inherit',
              minWidth: sidebarCollapsed ? 'auto' : 40,
              justifyContent: 'center'
            }}>
              <Logout />
            </ListItemIcon>
            {!sidebarCollapsed && (
              <ListItemText
                primary="退出登录"
                sx={{
                  '& .MuiListItemText-primary': {
                    fontWeight: 500
                  }
                }}
              />
            )}
          </ListItemButton>
        </Box>

      </Drawer>

      {/* 主内容区域 */}
      <Box sx={{
        flexGrow: 1,
        display: 'flex',
        flexDirection: 'column',
        marginLeft: sidebarOpen ? 0 : '-280px',
        transition: 'margin-left 0.3s ease'
      }}>


        {/* 页面内容 */}
        <Box sx={{ 
          flexGrow: 1, 
          overflow: 'hidden',
          display: 'flex',
          flexDirection: 'column',
          height: '100%'
        }}>
          {renderPageContent()}
        </Box>
      </Box>

      {/* 套餐升级对话框 */}
      <Dialog 
        open={showUpgradeDialog} 
        onClose={() => setShowUpgradeDialog(false)}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 2,
            maxHeight: '90vh'
          }
        }}
      >
        <DialogTitle sx={{ 
          p: 2.5,
          backgroundColor: '#f0f7ff',
          borderBottom: '1px solid #dbeafe'
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
              <WorkspacePremium sx={{ color: '#3b82f6', fontSize: 28 }} />
              <Typography variant="h5" sx={{ 
                fontWeight: 600,
                color: '#1e40af'
              }}>
                选择订阅套餐
              </Typography>
            </Box>
            <IconButton onClick={() => setShowUpgradeDialog(false)} size="small">
              <Close />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent sx={{ backgroundColor: '#fafbfc', py: 2, px: 2 }}>
          <Grid container spacing={2}>
            {/* 左侧 - 套餐卡片 */}
            <Grid item xs={12} md={7}>
              {/* 付费周期选择 */}
              <Box sx={{ mb: 3 }}>
                <Typography variant="subtitle2" sx={{ mb: 1.5, color: '#1e40af', fontWeight: 600 }}>
                  选择付费周期
                </Typography>
                <Box sx={{ 
                  display: 'flex',
                  backgroundColor: '#f0f7ff',
                  borderRadius: 2,
                  p: 0.5,
                  border: '1px solid #dbeafe'
                }}>
                  {durationOptions.map((option) => (
                    <Button
                      key={option.id}
                      variant={selectedDuration === option.id ? 'contained' : 'text'}
                      size="small"
                      onClick={() => setSelectedDuration(option.id)}
                      sx={{
                        flex: 1,
                        mx: 0.25,
                        borderRadius: 1.5,
                        py: 1,
                        backgroundColor: selectedDuration === option.id ? '#3b82f6' : 'transparent',
                        color: selectedDuration === option.id ? 'white' : '#64748b',
                        '&:hover': {
                          backgroundColor: selectedDuration === option.id ? '#2563eb' : '#e0f2fe'
                        }
                      }}
                    >
                      <Box>
                        <Box>{option.label}</Box>
                        {option.discount > 0 && (
                          <Typography variant="caption" sx={{
                            display: 'block',
                            fontSize: '0.65rem',
                            opacity: 0.9
                          }}>
                            省{option.discount}%
                          </Typography>
                        )}
                      </Box>
                    </Button>
                  ))}
                </Box>
              </Box>

              {/* 套餐选择卡片 */}
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                {loadingPlans ? (
                  <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
                    <CircularProgress />
                  </Box>
                ) : (
                  (availablePlans.length > 0 ? availablePlans : subscriptionPlans).map((plan) => {
                  const currentPrice = plan.pricing[selectedDuration];
                  const monthlyEquivalent = selectedDuration === 'monthly' ? currentPrice :
                                           selectedDuration === 'quarterly' ? Math.round(currentPrice / 3) :
                                           Math.round(currentPrice / 12);
                  
                  // 检查是否是当前用户正在使用的套餐且付费周期相同
                  const isCurrentPlan = currentSubscription &&
                    (String(currentSubscription.plan_id) === String(plan.id)) &&
                    (currentSubscription.billing_cycle === selectedDuration);
                  const shouldDisable = plan.is_purchased || isCurrentPlan;

                  return (
                    <Card
                      key={plan.id}
                      onClick={() => !shouldDisable && setSelectedPlan(plan.id)}
                      sx={{
                        position: 'relative',
                        cursor: shouldDisable ? 'default' : 'pointer',
                        border: '2px solid',
                        borderColor: shouldDisable ? '#10b981' : (selectedPlan === plan.id ? plan.color : '#e5e7eb'),
                        backgroundColor: shouldDisable ? '#f0fdf4' : (selectedPlan === plan.id ? '#f0f7ff' : 'white'),
                        opacity: shouldDisable ? 0.8 : 1,
                        transition: 'all 0.2s',
                        '&:hover': {
                          borderColor: shouldDisable ? '#10b981' : plan.color,
                          transform: shouldDisable ? 'none' : 'translateX(4px)',
                          boxShadow: '0 4px 12px rgba(0,0,0,0.08)'
                        }
                      }}
                    >
                      {shouldDisable && (
                        <Chip
                          label="正在使用"
                          size="small"
                          sx={{
                            position: 'absolute',
                            top: 12,
                            right: 12,
                            backgroundColor: '#10b981',
                            color: 'white',
                            fontWeight: 600,
                            fontSize: '0.65rem',
                            height: 20
                          }}
                        />
                      )}
                      {plan.popular && !shouldDisable && (
                        <Chip
                          label="推荐"
                          size="small"
                          sx={{
                            position: 'absolute',
                            top: 12,
                            right: 12,
                            backgroundColor: '#3b82f6',
                            color: 'white',
                            fontWeight: 600,
                            fontSize: '0.65rem',
                            height: 20
                          }}
                        />
                      )}

                      <Box sx={{ p: 2.5, display: 'flex', alignItems: 'center', gap: 2 }}>
                        <Radio
                          checked={selectedPlan === plan.id}
                          sx={{
                            color: '#e5e7eb',
                            '&.Mui-checked': {
                              color: plan.color
                            }
                          }}
                        />
                        
                        <Box sx={{
                          width: 44,
                          height: 44,
                          borderRadius: 2,
                          backgroundColor: selectedPlan === plan.id ? plan.color : '#f3f4f6',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          flexShrink: 0
                        }}>
                          {React.cloneElement(plan.icon, { 
                            sx: { 
                              color: selectedPlan === plan.id ? 'white' : plan.color,
                              fontSize: 22
                            }
                          })}
                        </Box>
                        
                        <Box sx={{ flex: 1 }}>
                          <Typography variant="h6" sx={{ 
                            fontWeight: 600,
                            color: '#1a1a1a',
                            fontSize: '1.1rem'
                          }}>
                            {plan.name}
                          </Typography>
                          <Typography variant="body2" sx={{ 
                            color: '#64748b',
                            fontSize: '0.8rem'
                          }}>
                            {plan.features.slice(0, 2).join(' · ')}
                          </Typography>
                        </Box>

                        <Box sx={{ textAlign: 'right' }}>
                          <Box sx={{ display: 'flex', alignItems: 'baseline', gap: 0.5 }}>
                            <Typography variant="h5" sx={{ 
                              fontWeight: 700,
                              color: plan.color
                            }}>
                              ¥{monthlyEquivalent}
                            </Typography>
                            <Typography variant="body2" sx={{ color: '#6b7280' }}>
                              /月
                            </Typography>
                          </Box>
                          <Typography variant="caption" sx={{ 
                            color: '#94a3b8',
                            fontSize: '0.7rem'
                          }}>
                            {selectedDuration === 'monthly' ? '按月付' :
                             selectedDuration === 'quarterly' ? `¥${currentPrice}/季` :
                             `¥${currentPrice}/年`}
                          </Typography>
                        </Box>
                      </Box>
                    </Card>
                  );
                }))}
              </Box>
            </Grid>

            {/* 右侧 - 说明区域 */}
            <Grid item xs={12} md={5}>
              <Box sx={{ 
                p: 2,
                backgroundColor: '#f0f7ff',
                borderRadius: 2,
                border: '1px solid #dbeafe'
              }}>
                {/* 选中套餐详情 */}
                <Typography variant="h6" sx={{ 
                  mb: 2,
                  color: '#1e40af',
                  fontWeight: 600,
                  fontSize: '1rem'
                }}>
                  {(availablePlans.length > 0 ? availablePlans : subscriptionPlans).find(p => p.id === selectedPlan)?.name}功能详情
                </Typography>

                <Box sx={{ mb: 3 }}>
                  {(availablePlans.length > 0 ? availablePlans : subscriptionPlans).find(p => p.id === selectedPlan)?.features.map((feature, index) => (
                    <Box key={index} sx={{ 
                      display: 'flex', 
                      alignItems: 'flex-start',
                      gap: 1,
                      mb: 1.5
                    }}>
                      <CheckCircle sx={{ 
                        fontSize: 16, 
                        color: '#10b981',
                        mt: 0.25,
                        flexShrink: 0
                      }} />
                      <Typography variant="body2" sx={{ 
                        color: '#475569',
                        fontSize: '0.875rem'
                      }}>
                        {feature}
                      </Typography>
                    </Box>
                  ))}
                </Box>

                <Divider sx={{ my: 2 }} />

                {/* 付款方式 */}
                <Typography variant="subtitle2" sx={{ 
                  mb: 1.5,
                  color: '#1e40af',
                  fontWeight: 600
                }}>
                  付款方式
                </Typography>
                <Box sx={{ display: 'flex', gap: 1, mb: 3 }}>
                  <Button
                    size="small"
                    variant={paymentMethod === 'alipay' ? 'contained' : 'outlined'}
                    onClick={() => setPaymentMethod('alipay')}
                    sx={{ 
                      flex: 1,
                      fontSize: '0.75rem'
                    }}
                  >
                    支付宝
                  </Button>
                  <Button
                    size="small"
                    variant={paymentMethod === 'wechat' ? 'contained' : 'outlined'}
                    onClick={() => setPaymentMethod('wechat')}
                    sx={{ 
                      flex: 1,
                      fontSize: '0.75rem'
                    }}
                  >
                    微信
                  </Button>
                  <Button
                    size="small"
                    variant={paymentMethod === 'card' ? 'contained' : 'outlined'}
                    onClick={() => setPaymentMethod('card')}
                    sx={{ 
                      flex: 1,
                      fontSize: '0.75rem'
                    }}
                  >
                    银行卡
                  </Button>
                </Box>

                {/* 订单摘要 */}
                <Card sx={{ 
                  p: 1.5, 
                  backgroundColor: 'white',
                  boxShadow: 'none',
                  border: '1px solid #dbeafe'
                }}>
                  <Typography variant="subtitle2" sx={{ 
                    fontWeight: 600, 
                    mb: 1.5, 
                    color: '#1e293b' 
                  }}>
                    订单摘要
                  </Typography>
                  
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="body2" sx={{ color: '#64748b' }}>
                      {(availablePlans.length > 0 ? availablePlans : subscriptionPlans).find(p => p.id === selectedPlan)?.name}
                    </Typography>
                    <Typography variant="body2" sx={{ fontWeight: 600 }}>
                      ¥{(availablePlans.length > 0 ? availablePlans : subscriptionPlans).find(p => p.id === selectedPlan)?.pricing[selectedDuration]}
                    </Typography>
                  </Box>
                  
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="body2" sx={{ color: '#64748b' }}>
                      付费周期
                    </Typography>
                    <Typography variant="body2">
                      {durationOptions.find(d => d.id === selectedDuration)?.label}
                    </Typography>
                  </Box>
                  
                  {durationOptions.find(d => d.id === selectedDuration)?.discount > 0 && (
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2" sx={{ color: '#64748b' }}>
                        优惠折扣
                      </Typography>
                      <Typography variant="body2" sx={{ color: '#10b981' }}>
                        -{durationOptions.find(d => d.id === selectedDuration)?.discount}%
                      </Typography>
                    </Box>
                  )}
                  
                  <Divider sx={{ my: 1 }} />
                  
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                      总计
                    </Typography>
                    <Typography variant="subtitle1" sx={{
                      fontWeight: 700,
                      color: '#3b82f6'
                    }}>
                      ¥{(availablePlans.length > 0 ? availablePlans : subscriptionPlans).find(p => p.id === selectedPlan)?.pricing[selectedDuration]}
                    </Typography>
                  </Box>
                </Card>
              </Box>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions sx={{ p: 3, backgroundColor: '#f0f7ff' }}>
          <Button onClick={() => setShowUpgradeDialog(false)} sx={{ color: '#64748b' }}>
            取消
          </Button>
          <Button
            variant="contained"
            onClick={handleSubscribe}
            disabled={isProcessingPayment}
            sx={{
              backgroundColor: '#3b82f6',
              '&:hover': {
                backgroundColor: '#2563eb'
              }
            }}
          >
            {isProcessingPayment ? '处理中...' : (() => {
              if (!currentSubscription) return '立即订阅';
              const isSamePlan = currentSubscription.plan_id === selectedPlan;
              const isDifferentBillingCycle = currentSubscription.billing_cycle !== selectedDuration;
              if (isSamePlan && isDifferentBillingCycle) return '立即升级';
              return '立即购买';
            })()}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}

export default EnterpriseControlCenter;
