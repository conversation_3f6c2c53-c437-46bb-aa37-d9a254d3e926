import React, { useState, useEffect } from 'react';
import CreateTemplateDialog from './CreateTemplateDialog';
import EditTemplateDialog from './EditTemplateDialog';
import templateService from '../../services/templateService';
import {
  Box,
  Typography,
  Card,
  CardContent,
  CardActions,
  Grid,
  Button,
  IconButton,
  Chip,
  TextField,
  InputAdornment,
  Tab,
  Tabs,
  Paper,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  ListItemSecondaryAction,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tooltip,
  Badge,
  Divider,
  Alert,
  Snackbar,
  ToggleButton,
  ToggleButtonGroup,
  Rating,
  LinearProgress,
  Pagination,
  Switch,
} from '@mui/material';
import {
  Add,
  Edit,
  Delete,
  Favorite,
  FavoriteBorder,
  Search,
  FilterList,
  ViewModule,
  Sort,
  Download,
  Upload,
  Share,
  BookmarkBorder,
  Bookmark,
  LocalOffer,

  Article,
  Campaign,
  Business,
  School,
  Email,
  TrendingUp,
  Psychology,
  AutoAwesome,
  Code,
  Lightbulb,
  StarBorder,
  Star,
  AccessTime,
  Preview,
  Visibility,
  Settings,
  MoreVert,
  Check,
  Close,
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';
import ApiService from '../../services/api';

const StyledContainer = styled(Box)(({ theme }) => ({
  height: 'calc(100vh - 64px)',
  display: 'flex',
  flexDirection: 'column',
  backgroundColor: '#fafafa',
  overflow: 'hidden',
}));

const HeaderSection = styled(Box)(({ theme }) => ({
  padding: theme.spacing(3),
  backgroundColor: '#fff',
  borderBottom: '1px solid #e0e0e0',
}));

const ContentSection = styled(Box)(({ theme }) => ({
  flex: 1,
  overflow: 'auto',
  padding: theme.spacing(3),
  '&::-webkit-scrollbar': {
    width: '8px',
  },
  '&::-webkit-scrollbar-track': {
    background: 'transparent',
  },
  '&::-webkit-scrollbar-thumb': {
    background: '#e0e0e0',
    borderRadius: '4px',
  },
}));

const TemplateCard = styled(Card)(({ theme }) => ({
  width: '320px !important', // 强制固定宽度
  height: '300px !important', // 强制固定高度
  minWidth: '320px !important', // 强制最小宽度
  maxWidth: '320px !important', // 强制最大宽度
  display: 'flex !important',
  flexDirection: 'column !important',
  borderRadius: '0 !important',
  border: '1px solid #e5e5e5 !important',
  transition: 'all 0.2s ease',
  cursor: 'pointer',
  boxShadow: 'none !important',
  position: 'relative',
  overflow: 'hidden !important',
  backgroundColor: '#fff !important',
  flexShrink: '0 !important', // 防止收缩
  flexGrow: '0 !important', // 防止增长
  boxSizing: 'border-box !important', // 确保边框包含在尺寸内
  '&:hover': {
    borderColor: '#d0d0d0 !important',
    backgroundColor: '#fafafa !important',
    '& .template-actions': {
      opacity: 1,
    },
    '& .template-edit-actions': {
      opacity: 1,
    },
  },
}));



const StyledTabs = styled(Tabs)(({ theme }) => ({
  borderBottom: `1px solid ${theme.palette.divider}`,
  '& .MuiTabs-indicator': {
    height: 3,
    borderRadius: '3px 3px 0 0',
  },
}));



// 模板数据 - 将从后端API获取
const templatesData = [];

function WritingTemplates() {

  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState('all');
  const [templates, setTemplates] = useState([]);
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [showPreview, setShowPreview] = useState(false);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState({ page: 1, size: 4, total: 0, pages: 0 });
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState(null);

  // 加载模板数据
  const loadTemplates = async (resetPage = false, targetPage = null) => {
    try {
      setLoading(true);
      const currentPage = resetPage ? 1 : (targetPage || pagination.page);
      const queryParams = {
        page: currentPage,
        size: pagination.size,
      };



      if (searchQuery) {
        queryParams.keyword = searchQuery;
      }

      // 处理状态筛选
      if (sortBy === 'active') {
        queryParams.is_active = true;
      } else if (sortBy === 'inactive') {
        queryParams.is_active = false;
      }
      // sortBy === 'all' 时不传递is_active参数，后端会返回所有状态的模板



      const response = await templateService.getTemplateList(queryParams);

      if (response.success && response.data) {
        setTemplates(response.data.items || []);
        setPagination(response.data.pagination || { page: currentPage, size: 20, total: 0, pages: 0 });
      } else {
        setSnackbar({ open: true, message: '加载模板失败', severity: 'error' });
      }
    } catch (error) {
      console.error('加载模板失败:', error);
      setSnackbar({ open: true, message: '加载模板失败，请稍后重试', severity: 'error' });
    } finally {
      setLoading(false);
    }
  };

  // 初始加载
  useEffect(() => {
    loadTemplates(true); // 初始加载时重置页码
  }, []);

  // 当状态筛选变化时立即重新加载
  useEffect(() => {
    loadTemplates(true);
  }, [sortBy]);

  // 搜索防抖处理
  useEffect(() => {
    const debounceTimer = setTimeout(() => {
      // 搜索框为空时加载所有模板
      // 有搜索内容且长度大于2个字符时发送API请求
      // 1-2个字符的搜索使用本地过滤
      if (searchQuery.trim() === '' || searchQuery.length > 2) {
        loadTemplates(true);
      }
    }, 300); // 300ms 防抖延迟，更快响应

    return () => clearTimeout(debounceTimer);
  }, [searchQuery]);





  // 本地搜索过滤，提供即时搜索体验
  // 注意：状态筛选由后端处理，这里只处理搜索关键词的即时过滤
  const getFilteredTemplates = () => {
    // 如果搜索查询为空，或者搜索查询长度大于2（会触发后端搜索），直接返回后端数据
    if (!searchQuery.trim() || searchQuery.length > 2) {
      return templates;
    }

    // 只对1-2个字符的短查询进行前端过滤
    const query = searchQuery.toLowerCase().trim();
    return templates.filter(template => {
      // 搜索模板名称
      const nameMatch = template.template_name?.toLowerCase().includes(query);

      // 搜索模板描述
      const descMatch = template.template_description?.toLowerCase().includes(query);



      // 搜索模板内容
      const contentMatch = template.prompt_template?.toLowerCase().includes(query);

      return nameMatch || descMatch || contentMatch;
    });
  };

  const sortedTemplates = getFilteredTemplates();

  // 分页处理
  const handlePageChange = (event, newPage) => {
    setPagination(prev => ({ ...prev, page: newPage }));
    loadTemplates(false, newPage); // 重新加载数据
  };

  // 直接使用后端返回的模板数据（已经是当前页的数据）
  const getCurrentPageTemplates = () => {
    return sortedTemplates; // 后端已经返回了当前页的数据
  };

  // 启用/禁用模板
  const handleToggleTemplate = async (templateId, currentStatus) => {
    try {
      const response = await templateService.updateTemplate(templateId, {
        is_active: !currentStatus
      });

      if (response.success) {
        setTemplates(prev => prev.map(t =>
          t.id === templateId ? { ...t, is_active: !currentStatus } : t
        ));
        setSnackbar({
          open: true,
          message: !currentStatus ? '模板已启用' : '模板已禁用',
          severity: 'success'
        });
      } else {
        setSnackbar({
          open: true,
          message: response.message || '操作失败',
          severity: 'error'
        });
      }
    } catch (error) {
      console.error('切换模板状态失败:', error);
      setSnackbar({
        open: true,
        message: '操作失败，请稍后重试',
        severity: 'error'
      });
    }
  };

  const handleFavorite = (templateId) => {
    setTemplates(prev => prev.map(t =>
      t.id === templateId ? { ...t, isFavorite: !t.isFavorite } : t
    ));
    setSnackbar({
      open: true,
      message: templates.find(t => t.id === templateId).isFavorite ? '已取消收藏' : '已添加收藏',
      severity: 'success'
    });
  };

  const handleUseTemplate = (template) => {
    setSelectedTemplate(template);
    setShowPreview(true);
  };



  const handleEditTemplate = (template) => {
    setEditingTemplate(template);
    setEditDialogOpen(true);
  };

  const handleDeleteTemplate = async (templateId) => {
    if (window.confirm('确定要删除这个模板吗？此操作不可撤销。')) {
      try {
        const response = await templateService.deleteTemplate(templateId);
        if (response.success) {
          setTemplates(prev => prev.filter(t => t.id !== templateId));
          setSnackbar({ open: true, message: '模板已删除', severity: 'success' });
        } else {
          setSnackbar({ open: true, message: response.message || '删除失败', severity: 'error' });
        }
      } catch (error) {
        console.error('删除模板失败:', error);
        setSnackbar({ open: true, message: '删除失败，请稍后重试', severity: 'error' });
      }
    }
  };





  const renderTemplateListItem = (template) => (
    <Paper
      key={template.id}
      elevation={0}
      sx={{
        p: 2,
        mb: 1,
        border: '1px solid #e5e7eb',
        borderRadius: 2,
        cursor: 'pointer',
        transition: 'all 0.2s ease',
        '&:hover': {
          borderColor: '#1976d2',
          boxShadow: '0 2px 8px rgba(25, 118, 210, 0.1)',
        },
      }}
      onClick={() => handleUseTemplate(template)}
    >
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        {/* 左侧内容 */}
        <Box sx={{ flex: 1, minWidth: 0 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>
            <Typography
              variant="h6"
              sx={{
                fontSize: '1rem',
                fontWeight: 600,
                color: '#1f2937',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
                maxWidth: '300px',
              }}
            >
              {template.template_name}
            </Typography>

            {/* 状态标签 */}
            <Box sx={{ display: 'flex', gap: 1 }}>
              {template.is_active ? (
                <Chip
                  label="启用中"
                  size="small"
                  sx={{
                    backgroundColor: '#dcfce7',
                    color: '#166534',
                    fontSize: '0.75rem',
                    height: '24px',
                  }}
                />
              ) : (
                <Chip
                  label="已禁用"
                  size="small"
                  variant="outlined"
                  sx={{
                    borderColor: '#d1d5db',
                    color: '#6b7280',
                    fontSize: '0.75rem',
                    height: '24px',
                  }}
                />
              )}
            </Box>
          </Box>

          <Typography
            variant="body2"
            sx={{
              color: '#6b7280',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              display: '-webkit-box',
              WebkitLineClamp: 2,
              WebkitBoxOrient: 'vertical',
              lineHeight: 1.5,
              mb: 1,
            }}
          >
            {template.template_description || '暂无描述'}
          </Typography>

          <Typography variant="caption" sx={{ color: '#9ca3af' }}>
            0 次使用
          </Typography>
        </Box>

        {/* 右侧操作区域 */}
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, ml: 2 }}>
          {/* 启用/禁用开关 */}
          <Switch
            size="small"
            checked={template.is_active}
            onChange={(e) => {
              e.stopPropagation();
              handleToggleTemplate(template.id, template.is_active);
            }}
            sx={{
              '& .MuiSwitch-switchBase': {
                '&.Mui-checked': {
                  color: '#10b981',
                  '& + .MuiSwitch-track': {
                    backgroundColor: '#10b981',
                  },
                },
              },
              '& .MuiSwitch-track': {
                backgroundColor: '#d1d5db',
              },
            }}
          />

          {/* 操作按钮 */}
          <IconButton
            size="small"
            onClick={(e) => {
              e.stopPropagation();
              handleUseTemplate(template);
            }}
            sx={{
              color: '#6b7280',
              '&:hover': {
                color: '#1976d2',
                backgroundColor: 'rgba(25, 118, 210, 0.08)',
              }
            }}
          >
            <Visibility fontSize="small" />
          </IconButton>

          <IconButton
            size="small"
            onClick={(e) => {
              e.stopPropagation();
              handleEditTemplate(template);
            }}
            sx={{
              color: '#6b7280',
              '&:hover': {
                color: '#1976d2',
                backgroundColor: 'rgba(25, 118, 210, 0.08)',
              }
            }}
          >
            <Edit fontSize="small" />
          </IconButton>

          <IconButton
            size="small"
            onClick={(e) => {
              e.stopPropagation();
              handleDeleteTemplate(template.id);
            }}
            sx={{
              color: '#6b7280',
              '&:hover': {
                color: '#ef4444',
                backgroundColor: 'rgba(239, 68, 68, 0.08)',
              }
            }}
          >
            <Delete fontSize="small" />
          </IconButton>
        </Box>
      </Box>
    </Paper>


  );

  return (
    <StyledContainer>
      {/* 头部区域 */}
      <HeaderSection>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Box>
            <Typography variant="h5" sx={{ fontWeight: 600, color: '#333', mb: 0.5 }}>
              写作模板库
            </Typography>
            <Typography variant="body2" sx={{ color: '#999' }}>
              精选的写作模板，助您快速创建高质量内容
            </Typography>
          </Box>
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={() => setShowCreateDialog(true)}
            sx={{
              backgroundColor: '#3b82f6',
              color: 'white',
              borderRadius: 0.5,
              px: 2.5,
              py: 1,
              fontWeight: 500,
              boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
              '&:hover': {
                backgroundColor: '#2563eb',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
              }
            }}
          >
            创建模板
          </Button>
        </Box>



        {/* 搜索和筛选栏 */}
        <Box display="flex" gap={2} alignItems="center" sx={{ pt: 2, borderTop: '1px solid #f0f0f0' }}>
          <TextField
            placeholder="搜索模板名称、描述、内容..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            size="small"
            sx={{
              flex: 1,
              maxWidth: 400,
              '& .MuiOutlinedInput-root': {
                borderRadius: 0.5,
                '& fieldset': {
                  borderColor: '#e5e5e5',
                },
                '&:hover fieldset': {
                  borderColor: '#d0d0d0',
                },
              }
            }}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Search sx={{ color: '#999' }} />
                </InputAdornment>
              ),
            }}
          />

          <FormControl size="small" sx={{ minWidth: 120 }}>
            <Select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              displayEmpty
              sx={{
                borderRadius: 0.5,
                '& .MuiOutlinedInput-notchedOutline': {
                  borderColor: '#e5e5e5',
                },
                '&:hover .MuiOutlinedInput-notchedOutline': {
                  borderColor: '#d0d0d0',
                },
              }}
            >
              <MenuItem value="active">启用</MenuItem>
              <MenuItem value="inactive">禁用</MenuItem>
              <MenuItem value="all">全部状态</MenuItem>
            </Select>
          </FormControl>



        </Box>
      </HeaderSection>

      {/* 内容区域 */}
      <ContentSection>
        {loading ? (
          <Box
            display="flex"
            flexDirection="column"
            alignItems="center"
            justifyContent="center"
            height="400px"
          >
            <LinearProgress sx={{ width: '200px', mb: 2 }} />
            <Typography variant="body2" color="text.secondary">
              正在加载模板...
            </Typography>
          </Box>
        ) : (
          <>
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                gap: 0,
                width: '100%',
                minHeight: '400px',
              }}
            >
              {getCurrentPageTemplates().map(template => renderTemplateListItem(template))}
            </Box>

            {/* 分页组件 */}
            {sortedTemplates.length > 0 && (
              <Box sx={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                mt: 4,
                mb: 2,
                gap: 2
              }}>
                <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.875rem' }}>
                  共 {pagination.total} 个模板，第 {pagination.page} 页，共 {pagination.pages} 页
                </Typography>
                <Pagination
                  count={pagination.pages}
                  page={pagination.page}
                  onChange={handlePageChange}
                  color="primary"
                  size="medium"
                  showFirstButton
                  showLastButton
                  sx={{
                    '& .MuiPaginationItem-root': {
                      borderRadius: 2,
                      fontWeight: 500,
                      fontSize: '0.875rem',
                      minWidth: '36px',
                      height: '36px',
                      margin: '0 2px',
                      '&:hover': {
                        backgroundColor: 'rgba(25, 118, 210, 0.08)',
                      },
                    },
                    '& .Mui-selected': {
                      backgroundColor: '#1976d2',
                      color: '#fff',
                      fontWeight: 600,
                      '&:hover': {
                        backgroundColor: '#1565c0',
                      },
                    },
                    '& .MuiPaginationItem-previousNext': {
                      fontSize: '1rem',
                    },
                  }}
                />
              </Box>
            )}
          </>
        )}
      </ContentSection>

      {/* 模板预览对话框 */}
      <Dialog
        open={showPreview}
        onClose={() => setShowPreview(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Box display="flex" justifyContent="space-between" alignItems="flex-start">
            <Box>
              <Typography variant="h6" sx={{ mb: 0.5 }}>
                {selectedTemplate?.template_name}
              </Typography>
              {selectedTemplate?.template_description && (
                <Typography variant="body2" color="text.secondary">
                  {selectedTemplate.template_description}
                </Typography>
              )}

            </Box>
            <IconButton onClick={() => setShowPreview(false)}>
              <Close />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent dividers>
          {selectedTemplate?.prompt_template ? (
            <Box>
              {/* 模板内容 */}
              <Typography variant="subtitle2" sx={{ mb: 2, fontWeight: 600, color: '#333' }}>
                模板内容
              </Typography>
              <Paper sx={{ p: 3, backgroundColor: 'grey.50', mb: 3 }}>
                <pre style={{ whiteSpace: 'pre-wrap', fontFamily: 'inherit', margin: 0 }}>
                  {selectedTemplate.prompt_template}
                </pre>
              </Paper>

              {/* 默认参数 */}
              {selectedTemplate?.default_parameters && (
                <Box>
                  <Typography variant="subtitle2" sx={{ mb: 2, fontWeight: 600, color: '#333' }}>
                    默认参数
                  </Typography>
                  <Paper sx={{ p: 3, backgroundColor: '#f8f9fa', border: '1px solid #e9ecef' }}>
                    {(() => {
                      // 处理不同的参数格式
                      const params = selectedTemplate.default_parameters;

                      // 如果有 parameters 数组，使用它
                      if (params.parameters && Array.isArray(params.parameters)) {
                        return (
                          <Grid container spacing={2}>
                            {params.parameters.map((param, index) => (
                              <Grid item xs={12} sm={6} key={param.key || index}>
                                <Box sx={{
                                  p: 2,
                                  backgroundColor: '#fff',
                                  borderRadius: 1,
                                  border: '1px solid #e0e0e0'
                                }}>
                                  <Typography variant="caption" sx={{
                                    color: '#666',
                                    fontSize: '0.75rem',
                                    fontWeight: 500,
                                    textTransform: 'uppercase',
                                    letterSpacing: '0.5px'
                                  }}>
                                    {param.key}
                                  </Typography>
                                  <Typography variant="body2" sx={{
                                    mt: 0.5,
                                    color: '#333',
                                    wordBreak: 'break-word'
                                  }}>
                                    <strong>标签:</strong> {param.label}<br/>
                                    <strong>必填:</strong> {param.required ? '是' : '否'}<br/>
                                    <strong>提示:</strong> {param.placeholder || '无'}
                                  </Typography>
                                </Box>
                              </Grid>
                            ))}
                          </Grid>
                        );
                      }

                      // 否则按照键值对处理
                      const entries = Object.entries(params);
                      if (entries.length === 0) {
                        return (
                          <Typography variant="body2" sx={{ color: '#666', fontStyle: 'italic' }}>
                            暂无参数配置
                          </Typography>
                        );
                      }

                      return (
                        <Grid container spacing={2}>
                          {entries.map(([key, value]) => (
                            <Grid item xs={12} sm={6} key={key}>
                              <Box sx={{
                                p: 2,
                                backgroundColor: '#fff',
                                borderRadius: 1,
                                border: '1px solid #e0e0e0'
                              }}>
                                <Typography variant="caption" sx={{
                                  color: '#666',
                                  fontSize: '0.75rem',
                                  fontWeight: 500,
                                  textTransform: 'uppercase',
                                  letterSpacing: '0.5px'
                                }}>
                                  {key}
                                </Typography>
                                <Typography variant="body2" sx={{
                                  mt: 0.5,
                                  color: '#333',
                                  wordBreak: 'break-word'
                                }}>
                                  {typeof value === 'object' ? JSON.stringify(value, null, 2) : String(value)}
                                </Typography>
                              </Box>
                            </Grid>
                          ))}
                        </Grid>
                      );
                    })()}
                  </Paper>
                </Box>
              )}
            </Box>
          ) : (
            <Alert severity="info">该模板暂无预览内容</Alert>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowPreview(false)}>关闭</Button>
        </DialogActions>
      </Dialog>



      {/* Snackbar 通知 */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={3000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          variant="filled"
        >
          {snackbar.message}
        </Alert>
      </Snackbar>

      {/* 新建模板对话框 */}
      <CreateTemplateDialog
        open={showCreateDialog}
        onClose={() => setShowCreateDialog(false)}
        onSuccess={() => {
          // 重新加载模板列表以应用当前的筛选条件
          loadTemplates(false);
          setSnackbar({ open: true, message: '模板创建成功', severity: 'success' });
          setShowCreateDialog(false);
        }}
      />

      {/* 编辑模板对话框 */}
      <EditTemplateDialog
        open={editDialogOpen}
        onClose={() => setEditDialogOpen(false)}
        template={editingTemplate}
        onSuccess={() => {
          // 重新加载模板列表以应用当前的筛选条件
          loadTemplates(false);
          setSnackbar({ open: true, message: '模板更新成功', severity: 'success' });
          setEditDialogOpen(false);
        }}
      />
    </StyledContainer>
  );
}

export default WritingTemplates;