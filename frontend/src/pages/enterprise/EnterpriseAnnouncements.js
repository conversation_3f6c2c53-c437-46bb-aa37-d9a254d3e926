import React, { useState, useEffect, useMemo } from 'react';
import announcementService from '../../services/announcementService';
import { AppConfig } from '../../config/app-config';
import {
  Box,
  Typography,
  Card,
  CardContent,
  CardActions,
  Button,
  Chip,
  Avatar,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  ListItemSecondaryAction,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Divider,
  Alert,
  Skeleton,
  Badge,
  Grid,
  Paper,
  Stack,
  Tooltip,
  Fade,
  Collapse,
  Pagination,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  CircularProgress
} from '@mui/material';
import {
  Campaign,
  Announcement,
  Info,
  Warning,
  Error,
  CheckCircle,
  Close,
  AccessTime,
  Person,
  Visibility,
  ExpandMore,
  ExpandLess,
  NotificationsActive,
  PriorityHigh,
  Schedule,
  TrendingUp
} from '@mui/icons-material';

const EnterpriseAnnouncements = () => {
  const [announcements, setAnnouncements] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedAnnouncement, setSelectedAnnouncement] = useState(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [detailLoading, setDetailLoading] = useState(false);
  // 企业控制中心固定显示企业公告
  const [page, setPage] = useState(0);
  const [rowsPerPage] = useState(5); // 每页显示5条
  const [totalCount, setTotalCount] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [statistics, setStatistics] = useState({
    total: 0,
    unread: 0,
    high_priority: 0,
    feature_updates: 0
  });
  const [error, setError] = useState(null);

  // 获取公告列表 - 企业控制中心固定获取企业公告
  const fetchAnnouncements = async (pageNum = page + 1) => {
    try {
      setLoading(true);
      setError(null);

      console.log('开始获取企业公告，页码:', pageNum);

      // 获取认证token - 使用正确的配置
      const tokenKey = `${AppConfig.storage.prefix}${AppConfig.storage.tokenKey}`;
      const token = localStorage.getItem(tokenKey) || localStorage.getItem('geo_app_token');
      console.log('Token键:', tokenKey);
      console.log('使用token:', token ? `${token.substring(0, 20)}...` : '无token');

      // 企业控制中心固定使用enterprise目标受众
      const selectedAudience = 'enterprise';
      console.log('企业控制中心使用目标受众:', selectedAudience);

      // 先直接用fetch测试API，确保能获取到数据
      const testUrl = `http://localhost:8000/api/v1/announcements?page=${pageNum}&page_size=${rowsPerPage}&target_audience=${selectedAudience}`;

      console.log('直接fetch测试API...');
      console.log('测试URL:', testUrl);

      const testResponse = await fetch(testUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      console.log('直接fetch响应状态:', testResponse.status);
      const testData = await testResponse.json();
      console.log('直接fetch响应数据:', testData);

      if (testResponse.ok && testData.success && testData.data && testData.data.items) {
        console.log('直接fetch成功，使用fetch数据');
        const { items, pagination } = testData.data;

        // 直接处理fetch获取的数据
        if (items && items.length > 0) {
          console.log('处理fetch获取的items:', items);

          const formattedAnnouncements = items.map(item => ({
            id: item.id,
            title: item.title || '无标题',
            content: item.content || '',
            summary: item.summary || '',
            type: mapAnnouncementType(item.type),
            priority: mapPriority(item.priority),
            publishTime: item.publish_time,
            author: item.creator_name || '系统管理员',
            readCount: item.view_count || 0,
            isRead: false,
            tags: getAnnouncementTags(item),
            category: item.type,
            targetAudience: item.target_audience,
            status: item.status,
            isPinned: item.is_pinned,
            isPopup: item.is_popup,
            expireTime: item.expire_time
          }));

          console.log('格式化后的数据:', formattedAnnouncements);
          setAnnouncements(formattedAnnouncements);
          setTotalCount(pagination.total);
          setTotalPages(pagination.pages);
          updateStatistics(formattedAnnouncements, pagination.total);
          return; // 成功处理，直接返回
        }
      }

      console.log('直接fetch失败，尝试使用announcementService...');

      // 如果直接fetch失败，再尝试使用announcementService
      const data = await announcementService.getAnnouncements({
        page: pageNum,
        page_size: rowsPerPage,
        target_audience: selectedAudience
      });

      console.log('announcementService响应数据:', data);
      console.log('data.success:', data.success);
      console.log('data.data:', data.data);
      console.log('data类型:', typeof data);
      console.log('data结构:', Object.keys(data));
      console.log('完整data内容:', JSON.stringify(data, null, 2));

      // announcementService返回response.data，即完整的API响应
      if (data && data.success && data.data) {
        console.log('进入数据处理分支');
        const { items, pagination } = data.data;
        console.log('获取到的items:', items);
        console.log('items类型:', typeof items);
        console.log('items是否为数组:', Array.isArray(items));
        console.log('items长度:', items ? items.length : 'undefined');
        console.log('分页信息:', pagination);
        console.log('获取到的items:', items);
        console.log('items类型:', typeof items);
        console.log('items是否为数组:', Array.isArray(items));
        console.log('items长度:', items ? items.length : 'undefined');
        console.log('分页信息:', pagination);

        if (items && items.length > 0) {
          console.log('原始API数据:', items);
          console.log('第一个item详情:', items[0]);

          // 按发布时间排序
          items.sort((a, b) => new Date(b.publish_time) - new Date(a.publish_time));

          // 转换API数据格式
          const formattedAnnouncements = items.map(item => {
            console.log('处理item:', item);
            const formatted = {
              id: item.id,
              title: item.title || '无标题',
              content: item.content || '',
              summary: item.summary || '',
              type: mapAnnouncementType(item.type),
              priority: mapPriority(item.priority),
              publishTime: item.publish_time,
              author: item.creator_name || '系统管理员',
              readCount: item.view_count || 0,
              isRead: false,
              tags: getAnnouncementTags(item),
              category: item.type,
              targetAudience: item.target_audience,
              status: item.status,
              isPinned: item.is_pinned,
              isPopup: item.is_popup,
              expireTime: item.expire_time
            };
            console.log('格式化后的item:', formatted);
            return formatted;
          });

          console.log('格式化后的数据:', formattedAnnouncements);
          console.log('设置announcements数组，长度:', formattedAnnouncements.length);

          // 使用实际的格式化数据
          console.log('最终设置的数据:', formattedAnnouncements);
          console.log('即将调用setAnnouncements');
          setAnnouncements(formattedAnnouncements);

          // 强制重新渲染
          setTimeout(() => {
            console.log('延迟检查announcements状态:', announcements);
            // 如果状态没有更新，强制设置
            if (announcements.length === 0) {
              console.log('强制重新设置announcements');
              setAnnouncements([...formattedAnnouncements]);
            }
          }, 500);
          setTotalCount(pagination.total);
          setTotalPages(pagination.pages);

          // 验证状态设置
          setTimeout(() => {
            console.log('当前announcements状态:', announcements);
            console.log('当前announcements长度:', announcements.length);
          }, 100);

          // 更新统计信息
          updateStatistics(formattedAnnouncements, pagination.total);
        } else {
          // 没有数据
          setAnnouncements([]);
          setTotalCount(0);
          setTotalPages(0);
          setStatistics({ total: 0, unread: 0, high_priority: 0, feature_updates: 0 });
        }
      } else {

        setError('获取公告数据失败: 数据格式不正确');
      }
    } catch (error) {


      // 检查是否是认证错误
      if (error.response && error.response.status === 401) {
        setError('认证失败，请重新登录');
      } else if (error.response && error.response.status === 403) {
        setError('权限不足，无法访问公告');
      } else if (error.response) {

        setError(`服务器错误: ${error.response.status} - ${error.response.data?.message || '未知错误'}`);
      } else if (error.request) {

        setError('网络连接失败，请检查网络连接');
      } else {

        setError('获取公告列表失败，请稍后重试');
      }
    } finally {
      setLoading(false);
    }
  };

  // 获取公告详情
  const fetchAnnouncementDetail = async (id) => {
    try {
      console.log('获取公告详情，ID:', id);
      const response = await announcementService.getAnnouncementById(id);
      console.log('公告详情API响应:', response);

      // 检查不同的响应格式
      let item = null;
      if (response.success && response.data) {
        item = response.data;
      } else if (response.data) {
        // 有些API直接返回data
        item = response.data;
      } else if (response.success !== false && response.id) {
        // 直接返回公告对象
        item = response;
      } else {
        console.warn('无法解析公告详情响应:', response);
        return null;
      }

      if (item) {
        const detailData = {
          id: item.id,
          title: item.title,
          content: item.content,
          summary: item.summary,
          type: mapAnnouncementType(item.type),
          priority: mapPriority(item.priority), // 映射优先级
          publishTime: item.publish_time,
          author: item.creator_name || '系统管理员',
          readCount: item.view_count || 0,
          isRead: true, // 查看详情后标记为已读
          tags: getAnnouncementTags(item),
          category: item.type,
          targetAudience: item.target_audience,
          status: item.status,
          isPinned: item.is_pinned,
          isPopup: item.is_popup,
          expireTime: item.expire_time
        };
        console.log('格式化后的公告详情:', detailData);
        return detailData;
      }

      return null;
    } catch (error) {
      console.error('获取公告详情失败:', error);
      throw error;
    }
  };

  // 映射公告类型（根据后端模型的实际枚举值）
  const mapAnnouncementType = (apiType) => {
    const typeMap = {
      'system': 'warning',      // 系统公告
      'maintenance': 'warning', // 维护公告
      'feature': 'success',     // 功能公告
      'promotion': 'info',      // 推广公告
      'notice': 'info'          // 通知公告（默认类型）
    };
    return typeMap[apiType] || 'info';
  };

  // 映射优先级（根据后端模型的实际枚举值）
  const mapPriority = (apiPriority) => {
    const priorityMap = {
      'low': 'low',           // 低优先级
      'normal': 'medium',     // 普通优先级 -> 中等
      'high': 'high',         // 高优先级
      'urgent': 'high'        // 紧急优先级 -> 高优先级
    };
    return priorityMap[apiPriority] || 'medium';
  };

  // 生成公告标签
  const getAnnouncementTags = (item) => {
    const tags = [];

    if (item.is_pinned) tags.push('置顶');
    if (item.priority === 'high') tags.push('重要');
    if (item.target_audience === 'enterprise') tags.push('企业');
    if (item.target_audience === 'all') tags.push('全部');

    // 根据类型添加标签（根据后端模型的实际枚举值）
    const typeTagMap = {
      'system': '系统',
      'maintenance': '维护',
      'feature': '功能',
      'promotion': '推广',
      'notice': '通知'
    };

    if (typeTagMap[item.type]) {
      tags.push(typeTagMap[item.type]);
    }

    return tags;
  };

  // 更新统计信息
  const updateStatistics = (announcements, total) => {
    const stats = {
      total: total,
      unread: announcements.filter(a => !a.isRead).length,
      high_priority: announcements.filter(a => a.priority === 'high').length,
      feature_updates: announcements.filter(a => a.category === 'feature').length
    };
    setStatistics(stats);
  };

  useEffect(() => {
    // 检查认证状态 - 使用正确的token键
    const tokenKey = `${AppConfig.storage.prefix}${AppConfig.storage.tokenKey}`;
    const geoToken = localStorage.getItem('geo_app_token'); // 备用检查
    const configToken = localStorage.getItem(tokenKey);

    console.log('Token键配置:', tokenKey);
    console.log('geo_app_token:', geoToken ? '存在' : '不存在');
    console.log('配置token:', configToken ? '存在' : '不存在');

    // 如果没有配置的token但有geo_app_token，复制过去
    if (!configToken && geoToken) {
      console.log('复制geo_app_token到配置键');
      localStorage.setItem(tokenKey, geoToken);
    }

    fetchAnnouncements();
  }, [page]); // 企业控制中心只依赖页码变化

  // 监控announcements状态变化
  useEffect(() => {
    console.log('announcements状态变化:', announcements);
    console.log('announcements长度:', announcements.length);
    console.log('announcements类型:', typeof announcements);
    console.log('announcements是否为数组:', Array.isArray(announcements));
  }, [announcements]);

  // 获取类型图标和颜色
  const getTypeConfig = (type) => {
    const configs = {
      info: {
        icon: <Info />,
        color: '#2196f3',
        bgColor: '#e3f2fd',
        label: '通知'
      },
      success: {
        icon: <CheckCircle />,
        color: '#4caf50',
        bgColor: '#e8f5e8',
        label: '功能'
      },
      warning: {
        icon: <Warning />,
        color: '#ff9800',
        bgColor: '#fff3e0',
        label: '维护'
      },
      error: {
        icon: <Error />,
        color: '#f44336',
        bgColor: '#ffebee',
        label: '安全'
      }
    };
    return configs[type] || configs.info;
  };

  // 获取优先级配置
  const getPriorityConfig = (priority) => {
    const configs = {
      high: { color: '#f44336', label: '高' },
      medium: { color: '#ff9800', label: '中' },
      low: { color: '#4caf50', label: '低' }
    };
    return configs[priority] || configs.medium;
  };

  // 处理公告点击
  const handleAnnouncementClick = async (announcement) => {
    console.log('点击公告:', announcement.title);

    // 先设置基本的公告信息，确保弹窗能够显示
    setSelectedAnnouncement(announcement);
    setDialogOpen(true);
    setDetailLoading(true);

    try {
      // 获取详细信息
      const detailData = await fetchAnnouncementDetail(announcement.id);
      console.log('获取到详细信息:', detailData);

      // 如果获取到详细信息，则更新；否则保持基本信息
      if (detailData) {
        setSelectedAnnouncement(detailData);
      } else {
        console.warn('未获取到详细信息，使用基本信息');
        // 确保基本信息有content字段
        setSelectedAnnouncement({
          ...announcement,
          content: announcement.summary || announcement.content || '暂无详细内容'
        });
      }

      // 标记为已读并更新阅读次数
      setAnnouncements(prev =>
        prev.map(item =>
          item.id === announcement.id
            ? { ...item, isRead: true, readCount: (item.readCount || 0) + 1 }
            : item
        )
      );

      // TODO: 标记已读API暂时注释，等后端实现
      // try {
      //   await announcementService.markAsRead(announcement.id);
      // } catch (error) {
      //   console.warn('标记已读失败:', error);
      // }

    } catch (error) {
      console.error('获取公告详情失败:', error);
      // 如果获取详情失败，仍然显示基本信息
      setSelectedAnnouncement({
        ...announcement,
        content: announcement.summary || announcement.content || '获取详细内容失败，请稍后重试'
      });
    } finally {
      setDetailLoading(false);
    }
  };

  // 关闭详情对话框
  const handleCloseDialog = (event, reason) => {
    console.log('关闭弹窗, reason:', reason);
    // 防止在加载时意外关闭
    if (detailLoading && reason === 'backdropClick') {
      console.log('正在加载，阻止关闭');
      return;
    }
    setDialogOpen(false);
    setSelectedAnnouncement(null);
    setDetailLoading(false);
  };

  // 处理分页变化
  const handleChangePage = (event, newPage) => {
    setPage(newPage);
    // 页面变化时重新获取数据
    fetchAnnouncements(newPage + 1);
  };

  // 获取当前页的公告数据（使用useMemo优化性能）
  const currentPageAnnouncements = useMemo(() => {
    console.log('计算当前页公告数据');
    console.log('当前公告数据:', announcements);
    console.log('announcements长度:', announcements.length);
    console.log('announcements类型:', typeof announcements);
    console.log('announcements是否为数组:', Array.isArray(announcements));
    return announcements;
  }, [announcements]);

  // 格式化时间
  const formatTime = (timeString) => {
    const date = new Date(timeString);
    const now = new Date();
    const diff = now - date;
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    
    if (days === 0) {
      const hours = Math.floor(diff / (1000 * 60 * 60));
      if (hours === 0) {
        const minutes = Math.floor(diff / (1000 * 60));
        return `${minutes}分钟前`;
      }
      return `${hours}小时前`;
    } else if (days === 1) {
      return '昨天';
    } else if (days < 7) {
      return `${days}天前`;
    } else {
      return date.toLocaleDateString('zh-CN');
    }
  };

  if (loading) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography variant="h4" gutterBottom>
          最新公告
        </Typography>
        <Grid container spacing={3}>
          {[1, 2, 3].map((item) => (
            <Grid item xs={12} key={item}>
              <Card>
                <CardContent>
                  <Skeleton variant="text" width="60%" height={32} />
                  <Skeleton variant="text" width="100%" height={20} sx={{ mt: 1 }} />
                  <Skeleton variant="text" width="80%" height={20} />
                  <Box sx={{ display: 'flex', gap: 1, mt: 2 }}>
                    <Skeleton variant="rectangular" width={60} height={24} />
                    <Skeleton variant="rectangular" width={40} height={24} />
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* 页面标题 */}
      <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
        <Box>
          <Typography variant="h4" gutterBottom sx={{ fontWeight: 600, color: '#1a1a1a' }}>
            最新公告
          </Typography>
          <Typography variant="body1" color="text.secondary">
            查看系统公告和重要通知信息
          </Typography>
        </Box>
        <Button
          variant="outlined"
          size="small"
          onClick={() => {
            console.log('手动刷新企业公告数据');
            fetchAnnouncements();
          }}
          sx={{ mt: 1 }}
        >
          刷新数据
        </Button>
      </Box>

      {/* 统计信息 */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Paper sx={{ p: 2, textAlign: 'center', bgcolor: '#e3f2fd' }}>
            <Typography variant="h6" color="#1976d2">
              {totalCount}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              总公告数
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Paper sx={{ p: 2, textAlign: 'center', bgcolor: '#ffebee' }}>
            <Typography variant="h6" color="#d32f2f">
              {statistics.unread}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              未读公告
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Paper sx={{ p: 2, textAlign: 'center', bgcolor: '#fff3e0' }}>
            <Typography variant="h6" color="#f57c00">
              {statistics.high_priority}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              高优先级
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Paper sx={{ p: 2, textAlign: 'center', bgcolor: '#e8f5e8' }}>
            <Typography variant="h6" color="#388e3c">
              {statistics.feature_updates}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              功能更新
            </Typography>
          </Paper>
        </Grid>
      </Grid>

      {/* 错误提示 */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* 公告列表 */}
      <Card>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell width="50">状态</TableCell>
                <TableCell width="60">类型</TableCell>
                <TableCell>标题</TableCell>
                <TableCell width="100">优先级</TableCell>
                <TableCell width="120">发布者</TableCell>
                <TableCell width="120">发布时间</TableCell>
                <TableCell width="100">阅读次数</TableCell>
                <TableCell width="100">操作</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                // 加载状态
                Array.from({ length: rowsPerPage }).map((_, index) => (
                  <TableRow key={index}>
                    <TableCell><Skeleton width={20} height={20} /></TableCell>
                    <TableCell><Skeleton width={40} height={24} /></TableCell>
                    <TableCell><Skeleton width="80%" height={20} /></TableCell>
                    <TableCell><Skeleton width={40} height={24} /></TableCell>
                    <TableCell><Skeleton width={60} height={20} /></TableCell>
                    <TableCell><Skeleton width={80} height={20} /></TableCell>
                    <TableCell><Skeleton width={50} height={20} /></TableCell>
                    <TableCell><Skeleton width={60} height={32} /></TableCell>
                  </TableRow>
                ))
              ) : currentPageAnnouncements.length === 0 ? (
                // 空状态
                <TableRow>
                  <TableCell colSpan={8} sx={{ textAlign: 'center', py: 4 }}>
                    <Typography variant="body2" color="text.secondary">
                      暂无公告数据
                    </Typography>
                  </TableCell>
                </TableRow>
              ) : (
                currentPageAnnouncements.map((announcement) => {
                  const typeConfig = getTypeConfig(announcement.type);
                  const priorityConfig = getPriorityConfig(announcement.priority);

                  return (
                    <TableRow
                      key={announcement.id}
                      hover
                      sx={{
                        '&:hover': {
                          backgroundColor: '#f5f5f5'
                        }
                      }}
                    >
                      {/* 状态列 */}
                      <TableCell>
                        {!announcement.isRead ? (
                          <Badge color="primary" variant="dot" />
                        ) : (
                          <CheckCircle sx={{ fontSize: 16, color: '#4caf50' }} />
                        )}
                      </TableCell>

                      {/* 类型列 */}
                      <TableCell>
                        <Tooltip title={typeConfig.label}>
                          <Avatar
                            sx={{
                              bgcolor: typeConfig.bgColor,
                              color: typeConfig.color,
                              width: 32,
                              height: 32
                            }}
                          >
                            {typeConfig.icon}
                          </Avatar>
                        </Tooltip>
                      </TableCell>

                      {/* 标题列 */}
                      <TableCell>
                        <Box>
                          <Typography
                            variant="subtitle2"
                            sx={{
                              fontWeight: announcement.isRead ? 400 : 600,
                              color: announcement.isRead ? '#666' : '#1a1a1a',
                              mb: 0.5
                            }}
                          >
                            {announcement.title}
                          </Typography>
                          <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
                            {announcement.tags.slice(0, 2).map((tag, index) => (
                              <Chip
                                key={index}
                                label={tag}
                                size="small"
                                variant="outlined"
                                sx={{ fontSize: '0.7rem', height: 20 }}
                              />
                            ))}
                            {announcement.tags.length > 2 && (
                              <Chip
                                label={`+${announcement.tags.length - 2}`}
                                size="small"
                                variant="outlined"
                                sx={{ fontSize: '0.7rem', height: 20 }}
                              />
                            )}
                          </Box>
                        </Box>
                      </TableCell>

                      {/* 优先级列 */}
                      <TableCell>
                        <Chip
                          label={priorityConfig.label}
                          size="small"
                          sx={{
                            bgcolor: `${priorityConfig.color}20`,
                            color: priorityConfig.color,
                            fontWeight: 500,
                            fontSize: '0.75rem'
                          }}
                        />
                      </TableCell>

                      {/* 发布者列 */}
                      <TableCell>
                        <Typography variant="body2" color="text.secondary">
                          {announcement.author}
                        </Typography>
                      </TableCell>

                      {/* 发布时间列 */}
                      <TableCell>
                        <Typography variant="body2" color="text.secondary">
                          {formatTime(announcement.publishTime)}
                        </Typography>
                      </TableCell>

                      {/* 阅读次数列 */}
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                          <Visibility sx={{ fontSize: 14, color: '#666' }} />
                          <Typography variant="body2" color="text.secondary">
                            {announcement.readCount}
                          </Typography>
                        </Box>
                      </TableCell>

                      {/* 操作列 */}
                      <TableCell>
                        <Button
                          size="small"
                          variant="outlined"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleAnnouncementClick(announcement);
                          }}
                          sx={{ fontSize: '0.75rem' }}
                        >
                          查看
                        </Button>
                      </TableCell>
                    </TableRow>
                  );
                })
              )}
            </TableBody>
          </Table>
        </TableContainer>

        {/* 分页组件 */}
        <TablePagination
          component="div"
          count={totalCount}
          page={page}
          onPageChange={handleChangePage}
          rowsPerPage={rowsPerPage}
          rowsPerPageOptions={[5]}
          labelDisplayedRows={({ from, to, count }) =>
            `第 ${from}-${to} 条，共 ${count} 条`
          }
          labelRowsPerPage="每页显示:"
          sx={{
            borderTop: '1px solid #e0e0e0',
            '.MuiTablePagination-toolbar': {
              paddingLeft: 2,
              paddingRight: 2
            }
          }}
        />
      </Card>

      {/* 公告详情对话框 */}
      <Dialog
        open={dialogOpen}
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
        slotProps={{
          paper: {
            sx: { borderRadius: 2 }
          }
        }}
      >
        {selectedAnnouncement && (
          <>
            <DialogTitle sx={{ pb: 1 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Avatar sx={{ 
                    bgcolor: getTypeConfig(selectedAnnouncement.type).bgColor,
                    color: getTypeConfig(selectedAnnouncement.type).color
                  }}>
                    {getTypeConfig(selectedAnnouncement.type).icon}
                  </Avatar>
                  <Box>
                    <Typography variant="h6" sx={{ fontWeight: 600 }}>
                      {selectedAnnouncement.title}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      发布时间: {selectedAnnouncement.publishTime} | 作者: {selectedAnnouncement.author}
                    </Typography>
                  </Box>
                </Box>
                <IconButton onClick={handleCloseDialog} size="small">
                  <Close />
                </IconButton>
              </Box>
            </DialogTitle>
            
            <DialogContent>
              {detailLoading ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
                  <CircularProgress />
                </Box>
              ) : (
                <>
                  <Box sx={{ mb: 2 }}>
                    <Stack direction="row" spacing={1} flexWrap="wrap">
                      <Chip
                        label={getTypeConfig(selectedAnnouncement.type).label}
                        size="small"
                        sx={{
                          bgcolor: getTypeConfig(selectedAnnouncement.type).bgColor,
                          color: getTypeConfig(selectedAnnouncement.type).color
                        }}
                      />
                      <Chip
                        label={`优先级: ${getPriorityConfig(selectedAnnouncement.priority).label}`}
                        size="small"
                        sx={{
                          bgcolor: `${getPriorityConfig(selectedAnnouncement.priority).color}20`,
                          color: getPriorityConfig(selectedAnnouncement.priority).color
                        }}
                      />
                      {selectedAnnouncement.tags && selectedAnnouncement.tags.map((tag, index) => (
                        <Chip
                          key={index}
                          label={tag}
                          size="small"
                          variant="outlined"
                        />
                      ))}
                    </Stack>
                  </Box>

                  <Typography
                    variant="body1"
                    sx={{
                      lineHeight: 1.8,
                      whiteSpace: 'pre-line'
                    }}
                  >
                    {selectedAnnouncement.content}
                  </Typography>

                  <Box sx={{ mt: 3, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>
                    <Typography variant="caption" color="text.secondary">
                      阅读次数: {selectedAnnouncement.readCount || 0} |
                      发布时间: {selectedAnnouncement.publishTime} |
                      分类: {selectedAnnouncement.category}
                    </Typography>
                  </Box>
                </>
              )}
            </DialogContent>
            
            <DialogActions sx={{ p: 2 }}>
              <Button onClick={handleCloseDialog} variant="contained">
                关闭
              </Button>
            </DialogActions>
          </>
        )}
      </Dialog>
    </Box>
  );
};

export default EnterpriseAnnouncements;
