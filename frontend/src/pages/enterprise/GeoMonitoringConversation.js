import React, { useState, useRef, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  TextField,
  IconButton,
  Button,
  Card,
  Grid,
  Avatar,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemButton,
  ListItemIcon,
  ListItemAvatar,
  Divider,
  InputAdornment,
  Menu,
  MenuItem,
  Tooltip,
  Badge,
  Stack,
  LinearProgress,
  CircularProgress,
  Collapse,
  Alert,
  Skeleton,
  ToggleButton,
  ToggleButtonGroup,
  Dialog,
  DialogContent,
  DialogTitle,
  DialogActions,
} from '@mui/material';
import {
  Add,
  Search,
  Send,
  MoreHoriz,
  Edit,
  Delete,
  ContentCopy,
  Share,
  Download,
  Refresh,
  KeyboardVoice,
  AttachFile,
  Image as ImageIcon,
  InsertChart,
  TableChart,
  BarChart,
  PieChart,
  ShowChart,
  Timeline,
  TrendingUp,
  TrendingDown,
  Assessment,
  Analytics,
  QueryStats,
  Insights,
  AutoAwesome,
  Psychology,
  SmartToy,
  Public,
  Speed,
  DataUsage,
  CompareArrows,
  LocationOn,
  DeviceHub,
  Cloud,
  Storage,
  Memory,
  BubbleChart,
  ScatterPlot,
  DonutLarge,
  WaterfallChart,
  CandlestickChart,
  StackedLineChart,
  MultilineChart,
  Leaderboard,
  ThumbUp,
  ThumbDown,
  StarBorder,
  Star,
  BookmarkBorder,
  Bookmark,
  ExpandMore,
  ExpandLess,
  ChevronRight,
  Close,
  Fullscreen,
  FullscreenExit,
  DarkMode,
  LightMode,
  Settings,
  Help,
  ArrowBack,
  ArrowForward,
  History,
  Schedule,
  CalendarToday,
  FilterList,
  Sort,
  ViewModule,
  ViewList,
  Dashboard,
  Widgets,
  Extension,
  IntegrationInstructions,
  Api,
  Code,
  Terminal,
  DataObject,
  Functions,
  Calculate,
  AutoGraph,
  ModelTraining,
  Hub,
  Sync,
  CloudSync,
  Loop,
  PlayArrow,
  Pause,
  Stop,
  CheckCircle,
  Error,
  Warning,
  Info,
  HelpOutline,
  Lightbulb,
  TipsAndUpdates,
  AutoFixHigh,
  Magic,
  Bolt,
  FlashOn,
  ElectricBolt,
  Explore,
  RocketLaunch,
  Science,
  Biotech,
  PsychologyAlt,
  Chat,
  AutoMode,
  OpenInFull,
  CloseFullscreen,
} from '@mui/icons-material';
import { styled, alpha, keyframes } from '@mui/material/styles';

// 动画定义
const fadeIn = keyframes`
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
`;

const pulse = keyframes`
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
`;

// 样式组件
const MainContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  height: '100vh',
  backgroundColor: '#f8f9fa',
  overflow: 'hidden',
}));

const ChatContainer = styled(Box)(({ theme }) => ({
  flex: 1.5,
  display: 'flex',
  flexDirection: 'column',
  backgroundColor: '#ffffff',
  borderRight: '1px solid rgba(0, 0, 0, 0.08)',
}));

const ChatHeader = styled(Box)(({ theme }) => ({
  padding: theme.spacing(2),
  borderBottom: '1px solid rgba(0, 0, 0, 0.08)',
  backgroundColor: '#ffffff',
}));

const HistoryBar = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(1),
  overflowX: 'auto',
  padding: theme.spacing(1, 0),
  marginTop: theme.spacing(1),
  '&::-webkit-scrollbar': {
    height: '4px',
  },
  '&::-webkit-scrollbar-thumb': {
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
    borderRadius: '2px',
  },
}));

const HistoryChip = styled(Chip)(({ theme, selected }) => ({
  borderRadius: theme.spacing(2),
  backgroundColor: selected ? '#6366f1' : 'rgba(0, 0, 0, 0.04)',
  color: selected ? '#ffffff' : theme.palette.text.primary,
  border: selected ? '1px solid #6366f1' : '1px solid rgba(0, 0, 0, 0.08)',
  cursor: 'pointer',
  transition: 'all 0.2s ease',
  minWidth: 100,
  '&:hover': {
    backgroundColor: selected ? '#5558e3' : 'rgba(0, 0, 0, 0.08)',
  },
}));

const MessagesContainer = styled(Box)(({ theme }) => ({
  flex: 1,
  overflowY: 'auto',
  padding: theme.spacing(3),
  backgroundColor: '#fafafa',
  '&::-webkit-scrollbar': {
    width: '6px',
  },
  '&::-webkit-scrollbar-thumb': {
    backgroundColor: 'rgba(0, 0, 0, 0.2)',
    borderRadius: '3px',
  },
}));

const MessageBubble = styled(Box)(({ theme, isUser }) => ({
  display: 'flex',
  justifyContent: isUser ? 'flex-end' : 'flex-start',
  marginBottom: theme.spacing(2),
  animation: `${fadeIn} 0.3s ease`,
}));

const BubbleContent = styled(Box)(({ theme, isUser }) => ({
  maxWidth: '70%',
  padding: theme.spacing(1.5, 2),
  borderRadius: theme.spacing(2),
  backgroundColor: isUser ? '#6366f1' : '#ffffff',
  color: isUser ? '#ffffff' : theme.palette.text.primary,
  boxShadow: isUser 
    ? '0 2px 8px rgba(99, 102, 241, 0.25)' 
    : '0 2px 8px rgba(0, 0, 0, 0.08)',
  border: isUser ? 'none' : '1px solid rgba(0, 0, 0, 0.08)',
}));

const InputContainer = styled(Box)(({ theme }) => ({
  padding: theme.spacing(2),
  borderTop: '1px solid rgba(0, 0, 0, 0.08)',
  backgroundColor: '#ffffff',
}));

const DashboardContainer = styled(Box)(({ theme }) => ({
  flex: 2,
  backgroundColor: '#fafafa',
  overflowY: 'auto',
  padding: theme.spacing(3),
}));

const DashboardCard = styled(Card)(({ theme }) => ({
  borderRadius: theme.spacing(2),
  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
  border: '1px solid rgba(0, 0, 0, 0.08)',
  transition: 'all 0.3s ease',
  animation: `${fadeIn} 0.5s ease`,
  '&:hover': {
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
  },
}));

const MetricCard = styled(Box)(({ theme }) => ({
  padding: theme.spacing(2),
  borderRadius: theme.spacing(1.5),
  backgroundColor: '#ffffff',
  border: '1px solid rgba(0, 0, 0, 0.08)',
  transition: 'all 0.3s ease',
  '&:hover': {
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.08)',
    transform: 'translateY(-2px)',
  },
}));

const StyledTextField = styled(TextField)(({ theme }) => ({
  '& .MuiOutlinedInput-root': {
    borderRadius: theme.spacing(3),
    backgroundColor: '#f8f9fa',
    '& fieldset': {
      borderColor: 'rgba(0, 0, 0, 0.08)',
    },
    '&:hover fieldset': {
      borderColor: 'rgba(0, 0, 0, 0.16)',
    },
    '&.Mui-focused fieldset': {
      borderColor: '#6366f1',
    },
  },
}));

const SuggestionChip = styled(Chip)(({ theme }) => ({
  margin: theme.spacing(0.5),
  borderRadius: theme.spacing(2),
  backgroundColor: 'rgba(99, 102, 241, 0.08)',
  color: '#6366f1',
  border: '1px solid rgba(99, 102, 241, 0.2)',
  '&:hover': {
    backgroundColor: 'rgba(99, 102, 241, 0.12)',
    borderColor: '#6366f1',
  },
}));

// 预设对话历史
const initialConversations = [
  { id: 1, title: '今日流量分析', time: '10分钟前', preview: '显示今天的网站流量数据...' },
  { id: 2, title: 'SEO关键词排名', time: '1小时前', preview: '分析本周关键词排名变化...' },
  { id: 3, title: '竞争对手监控', time: '3小时前', preview: '对比主要竞争对手的表现...' },
  { id: 4, title: 'AI性能报告', time: '昨天', preview: 'AI服务的响应时间和准确率...' },
];

// 快速提示
const quickPrompts = [
  { icon: <TrendingUp />, text: '显示今日流量趋势', color: '#10b981' },
  { icon: <Assessment />, text: '分析关键词排名', color: '#3b82f6' },
  { icon: <CompareArrows />, text: '对比竞争对手', color: '#8b5cf6' },
  { icon: <Psychology />, text: 'AI性能分析', color: '#ec4899' },
  { icon: <Public />, text: '地域分布图', color: '#f59e0b' },
  { icon: <Speed />, text: '网站性能监控', color: '#06b6d4' },
];

function GeoMonitoringConversation() {
  const [selectedConversation, setSelectedConversation] = useState(1);
  const [conversations, setConversations] = useState(initialConversations);
  const [messages, setMessages] = useState([
    {
      id: 1,
      type: 'assistant',
      content: '您好！我是您的GEO监控助手。我可以帮您分析流量数据、监控SEO表现、对比竞争对手，以及生成各种数据报告。请问有什么可以帮助您的？',
    },
  ]);
  const [inputValue, setInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [dashboardData, setDashboardData] = useState(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [showHistory, setShowHistory] = useState(false);
  const [mode, setMode] = useState('chat'); // 'chat' or 'agent'
  const [htmlContent, setHtmlContent] = useState('');
  const [isFullscreen, setIsFullscreen] = useState(false);
  const messagesEndRef = useRef(null);
  const htmlContainerRef = useRef(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Mock API function to simulate backend HTML generation
  const mockGenerateHTML = async (prompt) => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    // Return mock HTML based on prompt
    const htmlTemplates = {
      default: `
        <!DOCTYPE html>
        <html>
        <head>
          <style>
            body { font-family: Arial, sans-serif; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
            .container { max-width: 1200px; margin: 0 auto; background: white; border-radius: 10px; padding: 30px; box-shadow: 0 20px 60px rgba(0,0,0,0.3); }
            h1 { color: #333; border-bottom: 2px solid #667eea; padding-bottom: 10px; }
            .metrics { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 20px 0; }
            .metric-card { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; }
            .metric-value { font-size: 36px; font-weight: bold; margin: 10px 0; }
            .metric-label { font-size: 14px; opacity: 0.9; }
            .chart { background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0; height: 300px; display: flex; align-items: center; justify-content: center; }
          </style>
        </head>
        <body>
          <div class="container">
            <h1>📊 数据分析报告</h1>
            <p>基于您的查询："${prompt}"，生成以下分析报告：</p>
            <div class="metrics">
              <div class="metric-card">
                <div class="metric-label">总访问量</div>
                <div class="metric-value">125,432</div>
                <div class="metric-label">↑ 15.3% vs 上周</div>
              </div>
              <div class="metric-card">
                <div class="metric-label">用户增长</div>
                <div class="metric-value">8,756</div>
                <div class="metric-label">↑ 23.1% vs 上月</div>
              </div>
              <div class="metric-card">
                <div class="metric-label">转化率</div>
                <div class="metric-value">4.8%</div>
                <div class="metric-label">↑ 0.5% vs 平均</div>
              </div>
            </div>
            <div class="chart">
              <svg width="100%" height="100%" viewBox="0 0 400 200">
                <polyline points="20,180 60,150 100,160 140,120 180,100 220,80 260,90 300,60 340,40 380,30" 
                  stroke="#667eea" stroke-width="3" fill="none"/>
                <text x="200" y="190" text-anchor="middle" fill="#666">时间趋势图</text>
              </svg>
            </div>
          </div>
        </body>
        </html>
      `,
      seo: `
        <!DOCTYPE html>
        <html>
        <head>
          <style>
            body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; padding: 20px; background: #f0f2f5; }
            .dashboard { max-width: 1400px; margin: 0 auto; }
            .header { background: linear-gradient(90deg, #4f46e5 0%, #7c3aed 100%); color: white; padding: 30px; border-radius: 15px; margin-bottom: 30px; }
            .kpi-grid { display: grid; grid-template-columns: repeat(4, 1fr); gap: 20px; margin-bottom: 30px; }
            .kpi-card { background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.07); }
            .kpi-title { color: #6b7280; font-size: 14px; margin-bottom: 8px; }
            .kpi-value { color: #111827; font-size: 32px; font-weight: bold; }
            .kpi-change { color: #10b981; font-size: 14px; margin-top: 8px; }
            .table-container { background: white; border-radius: 12px; padding: 25px; box-shadow: 0 4px 6px rgba(0,0,0,0.07); }
            table { width: 100%; border-collapse: collapse; }
            th { background: #f9fafb; padding: 12px; text-align: left; font-weight: 600; color: #374151; }
            td { padding: 12px; border-top: 1px solid #e5e7eb; }
            .badge { display: inline-block; padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; }
            .badge-success { background: #d1fae5; color: #065f46; }
            .badge-warning { background: #fed7aa; color: #92400e; }
          </style>
        </head>
        <body>
          <div class="dashboard">
            <div class="header">
              <h1>🔍 SEO 性能监控面板</h1>
              <p>实时追踪关键词排名和搜索引擎优化表现</p>
            </div>
            <div class="kpi-grid">
              <div class="kpi-card">
                <div class="kpi-title">平均排名</div>
                <div class="kpi-value">3.2</div>
                <div class="kpi-change">↑ 0.8 位</div>
              </div>
              <div class="kpi-card">
                <div class="kpi-title">关键词覆盖</div>
                <div class="kpi-value">856</div>
                <div class="kpi-change">↑ 125 个</div>
              </div>
              <div class="kpi-card">
                <div class="kpi-title">点击率</div>
                <div class="kpi-value">5.4%</div>
                <div class="kpi-change">↑ 1.2%</div>
              </div>
              <div class="kpi-card">
                <div class="kpi-title">展现量</div>
                <div class="kpi-value">1.2M</div>
                <div class="kpi-change">↑ 230K</div>
              </div>
            </div>
            <div class="table-container">
              <h2>🎯 关键词排名TOP10</h2>
              <table>
                <thead>
                  <tr>
                    <th>关键词</th>
                    <th>当前排名</th>
                    <th>变化</th>
                    <th>搜索量</th>
                    <th>状态</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>AI内容生成</td>
                    <td>2</td>
                    <td style="color: #10b981;">↑ 1</td>
                    <td>12,500</td>
                    <td><span class="badge badge-success">优秀</span></td>
                  </tr>
                  <tr>
                    <td>SEO优化工具</td>
                    <td>3</td>
                    <td style="color: #ef4444;">↓ 1</td>
                    <td>8,900</td>
                    <td><span class="badge badge-success">良好</span></td>
                  </tr>
                  <tr>
                    <td>内容营销平台</td>
                    <td>5</td>
                    <td style="color: #6b7280;">-</td>
                    <td>6,200</td>
                    <td><span class="badge badge-warning">关注</span></td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </body>
        </html>
      `,
      competitor: `
        <!DOCTYPE html>
        <html>
        <head>
          <style>
            body { font-family: system-ui; margin: 0; padding: 20px; background: #1a1a2e; color: #eee; }
            .container { max-width: 1400px; margin: 0 auto; }
            .title { font-size: 36px; margin-bottom: 30px; background: linear-gradient(45deg, #00ff88, #00b4d8); -webkit-background-clip: text; -webkit-text-fill-color: transparent; }
            .grid { display: grid; grid-template-columns: repeat(3, 1fr); gap: 25px; }
            .card { background: #16213e; border-radius: 15px; padding: 25px; border: 1px solid #0f3460; }
            .card h3 { color: #00ff88; margin-top: 0; }
            .progress-bar { background: #0f3460; height: 30px; border-radius: 15px; overflow: hidden; margin: 15px 0; }
            .progress-fill { height: 100%; background: linear-gradient(90deg, #00ff88, #00b4d8); display: flex; align-items: center; justify-content: flex-end; padding-right: 10px; color: #1a1a2e; font-weight: bold; }
            .radar-chart { width: 100%; height: 300px; display: flex; align-items: center; justify-content: center; background: #0f3460; border-radius: 10px; margin-top: 20px; }
          </style>
        </head>
        <body>
          <div class="container">
            <h1 class="title">⚔️ 竞争对手分析报告</h1>
            <div class="grid">
              <div class="card">
                <h3>您的网站</h3>
                <p>月流量: 285,430</p>
                <div class="progress-bar">
                  <div class="progress-fill" style="width: 85%;">85分</div>
                </div>
                <p>SEO得分: 85/100</p>
                <p>内容质量: ⭐⭐⭐⭐</p>
                <p>用户体验: 优秀</p>
              </div>
              <div class="card">
                <h3>竞争对手A</h3>
                <p>月流量: 320,000</p>
                <div class="progress-bar">
                  <div class="progress-fill" style="width: 78%; background: linear-gradient(90deg, #ff6b6b, #ffd166);">78分</div>
                </div>
                <p>SEO得分: 78/100</p>
                <p>内容质量: ⭐⭐⭐⭐</p>
                <p>用户体验: 良好</p>
              </div>
              <div class="card">
                <h3>竞争对手B</h3>
                <p>月流量: 280,000</p>
                <div class="progress-bar">
                  <div class="progress-fill" style="width: 72%; background: linear-gradient(90deg, #ff6b6b, #ff9a00);">72分</div>
                </div>
                <p>SEO得分: 72/100</p>
                <p>内容质量: ⭐⭐⭐</p>
                <p>用户体验: 一般</p>
              </div>
            </div>
            <div class="card" style="margin-top: 25px;">
              <h3>📊 综合对比分析</h3>
              <div class="radar-chart">
                <svg width="400" height="280" viewBox="0 0 400 280">
                  <polygon points="200,40 320,100 320,180 200,240 80,180 80,100" fill="none" stroke="#0f3460" stroke-width="2"/>
                  <polygon points="200,80 280,120 280,160 200,200 120,160 120,120" fill="rgba(0,255,136,0.2)" stroke="#00ff88" stroke-width="2"/>
                  <text x="200" y="30" text-anchor="middle" fill="#eee">流量</text>
                  <text x="340" y="100" fill="#eee">SEO</text>
                  <text x="340" y="180" fill="#eee">内容</text>
                  <text x="200" y="260" text-anchor="middle" fill="#eee">技术</text>
                  <text x="60" y="180" text-anchor="end" fill="#eee">用户</text>
                  <text x="60" y="100" text-anchor="end" fill="#eee">社交</text>
                </svg>
              </div>
            </div>
          </div>
        </body>
        </html>
      `
    };

    // Select template based on keywords in prompt
    let selectedTemplate = htmlTemplates.default;
    if (prompt.includes('SEO') || prompt.includes('关键词') || prompt.includes('排名')) {
      selectedTemplate = htmlTemplates.seo;
    } else if (prompt.includes('竞争') || prompt.includes('对手')) {
      selectedTemplate = htmlTemplates.competitor;
    }

    return selectedTemplate;
  };

  const handleNewConversation = () => {
    const newConv = {
      id: Math.max(...conversations.map(c => c.id)) + 1,
      title: `新对话 ${conversations.length + 1}`,
      time: '刚刚',
      preview: '新的监控分析...',
    };
    setConversations([newConv, ...conversations]);
    setSelectedConversation(newConv.id);
    setMessages([{
      id: 1,
      type: 'assistant',
      content: '您好！我是您的GEO监控助手。请问有什么可以帮助您的？',
    }]);
    setDashboardData(null);
  };

  const handleSelectConversation = (convId) => {
    setSelectedConversation(convId);
    // 这里可以加载对应的历史消息
    const conv = conversations.find(c => c.id === convId);
    setMessages([{
      id: 1,
      type: 'assistant',
      content: `已切换到对话：${conv.title}`,
    }]);
    setDashboardData(null);
  };

  const handleDeleteConversation = (convId) => {
    if (conversations.length > 1) {
      const newConvs = conversations.filter(c => c.id !== convId);
      setConversations(newConvs);
      if (selectedConversation === convId) {
        setSelectedConversation(newConvs[0].id);
        handleSelectConversation(newConvs[0].id);
      }
    }
  };

  const handleSendMessage = async (text = null) => {
    const messageText = text || inputValue;
    if (!messageText.trim()) return;

    // 添加用户消息
    const userMessage = {
      id: messages.length + 1,
      type: 'user',
      content: messageText,
    };
    setMessages([...messages, userMessage]);
    setInputValue('');
    setIsTyping(true);

    if (mode === 'agent') {
      // Agent模式：直接生成HTML页面
      try {
        setIsGenerating(true);
        const htmlResponse = await mockGenerateHTML(messageText);
        setHtmlContent(htmlResponse);
        setDashboardData(null); // Clear dashboard data when showing HTML
        
        const aiResponse = {
          id: messages.length + 2,
          type: 'assistant',
          content: '✨ 页面已生成完成！您可以在右侧查看生成的HTML页面，点击全屏按钮可以放大查看。',
        };
        setMessages(prev => [...prev, aiResponse]);
      } catch (error) {
        const errorResponse = {
          id: messages.length + 2,
          type: 'assistant',
          content: '❌ 生成页面时出现错误，请稍后重试。',
        };
        setMessages(prev => [...prev, errorResponse]);
      } finally {
        setIsTyping(false);
        setIsGenerating(false);
      }
    } else {
      // Chat模式：传统对话模式
      setTimeout(() => {
        const aiResponse = {
          id: messages.length + 2,
          type: 'assistant',
          content: generateAIResponse(messageText),
        };
        setMessages(prev => [...prev, aiResponse]);
        setIsTyping(false);

        // 生成相应的仪表板
        if (messageText.includes('流量') || messageText.includes('分析') || messageText.includes('报告')) {
          setIsGenerating(true);
          setTimeout(() => {
            setDashboardData(generateDashboardData(messageText));
            setHtmlContent(''); // Clear HTML content when showing dashboard
            setIsGenerating(false);
          }, 1500);
        }
      }, 1000);
    }
  };

  const generateAIResponse = (prompt) => {
    if (prompt.includes('流量')) {
      return '好的，我正在为您生成今日流量分析报告。数据显示今日访问量为15,432次，相比昨天增长了12.5%。主要流量来源为自然搜索（45%）和直接访问（28%）。';
    } else if (prompt.includes('关键词') || prompt.includes('SEO')) {
      return '我已经分析了您的关键词排名情况。"AI内容生成"关键词排名第2位，上升1位；"SEO优化工具"排名第3位，下降1位。整体排名表现良好。';
    } else if (prompt.includes('竞争')) {
      return '竞争对手分析完成。您的网站月流量为285,430，略低于竞争对手A（320,000），但高于竞争对手B（280,000）。在内容质量评分上，您的得分为85分，处于中等水平。';
    } else {
      return '我理解您的需求。让我为您准备相关的数据分析和可视化报告。';
    }
  };

  const generateDashboardData = (prompt) => {
    return {
      metrics: [
        { label: '今日访问', value: '15,432', change: '+12.5%', trend: 'up', icon: <TrendingUp /> },
        { label: '平均排名', value: '3.2', change: '-0.8', trend: 'up', icon: <Leaderboard /> },
        { label: '转化率', value: '4.8%', change: '+1.2%', trend: 'up', icon: <ShowChart /> },
        { label: '跳出率', value: '32.5%', change: '-2.3%', trend: 'down', icon: <TrendingDown /> },
      ],
      charts: [
        { type: 'line', title: '7日流量趋势', id: 'traffic-trend' },
        { type: 'bar', title: '流量来源分布', id: 'traffic-source' },
        { type: 'pie', title: '设备类型', id: 'device-type' },
        { type: 'geo', title: '地域分布', id: 'geo-distribution' },
      ],
    };
  };

  const renderDashboard = () => {
    if (!dashboardData) return null;

    return (
      <Box sx={{ mt: 3 }}>
        {/* 指标卡片 */}
        <Grid container spacing={2} sx={{ mb: 3 }}>
          {dashboardData.metrics.map((metric, index) => (
            <Grid item xs={12} sm={6} md={6} lg={3} key={index}>
              <MetricCard>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography variant="caption" color="text.secondary">
                      {metric.label}
                    </Typography>
                    <Typography variant="h5" fontWeight="bold" sx={{ mt: 0.5 }}>
                      {metric.value}
                    </Typography>
                    <Box display="flex" alignItems="center" mt={1}>
                      <Chip
                        size="small"
                        label={metric.change}
                        color={metric.trend === 'up' ? 'success' : 'error'}
                        sx={{ height: 20, fontSize: '0.75rem' }}
                      />
                    </Box>
                  </Box>
                  <Avatar sx={{ 
                    bgcolor: metric.trend === 'up' ? 'rgba(16, 185, 129, 0.1)' : 'rgba(239, 68, 68, 0.1)',
                    color: metric.trend === 'up' ? '#10b981' : '#ef4444',
                  }}>
                    {metric.icon}
                  </Avatar>
                </Box>
              </MetricCard>
            </Grid>
          ))}
        </Grid>

        {/* 图表区域 */}
        <Grid container spacing={2}>
          {dashboardData.charts.map((chart, index) => (
            <Grid item xs={12} md={6} key={index}>
              <DashboardCard>
                <Box sx={{ p: 2 }}>
                  <Typography variant="h6" fontWeight="600" gutterBottom>
                    {chart.title}
                  </Typography>
                  <Box sx={{ 
                    height: 250, 
                    display: 'flex', 
                    alignItems: 'center', 
                    justifyContent: 'center',
                    backgroundColor: '#f8f9fa',
                    borderRadius: 1,
                  }}>
                    {chart.type === 'line' && <ShowChart sx={{ fontSize: 48, color: '#e5e7eb' }} />}
                    {chart.type === 'bar' && <BarChart sx={{ fontSize: 48, color: '#e5e7eb' }} />}
                    {chart.type === 'pie' && <PieChart sx={{ fontSize: 48, color: '#e5e7eb' }} />}
                    {chart.type === 'geo' && <Public sx={{ fontSize: 48, color: '#e5e7eb' }} />}
                  </Box>
                </Box>
              </DashboardCard>
            </Grid>
          ))}
        </Grid>
      </Box>
    );
  };

  return (
    <MainContainer>
      {/* 对话区域 */}
      <ChatContainer>
        <ChatHeader>
          {/* 顶部信息栏 */}
          <Box display="flex" alignItems="center" justifyContent="space-between">
            <Box display="flex" alignItems="center" gap={2}>
              <Avatar sx={{ bgcolor: '#6366f1' }}>
                <SmartToy />
              </Avatar>
              <Box>
                <Typography variant="h6" fontWeight={600}>
                  GEO监控助手
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  智能数据分析 • 实时监控
                </Typography>
              </Box>
              {/* 模式切换按钮 */}
              <ToggleButtonGroup
                value={mode}
                exclusive
                onChange={(e, newMode) => newMode && setMode(newMode)}
                size="small"
                sx={{
                  '& .MuiToggleButton-root': {
                    textTransform: 'none',
                    px: 2,
                  },
                }}
              >
                <ToggleButton value="chat">
                  <Chat sx={{ mr: 1, fontSize: 18 }} />
                  对话模式
                </ToggleButton>
                <ToggleButton value="agent">
                  <AutoMode sx={{ mr: 1, fontSize: 18 }} />
                  Agent模式
                </ToggleButton>
              </ToggleButtonGroup>
            </Box>
            <Box display="flex" alignItems="center" gap={1}>
              <Button
                size="small"
                startIcon={<Add />}
                onClick={handleNewConversation}
                sx={{
                  textTransform: 'none',
                  borderRadius: 2,
                  backgroundColor: '#6366f1',
                  color: '#ffffff',
                  '&:hover': {
                    backgroundColor: '#5558e3',
                  },
                }}
              >
                新建对话
              </Button>
              <Tooltip title="历史对话">
                <IconButton 
                  size="small"
                  onClick={() => setShowHistory(!showHistory)}
                  sx={{ 
                    color: showHistory ? '#6366f1' : 'inherit',
                  }}
                >
                  <History />
                </IconButton>
              </Tooltip>
              <IconButton size="small">
                <Refresh />
              </IconButton>
              <IconButton size="small">
                <MoreHoriz />
              </IconButton>
            </Box>
          </Box>
          
          {/* 历史对话横向滚动条 */}
          <Collapse in={showHistory}>
            <HistoryBar>
              {conversations.map((conv) => (
                <HistoryChip
                  key={conv.id}
                  label={conv.title}
                  selected={selectedConversation === conv.id}
                  onClick={() => handleSelectConversation(conv.id)}
                  onDelete={conversations.length > 1 ? () => handleDeleteConversation(conv.id) : undefined}
                  size="small"
                />
              ))}
            </HistoryBar>
          </Collapse>
        </ChatHeader>

        <MessagesContainer>
          {messages.map((message) => (
            <MessageBubble key={message.id} isUser={message.type === 'user'}>
              {message.type === 'assistant' && (
                <Avatar sx={{ 
                  mr: 1, 
                  bgcolor: '#6366f1',
                  width: 32,
                  height: 32,
                }}>
                  <SmartToy sx={{ fontSize: 18 }} />
                </Avatar>
              )}
              <BubbleContent isUser={message.type === 'user'}>
                <Typography variant="body2">
                  {message.content}
                </Typography>
              </BubbleContent>
              {message.type === 'user' && (
                <Avatar sx={{ 
                  ml: 1,
                  bgcolor: '#e5e7eb',
                  width: 32,
                  height: 32,
                }}>
                  U
                </Avatar>
              )}
            </MessageBubble>
          ))}

          {isTyping && (
            <Box display="flex" alignItems="center" gap={1}>
              <Avatar sx={{ 
                bgcolor: '#6366f1',
                width: 32,
                height: 32,
              }}>
                <SmartToy sx={{ fontSize: 18 }} />
              </Avatar>
              <Box sx={{ 
                display: 'flex', 
                gap: 0.5,
                padding: '12px 16px',
                backgroundColor: '#ffffff',
                borderRadius: 2,
                border: '1px solid rgba(0, 0, 0, 0.08)',
              }}>
                <Box sx={{ 
                  width: 8, 
                  height: 8, 
                  borderRadius: '50%', 
                  bgcolor: '#6366f1',
                  animation: `${pulse} 1.4s infinite`,
                }} />
                <Box sx={{ 
                  width: 8, 
                  height: 8, 
                  borderRadius: '50%', 
                  bgcolor: '#6366f1',
                  animation: `${pulse} 1.4s infinite`,
                  animationDelay: '0.2s',
                }} />
                <Box sx={{ 
                  width: 8, 
                  height: 8, 
                  borderRadius: '50%', 
                  bgcolor: '#6366f1',
                  animation: `${pulse} 1.4s infinite`,
                  animationDelay: '0.4s',
                }} />
              </Box>
            </Box>
          )}

          <div ref={messagesEndRef} />
        </MessagesContainer>

        {/* 快速提示 */}
        {messages.length === 1 && (
          <Box sx={{ px: 3, pb: 2 }}>
            <Typography variant="caption" color="text.secondary" gutterBottom>
              快速开始
            </Typography>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', mt: 1 }}>
              {mode === 'agent' ? (
                // Agent模式的快速提示
                [
                  { icon: <Assessment />, text: '生成SEO分析报告页面', color: '#3b82f6' },
                  { icon: <CompareArrows />, text: '生成竞争对手对比页面', color: '#8b5cf6' },
                  { icon: <Dashboard />, text: '生成数据仪表板页面', color: '#10b981' },
                  { icon: <Analytics />, text: '生成流量分析页面', color: '#ec4899' },
                  { icon: <Public />, text: '生成地域分布页面', color: '#f59e0b' },
                  { icon: <Leaderboard />, text: '生成排行榜页面', color: '#06b6d4' },
                ].map((prompt, index) => (
                  <SuggestionChip
                    key={index}
                    icon={prompt.icon}
                    label={prompt.text}
                    onClick={() => handleSendMessage(prompt.text)}
                    sx={{
                      '& .MuiChip-icon': {
                        color: prompt.color,
                      },
                    }}
                  />
                ))
              ) : (
                // Chat模式的快速提示
                quickPrompts.map((prompt, index) => (
                  <SuggestionChip
                    key={index}
                    icon={prompt.icon}
                    label={prompt.text}
                    onClick={() => handleSendMessage(prompt.text)}
                    sx={{
                      '& .MuiChip-icon': {
                        color: prompt.color,
                      },
                    }}
                  />
                ))
              )}
            </Box>
          </Box>
        )}

        <InputContainer>
          <StyledTextField
            fullWidth
            multiline
            maxRows={4}
            placeholder={
              mode === 'agent' 
                ? "描述您想要生成的页面，例如：生成SEO分析报告页面" 
                : "输入您的问题，例如：显示今天的流量数据"
            }
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={(e) => {
              if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                handleSendMessage();
              }
            }}
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton onClick={() => handleSendMessage()} disabled={!inputValue.trim()}>
                    <Send color={inputValue.trim() ? 'primary' : 'disabled'} />
                  </IconButton>
                </InputAdornment>
              ),
            }}
          />
        </InputContainer>
      </ChatContainer>

      {/* 右侧仪表板 */}
      <DashboardContainer>
        {/* 全屏按钮 */}
        {(htmlContent || dashboardData) && (
          <Box sx={{ position: 'absolute', top: 16, right: 16, zIndex: 10 }}>
            <Tooltip title={isFullscreen ? "退出全屏" : "全屏查看"}>
              <IconButton 
                onClick={() => setIsFullscreen(!isFullscreen)}
                sx={{ 
                  bgcolor: 'white', 
                  boxShadow: 2,
                  '&:hover': { bgcolor: '#f5f5f5' }
                }}
              >
                {isFullscreen ? <CloseFullscreen /> : <OpenInFull />}
              </IconButton>
            </Tooltip>
          </Box>
        )}

        {isGenerating ? (
          <Box sx={{ textAlign: 'center', py: 10 }}>
            <CircularProgress sx={{ color: '#6366f1' }} />
            <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
              {mode === 'agent' ? '正在生成HTML页面...' : '正在生成数据报告...'}
            </Typography>
          </Box>
        ) : htmlContent ? (
          // 显示HTML内容
          <Box sx={{ height: '100%', width: '100%', position: 'relative' }}>
            <iframe
              ref={htmlContainerRef}
              srcDoc={htmlContent}
              style={{
                width: '100%',
                height: '100%',
                border: 'none',
                borderRadius: '8px',
                backgroundColor: 'white',
              }}
              title="Generated HTML Content"
              sandbox="allow-scripts allow-same-origin"
            />
          </Box>
        ) : dashboardData ? (
          renderDashboard()
        ) : (
          <Box sx={{ 
            height: '100%', 
            display: 'flex', 
            flexDirection: 'column',
            alignItems: 'center', 
            justifyContent: 'center',
            color: 'text.secondary',
          }}>
            <Dashboard sx={{ fontSize: 64, color: '#e5e7eb', mb: 2 }} />
            <Typography variant="h6" gutterBottom>
              {mode === 'agent' ? 'HTML页面生成器' : '数据面板'}
            </Typography>
            <Typography variant="body2" textAlign="center" sx={{ maxWidth: 300 }}>
              {mode === 'agent' 
                ? '在左侧对话中描述您想要的页面，我会为您生成HTML页面'
                : '在左侧对话中输入您的需求，我会为您生成相应的数据分析和可视化报告'}
            </Typography>
          </Box>
        )}
      </DashboardContainer>

      {/* 全屏对话框 */}
      <Dialog
        fullScreen
        open={isFullscreen}
        onClose={() => setIsFullscreen(false)}
        TransitionProps={{
          timeout: 300,
        }}
      >
        <DialogTitle sx={{ 
          bgcolor: '#6366f1', 
          color: 'white',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        }}>
          <Typography variant="h6">
            {mode === 'agent' ? '生成的HTML页面' : '数据分析报告'}
          </Typography>
          <IconButton 
            edge="end" 
            color="inherit" 
            onClick={() => setIsFullscreen(false)}
          >
            <Close />
          </IconButton>
        </DialogTitle>
        <DialogContent sx={{ p: 0, bgcolor: '#f5f5f5' }}>
          {htmlContent ? (
            <iframe
              srcDoc={htmlContent}
              style={{
                width: '100%',
                height: '100%',
                border: 'none',
              }}
              title="Fullscreen HTML Content"
              sandbox="allow-scripts allow-same-origin"
            />
          ) : dashboardData ? (
            <Box sx={{ p: 3, bgcolor: '#fafafa', height: '100%', overflowY: 'auto' }}>
              {renderDashboard()}
            </Box>
          ) : null}
        </DialogContent>
      </Dialog>
    </MainContainer>
  );
}

export default GeoMonitoringConversation;