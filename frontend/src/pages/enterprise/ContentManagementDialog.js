import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Box,
  Typography,
  Chip,
  Grid,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  IconButton,
  CircularProgress,
  Divider
} from '@mui/material';
import {
  Send,
  Add,
  CloudUpload,
  Delete,
  Category,
  TrendingUp,
  People,
  AttachFile,
  Star,
  Sort,
  FilterList
} from '@mui/icons-material';
import { contentService } from '../../services/contentService';
import uploadService from '../../services/uploadService';



function ContentManagementDialog({ 
  open, 
  onClose, 
  type, // 'submission' or 'creation'
  onSubmit 
}) {
  const [step, setStep] = useState(1); // 1: 填写内容, 2: 选择渠道
  const [formData, setFormData] = useState({
    title: '',
    description: '', // 需求描述字段
    content: '',
    requirements: '',
    attachments: [],
    selectedChannels: [],
    selectedService: '', // 添加选中的服务字段
    tags: '', // 添加标签字段
    deadline: '' // 添加截止时间字段
    // price 字段已移除 - 将从选择的渠道服务中获取
  });
  
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);
  
  // 渠道选择相关状态
  const [primaryCategory, setPrimaryCategory] = useState('');
  const [secondaryCategory, setSecondaryCategory] = useState('');
  const [accountTag, setAccountTag] = useState('');
  const [sortBy, setSortBy] = useState('recommended');
  const [selectedTags, setSelectedTags] = useState([]);

  // 分页相关状态
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(6); // 每页显示6个服务

  // 预设标签（特别是自媒体相关）
  const presetTags = [
    '微信公众号', '抖音', '小红书', '微博', 'B站', '快手',
    '知乎', '今日头条', '百家号', '企鹅号', '大鱼号',
    '图文', '短视频', '直播', '种草', '测评', '教程',
    '美食', '旅游', '时尚', '科技', '教育', '健康',
    '母婴', '汽车', '房产', '金融', '游戏', '娱乐'
  ];

  // 真实分类数据状态
  const [primaryCategories, setPrimaryCategories] = useState([]);
  const [secondaryCategories, setSecondaryCategories] = useState([]);
  const [channelServices, setChannelServices] = useState([]);
  const [channelList, setChannelList] = useState([]);
  const [loadingCategories, setLoadingCategories] = useState(false);

  // 加载一级分类
  const loadPrimaryCategories = async () => {
    try {
      setLoadingCategories(true);
      const response = await contentService.getTopLevelCategories();

      if (response.success && response.data && response.data.categories) {
        setPrimaryCategories(response.data.categories);
      } else {
        setPrimaryCategories([]);
      }
    } catch (error) {
      setPrimaryCategories([]);
    } finally {
      setLoadingCategories(false);
    }
  };

  // 加载二级分类
  const loadSecondaryCategories = async (parentId) => {
    try {
      setLoadingCategories(true);
      const response = await contentService.getSecondaryCategories(parentId);

      if (response.success && response.data && response.data.categories) {
        setSecondaryCategories(response.data.categories);
      } else {
        setSecondaryCategories([]);
      }
    } catch (error) {
      setSecondaryCategories([]);
    } finally {
      setLoadingCategories(false);
    }
  };

  // 加载渠道服务列表
  const loadChannelServices = async (categoryId) => {
    try {
      setLoadingCategories(true);
      const response = await contentService.getChannelServices({
        category_id: categoryId,
        is_active: true
      });

      if (response.success && response.data) {
        // 检查数据结构，可能是 response.data.items 或直接是 response.data
        const services = response.data.items || response.data || [];
        setChannelServices(Array.isArray(services) ? services : []);
      } else {
        setChannelServices([]);
      }
    } catch (error) {
      setChannelServices([]);
    } finally {
      setLoadingCategories(false);
    }
  };

  // 组件挂载时加载一级分类
  useEffect(() => {
    if (open && step === 2) {
      loadPrimaryCategories();
    }
  }, [open, step]);

  // 默认选择自媒体分类
  useEffect(() => {
    if (primaryCategories.length > 0 && !primaryCategory) {
      // 查找自媒体相关的分类（可能的名称：自媒体、新媒体、社交媒体等）
      const mediaCategory = primaryCategories.find(cat =>
        cat.category_name && (
          cat.category_name.includes('自媒体') ||
          cat.category_name.includes('新媒体') ||
          cat.category_name.includes('社交媒体') ||
          cat.category_name.includes('媒体')
        )
      );

      if (mediaCategory) {
        setPrimaryCategory(mediaCategory.id);
        loadSecondaryCategories(mediaCategory.id);
      } else if (primaryCategories.length > 0) {
        // 如果没找到自媒体分类，默认选择第一个
        setPrimaryCategory(primaryCategories[0].id);
        loadSecondaryCategories(primaryCategories[0].id);
      }
    }
  }, [primaryCategories, primaryCategory]);

  // 当选择一级分类时，加载二级分类
  useEffect(() => {
    if (primaryCategory) {
      loadSecondaryCategories(primaryCategory);
      setSecondaryCategory(''); // 重置二级分类选择
      setChannelServices([]); // 重置服务列表
      setAccountTag(''); // 重置标签选择
    } else {
      setSecondaryCategories([]);
      setChannelServices([]);
    }
  }, [primaryCategory]);

  // 当选择二级分类时，重置相关状态
  useEffect(() => {
    if (secondaryCategory) {
      setFormData(prev => ({ ...prev, selectedService: '' })); // 重置服务选择
      setAccountTag(''); // 重置标签选择
      setChannelServices([]); // 重置服务列表，等待标签输入
    } else {
      setChannelServices([]);
    }
  }, [secondaryCategory]);

  // 当标签变化时，重新加载服务列表
  useEffect(() => {
    if (secondaryCategory && accountTag.trim()) {
      // 基于二级分类和标签筛选服务
      loadChannelServicesWithTags(secondaryCategory, accountTag.trim());
    } else if (secondaryCategory && !accountTag.trim()) {
      // 如果没有标签，加载该分类下的所有服务
      loadChannelServices(secondaryCategory);
    } else {
      setChannelServices([]);
    }
  }, [secondaryCategory, accountTag]);

  // 基于标签加载服务列表
  const loadChannelServicesWithTags = async (categoryId, tags) => {
    try {
      setLoadingCategories(true);

      // 调用API获取包含指定标签的服务
      const response = await contentService.getServicesByTags(categoryId, tags);

      if (response.success && response.data) {
        // 检查数据结构，可能是 response.data.items 或直接是 response.data
        const services = response.data.items || response.data || [];
        setChannelServices(Array.isArray(services) ? services : []);
      } else {
        setChannelServices([]);
      }
    } catch (error) {
      setChannelServices([]);
    } finally {
      setLoadingCategories(false);
    }
  };

  // 当选择的服务变化时，重新获取渠道列表
  useEffect(() => {
    const loadChannels = async () => {
      if (formData.selectedService) {
        setLoadingCategories(true);
        try {
          const channels = await getChannelList();
          setChannelList(channels);
        } catch (error) {
          setChannelList([]);
        } finally {
          setLoadingCategories(false);
        }
      } else {
        setChannelList([]);
      }
    };

    loadChannels();
  }, [formData.selectedService, accountTag]);

  const handleFileUpload = (event) => {
    const files = Array.from(event.target.files);

    // 验证文件
    const validFiles = [];
    const errors = [];

    files.forEach(file => {
      const validation = uploadService.validateFile(file, [], 10 * 1024 * 1024); // 10MB限制
      if (validation.valid) {
        validFiles.push({
          id: Date.now() + Math.random(),
          name: file.name,
          size: file.size,
          type: file.type,
          file: file // 存储实际的File对象
        });
      } else {
        errors.push(`${file.name}: ${validation.error}`);
      }
    });

    if (errors.length > 0) {
      alert('文件验证失败:\n' + errors.join('\n'));
    }

    if (validFiles.length > 0) {
      setFormData(prev => ({
        ...prev,
        attachments: [...prev.attachments, ...validFiles]
      }));
    }
  };
  
  const removeFile = (fileId) => {
    setFormData(prev => ({
      ...prev,
      attachments: prev.attachments.filter(f => f.id !== fileId)
    }));
  };
  
  const validateStep1 = () => {
    const newErrors = {};

    if (!formData.title.trim()) {
      newErrors.title = type === 'creation' ? '请输入需求标题' : '请输入稿件标题';
    } else if (formData.title.trim().length < 5) {
      newErrors.title = '标题至少需要5个字符';
    } else if (formData.title.length > 200) {
      newErrors.title = '标题不能超过200个字符';
    }

    // 验证需求描述
    if (!formData.description.trim()) {
      newErrors.description = '请输入需求描述';
    } else if (formData.description.trim().length < 10) {
      newErrors.description = '需求描述至少需要10个字符';
    } else if (formData.description.length > 500) {
      newErrors.description = '需求描述不能超过500个字符';
    }

    if (type === 'submission' && !formData.content.trim()) {
      newErrors.content = '请输入稿件内容';
    } else if (type === 'submission' && formData.content.trim().length < 10) {
      newErrors.content = '稿件内容至少需要10个字符';
    }

    if (type === 'creation' && !formData.requirements.trim()) {
      newErrors.requirements = '请输入创作要求';
    } else if (type === 'creation' && formData.requirements.trim().length < 10) {
      newErrors.requirements = '创作要求至少需要10个字符';
    }

    // 验证标签（可选）
    if (formData.tags.trim()) {
      const tags = formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag);
      if (tags.length > 10) {
        newErrors.tags = '标签数量不能超过10个';
      }
    }

    // 验证截止时间
    if (!formData.deadline) {
      newErrors.deadline = '请选择截止时间';
    } else {
      const deadlineDate = new Date(formData.deadline + 'T23:59:59'); // 设置为当天的最后时刻
      const now = new Date();
      if (deadlineDate <= now) {
        newErrors.deadline = '截止时间必须是今天之后的日期';
      }
    }

    // 价格验证已移除 - 价格将从选择的渠道服务中获取

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const validateStep2 = () => {
    const newErrors = {};
    
    if (formData.selectedChannels.length === 0) {
      newErrors.channels = '请至少选择一个发布渠道';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  const handleNextStep = () => {
    if (step === 1 && validateStep1()) {
      setStep(2);
    }
  };

  const handlePrevStep = () => {
    if (step === 2) {
      setStep(1);
    }
  };

  const handleSubmit = async () => {
    if (!validateStep2()) return;

    setLoading(true);
    try {
      // 如果有附件，先上传文件
      let uploadedAttachments = [];
      if (formData.attachments && formData.attachments.length > 0) {
        const filesToUpload = formData.attachments.filter(attachment => attachment.file);

        if (filesToUpload.length > 0) {
          const uploadResults = await uploadService.uploadFiles(
            filesToUpload.map(attachment => attachment.file),
            'content', // 业务类型
            null, // 业务ID
            false, // 不公开
            null, // 不设置过期时间
            (current, total, filename, status, error) => {
              console.log(`上传进度: ${current}/${total} - ${filename} - ${status}`);
              if (error) {
                console.error(`文件上传失败: ${filename} - ${error}`);
              }
            }
          );

          // 处理上传结果，只保留成功上传的文件
          uploadedAttachments = uploadResults
            .filter(result => result.success)
            .map((result, index) => {
              console.log('上传结果详情:', result.result);
              // 根据后端实际返回的数据结构调整
              return {
                id: result.result.file_id || result.result.id,
                name: result.file.name, // 使用原始文件名
                size: result.file.size, // 使用原始文件大小
                url: result.result.access_url || result.result.storage_url || result.result.url,
                access_token: result.result.access_token || null
              };
            });

          // 调试信息
          console.log('文件上传结果:', uploadResults);
          console.log('处理后的附件:', uploadedAttachments);

          // 检查是否有上传失败的文件
          const failedUploads = uploadResults.filter(result => !result.success);
          if (failedUploads.length > 0) {
            const failedFileNames = failedUploads.map(result => result.file.name).join(', ');
            throw new Error(`以下文件上传失败: ${failedFileNames}`);
          }
        }
      }

      // 准备提交数据，将上传的文件信息添加到表单数据中
      const submitData = {
        ...formData,
        attachments: uploadedAttachments
      };

      // 调试信息
      console.log('Dialog提交数据:', submitData);
      console.log('上传的附件:', uploadedAttachments);

      await onSubmit(submitData);
      handleClose();
    } catch (error) {
      console.error('提交失败:', error);
      alert('提交失败: ' + (error.message || '未知错误'));
    } finally {
      setLoading(false);
    }
  };
  
  const handleClose = () => {
    setStep(1);
    setFormData({
      title: '',
      description: '',
      content: '',
      requirements: '',
      attachments: [],
      selectedChannels: [],
      selectedService: '',
      tags: '',
      deadline: ''
      // price 字段已移除
    });
    setErrors({});
    setPrimaryCategory('');
    setSecondaryCategory('');
    setSecondaryCategories([]);
    setChannelServices([]);
    setAccountTag('');
    onClose();
  };
  
  const getChannelList = async () => {
    if (!formData.selectedService) return [];

    try {
      // 使用新的专门接口获取可用渠道商
      const filters = {};

      // 添加标签筛选
      if (accountTag && accountTag.trim()) {
        filters.tags = [accountTag.trim()];
      }

      const response = await contentService.getAvailableProviders(formData.selectedService, filters);

      if (response.success) {
        return response.data.items || [];
      } else {
        return [];
      }
    } catch (error) {
      return [];
    }
  };
  
  const selectChannel = (channel) => {
    // 统一数据格式，确保兼容新的渠道商数据结构
    const normalizedChannel = {
      id: channel.provider_id || channel.id,
      provider_name: channel.provider_name,
      provider_type: channel.provider_type,
      service_name: channel.service_provided || channel.provider_name, // 显示名称用渠道商名称
      service_icon: channel.service_icon,
      platform_type: channel.platform_type,
      base_price: channel.base_price,
      rating_score: channel.rating_score,
      delivery_time: channel.delivery_time,
      completed_orders: channel.completed_orders,
      is_featured: channel.is_featured,
      verification_status: channel.verification_status,
      business_description: channel.business_description,
      contact_phone: channel.contact_phone,
      contact_email: channel.contact_email
    };

    const isSelected = formData.selectedChannels.find(c => c.id === normalizedChannel.id);
    if (isSelected) {
      setFormData(prev => ({
        ...prev,
        selectedChannels: prev.selectedChannels.filter(c => c.id !== normalizedChannel.id)
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        selectedChannels: [...prev.selectedChannels, normalizedChannel]
      }));
    }
  };
  
  return (
    <Dialog 
      open={open} 
      onClose={handleClose}
      maxWidth={step === 2 ? "xl" : "md"}
      fullWidth
      PaperProps={{ sx: { borderRadius: 0.5 } }}
    >
      <DialogTitle sx={{ borderBottom: '1px solid #e5e7eb', pb: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box>
            <Typography variant="h6" sx={{ fontWeight: 600 }}>
              {step === 1 ? (type === 'creation' ? '创建需求' : '发布稿件') : '选择渠道服务'}
            </Typography>
            <Typography variant="body2" sx={{ color: '#6b7280', mt: 0.5 }}>
              {step === 1
                ? (type === 'creation' ? '填写创作需求的基本信息' : '填写稿件的基本信息')
                : '先选择渠道分类，再选择具体服务，最后选择渠道商'}
            </Typography>
          </Box>
          {/* 步骤指示器 */}
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Chip 
              label="1" 
              size="small" 
              color={step === 1 ? "primary" : "default"}
              sx={{ minWidth: 28 }}
            />
            <Box sx={{ width: 20, height: 2, bgcolor: step === 2 ? '#3b82f6' : '#e5e7eb' }} />
            <Chip 
              label="2" 
              size="small" 
              color={step === 2 ? "primary" : "default"}
              sx={{ minWidth: 28 }}
            />
          </Box>
        </Box>
      </DialogTitle>
      
      <DialogContent sx={{ pt: 3 }}>
        {step === 1 && (
          /* 第一步：填写基本信息 */
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3, maxWidth: 600, mx: 'auto' }}>
            {/* 标题 */}
            <TextField
              fullWidth
              label={type === 'creation' ? '需求标题 *' : '稿件标题 *'}
              value={formData.title}
              onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
              error={!!errors.title}
              helperText={errors.title}
              placeholder={type === 'creation' ? '请输入需求标题，例如：品牌推广文案创作' : '请输入稿件标题，例如：品牌推广文案'}
              sx={{
                mt: 2, // 增加上边距
                '& .MuiOutlinedInput-root': {
                  borderRadius: 1,
                  backgroundColor: '#fff',
                  display: 'flex',
                  alignItems: 'center',
                  '&:hover fieldset': {
                    borderColor: '#d1d5db',
                    borderWidth: '1px'
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: '#3b82f6',
                    borderWidth: '2px'
                  },
                  '& fieldset': {
                    borderColor: '#e5e7eb'
                  }
                },
                '& .MuiInputBase-input': {
                  padding: '14px',
                  fontSize: '14px',
                  color: '#374151',
                  lineHeight: '1.4',
                  display: 'flex',
                  alignItems: 'center',
                  '&::placeholder': {
                    color: '#9ca3af',
                    opacity: 1
                  }
                }
              }}
            />

            {/* 需求描述 */}
            <TextField
              fullWidth
              label="需求描述 *"
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              error={!!errors.description}
              helperText={errors.description}
              multiline
              rows={3}
              placeholder={type === 'creation' ? '请简要描述您的创作需求概要...' : '请简要描述稿件发布需求概要...'}
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: 1,
                  backgroundColor: '#fff',
                  '&:hover fieldset': {
                    borderColor: '#d1d5db',
                    borderWidth: '1px'
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: '#3b82f6',
                    borderWidth: '2px'
                  },
                  '& fieldset': {
                    borderColor: '#e5e7eb'
                  }
                },
                '& .MuiInputBase-input': {
                  padding: '14px',
                  fontSize: '14px',
                  color: '#374151',
                  lineHeight: '1.5',
                  '&::placeholder': {
                    color: '#9ca3af',
                    opacity: 1
                  }
                }
              }}
            />

            {/* 内容/要求 */}
            {type === 'submission' ? (
              <TextField
                fullWidth
                label="稿件内容 *"
                value={formData.content}
                onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
                error={!!errors.content}
                helperText={errors.content}
                multiline
                rows={10}
                placeholder="请输入稿件的完整内容，包括标题、正文、图片说明等..."
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 1,
                    backgroundColor: '#fff',
                    '&:hover fieldset': {
                      borderColor: '#d1d5db',
                      borderWidth: '1px'
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: '#3b82f6',
                      borderWidth: '2px'
                    },
                    '& fieldset': {
                      borderColor: '#e5e7eb'
                    }
                  },
                  '& .MuiInputBase-input': {
                    padding: '14px',
                    fontSize: '14px',
                    color: '#374151',
                    lineHeight: '1.5',
                    '&::placeholder': {
                      color: '#9ca3af',
                      opacity: 1
                    }
                  }
                }}
              />
            ) : (
              <TextField
                fullWidth
                label="创作要求 *"
                value={formData.requirements}
                onChange={(e) => setFormData(prev => ({ ...prev, requirements: e.target.value }))}
                error={!!errors.requirements}
                helperText={errors.requirements}
                multiline
                rows={10}
                placeholder="请详细描述创作要求，包括内容主题、写作风格、字数要求、关键信息等..."
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 1,
                    backgroundColor: '#fff',
                    '&:hover fieldset': {
                      borderColor: '#d1d5db',
                      borderWidth: '1px'
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: '#3b82f6',
                      borderWidth: '2px'
                    },
                    '& fieldset': {
                      borderColor: '#e5e7eb'
                    }
                  },
                  '& .MuiInputBase-input': {
                    padding: '14px',
                    fontSize: '14px',
                    color: '#374151',
                    lineHeight: '1.5',
                    '&::placeholder': {
                      color: '#9ca3af',
                      opacity: 1
                    }
                  }
                }}
              />
            )}

            {/* 标签输入 */}
            <TextField
              fullWidth
              label="标签（可选）"
              value={formData.tags}
              onChange={(e) => setFormData(prev => ({ ...prev, tags: e.target.value }))}
              error={!!errors.tags}
              helperText={errors.tags}
              placeholder="请输入标签，用逗号分隔，例如：营销推广，品牌"
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: 1,
                  backgroundColor: '#fff',
                  display: 'flex',
                  alignItems: 'center',
                  '&:hover fieldset': {
                    borderColor: '#d1d5db',
                    borderWidth: '1px'
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: '#3b82f6',
                    borderWidth: '2px'
                  },
                  '& fieldset': {
                    borderColor: '#e5e7eb'
                  }
                },
                '& .MuiInputBase-input': {
                  padding: '14px',
                  fontSize: '14px',
                  color: '#374151',
                  lineHeight: '1.4',
                  display: 'flex',
                  alignItems: 'center',
                  '&::placeholder': {
                    color: '#9ca3af',
                    opacity: 1
                  }
                }
              }}
            />

            {/* 截止时间 */}
            <Box sx={{ position: 'relative' }}>
              <TextField
                fullWidth
                label="截止时间 *"
                type="date"
                value={formData.deadline}
                onChange={(e) => setFormData(prev => ({ ...prev, deadline: e.target.value }))}
                error={!!errors.deadline}
                helperText={errors.deadline}
                slotProps={{
                  inputLabel: {
                    shrink: true,
                  },
                  htmlInput: {
                    min: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString().slice(0, 10), // 最早明天，只取日期部分
                    style: {
                      colorScheme: 'light',
                      fontSize: '14px',
                      padding: '12px 40px 12px 14px', // 右侧留出空间给图标
                      fontFamily: 'inherit'
                    }
                  }
                }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 1,
                    backgroundColor: '#fff',
                    display: 'flex',
                    alignItems: 'center',
                    '&:hover fieldset': {
                      borderColor: '#d1d5db',
                      borderWidth: '1px'
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: '#3b82f6',
                      borderWidth: '2px'
                    },
                    '& fieldset': {
                      borderColor: '#e5e7eb'
                    }
                  },
                  '& .MuiInputBase-input': {
                    padding: '14px',
                    fontSize: '14px',
                    color: formData.deadline ? '#374151' : 'transparent',
                    lineHeight: '1.4',
                    display: 'flex',
                    alignItems: 'center',
                    '&::-webkit-datetime-edit': {
                      color: formData.deadline ? '#374151' : 'transparent'
                    },
                    '&::-webkit-datetime-edit-text': {
                      color: formData.deadline ? '#374151' : 'transparent'
                    },
                    '&::-webkit-datetime-edit-month-field': {
                      color: formData.deadline ? '#374151' : 'transparent'
                    },
                    '&::-webkit-datetime-edit-day-field': {
                      color: formData.deadline ? '#374151' : 'transparent'
                    },
                    '&::-webkit-datetime-edit-year-field': {
                      color: formData.deadline ? '#374151' : 'transparent'
                    }
                  },
                  '& input[type="date"]::-webkit-calendar-picker-indicator': {
                    cursor: 'pointer',
                    opacity: 0.6,
                    padding: '4px',
                    borderRadius: '4px',
                    position: 'absolute',
                    right: '12px',
                    top: '50%',
                    transform: 'translateY(-50%)',
                    width: '20px',
                    height: '20px',
                    '&:hover': {
                      opacity: 1,
                      backgroundColor: '#f3f4f6'
                    }
                  },
                  '& .MuiInputBase-input[type="date"]': {
                    paddingRight: '40px' // 为图标留出空间
                  }
                }}
              />
              {/* 占位符文本 */}
              {!formData.deadline && (
                <Typography
                  variant="body2"
                  sx={{
                    position: 'absolute',
                    top: '50%',
                    left: '14px',
                    transform: 'translateY(-50%)',
                    color: '#9ca3af',
                    pointerEvents: 'none',
                    fontSize: '14px'
                  }}
                >
                  请选择需求的截止时间
                </Typography>
              )}
            </Box>

            {/* 价格字段已移除 - 将从选择的渠道服务中自动获取价格 */}

            {/* 文件上传 */}
            <Box>
              <Typography variant="subtitle1" sx={{ mb: 1.5, fontWeight: 600, fontSize: '14px' }}>
                附件上传（可选）
              </Typography>
              <Paper
                sx={{
                  p: 2,
                  border: '1px dashed #d1d5db',
                  borderRadius: 1,
                  textAlign: 'center',
                  backgroundColor: '#fafafa',
                  '&:hover': {
                    borderColor: '#3b82f6',
                    bgcolor: '#f8fafc'
                  }
                }}
              >
                <Button
                  variant="outlined"
                  startIcon={<CloudUpload />}
                  component="label"
                  size="small"
                  sx={{ fontSize: '13px' }}
                >
                  选择文件上传
                  <input
                    type="file"
                    hidden
                    multiple
                    onChange={handleFileUpload}
                  />
                </Button>
                <Typography variant="body2" sx={{ color: '#6b7280', mt: 0.5, fontSize: '12px' }}>
                  支持图片、文档等格式，单个文件最大10MB
                </Typography>
              </Paper>

              {formData.attachments.length > 0 && (
                <Box sx={{ mt: 1.5 }}>
                  <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 600, fontSize: '13px' }}>
                    已上传的文件：
                  </Typography>
                  <List dense>
                    {formData.attachments.map((file) => (
                      <ListItem
                        key={file.id}
                        sx={{ bgcolor: '#f8fafc', borderRadius: 0.5, mb: 0.5, py: 0.5 }}
                        secondaryAction={
                          <IconButton onClick={() => removeFile(file.id)} size="small">
                            <Delete fontSize="small" />
                          </IconButton>
                        }
                      >
                        <ListItemIcon sx={{ minWidth: 32 }}>
                          <AttachFile fontSize="small" />
                        </ListItemIcon>
                        <ListItemText
                          primary={file.name}
                          secondary={`${(file.size / 1024 / 1024).toFixed(2)} MB`}
                          primaryTypographyProps={{ fontSize: '13px' }}
                          secondaryTypographyProps={{ fontSize: '11px' }}
                        />
                      </ListItem>
                    ))}
                  </List>
                </Box>
              )}
            </Box>
          </Box>
        )}
        
        {step === 2 && (
          /* 第二步：选择渠道 */
          <Box>
            {/* 渠道分类选择 - 新的上下布局 */}
            <Box sx={{ mb: 4 }}>
              {/* 紧凑布局 */}
              <Box>
                {/* 主分类 - 同行显示 */}
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2, gap: 2 }}>
                  <Typography variant="subtitle1" sx={{ fontWeight: 600, color: '#374151', minWidth: '60px' }}>
                    主分类
                  </Typography>
                  {loadingCategories && primaryCategories.length === 0 ? (
                    <CircularProgress size={20} />
                  ) : (
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                      {primaryCategories.map((mainCat) => (
                        <Chip
                          key={mainCat.id}
                          label={mainCat.category_name}
                          clickable
                          color={primaryCategory === mainCat.id ? 'primary' : 'default'}
                          onClick={() => {
                            setPrimaryCategory(mainCat.id);
                            setSecondaryCategory('');
                            setFormData(prev => ({ ...prev, selectedService: '' }));
                            setCurrentPage(1); // 重置分页
                            loadSecondaryCategories(mainCat.id);
                          }}
                          sx={{
                            fontSize: '14px',
                            height: '32px',
                            '&:hover': {
                              bgcolor: primaryCategory === mainCat.id ? 'primary.dark' : '#f3f4f6'
                            }
                          }}
                        />
                      ))}
                    </Box>
                  )}
                </Box>

                {/* 频道选择 - 同行显示 */}
                {primaryCategory && secondaryCategories.length > 0 && (
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2, gap: 2 }}>
                    <Typography variant="subtitle1" sx={{ fontWeight: 600, color: '#374151', minWidth: '60px' }}>
                      频道选择
                    </Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                      {secondaryCategories.map((subCat) => (
                        <Chip
                          key={subCat.id}
                          label={subCat.category_name}
                          clickable
                          variant={secondaryCategory === subCat.id ? 'filled' : 'outlined'}
                          color={secondaryCategory === subCat.id ? 'primary' : 'default'}
                          onClick={() => {
                            setSecondaryCategory(subCat.id);
                            setFormData(prev => ({ ...prev, selectedService: '' }));
                            setCurrentPage(1); // 重置分页
                            loadChannelServices(subCat.id);
                          }}
                          sx={{
                            fontSize: '13px',
                            height: '28px',
                            borderRadius: '14px',
                            '&:hover': {
                              bgcolor: secondaryCategory === subCat.id ? 'primary.dark' : '#e3f2fd'
                            }
                          }}
                        />
                      ))}
                    </Box>
                  </Box>
                )}

                {/* 快速筛选 - 同行显示 */}
                {secondaryCategory && (
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 3, gap: 2 }}>
                    <Typography variant="subtitle1" sx={{ fontWeight: 600, color: '#374151', minWidth: '60px' }}>
                      快速筛选
                    </Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, flex: 1 }}>
                      {presetTags.slice(0, 12).map((tag) => (
                        <Chip
                          key={tag}
                          label={tag}
                          size="small"
                          clickable
                          color={selectedTags.includes(tag) ? 'primary' : 'default'}
                          onClick={() => {
                            setSelectedTags(prev =>
                              prev.includes(tag)
                                ? prev.filter(t => t !== tag)
                                : [...prev, tag]
                            );
                            // 同时更新搜索框
                            if (!selectedTags.includes(tag)) {
                              setAccountTag(tag);
                            }
                          }}
                          sx={{
                            fontSize: '11px',
                            height: '24px',
                            '&:hover': {
                              bgcolor: selectedTags.includes(tag) ? 'primary.dark' : '#f3f4f6'
                            }
                          }}
                        />
                      ))}
                    </Box>
                  </Box>
                )}

                {/* 搜索和排序 - 同行显示 */}
                {secondaryCategory && (
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
                    <TextField
                      size="small"
                      placeholder="搜索服务或标签..."
                      value={accountTag}
                      onChange={(e) => {
                        setAccountTag(e.target.value);
                        setCurrentPage(1); // 搜索时重置分页
                      }}
                      sx={{ width: 300 }}
                    />
                    <FormControl size="small" sx={{ minWidth: 120 }}>
                      <InputLabel>排序方式</InputLabel>
                      <Select
                        value="recommended"
                        label="排序方式"
                      >
                        <MenuItem value="recommended">推荐排序</MenuItem>
                        <MenuItem value="price_asc">价格升序</MenuItem>
                        <MenuItem value="price_desc">价格降序</MenuItem>
                        <MenuItem value="rating">评分排序</MenuItem>
                      </Select>
                    </FormControl>
                  </Box>
                )}

                {/* 可用服务 - 小卡片形式 */}
                <Box>
                  <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600, color: '#374151' }}>
                    可用服务
                  </Typography>

                  {!secondaryCategory ? (
                    <Box sx={{ textAlign: 'center', py: 4, color: '#9ca3af', bgcolor: '#f9fafb', borderRadius: 1 }}>
                      <Typography variant="body2">
                        请先选择频道分类
                      </Typography>
                    </Box>
                  ) : channelServices.length === 0 ? (
                    <Box sx={{ textAlign: 'center', py: 4, color: '#9ca3af', bgcolor: '#f9fafb', borderRadius: 1 }}>
                      <Typography variant="body2">
                        该分类下暂无可用服务
                      </Typography>
                    </Box>
                  ) : (
                    <>
                      {/* 服务卡片网格 */}
                      <Grid container spacing={2} sx={{ mb: 3 }}>
                        {channelServices
                          .filter(service => {
                            if (!accountTag.trim()) return true;
                            const searchTerm = accountTag.toLowerCase();
                            return (
                              service.service_name?.toLowerCase().includes(searchTerm) ||
                              service.service_description?.toLowerCase().includes(searchTerm) ||
                              (service.tags && service.tags.some(tag => tag.toLowerCase().includes(searchTerm)))
                            );
                          })
                          .slice((currentPage - 1) * pageSize, currentPage * pageSize)
                          .map((service) => (
                            <Grid item key={service.id}>
                              <Paper
                                sx={{
                                  p: 2,
                                  cursor: 'pointer',
                                  border: formData.selectedService === service.id ? '2px solid #3b82f6' : '1px solid #e5e7eb',
                                  borderRadius: 1,
                                  transition: 'all 0.2s',
                                  minHeight: '100px',
                                  width: 'auto',
                                  minWidth: '200px',
                                  maxWidth: '300px',
                                  display: 'flex',
                                  flexDirection: 'column',
                                  '&:hover': {
                                    boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                                    transform: 'translateY(-1px)'
                                  },
                                  ...(formData.selectedService === service.id && {
                                    bgcolor: '#f0f9ff',
                                    boxShadow: '0 2px 12px rgba(59, 130, 246, 0.2)'
                                  })
                                }}
                                onClick={() => {
                                  setFormData(prev => ({ ...prev, selectedService: service.id }));
                                }}
                              >
                                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
                                  <Box sx={{ display: 'flex', alignItems: 'center', flex: 1, minWidth: 0 }}>
                                    {service.service_icon && (
                                      <Box sx={{ mr: 1, fontSize: '16px', flexShrink: 0 }}>
                                        {service.service_icon}
                                      </Box>
                                    )}
                                    <Typography
                                      variant="subtitle2"
                                      sx={{
                                        fontWeight: 600,
                                        fontSize: '14px',
                                        overflow: 'hidden',
                                        textOverflow: 'ellipsis',
                                        whiteSpace: 'nowrap',
                                        flex: 1
                                      }}
                                      title={service.service_name || '未命名服务'}
                                    >
                                      {(service.service_name || '未命名服务').length > 12
                                        ? `${(service.service_name || '未命名服务').substring(0, 12)}...`
                                        : (service.service_name || '未命名服务')
                                      }
                                    </Typography>
                                  </Box>
                                  {formData.selectedService === service.id && (
                                    <Chip
                                      label="已选择"
                                      size="small"
                                      color="primary"
                                      sx={{ height: '20px', fontSize: '10px', flexShrink: 0, ml: 1 }}
                                    />
                                  )}
                                </Box>

                                {service.service_description && (
                                  <Typography variant="caption" sx={{
                                    color: '#6b7280',
                                    fontSize: '12px',
                                    lineHeight: 1.3,
                                    mb: 1,
                                    display: '-webkit-box',
                                    WebkitLineClamp: 2,
                                    WebkitBoxOrient: 'vertical',
                                    overflow: 'hidden'
                                  }}>
                                    {service.service_description.length > 50
                                      ? `${service.service_description.substring(0, 50)}...`
                                      : service.service_description
                                    }
                                  </Typography>
                                )}

                                {/* 价格显示 */}
                                <Box sx={{ mt: 'auto', pt: 1 }}>
                                  <Typography variant="body2" sx={{
                                    color: '#ef4444',
                                    fontWeight: 600,
                                    fontSize: '14px'
                                  }}>
                                    ¥{service.price || '面议'}
                                  </Typography>
                                </Box>
                              </Paper>
                            </Grid>
                          ))}
                      </Grid>

                      {/* 分页控件 */}
                      {channelServices.filter(service => {
                        if (!accountTag.trim()) return true;
                        const searchTerm = accountTag.toLowerCase();
                        return (
                          service.service_name?.toLowerCase().includes(searchTerm) ||
                          service.service_description?.toLowerCase().includes(searchTerm) ||
                          (service.tags && service.tags.some(tag => tag.toLowerCase().includes(searchTerm)))
                        );
                      }).length > pageSize && (
                        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', gap: 1 }}>
                          <Button
                            size="small"
                            disabled={currentPage === 1}
                            onClick={() => setCurrentPage(prev => prev - 1)}
                          >
                            上一页
                          </Button>

                          {/* 页码 */}
                          {Array.from({
                            length: Math.ceil(channelServices.filter(service => {
                              if (!accountTag.trim()) return true;
                              const searchTerm = accountTag.toLowerCase();
                              return (
                                service.service_name?.toLowerCase().includes(searchTerm) ||
                                service.service_description?.toLowerCase().includes(searchTerm) ||
                                (service.tags && service.tags.some(tag => tag.toLowerCase().includes(searchTerm)))
                              );
                            }).length / pageSize)
                          }, (_, i) => i + 1).map((page) => (
                            <Button
                              key={page}
                              size="small"
                              variant={currentPage === page ? 'contained' : 'outlined'}
                              onClick={() => setCurrentPage(page)}
                              sx={{ minWidth: '32px', height: '32px' }}
                            >
                              {page}
                            </Button>
                          ))}

                          <Button
                            size="small"
                            disabled={currentPage === Math.ceil(channelServices.filter(service => {
                              if (!accountTag.trim()) return true;
                              const searchTerm = accountTag.toLowerCase();
                              return (
                                service.service_name?.toLowerCase().includes(searchTerm) ||
                                service.service_description?.toLowerCase().includes(searchTerm) ||
                                (service.tags && service.tags.some(tag => tag.toLowerCase().includes(searchTerm)))
                              );
                            }).length / pageSize)}
                            onClick={() => setCurrentPage(prev => prev + 1)}
                          >
                            下一页
                          </Button>
                        </Box>
                      )}
                    </>
                  )}
                </Box>
              </Box>
            </Box>


          </Box>
        )}
      </DialogContent>
      
      <DialogActions sx={{ borderTop: '1px solid #e5e7eb', px: 3, py: 2 }}>
        {step === 1 ? (
          <>
            <Button onClick={handleClose} color="inherit">
              取消
            </Button>
            <Button
              onClick={handleNextStep}
              variant="contained"
              startIcon={<Add />}
            >
              选择渠道服务
            </Button>
          </>
        ) : (
          <>
            <Button onClick={handlePrevStep} color="inherit">
              上一步
            </Button>
            <Button onClick={handleClose} color="inherit" sx={{ ml: 1 }}>
              取消
            </Button>
            <Button
              onClick={handleSubmit}
              variant="contained"
              disabled={loading || formData.selectedChannels.length === 0}
              startIcon={loading ? <CircularProgress size={20} /> : <Send />}
              sx={{ ml: 1 }}
            >
              {loading ? '发布中...' : (type === 'creation' ? '发布需求' : '发布稿件')}
            </Button>
          </>
        )}
      </DialogActions>
    </Dialog>
  );
}

export default ContentManagementDialog;
