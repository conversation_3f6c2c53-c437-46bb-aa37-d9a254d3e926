import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Typography,
  Paper,
  IconButton,
  TextField,
  Button,
  Avatar,
  Divider,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  Tooltip,
  CircularProgress,
  Alert,
  Snackbar,
  Menu,
  Fade,
  InputAdornment,
  Grid,
  FormHelperText,
  Checkbox,
  Tabs,
  Tab,
  Card,
  CardContent,
  Switch,
  FormControlLabel,
  Pagination,
} from '@mui/material';
import {
  Send,
  AutoAwesome,
  History,
  ContentCopy,
  Download,
  Save,
  Settings,
  SmartToy,
  Publish,
  MoreVert,
  Article,
  Refresh,
  Delete,
  Add,
  Edit,
  Tune,
  Description,
  BusinessCenter,
  Label,
  Category,
  KeyboardArrowDown,
  CheckCircle,
  Psychology,
  TextFields,
  LibraryBooks,
  Storage,
  Link,
} from '@mui/icons-material';
import ApiService from '../../services/api';
import templateService from '../../services/templateService';

function AIContentCreation() {
  // 状态管理
  const [messages, setMessages] = useState([
    {
      id: 1,
      text: '您好！我是AI内容创作助手。请告诉我您想创建什么类型的内容，我会根据您的需求生成专业的文章。',
      isUser: false
    }
  ]);
  const [inputMessage, setInputMessage] = useState('');
  const [articleContent, setArticleContent] = useState('');
  const [articleTitle, setArticleTitle] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });
  const messagesEndRef = useRef(null);

  // 对话历史记录管理
  const [conversations, setConversations] = useState([]);
  const [currentConversationId, setCurrentConversationId] = useState(null);
  const [showHistoryDialog, setShowHistoryDialog] = useState(false);
  const [loadingConversations, setLoadingConversations] = useState(false);

  // 标签页管理
  const [activeTab, setActiveTab] = useState(0);

  // 模板和参数管理
  const [templates, setTemplates] = useState([]);
  const [selectedTemplate, setSelectedTemplate] = useState('');
  const [templateParameters, setTemplateParameters] = useState([]);
  const [parameterValues, setParameterValues] = useState({});
  const [loadingTemplates, setLoadingTemplates] = useState(false);

  // 知识库管理
  const [selectedKnowledgeBases, setSelectedKnowledgeBases] = useState([]);
  const [knowledgeEnabled, setKnowledgeEnabled] = useState(false);
  const [availableKnowledgeBases, setAvailableKnowledgeBases] = useState([]);
  const [loadingKnowledgeBases, setLoadingKnowledgeBases] = useState(false);

  // 知识库分页
  const [knowledgeCurrentPage, setKnowledgeCurrentPage] = useState(1);
  const [knowledgeTotalPages, setKnowledgeTotalPages] = useState(1);
  const knowledgePageSize = 5; // 每页显示5个知识库
  
  // 系统提示词（固定不变）
  const systemPrompt = '你是一位专业的内容创作者，能够根据用户需求生成高质量的文章内容。请确保内容专业、结构清晰、语言流畅。';

  // 加载对话历史列表
  const loadConversations = async () => {
    setLoadingConversations(true);
    try {
      const token = localStorage.getItem('ai_seo_auth_token');
      const response = await fetch('http://localhost:8000/api/v1/conversations?page=1&size=20', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        if (data?.items && Array.isArray(data.items)) {
          setConversations(data.items);
        }
      } else {
        console.error('加载对话历史失败:', response.status);
      }
    } catch (error) {
      console.error('加载对话历史失败:', error);
    } finally {
      setLoadingConversations(false);
    }
  };

  // 创建新对话
  const createNewConversation = async (title) => {
    try {
      const token = localStorage.getItem('ai_seo_auth_token');
      const response = await fetch('http://localhost:8000/api/v1/conversations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          title: title || `新对话 ${new Date().toLocaleString()}`,
          template_id: selectedTemplate || null,
          template_parameters: parameterValues,
          knowledge_bases: knowledgeEnabled ? selectedKnowledgeBases : []
        }),
      });

      if (response.ok) {
        const data = await response.json();
        setCurrentConversationId(data.id);
        await loadConversations(); // 重新加载对话列表
        return data.id;
      } else {
        console.error('创建对话失败:', response.status);
        return null;
      }
    } catch (error) {
      console.error('创建对话失败:', error);
      return null;
    }
  };

  // 加载特定对话的消息历史
  const loadConversationMessages = async (conversationId) => {
    try {
      const token = localStorage.getItem('ai_seo_auth_token');
      const response = await fetch(`http://localhost:8000/api/v1/conversations/${conversationId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        if (data.messages && Array.isArray(data.messages)) {
          // 转换消息格式
          const formattedMessages = data.messages.map(msg => ({
            id: msg.id,
            text: msg.content,
            isUser: msg.message_type === 'USER',
            timestamp: msg.created_at
          }));
          setMessages(formattedMessages);

          // 如果有AI回复，显示最后一个AI回复作为生成内容
          const lastAIMessage = formattedMessages.filter(msg => !msg.isUser).pop();
          if (lastAIMessage) {
            setArticleContent(lastAIMessage.text);
          }
        }
      } else {
        console.error('加载对话消息失败:', response.status);
      }
    } catch (error) {
      console.error('加载对话消息失败:', error);
    }
  };

  // 加载知识库列表
  const loadKnowledgeBases = async (page = 1) => {
    console.log('🔄 开始加载知识库...', `页码: ${page}`);
    setLoadingKnowledgeBases(true);
    try {
      const token = localStorage.getItem('ai_seo_auth_token');
      console.log('🔑 Token存在:', !!token);
      const response = await fetch(`http://localhost:8000/api/v1/knowledge-bases?page=${page}&size=${knowledgePageSize}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        console.log('📊 API响应数据:', data);
        // API返回的数据结构是 { items: [...], pagination: {...} }
        if (data?.items && Array.isArray(data.items)) {
          console.log('📚 设置知识库数据:', data.items);
          setAvailableKnowledgeBases(data.items);

          // 更新分页信息
          if (data.pagination) {
            setKnowledgeTotalPages(data.pagination.pages || 1);
            setKnowledgeCurrentPage(data.pagination.page || 1);
          }
        } else {
          console.log('⚠️ 没有找到知识库数据');
          setAvailableKnowledgeBases([]);
          setKnowledgeTotalPages(1);
          setKnowledgeCurrentPage(1);
        }
      } else {
        console.error('❌ API请求失败:', response.status);
        setAvailableKnowledgeBases([]);
        setKnowledgeTotalPages(1);
        setKnowledgeCurrentPage(1);
      }
    } catch (error) {
      console.error('加载知识库列表失败:', error);
      setAvailableKnowledgeBases([]);
      setKnowledgeTotalPages(1);
      setKnowledgeCurrentPage(1);

      // 如果是认证错误，不显示错误提示
      if (error.response?.status === 401 || error.response?.status === 403) {
        console.log('用户未认证，跳过加载知识库');
      } else {
        setSnackbar({
          open: true,
          message: '加载知识库列表失败，请稍后重试',
          severity: 'error'
        });
      }
    } finally {
      setLoadingKnowledgeBases(false);
    }
  };

  // 初始化加载模板和知识库
  useEffect(() => {
    loadTemplates();
    loadKnowledgeBases();
    loadConversations();
  }, []);

  // 知识库选择切换
  const handleKnowledgeBaseToggle = (kbId) => {
    setSelectedKnowledgeBases(prev => {
      if (prev.includes(kbId)) {
        return prev.filter(id => id !== kbId);
      } else {
        return [...prev, kbId];
      }
    });
  };

  // 知识库分页处理
  const handleKnowledgePageChange = (event, page) => {
    setKnowledgeCurrentPage(page);
    loadKnowledgeBases(page);
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // 解析模板参数
  const parseTemplateParameters = (defaultParameters) => {
    if (!defaultParameters || typeof defaultParameters !== 'object') {
      return [
        { key: 'content', label: '内容要求', required: true, placeholder: '请输入内容要求' }
      ];
    }

    // 如果default_parameters包含参数定义，直接使用
    if (defaultParameters.parameters && Array.isArray(defaultParameters.parameters)) {
      return defaultParameters.parameters;
    }

    // 否则根据default_parameters的键值对生成参数
    return Object.keys(defaultParameters).map(key => ({
      key: key,
      label: key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
      required: true,
      placeholder: `请输入${key.replace(/_/g, ' ')}`
    }));
  };

  // 获取备用模板数据
  const getFallbackTemplates = () => {
    return [
      {
        id: 'product_intro',
        name: '产品介绍',
        description: '用于生成产品介绍文章',
        parameters: [
          { key: 'product_name', label: '产品名称', required: true, placeholder: '请输入产品名称' },
          { key: 'product_features', label: '产品特点', required: true, placeholder: '请输入产品主要特点' },
          { key: 'target_audience', label: '目标用户', required: false, placeholder: '请描述目标用户群体' },
        ]
      },
      {
        id: 'company_news',
        name: '公司新闻',
        description: '用于生成公司新闻稿',
        parameters: [
          { key: 'company_name', label: '公司名称', required: true, placeholder: '请输入公司名称' },
          { key: 'news_topic', label: '新闻主题', required: true, placeholder: '请输入新闻主题' },
        ]
      },
      {
        id: 'seo_blog',
        name: 'SEO博客',
        description: '用于生成SEO优化的博客文章',
        parameters: [
          { key: 'main_keyword', label: '主关键词', required: true, placeholder: '请输入主要关键词' },
          { key: 'secondary_keywords', label: '次要关键词', required: false, placeholder: '请输入次要关键词，用逗号分隔' },
          { key: 'target_length', label: '目标字数', required: false, placeholder: '例如：1500' },
          { key: 'industry', label: '行业领域', required: true, placeholder: '请输入所属行业' },
          { key: 'content_style', label: '内容风格', required: false, placeholder: '专业/通俗/技术等' },
        ]
      },
      {
        id: 'marketing_copy',
        name: '营销文案',
        description: '用于生成营销推广文案',
        parameters: [
          { key: 'brand_name', label: '品牌名称', required: true, placeholder: '请输入品牌名称' },
          { key: 'campaign_goal', label: '营销目标', required: true, placeholder: '例如：提升品牌知名度' },
          { key: 'promotion_channel', label: '推广渠道', required: false, placeholder: '例如：社交媒体、邮件等' },
          { key: 'call_to_action', label: '行动号召', required: false, placeholder: '例如：立即购买、免费试用等' },
          { key: 'special_offer', label: '特别优惠', required: false, placeholder: '如有优惠信息请填写' },
          { key: 'tone_of_voice', label: '语调风格', required: false, placeholder: '例如：专业、友好、激情等' },
        ]
      },
      {
        id: 'simple_article',
        name: '通用文章',
        description: '简单的文章生成，参数较少',
        parameters: [
          { key: 'topic', label: '文章主题', required: true, placeholder: '请输入文章主题' },
        ]
      }
    ];
  };

  // 加载模板列表
  const loadTemplates = async () => {
    setLoadingTemplates(true);
    try {
      // 调用真实的API获取模板列表
      const response = await templateService.getTemplateList({
        page: 1,
        size: 100,
        is_active: true
      });

      if (response.success && response.data && response.data.items) {
        // 转换API响应数据为前端需要的格式
        const apiTemplates = response.data.items.map(template => ({
          id: template.id,
          name: template.template_name,
          description: template.template_description || '暂无描述',
          parameters: parseTemplateParameters(template.default_parameters)
        }));

        setTemplates(apiTemplates);

        // 默认选择第一个模板
        if (apiTemplates.length > 0) {
          setSelectedTemplate(apiTemplates[0].id);
          setTemplateParameters(apiTemplates[0].parameters);
          // 初始化参数值
          const initialValues = {};
          apiTemplates[0].parameters.forEach(param => {
            initialValues[param.key] = '';
          });
          setParameterValues(initialValues);
        }
      } else {
        // 如果API调用失败，使用备用的模拟数据
        console.warn('API调用失败，使用备用模板数据');
        const fallbackTemplates = getFallbackTemplates();
        setTemplates(fallbackTemplates);

        if (fallbackTemplates.length > 0) {
          setSelectedTemplate(fallbackTemplates[0].id);
          setTemplateParameters(fallbackTemplates[0].parameters);
          const initialValues = {};
          fallbackTemplates[0].parameters.forEach(param => {
            initialValues[param.key] = '';
          });
          setParameterValues(initialValues);
        }
      }
    } catch (error) {
      console.error('Failed to load templates:', error);

      // 发生错误时使用备用模板数据
      const fallbackTemplates = getFallbackTemplates();
      setTemplates(fallbackTemplates);

      if (fallbackTemplates.length > 0) {
        setSelectedTemplate(fallbackTemplates[0].id);
        setTemplateParameters(fallbackTemplates[0].parameters);
        const initialValues = {};
        fallbackTemplates[0].parameters.forEach(param => {
          initialValues[param.key] = '';
        });
        setParameterValues(initialValues);
      }

      setSnackbar({ open: true, message: '加载模板失败，请稍后重试', severity: 'error' });
    } finally {
      setLoadingTemplates(false);
    }
  };

  // 处理模板切换
  const handleTemplateChange = (templateId) => {
    const template = templates.find(t => t.id === templateId);
    if (template) {
      setSelectedTemplate(templateId);
      setTemplateParameters(template.parameters);
      // 重置参数值
      const initialValues = {};
      template.parameters.forEach(param => {
        initialValues[param.key] = '';
      });
      setParameterValues(initialValues);
    }
  };

  // 处理参数值更新
  const handleParameterChange = (key, value) => {
    setParameterValues(prev => ({
      ...prev,
      [key]: value
    }));
  };

  // 构建带参数的提示词（类似Jinja模板）
  const buildPromptWithParameters = (userInput) => {
    let enhancedPrompt = userInput;
    
    // 检查是否有填写的参数
    const filledParams = Object.entries(parameterValues).filter(([_, value]) => value);
    
    if (filledParams.length > 0 && Array.isArray(templateParameters)) {
      // 在用户输入后添加参数上下文
      enhancedPrompt += '\n\n请基于以下信息生成内容：\n';
      
      // 将参数值融入提示词
      templateParameters.forEach(param => {
        const value = parameterValues[param.key];
        if (value) {
          enhancedPrompt += `${param.label}：${value}\n`;
        }
      });
    }
    
    // 如果用户输入中包含参数占位符（如 {{product_name}}），替换它们
    Object.entries(parameterValues).forEach(([key, value]) => {
      if (value) {
        // 支持多种占位符格式
        const patterns = [
          new RegExp(`{{\\s*${key}\\s*}}`, 'g'),  // {{ product_name }}
          new RegExp(`{${key}}`, 'g'),             // {product_name}
          new RegExp(`\\$\\{${key}\\}`, 'g'),      // ${product_name}
        ];
        
        patterns.forEach(pattern => {
          enhancedPrompt = enhancedPrompt.replace(pattern, value);
        });
      }
    });
    
    return enhancedPrompt;
  };

  // 处理发送消息
  const handleSendMessage = async () => {
    if (!inputMessage.trim() || isGenerating) return;

    // 如果没有当前对话，创建新对话
    let conversationId = currentConversationId;
    if (!conversationId) {
      conversationId = await createNewConversation(inputMessage.slice(0, 50) + '...');
      if (!conversationId) {
        setSnackbar({ open: true, message: '创建对话失败', severity: 'error' });
        return;
      }
    }

    // 构建带参数的提示词
    const enhancedInput = buildPromptWithParameters(inputMessage);

    const userMessage = {
      id: Date.now(),
      text: inputMessage,
      isUser: true,
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsGenerating(true);

    try {
      // 显示当前使用的参数
      const paramInfo = Object.entries(parameterValues)
        .filter(([_, value]) => value)
        .map(([key, value]) => {
          const param = Array.isArray(templateParameters) 
            ? templateParameters.find(p => p.key === key)
            : null;
          return param ? `${param.label}: ${value}` : '';
        })
        .filter(info => info)
        .join(', ');

      const aiResponse = {
        id: Date.now() + 1,
        text: `正在根据您的需求生成内容。${paramInfo ? `\n使用参数：${paramInfo}` : ''}\n\n系统正在处理中...`,
        isUser: false,
      };
      
      setMessages(prev => [...prev, aiResponse]);
      
      // 调用真实的豆包AI API
      try {
        const token = localStorage.getItem('ai_seo_auth_token');
        const response = await fetch('http://localhost:8000/api/v1/ai/content/generate/v2', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
          },
          body: JSON.stringify({
            content_type: 'article',
            topic: enhancedInput,
            keywords: enhancedInput.split(/\s+/).filter(word => word.length > 0).slice(0, 5).concat(['内容', 'AI']).slice(0, 3),
            target_audience: parameterValues.target_audience || '通用受众',
            tone: 'professional',
            length: 'medium',
            language: 'zh-CN',
            ai_model: 'doubao',
            template_id: selectedTemplate || null,
            template_parameters: parameterValues,
            knowledge_bases: knowledgeEnabled ? selectedKnowledgeBases : [],
            conversation_id: conversationId,
            is_first_request: !currentConversationId
          }),
        });

        if (!response.ok) {
          throw new Error('网络请求失败');
        }

        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let fullContent = '';

        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          const chunk = decoder.decode(value);
          const lines = chunk.split('\n');

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              try {
                const data = JSON.parse(line.slice(6));

                if (data.type === 'content') {
                  fullContent += data.content;
                  setArticleContent(fullContent);
                } else if (data.type === 'final') {
                  setArticleContent(data.generated_content);
                  setArticleTitle(parameterValues.title || '生成的文章');

                  // 更新AI响应
                  setMessages(prev => {
                    const newMessages = [...prev];
                    newMessages[newMessages.length - 1] = {
                      ...newMessages[newMessages.length - 1],
                      text: '内容已生成完成！您可以在右侧查看生成的文章。'
                    };
                    return newMessages;
                  });

                  setIsGenerating(false);
                  return;
                } else if (data.type === 'error') {
                  throw new Error(data.message);
                }
              } catch (parseError) {
                console.warn('解析流式数据失败:', parseError);
              }
            }
          }
        }
      } catch (apiError) {
        console.error('API调用失败:', apiError);
        setSnackbar({ open: true, message: `内容生成失败: ${apiError.message}`, severity: 'error' });
        setIsGenerating(false);
      }
      
    } catch (error) {
      console.error('Failed to generate content:', error);
      setSnackbar({ open: true, message: '内容生成失败，请重试', severity: 'error' });
      setIsGenerating(false);
    }
  };

  // 生成示例内容（根据模板和参数）
  const generateSampleContent = (input, params) => {
    const template = templates.find(t => t.id === selectedTemplate);
    let title = '生成的文章';
    let content = '';

    // 根据不同模板生成不同类型的内容
    switch (selectedTemplate) {
      case 'product_intro':
        title = params.product_name ? `${params.product_name} - 产品介绍` : '产品介绍';
        content = `
          <h2>产品概述</h2>
          <p>${params.product_name || '我们的产品'}是一款创新的解决方案，专为现代企业设计。${params.product_features ? `主要特点包括：${params.product_features}。` : ''}</p>
          
          <h2>核心优势</h2>
          <p>通过先进的技术架构和用户友好的设计，我们为客户提供了卓越的使用体验。${params.target_audience ? `特别适合${params.target_audience}使用。` : ''}</p>
          
          <h2>功能特性</h2>
          <ul>
            <li>高性能处理能力</li>
            <li>安全可靠的数据保护</li>
            <li>灵活的定制化选项</li>
            <li>7×24小时技术支持</li>
          </ul>
        `;
        break;
        
      case 'seo_blog':
        title = params.main_keyword ? `${params.main_keyword}完整指南` : 'SEO优化文章';
        content = `
          <h2>什么是${params.main_keyword || 'SEO优化'}</h2>
          <p>在${params.industry || '数字营销'}领域，${params.main_keyword || 'SEO'}是提升在线可见度的关键策略。${params.secondary_keywords ? `相关概念包括${params.secondary_keywords}。` : ''}</p>
          
          <h2>为什么${params.main_keyword || 'SEO'}如此重要</h2>
          <p>随着互联网竞争的加剧，掌握${params.main_keyword || 'SEO技巧'}对于业务成功至关重要。</p>
          
          <h2>实施策略</h2>
          <ol>
            <li>关键词研究与分析</li>
            <li>内容优化与创作</li>
            <li>技术SEO优化</li>
            <li>链接建设策略</li>
          </ol>
          
          <h2>最佳实践建议</h2>
          <p>${params.content_style === '技术' ? '从技术角度来看，' : ''}实施这些策略需要持续的努力和优化。</p>
        `;
        break;
        
      default:
        title = params.topic || '数字化转型：企业成功的关键路径';
        content = `
          <h2>引言</h2>
          <p>在当今快速变化的商业环境中，${params.topic || '数字化转型'}已经成为企业保持竞争力的必要条件。</p>
          
          <h2>核心要素</h2>
          <p>成功实施需要从多个维度同时推进，包括技术升级、流程优化和文化变革。</p>
          
          <h2>实施策略</h2>
          <p>企业应该采取渐进式的策略，从小规模试点开始，逐步扩大实施范围。</p>
          
          <h2>结语</h2>
          <p>这是一个需要持续投入和不断优化的长期过程。</p>
        `;
    }

    return { title, content };
  };

  // 验证必填参数
  const validateRequiredParameters = () => {
    if (!templateParameters || templateParameters.length === 0) {
      return true;
    }
    for (const param of templateParameters) {
      if (param.required && !parameterValues[param.key]) {
        return false;
      }
    }
    return true;
  };

  // 复制内容
  const handleCopyContent = () => {
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = articleContent;
    const textContent = tempDiv.textContent || tempDiv.innerText || '';
    navigator.clipboard.writeText(textContent);
    setSnackbar({ open: true, message: '内容已复制到剪贴板', severity: 'success' });
  };

  // 下载内容
  const handleDownloadContent = () => {
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = articleContent;
    const textContent = tempDiv.textContent || tempDiv.innerText || '';
    const blob = new Blob([textContent], { type: 'text/plain;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${articleTitle || '文章'}.txt`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
    setSnackbar({ open: true, message: '文件下载成功', severity: 'success' });
  };

  // 获取参数提示文本
  const getParameterHintText = () => {
    if (!templateParameters || !Array.isArray(templateParameters)) {
      return '参数配置';
    }
    const filledCount = Object.values(parameterValues).filter(v => v).length;
    const totalCount = templateParameters.length;
    const requiredCount = templateParameters.filter(p => p.required).length;
    
    if (filledCount === 0) {
      return `可配置 ${totalCount} 个参数（${requiredCount} 个必填）`;
    }
    return `已配置 ${filledCount}/${totalCount} 个参数`;
  };

  return (
    <Box sx={{ width: '100%', height: '100%', display: 'flex', flexDirection: 'column', backgroundColor: '#fafafa' }}>
      {/* 页面标题 */}
      <Box sx={{
        backgroundColor: '#fff',
        borderBottom: '1px solid #e0e0e0',
      }}>
        <Box sx={{ p: 3, pb: 0 }}>
          <Box>
            <Typography variant="h5" sx={{ fontWeight: 600, color: '#333', mb: 0.5 }}>
              AI内容创作
            </Typography>
            <Typography variant="body2" sx={{ color: '#999' }}>
              通过智能对话生成专业文章内容
            </Typography>
          </Box>
        </Box>

        {/* 标签页 */}
        <Tabs
          value={activeTab}
          onChange={(e, newValue) => setActiveTab(newValue)}
          sx={{
            px: 3,
            '& .MuiTabs-indicator': {
              backgroundColor: '#1976d2'
            }
          }}
        >
          <Tab
            icon={<SmartToy />}
            label="AI对话"
            iconPosition="start"
            sx={{
              textTransform: 'none',
              fontWeight: 500,
              minHeight: 48
            }}
          />
          <Tab
            icon={<Settings />}
            label="参数设置"
            iconPosition="start"
            sx={{
              textTransform: 'none',
              fontWeight: 500,
              minHeight: 48
            }}
          />
          <Tab
            icon={<LibraryBooks />}
            label="知识库设置"
            iconPosition="start"
            sx={{
              textTransform: 'none',
              fontWeight: 500,
              minHeight: 48
            }}
          />
        </Tabs>
      </Box>

      {/* 主内容区域 */}
      <Box sx={{ flex: 1, overflow: 'hidden' }}>
        {/* AI对话标签页 */}
        {activeTab === 0 && (
          <Box sx={{ height: '100%', display: 'flex', overflow: 'hidden' }}>
        {/* 对话区域 */}
        <Box sx={{
          flex: 1,
          display: 'flex',
          flexDirection: 'column',
          backgroundColor: '#fff'
        }}>
          {/* 对话头部 */}
          <Box sx={{ 
            p: 2, 
            borderBottom: '1px solid #f0f0f0',
            display: 'flex',
            alignItems: 'center',
            gap: 1
          }}>
            <SmartToy sx={{ color: '#1976d2' }} />
            <Typography variant="subtitle1" sx={{ fontWeight: 500, color: '#333' }}>
              AI助手
            </Typography>
            <Box sx={{ ml: 'auto', display: 'flex', gap: 0.5, alignItems: 'center' }}>
              {Object.entries(parameterValues).filter(([_, v]) => v).length > 0 && (
                <Chip
                  label={`${Object.entries(parameterValues).filter(([_, v]) => v).length} 个参数`}
                  size="small"
                  color="primary"
                  variant="outlined"
                />
              )}
              <IconButton
                size="small"
                onClick={() => setShowHistoryDialog(true)}
                sx={{ color: '#666' }}
                title="历史对话"
              >
                <History />
              </IconButton>
              <IconButton
                size="small"
                onClick={async () => {
                  setCurrentConversationId(null);
                  setMessages([{
                    id: 1,
                    text: '您好！我是AI内容创作助手。请告诉我您想创建什么类型的内容，我会根据您的需求生成专业的文章。',
                    isUser: false
                  }]);
                  setArticleContent('');
                  setArticleTitle('');
                }}
                sx={{ color: '#666' }}
                title="新建对话"
              >
                <Add />
              </IconButton>
            </Box>
          </Box>

          {/* 消息列表 */}
          <Box sx={{ 
            flex: 1, 
            overflowY: 'auto',
            p: 2,
            display: 'flex',
            flexDirection: 'column',
            gap: 2
          }}>
            {messages.map((message) => (
              <Box
                key={message.id}
                sx={{
                  display: 'flex',
                  justifyContent: message.isUser ? 'flex-end' : 'flex-start',
                }}
              >
                <Box sx={{
                  maxWidth: '85%',
                  p: 1.5,
                  borderRadius: 1,
                  backgroundColor: message.isUser ? '#1976d2' : '#f5f5f5',
                  color: message.isUser ? '#fff' : '#333',
                }}>
                  {!message.isUser && (
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
                      <SmartToy sx={{ fontSize: 16, color: '#1976d2' }} />
                      <Typography variant="caption" sx={{ fontWeight: 500, color: '#666' }}>
                        AI助手
                      </Typography>
                    </Box>
                  )}
                  <Typography variant="body2" sx={{ whiteSpace: 'pre-line' }}>
                    {message.text}
                  </Typography>
                </Box>
              </Box>
            ))}
            {isGenerating && (
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <CircularProgress size={16} />
                <Typography variant="body2" sx={{ color: '#666' }}>
                  正在生成内容...
                </Typography>
              </Box>
            )}
            <div ref={messagesEndRef} />
          </Box>

          {/* 输入区域 */}
          <Box sx={{ p: 2, borderTop: '1px solid #f0f0f0' }}>
            <Typography variant="caption" sx={{ color: '#999', display: 'block', mb: 1 }}>
              提示：您可以使用 {`{{参数名}}`} 格式引用参数，如 {`{{product_name}}`}
            </Typography>
            <TextField
              fullWidth
              multiline
              maxRows={4}
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              onKeyPress={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  handleSendMessage();
                }
              }}
              placeholder={`输入您的需求，例如：帮我写一篇关于{{${templateParameters[0]?.key || 'topic'}}}的文章...`}
              variant="outlined"
              size="small"
              disabled={isGenerating}
              sx={{
                '& .MuiOutlinedInput-root': {
                  backgroundColor: '#fafafa',
                  '&:hover': {
                    backgroundColor: '#f5f5f5',
                  },
                  '&.Mui-focused': {
                    backgroundColor: '#fff',
                  }
                }
              }}
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton 
                      onClick={handleSendMessage} 
                      disabled={!inputMessage.trim() || isGenerating}
                      size="small"
                      sx={{ color: '#1976d2' }}
                    >
                      <Send />
                    </IconButton>
                  </InputAdornment>
                ),
              }}
            />
          </Box>
        </Box>

        {/* 中间内容展示区域 */}
        <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column', backgroundColor: '#fff', borderRight: '1px solid #e0e0e0' }}>
          {/* 内容头部 */}
          <Box sx={{ 
            p: 2, 
            borderBottom: '1px solid #f0f0f0',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between'
          }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Article sx={{ color: '#666' }} />
              {articleTitle ? (
                <Typography variant="subtitle1" sx={{ fontWeight: 500, color: '#333' }}>
                  {articleTitle}
                </Typography>
              ) : (
                <Typography variant="subtitle1" sx={{ color: '#999' }}>
                  等待生成内容...
                </Typography>
              )}
            </Box>
            
            {articleContent && (
              <Box sx={{ display: 'flex', gap: 1 }}>
                <IconButton 
                  size="small" 
                  onClick={handleCopyContent}
                  sx={{ color: '#666' }}
                >
                  <ContentCopy fontSize="small" />
                </IconButton>
                <IconButton 
                  size="small" 
                  onClick={handleDownloadContent}
                  sx={{ color: '#666' }}
                >
                  <Download fontSize="small" />
                </IconButton>
                <IconButton 
                  size="small"
                  sx={{ color: '#666' }}
                >
                  <Save fontSize="small" />
                </IconButton>
                <IconButton 
                  size="small"
                  sx={{ color: '#1976d2' }}
                >
                  <Publish fontSize="small" />
                </IconButton>
              </Box>
            )}
          </Box>

          {/* 内容展示区域 */}
          <Box sx={{ 
            flex: 1, 
            overflowY: 'auto',
            p: 3
          }}>
            {articleContent ? (
              <Box sx={{
                maxWidth: '600px',
                margin: '0 auto',
                '& h2': {
                  fontSize: '1.5rem',
                  fontWeight: 600,
                  color: '#333',
                  marginTop: '24px',
                  marginBottom: '16px',
                },
                '& p': {
                  fontSize: '1rem',
                  lineHeight: 1.8,
                  color: '#666',
                  marginBottom: '16px',
                },
                '& ul, & ol': {
                  paddingLeft: '24px',
                  marginBottom: '16px',
                  '& li': {
                    fontSize: '1rem',
                    lineHeight: 1.8,
                    color: '#666',
                    marginBottom: '8px',
                  }
                },
                '& strong': {
                  color: '#333',
                  fontWeight: 500,
                }
              }}>
                <div dangerouslySetInnerHTML={{ __html: articleContent }} />
              </Box>
            ) : (
              <Box sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                color: '#999'
              }}>
                <AutoAwesome sx={{ fontSize: 48, mb: 2, opacity: 0.3 }} />
                <Typography variant="h6" sx={{ color: '#666', mb: 1 }}>
                  开始创作您的内容
                </Typography>
                <Typography variant="body2" sx={{ color: '#999' }}>
                  在左侧对话框中输入您的需求，AI将为您生成专业的文章
                </Typography>
              </Box>
            )}
          </Box>
        </Box>
          </Box>
        )}

        {/* 参数设置标签页 */}
        {activeTab === 1 && (
          <Box sx={{ height: '100%', p: 3, overflow: 'auto' }}>
            <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
              写作模板参数设置
            </Typography>

            {/* 模板选择 */}
            <Card sx={{ mb: 3 }}>
              <CardContent>
                <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600, display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Psychology sx={{ color: '#1976d2' }} />
                  选择写作模板
                </Typography>
                <FormControl fullWidth>
                  <Select
                    value={selectedTemplate}
                    onChange={(e) => handleTemplateChange(e.target.value)}
                    displayEmpty
                    sx={{
                      '& .MuiSelect-select': {
                        py: 1.5,
                        display: 'flex',
                        alignItems: 'center',
                        gap: 1
                      }
                    }}
                  >
                    <MenuItem value="">
                      <em>请选择模板</em>
                    </MenuItem>
                    {templates.map(template => (
                      <MenuItem key={template.id} value={template.id}>
                        <Box>
                          <Typography variant="body2" sx={{ fontWeight: 500 }}>
                            {template.name}
                          </Typography>
                          <Typography variant="caption" sx={{ color: '#666', display: 'block' }}>
                            {template.description}
                          </Typography>
                        </Box>
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>

                {selectedTemplate && (
                  <Box sx={{ mt: 2, p: 2, backgroundColor: '#f8f9fa', borderRadius: 1 }}>
                    <Typography variant="body2" sx={{ color: '#666' }}>
                      已选择模板：<strong>{templates.find(t => t.id === selectedTemplate)?.name}</strong>
                    </Typography>
                  </Box>
                )}
              </CardContent>
            </Card>

            {/* 参数配置 */}
            {selectedTemplate && templateParameters.length > 0 && (
              <Card sx={{ mb: 3 }}>
                <CardContent>
                  <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600, display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Tune sx={{ color: '#1976d2' }} />
                    参数配置
                  </Typography>
                  <Typography variant="body2" sx={{ mb: 3, color: '#666' }}>
                    请填写以下参数，这些参数将用于生成个性化的内容。
                  </Typography>

                  <Grid container spacing={3}>
                    {templateParameters.map((param) => (
                      <Grid item xs={12} sm={param.type === 'textarea' ? 12 : 6} key={param.key}>
                        <Box>
                          <Typography variant="body2" sx={{ mb: 1, fontWeight: 500 }}>
                            {param.label} {param.required && <span style={{ color: '#f44336' }}>*</span>}
                          </Typography>
                          <TextField
                            fullWidth
                            multiline={param.type === 'textarea'}
                            rows={param.type === 'textarea' ? 4 : 1}
                            value={parameterValues[param.key] || ''}
                            onChange={(e) => setParameterValues(prev => ({
                              ...prev,
                              [param.key]: e.target.value
                            }))}
                            placeholder={param.placeholder || `请输入${param.label}`}
                            variant="outlined"
                            size="small"
                            sx={{
                              backgroundColor: '#fff',
                              '& .MuiOutlinedInput-root': {
                                '&:hover fieldset': {
                                  borderColor: '#1976d2',
                                },
                              }
                            }}
                          />
                          {param.description && (
                            <Typography variant="caption" sx={{ color: '#666', mt: 0.5, display: 'block' }}>
                              💡 {param.description}
                            </Typography>
                          )}
                        </Box>
                      </Grid>
                    ))}
                  </Grid>

                  {/* 参数预览 */}
                  {Object.keys(parameterValues).length > 0 && (
                    <Box sx={{ mt: 3, p: 2, backgroundColor: '#f0f7ff', borderRadius: 1, border: '1px solid #dbeafe' }}>
                      <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 600, color: '#1976d2' }}>
                        参数预览
                      </Typography>
                      <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                        {Object.entries(parameterValues).filter(([_, value]) => value).map(([key, value]) => {
                          const param = templateParameters.find(p => p.key === key);
                          return (
                            <Chip
                              key={key}
                              label={`${param?.label}: ${value}`}
                              size="small"
                              color="primary"
                              variant="outlined"
                            />
                          );
                        })}
                      </Box>
                    </Box>
                  )}
                </CardContent>
              </Card>
            )}

            {!selectedTemplate && (
              <Box sx={{
                textAlign: 'center',
                py: 8,
                color: '#999'
              }}>
                <Settings sx={{ fontSize: 48, mb: 2, opacity: 0.5 }} />
                <Typography variant="body1" sx={{ mb: 1 }}>
                  请先选择一个写作模板来配置参数
                </Typography>
                <Typography variant="body2">
                  选择模板后，您可以配置相应的参数来个性化您的内容生成
                </Typography>
              </Box>
            )}
          </Box>
        )}

        {/* 知识库设置标签页 */}
        {activeTab === 2 && (
          <Box sx={{ height: '100%', p: 3, overflow: 'auto' }}>
            <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
              知识库关联设置
            </Typography>

            <Card>
              <CardContent>
                <Box sx={{ mb: 2 }}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={knowledgeEnabled}
                        onChange={(e) => setKnowledgeEnabled(e.target.checked)}
                      />
                    }
                    label="启用知识库"
                  />

                  {knowledgeEnabled && (
                    <Chip
                      icon={<LibraryBooks />}
                      label={`${selectedKnowledgeBases.length}个知识库`}
                      variant="outlined"
                      size="small"
                      sx={{ ml: 2 }}
                    />
                  )}
                </Box>

                {knowledgeEnabled && (
                  <>
                    <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600 }}>
                      选择相关知识库
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 3, color: '#666' }}>
                      选择与您的写作内容相关的知识库，AI将基于这些知识库生成更准确的内容。
                    </Typography>

                    {loadingKnowledgeBases ? (
                      <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
                        <CircularProgress />
                      </Box>
                    ) : availableKnowledgeBases.length === 0 ? (
                      <Box sx={{ textAlign: 'center', p: 4 }}>
                        <LibraryBooks sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                        <Typography variant="h6" color="text.secondary" gutterBottom>
                          还没有知识库
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          请先在知识库管理中创建知识库
                        </Typography>
                      </Box>
                    ) : (
                      <>
                        <List sx={{ width: '100%', bgcolor: 'background.paper' }}>
                          {availableKnowledgeBases.map((kb, index) => (
                            <ListItem
                              key={kb.id}
                              sx={{
                                border: '1px solid #e0e0e0',
                                borderColor: selectedKnowledgeBases.includes(kb.id) ? '#1976d2' : '#e0e0e0',
                                backgroundColor: selectedKnowledgeBases.includes(kb.id) ? '#e3f2fd' : '#fff',
                                borderRadius: 1,
                                mb: 1,
                                cursor: 'pointer',
                                transition: 'all 0.2s ease',
                                '&:hover': {
                                  backgroundColor: selectedKnowledgeBases.includes(kb.id) ? '#e3f2fd' : '#f5f5f5',
                                  borderColor: '#1976d2',
                                  boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
                                }
                              }}
                              onClick={() => handleKnowledgeBaseToggle(kb.id)}
                            >
                              <ListItemIcon sx={{ minWidth: 40 }}>
                                <Checkbox
                                  checked={selectedKnowledgeBases.includes(kb.id)}
                                  size="small"
                                />
                              </ListItemIcon>
                              <ListItemText
                                primary={
                                  <Typography
                                    variant="subtitle1"
                                    sx={{
                                      fontSize: '0.95rem',
                                      fontWeight: 600,
                                      overflow: 'hidden',
                                      textOverflow: 'ellipsis',
                                      whiteSpace: 'nowrap'
                                    }}
                                  >
                                    📚 {kb.name}
                                  </Typography>
                                }
                                secondary={
                                  <Typography
                                    variant="body2"
                                    sx={{
                                      color: '#666',
                                      fontSize: '0.8rem',
                                      overflow: 'hidden',
                                      textOverflow: 'ellipsis',
                                      whiteSpace: 'nowrap',
                                      mt: 0.5
                                    }}
                                  >
                                    {kb.description || '暂无描述'}
                                  </Typography>
                                }
                              />
                              <Box sx={{ display: 'flex', gap: 1, ml: 2 }}>
                                <Chip
                                  label={`${kb.document_count || 0} 文档`}
                                  size="small"
                                  variant="outlined"
                                  sx={{ fontSize: '0.7rem', height: 24 }}
                                />
                                <Chip
                                  label={`${kb.vector_count || 0} 向量`}
                                  size="small"
                                  variant="outlined"
                                  sx={{ fontSize: '0.7rem', height: 24 }}
                                />
                              </Box>
                            </ListItem>
                          ))}
                        </List>

                        {/* 分页组件 */}
                        {knowledgeTotalPages > 1 && (
                          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
                            <Pagination
                              count={knowledgeTotalPages}
                              page={knowledgeCurrentPage}
                              onChange={handleKnowledgePageChange}
                              color="primary"
                              showFirstButton
                              showLastButton
                              size="medium"
                            />
                          </Box>
                        )}
                      </>
                    )}

                    {selectedKnowledgeBases.length > 0 && (
                      <Box sx={{ mt: 3, p: 2, backgroundColor: '#f8f9fa', borderRadius: 1 }}>
                        <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 600 }}>
                          已选择的知识库：
                        </Typography>
                        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                          {selectedKnowledgeBases.map(kbId => {
                            const kb = availableKnowledgeBases.find(k => k.id === kbId);
                            return (
                              <Chip
                                key={kbId}
                                label={`📚 ${kb?.name || '未知知识库'}`}
                                onDelete={() => handleKnowledgeBaseToggle(kbId)}
                                color="primary"
                                variant="outlined"
                              />
                            );
                          })}
                        </Box>
                      </Box>
                    )}
                  </>
                )}
              </CardContent>
            </Card>
          </Box>
        )}
      </Box>

      {/* 历史对话对话框 */}
      <Dialog
        open={showHistoryDialog}
        onClose={() => setShowHistoryDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <History />
            历史对话
          </Box>
        </DialogTitle>
        <DialogContent>
          {loadingConversations ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          ) : conversations.length === 0 ? (
            <Box sx={{ textAlign: 'center', p: 3 }}>
              <Typography variant="body2" color="text.secondary">
                暂无历史对话
              </Typography>
            </Box>
          ) : (
            <List>
              {conversations.map((conversation) => (
                <ListItem
                  key={conversation.id}
                  onClick={async () => {
                    setCurrentConversationId(conversation.id);
                    await loadConversationMessages(conversation.id);
                    setShowHistoryDialog(false);
                  }}
                  sx={{
                    border: '1px solid #e5e7eb',
                    borderRadius: '8px',
                    mb: 1,
                    cursor: 'pointer',
                    '&:hover': {
                      backgroundColor: '#f9fafb',
                    },
                  }}
                >
                  <ListItemIcon>
                    <History />
                  </ListItemIcon>
                  <ListItemText
                    primary={conversation.title}
                    secondary={`${new Date(conversation.created_at).toLocaleString()} · ${conversation.message_count || 0} 条消息`}
                  />
                </ListItem>
              ))}
            </List>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowHistoryDialog(false)}>关闭</Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar 通知 */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={3000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          variant="filled"
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
}

export default AIContentCreation;