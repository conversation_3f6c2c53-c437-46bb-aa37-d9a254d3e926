import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Tabs,
  Tab,
  Card,
  Grid,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Chip,
  Avatar,
  IconButton,
  Button,
  LinearProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Tooltip,
  Alert,
  Divider,
  CircularProgress,
  Rating,
  ToggleButton,
  ToggleButtonGroup,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Slider,
} from '@mui/material';
import {
  TrendingUp,
  TrendingDown,
  LocationOn,
  Analytics,
  Assessment,
  Timeline,
  BarChartOutlined,
  PieChartOutlined,
  ShowChart,
  Groups,
  Language,
  Speed,
  Visibility,
  Search,
  FilterList,
  Download,
  Refresh,
  ArrowUpward,
  ArrowDownward,
  Remove,
  Star,
  Public,
  LocalOffer,
  Psychology,
  EmojiEvents,
  Warning,
  CheckCircle,
  Error,
  Info,
  CalendarToday,
  AccessTime,
  CompareArrows,
  Insights,
  AutoGraph,
  QueryStats,
  Leaderboard,
  Monitor,
  Domain,
  KeyboardArrowUp,
  KeyboardArrowDown,
  FiberManualRecord,
  Add,
  Delete,
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';

const StyledContainer = styled(Box)(({ theme }) => ({
  height: 'calc(100vh - 64px)',
  display: 'flex',
  flexDirection: 'column',
  backgroundColor: '#ffffff',
  overflow: 'hidden',
}));

const HeaderSection = styled(Box)(({ theme }) => ({
  padding: theme.spacing(2, 3),
  backgroundColor: '#ffffff',
  borderBottom: '1px solid #f0f0f0',
}));

const ContentSection = styled(Box)(({ theme }) => ({
  flex: 1,
  overflow: 'auto',
  padding: theme.spacing(2),
  backgroundColor: '#ffffff',
  '&::-webkit-scrollbar': {
    width: '6px',
  },
  '&::-webkit-scrollbar-track': {
    backgroundColor: '#ffffff',
  },
  '&::-webkit-scrollbar-thumb': {
    backgroundColor: '#d1d5db',
    '&:hover': {
      backgroundColor: '#9ca3af',
    },
  },
}));

const MetricCard = styled(Box)(({ theme }) => ({
  height: '100%',
  borderRadius: 0,
  border: '1px solid #f0f0f0',
  backgroundColor: '#ffffff',
  transition: 'border-color 0.2s ease',
  '&:hover': {
    borderColor: '#e5e7eb',
  },
}));

const ChartCard = styled(Box)(({ theme }) => ({
  padding: 0,
  height: '100%',
  minHeight: '300px',
  borderRadius: 0,
  border: '1px solid #f0f0f0',
  display: 'flex',
  flexDirection: 'column',
  backgroundColor: '#ffffff',
  overflow: 'hidden',
}));

// 图表颜色配置
const COLORS = {
  primary: '#1976d2',
  secondary: '#42a5f5',
  success: '#4caf50',
  warning: '#ff9800',
  error: '#f44336',
  info: '#00bcd4',
  purple: '#666666',
  pink: '#e91e63',
  indigo: '#3f51b5',
  teal: '#009688',
};

// AI平台配置
const AI_PLATFORMS = {
  chatgpt: { name: 'ChatGPT', color: '#10a37f', icon: '🤖' },
  claude: { name: 'Claude', color: '#6b46c1', icon: '🧠' },
  perplexity: { name: 'Perplexity', color: '#1fb6ff', icon: '🔍' },
  grok: { name: 'Grok', color: '#000000', icon: '🚀' },
  gemini: { name: 'Gemini', color: '#4285f4', icon: '✨' },
};

const GeoMonitoringUpdated = () => {
  const [currentTab, setCurrentTab] = useState(1); // 默认显示排名分析标签
  const [selectedProject, setSelectedProject] = useState('all');
  const [timeRange, setTimeRange] = useState('7d');
  const [loading, setLoading] = useState(false);
  
  // 分页状态
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(8); // 默认每页显示8个关键词
  
  // 展开行状态
  const [expandedRows, setExpandedRows] = useState([]);
  
  // 添加关键词对话框状态
  const [addKeywordDialog, setAddKeywordDialog] = useState(false);
  const [newKeyword, setNewKeyword] = useState({
    keyword: '',
    volume: '',
    difficulty: 50,
  });
  
  // 竞争对手分析状态
  const [selectedCompetitor, setSelectedCompetitor] = useState('yousou');

  // 扩展的关键词排名数据（包含各AI平台排名）
  const [keywordRankingData, setKeywordRankingData] = useState([
    { 
      id: 1,
      keyword: 'AI搜索优化', 
      volume: 18500, 
      difficulty: 85,
      avgRank: 3.0,
      trend: 'up',
      platforms: {
        chatgpt: { rank: 2, change: 1 },
        claude: { rank: 3, change: 0 },
        perplexity: { rank: 1, change: 2 },
        grok: { rank: 5, change: -1 },
        gemini: { rank: 4, change: 1 },
      }
    },
    { 
      id: 2,
      keyword: '搜索引擎排名', 
      volume: 22000, 
      difficulty: 78,
      avgRank: 1.8,
      trend: 'stable',
      platforms: {
        chatgpt: { rank: 1, change: 0 },
        claude: { rank: 2, change: 1 },
        perplexity: { rank: 3, change: -1 },
        grok: { rank: 2, change: 0 },
        gemini: { rank: 1, change: 0 },
      }
    },
    { 
      id: 3,
      keyword: '关键词分析', 
      volume: 15000, 
      difficulty: 65,
      avgRank: 5.2,
      trend: 'down',
      platforms: {
        chatgpt: { rank: 5, change: -2 },
        claude: { rank: 4, change: 0 },
        perplexity: { rank: 6, change: -1 },
        grok: { rank: 8, change: 2 },
        gemini: { rank: 3, change: 1 },
      }
    },
    { 
      id: 4,
      keyword: 'SEO工具', 
      volume: 28000, 
      difficulty: 92,
      avgRank: 4.0,
      trend: 'up',
      platforms: {
        chatgpt: { rank: 3, change: 2 },
        claude: { rank: 5, change: 1 },
        perplexity: { rank: 2, change: 0 },
        grok: { rank: 4, change: -1 },
        gemini: { rank: 6, change: 0 },
      }
    },
    { 
      id: 5,
      keyword: '内容优化', 
      volume: 12000, 
      difficulty: 58,
      avgRank: 8.0,
      trend: 'down',
      platforms: {
        chatgpt: { rank: 8, change: -1 },
        claude: { rank: 6, change: 0 },
        perplexity: { rank: 7, change: 1 },
        grok: { rank: 10, change: -2 },
        gemini: { rank: 9, change: 0 },
      }
    },
    { 
      id: 6,
      keyword: '网站排名监控', 
      volume: 19000, 
      difficulty: 72,
      avgRank: 4.0,
      trend: 'up',
      platforms: {
        chatgpt: { rank: 4, change: 1 },
        claude: { rank: 3, change: 0 },
        perplexity: { rank: 5, change: 2 },
        grok: { rank: 6, change: -1 },
        gemini: { rank: 2, change: 0 },
      }
    },
    { 
      id: 7,
      keyword: 'AI内容生成', 
      volume: 8500, 
      difficulty: 45,
      avgRank: 11.2,
      trend: 'up',
      platforms: {
        chatgpt: { rank: 12, change: 3 },
        claude: { rank: 8, change: 1 },
        perplexity: { rank: 10, change: 0 },
        grok: { rank: 15, change: -2 },
        gemini: { rank: 11, change: 1 },
      }
    },
    { 
      id: 8,
      keyword: '搜索流量分析', 
      volume: 16000, 
      difficulty: 68,
      avgRank: 6.2,
      trend: 'stable',
      platforms: {
        chatgpt: { rank: 6, change: 0 },
        claude: { rank: 7, change: 1 },
        perplexity: { rank: 4, change: 0 },
        grok: { rank: 9, change: -1 },
        gemini: { rank: 5, change: 2 },
      }
    },
    { 
      id: 9,
      keyword: '长尾关键词', 
      volume: 9500, 
      difficulty: 52,
      avgRank: 10.0,
      trend: 'up',
      platforms: {
        chatgpt: { rank: 9, change: 1 },
        claude: { rank: 11, change: -1 },
        perplexity: { rank: 8, change: 0 },
        grok: { rank: 12, change: 2 },
        gemini: { rank: 10, change: 0 },
      }
    },
    { 
      id: 10,
      keyword: '语音搜索优化', 
      volume: 11000, 
      difficulty: 63,
      avgRank: 8.2,
      trend: 'up',
      platforms: {
        chatgpt: { rank: 7, change: 2 },
        claude: { rank: 9, change: 0 },
        perplexity: { rank: 6, change: 1 },
        grok: { rank: 11, change: -1 },
        gemini: { rank: 8, change: 0 },
      }
    },
    { 
      id: 11,
      keyword: '本地SEO优化', 
      volume: 7500, 
      difficulty: 41,
      avgRank: 11.6,
      trend: 'down',
      platforms: {
        chatgpt: { rank: 10, change: -1 },
        claude: { rank: 12, change: 0 },
        perplexity: { rank: 9, change: 1 },
        grok: { rank: 14, change: -2 },
        gemini: { rank: 13, change: 0 },
      }
    },
    { 
      id: 12,
      keyword: '移动端优化', 
      volume: 8800, 
      difficulty: 55,
      avgRank: 12.0,
      trend: 'stable',
      platforms: {
        chatgpt: { rank: 11, change: 0 },
        claude: { rank: 10, change: 1 },
        perplexity: { rank: 12, change: -1 },
        grok: { rank: 13, change: 0 },
        gemini: { rank: 14, change: 2 },
      }
    },
    { 
      id: 13,
      keyword: '网站速度优化', 
      volume: 14200, 
      difficulty: 71,
      avgRank: 7.4,
      trend: 'up',
      platforms: {
        chatgpt: { rank: 8, change: 1 },
        claude: { rank: 6, change: 2 },
        perplexity: { rank: 7, change: 0 },
        grok: { rank: 9, change: -1 },
        gemini: { rank: 7, change: 0 },
      }
    },
    { 
      id: 14,
      keyword: '结构化数据', 
      volume: 6300, 
      difficulty: 82,
      avgRank: 9.8,
      trend: 'down',
      platforms: {
        chatgpt: { rank: 10, change: -2 },
        claude: { rank: 9, change: 0 },
        perplexity: { rank: 11, change: -1 },
        grok: { rank: 8, change: 1 },
        gemini: { rank: 10, change: 0 },
      }
    },
    { 
      id: 15,
      keyword: '反向链接建设', 
      volume: 10500, 
      difficulty: 76,
      avgRank: 5.6,
      trend: 'up',
      platforms: {
        chatgpt: { rank: 5, change: 2 },
        claude: { rank: 6, change: 1 },
        perplexity: { rank: 4, change: 3 },
        grok: { rank: 7, change: 0 },
        gemini: { rank: 6, change: -1 },
      }
    },
  ]);

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleRowExpand = (id) => {
    setExpandedRows(prev => {
      if (prev.includes(id)) {
        return prev.filter(rowId => rowId !== id);
      } else {
        return [...prev, id];
      }
    });
  };

  // 处理添加关键词
  const handleAddKeyword = () => {
    const newKeywordData = {
      id: Date.now(),
      keyword: newKeyword.keyword,
      volume: parseInt(newKeyword.volume) || 0,
      difficulty: newKeyword.difficulty,
      avgRank: Math.floor(Math.random() * 20) + 1,
      trend: 'stable',
      platforms: {
        chatgpt: { rank: Math.floor(Math.random() * 20) + 1, change: 0 },
        claude: { rank: Math.floor(Math.random() * 20) + 1, change: 0 },
        perplexity: { rank: Math.floor(Math.random() * 20) + 1, change: 0 },
        grok: { rank: Math.floor(Math.random() * 20) + 1, change: 0 },
        gemini: { rank: Math.floor(Math.random() * 20) + 1, change: 0 },
      }
    };
    
    setKeywordRankingData(prev => [...prev, newKeywordData]);
    setAddKeywordDialog(false);
    setNewKeyword({ keyword: '', volume: '', difficulty: 50 });
  };

  // 处理删除关键词
  const handleDeleteKeyword = (id) => {
    setKeywordRankingData(prev => prev.filter(item => item.id !== id));
  };

  // 获取排名颜色
  const getRankColor = (rank) => {
    if (rank <= 3) return COLORS.success;
    if (rank <= 10) return COLORS.warning;
    return COLORS.error;
  };

  // 获取排名变化图标
  const getRankChangeIcon = (change) => {
    if (change > 0) return <ArrowUpward sx={{ fontSize: 14, color: COLORS.success }} />;
    if (change < 0) return <ArrowDownward sx={{ fontSize: 14, color: COLORS.error }} />;
    return <Remove sx={{ fontSize: 14, color: 'text.secondary' }} />;
  };

  // 渲染排名分析内容
  const renderRankingAnalysis = () => {
    const paginatedData = keywordRankingData.slice(
      page * rowsPerPage,
      page * rowsPerPage + rowsPerPage
    );

    return (
      <Box>
        {/* 关键词排名表格 */}
        <ChartCard>
          <Box sx={{ p: 2, pb: 0 }}>
            <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Box>
              <Typography variant="h6" fontWeight="600">
                关键词AI平台排名详情
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
                共 {keywordRankingData.length} 个关键词 · 第 {page + 1} 页，共 {Math.ceil(keywordRankingData.length / rowsPerPage)} 页
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Button
                variant="contained"
                startIcon={<Add />}
                onClick={() => setAddKeywordDialog(true)}
                size="small"
                sx={{
                  backgroundColor: '#1976d2',
                  '&:hover': { backgroundColor: '#1565c0' },
                  textTransform: 'none',
                }}
              >
                添加关键词
              </Button>
              <IconButton size="small">
                <FilterList />
              </IconButton>
              <IconButton size="small">
                <Download />
              </IconButton>
              <IconButton size="small">
                <Refresh />
              </IconButton>
              </Box>
            </Box>
          </Box>

          <TableContainer sx={{ px: 2, backgroundColor: '#ffffff' }}>
            <Table size="medium" sx={{ backgroundColor: '#ffffff' }}>
              <TableHead>
                <TableRow>
                  <TableCell width="30"></TableCell>
                  <TableCell>关键词</TableCell>
                  <TableCell align="center">搜索量</TableCell>
                  <TableCell align="center">难度</TableCell>
                  <TableCell align="center">平均排名</TableCell>
                  <TableCell align="center">趋势</TableCell>
                  {Object.keys(AI_PLATFORMS).map(platform => (
                    <TableCell key={platform} align="center">
                      <Tooltip title={AI_PLATFORMS[platform].name}>
                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 0.5 }}>
                          <span>{AI_PLATFORMS[platform].icon}</span>
                          <Typography variant="caption" sx={{ display: { xs: 'none', lg: 'inline' } }}>
                            {AI_PLATFORMS[platform].name}
                          </Typography>
                        </Box>
                      </Tooltip>
                    </TableCell>
                  ))}
                  <TableCell align="center">操作</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {paginatedData.map((row) => (
                  <React.Fragment key={row.id}>
                    <TableRow 
                      sx={{ 
                        cursor: 'pointer',
                        '&:hover': {
                          backgroundColor: '#fafafa'
                        }
                      }}
                      onClick={() => handleRowExpand(row.id)}
                    >
                      <TableCell>
                        <IconButton size="small">
                          {expandedRows.includes(row.id) ? <KeyboardArrowUp /> : <KeyboardArrowDown />}
                        </IconButton>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" fontWeight="600">
                          {row.keyword}
                        </Typography>
                      </TableCell>
                      <TableCell align="center">
                        <Typography variant="body2">
                          {row.volume.toLocaleString()}
                        </Typography>
                      </TableCell>
                      <TableCell align="center">
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <LinearProgress
                            variant="determinate"
                            value={row.difficulty}
                            sx={{
                              width: 60,
                              height: 6,
                              borderRadius: 3,
                              backgroundColor: '#e0e0e0',
                              '& .MuiLinearProgress-bar': {
                                backgroundColor: 
                                  row.difficulty > 70 ? COLORS.error : 
                                  row.difficulty > 40 ? COLORS.warning : 
                                  COLORS.success,
                              },
                            }}
                          />
                          <Typography variant="caption">{row.difficulty}</Typography>
                        </Box>
                      </TableCell>
                      <TableCell align="center">
                        <Chip
                          label={row.avgRank.toFixed(1)}
                          size="small"
                          sx={{
                            backgroundColor: getRankColor(row.avgRank),
                            color: 'white',
                            fontWeight: 600,
                          }}
                        />
                      </TableCell>
                      <TableCell align="center">
                        {row.trend === 'up' && <TrendingUp sx={{ color: COLORS.success }} />}
                        {row.trend === 'down' && <TrendingDown sx={{ color: COLORS.error }} />}
                        {row.trend === 'stable' && <Remove sx={{ color: 'text.secondary' }} />}
                      </TableCell>
                      {Object.keys(AI_PLATFORMS).map(platform => (
                        <TableCell key={platform} align="center">
                          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 0.5 }}>
                            <Chip
                              label={row.platforms[platform].rank}
                              size="small"
                              sx={{
                                backgroundColor: getRankColor(row.platforms[platform].rank),
                                color: 'white',
                                fontWeight: 600,
                                minWidth: 32,
                                height: 24,
                              }}
                            />
                            {getRankChangeIcon(row.platforms[platform].change)}
                          </Box>
                        </TableCell>
                      ))}
                      <TableCell align="center">
                        <IconButton 
                          size="small" 
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteKeyword(row.id);
                          }}
                          sx={{ color: '#f44336' }}
                        >
                          <Delete fontSize="small" />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                    
                    {/* 展开的详细信息 */}
                    {expandedRows.includes(row.id) && (
                      <TableRow>
                        <TableCell colSpan={12} sx={{ py: 2, bgcolor: '#ffffff' }}>
                          <Box sx={{ px: 2 }}>
                            <Grid container spacing={3}>
                              <Grid item xs={12} md={6}>
                                <Typography variant="subtitle2" fontWeight="600" gutterBottom>
                                  排名变化趋势
                                </Typography>
                                <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                                  {Object.entries(AI_PLATFORMS).map(([platform, config]) => (
                                    <Box key={platform} sx={{ 
                                      display: 'flex', 
                                      alignItems: 'center', 
                                      gap: 1,
                                      p: 1,
                                      border: '1px solid',
                                      borderColor: 'divider',
                                      borderRadius: 1,
                                      bgcolor: 'background.paper',
                                    }}>
                                      <span>{config.icon}</span>
                                      <Typography variant="body2" fontWeight="500">
                                        {config.name}:
                                      </Typography>
                                      <Typography variant="body2">
                                        #{row.platforms[platform].rank}
                                      </Typography>
                                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                        {getRankChangeIcon(row.platforms[platform].change)}
                                        <Typography variant="caption" sx={{ ml: 0.5 }}>
                                          {Math.abs(row.platforms[platform].change)}
                                        </Typography>
                                      </Box>
                                    </Box>
                                  ))}
                                </Box>
                              </Grid>
                              <Grid item xs={12} md={6}>
                                <Typography variant="subtitle2" fontWeight="600" gutterBottom>
                                  优化建议
                                </Typography>
                                <Box>
                                  <Box sx={{ py: 1 }}>
                                    <Typography variant="body2" fontWeight="500">
                                      提升内容相关性
                                    </Typography>
                                    <Typography variant="caption" color="text.secondary">
                                      增加关键词密度和语义相关词
                                    </Typography>
                                  </Box>
                                  <Box sx={{ py: 1 }}>
                                    <Typography variant="body2" fontWeight="500">
                                      优化页面结构
                                    </Typography>
                                    <Typography variant="caption" color="text.secondary">
                                      改善标题标签和元描述
                                    </Typography>
                                  </Box>
                                </Box>
                              </Grid>
                            </Grid>
                          </Box>
                        </TableCell>
                      </TableRow>
                    )}
                  </React.Fragment>
                ))}
              </TableBody>
            </Table>
          </TableContainer>

          {/* 分页控制 */}
          <Box sx={{ 
            display: 'flex', 
            justifyContent: 'space-between', 
            alignItems: 'center',
            borderTop: '1px solid #e0e0e0',
            bgcolor: '#ffffff',
            px: 2,
            py: 1.5,
            mt: 'auto'
          }}>
            {/* 左侧：每页显示数量选择 */}
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Typography variant="body2" color="text.secondary">
                每页显示：
              </Typography>
              <Select
                value={rowsPerPage}
                onChange={(e) => handleChangeRowsPerPage(e)}
                size="small"
                sx={{ minWidth: 70 }}
              >
                <MenuItem value={8}>8条</MenuItem>
                <MenuItem value={16}>16条</MenuItem>
                <MenuItem value={24}>24条</MenuItem>
              </Select>
            </Box>
            
            {/* 右侧：页码和分页按钮 */}
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              {/* 页码按钮组 */}
              <Box sx={{ display: 'flex', gap: 0.5 }}>
                {(() => {
                  const totalPages = Math.ceil(keywordRankingData.length / rowsPerPage);
                  const pageNumbers = [];
                  
                  // 显示逻辑：始终显示第一页、当前页及其前后页、最后一页
                  for (let i = 0; i < totalPages; i++) {
                    if (
                      i === 0 || // 第一页
                      i === totalPages - 1 || // 最后一页
                      (i >= page - 1 && i <= page + 1) // 当前页及其前后页
                    ) {
                      pageNumbers.push(i);
                    } else if (i === page - 2 || i === page + 2) {
                      pageNumbers.push('...');
                    }
                  }
                  
                  // 去重
                  const uniquePageNumbers = [...new Set(pageNumbers)];
                  
                  return uniquePageNumbers.map((pageNum, index) => {
                    if (pageNum === '...') {
                      return (
                        <Typography key={`ellipsis-${index}`} variant="body2" sx={{ px: 1 }}>
                          ...
                        </Typography>
                      );
                    }
                    return (
                      <Button
                        key={pageNum}
                        variant={page === pageNum ? 'contained' : 'text'}
                        size="small"
                        onClick={() => setPage(pageNum)}
                        sx={{
                          minWidth: 32,
                          height: 32,
                          p: 0,
                          fontSize: '0.875rem',
                          ...(page === pageNum && {
                            bgcolor: '#1976d2',
                            color: 'white',
                            '&:hover': {
                              bgcolor: '#1565c0',
                            },
                          }),
                        }}
                      >
                        {pageNum + 1}
                      </Button>
                    );
                  });
                })()}
              </Box>
              
              {/* 分页信息和箭头 */}
              <TablePagination
                rowsPerPageOptions={[]} // 隐藏默认的每页显示选项
                component="div"
                count={keywordRankingData.length}
                rowsPerPage={rowsPerPage}
                page={page}
                onPageChange={handleChangePage}
                onRowsPerPageChange={() => {}}
                labelRowsPerPage=""
                labelDisplayedRows={({ from, to, count }) => (
                  <Typography variant="body2" color="text.secondary">
                    {from}-{to} / {count}
                  </Typography>
                )}
                sx={{
                  '.MuiTablePagination-toolbar': {
                    minHeight: 40,
                    paddingLeft: 0,
                    paddingRight: 0,
                  },
                  '.MuiTablePagination-displayedRows': {
                    marginRight: 1,
                  },
                  '.MuiTablePagination-actions': {
                    marginLeft: 1,
                  },
                }}
              />
            </Box>
          </Box>
        </ChartCard>

        {/* 添加关键词对话框 */}
        <Dialog 
          open={addKeywordDialog} 
          onClose={() => setAddKeywordDialog(false)}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>
            添加关键词到监控
          </DialogTitle>
          <DialogContent>
            <Box sx={{ pt: 2 }}>
              <TextField
                fullWidth
                label="关键词"
                value={newKeyword.keyword}
                onChange={(e) => setNewKeyword({ ...newKeyword, keyword: e.target.value })}
                placeholder="例如：AI搜索优化"
                sx={{ mb: 3 }}
                required
              />
              
              <TextField
                fullWidth
                label="月搜索量"
                type="number"
                value={newKeyword.volume}
                onChange={(e) => setNewKeyword({ ...newKeyword, volume: e.target.value })}
                placeholder="例如：15000"
                sx={{ mb: 3 }}
              />
              
              <Box sx={{ mb: 2 }}>
                <Typography gutterBottom>
                  竞争难度: {newKeyword.difficulty}
                </Typography>
                <Slider
                  value={newKeyword.difficulty}
                  onChange={(e, value) => setNewKeyword({ ...newKeyword, difficulty: value })}
                  min={0}
                  max={100}
                  marks={[
                    { value: 0, label: '低' },
                    { value: 50, label: '中' },
                    { value: 100, label: '高' },
                  ]}
                  sx={{
                    '& .MuiSlider-track': {
                      backgroundColor: 
                        newKeyword.difficulty > 70 ? '#f44336' : 
                        newKeyword.difficulty > 40 ? '#ff9800' : 
                        '#4caf50',
                    },
                    '& .MuiSlider-thumb': {
                      backgroundColor: 
                        newKeyword.difficulty > 70 ? '#f44336' : 
                        newKeyword.difficulty > 40 ? '#ff9800' : 
                        '#4caf50',
                    },
                  }}
                />
              </Box>

              <Alert severity="info" sx={{ mt: 2 }}>
                添加关键词后，系统将自动开始监控该关键词在各AI平台的排名表现
              </Alert>
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setAddKeywordDialog(false)}>
              取消
            </Button>
            <Button 
              onClick={handleAddKeyword}
              variant="contained"
              disabled={!newKeyword.keyword}
            >
              添加
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    );
  };

  // 计算排名分布数据
  const calculateRankDistribution = () => {
    const distribution = {
      top3: 0,
      top5: 0,
      top10: 0,
      beyond10: 0,
    };
    
    keywordRankingData.forEach(keyword => {
      if (keyword.avgRank <= 3) {
        distribution.top3++;
        distribution.top5++;
        distribution.top10++;
      } else if (keyword.avgRank <= 5) {
        distribution.top5++;
        distribution.top10++;
      } else if (keyword.avgRank <= 10) {
        distribution.top10++;
      } else {
        distribution.beyond10++;
      }
    });
    
    return distribution;
  };

  // 计算各平台统计数据
  const calculatePlatformStats = () => {
    const stats = {};
    
    Object.keys(AI_PLATFORMS).forEach(platform => {
      let totalRank = 0;
      let top3Count = 0;
      let top10Count = 0;
      let improvingCount = 0;
      let decliningCount = 0;
      
      keywordRankingData.forEach(keyword => {
        const platformData = keyword.platforms[platform];
        totalRank += platformData.rank;
        
        if (platformData.rank <= 3) top3Count++;
        if (platformData.rank <= 10) top10Count++;
        if (platformData.change > 0) improvingCount++;
        if (platformData.change < 0) decliningCount++;
      });
      
      stats[platform] = {
        avgRank: (totalRank / keywordRankingData.length).toFixed(1),
        top3: top3Count,
        top10: top10Count,
        improving: improvingCount,
        declining: decliningCount,
        stable: keywordRankingData.length - improvingCount - decliningCount,
      };
    });
    
    return stats;
  };

  const rankDistribution = calculateRankDistribution();
  const platformStats = calculatePlatformStats();

  // 渲染AI平台分析
  const renderAIPlatformAnalysis = () => (
    <Box>
      {/* 平台对比总览 */}
      <Grid container spacing={2} sx={{ mb: 3 }}>
        {Object.entries(AI_PLATFORMS).map(([platform, config]) => {
          const stats = platformStats[platform];
          return (
            <Grid item xs={12} sm={6} md={2.4} key={platform}>
              <MetricCard>
                <Box sx={{ p: 2 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                    <Typography variant="h4">{config.icon}</Typography>
                    <Box sx={{ flex: 1 }}>
                      <Typography variant="subtitle2" fontWeight="600">
                        {config.name}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        综合评分
                      </Typography>
                    </Box>
                    <Box sx={{ 
                      width: 50, 
                      height: 50, 
                      display: 'flex', 
                      alignItems: 'center', 
                      justifyContent: 'center',
                      border: '3px solid',
                      borderColor: config.color,
                      borderRadius: 0,
                    }}>
                      <Typography variant="h6" fontWeight="bold" sx={{ color: config.color }}>
                        {(100 - parseFloat(stats.avgRank) * 5).toFixed(0)}
                      </Typography>
                    </Box>
                  </Box>
                  
                  <Box sx={{ mb: 1 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
                      <Typography variant="caption" color="text.secondary">平均排名</Typography>
                      <Typography variant="caption" fontWeight="600">{stats.avgRank}</Typography>
                    </Box>
                    <LinearProgress 
                      variant="determinate" 
                      value={(20 - parseFloat(stats.avgRank)) * 5}
                      sx={{ 
                        height: 3, 
                        borderRadius: 0,
                        bgcolor: '#e0e0e0',
                        '& .MuiLinearProgress-bar': {
                          bgcolor: config.color,
                          borderRadius: 0,
                        }
                      }}
                    />
                  </Box>
                  
                  <Box sx={{ 
                    display: 'flex', 
                    justifyContent: 'space-between',
                    pt: 1,
                    borderTop: '1px solid #f0f0f0',
                  }}>
                    <Box sx={{ textAlign: 'center' }}>
                      <Typography variant="h6" fontWeight="600" sx={{ color: '#4caf50' }}>
                        {stats.top3}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">Top3</Typography>
                    </Box>
                    <Box sx={{ textAlign: 'center' }}>
                      <Typography variant="h6" fontWeight="600" sx={{ color: '#ff9800' }}>
                        {stats.top10}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">Top10</Typography>
                    </Box>
                    <Box sx={{ textAlign: 'center' }}>
                      <Typography variant="h6" fontWeight="600" sx={{ color: stats.improving > stats.declining ? '#4caf50' : '#f44336' }}>
                        {stats.improving > stats.declining ? '+' : ''}{stats.improving - stats.declining}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">趋势</Typography>
                    </Box>
                  </Box>
                </Box>
              </MetricCard>
            </Grid>
          );
        })}
      </Grid>

      {/* 平台详细分析 */}
      <Grid container spacing={2}>
        {/* 左侧 - 排名分布对比 */}
        <Grid item xs={12} md={6}>
          <ChartCard>
            <Box sx={{ p: 2 }}>
              <Typography variant="h6" gutterBottom fontWeight="600">
                各平台排名分布对比
              </Typography>
              
              {/* 堆叠条形图 */}
              <Box sx={{ mt: 3 }}>
                {Object.entries(AI_PLATFORMS).map(([platform, config]) => {
                  const stats = platformStats[platform];
                  const top3Percent = (stats.top3 / keywordRankingData.length * 100).toFixed(0);
                  const top10Percent = ((stats.top10 - stats.top3) / keywordRankingData.length * 100).toFixed(0);
                  const beyondPercent = (100 - stats.top10 / keywordRankingData.length * 100).toFixed(0);
                  
                  return (
                    <Box key={platform} sx={{ mb: 2 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>
                        <Typography variant="h5">{config.icon}</Typography>
                        <Typography variant="body2" sx={{ width: 80 }}>{config.name}</Typography>
                        <Box sx={{ flex: 1, display: 'flex', height: 24 }}>
                          <Box sx={{ 
                            width: `${top3Percent}%`, 
                            bgcolor: '#4caf50',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                          }}>
                            {top3Percent > 10 && (
                              <Typography variant="caption" sx={{ color: 'white', fontSize: '10px' }}>
                                {top3Percent}%
                              </Typography>
                            )}
                          </Box>
                          <Box sx={{ 
                            width: `${top10Percent}%`, 
                            bgcolor: '#ff9800',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                          }}>
                            {top10Percent > 10 && (
                              <Typography variant="caption" sx={{ color: 'white', fontSize: '10px' }}>
                                {top10Percent}%
                              </Typography>
                            )}
                          </Box>
                          <Box sx={{ 
                            width: `${beyondPercent}%`, 
                            bgcolor: '#f44336',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                          }}>
                            {beyondPercent > 10 && (
                              <Typography variant="caption" sx={{ color: 'white', fontSize: '10px' }}>
                                {beyondPercent}%
                              </Typography>
                            )}
                          </Box>
                        </Box>
                        <Typography variant="caption" sx={{ width: 40, textAlign: 'right' }}>
                          {stats.avgRank}
                        </Typography>
                      </Box>
                    </Box>
                  );
                })}
              </Box>
              
              {/* 图例 */}
              <Box sx={{ display: 'flex', gap: 3, mt: 3, pt: 2, borderTop: '1px solid #f0f0f0' }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Box sx={{ width: 12, height: 12, bgcolor: '#4caf50' }} />
                  <Typography variant="caption">Top 3</Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Box sx={{ width: 12, height: 12, bgcolor: '#ff9800' }} />
                  <Typography variant="caption">Top 4-10</Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Box sx={{ width: 12, height: 12, bgcolor: '#f44336' }} />
                  <Typography variant="caption">10名后</Typography>
                </Box>
              </Box>
            </Box>
          </ChartCard>
        </Grid>

        {/* 右侧 - 平台趋势分析 */}
        <Grid item xs={12} md={6}>
          <ChartCard>
            <Box sx={{ p: 2 }}>
              <Typography variant="h6" gutterBottom fontWeight="600">
                平台排名趋势（7天）
              </Typography>
              
              {/* 模拟趋势线图 */}
              <Box sx={{ position: 'relative', height: 250, mt: 3 }}>
                {/* 网格背景 */}
                <Box sx={{ position: 'absolute', width: '100%', height: '100%' }}>
                  {[0, 1, 2, 3, 4].map((i) => (
                    <Box key={i} sx={{ 
                      position: 'absolute', 
                      width: '100%', 
                      borderTop: '1px solid #f0f0f0',
                      top: `${i * 25}%`
                    }} />
                  ))}
                </Box>
                
                {/* 趋势线 */}
                <svg width="100%" height="100%" style={{ position: 'relative', zIndex: 1 }}>
                  <polyline
                    points="0,120 60,115 120,110 180,105 240,108 300,100 360,95"
                    fill="none"
                    stroke="#10a37f"
                    strokeWidth="2"
                  />
                  <polyline
                    points="0,100 60,98 120,95 180,97 240,93 300,90 360,88"
                    fill="none"
                    stroke="#6b46c1"
                    strokeWidth="2"
                  />
                  <polyline
                    points="0,80 60,82 120,78 180,75 240,77 300,73 360,70"
                    fill="none"
                    stroke="#1fb6ff"
                    strokeWidth="2"
                  />
                  <polyline
                    points="0,140 60,135 120,138 180,130 240,125 300,128 360,120"
                    fill="none"
                    stroke="#000000"
                    strokeWidth="2"
                  />
                  <polyline
                    points="0,110 60,108 120,105 180,102 240,100 300,98 360,95"
                    fill="none"
                    stroke="#4285f4"
                    strokeWidth="2"
                  />
                </svg>
                
                {/* X轴标签 */}
                <Box sx={{ position: 'absolute', bottom: -25, width: '100%', display: 'flex', justifyContent: 'space-between' }}>
                  {['1日', '2日', '3日', '4日', '5日', '6日', '7日'].map((day) => (
                    <Typography key={day} variant="caption" color="text.secondary">{day}</Typography>
                  ))}
                </Box>
                
                {/* Y轴标签 */}
                <Box sx={{ position: 'absolute', left: -25, top: 0, height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'space-between' }}>
                  {[1, 5, 10, 15, 20].map((num) => (
                    <Typography key={num} variant="caption" color="text.secondary">{num}</Typography>
                  ))}
                </Box>
              </Box>
              
              {/* 平台图例 */}
              <Grid container spacing={2} sx={{ mt: 2 }}>
                {Object.entries(AI_PLATFORMS).map(([platform, config]) => (
                  <Grid item xs={6} sm={2.4} key={platform}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                      <Box sx={{ width: 16, height: 2, bgcolor: config.color }} />
                      <Typography variant="caption">{config.name}</Typography>
                    </Box>
                  </Grid>
                ))}
              </Grid>
            </Box>
          </ChartCard>
        </Grid>

        {/* 底部 - 关键词在各平台表现 */}
        <Grid item xs={12}>
          <ChartCard>
            <Box sx={{ p: 2 }}>
              <Typography variant="h6" gutterBottom fontWeight="600">
                热门关键词平台表现对比
              </Typography>
              
              <TableContainer sx={{ mt: 2 }}>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>关键词</TableCell>
                      <TableCell align="center">搜索量</TableCell>
                      {Object.entries(AI_PLATFORMS).map(([platform, config]) => (
                        <TableCell key={platform} align="center">
                          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 0.5 }}>
                            <Typography>{config.icon}</Typography>
                            <Typography variant="caption">{config.name}</Typography>
                          </Box>
                        </TableCell>
                      ))}
                      <TableCell align="center">平均排名</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {keywordRankingData.slice(0, 5).map((keyword) => (
                      <TableRow key={keyword.id}>
                        <TableCell>{keyword.keyword}</TableCell>
                        <TableCell align="center">
                          <Chip label={keyword.volume.toLocaleString()} size="small" variant="outlined" />
                        </TableCell>
                        {Object.entries(AI_PLATFORMS).map(([platform]) => {
                          const rank = keyword.platforms[platform].rank;
                          const change = keyword.platforms[platform].change;
                          return (
                            <TableCell key={platform} align="center">
                              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 0.5 }}>
                                <Typography 
                                  variant="body2" 
                                  fontWeight="600"
                                  sx={{ 
                                    color: rank <= 3 ? '#4caf50' : rank <= 10 ? '#ff9800' : '#f44336' 
                                  }}
                                >
                                  {rank}
                                </Typography>
                                {change !== 0 && (
                                  <Typography 
                                    variant="caption" 
                                    sx={{ 
                                      color: change > 0 ? '#4caf50' : '#f44336',
                                      fontSize: '10px'
                                    }}
                                  >
                                    {change > 0 ? `+${change}` : change}
                                  </Typography>
                                )}
                              </Box>
                            </TableCell>
                          );
                        })}
                        <TableCell align="center">
                          <Typography variant="body2" fontWeight="600">
                            {keyword.avgRank}
                          </Typography>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
              
              {/* 分析说明 */}
              <Box sx={{ mt: 3, p: 2, bgcolor: '#ffffff', border: '1px solid #e0e0e0' }}>
                <Typography variant="body2" sx={{ color: '#6b7280' }}>
                  <Info sx={{ fontSize: 16, verticalAlign: 'middle', mr: 1, color: '#9ca3af' }} />
                  Perplexity 在新兴AI搜索领域表现最佳，ChatGPT 保持稳定领先，Gemini 近期排名提升明显。
                  建议针对不同平台特点优化内容策略。
                </Typography>
              </Box>
            </Box>
          </ChartCard>
        </Grid>
      </Grid>
    </Box>
  );

  // 渲染竞争对手分析
  const renderCompetitorAnalysis = () => {
    // 竞争对手数据
    const competitors = {
      yousou: {
        id: 'yousou',
        name: '优搜科技',
        domain: 'yousou.com',
        description: '专注于AI搜索技术的创新企业',
        platforms: {
          chatgpt: { avgRank: 4.2, top3: 42, top10: 78, total: 156 },
          claude: { avgRank: 5.1, top3: 35, top10: 65, total: 142 },
          perplexity: { avgRank: 3.8, top3: 48, top10: 82, total: 163 },
          grok: { avgRank: 6.5, top3: 28, top10: 55, total: 128 },
          gemini: { avgRank: 4.9, top3: 38, top10: 71, total: 149 },
        },
        keywords: [
          { keyword: 'AI搜索优化', ourRank: 3, theirRank: 2, trend: 'up' },
          { keyword: '搜索引擎排名', ourRank: 1, theirRank: 4, trend: 'down' },
          { keyword: '关键词分析', ourRank: 5, theirRank: 3, trend: 'stable' },
          { keyword: 'SEO工具', ourRank: 4, theirRank: 6, trend: 'up' },
          { keyword: '内容优化', ourRank: 8, theirRank: 5, trend: 'down' },
        ]
      },
      zhisou: {
        id: 'zhisou',
        name: '智搜网络',
        domain: 'zhisou.net',
        description: '传统搜索优化服务提供商',
        platforms: {
          chatgpt: { avgRank: 6.8, top3: 25, top10: 52, total: 124 },
          claude: { avgRank: 7.2, top3: 22, top10: 48, total: 115 },
          perplexity: { avgRank: 5.9, top3: 31, top10: 58, total: 132 },
          grok: { avgRank: 8.1, top3: 18, top10: 41, total: 108 },
          gemini: { avgRank: 6.5, top3: 28, top10: 55, total: 121 },
        },
        keywords: [
          { keyword: 'AI搜索优化', ourRank: 3, theirRank: 5, trend: 'stable' },
          { keyword: '搜索引擎排名', ourRank: 1, theirRank: 3, trend: 'up' },
          { keyword: '关键词分析', ourRank: 5, theirRank: 7, trend: 'down' },
          { keyword: 'SEO工具', ourRank: 4, theirRank: 2, trend: 'up' },
          { keyword: '内容优化', ourRank: 8, theirRank: 9, trend: 'stable' },
        ]
      },
      aisearch: {
        id: 'aisearch',
        name: 'AI搜索专家',
        domain: 'aisearch.pro',
        description: '领先的AI搜索技术解决方案',
        platforms: {
          chatgpt: { avgRank: 2.5, top3: 68, top10: 115, total: 189 },
          claude: { avgRank: 3.2, top3: 58, top10: 102, total: 175 },
          perplexity: { avgRank: 2.1, top3: 72, top10: 125, total: 195 },
          grok: { avgRank: 4.8, top3: 45, top10: 85, total: 168 },
          gemini: { avgRank: 3.6, top3: 55, top10: 98, total: 182 },
        },
        keywords: [
          { keyword: 'AI搜索优化', ourRank: 3, theirRank: 1, trend: 'up' },
          { keyword: '搜索引擎排名', ourRank: 1, theirRank: 2, trend: 'up' },
          { keyword: '关键词分析', ourRank: 5, theirRank: 4, trend: 'stable' },
          { keyword: 'SEO工具', ourRank: 4, theirRank: 5, trend: 'down' },
          { keyword: '内容优化', ourRank: 8, theirRank: 3, trend: 'up' },
        ]
      }
    };

    const currentCompetitor = competitors[selectedCompetitor];

    // 计算我们的平台数据（用于对比）
    const ourPlatformData = {
      chatgpt: { avgRank: 5.8, top3: 38, top10: 72, total: 145 },
      claude: { avgRank: 6.2, top3: 35, top10: 68, total: 138 },
      perplexity: { avgRank: 4.5, top3: 45, top10: 78, total: 152 },
      grok: { avgRank: 7.3, top3: 28, top10: 58, total: 125 },
      gemini: { avgRank: 5.1, top3: 40, top10: 75, total: 148 },
    };

    return (
      <Box>
        {/* 顶部选择栏 */}
        <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Typography variant="h6" fontWeight="600">
              竞争对手分析
            </Typography>
            <FormControl size="small" sx={{ minWidth: 200 }}>
              <Select
                value={selectedCompetitor}
                onChange={(e) => setSelectedCompetitor(e.target.value)}
                displayEmpty
              >
                {Object.values(competitors).map(comp => (
                  <MenuItem key={comp.id} value={comp.id}>
                    {comp.name} ({comp.domain})
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>
          <Button
            variant="contained"
            startIcon={<Assessment />}
            sx={{
              backgroundColor: '#1976d2',
              '&:hover': { backgroundColor: '#1565c0' },
              textTransform: 'none',
            }}
          >
            生成详细报告
          </Button>
        </Box>

        {/* 竞争对手概览 */}
        <Grid container spacing={2} sx={{ mb: 3 }}>
          <Grid item xs={12}>
            <ChartCard>
              <Box sx={{ p: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                  <Box>
                    <Typography variant="h6" fontWeight="600">
                      {currentCompetitor.name}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {currentCompetitor.domain} • {currentCompetitor.description}
                    </Typography>
                  </Box>
                  <Chip 
                    label="实时监控中" 
                    color="success" 
                    size="small"
                    icon={<FiberManualRecord sx={{ fontSize: 8 }} />}
                  />
                </Box>
              </Box>
            </ChartCard>
          </Grid>
        </Grid>

        {/* AI平台排名对比 */}
        <Grid container spacing={2} sx={{ mb: 3 }}>
          <Grid item xs={12} md={8}>
            <ChartCard>
              <Box sx={{ p: 2 }}>
                <Typography variant="h6" gutterBottom fontWeight="600">
                  各AI平台排名对比
                </Typography>
                
                {/* 平台对比图表 */}
                <Box sx={{ mt: 3 }}>
                  {Object.entries(AI_PLATFORMS).map(([platform, config]) => {
                    const ourData = ourPlatformData[platform];
                    const theirData = currentCompetitor.platforms[platform];
                    const betterAvgRank = ourData.avgRank < theirData.avgRank;
                    
                    return (
                      <Box key={platform} sx={{ mb: 3 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>
                          <Typography variant="h5">{config.icon}</Typography>
                          <Typography variant="subtitle2" fontWeight="600" sx={{ width: 100 }}>
                            {config.name}
                          </Typography>
                          
                          {/* 对比条形图 */}
                          <Box sx={{ flex: 1 }}>
                            {/* 我们的数据 */}
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
                              <Typography variant="caption" sx={{ width: 60, color: '#1976d2' }}>
                                我们
                              </Typography>
                              <Box sx={{ flex: 1, display: 'flex', alignItems: 'center', gap: 1 }}>
                                <Box sx={{ 
                                  width: `${(20 - ourData.avgRank) * 5}%`,
                                  height: 20,
                                  bgcolor: '#1976d2',
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'flex-end',
                                  pr: 1,
                                }}>
                                  <Typography variant="caption" sx={{ color: 'white', fontSize: '11px' }}>
                                    {ourData.avgRank}
                                  </Typography>
                                </Box>
                                <Typography variant="caption" color="text.secondary">
                                  Top3: {ourData.top3} | Top10: {ourData.top10}
                                </Typography>
                              </Box>
                            </Box>
                            
                            {/* 竞争对手的数据 */}
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Typography variant="caption" sx={{ width: 60, color: '#ff9800' }}>
                                {currentCompetitor.name.slice(0, 4)}
                              </Typography>
                              <Box sx={{ flex: 1, display: 'flex', alignItems: 'center', gap: 1 }}>
                                <Box sx={{ 
                                  width: `${(20 - theirData.avgRank) * 5}%`,
                                  height: 20,
                                  bgcolor: '#ff9800',
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'flex-end',
                                  pr: 1,
                                }}>
                                  <Typography variant="caption" sx={{ color: 'white', fontSize: '11px' }}>
                                    {theirData.avgRank}
                                  </Typography>
                                </Box>
                                <Typography variant="caption" color="text.secondary">
                                  Top3: {theirData.top3} | Top10: {theirData.top10}
                                </Typography>
                              </Box>
                            </Box>
                          </Box>
                          
                          {/* 胜负指示器 */}
                          <Box sx={{ width: 40, textAlign: 'center' }}>
                            {betterAvgRank ? (
                              <CheckCircle sx={{ color: '#4caf50', fontSize: 20 }} />
                            ) : (
                              <Warning sx={{ color: '#ff9800', fontSize: 20 }} />
                            )}
                          </Box>
                        </Box>
                      </Box>
                    );
                  })}
                </Box>

                {/* 总结 */}
                <Box sx={{ mt: 3, p: 2, bgcolor: '#ffffff', border: '1px solid #1976d2' }}>
                  <Typography variant="body2">
                    <Info sx={{ fontSize: 16, verticalAlign: 'middle', mr: 1, color: '#1976d2' }} />
                    在{Object.entries(AI_PLATFORMS).filter(([platform]) => 
                      ourPlatformData[platform].avgRank < currentCompetitor.platforms[platform].avgRank
                    ).length}个AI平台上，我们的平均排名优于{currentCompetitor.name}
                  </Typography>
                </Box>
              </Box>
            </ChartCard>
          </Grid>

          {/* 右侧统计 */}
          <Grid item xs={12} md={4}>
            <Grid container spacing={2}>
              {/* 整体对比 */}
              <Grid item xs={12}>
                <MetricCard>
                  <Box sx={{ p: 2 }}>
                    <Typography variant="subtitle2" fontWeight="600" gutterBottom>
                      整体表现对比
                    </Typography>
                    
                    <Box sx={{ mt: 2 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                        <Typography variant="caption" color="text.secondary">平均排名</Typography>
                        <Box sx={{ display: 'flex', gap: 2 }}>
                          <Typography variant="body2" sx={{ color: '#1976d2' }}>
                            我们: 5.6
                          </Typography>
                          <Typography variant="body2" sx={{ color: '#ff9800' }}>
                            对手: {(Object.values(currentCompetitor.platforms).reduce((sum, p) => sum + p.avgRank, 0) / 5).toFixed(1)}
                          </Typography>
                        </Box>
                      </Box>
                      
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                        <Typography variant="caption" color="text.secondary">Top3关键词</Typography>
                        <Box sx={{ display: 'flex', gap: 2 }}>
                          <Typography variant="body2" sx={{ color: '#1976d2' }}>
                            我们: 186
                          </Typography>
                          <Typography variant="body2" sx={{ color: '#ff9800' }}>
                            对手: {Object.values(currentCompetitor.platforms).reduce((sum, p) => sum + p.top3, 0)}
                          </Typography>
                        </Box>
                      </Box>
                      
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                        <Typography variant="caption" color="text.secondary">监控关键词</Typography>
                        <Box sx={{ display: 'flex', gap: 2 }}>
                          <Typography variant="body2" sx={{ color: '#1976d2' }}>
                            我们: 708
                          </Typography>
                          <Typography variant="body2" sx={{ color: '#ff9800' }}>
                            对手: {Object.values(currentCompetitor.platforms).reduce((sum, p) => sum + p.total, 0)}
                          </Typography>
                        </Box>
                      </Box>
                    </Box>
                  </Box>
                </MetricCard>
              </Grid>

              {/* 优势平台 */}
              <Grid item xs={12}>
                <MetricCard>
                  <Box sx={{ p: 2 }}>
                    <Typography variant="subtitle2" fontWeight="600" gutterBottom>
                      我们的优势平台
                    </Typography>
                    <Box>
                      {Object.entries(AI_PLATFORMS)
                        .filter(([platform]) => 
                          ourPlatformData[platform].avgRank < currentCompetitor.platforms[platform].avgRank
                        )
                        .slice(0, 3)
                        .map(([platform, config]) => (
                          <Box key={platform} sx={{ py: 0.5 }}>
                            <Typography variant="body2">
                              {config.icon} {config.name} 
                              <Typography component="span" variant="caption" sx={{ ml: 1, color: '#4caf50' }}>
                                领先{(currentCompetitor.platforms[platform].avgRank - ourPlatformData[platform].avgRank).toFixed(1)}位
                              </Typography>
                            </Typography>
                          </Box>
                        ))}
                    </Box>
                  </Box>
                </MetricCard>
              </Grid>
            </Grid>
          </Grid>
        </Grid>

        {/* 关键词排名对比 */}
        <ChartCard>
          <Box sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom fontWeight="600">
              共同关键词排名对比
            </Typography>
            
            <TableContainer sx={{ mt: 2 }}>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>关键词</TableCell>
                    <TableCell align="center">我们的排名</TableCell>
                    <TableCell align="center">{currentCompetitor.name}排名</TableCell>
                    <TableCell align="center">差距</TableCell>
                    <TableCell align="center">趋势</TableCell>
                    <TableCell align="center">建议</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {currentCompetitor.keywords.map((kw, index) => {
                    const gap = kw.ourRank - kw.theirRank;
                    const isWinning = gap < 0;
                    
                    return (
                      <TableRow key={index}>
                        <TableCell>{kw.keyword}</TableCell>
                        <TableCell align="center">
                          <Chip
                            label={kw.ourRank}
                            size="small"
                            sx={{
                              backgroundColor: kw.ourRank <= 3 ? '#4caf50' : kw.ourRank <= 10 ? '#ff9800' : '#f44336',
                              color: 'white',
                              fontWeight: 600,
                            }}
                          />
                        </TableCell>
                        <TableCell align="center">
                          <Chip
                            label={kw.theirRank}
                            size="small"
                            sx={{
                              backgroundColor: kw.theirRank <= 3 ? '#4caf50' : kw.theirRank <= 10 ? '#ff9800' : '#f44336',
                              color: 'white',
                              fontWeight: 600,
                            }}
                          />
                        </TableCell>
                        <TableCell align="center">
                          <Typography 
                            variant="body2" 
                            sx={{ 
                              color: isWinning ? '#4caf50' : '#f44336',
                              fontWeight: 600 
                            }}
                          >
                            {isWinning ? `领先${Math.abs(gap)}位` : `落后${Math.abs(gap)}位`}
                          </Typography>
                        </TableCell>
                        <TableCell align="center">
                          {kw.trend === 'up' && <TrendingUp sx={{ color: '#4caf50', fontSize: 20 }} />}
                          {kw.trend === 'down' && <TrendingDown sx={{ color: '#f44336', fontSize: 20 }} />}
                          {kw.trend === 'stable' && <Remove sx={{ color: '#9ca3af', fontSize: 20 }} />}
                        </TableCell>
                        <TableCell align="center">
                          <Chip
                            label={isWinning ? '保持优势' : '需要优化'}
                            size="small"
                            variant="outlined"
                            sx={{
                              borderColor: isWinning ? '#4caf50' : '#ff9800',
                              color: isWinning ? '#4caf50' : '#ff9800',
                            }}
                          />
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </TableContainer>
          </Box>
        </ChartCard>
      </Box>
    );
  };

  // 渲染数据概览
  const renderDataOverview = () => (
    <Box>
      {/* 总体统计卡片 */}
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard>
            <Box sx={{ p: 2.5 }}>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" variant="body2" gutterBottom>
                    监控关键词
                  </Typography>
                  <Typography variant="h4" fontWeight="bold">
                    {keywordRankingData.length}
                  </Typography>
                  <Box display="flex" alignItems="center" mt={1}>
                    <TrendingUp sx={{ color: '#4caf50', fontSize: 20, mr: 0.5 }} />
                    <Typography variant="body2" sx={{ color: '#4caf50' }}>
                      +12.5%
                    </Typography>
                  </Box>
                </Box>
                <Box sx={{ 
                  width: 40, 
                  height: 40, 
                  display: 'flex', 
                  alignItems: 'center', 
                  justifyContent: 'center',
                  bgcolor: '#ffffff',
                  borderRadius: 0,
                }}>
                  <Analytics sx={{ color: '#1976d2', fontSize: 24 }} />
                </Box>
              </Box>
            </Box>
          </MetricCard>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard>
            <Box sx={{ p: 2.5 }}>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" variant="body2" gutterBottom>
                    平均排名
                  </Typography>
                  <Typography variant="h4" fontWeight="bold">
                    6.8
                  </Typography>
                  <Box display="flex" alignItems="center" mt={1}>
                    <ArrowUpward sx={{ color: '#4caf50', fontSize: 20, mr: 0.5 }} />
                    <Typography variant="body2" sx={{ color: '#4caf50' }}>
                      提升1.2位
                    </Typography>
                  </Box>
                </Box>
                <Box sx={{ 
                  width: 40, 
                  height: 40, 
                  display: 'flex', 
                  alignItems: 'center', 
                  justifyContent: 'center',
                  bgcolor: '#ffffff',
                  borderRadius: 0,
                }}>
                  <Leaderboard sx={{ color: '#4caf50', fontSize: 24 }} />
                </Box>
              </Box>
            </Box>
          </MetricCard>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard>
            <Box sx={{ p: 2.5 }}>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" variant="body2" gutterBottom>
                    Top3关键词
                  </Typography>
                  <Typography variant="h4" fontWeight="bold">
                    {rankDistribution.top3}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" mt={1}>
                    占比 {Math.round(rankDistribution.top3 / keywordRankingData.length * 100)}%
                  </Typography>
                </Box>
                <Box sx={{ 
                  width: 40, 
                  height: 40, 
                  display: 'flex', 
                  alignItems: 'center', 
                  justifyContent: 'center',
                  bgcolor: '#ffffff',
                  borderRadius: 0,
                }}>
                  <EmojiEvents sx={{ color: '#ff9800', fontSize: 24 }} />
                </Box>
              </Box>
            </Box>
          </MetricCard>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard>
            <Box sx={{ p: 2.5 }}>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" variant="body2" gutterBottom>
                    优化机会
                  </Typography>
                  <Typography variant="h4" fontWeight="bold">
                    {rankDistribution.beyond10}
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#ff9800', mt: 1 }}>
                    需重点关注
                  </Typography>
                </Box>
                <Box sx={{ 
                  width: 40, 
                  height: 40, 
                  display: 'flex', 
                  alignItems: 'center', 
                  justifyContent: 'center',
                  bgcolor: '#ffffff',
                  borderRadius: 0,
                }}>
                  <Warning sx={{ color: '#f44336', fontSize: 24 }} />
                </Box>
              </Box>
            </Box>
          </MetricCard>
        </Grid>
      </Grid>

      {/* 排名趋势和分布 */}
      <ChartCard sx={{ mb: 3 }}>
        <Box sx={{ p: 2 }}>
          <Typography variant="h6" gutterBottom fontWeight="600">
            关键词排名趋势与分布
          </Typography>
          
          <Grid container spacing={4} sx={{ mt: 2 }}>
            {/* 左侧趋势数据 */}
            <Grid item xs={12} md={4}>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                {[
                  { label: 'Top 3 关键词', value: rankDistribution.top3, percentage: Math.round(rankDistribution.top3 / keywordRankingData.length * 100), color: '#4caf50', trend: '+2' },
                  { label: 'Top 5 关键词', value: rankDistribution.top5, percentage: Math.round(rankDistribution.top5 / keywordRankingData.length * 100), color: '#00bcd4', trend: '+3' },
                  { label: 'Top 10 关键词', value: rankDistribution.top10, percentage: Math.round(rankDistribution.top10 / keywordRankingData.length * 100), color: '#ff9800', trend: '+1' },
                  { label: '10名后关键词', value: rankDistribution.beyond10, percentage: Math.round(rankDistribution.beyond10 / keywordRankingData.length * 100), color: '#f44336', trend: '-1' },
                ].map((item, index) => (
                  <Box key={index} sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Box sx={{ width: 4, height: 40, bgcolor: item.color }} />
                    <Box sx={{ flex: 1 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Typography variant="body2" sx={{ fontWeight: 500 }}>{item.label}</Typography>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Typography variant="h6" sx={{ fontWeight: 600 }}>{item.value}</Typography>
                          <Typography variant="caption" color="text.secondary">({item.percentage}%)</Typography>
                        </Box>
                      </Box>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 0.5 }}>
                        <LinearProgress 
                          variant="determinate" 
                          value={item.percentage}
                          sx={{ 
                            flex: 1,
                            height: 4, 
                            borderRadius: 0,
                            bgcolor: '#e0e0e0',
                            '& .MuiLinearProgress-bar': {
                              bgcolor: item.color,
                              borderRadius: 0,
                            }
                          }}
                        />
                        <Typography variant="caption" sx={{ color: parseInt(item.trend) > 0 ? '#4caf50' : '#f44336' }}>
                          {item.trend}
                        </Typography>
                      </Box>
                    </Box>
                  </Box>
                ))}
              </Box>
            </Grid>

            {/* 中间趋势图 */}
            <Grid item xs={12} md={4}>
              <Box sx={{ height: 200, position: 'relative', border: '1px solid #f0f0f0', p: 2 }}>
                <Typography variant="subtitle2" gutterBottom>7天排名趋势</Typography>
                {/* 简单的趋势线 */}
                <Box sx={{ position: 'relative', height: 150, mt: 2 }}>
                  <svg width="100%" height="100%">
                    <polyline
                      points="10,80 50,70 90,75 130,60 170,55 210,50 250,45"
                      fill="none"
                      stroke="#4caf50"
                      strokeWidth="2"
                    />
                    <polyline
                      points="10,100 50,95 90,90 130,85 170,88 210,80 250,75"
                      fill="none"
                      stroke="#ff9800"
                      strokeWidth="2"
                      strokeDasharray="5,5"
                    />
                  </svg>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-around', mt: 1 }}>
                  <Typography variant="caption" color="text.secondary">周一</Typography>
                  <Typography variant="caption" color="text.secondary">周三</Typography>
                  <Typography variant="caption" color="text.secondary">周五</Typography>
                  <Typography variant="caption" color="text.secondary">周日</Typography>
                </Box>
              </Box>
            </Grid>

            {/* 右侧关键指标 */}
            <Grid item xs={12} md={4}>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <Box sx={{ p: 2, border: '1px solid #f0f0f0' }}>
                  <Typography variant="caption" color="text.secondary">整体表现</Typography>
                  <Box sx={{ display: 'flex', alignItems: 'baseline', gap: 1, mt: 1 }}>
                    <Typography variant="h4" fontWeight="600" sx={{ color: '#4caf50' }}>85</Typography>
                    <Typography variant="body2" color="text.secondary">分</Typography>
                  </Box>
                  <Typography variant="caption" color="text.secondary">较上周提升12分</Typography>
                </Box>
                
                <Box sx={{ p: 2, border: '1px solid #f0f0f0' }}>
                  <Typography variant="caption" color="text.secondary">最佳平台</Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 1 }}>
                    <Typography variant="h5">🤖</Typography>
                    <Typography variant="h6" fontWeight="600">ChatGPT</Typography>
                  </Box>
                  <Typography variant="caption" color="text.secondary">平均排名 5.8</Typography>
                </Box>
                
                <Box sx={{ p: 2, border: '1px solid #f0f0f0' }}>
                  <Typography variant="caption" color="text.secondary">优化建议</Typography>
                  <Typography variant="body2" sx={{ mt: 1 }}>
                    有{rankDistribution.beyond10}个关键词排名10名后，建议重点优化内容质量
                  </Typography>
                </Box>
              </Box>
            </Grid>
          </Grid>
        </Box>
      </ChartCard>
    </Box>
  );

  return (
    <StyledContainer>
      <HeaderSection>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Typography variant="h5" fontWeight="bold">
            GEO监控中心
          </Typography>
          <Box display="flex" gap={2}>
            <FormControl size="small" sx={{ minWidth: 150 }}>
              <InputLabel>选择项目</InputLabel>
              <Select
                value={selectedProject}
                onChange={(e) => setSelectedProject(e.target.value)}
                label="选择项目"
              >
                <MenuItem value="all">所有项目</MenuItem>
                <MenuItem value="proj1">AI智能助手</MenuItem>
                <MenuItem value="proj2">数字化营销平台</MenuItem>
                <MenuItem value="proj3">企业管理系统</MenuItem>
              </Select>
            </FormControl>
            <ToggleButtonGroup
              value={timeRange}
              exclusive
              onChange={(e, newValue) => newValue && setTimeRange(newValue)}
              size="small"
            >
              <ToggleButton value="24h">24小时</ToggleButton>
              <ToggleButton value="7d">7天</ToggleButton>
              <ToggleButton value="30d">30天</ToggleButton>
              <ToggleButton value="90d">90天</ToggleButton>
            </ToggleButtonGroup>
          </Box>
        </Box>

        <Tabs
          value={currentTab}
          onChange={(e, newValue) => setCurrentTab(newValue)}
          variant="scrollable"
          scrollButtons="auto"
        >
          <Tab label="数据概览" icon={<Analytics />} iconPosition="start" />
          <Tab label="排名分析" icon={<Leaderboard />} iconPosition="start" />
          <Tab label="AI平台分析" icon={<Psychology />} iconPosition="start" />
          <Tab label="竞争对手" icon={<Groups />} iconPosition="start" />
        </Tabs>
      </HeaderSection>

      <ContentSection>
        {loading ? (
          <Box display="flex" justifyContent="center" alignItems="center" height="100%">
            <CircularProgress />
          </Box>
        ) : (
          <>
            {currentTab === 0 && renderDataOverview()}
            {currentTab === 1 && renderRankingAnalysis()}
            {currentTab === 2 && renderAIPlatformAnalysis()}
            {currentTab === 3 && renderCompetitorAnalysis()}
          </>
        )}
      </ContentSection>
    </StyledContainer>
  );
};

export default GeoMonitoringUpdated;