import React, { useState, useRef, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  TextField,
  IconButton,
  Button,
  Card,
  Grid,
  Avatar,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Fade,
  Slide,
  CircularProgress,
  Divider,
  Alert,
  Tooltip,
  LinearProgress,
  Badge,
  Stack,
  Collapse,
} from '@mui/material';
import {
  Send,
  AutoAwesomeMotion,
  Dashboard,
  TrendingUp,
  LocationOn,
  Analytics,
  Timeline,
  CompareArrows,
  Psychology,
  SmartToy,
  Lightbulb,
  Speed,
  Public,
  Assessment,
  BarChart,
  PieChart,
  ShowChart,
  TableChart,
  MapOutlined,
  CloudDownload,
  Refresh,
  History,
  BookmarkBorder,
  ContentCopy,
  ThumbUp,
  ThumbDown,
  MoreVert,
  AttachFile,
  Mic,
  MicOff,
  Image,
  KeyboardVoice,
  Stop,
  PlayArrow,
} from '@mui/icons-material';
import { styled, keyframes } from '@mui/material/styles';

const fadeIn = keyframes`
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
`;

const pulse = keyframes`
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
`;

const MainContainer = styled(Box)(({ theme }) => ({
  height: '100vh',
  display: 'flex',
  flexDirection: 'column',
  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
  position: 'relative',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: 'url("data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.05"%3E%3Cpath d="M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")',
    opacity: 0.1,
  },
}));

const ChatContainer = styled(Paper)(({ theme }) => ({
  flex: 1,
  margin: theme.spacing(2),
  borderRadius: theme.spacing(3),
  display: 'flex',
  flexDirection: 'column',
  background: 'rgba(255, 255, 255, 0.98)',
  backdropFilter: 'blur(20px)',
  boxShadow: '0 20px 60px rgba(0, 0, 0, 0.15)',
  overflow: 'hidden',
}));

const HeaderBar = styled(Box)(({ theme }) => ({
  padding: theme.spacing(2, 3),
  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
  color: '#ffffff',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  boxShadow: '0 2px 10px rgba(0, 0, 0, 0.1)',
}));

const MessagesContainer = styled(Box)(({ theme }) => ({
  flex: 1,
  overflowY: 'auto',
  padding: theme.spacing(3),
  display: 'flex',
  flexDirection: 'column',
  gap: theme.spacing(2),
  '&::-webkit-scrollbar': {
    width: '8px',
  },
  '&::-webkit-scrollbar-track': {
    background: '#f1f1f1',
    borderRadius: '10px',
  },
  '&::-webkit-scrollbar-thumb': {
    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    borderRadius: '10px',
  },
}));

const InputContainer = styled(Box)(({ theme }) => ({
  padding: theme.spacing(2, 3),
  borderTop: '1px solid rgba(0, 0, 0, 0.08)',
  background: 'rgba(255, 255, 255, 0.98)',
}));

const MessageBubble = styled(Box)(({ theme, isUser }) => ({
  maxWidth: isUser ? '70%' : '85%',
  alignSelf: isUser ? 'flex-end' : 'flex-start',
  animation: `${fadeIn} 0.3s ease-out`,
}));

const BubbleContent = styled(Paper)(({ theme, isUser }) => ({
  padding: theme.spacing(2, 2.5),
  borderRadius: isUser 
    ? '20px 20px 4px 20px' 
    : '20px 20px 20px 4px',
  background: isUser 
    ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
    : '#ffffff',
  color: isUser ? '#ffffff' : theme.palette.text.primary,
  boxShadow: isUser 
    ? '0 4px 15px rgba(102, 126, 234, 0.3)'
    : '0 4px 15px rgba(0, 0, 0, 0.08)',
  border: isUser ? 'none' : '1px solid rgba(0, 0, 0, 0.05)',
  wordBreak: 'break-word',
}));

const TemplateCard = styled(Card)(({ theme }) => ({
  padding: theme.spacing(2),
  borderRadius: theme.spacing(2),
  background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
  cursor: 'pointer',
  transition: 'all 0.3s ease',
  border: '2px solid transparent',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 10px 30px rgba(0, 0, 0, 0.15)',
    borderColor: '#667eea',
    animation: `${pulse} 1s infinite`,
  },
}));

const QuickActionButton = styled(Button)(({ theme }) => ({
  borderRadius: theme.spacing(3),
  padding: theme.spacing(1, 2),
  textTransform: 'none',
  background: 'rgba(102, 126, 234, 0.1)',
  color: '#667eea',
  border: '1px solid rgba(102, 126, 234, 0.3)',
  '&:hover': {
    background: 'rgba(102, 126, 234, 0.2)',
    transform: 'scale(1.05)',
  },
}));

const StyledTextField = styled(TextField)(({ theme }) => ({
  '& .MuiOutlinedInput-root': {
    borderRadius: theme.spacing(3),
    background: '#f8f9fa',
    '&:hover fieldset': {
      borderColor: '#667eea',
    },
    '&.Mui-focused fieldset': {
      borderColor: '#667eea',
      borderWidth: '2px',
    },
  },
}));

const AnimatedIcon = styled(Box)(({ theme }) => ({
  animation: `${pulse} 2s infinite`,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
}));

const monitoringTemplates = [
  {
    id: 'traffic',
    title: '流量分析仪表盘',
    description: '实时流量趋势、来源分布、用户行为分析',
    icon: <TrendingUp />,
    prompt: '生成流量分析报告，包括今日流量、来源分布和用户行为',
    color: '#4caf50',
  },
  {
    id: 'ranking',
    title: 'SEO排名监控',
    description: '关键词排名变化、竞争对手分析、搜索趋势',
    icon: <Assessment />,
    prompt: '显示关键词排名情况和变化趋势',
    color: '#2196f3',
  },
  {
    id: 'geo',
    title: '地域分布地图',
    description: '全球访问分布、地区热力图、城市排名',
    icon: <Public />,
    prompt: '展示全球用户地理分布情况',
    color: '#ff9800',
  },
  {
    id: 'competitor',
    title: '竞争对手分析',
    description: '竞品流量对比、市场份额、增长趋势',
    icon: <CompareArrows />,
    prompt: '分析主要竞争对手的表现和市场份额',
    color: '#9c27b0',
  },
  {
    id: 'ai-performance',
    title: 'AI性能监控',
    description: 'AI服务响应时间、准确率、用户满意度',
    icon: <Psychology />,
    prompt: '监控AI服务的性能指标',
    color: '#e91e63',
  },
  {
    id: 'realtime',
    title: '实时数据流',
    description: '实时访问监控、异常检测、告警通知',
    icon: <Speed />,
    prompt: '显示实时数据流和异常检测',
    color: '#00bcd4',
  },
];

const suggestionPrompts = [
  "今天的网站流量如何？",
  "显示本周关键词排名变化",
  "分析竞争对手最新动态",
  "生成月度SEO报告",
  "展示用户地理分布热力图",
  "AI内容生成效率分析",
  "网站性能优化建议",
  "用户行为路径分析",
];

function GeoMonitoringChatBI() {
  const [messages, setMessages] = useState([
    {
      id: 1,
      type: 'bot',
      content: '您好！我是GEO监控智能助手 🤖',
      subContent: '我可以帮您生成各种监控仪表盘、分析报告和数据洞察。您可以直接告诉我您的需求，或选择下方的模板快速开始。',
      timestamp: new Date(),
    },
  ]);
  const [inputValue, setInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [showTemplates, setShowTemplates] = useState(true);
  const [generatingChart, setGeneratingChart] = useState(false);
  const messagesEndRef = useRef(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = async (customPrompt = null) => {
    const prompt = customPrompt || inputValue;
    if (!prompt.trim()) return;

    const userMessage = {
      id: messages.length + 1,
      type: 'user',
      content: prompt,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsTyping(true);
    setShowTemplates(false);

    setTimeout(() => {
      const botResponse = generateBotResponse(prompt);
      setMessages(prev => [...prev, botResponse]);
      setIsTyping(false);
      setGeneratingChart(true);
      
      setTimeout(() => {
        setGeneratingChart(false);
      }, 1500);
    }, 1500);
  };

  const generateBotResponse = (prompt) => {
    const responses = {
      traffic: {
        content: '正在为您生成流量分析仪表盘...',
        chart: {
          type: 'traffic',
          data: {
            todayVisits: 15432,
            change: '+12.5%',
            sources: [
              { name: '自然搜索', value: 45 },
              { name: '直接访问', value: 28 },
              { name: '社交媒体', value: 15 },
              { name: '推荐链接', value: 12 },
            ],
          },
        },
      },
      ranking: {
        content: '正在加载SEO排名数据...',
        chart: {
          type: 'ranking',
          data: {
            topKeywords: [
              { keyword: 'AI内容生成', position: 2, change: 1 },
              { keyword: 'SEO优化工具', position: 3, change: -1 },
              { keyword: '企业数字化', position: 5, change: 2 },
              { keyword: '智能营销平台', position: 4, change: 0 },
            ],
          },
        },
      },
      default: {
        content: '我理解您的需求，正在为您准备相关数据...',
        chart: {
          type: 'custom',
          data: {},
        },
      },
    };

    let responseType = 'default';
    if (prompt.includes('流量')) responseType = 'traffic';
    else if (prompt.includes('排名') || prompt.includes('关键词')) responseType = 'ranking';

    return {
      id: messages.length + 2,
      type: 'bot',
      content: responses[responseType].content,
      chart: responses[responseType].chart,
      timestamp: new Date(),
    };
  };

  const handleTemplateClick = (template) => {
    setSelectedTemplate(template);
    handleSendMessage(template.prompt);
  };

  const handleVoiceInput = () => {
    setIsListening(!isListening);
  };

  const renderChart = (chart) => {
    if (!chart) return null;

    switch (chart.type) {
      case 'traffic':
        return (
          <Card sx={{ mt: 2, p: 2, borderRadius: 2, background: 'linear-gradient(135deg, #667eea15 0%, #764ba215 100%)' }}>
            <Typography variant="h6" gutterBottom sx={{ color: '#667eea', fontWeight: 600 }}>
              📊 今日流量概览
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={6}>
                <Box sx={{ textAlign: 'center', p: 2 }}>
                  <Typography variant="h3" sx={{ color: '#667eea', fontWeight: 700 }}>
                    {chart.data.todayVisits?.toLocaleString()}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    今日访问量
                  </Typography>
                  <Chip 
                    label={chart.data.change} 
                    color="success" 
                    size="small" 
                    sx={{ mt: 1 }}
                  />
                </Box>
              </Grid>
              <Grid item xs={6}>
                <Box sx={{ p: 1 }}>
                  <Typography variant="body2" gutterBottom>流量来源</Typography>
                  {chart.data.sources?.map((source, index) => (
                    <Box key={index} sx={{ mb: 1 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
                        <Typography variant="caption">{source.name}</Typography>
                        <Typography variant="caption" fontWeight="bold">{source.value}%</Typography>
                      </Box>
                      <LinearProgress 
                        variant="determinate" 
                        value={source.value} 
                        sx={{ 
                          height: 6, 
                          borderRadius: 3,
                          backgroundColor: '#e0e0e0',
                          '& .MuiLinearProgress-bar': {
                            backgroundColor: '#667eea',
                          },
                        }}
                      />
                    </Box>
                  ))}
                </Box>
              </Grid>
            </Grid>
          </Card>
        );
      
      case 'ranking':
        return (
          <Card sx={{ mt: 2, p: 2, borderRadius: 2, background: 'linear-gradient(135deg, #2196f315 0%, #00bcd415 100%)' }}>
            <Typography variant="h6" gutterBottom sx={{ color: '#2196f3', fontWeight: 600 }}>
              🏆 关键词排名
            </Typography>
            <List dense>
              {chart.data.topKeywords?.map((item, index) => (
                <ListItem key={index} sx={{ px: 0 }}>
                  <ListItemAvatar>
                    <Avatar sx={{ 
                      bgcolor: item.position <= 3 ? '#4caf50' : '#ff9800',
                      width: 32,
                      height: 32,
                      fontSize: '0.875rem',
                    }}>
                      #{item.position}
                    </Avatar>
                  </ListItemAvatar>
                  <ListItemText 
                    primary={item.keyword}
                    secondary={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                        {item.change > 0 && <TrendingUp sx={{ fontSize: 16, color: '#4caf50' }} />}
                        {item.change < 0 && <TrendingUp sx={{ fontSize: 16, color: '#f44336', transform: 'rotate(180deg)' }} />}
                        {item.change !== 0 && (
                          <Typography variant="caption" color={item.change > 0 ? 'success.main' : 'error.main'}>
                            {Math.abs(item.change)} 位
                          </Typography>
                        )}
                        {item.change === 0 && (
                          <Typography variant="caption" color="text.secondary">
                            保持不变
                          </Typography>
                        )}
                      </Box>
                    }
                  />
                </ListItem>
              ))}
            </List>
          </Card>
        );

      default:
        return (
          <Card sx={{ mt: 2, p: 3, borderRadius: 2, textAlign: 'center', background: '#f8f9fa' }}>
            <CircularProgress sx={{ color: '#667eea' }} />
            <Typography variant="body2" sx={{ mt: 2 }} color="text.secondary">
              正在生成自定义图表...
            </Typography>
          </Card>
        );
    }
  };

  return (
    <MainContainer>
      <ChatContainer>
        <HeaderBar>
          <Box display="flex" alignItems="center" gap={2}>
            <AnimatedIcon>
              <Avatar sx={{ 
                bgcolor: 'rgba(255, 255, 255, 0.2)', 
                backdropFilter: 'blur(10px)',
                border: '2px solid rgba(255, 255, 255, 0.3)',
              }}>
                <SmartToy sx={{ color: '#ffffff' }} />
              </Avatar>
            </AnimatedIcon>
            <Box>
              <Typography variant="h6" fontWeight="bold">
                GEO监控 ChatBI
              </Typography>
              <Typography variant="caption" sx={{ opacity: 0.9 }}>
                智能对话式数据分析平台
              </Typography>
            </Box>
          </Box>
          <Box display="flex" gap={1}>
            <Tooltip title="查看历史">
              <IconButton size="small" sx={{ color: '#ffffff' }}>
                <History />
              </IconButton>
            </Tooltip>
            <Tooltip title="收藏的图表">
              <IconButton size="small" sx={{ color: '#ffffff' }}>
                <BookmarkBorder />
              </IconButton>
            </Tooltip>
            <Tooltip title="刷新数据">
              <IconButton size="small" sx={{ color: '#ffffff' }}>
                <Refresh />
              </IconButton>
            </Tooltip>
          </Box>
        </HeaderBar>

        <MessagesContainer>
          {messages.map((message) => (
            <MessageBubble key={message.id} isUser={message.type === 'user'}>
              {message.type === 'bot' && (
                <Box display="flex" alignItems="flex-start" gap={1} mb={1}>
                  <Avatar sx={{ 
                    width: 32, 
                    height: 32, 
                    bgcolor: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  }}>
                    <SmartToy sx={{ fontSize: 20 }} />
                  </Avatar>
                  <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5 }}>
                    AI助手
                  </Typography>
                </Box>
              )}
              <BubbleContent isUser={message.type === 'user'} elevation={0}>
                <Typography variant="body1">
                  {message.content}
                </Typography>
                {message.subContent && (
                  <Typography variant="body2" sx={{ mt: 1, opacity: 0.9 }}>
                    {message.subContent}
                  </Typography>
                )}
                {message.chart && !generatingChart && renderChart(message.chart)}
              </BubbleContent>
              {message.type === 'bot' && message.chart && (
                <Box display="flex" gap={1} mt={1} ml={5}>
                  <IconButton size="small">
                    <ContentCopy fontSize="small" />
                  </IconButton>
                  <IconButton size="small">
                    <CloudDownload fontSize="small" />
                  </IconButton>
                  <IconButton size="small">
                    <ThumbUp fontSize="small" />
                  </IconButton>
                </Box>
              )}
            </MessageBubble>
          ))}

          {isTyping && (
            <Box display="flex" alignItems="center" gap={1}>
              <Avatar sx={{ 
                width: 32, 
                height: 32, 
                bgcolor: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              }}>
                <SmartToy sx={{ fontSize: 20 }} />
              </Avatar>
              <Box sx={{ display: 'flex', gap: 0.5 }}>
                <Box sx={{ 
                  width: 8, 
                  height: 8, 
                  borderRadius: '50%', 
                  bgcolor: '#667eea',
                  animation: 'pulse 1.4s infinite',
                  animationDelay: '0s',
                }} />
                <Box sx={{ 
                  width: 8, 
                  height: 8, 
                  borderRadius: '50%', 
                  bgcolor: '#667eea',
                  animation: 'pulse 1.4s infinite',
                  animationDelay: '0.2s',
                }} />
                <Box sx={{ 
                  width: 8, 
                  height: 8, 
                  borderRadius: '50%', 
                  bgcolor: '#667eea',
                  animation: 'pulse 1.4s infinite',
                  animationDelay: '0.4s',
                }} />
              </Box>
            </Box>
          )}

          <div ref={messagesEndRef} />
        </MessagesContainer>

        <Collapse in={showTemplates}>
          <Box sx={{ px: 3, pb: 2 }}>
            <Typography variant="subtitle2" color="text.secondary" gutterBottom>
              🎯 快速开始 - 选择一个监控模板
            </Typography>
            <Grid container spacing={2}>
              {monitoringTemplates.map((template) => (
                <Grid item xs={12} sm={6} md={4} key={template.id}>
                  <TemplateCard onClick={() => handleTemplateClick(template)}>
                    <Box display="flex" alignItems="center" gap={1.5}>
                      <Avatar sx={{ 
                        bgcolor: `${template.color}20`,
                        color: template.color,
                        width: 40,
                        height: 40,
                      }}>
                        {template.icon}
                      </Avatar>
                      <Box flex={1}>
                        <Typography variant="subtitle2" fontWeight="600">
                          {template.title}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {template.description}
                        </Typography>
                      </Box>
                    </Box>
                  </TemplateCard>
                </Grid>
              ))}
            </Grid>
          </Box>
        </Collapse>

        <Box sx={{ px: 3, pb: 1 }}>
          <Box display="flex" gap={1} flexWrap="wrap">
            {suggestionPrompts.slice(0, 4).map((prompt, index) => (
              <QuickActionButton
                key={index}
                size="small"
                startIcon={<Lightbulb fontSize="small" />}
                onClick={() => handleSendMessage(prompt)}
              >
                {prompt}
              </QuickActionButton>
            ))}
          </Box>
        </Box>

        <InputContainer>
          <Box display="flex" gap={1} alignItems="center">
            <IconButton size="small">
              <AttachFile />
            </IconButton>
            <StyledTextField
              fullWidth
              variant="outlined"
              placeholder="输入您的问题或需求，例如：'显示本周流量趋势' 或 '分析竞争对手表现'"
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyPress={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  handleSendMessage();
                }
              }}
              size="small"
              multiline
              maxRows={3}
              InputProps={{
                endAdornment: (
                  <Box display="flex" gap={0.5}>
                    <IconButton 
                      size="small" 
                      onClick={handleVoiceInput}
                      sx={{ color: isListening ? '#f44336' : '#667eea' }}
                    >
                      {isListening ? <MicOff /> : <Mic />}
                    </IconButton>
                    <IconButton
                      size="small"
                      onClick={() => handleSendMessage()}
                      disabled={!inputValue.trim()}
                      sx={{ 
                        background: inputValue.trim() ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' : 'transparent',
                        color: inputValue.trim() ? '#ffffff' : '#999',
                        '&:hover': {
                          background: inputValue.trim() ? 'linear-gradient(135deg, #764ba2 0%, #667eea 100%)' : 'transparent',
                        },
                      }}
                    >
                      <Send />
                    </IconButton>
                  </Box>
                ),
              }}
            />
          </Box>
          <Box display="flex" justifyContent="space-between" alignItems="center" mt={1}>
            <Typography variant="caption" color="text.secondary">
              按 Enter 发送，Shift + Enter 换行
            </Typography>
            <Box display="flex" gap={1}>
              <Chip 
                label="GPT-4 Turbo" 
                size="small" 
                icon={<Psychology />}
                sx={{ 
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  color: '#ffffff',
                }}
              />
              <Chip 
                label="实时数据" 
                size="small" 
                color="success"
                icon={<Speed />}
              />
            </Box>
          </Box>
        </InputContainer>
      </ChatContainer>
    </MainContainer>
  );
}

export default GeoMonitoringChatBI;