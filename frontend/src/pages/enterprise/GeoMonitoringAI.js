// GeoMonitoringAI Component - Updated version with database template system
import React, { useState, useRef, useEffect } from 'react';
import geoTemplateService from '../../services/geoTemplateService';
import aiService from '../../services/aiService';
import apiService from '../../services/api';
import {
  Box,
  Typography,
  Paper,
  TextField,
  IconButton,
  Button,
  Avatar,
  Chip,
  Divider,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Card,
  CardContent,
  Grid,
  ToggleButton,
  ToggleButtonGroup,
  InputAdornment,
  Tooltip,
  Badge,
  Tabs,
  Tab,
  CircularProgress,
  FormControl,
  Select,
  MenuItem,
} from '@mui/material';
import {
  Send,
  Psychology,
  Add,
  History,
  Description,
  AutoFixHigh,
  Clear,
  ContentCopy,
  ThumbUp,
  ThumbDown,
  Refresh,
  Settings,
  SmartToy,
  AttachFile,
  Mic,
  Stop,
  PlayArrow,
  CheckCircle,
  Code,
  Preview,
  Download,
  Chat,
  AutoAwesome,
  Fullscreen,
  FullscreenExit,
  Star,
  StarBorder,
  ChevronRight,
  Close,
  ArrowForward,
  Error,
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';

const StyledContainer = styled(Box)(({ theme }) => ({
  width: '100%',
  height: '100%',
  display: 'flex',
  backgroundColor: '#ffffff',
  position: 'relative',
}));

const LeftPanel = styled(Box)(({ theme }) => ({
  flex: '1 1 60%',
  display: 'flex',
  flexDirection: 'column',
  borderRight: '1px solid #e5e7eb',
  minWidth: '400px',
}));

const RightPanel = styled(Box)(({ theme }) => ({
  flex: '1 1 40%',
  display: 'flex',
  flexDirection: 'column',
  backgroundColor: '#f9fafb',
  minWidth: '300px',
}));

const HeaderSection = styled(Box)(({ theme }) => ({
  padding: theme.spacing(2, 3),
  backgroundColor: '#ffffff',
  borderBottom: '1px solid #e5e7eb',
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
}));

const ChatContainer = styled(Box)(({ theme }) => ({
  flex: '1 1 auto',
  overflowY: 'auto',
  padding: theme.spacing(3),
  backgroundColor: '#f9fafb',
  '&::-webkit-scrollbar': {
    width: '6px',
  },
  '&::-webkit-scrollbar-track': {
    background: 'transparent',
  },
  '&::-webkit-scrollbar-thumb': {
    background: '#d1d5db',
    borderRadius: '3px',
  },
}));

const MessageBubble = styled(Paper)(({ theme, isUser }) => ({
  padding: theme.spacing(2),
  marginBottom: theme.spacing(2),
  maxWidth: '85%',
  alignSelf: isUser ? 'flex-end' : 'flex-start',
  backgroundColor: isUser ? '#3b82f6' : '#ffffff',
  color: isUser ? '#ffffff' : '#1f2937',
  borderRadius: '12px',
  boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
  position: 'relative',
}));

const InputSection = styled(Box)(({ theme }) => ({
  padding: theme.spacing(2, 3),
  backgroundColor: '#ffffff',
  borderTop: '1px solid #e5e7eb',
}));

const TemplateCard = styled(Card)(({ theme }) => ({
  cursor: 'pointer',
  transition: 'all 0.2s ease',
  border: '1px solid #e5e7eb',
  borderRadius: '12px',
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  '&:hover': {
    borderColor: '#3b82f6',
    boxShadow: '0 8px 25px rgba(59, 130, 246, 0.15)',
    transform: 'translateY(-4px)',
  },
}));

const PreviewContainer = styled(Box)(({ theme }) => ({
  flex: '1 1 auto',
  padding: theme.spacing(2),
  overflowY: 'auto',
  '&::-webkit-scrollbar': {
    width: '6px',
  },
  '&::-webkit-scrollbar-track': {
    background: 'transparent',
  },
  '&::-webkit-scrollbar-thumb': {
    background: '#d1d5db',
    borderRadius: '3px',
  },
}));

// 辅助函数：根据索引生成不同的渐变色
const getTemplateGradient = (index) => {
  const gradients = [
    '#667eea, #764ba2', // 紫蓝渐变
    '#f093fb, #f5576c', // 粉红渐变
    '#4facfe, #00f2fe', // 蓝青渐变
    '#43e97b, #38f9d7', // 绿青渐变
    '#fa709a, #fee140', // 粉黄渐变
    '#a8edea, #fed6e3', // 青粉渐变
    '#ff9a9e, #fecfef', // 粉色渐变
    '#a18cd1, #fbc2eb', // 紫粉渐变
  ];
  return gradients[index % gradients.length];
};

function GeoMonitoringAI() {
  console.log('GeoMonitoringAI component loaded - new version with database template system');
  const [messages, setMessages] = useState([
    {
      id: 1,
      type: 'assistant',
      content: '您好！我是GEO监控助手，可以帮您分析SEO数据、监控网站排名、生成优化建议和HTML报告。请问有什么可以帮助您的吗？',
      timestamp: new Date(),
    },
  ]);
  const [inputText, setInputText] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [mode, setMode] = useState('dialog'); // 'dialog' or 'agent'
  const [templateDialog, setTemplateDialog] = useState(false);
  const [templateType, setTemplateType] = useState('');
  const [historyDialog, setHistoryDialog] = useState(false);
  const [generatedHTML, setGeneratedHTML] = useState('');
  const [htmlCode, setHtmlCode] = useState('');
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isFavorited, setIsFavorited] = useState(false);

  // 动态加载的模板数据
  const [standardTemplates, setStandardTemplates] = useState([]);
  const [customTemplates, setCustomTemplates] = useState([]);
  const [templatesLoading, setTemplatesLoading] = useState(false);
  const [templatesError, setTemplatesError] = useState('');

  // 项目相关状态
  const [projects, setProjects] = useState([]);
  const [selectedProject, setSelectedProject] = useState('');
  const [projectsLoading, setProjectsLoading] = useState(false);
  const [projectsError, setProjectsError] = useState('');

  // 对话记录相关状态
  const [currentConversationId, setCurrentConversationId] = useState(null);
  const [conversationHistory, setConversationHistory] = useState([]);
  const [conversationTitle, setConversationTitle] = useState('');

  const chatEndRef = useRef(null);
  const iframeRef = useRef(null);
  const fullscreenContainerRef = useRef(null);

  // 自动滚动到底部
  const scrollToBottom = () => {
    chatEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // 加载模板数据和项目列表
  useEffect(() => {
    loadTemplates();
    loadProjects();
  }, []);

  // 加载标准模板和用户自定义模板
  const loadTemplates = async () => {
    setTemplatesLoading(true);
    setTemplatesError('');

    try {
      // 并行加载标准模板和用户模板
      const [standardResponse, customResponse] = await Promise.all([
        geoTemplateService.getStandardTemplates(),
        geoTemplateService.getUserTemplates()
      ]);

      setStandardTemplates(standardResponse || []);
      setCustomTemplates(customResponse || []);

      console.log('模板加载成功:', {
        standard: standardResponse?.length || 0,
        custom: customResponse?.length || 0
      });

    } catch (error) {
      console.error('加载模板失败:', error);
      setTemplatesError('加载模板失败，请刷新页面重试');

      // 如果API调用失败，使用默认数据
      setStandardTemplates([
        { id: 1, template_key: 'seo_ranking_analysis', title: 'SEO排名分析报告', description: '生成关键词排名分析HTML报告', icon: 'Psychology' },
        { id: 2, template_key: 'competitor_comparison', title: '竞品对比报告', description: '生成竞争对手对比HTML页面', icon: 'AutoFixHigh' },
        { id: 3, template_key: 'content_optimization', title: '内容优化方案', description: '生成内容优化建议HTML文档', icon: 'Description' },
        { id: 4, template_key: 'technical_seo_audit', title: '技术SEO诊断', description: '生成技术问题诊断HTML报告', icon: 'Settings' },
      ]);
      setCustomTemplates([
        { id: 1, template_name: '月度SEO报告模板', description: '每月SEO数据汇总HTML模板', created_at: '2024-01-15T10:00:00Z' },
        { id: 2, template_name: '关键词研究模板', description: '关键词机会分析HTML报告', created_at: '2024-01-10T10:00:00Z' },
      ]);
    } finally {
      setTemplatesLoading(false);
    }
  };

  // 加载项目列表
  const loadProjects = async () => {
    setProjectsLoading(true);
    setProjectsError('');

    try {
      // 暂时使用默认数据，后续可以替换为真实API调用
      const defaultProjects = [
        {
          id: 'demo-1',
          project_name: '测试AI搜索监控项目',
          target_brand: 'AI助手',
          target_website: 'https://example.com',
          keywords: ['AI助手', '智能客服', '自动化'],
          status: 'active'
        },
        {
          id: 'demo-2',
          project_name: '物联网零代码平台',
          target_brand: '物联网',
          target_website: 'https://iot-platform.com',
          keywords: ['物联网', '零代码', '智能设备'],
          status: 'active'
        },
        {
          id: 'demo-3',
          project_name: '企业数字化转型',
          target_brand: '数字化',
          target_website: 'https://digital-transform.com',
          keywords: ['数字化转型', '企业管理', '云计算'],
          status: 'active'
        },
      ];

      // 模拟API延迟
      await new Promise(resolve => setTimeout(resolve, 500));

      setProjects(defaultProjects);
      // 如果没有选中项目且有项目列表，默认选择第一个
      if (!selectedProject && defaultProjects.length > 0) {
        setSelectedProject(defaultProjects[0].id);
      }
      console.log('项目加载成功:', defaultProjects.length);

      // 如果需要调用真实API，可以取消注释以下代码：
      /*
      const response = await fetch('/api/v1/monitoring/projects?page=1&size=100', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.success && data.data?.items) {
        setProjects(data.data.items);
        if (!selectedProject && data.data.items.length > 0) {
          setSelectedProject(data.data.items[0].id);
        }
        console.log('项目加载成功:', data.data.items.length);
      } else {
        throw new Error(data.message || '获取项目列表失败');
      }
      */

    } catch (error) {
      console.error('加载项目失败:', error);
      setProjectsError('加载项目失败，请刷新页面重试');
    } finally {
      setProjectsLoading(false);
    }
  };

  // 创建或获取对话记录
  const createOrGetConversation = async (userMessage, projectInfo) => {
    try {
      // 如果没有当前对话ID，创建新对话
      if (!currentConversationId) {
        const title = `GEO监控 - ${projectInfo?.project_name || '未知项目'} - ${userMessage.substring(0, 20)}...`;

        const data = await apiService.post('/geo-conversations', {
          title: title,
          template_parameters: {
            page: 'geo-monitoring',
            project_id: selectedProject,
            project_name: projectInfo?.project_name,
            target_brand: projectInfo?.target_brand,
            keywords: projectInfo?.keywords,
            target_website: projectInfo?.target_website
          }
        });

        const conversationId = data.id;
        setCurrentConversationId(conversationId);
        setConversationTitle(title);
        console.log('创建GEO监控对话记录成功:', conversationId);
        return conversationId;
      }

      return currentConversationId;
    } catch (error) {
      console.error('创建GEO监控对话记录异常:', error);
      return null;
    }
  };

  // 保存消息到对话记录
  const saveMessageToConversation = async (conversationId, messageType, content, requestId = null) => {
    try {
      const data = await apiService.post(`/geo-conversations/${conversationId}/messages`, {
        content: content,
        message_type: messageType,
        request_id: requestId
      });

      console.log('保存GEO监控消息成功:', data);
      return data;
    } catch (error) {
      console.error('保存GEO监控消息异常:', error);
      return null;
    }
  };

  // 更新iframe内容
  useEffect(() => {
    if (iframeRef.current && generatedHTML) {
      const iframeDoc = iframeRef.current.contentDocument || iframeRef.current.contentWindow.document;
      iframeDoc.open();
      iframeDoc.write(generatedHTML);
      iframeDoc.close();
    }
  }, [generatedHTML]);

  // 生成HTML内容（使用新的模板服务）
  const generateHTMLContent = async (templateTitle, templateKey = null, templateId = null, isCustom = false) => {
    try {
      // 生成默认变量
      const variables = geoTemplateService.generateDefaultVariables(templateTitle);

      let htmlContent;

      if (isCustom && templateId) {
        // 渲染用户自定义模板
        htmlContent = await geoTemplateService.renderUserTemplate(templateId, variables);
      } else if (templateKey) {
        // 渲染标准模板
        htmlContent = await geoTemplateService.renderStandardTemplate(templateKey, variables);
      } else {
        throw new Error('缺少模板标识');
      }

      setGeneratedHTML(htmlContent);
      setHtmlCode(htmlContent);

      console.log('HTML生成成功:', templateTitle);

    } catch (error) {
      console.error('生成HTML内容失败:', error);

      // 生成错误页面
      const errorHtml = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>生成失败</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 40px; text-align: center; background: #f5f5f5; }
        .error-container { background: white; border-radius: 10px; padding: 40px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); max-width: 600px; margin: 0 auto; }
        .error-icon { font-size: 4rem; color: #ef4444; margin-bottom: 20px; }
        .error-title { color: #1f2937; font-size: 1.5rem; margin-bottom: 10px; }
        .error-message { color: #6b7280; margin-bottom: 20px; }
        .retry-button { background: #3b82f6; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-icon">⚠️</div>
        <h2 class="error-title">HTML生成失败</h2>
        <p class="error-message">无法生成模板内容，请稍后重试。</p>
        <p class="error-message">错误信息: ${error.message}</p>
        <button class="retry-button" onclick="window.location.reload()">重新加载</button>
    </div>
</body>
</html>`;
      setGeneratedHTML(errorHtml);
      setHtmlCode(errorHtml);
    }
  };

  // 清理HTML内容，移除markdown代码块标记
  const cleanHtmlContent = (content) => {
    if (!content) return content;

    // 移除开头的```html标记
    let cleaned = content.replace(/^```html\s*\n?/gm, '');
    // 移除结尾的```标记
    cleaned = cleaned.replace(/\n?```\s*$/gm, '');
    // 移除中间可能出现的```标记
    cleaned = cleaned.replace(/```\s*\n?/gm, '');

    // 确保HTML内容以<!DOCTYPE html>开始
    if (!cleaned.trim().startsWith('<!DOCTYPE html>') && cleaned.includes('<html')) {
      cleaned = '<!DOCTYPE html>\n' + cleaned.trim();
    }

    return cleaned.trim();
  };

  // 设置HTML内容（用于Agent模式）
  const setHtmlContent = (htmlContent) => {
    if (htmlContent) {
      const cleanedContent = cleanHtmlContent(htmlContent);
      setGeneratedHTML(cleanedContent);
      setHtmlCode(cleanedContent);
    }
  };




  // 发送消息（使用流式响应）
  const handleSendMessage = async () => {
    if (!inputText.trim()) return;

    const userMessage = {
      id: Date.now(),
      type: 'user',
      content: inputText,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    const currentInput = inputText;
    setInputText('');
    setIsTyping(true);

    // 创建AI响应消息
    const aiMessageId = Date.now() + 1;
    const aiResponse = {
      id: aiMessageId,
      type: 'assistant',
      content: '',
      timestamp: new Date(),
    };
    setMessages(prev => [...prev, aiResponse]);

    try {
      // 准备对话历史（转换为AI服务需要的格式）
      const conversationHistory = messages.map(msg => ({
        role: msg.type === 'user' ? 'user' : 'assistant',
        content: msg.content,
        timestamp: msg.timestamp
      }));

      // 获取选中的项目信息
      const selectedProjectInfo = projects.find(p => p.id === selectedProject);

      // 创建或获取对话记录
      const conversationId = await createOrGetConversation(currentInput, selectedProjectInfo);

      // 保存用户消息到对话记录
      if (conversationId) {
        await saveMessageToConversation(conversationId, 'user', currentInput);
      }

      // 调用流式AI服务
      await aiService.sendMessageStream(
        currentInput,
        mode === 'dialog' ? 'chat' : 'agent',
        conversationHistory,
        {
          page: 'geo-monitoring',
          project_id: selectedProject,
          project_name: selectedProjectInfo?.project_name,
          target_brand: selectedProjectInfo?.target_brand,
          keywords: selectedProjectInfo?.keywords,
          target_website: selectedProjectInfo?.target_website,
          conversation_id: conversationId
        },
        // onChunk - 接收流式数据
        (chunk, type) => {
          if (type === 'message') {
            setMessages(prev => prev.map(msg =>
              msg.id === aiMessageId
                ? { ...msg, content: msg.content + chunk }
                : msg
            ));
          } else if (type === 'html') {
            // 如果是Agent模式且返回了HTML内容，更新HTML预览
            if (mode === 'agent') {
              setHtmlContent(chunk);
            }
          }
        },
        // onComplete - 完成时的回调
        async (finalResponse) => {
          setIsTyping(false);

          // 保存AI回复到对话记录
          if (conversationId && finalResponse) {
            // 获取AI消息的完整内容
            const aiMessage = messages.find(msg => msg.id === aiMessageId);
            const aiContent = aiMessage ? aiMessage.content : finalResponse;

            await saveMessageToConversation(conversationId, 'assistant', aiContent);
          }
        },
        // onError - 错误时的回调
        (error) => {
          setIsTyping(false);
          setMessages(prev => prev.map(msg =>
            msg.id === aiMessageId
              ? { ...msg, content: `抱歉，处理您的请求时遇到了问题：${error}\n\n请稍后重试或联系技术支持。` }
              : msg
          ));
        }
      );

    } catch (error) {
      console.error('发送消息失败:', error);
      setIsTyping(false);
      setMessages(prev => prev.map(msg =>
        msg.id === aiMessageId
          ? { ...msg, content: '抱歉，网络连接出现问题，请检查网络连接后重试。' }
          : msg
      ));
    }
  };

  // 标准模板数据现在从数据库动态加载，存储在state中

  // 历史对话数据
  const historyConversations = [
    { id: 1, title: '首页优化HTML报告', date: '2024-01-20', messages: 15 },
    { id: 2, title: '产品页面分析文档', date: '2024-01-19', messages: 8 },
    { id: 3, title: '技术诊断HTML页面', date: '2024-01-18', messages: 12 },
  ];

  // 处理模板选择
  const handleTemplateSelect = async (template) => {
    // 自动切换到Agent模式以生成HTML
    setMode('agent');
    setTemplateDialog(false);

    // 确定模板信息
    const isStandard = templateType === 'standard';
    const templateTitle = isStandard ? template.title : template.template_name;
    const templateKey = isStandard ? template.template_key : null;
    const templateId = !isStandard ? template.id : null;

    // 直接生成HTML，不在对话中显示模板选择消息
    try {
      // 生成HTML内容（使用新的模板服务）
      await generateHTMLContent(templateTitle, templateKey, templateId, !isStandard);

      console.log('HTML生成成功:', templateTitle);
    } catch (error) {
      console.error('模板生成失败:', error);
      // 如果生成失败，可以选择显示一个简单的错误提示，但不添加到对话中
      // 或者可以通过其他方式通知用户，比如toast提示
    }
  };

  // 处理历史对话选择
  const handleHistorySelect = (conversation) => {
    // 加载历史对话
    setHistoryDialog(false);
    // 这里可以加载历史消息和HTML
  };

  // 下载HTML文件
  const downloadHTML = () => {
    const blob = new Blob([htmlCode], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'seo-report-' + Date.now() + '.html';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // 全屏切换
  const toggleFullscreen = () => {
    if (!isFullscreen) {
      if (fullscreenContainerRef.current?.requestFullscreen) {
        fullscreenContainerRef.current.requestFullscreen();
      }
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      }
    }
    setIsFullscreen(!isFullscreen);
  };

  // 收藏模板
  const toggleFavorite = () => {
    if (!isFavorited && generatedHTML) {
      // 添加到自定义模板
      const newTemplate = {
        id: Date.now(),
        title: '收藏的报告 - ' + new Date().toLocaleDateString('zh-CN'),
        description: '从生成的HTML报告收藏',
        created: new Date().toLocaleDateString('zh-CN'),
      };
      setCustomTemplates(prev => [...prev, newTemplate]);
    }
    setIsFavorited(!isFavorited);
  };

  // 监听全屏变化
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };
    
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
    };
  }, []);

  return (
    <StyledContainer>
      {/* 左侧聊天面板 */}
      <LeftPanel>
        {/* 头部区域 */}
        <HeaderSection>
          <Box display="flex" alignItems="center" gap={2}>
            <Avatar sx={{ bgcolor: '#3b82f6', width: 40, height: 40 }}>
              <Psychology />
            </Avatar>
            <Box flex={1}>
              <Typography variant="h6" fontWeight="600">
                GEO监控助手
              </Typography>
              <Typography variant="caption" color="text.secondary">
                智能SEO分析与HTML报告生成
              </Typography>
            </Box>
          </Box>

          {/* 项目选择区域 */}
          <Box sx={{ mb: 2 }}>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 1, fontSize: '0.875rem' }}>
              选择项目：
            </Typography>
            {projectsLoading ? (
              <Box display="flex" alignItems="center" gap={1}>
                <CircularProgress size={16} />
                <Typography variant="body2" color="text.secondary">
                  加载项目中...
                </Typography>
              </Box>
            ) : projectsError ? (
              <Typography variant="body2" color="error" sx={{ fontSize: '0.875rem' }}>
                {projectsError}
              </Typography>
            ) : (
              <FormControl fullWidth size="small">
                <Select
                  value={selectedProject}
                  onChange={(e) => setSelectedProject(e.target.value)}
                  displayEmpty
                  sx={{
                    '& .MuiOutlinedInput-notchedOutline': {
                      borderColor: '#e5e7eb',
                    },
                    '&:hover .MuiOutlinedInput-notchedOutline': {
                      borderColor: '#3b82f6',
                    },
                    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                      borderColor: '#3b82f6',
                    },
                  }}
                >
                  <MenuItem value="" disabled>
                    <Typography color="text.secondary">请选择项目</Typography>
                  </MenuItem>
                  {projects.map((project) => (
                    <MenuItem key={project.id} value={project.id}>
                      <Box>
                        <Typography variant="body2" fontWeight="500">
                          {project.project_name}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {project.target_brand} • {project.status === 'active' ? '活跃' : '暂停'}
                        </Typography>
                      </Box>
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            )}
          </Box>

          <Box display="flex" gap={1}>
            <Button
              size="small"
              variant="outlined"
              startIcon={<Description />}
              onClick={() => {
                setTemplateType('standard');
                setTemplateDialog(true);
              }}
              sx={{ textTransform: 'none' }}
            >
              标准模板
            </Button>
            <Button
              size="small"
              variant="outlined"
              startIcon={<AutoFixHigh />}
              onClick={() => {
                setTemplateType('custom');
                setTemplateDialog(true);
              }}
              sx={{ textTransform: 'none' }}
            >
              自定义模板
            </Button>
            <Button
              size="small"
              variant="outlined"
              startIcon={<History />}
              onClick={() => setHistoryDialog(true)}
              sx={{ textTransform: 'none' }}
            >
              历史对话
            </Button>
            <Button
              size="small"
              variant="contained"
              startIcon={<Add />}
              onClick={() => {
                // 重置对话状态
                setCurrentConversationId(null);
                setConversationTitle('');
                setMessages([{
                  id: 1,
                  type: 'assistant',
                  content: '新对话已创建！请告诉我您需要分析什么内容或生成什么样的HTML报告？',
                  timestamp: new Date(),
                }]);
                setGeneratedHTML('');
                setHtmlCode('');
              }}
              sx={{
                backgroundColor: '#3b82f6',
                textTransform: 'none',
                '&:hover': {
                  backgroundColor: '#2563eb',
                },
              }}
            >
              新建对话
            </Button>
          </Box>
        </HeaderSection>

        {/* 聊天内容区域 */}
        <ChatContainer>
          <Box display="flex" flexDirection="column">
            {messages.map((message) => (
              <MessageBubble
                key={message.id}
                isUser={message.type === 'user'}
                elevation={0}
              >
                <Box display="flex" alignItems="flex-start" gap={1.5}>
                  {message.type === 'assistant' && (
                    <Avatar sx={{ bgcolor: '#3b82f6', width: 32, height: 32 }}>
                      <SmartToy sx={{ fontSize: 18 }} />
                    </Avatar>
                  )}
                  <Box flex={1}>
                    <Typography
                      variant="body2"
                      sx={{
                        whiteSpace: 'pre-wrap',
                        wordBreak: 'break-word',
                      }}
                    >
                      {message.content}
                    </Typography>
                    {message.type === 'assistant' && (
                      <Box display="flex" gap={1} mt={2}>
                        <IconButton size="small">
                          <ContentCopy sx={{ fontSize: 16 }} />
                        </IconButton>
                        <IconButton size="small">
                          <ThumbUp sx={{ fontSize: 16 }} />
                        </IconButton>
                        <IconButton size="small">
                          <ThumbDown sx={{ fontSize: 16 }} />
                        </IconButton>
                        <IconButton size="small">
                          <Refresh sx={{ fontSize: 16 }} />
                        </IconButton>
                      </Box>
                    )}
                  </Box>
                </Box>
              </MessageBubble>
            ))}
            {isTyping && (
              <MessageBubble isUser={false} elevation={0}>
                <Box display="flex" alignItems="center" gap={1}>
                  <Avatar sx={{ bgcolor: '#3b82f6', width: 32, height: 32 }}>
                    <SmartToy sx={{ fontSize: 18 }} />
                  </Avatar>
                  <Typography variant="body2" color="text.secondary">
                    正在生成...
                  </Typography>
                </Box>
              </MessageBubble>
            )}
            <div ref={chatEndRef} />
          </Box>
        </ChatContainer>

        {/* 输入区域 */}
        <InputSection>
          <Box display="flex" alignItems="center" gap={2} mb={2}>
            <Typography variant="body2" color="text.secondary">
              模式选择：
            </Typography>
            <ToggleButtonGroup
              value={mode}
              exclusive
              onChange={(e, newMode) => {
                if (newMode !== null) {
                  setMode(newMode);
                }
              }}
              size="small"
            >
              <ToggleButton value="dialog" sx={{ textTransform: 'none' }}>
                <Chat sx={{ fontSize: 18, mr: 1 }} />
                对话模式
              </ToggleButton>
              <ToggleButton value="agent" sx={{ textTransform: 'none' }}>
                <AutoAwesome sx={{ fontSize: 18, mr: 1 }} />
                Agent模式
              </ToggleButton>
            </ToggleButtonGroup>
            <Typography variant="caption" color="text.secondary" sx={{ ml: 2 }}>
              {mode === 'dialog' ? '普通对话，获取分析建议' : '生成HTML报告和页面'}
            </Typography>
          </Box>
          <Box display="flex" gap={2}>
            <TextField
              fullWidth
              multiline
              maxRows={4}
              placeholder={mode === 'dialog' 
                ? "输入您要分析的关键词或问题..." 
                : "输入要生成HTML报告的主题..."}
              value={inputText}
              onChange={(e) => setInputText(e.target.value)}
              onKeyPress={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  handleSendMessage();
                }
              }}
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: '8px',
                },
              }}
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton size="small">
                      <AttachFile />
                    </IconButton>
                    <IconButton size="small">
                      <Mic />
                    </IconButton>
                  </InputAdornment>
                ),
              }}
            />
            <Button
              variant="contained"
              endIcon={<Send />}
              onClick={handleSendMessage}
              sx={{
                backgroundColor: '#3b82f6',
                minWidth: '120px',
                '&:hover': {
                  backgroundColor: '#2563eb',
                },
              }}
            >
              发送
            </Button>
          </Box>
        </InputSection>
      </LeftPanel>

      {/* 右侧预览面板 */}
      <RightPanel ref={fullscreenContainerRef}>
        <Box sx={{ 
          borderBottom: 1, 
          borderColor: 'divider', 
          backgroundColor: '#fff',
          p: 2,
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}>
          <Typography variant="h6" sx={{ fontWeight: 600 }}>
            HTML预览
          </Typography>
          {generatedHTML && (
            <Box sx={{ display: 'flex', gap: 1 }}>
              <IconButton
                onClick={toggleFavorite}
                sx={{ 
                  color: isFavorited ? '#f59e0b' : '#9ca3af',
                  '&:hover': {
                    color: isFavorited ? '#d97706' : '#6b7280',
                  }
                }}
                title="收藏为自定义模板"
              >
                {isFavorited ? <Star /> : <StarBorder />}
              </IconButton>
              <IconButton
                onClick={toggleFullscreen}
                sx={{ 
                  color: '#6b7280',
                  '&:hover': {
                    color: '#1f2937',
                  }
                }}
                title={isFullscreen ? "退出全屏" : "全屏"}
              >
                {isFullscreen ? <FullscreenExit /> : <Fullscreen />}
              </IconButton>
              <Button
                size="small"
                startIcon={<Download />}
                onClick={downloadHTML}
                sx={{ textTransform: 'none' }}
              >
                下载HTML
              </Button>
            </Box>
          )}
        </Box>

        <PreviewContainer>
          {generatedHTML ? (
            <iframe
              ref={iframeRef}
              style={{
                width: '100%',
                height: '100%',
                border: 'none',
                borderRadius: '8px',
                backgroundColor: '#fff',
              }}
              title="HTML Preview"
            />
          ) : (
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                height: '100%',
                color: '#9ca3af',
              }}
            >
              <Preview sx={{ fontSize: 48, mb: 2 }} />
              <Typography variant="h6" color="text.secondary">
                HTML预览区域
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                {mode === 'agent' 
                  ? '发送消息后将在此处显示生成的HTML页面' 
                  : '切换到Agent模式以生成HTML内容'}
              </Typography>
            </Box>
          )}
        </PreviewContainer>
      </RightPanel>

      {/* 标准模板对话框 */}
      <Dialog
        open={templateDialog}
        onClose={() => setTemplateDialog(false)}
        maxWidth="lg"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: '12px',
            boxShadow: '0 8px 32px rgba(0, 0, 0, 0.08)',
            overflow: 'hidden',
            backgroundColor: 'white',
          }
        }}
      >
        <DialogTitle
          sx={{
            backgroundColor: 'white',
            color: '#1f2937',
            py: 3,
            px: 4,
            borderBottom: '1px solid #e5e7eb',
          }}
        >
          <Box display="flex" alignItems="center" justifyContent="space-between">
            <Box display="flex" alignItems="center" gap={2}>
              <Avatar sx={{
                bgcolor: '#f3f4f6',
                width: 40,
                height: 40,
                color: '#6b7280',
              }}>
                <Description />
              </Avatar>
              <Box>
                <Typography variant="h5" fontWeight="600" color="#1f2937">
                  {templateType === 'standard' ? '选择标准模板' : '选择自定义模板'}
                </Typography>
                <Typography variant="body2" color="#6b7280" sx={{ mt: 0.5 }}>
                  {templateType === 'standard' ? '快速开始，选择预设的专业模板' : '使用您保存的自定义模板'}
                </Typography>
              </Box>
            </Box>
            <IconButton
              onClick={() => setTemplateDialog(false)}
              sx={{
                color: '#6b7280',
                '&:hover': {
                  backgroundColor: '#f3f4f6',
                }
              }}
            >
              <Close />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent sx={{ p: 0, backgroundColor: 'white', height: '500px' }}>
          {templatesLoading ? (
            <Box sx={{
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              alignItems: 'center',
              height: '100%',
              gap: 2
            }}>
              <CircularProgress size={48} sx={{ color: '#3b82f6' }} />
              <Typography variant="h6" color="#6b7280">加载模板中...</Typography>
              <Typography variant="body2" color="#9ca3af">
                正在为您准备精美的模板
              </Typography>
            </Box>
          ) : templatesError ? (
            <Box sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              height: '100%',
              px: 4,
              gap: 2
            }}>
              <Avatar sx={{ bgcolor: '#fee2e2', color: '#dc2626', width: 56, height: 56 }}>
                <Error />
              </Avatar>
              <Typography variant="h6" color="error" textAlign="center">
                {templatesError}
              </Typography>
              <Button
                variant="contained"
                onClick={loadTemplates}
                sx={{
                  backgroundColor: '#3b82f6',
                  borderRadius: '8px',
                  px: 3,
                  py: 1,
                  textTransform: 'none',
                  fontWeight: 600,
                  '&:hover': {
                    backgroundColor: '#2563eb',
                  }
                }}
              >
                重新加载
              </Button>
            </Box>
          ) : (
            <Box sx={{ display: 'flex', height: '100%' }}>
              {/* 左侧模板列表 */}
              <Box sx={{
                width: '40%',
                borderRight: '1px solid #e5e7eb',
                backgroundColor: '#fafafa',
              }}>
                <List sx={{ py: 0 }}>
                  {(templateType === 'standard' ? standardTemplates : customTemplates).map((template, index) => (
                    <ListItem
                      key={template.id}
                      button
                      onClick={() => handleTemplateSelect(template)}
                      sx={{
                        py: 2,
                        px: 3,
                        borderBottom: '1px solid #e5e7eb',
                        cursor: 'pointer',
                        transition: 'all 0.2s ease',
                        '&:hover': {
                          backgroundColor: '#f3f4f6',
                        },
                        '&:last-child': {
                          borderBottom: 'none',
                        }
                      }}
                    >
                      <ListItemText
                        primary={
                          <Typography
                            variant="subtitle1"
                            fontWeight="600"
                            sx={{
                              color: '#1f2937',
                              mb: 0.5,
                              fontSize: '1rem',
                            }}
                          >
                            {template.title}
                          </Typography>
                        }
                        secondary={
                          <Typography
                            variant="body2"
                            color="#6b7280"
                            sx={{
                              lineHeight: 1.4,
                            }}
                          >
                            {template.description}
                          </Typography>
                        }
                      />
                      <ChevronRight sx={{ color: '#9ca3af', ml: 1 }} />
                    </ListItem>
                  ))}
                </List>
              </Box>

              {/* 右侧说明区域 */}
              <Box sx={{
                flex: 1,
                p: 4,
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                alignItems: 'center',
                backgroundColor: 'white',
              }}>
                <Box sx={{ textAlign: 'center', maxWidth: '300px' }}>
                  <Avatar sx={{
                    bgcolor: '#f3f4f6',
                    width: 80,
                    height: 80,
                    mx: 'auto',
                    mb: 3,
                    color: '#6b7280',
                  }}>
                    <Description sx={{ fontSize: '2rem' }} />
                  </Avatar>
                  <Typography variant="h6" fontWeight="600" color="#1f2937" sx={{ mb: 2 }}>
                    选择模板开始
                  </Typography>
                  <Typography variant="body2" color="#6b7280" sx={{ lineHeight: 1.6 }}>
                    从左侧列表中选择一个模板，系统将自动生成对应的HTML页面内容。每个模板都经过精心设计，适用于不同的业务场景。
                  </Typography>
                </Box>
              </Box>
            </Box>
          )}
        </DialogContent>
        <DialogActions sx={{
          p: 3,
          backgroundColor: 'white',
          borderTop: '1px solid #e5e7eb',
          justifyContent: 'flex-end',
        }}>
          <Button
            onClick={() => setTemplateDialog(false)}
            sx={{
              color: '#6b7280',
              textTransform: 'none',
              fontWeight: 500,
              px: 3,
              py: 1.5,
              borderRadius: '8px',
              border: '1px solid #e5e7eb',
              '&:hover': {
                backgroundColor: '#f9fafb',
                borderColor: '#d1d5db',
              }
            }}
          >
            取消
          </Button>
        </DialogActions>
      </Dialog>

      {/* 历史对话对话框 */}
      <Dialog
        open={historyDialog}
        onClose={() => setHistoryDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>历史对话</DialogTitle>
        <DialogContent>
          <List>
            {historyConversations.map((conversation) => (
              <ListItem
                key={conversation.id}
                button
                onClick={() => handleHistorySelect(conversation)}
                sx={{
                  border: '1px solid #e5e7eb',
                  borderRadius: '8px',
                  mb: 1,
                  '&:hover': {
                    backgroundColor: '#f9fafb',
                  },
                }}
              >
                <ListItemIcon>
                  <History />
                </ListItemIcon>
                <ListItemText
                  primary={conversation.title}
                  secondary={conversation.date + ' · ' + conversation.messages + ' 条消息'}
                />
              </ListItem>
            ))}
          </List>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setHistoryDialog(false)}>取消</Button>
        </DialogActions>
      </Dialog>
    </StyledContainer>
  );
}

export default GeoMonitoringAI;