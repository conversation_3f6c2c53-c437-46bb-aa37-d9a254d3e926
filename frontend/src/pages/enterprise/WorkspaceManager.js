import React, { useState, useEffect } from 'react';
import {
  Box,
  Typo<PERSON>,
  Button,
  Grid,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Chip,
  Avatar,
  LinearProgress,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Snackbar,
  Alert,
  Menu,
  ListItemIcon,
  ListItemText,
  Tooltip,
  FormHelperText,
  Switch,
  FormControlLabel,
  InputAdornment,
  Autocomplete,
} from '@mui/material';
import {
  Add,
  Edit,
  Delete,
  Settings,
  Search,
  Assignment,
  CheckCircle,
  Folder,
  MoreVert,
  ContentCopy,
  Archive,
  Share,
  Close,
  Groups,
  CalendarToday,
  TrendingUp,
  Warning,
  NotificationImportant,
  PlayArrow,
  Stop,
  AutoAwesome,
  Schedule,
  Visibility,
} from '@mui/icons-material';
import monitoringService from '../../services/monitoringService';

function WorkspaceManager() {
  // 项目列表状态
  const [projects, setProjects] = useState([]);
  const [filteredProjects, setFilteredProjects] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  
  // 分页状态
  const [currentPage, setCurrentPage] = useState(1);
  const projectsPerPage = 6; // 每页显示6个项目（2排）
  
  // 对话框状态
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [limitDialogOpen, setLimitDialogOpen] = useState(false);
  const [detailDialogOpen, setDetailDialogOpen] = useState(false);
  const [selectedProject, setSelectedProject] = useState(null);
  const [projectDetail, setProjectDetail] = useState(null);
  
  // 限制相关状态
  const [totalLimit, setTotalLimit] = useState(100);
  const [totalWarning, setTotalWarning] = useState(80);
  
  // 表单状态 - AI搜索引擎监控项目
  const [formData, setFormData] = useState({
    project_name: '',
    project_description: '',
    target_website: '',
    target_brand: '',
    keywords: [],
    search_engines: [],
    monitoring_frequency: 'daily',
    competitors: [],
  });

  // 表单错误状态
  const [formErrors, setFormErrors] = useState({});

  // 关键词输入状态
  const [keywordInput, setKeywordInput] = useState('');

  // 竞争对手输入状态
  const [competitorInput, setCompetitorInput] = useState({ name: '', website: '' });
  
  // 菜单状态
  const [anchorEl, setAnchorEl] = useState(null);
  const [menuProject, setMenuProject] = useState(null);
  
  // 通知状态
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success',
  });

  // 统计数据
  const [stats, setStats] = useState({
    total: 0,
    completed: 0,
    inProgress: 0,
    pending: 0,
  });
  
  // AI优化状态管理 - 为每个项目单独管理
  const [aiOptimizationStatus, setAiOptimizationStatus] = useState({});

  // 初始化数据
  useEffect(() => {
    loadProjects();
  }, []);

  // 搜索过滤
  useEffect(() => {
    if (searchTerm) {
      const filtered = projects.filter(project =>
        (project.project_name && project.project_name.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (project.project_description && project.project_description.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (project.target_brand && project.target_brand.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (project.keywords && project.keywords.some(keyword =>
          keyword.toLowerCase().includes(searchTerm.toLowerCase())
        ))
      );
      setFilteredProjects(filtered);
    } else {
      setFilteredProjects(projects);
    }
  }, [searchTerm, projects]);

  // 更新统计数据
  useEffect(() => {
    const total = projects.length;
    const active = projects.filter(p => p.status === 'active').length;
    const paused = projects.filter(p => p.status === 'paused').length;
    const completed = projects.filter(p => p.status === 'completed').length;

    setStats({ total, completed, inProgress: active, pending: paused });
  }, [projects]);

  // 加载监控项目列表
  const loadProjects = async () => {
    try {
      const params = {
        page: 1,
        size: 8,
        sort_by: 'created_at',
        sort_order: 'desc'
      };

      const response = await monitoringService.getMonitoringProjects(params);

      if (response && response.success && response.data) {
        const projects = response.data.items || [];
        setProjects(projects);
      } else {
        console.warn('获取监控项目列表失败:', response);
        // 使用示例数据作为后备
        const initialProjects = [
          {
            id: '1',
            project_name: 'AI教育科技项目',
            project_description: '教育领域AI应用推广',
            target_website: 'www.aiedu.com',
            target_brand: 'AI教育科技',
            keywords: ['AI教育', '在线学习', '智能教学'],
            search_engines: ['doubao', 'chatgpt'],
            monitoring_frequency: 'daily',
            status: 'active',
            created_at: new Date().toISOString(),
            next_monitor_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
            total_keywords: 3,
            competitors: []
          },
          {
            id: '2',
            project_name: '企业数字化转型',
            project_description: '数字化技术方案推广',
            target_website: 'www.digitaltrans.com',
            target_brand: '数字化转型专家',
            keywords: ['数字化转型', '企业升级', '智能办公'],
            search_engines: ['claude', 'gemini'],
            monitoring_frequency: 'weekly',
            status: 'active',
            created_at: new Date().toISOString(),
            next_monitor_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
            total_keywords: 3,
            competitors: []
          },
          {
            id: '3',
            project_name: '智能制造解决方案',
            project_description: '工业4.0智能制造推广',
            target_website: 'www.smartmfg.com',
            target_brand: '智能制造',
            keywords: ['智能制造', '工业4.0', '自动化生产'],
            search_engines: ['tongyi', 'wenxin'],
            monitoring_frequency: 'monthly',
            status: 'paused',
            created_at: new Date().toISOString(),
            next_monitor_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
            total_keywords: 3,
            competitors: []
          },
        ];
        setProjects(initialProjects);
      }
    } catch (error) {
      console.error('加载监控项目失败:', error);

      // 尝试从 localStorage 加载数据
      try {
        const savedProjects = localStorage.getItem('workspace_projects');
        if (savedProjects) {
          const parsedProjects = JSON.parse(savedProjects);
          setProjects(parsedProjects);
          console.log('从 localStorage 加载项目数据:', parsedProjects);
          return;
        }
      } catch (localError) {
        console.error('从 localStorage 加载数据失败:', localError);
      }

      // 如果 localStorage 也没有数据，使用示例数据作为后备
      const initialProjects = [
        {
          id: '1',
          project_name: 'AI教育科技项目',
          project_description: '教育领域AI应用推广',
          target_website: 'www.aiedu.com',
          target_brand: 'AI教育科技',
          keywords: ['AI教育', '在线学习', '智能教学'],
          search_engines: ['doubao', 'chatgpt'],
          monitoring_frequency: 'daily',
          status: 'active',
          created_at: new Date().toISOString(),
          next_monitor_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
          total_keywords: 3,
          competitors: []
        },
        {
          id: '2',
          project_name: '企业数字化转型',
          project_description: '数字化技术方案推广',
          target_website: 'www.digitaltrans.com',
          target_brand: '数字化转型专家',
          keywords: ['数字化转型', '企业升级', '智能办公'],
          search_engines: ['claude', 'gemini'],
          monitoring_frequency: 'weekly',
          status: 'active',
          created_at: new Date().toISOString(),
          next_monitor_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
          total_keywords: 3,
          competitors: []
        },
        {
          id: '3',
          project_name: '智能制造解决方案',
          project_description: '工业4.0智能制造推广',
          target_website: 'www.smartmfg.com',
          target_brand: '智能制造',
          keywords: ['智能制造', '工业4.0', '自动化生产'],
          search_engines: ['tongyi', 'wenxin'],
          monitoring_frequency: 'monthly',
          status: 'paused',
          created_at: new Date().toISOString(),
          next_monitor_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
          total_keywords: 3,
          competitors: []
        },
      ];
      setProjects(initialProjects);
      showSnackbar('加载项目列表失败，使用本地数据', 'warning');
    }
  };

  // 保存项目到 localStorage
  const saveProjects = (updatedProjects) => {
    setProjects(updatedProjects);
    localStorage.setItem('workspace_projects', JSON.stringify(updatedProjects));
  };

  // 创建新监控项目
  const handleCreateProject = async () => {
    try {
      // 验证表单数据
      const validation = monitoringService.validateProjectData(formData);
      if (!validation.isValid) {
        setFormErrors(validation.errors);
        return;
      }

      // 清除之前的错误
      setFormErrors({});

      // 调用API创建项目
      const response = await monitoringService.createMonitoringProject(formData);

      if (response && response.success) {
        showSnackbar('AI搜索引擎监控项目创建成功', 'success');
        setCreateDialogOpen(false);
        resetForm();
        // 重新加载项目列表
        loadProjects();
      } else {
        showSnackbar(response?.message || '创建项目失败', 'error');
      }
    } catch (error) {
      console.error('创建监控项目失败:', error);
      showSnackbar('创建项目失败，请稍后重试', 'error');
    }
  };

  // 编辑监控项目
  const handleEditProject = async () => {
    try {
      // 验证表单数据
      const validation = monitoringService.validateProjectData(formData);
      if (!validation.isValid) {
        setFormErrors(validation.errors);
        return;
      }

      // 清除之前的错误
      setFormErrors({});

      // 调用API更新项目
      const response = await monitoringService.updateMonitoringProject(selectedProject.id, formData);

      if (response && response.success) {
        showSnackbar('监控项目更新成功', 'success');
        setEditDialogOpen(false);
        resetForm();
        // 重新加载项目列表
        loadProjects();
      } else {
        showSnackbar(response?.message || '更新项目失败', 'error');
      }
    } catch (error) {
      console.error('更新监控项目失败:', error);
      showSnackbar('更新项目失败，请稍后重试', 'error');
    }
  };

  // 查看项目详情
  const handleViewProject = async (project) => {
    try {
      console.log('查看项目:', project);

      // 调用监控项目详情API
      const response = await monitoringService.getMonitoringProject(project.id);

      if (response && response.success) {
        console.log('项目详情API响应:', response.data);

        // 设置项目详情数据并打开对话框
        setProjectDetail(response.data);
        setDetailDialogOpen(true);

        showSnackbar(`查看项目: ${project.project_name}`, 'info');
      } else {
        showSnackbar(response?.message || '获取项目详情失败', 'error');
      }
    } catch (error) {
      console.error('获取项目详情失败:', error);
      showSnackbar('获取项目详情失败，请稍后重试', 'error');
    }
  };

  // 删除项目
  const handleDeleteProject = () => {
    const updatedProjects = projects.filter(p => p.id !== selectedProject.id);
    saveProjects(updatedProjects);

    setDeleteDialogOpen(false);
    setSelectedProject(null);
    showSnackbar('项目删除成功', 'success');
  };

  // 复制项目
  const handleDuplicateProject = (project) => {
    const duplicatedProject = {
      ...project,
      id: Date.now(),
      title: `${project.title} (副本)`,
      createdAt: new Date().toISOString(),
    };
    
    const updatedProjects = [...projects, duplicatedProject];
    saveProjects(updatedProjects);
    showSnackbar('项目复制成功', 'success');
  };

  // 归档项目
  const handleArchiveProject = (project) => {
    const updatedProjects = projects.map(p =>
      p.id === project.id ? { ...p, status: '已归档' } : p
    );
    
    saveProjects(updatedProjects);
    showSnackbar('项目已归档', 'info');
  };

  // 打开编辑对话框
  const openEditDialog = (project) => {
    setSelectedProject(project);
    setFormData({
      project_name: project.project_name || '',
      project_description: project.project_description || '',
      target_website: project.target_website || '',
      target_brand: project.target_brand || '',
      keywords: project.keywords || [],
      search_engines: project.search_engines || [],
      monitoring_frequency: project.monitoring_frequency || 'daily',
      competitors: project.competitors || [],
    });
    setFormErrors({});
    setKeywordInput('');
    setCompetitorInput({ name: '', website: '' });
    setEditDialogOpen(true);
    handleMenuClose();
  };

  // 打开删除对话框
  const openDeleteDialog = (project) => {
    setSelectedProject(project);
    setDeleteDialogOpen(true);
    handleMenuClose();
  };

  // 重置表单
  const resetForm = () => {
    setFormData({
      project_name: '',
      project_description: '',
      target_website: '',
      target_brand: '',
      keywords: [],
      search_engines: [],
      monitoring_frequency: 'daily',
      competitors: [],
    });
    setFormErrors({});
    setKeywordInput('');
    setCompetitorInput({ name: '', website: '' });
    setSelectedProject(null);
  };

  // 添加关键词
  const handleAddKeyword = () => {
    if (keywordInput.trim() && !formData.keywords.includes(keywordInput.trim())) {
      setFormData({
        ...formData,
        keywords: [...formData.keywords, keywordInput.trim()]
      });
      setKeywordInput('');
    }
  };

  // 删除关键词
  const handleRemoveKeyword = (keyword) => {
    setFormData({
      ...formData,
      keywords: formData.keywords.filter(k => k !== keyword)
    });
  };

  // 添加竞争对手
  const handleAddCompetitor = () => {
    if (competitorInput.name.trim() && competitorInput.website.trim()) {
      const newCompetitor = {
        name: competitorInput.name.trim(),
        website: competitorInput.website.trim()
      };
      setFormData({
        ...formData,
        competitors: [...formData.competitors, newCompetitor]
      });
      setCompetitorInput({ name: '', website: '' });
    }
  };

  // 删除竞争对手
  const handleRemoveCompetitor = (index) => {
    setFormData({
      ...formData,
      competitors: formData.competitors.filter((_, i) => i !== index)
    });
  };

  // 显示通知
  const showSnackbar = (message, severity = 'success') => {
    setSnackbar({ open: true, message, severity });
  };

  // 菜单处理
  const handleMenuOpen = (event, project) => {
    setAnchorEl(event.currentTarget);
    setMenuProject(project);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setMenuProject(null);
  };

  // 获取状态颜色
  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return '#4caf50';
      case 'paused': return '#ff9800';
      case 'completed': return '#2196f3';
      case 'cancelled': return '#9e9e9e';
      default: return '#757575';
    }
  };

  // 获取状态文本
  const getStatusText = (status) => {
    switch (status) {
      case 'active': return '进行中';
      case 'paused': return '已暂停';
      case 'completed': return '已完成';
      case 'cancelled': return '已取消';
      default: return '未知';
    }
  };

  // 获取监控频率文本
  const getFrequencyText = (frequency) => {
    switch (frequency) {
      case 'daily': return '每日';
      case 'weekly': return '每周';
      case 'monthly': return '每月';
      default: return frequency;
    }
  };

  return (
    <Box sx={{ width: '100%', minHeight: '100vh', backgroundColor: 'white' }}>
      {/* 顶部标题区域 */}
      <Box sx={{ borderBottom: '1px solid #f0f0f0', backgroundColor: 'white' }}>
        <Box sx={{ px: 4, py: 4 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Box>
              <Typography variant="h4" sx={{ 
                fontWeight: 700, 
                color: '#1a1a1a', 
                mb: 0.5,
                fontSize: { xs: '1.75rem', md: '2rem' }
              }}>
                工作空间
              </Typography>
              <Typography variant="body1" sx={{ color: '#6b7280', fontSize: '1rem' }}>
                管理项目和协作的统一工作空间
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', gap: 2 }}>
              <Button
                variant="contained"
                startIcon={<Add />}
                onClick={() => setCreateDialogOpen(true)}
                sx={{
                  backgroundColor: '#1976d2',
                  '&:hover': { backgroundColor: '#1565c0' }
                }}
              >
                新建项目
              </Button>
            </Box>
          </Box>
        </Box>
      </Box>


      {/* 项目列表区域 */}
      <Box sx={{ backgroundColor: 'white' }}>
        <Box sx={{ px: 4, py: 3 }}>
          {/* 标题和搜索栏 */}
          <Box sx={{ 
            display: 'flex', 
            justifyContent: 'space-between', 
            alignItems: 'center',
            mb: 3
          }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 3 }}>
              <Box>
                <Typography variant="h5" sx={{
                  fontWeight: 700,
                  color: '#1a1a1a',
                  mb: 0.5
                }}>
                  项目列表
                </Typography>
                <Typography variant="body2" sx={{ color: '#6b7280' }}>
                  管理所有进行中的项目
                </Typography>
              </Box>
              
              {/* 分页控件 */}
              <Box sx={{ 
                display: 'flex', 
                alignItems: 'center',
                gap: 1,
                ml: 4
              }}>
                <Button
                  size="small"
                  variant="outlined"
                  onClick={() => setCurrentPage(currentPage - 1)}
                  disabled={currentPage === 1}
                  sx={{
                    minWidth: 'auto',
                    px: 2,
                    borderColor: '#e5e7eb',
                    color: '#6b7280',
                    '&:hover': {
                      borderColor: '#9ca3af',
                      backgroundColor: '#f9fafb'
                    },
                    '&.Mui-disabled': {
                      borderColor: '#f3f4f6',
                      color: '#d1d5db'
                    }
                  }}
                >
                  上一页
                </Button>
                
                <Box sx={{ display: 'flex', gap: 0.5 }}>
                  {[...Array(Math.ceil(filteredProjects.length / projectsPerPage))].map((_, index) => (
                    <Button
                      key={index + 1}
                      size="small"
                      variant={currentPage === index + 1 ? 'contained' : 'text'}
                      onClick={() => setCurrentPage(index + 1)}
                      sx={{
                        minWidth: 32,
                        height: 32,
                        p: 0,
                        borderRadius: 1,
                        backgroundColor: currentPage === index + 1 ? '#3b82f6' : 'transparent',
                        color: currentPage === index + 1 ? 'white' : '#6b7280',
                        '&:hover': {
                          backgroundColor: currentPage === index + 1 ? '#2563eb' : '#f3f4f6'
                        }
                      }}
                    >
                      {index + 1}
                    </Button>
                  ))}
                </Box>
                
                <Button
                  size="small"
                  variant="outlined"
                  onClick={() => setCurrentPage(currentPage + 1)}
                  disabled={currentPage === Math.ceil(filteredProjects.length / projectsPerPage)}
                  sx={{
                    minWidth: 'auto',
                    px: 2,
                    borderColor: '#e5e7eb',
                    color: '#6b7280',
                    '&:hover': {
                      borderColor: '#9ca3af',
                      backgroundColor: '#f9fafb'
                    },
                    '&.Mui-disabled': {
                      borderColor: '#f3f4f6',
                      color: '#d1d5db'
                    }
                  }}
                >
                  下一页
                </Button>
              </Box>
            </Box>
            <TextField
              placeholder="搜索项目..."
              size="small"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              data-testid="project-search"
              sx={{
                width: 300,
                '& .MuiOutlinedInput-root': {
                  backgroundColor: '#f9fafb',
                  '&:hover': { backgroundColor: '#f3f4f6' },
                  '&.Mui-focused': { backgroundColor: 'white' }
                }
              }}
              InputProps={{
                startAdornment: <Search sx={{ color: '#9ca3af', mr: 1 }} />
              }}
            />
          </Box>

          {/* 项目网格 - 2排显示 */}
          <Grid container spacing={3}>
            {filteredProjects
              .slice((currentPage - 1) * projectsPerPage, currentPage * projectsPerPage)
              .map((project) => (
              <Grid item xs={12} md={6} key={project.id}>
                <Box
                  data-testid="project-card"
                  sx={{
                    p: 2.5,
                    height: '180px', // 固定高度确保卡片一致性
                    border: '1px solid',
                    borderColor: '#e5e7eb',
                    borderRadius: 2,
                    backgroundColor: 'white',
                    transition: 'all 0.2s ease',
                    display: 'flex',
                    flexDirection: 'column',
                    '&:hover': {
                      borderColor: '#d1d5db',
                      transform: 'translateY(-2px)',
                      boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
                    }
                  }}
                >
                  {/* 标题栏 */}
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                    <Typography variant="h6" data-testid="project-name" sx={{
                      fontWeight: 600,
                      color: '#1a1a1a',
                      flex: 1,
                      minWidth: 0,
                      lineHeight: 1.2,
                      fontSize: '1rem',
                      mr: 1,
                      // 由于限制了10个字符，通常不会溢出，但保留作为安全措施
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap'
                    }}>
                      {project.project_name}
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 1, alignItems: 'center', flexShrink: 0 }}>
                      <Chip
                        label={getStatusText(project.status)}
                        size="small"
                        data-testid="project-status"
                        sx={{
                          backgroundColor: getStatusColor(project.status),
                          color: '#fff',
                          fontWeight: 500,
                          height: 22,
                          fontSize: '0.75rem'
                        }}
                      />
                      <IconButton
                        size="small"
                        onClick={(e) => handleMenuOpen(e, project)}
                      >
                        <MoreVert fontSize="small" />
                      </IconButton>
                    </Box>
                  </Box>

                  {/* 项目描述 */}
                  <Box sx={{ flex: 1, mb: 2 }}>
                    <Typography variant="body2" data-testid="project-description" sx={{
                      color: '#6b7280',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      display: '-webkit-box',
                      WebkitLineClamp: 3,
                      WebkitBoxOrient: 'vertical',
                      lineHeight: 1.4,
                      height: '4.2em' // 固定3行的高度
                    }}>
                      {project.project_description || '暂无描述'}
                    </Typography>
                  </Box>

                  {/* 操作按钮 - 固定在底部 */}
                  <Box sx={{
                    display: 'flex',
                    gap: 0.5,
                    pt: 1.5,
                    borderTop: '1px solid #f0f0f0',
                    mt: 'auto' // 自动推到底部
                  }}>
                    <Button
                      size="small"
                      variant="outlined"
                      startIcon={<Visibility />}
                      onClick={() => handleViewProject && handleViewProject(project)}
                      data-testid="view-project-button"
                      sx={{
                        flex: 1,
                        fontSize: '0.7rem',
                        py: 0.4,
                        minWidth: 0,
                        px: 0.8,
                        textTransform: 'none'
                      }}
                    >
                      查看
                    </Button>
                    <Button
                      size="small"
                      variant="outlined"
                      startIcon={<Edit />}
                      onClick={() => openEditDialog(project)}
                      data-testid="edit-project-button"
                      sx={{
                        flex: 1,
                        fontSize: '0.7rem',
                        py: 0.4,
                        minWidth: 0,
                        px: 0.8,
                        textTransform: 'none'
                      }}
                    >
                      编辑
                    </Button>
                    <Button
                      size="small"
                      variant="outlined"
                      startIcon={<AutoAwesome />}
                      onClick={() => {
                        setAiOptimizationStatus(prev => ({
                          ...prev,
                          [project.id]: !prev[project.id]
                        }));
                        setSnackbar({
                          open: true,
                          message: aiOptimizationStatus[project.id] ? 'AI优化已停止' : 'AI优化已启动',
                          severity: 'success',
                        });
                      }}
                      sx={{
                        flex: 1,
                        fontSize: '0.7rem',
                        py: 0.4,
                        minWidth: 0,
                        px: 0.8,
                        textTransform: 'none',
                        color: aiOptimizationStatus[project.id] ? '#ef4444' : '#1976d2',
                        borderColor: aiOptimizationStatus[project.id] ? '#ef4444' : '#1976d2',
                        '&:hover': {
                          backgroundColor: aiOptimizationStatus[project.id] ? '#fef2f2' : '#e3f2fd',
                          borderColor: aiOptimizationStatus[project.id] ? '#dc2626' : '#1565c0'
                        }
                      }}
                    >
                      AI优化
                    </Button>
                    <Button
                      size="small"
                      variant="outlined"
                      startIcon={<Delete />}
                      onClick={() => openDeleteDialog(project)}
                      sx={{
                        flex: 1,
                        fontSize: '0.7rem',
                        py: 0.4,
                        minWidth: 0,
                        px: 0.8,
                        textTransform: 'none',
                        color: '#ef4444',
                        borderColor: '#ef4444',
                        '&:hover': {
                          backgroundColor: '#fef2f2',
                          borderColor: '#dc2626'
                        }
                      }}
                    >
                      删除
                    </Button>
                  </Box>
                </Box>
              </Grid>
            ))}
          </Grid>

          {/* 空状态 */}
          {filteredProjects.length === 0 && (
            <Box sx={{ 
              textAlign: 'center', 
              py: 8,
              backgroundColor: '#f9fafb',
              borderRadius: 2,
              border: '1px dashed #e5e7eb'
            }}>
              <Folder sx={{ fontSize: 64, color: '#d1d5db', mb: 2 }} />
              <Typography variant="h6" sx={{ color: '#6b7280', mb: 1 }}>
                暂无项目
              </Typography>
              <Typography variant="body2" sx={{ color: '#9ca3af', mb: 3 }}>
                点击"新建项目"按钮创建您的第一个项目
              </Typography>
              <Button
                variant="contained"
                startIcon={<Add />}
                onClick={() => setCreateDialogOpen(true)}
              >
                新建项目
              </Button>
            </Box>
          )}
        </Box>
      </Box>

      {/* 项目菜单 */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => openEditDialog(menuProject)}>
          <ListItemIcon><Edit fontSize="small" /></ListItemIcon>
          <ListItemText>编辑</ListItemText>
        </MenuItem>
        <MenuItem onClick={() => handleDuplicateProject(menuProject)}>
          <ListItemIcon><ContentCopy fontSize="small" /></ListItemIcon>
          <ListItemText>复制</ListItemText>
        </MenuItem>
        <MenuItem onClick={() => handleArchiveProject(menuProject)}>
          <ListItemIcon><Archive fontSize="small" /></ListItemIcon>
          <ListItemText>归档</ListItemText>
        </MenuItem>
        <MenuItem onClick={() => openDeleteDialog(menuProject)}>
          <ListItemIcon><Delete fontSize="small" color="error" /></ListItemIcon>
          <ListItemText>删除</ListItemText>
        </MenuItem>
      </Menu>

      {/* 创建项目对话框 */}
      <Dialog
        open={createDialogOpen}
        onClose={() => setCreateDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          创建AI搜索引擎监控项目
          <IconButton
            onClick={() => setCreateDialogOpen(false)}
            sx={{ position: 'absolute', right: 8, top: 8 }}
          >
            <Close />
          </IconButton>
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            {/* 项目名称 */}
            <TextField
              fullWidth
              label="项目名称"
              value={formData.project_name}
              onChange={(e) => {
                const value = e.target.value;
                if (value.length <= 10) {
                  setFormData({ ...formData, project_name: value });
                }
              }}
              error={!!formErrors.project_name}
              helperText={formErrors.project_name || `${formData.project_name.length}/10 字符`}
              sx={{ mb: 2 }}
              required
              inputProps={{ maxLength: 10 }}
            />

            {/* 项目描述 */}
            <TextField
              fullWidth
              label="项目描述"
              value={formData.project_description}
              onChange={(e) => setFormData({ ...formData, project_description: e.target.value })}
              multiline
              rows={3}
              sx={{ mb: 2 }}
            />

            {/* 目标网站和品牌 */}
            <Grid container spacing={2}>
              <Grid item xs={6}>
                <TextField
                  fullWidth
                  label="目标网站"
                  value={formData.target_website}
                  onChange={(e) => setFormData({ ...formData, target_website: e.target.value })}
                  error={!!formErrors.target_website}
                  helperText={formErrors.target_website || '如：www.example.com'}
                  required
                />
              </Grid>
              <Grid item xs={6}>
                <TextField
                  fullWidth
                  label="目标品牌"
                  value={formData.target_brand}
                  onChange={(e) => setFormData({ ...formData, target_brand: e.target.value })}
                  error={!!formErrors.target_brand}
                  helperText={formErrors.target_brand}
                  required
                />
              </Grid>
            </Grid>

            {/* 监控关键词 */}
            <Box sx={{ mt: 2, mb: 2 }}>
              <Typography variant="subtitle2" sx={{ mb: 1 }}>
                监控关键词 *
              </Typography>
              <Box sx={{ display: 'flex', gap: 1, mb: 1 }}>
                <TextField
                  fullWidth
                  size="small"
                  placeholder="输入关键词后按回车添加"
                  value={keywordInput}
                  onChange={(e) => setKeywordInput(e.target.value)}
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      handleAddKeyword();
                    }
                  }}
                />
                <Button variant="outlined" onClick={handleAddKeyword}>
                  添加
                </Button>
              </Box>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                {formData.keywords.map((keyword, index) => (
                  <Chip
                    key={index}
                    label={keyword}
                    onDelete={() => handleRemoveKeyword(keyword)}
                    size="small"
                  />
                ))}
              </Box>
              {formErrors.keywords && (
                <FormHelperText error>{formErrors.keywords}</FormHelperText>
              )}
            </Box>

            {/* AI搜索引擎选择 */}
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" sx={{ mb: 1 }}>
                监控的AI搜索引擎 *
              </Typography>
              <Autocomplete
                multiple
                options={monitoringService.getSupportedSearchEngines()}
                getOptionLabel={(option) => option.label}
                value={monitoringService.getSupportedSearchEngines().filter(engine =>
                  formData.search_engines.includes(engine.value)
                )}
                onChange={(event, newValue) => {
                  setFormData({
                    ...formData,
                    search_engines: newValue.map(item => item.value)
                  });
                }}
                renderTags={(value, getTagProps) =>
                  value.map((option, index) => (
                    <Chip
                      variant="outlined"
                      label={option.label}
                      {...getTagProps({ index })}
                      key={option.value}
                    />
                  ))
                }
                renderInput={(params) => (
                  <TextField
                    {...params}
                    placeholder="选择AI搜索引擎"
                    error={!!formErrors.search_engines}
                    helperText={formErrors.search_engines}
                  />
                )}
              />
            </Box>

            {/* 监控频率 */}
            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel>监控频率</InputLabel>
              <Select
                value={formData.monitoring_frequency}
                onChange={(e) => setFormData({ ...formData, monitoring_frequency: e.target.value })}
                label="监控频率"
              >
                {monitoringService.getMonitoringFrequencies().map(freq => (
                  <MenuItem key={freq.value} value={freq.value}>
                    {freq.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCreateDialogOpen(false)}>取消</Button>
          <Button
            onClick={handleCreateProject}
            variant="contained"
            disabled={!formData.project_name || !formData.target_website || !formData.target_brand ||
                     formData.keywords.length === 0 || formData.search_engines.length === 0}
            sx={{ backgroundColor: '#3b82f6', '&:hover': { backgroundColor: '#2563eb' } }}
          >
            创建监控项目
          </Button>
        </DialogActions>
      </Dialog>

      {/* 编辑AI搜索引擎监控项目对话框 */}
      <Dialog
        open={editDialogOpen}
        onClose={() => setEditDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          编辑AI搜索引擎监控项目
          <IconButton
            onClick={() => setEditDialogOpen(false)}
            sx={{ position: 'absolute', right: 8, top: 8 }}
          >
            <Close />
          </IconButton>
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            {/* 项目名称 */}
            <TextField
              fullWidth
              label="项目名称"
              value={formData.project_name}
              onChange={(e) => {
                const value = e.target.value;
                if (value.length <= 10) {
                  setFormData({ ...formData, project_name: value });
                }
              }}
              error={!!formErrors.project_name}
              helperText={formErrors.project_name || `${formData.project_name.length}/10 字符`}
              sx={{ mb: 2 }}
              required
              inputProps={{ maxLength: 10 }}
            />

            {/* 项目描述 */}
            <TextField
              fullWidth
              label="项目描述"
              value={formData.project_description}
              onChange={(e) => setFormData({ ...formData, project_description: e.target.value })}
              multiline
              rows={3}
              sx={{ mb: 2 }}
            />

            {/* 目标网站和品牌 */}
            <Grid container spacing={2}>
              <Grid item xs={6}>
                <TextField
                  fullWidth
                  label="目标网站"
                  value={formData.target_website}
                  onChange={(e) => setFormData({ ...formData, target_website: e.target.value })}
                  error={!!formErrors.target_website}
                  helperText={formErrors.target_website || '如：www.example.com'}
                  required
                />
              </Grid>
              <Grid item xs={6}>
                <TextField
                  fullWidth
                  label="目标品牌"
                  value={formData.target_brand}
                  onChange={(e) => setFormData({ ...formData, target_brand: e.target.value })}
                  error={!!formErrors.target_brand}
                  helperText={formErrors.target_brand}
                  required
                />
              </Grid>
            </Grid>

            {/* 监控关键词 */}
            <Box sx={{ mt: 2, mb: 2 }}>
              <Typography variant="subtitle2" sx={{ mb: 1 }}>
                监控关键词 *
              </Typography>
              <Box sx={{ display: 'flex', gap: 1, mb: 1 }}>
                <TextField
                  fullWidth
                  size="small"
                  placeholder="输入关键词后按回车添加"
                  value={keywordInput}
                  onChange={(e) => setKeywordInput(e.target.value)}
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      handleAddKeyword();
                    }
                  }}
                />
                <Button variant="outlined" onClick={handleAddKeyword}>
                  添加
                </Button>
              </Box>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                {formData.keywords.map((keyword, index) => (
                  <Chip
                    key={index}
                    label={keyword}
                    onDelete={() => handleRemoveKeyword(keyword)}
                    size="small"
                  />
                ))}
              </Box>
              {formErrors.keywords && (
                <FormHelperText error>{formErrors.keywords}</FormHelperText>
              )}
            </Box>

            {/* AI搜索引擎选择 */}
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" sx={{ mb: 1 }}>
                监控的AI搜索引擎 *
              </Typography>
              <Autocomplete
                multiple
                options={monitoringService.getSupportedSearchEngines()}
                getOptionLabel={(option) => option.label}
                value={monitoringService.getSupportedSearchEngines().filter(engine =>
                  formData.search_engines.includes(engine.value)
                )}
                onChange={(event, newValue) => {
                  setFormData({
                    ...formData,
                    search_engines: newValue.map(item => item.value)
                  });
                }}
                renderTags={(value, getTagProps) =>
                  value.map((option, index) => (
                    <Chip
                      variant="outlined"
                      label={option.label}
                      {...getTagProps({ index })}
                      key={option.value}
                    />
                  ))
                }
                renderInput={(params) => (
                  <TextField
                    {...params}
                    placeholder="选择AI搜索引擎"
                    error={!!formErrors.search_engines}
                    helperText={formErrors.search_engines}
                  />
                )}
              />
            </Box>

            {/* 监控频率 */}
            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel>监控频率</InputLabel>
              <Select
                value={formData.monitoring_frequency}
                onChange={(e) => setFormData({ ...formData, monitoring_frequency: e.target.value })}
                label="监控频率"
              >
                {monitoringService.getMonitoringFrequencies().map(freq => (
                  <MenuItem key={freq.value} value={freq.value}>
                    {freq.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditDialogOpen(false)}>取消</Button>
          <Button
            onClick={handleEditProject}
            variant="contained"
            disabled={!formData.project_name || !formData.target_website || !formData.target_brand ||
                     formData.keywords.length === 0 || formData.search_engines.length === 0}
            sx={{ backgroundColor: '#3b82f6', '&:hover': { backgroundColor: '#2563eb' } }}
          >
            保存修改
          </Button>
        </DialogActions>
      </Dialog>

      {/* 删除确认对话框 */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
        maxWidth="xs"
        fullWidth
      >
        <DialogTitle>确认删除</DialogTitle>
        <DialogContent>
          <Typography>
            确定要删除项目 "{selectedProject?.title}" 吗？此操作无法撤销。
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>取消</Button>
          <Button 
            onClick={handleDeleteProject}
            variant="contained"
            color="error"
          >
            删除
          </Button>
        </DialogActions>
      </Dialog>

      {/* 限额设置对话框 */}
      <Dialog
        open={limitDialogOpen}
        onClose={() => setLimitDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          总限额设置
          <IconButton
            onClick={() => setLimitDialogOpen(false)}
            sx={{ position: 'absolute', right: 8, top: 8 }}
          >
            <Close />
          </IconButton>
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <Typography variant="body2" sx={{ color: '#6b7280', mb: 3 }}>
              设置项目的总体预算限额和预警阈值
            </Typography>
            <TextField
              fullWidth
              label="总限额"
              value={totalLimit}
              onChange={(e) => setTotalLimit(e.target.value)}
              type="number"
              InputProps={{
                startAdornment: <InputAdornment position="start">¥</InputAdornment>,
              }}
              sx={{ mb: 3 }}
            />
            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" sx={{ color: '#374151', mb: 1 }}>
                预警百分比: {totalWarning}%
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <NotificationImportant sx={{ color: '#ff9800' }} />
                <Box sx={{ flex: 1 }}>
                  <LinearProgress
                    variant="determinate"
                    value={totalWarning}
                    sx={{
                      height: 8,
                      borderRadius: 4,
                      backgroundColor: '#f3f4f6',
                      '& .MuiLinearProgress-bar': {
                        borderRadius: 4,
                        backgroundColor: '#ff9800',
                      }
                    }}
                  />
                </Box>
                <Typography variant="body2" sx={{ minWidth: 40 }}>
                  {totalWarning}%
                </Typography>
              </Box>
            </Box>
            <TextField
              fullWidth
              label="预警百分比"
              value={totalWarning}
              onChange={(e) => setTotalWarning(Math.min(100, Math.max(0, parseInt(e.target.value) || 0)))}
              type="number"
              InputProps={{
                endAdornment: <InputAdornment position="end">%</InputAdornment>,
                inputProps: { min: 0, max: 100 }
              }}
              helperText="当使用额度达到此百分比时将触发预警"
              sx={{ mt: 2 }}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setLimitDialogOpen(false)}>取消</Button>
          <Button 
            onClick={() => {
              showSnackbar('限额设置已保存', 'success');
              setLimitDialogOpen(false);
            }}
            variant="contained"
          >
            保存
          </Button>
        </DialogActions>
      </Dialog>

      {/* 项目详情对话框 */}
      <Dialog
        open={detailDialogOpen}
        onClose={() => setDetailDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Visibility color="primary" />
            项目详情
          </Box>
        </DialogTitle>
        <DialogContent>
          {projectDetail && (
            <Box sx={{ mt: 2 }}>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                    项目名称
                  </Typography>
                  <Typography variant="body1" sx={{ mb: 2 }}>
                    {projectDetail.project_name || '未设置'}
                  </Typography>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                    项目状态
                  </Typography>
                  <Chip
                    label={projectDetail.status === 'active' ? '运行中' : '已停止'}
                    color={projectDetail.status === 'active' ? 'success' : 'default'}
                    size="small"
                    sx={{ mb: 2 }}
                  />
                </Grid>

                <Grid item xs={12}>
                  <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                    项目描述
                  </Typography>
                  <Typography variant="body1" sx={{ mb: 2 }}>
                    {projectDetail.project_description || '暂无描述'}
                  </Typography>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                    目标网站
                  </Typography>
                  <Typography variant="body1" sx={{ mb: 2 }}>
                    {projectDetail.target_website || '未设置'}
                  </Typography>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                    目标品牌
                  </Typography>
                  <Typography variant="body1" sx={{ mb: 2 }}>
                    {projectDetail.target_brand || '未设置'}
                  </Typography>
                </Grid>

                <Grid item xs={12}>
                  <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                    监控关键词
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                    {projectDetail.keywords && projectDetail.keywords.length > 0 ? (
                      projectDetail.keywords.map((keyword, index) => (
                        <Chip
                          key={index}
                          label={keyword}
                          size="small"
                          variant="outlined"
                        />
                      ))
                    ) : (
                      <Typography variant="body2" color="text.secondary">
                        暂无关键词
                      </Typography>
                    )}
                  </Box>
                </Grid>

                <Grid item xs={12}>
                  <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                    AI搜索引擎
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                    {projectDetail.search_engines && projectDetail.search_engines.length > 0 ? (
                      projectDetail.search_engines.map((engine, index) => (
                        <Chip
                          key={index}
                          label={engine}
                          size="small"
                          color="primary"
                          variant="outlined"
                        />
                      ))
                    ) : (
                      <Typography variant="body2" color="text.secondary">
                        暂无搜索引擎
                      </Typography>
                    )}
                  </Box>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                    监控频率
                  </Typography>
                  <Typography variant="body1" sx={{ mb: 2 }}>
                    {projectDetail.monitoring_frequency === 'daily' ? '每日' :
                     projectDetail.monitoring_frequency === 'weekly' ? '每周' :
                     projectDetail.monitoring_frequency === 'monthly' ? '每月' : '未设置'}
                  </Typography>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                    创建时间
                  </Typography>
                  <Typography variant="body1" sx={{ mb: 2 }}>
                    {projectDetail.created_at ?
                      new Date(projectDetail.created_at).toLocaleString('zh-CN') :
                      '未知'}
                  </Typography>
                </Grid>
              </Grid>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDetailDialogOpen(false)}>
            关闭
          </Button>
        </DialogActions>
      </Dialog>

      {/* 通知提示 */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={3000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert 
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
}

export default WorkspaceManager;