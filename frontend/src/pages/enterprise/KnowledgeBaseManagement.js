import React, { useState, useRef, useEffect } from 'react';
import {
  Box,
  Typo<PERSON>,
  Button,
  Grid,
  Card,
  CardContent,
  IconButton,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Chip,
  Alert,
  LinearProgress,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondary,
  Divider,
  FormControl,
  InputLabel,
  Select,
  Paper,
  Tabs,
  Tab,
  Avatar,
  Tooltip,
  CircularProgress,
  Snackbar,
  Pagination,
  TablePagination,
} from '@mui/material';
import knowledgeService from '../../services/knowledgeService';
import {
  Add,
  CloudUpload,
  MoreVert,
  FolderOpen,
  Description,
  Delete,
  Edit,
  Search,
  FilterList,
  Download,
  Share,
  MenuBook,
  InsertDriveFile,
  PictureAsPdf,
  Article,
  VideoLibrary,
  Image,
  Close,
  CloudDownload,
  Visibility,
  FileUpload,
  CreateNewFolder,
  DeleteOutline,
  DriveFileRenameOutline,
  CheckCircle,
  Error as ErrorIcon,
  Info,
  FindInPage,
  SearchOff,
} from '@mui/icons-material';

function KnowledgeBaseManagement() {
  // 状态管理
  const [knowledgeBases, setKnowledgeBases] = useState([]);
  const [loading, setLoading] = useState(true);
  const [documentsLoading, setDocumentsLoading] = useState(false);

  const [selectedKnowledgeBase, setSelectedKnowledgeBase] = useState(null);
  const [currentTab, setCurrentTab] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState('all');

  // 知识库内容搜索状态
  const [contentSearchQuery, setContentSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [isSearching, setIsSearching] = useState(false);
  const [showSearchResults, setShowSearchResults] = useState(false);

  // 分页状态
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [totalPages, setTotalPages] = useState(1);
  const [totalDocuments, setTotalDocuments] = useState(0);

  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedItem, setSelectedItem] = useState(null);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState('success');
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef(null);

  // 新建知识库表单
  const [newKnowledgeBase, setNewKnowledgeBase] = useState({
    name: '',
    description: '',
    tags: []
  });

  // 编辑知识库表单
  const [editKnowledgeBase, setEditKnowledgeBase] = useState({
    name: '',
    description: '',
    tags: []
  });

  // 上传文件列表
  const [uploadFiles, setUploadFiles] = useState([]);

  // 初始化数据
  useEffect(() => {
    loadKnowledgeBases();
  }, []);

  // 加载知识库列表
  const loadKnowledgeBases = async () => {
    try {
      setLoading(true);
      const response = await knowledgeService.getKnowledgeBases({ page: 1, size: 100 });

      // 处理响应数据格式，确保每个知识库都有files属性
      const kbList = (response.items || response || []).map(kb => ({
        ...kb,
        files: kb.files || []
      }));
      setKnowledgeBases(kbList);
    } catch (error) {
      console.error('加载知识库列表失败:', error);
      showSnackbar('加载知识库列表失败', 'error');
    } finally {
      setLoading(false);
    }
  };

  // 加载知识库文档
  const loadDocuments = async (kbId, page = 1) => {
    try {
      setDocumentsLoading(true);
      const response = await knowledgeService.getDocuments(kbId, { page, size: pageSize });

      // 更新分页信息
      const pagination = response.pagination || {};
      setTotalPages(pagination.pages || 1);
      setTotalDocuments(pagination.total || 0);
      setCurrentPage(page);

      // 更新知识库的文档列表
      const documents = response.items || response || [];
      setKnowledgeBases(prev => prev.map(kb =>
        kb.id === kbId
          ? { ...kb, files: documents, document_count: pagination.total || documents.length }
          : kb
      ));

      // 如果当前选中的知识库是被更新的知识库，也要更新selectedKnowledgeBase
      if (selectedKnowledgeBase && selectedKnowledgeBase.id === kbId) {
        setSelectedKnowledgeBase(prev => ({
          ...prev,
          files: documents,
          document_count: pagination.total || documents.length
        }));
      }
    } catch (error) {
      console.error('加载文档列表失败:', error);
      showSnackbar('加载文档列表失败', 'error');
    } finally {
      setDocumentsLoading(false);
    }
  };

  // 处理分页变化
  const handlePageChange = (event, page) => {
    if (selectedKnowledgeBase) {
      loadDocuments(selectedKnowledgeBase.id, page);
    }
  };

  // 处理每页大小变化
  const handlePageSizeChange = (event) => {
    const newPageSize = parseInt(event.target.value);
    setPageSize(newPageSize);
    setCurrentPage(1); // 重置到第一页
    if (selectedKnowledgeBase) {
      // 直接传递新的pageSize，而不是依赖状态
      loadDocumentsWithSize(selectedKnowledgeBase.id, 1, newPageSize);
    }
  };

  // 带自定义pageSize的loadDocuments函数
  const loadDocumentsWithSize = async (kbId, page = 1, customPageSize = null) => {
    try {
      setDocumentsLoading(true);
      const size = customPageSize || pageSize;
      const response = await knowledgeService.getDocuments(kbId, { page, size });

      // 更新分页信息
      const pagination = response.pagination || {};
      setTotalPages(pagination.pages || 1);
      setTotalDocuments(pagination.total || 0);
      setCurrentPage(page);

      // 更新知识库的文档列表
      const documents = response.items || response || [];
      setKnowledgeBases(prev => prev.map(kb =>
        kb.id === kbId
          ? { ...kb, files: documents, document_count: pagination.total || documents.length }
          : kb
      ));

      // 如果当前选中的知识库是被更新的知识库，也要更新selectedKnowledgeBase
      if (selectedKnowledgeBase && selectedKnowledgeBase.id === kbId) {
        setSelectedKnowledgeBase(prev => ({
          ...prev,
          files: documents,
          document_count: pagination.total || documents.length
        }));
      }
    } catch (error) {
      console.error('加载文档列表失败:', error);
      showSnackbar('加载文档列表失败', 'error');
    } finally {
      setDocumentsLoading(false);
    }
  };

  // 处理菜单打开
  const handleMenuOpen = (event, item) => {
    setAnchorEl(event.currentTarget);
    setSelectedItem(item);
  };

  // 处理菜单关闭
  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedItem(null);
  };

  // 搜索知识库内容
  const handleContentSearch = async (query) => {
    if (!selectedKnowledgeBase || !query.trim()) {
      setSearchResults([]);
      setShowSearchResults(false);
      return;
    }

    try {
      setIsSearching(true);
      const response = await knowledgeService.searchKnowledgeBase(selectedKnowledgeBase.id, {
        query: query.trim(),
        limit: 10
      });

      setSearchResults(response.results || []);
      setShowSearchResults(true);
    } catch (error) {
      console.error('搜索失败:', error);
      showSnackbar('搜索失败，请稍后重试', 'error');
      setSearchResults([]);
      setShowSearchResults(false);
    } finally {
      setIsSearching(false);
    }
  };

  // 清除搜索结果
  const clearSearch = () => {
    setContentSearchQuery('');
    setSearchResults([]);
    setShowSearchResults(false);
  };

  // 创建知识库
  const handleCreateKnowledgeBase = async () => {
    if (!newKnowledgeBase.name) {
      showSnackbar('请输入知识库名称', 'error');
      return;
    }

    try {
      const data = {
        name: newKnowledgeBase.name,
        description: newKnowledgeBase.description || ''
      };

      const response = await knowledgeService.createKnowledgeBase(data);

      // 重新加载知识库列表
      await loadKnowledgeBases();

      setCreateDialogOpen(false);
      setNewKnowledgeBase({ name: '', description: '', tags: [] });
      showSnackbar('知识库创建成功', 'success');
    } catch (error) {
      console.error('创建知识库失败:', error);
      showSnackbar('创建知识库失败', 'error');
    }
  };

  // 编辑知识库
  const handleEditKnowledgeBase = async () => {
    if (!editKnowledgeBase.name) {
      showSnackbar('请输入知识库名称', 'error');
      return;
    }

    try {
      const data = {
        name: editKnowledgeBase.name,
        description: editKnowledgeBase.description || ''
      };

      await knowledgeService.updateKnowledgeBase(selectedItem.id, data);

      // 重新加载知识库列表
      await loadKnowledgeBases();

      setEditDialogOpen(false);
      handleMenuClose();
      showSnackbar('知识库更新成功', 'success');
    } catch (error) {
      console.error('更新知识库失败:', error);
      showSnackbar('更新知识库失败', 'error');
    }
  };

  // 删除知识库
  const handleDeleteKnowledgeBase = async () => {
    try {
      await knowledgeService.deleteKnowledgeBase(selectedItem.id);

      // 重新加载知识库列表
      await loadKnowledgeBases();

      setDeleteDialogOpen(false);
      handleMenuClose();

      if (selectedKnowledgeBase?.id === selectedItem.id) {
        setSelectedKnowledgeBase(null);
      }

      showSnackbar('知识库删除成功', 'success');
    } catch (error) {
      console.error('删除知识库失败:', error);
      showSnackbar('删除知识库失败', 'error');
    }
  };

  // 处理文件选择
  const handleFileSelect = (event) => {
    const files = Array.from(event.target.files);
    setUploadFiles(files.map(file => ({
      file,
      name: file.name,
      size: formatFileSize(file.size),
      status: 'pending'
    })));
  };

  // 处理拖拽事件
  const handleDragOver = (event) => {
    event.preventDefault();
    event.stopPropagation();
  };

  const handleDragEnter = (event) => {
    event.preventDefault();
    event.stopPropagation();
  };

  const handleDragLeave = (event) => {
    event.preventDefault();
    event.stopPropagation();
  };

  const handleDrop = (event) => {
    event.preventDefault();
    event.stopPropagation();

    const files = Array.from(event.dataTransfer.files);
    if (files.length > 0) {
      setUploadFiles(files.map(file => ({
        file,
        name: file.name,
        size: formatFileSize(file.size),
        status: 'pending'
      })));
    }
  };

  // 上传文件到知识库
  const handleUploadFiles = async () => {
    if (!selectedKnowledgeBase) {
      showSnackbar('请先选择一个知识库', 'error');
      return;
    }

    setIsUploading(true);
    setUploadProgress(0);

    try {
      const totalFiles = uploadFiles.length;
      let completedFiles = 0;

      // 逐个上传文件
      for (const fileData of uploadFiles) {
        try {
          // 读取文件内容
          const fileContent = await new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => resolve(e.target.result);
            reader.onerror = reject;
            reader.readAsText(fileData.file);
          });

          // 使用文档创建API而不是文件上传API
          await knowledgeService.uploadDocument(selectedKnowledgeBase.id, {
            title: fileData.file.name,
            content: fileContent,
            file_type: fileData.file.type || 'text/plain'
          });

          completedFiles++;
          setUploadProgress((completedFiles / totalFiles) * 100);
        } catch (error) {
          console.error(`上传文件 ${fileData.name} 失败:`, error);
          showSnackbar(`上传文件 ${fileData.name} 失败`, 'error');
        }
      }

      // 重新加载文档列表
      await loadDocuments(selectedKnowledgeBase.id);

      setUploadDialogOpen(false);
      setUploadFiles([]);
      setUploadProgress(0);
      showSnackbar(`成功上传 ${completedFiles} 个文件`, 'success');
    } catch (error) {
      console.error('上传文件失败:', error);
      showSnackbar('上传文件失败', 'error');
    } finally {
      setIsUploading(false);
    }
  };

  // 删除文件
  const handleDeleteFile = async (kbId, fileId) => {
    try {
      await knowledgeService.deleteDocument(fileId);

      // 重新加载文档列表
      await loadDocuments(kbId);

      showSnackbar('文件删除成功', 'success');
    } catch (error) {
      console.error('删除文件失败:', error);
      showSnackbar('删除文件失败', 'error');
    }
  };

  // 处理下载文件
  const handleDownloadFile = async (file) => {
    try {
      const token = localStorage.getItem('ai_seo_auth_token');
      if (!token) {
        showSnackbar('请先登录', 'error');
        return;
      }

      // 使用fetch下载文件
      const response = await fetch(`http://localhost:8000/api/v1/knowledge-bases/${selectedKnowledgeBase.id}/documents/${file.id}/download`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error(`下载失败: ${response.status}`);
      }

      // 获取文件内容
      const blob = await response.blob();

      // 创建下载链接
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = file.title || 'document.txt';

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // 清理URL对象
      window.URL.revokeObjectURL(url);

      showSnackbar('文件下载成功', 'success');
    } catch (error) {
      console.error('下载文件失败:', error);
      showSnackbar('下载文件失败', 'error');
    }
  };

  // 显示提示消息
  const showSnackbar = (message, severity = 'success') => {
    setSnackbarMessage(message);
    setSnackbarSeverity(severity);
    setSnackbarOpen(true);
  };

  // 格式化文件大小
  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return Math.round(bytes / Math.pow(k, i) * 100) / 100 + ' ' + sizes[i];
  };

  // 获取文件类型
  const getFileType = (filename) => {
    const ext = filename.split('.').pop().toLowerCase();
    const typeMap = {
      pdf: 'pdf',
      doc: 'doc', docx: 'doc',
      xls: 'excel', xlsx: 'excel',
      ppt: 'ppt', pptx: 'ppt',
      jpg: 'image', jpeg: 'image', png: 'image', gif: 'image',
      mp4: 'video', avi: 'video', mov: 'video',
      txt: 'text', md: 'text'
    };
    return typeMap[ext] || 'file';
  };

  // 获取文件图标
  const getFileIcon = (type) => {
    const iconMap = {
      pdf: <PictureAsPdf sx={{ color: '#d32f2f' }} />,
      doc: <Article sx={{ color: '#1976d2' }} />,
      excel: <InsertDriveFile sx={{ color: '#388e3c' }} />,
      ppt: <InsertDriveFile sx={{ color: '#f57c00' }} />,
      image: <Image sx={{ color: '#7b1fa2' }} />,
      video: <VideoLibrary sx={{ color: '#c62828' }} />,
      text: <Description sx={{ color: '#616161' }} />,
      file: <InsertDriveFile sx={{ color: '#757575' }} />
    };
    return iconMap[type] || iconMap.file;
  };

  return (
    <Box sx={{ width: '100%', height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* 顶部操作栏 */}
      <Box sx={{ 
        p: 3, 
        backgroundColor: '#fff',
        borderBottom: '1px solid #e0e0e0'
      }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Box>
            <Typography variant="h5" sx={{ fontWeight: 600, color: '#333', mb: 0.5 }}>
              知识库管理
            </Typography>
            <Typography variant="body2" sx={{ color: '#999' }}>
              管理企业知识库，为AI内容生成提供专业资料
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', gap: 2 }}>
            <Button
              variant="outlined"
              startIcon={<CloudUpload />}
              onClick={() => setUploadDialogOpen(true)}
              disabled={!selectedKnowledgeBase}
            >
              上传文件
            </Button>
            <Button
              variant="contained"
              startIcon={<Add />}
              onClick={() => setCreateDialogOpen(true)}
            >
              新建知识库
            </Button>
          </Box>
        </Box>

        {/* 统计信息 */}
        <Box sx={{ display: 'flex', gap: 4, mt: 2 }}>
          <Box>
            <Typography variant="h4" sx={{ fontWeight: 500, color: '#1976d2' }}>
              {loading ? '-' : knowledgeBases.length}
            </Typography>
            <Typography variant="body2" sx={{ color: '#666' }}>知识库总数</Typography>
          </Box>
          <Box>
            <Typography variant="h4" sx={{ fontWeight: 500, color: '#388e3c' }}>
              {loading ? '-' : knowledgeBases.reduce((sum, kb) => sum + (kb.document_count || 0), 0)}
            </Typography>
            <Typography variant="body2" sx={{ color: '#666' }}>文档总数</Typography>
          </Box>
          <Box>
            <Typography variant="h4" sx={{ fontWeight: 500, color: '#f57c00' }}>
              {loading ? '-' : knowledgeBases.reduce((sum, kb) => sum + (kb.vector_count || 0), 0)}
            </Typography>
            <Typography variant="body2" sx={{ color: '#666' }}>向量数量</Typography>
          </Box>
        </Box>
      </Box>

      {/* 主内容区 */}
      <Box sx={{ display: 'flex', flex: 1, overflow: 'hidden' }}>
        {/* 左侧知识库列表 */}
        <Box sx={{ 
          width: 320, 
          borderRight: '1px solid #e0e0e0',
          backgroundColor: '#fafafa',
          overflow: 'auto'
        }}>
          <Box sx={{ p: 2 }}>
            <TextField
              fullWidth
              size="small"
              placeholder="搜索知识库..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              InputProps={{
                startAdornment: <Search sx={{ mr: 1, color: '#999' }} />
              }}
            />
          </Box>

          <List sx={{ px: 1 }} data-testid="knowledge-base-list">
            {loading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
                <CircularProgress />
              </Box>
            ) : knowledgeBases
              .filter(kb => kb.name && kb.name.toLowerCase().includes(searchQuery.toLowerCase()))
              .map((kb) => (
                <ListItem
                  key={kb.id}
                  button
                  selected={selectedKnowledgeBase?.id === kb.id}
                  onClick={() => {
                    // 确保知识库对象有必要的属性
                    const kbWithDefaults = {
                      ...kb,
                      files: kb.files || [],
                      tags: kb.tags || []
                    };
                    setSelectedKnowledgeBase(kbWithDefaults);
                    // 重置分页状态
                    setCurrentPage(1);
                    // 总是加载该知识库的文档以确保数据是最新的
                    loadDocuments(kb.id, 1);
                  }}
                  data-testid="knowledge-base-item"
                  sx={{
                    borderRadius: 1,
                    mb: 1,
                    '&.Mui-selected': {
                      backgroundColor: 'rgba(25, 118, 210, 0.08)',
                      '&:hover': {
                        backgroundColor: 'rgba(25, 118, 210, 0.12)'
                      }
                    }
                  }}
                >
                  <ListItemIcon>
                    <MenuBook sx={{ color: selectedKnowledgeBase?.id === kb.id ? '#1976d2' : '#666' }} />
                  </ListItemIcon>
                  <ListItemText
                    primary={kb.name}
                    secondary={`${kb.document_count || 0} 个文件 · ${kb.vector_count || 0} 个向量`}
                    primaryTypographyProps={{
                      fontWeight: selectedKnowledgeBase?.id === kb.id ? 600 : 400
                    }}
                  />
                  <IconButton
                    size="small"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleMenuOpen(e, kb);
                    }}
                  >
                    <MoreVert fontSize="small" />
                  </IconButton>
                </ListItem>
              ))}
          </List>
        </Box>

        {/* 右侧文件列表 */}
        <Box sx={{ flex: 1, overflow: 'auto', backgroundColor: '#fff' }}>
          {selectedKnowledgeBase ? (
            <Box sx={{ p: 3 }}>
              {/* 知识库信息 */}
              <Box sx={{ mb: 3 }}>
                <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                  {selectedKnowledgeBase.name}
                </Typography>
                <Typography variant="body2" sx={{ color: '#666', mb: 2 }}>
                  {selectedKnowledgeBase.description}
                </Typography>
                <Box sx={{ display: 'flex', gap: 1 }}>
                  {(selectedKnowledgeBase.tags || []).map((tag) => (
                    <Chip key={tag} label={tag} size="small" />
                  ))}
                </Box>
              </Box>

              <Divider sx={{ mb: 3 }} />

              {/* 知识库内容搜索 */}
              <Box sx={{ mb: 3 }}>
                <TextField
                  fullWidth
                  size="small"
                  placeholder="搜索知识库内容..."
                  value={contentSearchQuery}
                  onChange={(e) => setContentSearchQuery(e.target.value)}
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') {
                      handleContentSearch(contentSearchQuery);
                    }
                  }}
                  InputProps={{
                    startAdornment: (
                      <IconButton
                        size="small"
                        onClick={() => handleContentSearch(contentSearchQuery)}
                        disabled={isSearching}
                      >
                        {isSearching ? <CircularProgress size={16} /> : <FindInPage />}
                      </IconButton>
                    ),
                    endAdornment: showSearchResults && (
                      <IconButton
                        size="small"
                        onClick={clearSearch}
                      >
                        <Close />
                      </IconButton>
                    )
                  }}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      backgroundColor: '#f8f9fa'
                    }
                  }}
                />
              </Box>

              {/* 搜索结果 */}
              {showSearchResults && (
                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle2" sx={{ mb: 2, color: '#666' }}>
                    搜索结果 ({searchResults.length} 条)
                  </Typography>
                  {searchResults.length > 0 ? (
                    <Grid container spacing={2}>
                      {searchResults.map((result, index) => (
                        <Grid item xs={12} key={index}>
                          <Paper sx={{ p: 2, border: '1px solid #e0e0e0' }}>
                            <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
                              {result.title}
                            </Typography>
                            <Typography variant="body2" sx={{ color: '#666', mb: 1, lineHeight: 1.5 }}>
                              {result.content.length > 200
                                ? `${result.content.substring(0, 200)}...`
                                : result.content}
                            </Typography>
                            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                              <Chip
                                label={`相似度: ${(result.similarity_score * 100).toFixed(1)}%`}
                                size="small"
                                color="primary"
                                variant="outlined"
                              />
                              <Typography variant="caption" sx={{ color: '#999' }}>
                                文档ID: {result.document_id.substring(0, 8)}...
                              </Typography>
                            </Box>
                          </Paper>
                        </Grid>
                      ))}
                    </Grid>
                  ) : (
                    <Paper sx={{ p: 3, textAlign: 'center', backgroundColor: '#f8f9fa' }}>
                      <SearchOff sx={{ fontSize: 48, color: '#ccc', mb: 1 }} />
                      <Typography variant="body2" color="text.secondary">
                        没有找到相关内容
                      </Typography>
                    </Paper>
                  )}
                  <Divider sx={{ mt: 3, mb: 3 }} />
                </Box>
              )}

              {/* 文件筛选 */}
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Typography variant="subtitle1" sx={{ fontWeight: 500 }}>
                  文件列表 ({selectedKnowledgeBase.files?.length || selectedKnowledgeBase.document_count || 0})
                  {documentsLoading && <CircularProgress size={16} sx={{ ml: 1 }} />}
                </Typography>
                <FormControl size="small" sx={{ minWidth: 120 }}>
                  <InputLabel>文件类型</InputLabel>
                  <Select
                    value={filterType}
                    onChange={(e) => setFilterType(e.target.value)}
                    label="文件类型"
                  >
                    <MenuItem value="all">全部</MenuItem>
                    <MenuItem value="pdf">PDF</MenuItem>
                    <MenuItem value="doc">文档</MenuItem>
                    <MenuItem value="image">图片</MenuItem>
                    <MenuItem value="video">视频</MenuItem>
                    <MenuItem value="text">文本</MenuItem>
                  </Select>
                </FormControl>
              </Box>

              {/* 文件列表 */}
              <Grid container spacing={2}>
                {(selectedKnowledgeBase.files || [])
                  .filter(file => filterType === 'all' || file.file_type === filterType)
                  .map((file) => (
                    <Grid item xs={12} sm={6} md={4} key={file.id}>
                      <Card sx={{
                        height: '100%',
                        '&:hover': { boxShadow: 2 }
                      }}>
                        <CardContent>
                          <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 2 }}>
                            {getFileIcon(file.file_type || getFileType(file.title || ''))}
                            <Box sx={{ flex: 1, ml: 1.5 }}>
                              <Typography variant="body2" sx={{ fontWeight: 500, mb: 0.5 }}>
                                {file.title || file.name || '未命名文档'}
                              </Typography>
                              <Typography variant="caption" sx={{ color: '#999' }}>
                                {file.file_type || 'text'} · {new Date(file.created_at).toLocaleDateString()}
                              </Typography>
                              {file.embedding_status && (
                                <Chip
                                  label={file.embedding_status}
                                  size="small"
                                  color={file.embedding_status === 'COMPLETED' ? 'success' : 'default'}
                                  sx={{ mt: 0.5, fontSize: '0.7rem' }}
                                />
                              )}
                            </Box>
                            <IconButton
                              size="small"
                              onClick={() => handleDeleteFile(selectedKnowledgeBase.id, file.id)}
                            >
                              <Delete fontSize="small" />
                            </IconButton>
                          </Box>
                          <Box sx={{ display: 'flex', gap: 1 }}>
                            <Button size="small" startIcon={<Visibility />}>
                              预览
                            </Button>
                            <Button
                              size="small"
                              startIcon={<Download />}
                              onClick={() => handleDownloadFile(file)}
                            >
                              下载
                            </Button>
                          </Box>
                        </CardContent>
                      </Card>
                    </Grid>
                  ))}
              </Grid>

              {(!selectedKnowledgeBase.files || selectedKnowledgeBase.files.length === 0) && (
                <Box sx={{ 
                  textAlign: 'center', 
                  py: 8,
                  backgroundColor: '#fafafa',
                  borderRadius: 2
                }}>
                  <FolderOpen sx={{ fontSize: 48, color: '#ccc', mb: 2 }} />
                  <Typography variant="body1" sx={{ color: '#999' }}>
                    暂无文件
                  </Typography>
                  <Button
                    variant="outlined"
                    startIcon={<CloudUpload />}
                    sx={{ mt: 2 }}
                    onClick={() => setUploadDialogOpen(true)}
                  >
                    上传第一个文件
                  </Button>
                </Box>
              )}

              {/* 分页组件 */}
              {selectedKnowledgeBase.files && selectedKnowledgeBase.files.length > 0 && totalPages > 1 && (
                <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
                  <Pagination
                    count={totalPages}
                    page={currentPage}
                    onChange={handlePageChange}
                    color="primary"
                    showFirstButton
                    showLastButton
                  />
                </Box>
              )}

              {/* 每页显示数量选择 */}
              {selectedKnowledgeBase.files && selectedKnowledgeBase.files.length > 0 && (
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 2 }}>
                  <Typography variant="body2" sx={{ color: '#666' }}>
                    共 {totalDocuments} 个文件，第 {currentPage} 页，共 {totalPages} 页
                  </Typography>
                  <FormControl size="small" sx={{ minWidth: 120 }}>
                    <InputLabel>每页显示</InputLabel>
                    <Select
                      value={pageSize}
                      onChange={handlePageSizeChange}
                      label="每页显示"
                    >
                      <MenuItem value={5}>5</MenuItem>
                      <MenuItem value={10}>10</MenuItem>
                      <MenuItem value={20}>20</MenuItem>
                      <MenuItem value={50}>50</MenuItem>
                    </Select>
                  </FormControl>
                </Box>
              )}
            </Box>
          ) : (
            <Box sx={{ 
              display: 'flex', 
              flexDirection: 'column',
              alignItems: 'center', 
              justifyContent: 'center',
              height: '100%',
              color: '#999'
            }}>
              <MenuBook sx={{ fontSize: 64, mb: 2, color: '#e0e0e0' }} />
              <Typography variant="h6" sx={{ mb: 1 }}>
                选择一个知识库
              </Typography>
              <Typography variant="body2">
                从左侧列表中选择知识库查看文件
              </Typography>
            </Box>
          )}
        </Box>
      </Box>

      {/* 右键菜单 */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => {
          setEditKnowledgeBase({
            name: selectedItem.name,
            description: selectedItem.description,
            tags: selectedItem.tags
          });
          setEditDialogOpen(true);
        }}>
          <Edit fontSize="small" sx={{ mr: 1 }} />
          编辑
        </MenuItem>
        <MenuItem onClick={() => setDeleteDialogOpen(true)}>
          <Delete fontSize="small" sx={{ mr: 1 }} />
          删除
        </MenuItem>
      </Menu>

      {/* 创建知识库对话框 */}
      <Dialog open={createDialogOpen} onClose={() => setCreateDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>新建知识库</DialogTitle>
        <DialogContent>
          <TextField
            fullWidth
            label="知识库名称"
            value={newKnowledgeBase.name}
            onChange={(e) => setNewKnowledgeBase({ ...newKnowledgeBase, name: e.target.value })}
            sx={{ mb: 2, mt: 1 }}
          />
          <TextField
            fullWidth
            label="描述"
            multiline
            rows={3}
            value={newKnowledgeBase.description}
            onChange={(e) => setNewKnowledgeBase({ ...newKnowledgeBase, description: e.target.value })}
            sx={{ mb: 2 }}
          />
          <TextField
            fullWidth
            label="标签"
            placeholder="输入标签，用逗号分隔"
            value={newKnowledgeBase.tags.join(', ')}
            onChange={(e) => setNewKnowledgeBase({ 
              ...newKnowledgeBase, 
              tags: e.target.value.split(',').map(tag => tag.trim()).filter(tag => tag)
            })}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCreateDialogOpen(false)}>取消</Button>
          <Button variant="contained" onClick={handleCreateKnowledgeBase}>创建</Button>
        </DialogActions>
      </Dialog>

      {/* 编辑知识库对话框 */}
      <Dialog open={editDialogOpen} onClose={() => setEditDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>编辑知识库</DialogTitle>
        <DialogContent>
          <TextField
            fullWidth
            label="知识库名称"
            value={editKnowledgeBase.name}
            onChange={(e) => setEditKnowledgeBase({ ...editKnowledgeBase, name: e.target.value })}
            sx={{ mb: 2, mt: 1 }}
          />
          <TextField
            fullWidth
            label="描述"
            multiline
            rows={3}
            value={editKnowledgeBase.description}
            onChange={(e) => setEditKnowledgeBase({ ...editKnowledgeBase, description: e.target.value })}
            sx={{ mb: 2 }}
          />
          <TextField
            fullWidth
            label="标签"
            placeholder="输入标签，用逗号分隔"
            value={editKnowledgeBase.tags?.join(', ') || ''}
            onChange={(e) => setEditKnowledgeBase({ 
              ...editKnowledgeBase, 
              tags: e.target.value.split(',').map(tag => tag.trim()).filter(tag => tag)
            })}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditDialogOpen(false)}>取消</Button>
          <Button variant="contained" onClick={handleEditKnowledgeBase}>保存</Button>
        </DialogActions>
      </Dialog>

      {/* 上传文件对话框 */}
      <Dialog open={uploadDialogOpen} onClose={() => setUploadDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          上传文件到 {selectedKnowledgeBase?.name}
        </DialogTitle>
        <DialogContent>
          <input
            ref={fileInputRef}
            type="file"
            multiple
            style={{ display: 'none' }}
            onChange={handleFileSelect}
          />
          
          {uploadFiles.length === 0 ? (
            <Box sx={{
              border: '2px dashed #ccc',
              borderRadius: 2,
              p: 4,
              textAlign: 'center',
              cursor: 'pointer',
              '&:hover': { borderColor: '#1976d2' }
            }}
            onClick={() => fileInputRef.current?.click()}
            onDragOver={handleDragOver}
            onDragEnter={handleDragEnter}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
            >
              <CloudUpload sx={{ fontSize: 48, color: '#ccc', mb: 2 }} />
              <Typography variant="body1" sx={{ mb: 1 }}>
                点击或拖拽文件到这里
              </Typography>
              <Typography variant="body2" sx={{ color: '#999' }}>
                支持多文件上传，单个文件最大 100MB
              </Typography>
            </Box>
          ) : (
            <Box>
              <List>
                {uploadFiles.map((file, index) => (
                  <ListItem key={index}>
                    <ListItemIcon>
                      {getFileIcon(getFileType(file.name))}
                    </ListItemIcon>
                    <ListItemText
                      primary={file.name}
                      secondary={file.size}
                    />
                    <IconButton
                      size="small"
                      onClick={() => setUploadFiles(uploadFiles.filter((_, i) => i !== index))}
                    >
                      <Close />
                    </IconButton>
                  </ListItem>
                ))}
              </List>
              <Button
                fullWidth
                variant="outlined"
                onClick={() => fileInputRef.current?.click()}
                sx={{ mt: 2 }}
              >
                添加更多文件
              </Button>
            </Box>
          )}

          {isUploading && (
            <Box sx={{ mt: 2 }}>
              <LinearProgress variant="determinate" value={uploadProgress} />
              <Typography variant="body2" sx={{ mt: 1, textAlign: 'center' }}>
                上传中... {Math.round(uploadProgress)}%
              </Typography>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setUploadDialogOpen(false)} disabled={isUploading}>
            取消
          </Button>
          <Button 
            variant="contained" 
            onClick={handleUploadFiles}
            disabled={uploadFiles.length === 0 || isUploading}
            startIcon={isUploading ? <CircularProgress size={16} /> : <CloudUpload />}
          >
            {isUploading ? '上传中...' : '开始上传'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* 删除确认对话框 */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>确认删除</DialogTitle>
        <DialogContent>
          <Typography>
            确定要删除知识库 "{selectedItem?.name}" 吗？此操作不可恢复。
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>取消</Button>
          <Button variant="contained" color="error" onClick={handleDeleteKnowledgeBase}>
            删除
          </Button>
        </DialogActions>
      </Dialog>

      {/* 提示消息 */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={3000}
        onClose={() => setSnackbarOpen(false)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={() => setSnackbarOpen(false)}
          severity={snackbarSeverity}
          sx={{ width: '100%' }}
        >
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </Box>
  );
}

export default KnowledgeBaseManagement;