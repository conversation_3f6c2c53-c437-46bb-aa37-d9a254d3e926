import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Button,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Avatar,
  Divider,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
} from '@mui/material';
import {
  Storage,
  Api,
  TrendingUp,
  Assignment,
  ArrowForward,
  MoreVert,
  Announcement,
  Campaign,
  NotificationsActive,
  Info,
  Warning,
  CheckCircle,
  AccessTime,
  Add,
  BarChart,
  FileDownload,
  Article,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import announcementService from '../../services/announcementService';
import { AppConfig } from '../../config/app-config';

function EnterpriseDashboardNew({ onNavigate }) {
  const navigate = useNavigate();
  const [announcements, setAnnouncements] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedAnnouncement, setSelectedAnnouncement] = useState(null);
  const [announcementDetailOpen, setAnnouncementDetailOpen] = useState(false);
  const [detailLoading, setDetailLoading] = useState(false);

  // 模拟最近文章数据
  const recentArticles = [
    { id: 1, title: 'AI技术在企业数字化转型中的应用', status: '已发布', views: 1234, date: '2024-01-15', platform: '豆包' },
    { id: 2, title: '如何提升企业品牌在AI搜索中的排名', status: '审核中', views: 856, date: '2024-01-14', platform: 'Kimi' },
    { id: 3, title: '2024年企业营销新趋势分析', status: '已发布', views: 2156, date: '2024-01-13', platform: 'ChatGPT' },
    { id: 4, title: '企业内容营销策略完整指南', status: '草稿', views: 0, date: '2024-01-12', platform: '文心一言' },
    { id: 5, title: '数据驱动的营销决策方法论', status: '已发布', views: 3421, date: '2024-01-11', platform: '豆包' },
  ];

  // 获取企业公告数据
  const fetchAnnouncements = async () => {
    try {
      setLoading(true);


      const data = await announcementService.getAnnouncements({
        page: 1,
        page_size: 5, // 只获取前5条用于展示
        target_audience: 'enterprise'
      });



      // 检查不同的数据结构可能性
      let items = null;

      if (data && data.success && data.data && data.data.items) {
        // 标准结构：{success: true, data: {items: [], pagination: {}}}

        items = data.data.items;
      } else if (data && data.items && Array.isArray(data.items)) {
        // 直接包含items的结构：{items: [], pagination: {}, statistics: null}

        items = data.items;
      } else if (data && Array.isArray(data)) {
        // 直接数组结构：[{...}, {...}]

        items = data;
      } else {

      }

      if (items && Array.isArray(items)) {


        // 转换API数据格式为组件需要的格式
        const formattedAnnouncements = items.map(item => ({
          id: item.id,
          title: item.title,
          content: item.content || item.summary || '',
          type: mapAnnouncementType(item.type),
          date: formatDate(item.publish_time || item.created_at),
          priority: mapPriority(item.priority),
          author: item.creator_name || '系统管理员'
        }));


        setAnnouncements(formattedAnnouncements);
      } else {

        setAnnouncements([]);
      }
    } catch (error) {

      setAnnouncements([]);
    } finally {
      setLoading(false);
    }
  };

  // 映射公告类型
  const mapAnnouncementType = (apiType) => {
    const typeMap = {
      'system': 'warning',
      'maintenance': 'warning',
      'feature': 'success',
      'promotion': 'announcement',
      'notice': 'info'
    };
    return typeMap[apiType] || 'info';
  };

  // 映射优先级
  const mapPriority = (apiPriority) => {
    const priorityMap = {
      'low': 'low',
      'normal': 'medium',
      'high': 'high',
      'urgent': 'high'
    };
    return priorityMap[apiPriority] || 'medium';
  };

  // 格式化日期
  const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // 获取公告详情
  const fetchAnnouncementDetail = async (announcementId) => {
    try {
      setDetailLoading(true);


      const data = await announcementService.getAnnouncementById(announcementId);


      // 检查不同的响应格式
      let item = null;
      if (data && data.success && data.data) {
        item = data.data;
      } else if (data && data.data) {
        // 有些API直接返回data
        item = data.data;
      } else if (data && data.success !== false && data.id) {
        // 直接返回公告对象
        item = data;
      } else if (data && data.items && Array.isArray(data.items) && data.items.length > 0) {
        // 处理可能的数组响应格式
        item = data.items[0];
      } else {

        alert('获取公告详情失败');
        return;
      }

      if (item) {
        // 转换API数据格式
        const formattedAnnouncement = {
          id: item.id,
          title: item.title,
          content: item.content || item.summary || '',
          summary: item.summary || '',
          type: mapAnnouncementType(item.type),
          date: formatDate(item.publish_time || item.created_at),
          priority: mapPriority(item.priority),
          author: item.creator_name || '系统管理员',
          viewCount: item.view_count || 0,
          targetAudience: item.target_audience,
          status: item.status,
          isPinned: item.is_pinned,
          isPopup: item.is_popup,
          expireTime: item.expire_time ? formatDate(item.expire_time) : null
        };


        setSelectedAnnouncement(formattedAnnouncement);
        setAnnouncementDetailOpen(true);
      } else {

        alert('获取公告详情失败');
      }
    } catch (error) {

      alert('获取公告详情失败，请稍后重试');
    } finally {
      setDetailLoading(false);
    }
  };

  // 处理公告点击事件
  const handleAnnouncementClick = (announcement) => {

    fetchAnnouncementDetail(announcement.id);
  };

  // 关闭公告详情弹窗
  const handleCloseAnnouncementDetail = () => {
    setAnnouncementDetailOpen(false);
    setSelectedAnnouncement(null);
  };

  // 组件挂载时获取公告数据
  useEffect(() => {
    fetchAnnouncements();
  }, []);

  const getAnnouncementIcon = (type) => {
    switch(type) {
      case 'warning':
        return <Warning sx={{ color: '#f59e0b' }} />;
      case 'info':
        return <Info sx={{ color: '#3b82f6' }} />;
      case 'success':
        return <CheckCircle sx={{ color: '#10b981' }} />;
      case 'announcement':
        return <Campaign sx={{ color: '#8b5cf6' }} />;
      default:
        return <Announcement sx={{ color: '#6b7280' }} />;
    }
  };

  const getAnnouncementColor = (type) => {
    switch(type) {
      case 'warning':
        return '#fef3c7';
      case 'info':
        return '#dbeafe';
      case 'success':
        return '#d1fae5';
      case 'announcement':
        return '#ede9fe';
      default:
        return '#f3f4f6';
    }
  };

  return (
    <Box sx={{ width: '100%', backgroundColor: 'white', minHeight: '100vh' }}>
      {/* 顶部欢迎区域 - 与用户后台风格一致 */}
      <Box sx={{
        borderBottom: '1px solid #f0f0f0',
        backgroundColor: 'white'
      }}>
        <Container maxWidth="xl">
          <Box sx={{ py: 5 }}>
            <Typography variant="h3" sx={{
              fontWeight: 700,
              color: '#1a1a1a',
              mb: 1,
              fontSize: { xs: '2rem', md: '2.5rem' }
            }}>
              企业控制台
            </Typography>
            <Typography variant="body1" sx={{
              color: '#6b7280',
              fontSize: '1.125rem'
            }}>
              实时监控企业内容表现，优化AI搜索排名
            </Typography>
          </Box>
        </Container>
      </Box>

      {/* 核心数据统计栏 - 与用户后台风格一致 */}
      <Box sx={{
        borderBottom: '1px solid #f0f0f0',
        backgroundColor: '#fafafa'
      }}>
        <Container maxWidth="xl">
          <Box sx={{ py: 4 }}>
            <Grid container spacing={4}>
              <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Box sx={{
                    width: 44,
                    height: 44,
                    borderRadius: '12px',
                    backgroundColor: '#eff6ff',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <Storage sx={{ color: '#3b82f6', fontSize: 22 }} />
                  </Box>
                  <Box>
                    <Typography variant="caption" sx={{ 
                      color: '#9ca3af', 
                      fontSize: '0.75rem',
                      fontWeight: 500,
                      textTransform: 'uppercase',
                      letterSpacing: '0.05em'
                    }}>
                      监控项目
                    </Typography>
                    <Typography variant="h6" sx={{ 
                      color: '#1a1a1a',
                      fontWeight: 600,
                      fontSize: '1.5rem',
                      mt: 0.5
                    }}>
                      12
                    </Typography>
                  </Box>
                </Box>
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Box sx={{
                    width: 44,
                    height: 44,
                    borderRadius: '12px',
                    backgroundColor: '#f0fdf4',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <Api sx={{ color: '#10b981', fontSize: 22 }} />
                  </Box>
                  <Box>
                    <Typography variant="caption" sx={{ 
                      color: '#9ca3af', 
                      fontSize: '0.75rem',
                      fontWeight: 500,
                      textTransform: 'uppercase',
                      letterSpacing: '0.05em'
                    }}>
                      当月AI调用
                    </Typography>
                    <Typography variant="h6" sx={{ 
                      color: '#1a1a1a',
                      fontWeight: 600,
                      fontSize: '1.5rem',
                      mt: 0.5
                    }}>
                      8,456
                    </Typography>
                  </Box>
                </Box>
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Box sx={{
                    width: 44,
                    height: 44,
                    borderRadius: '12px',
                    backgroundColor: '#fef3c7',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <TrendingUp sx={{ color: '#f59e0b', fontSize: 22 }} />
                  </Box>
                  <Box>
                    <Typography variant="caption" sx={{ 
                      color: '#9ca3af', 
                      fontSize: '0.75rem',
                      fontWeight: 500,
                      textTransform: 'uppercase',
                      letterSpacing: '0.05em'
                    }}>
                      平均排名
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'baseline', gap: 1 }}>
                      <Typography variant="h6" sx={{ 
                        color: '#1a1a1a',
                        fontWeight: 600,
                        fontSize: '1.5rem',
                        mt: 0.5
                      }}>
                        6.5
                      </Typography>
                      <Typography variant="caption" sx={{ color: '#10b981', fontWeight: 600 }}>
                        ↑15%
                      </Typography>
                    </Box>
                  </Box>
                </Box>
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Box sx={{
                    width: 44,
                    height: 44,
                    borderRadius: '12px',
                    backgroundColor: '#fce7f3',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <Assignment sx={{ color: '#ec4899', fontSize: 22 }} />
                  </Box>
                  <Box>
                    <Typography variant="caption" sx={{ 
                      color: '#9ca3af', 
                      fontSize: '0.75rem',
                      fontWeight: 500,
                      textTransform: 'uppercase',
                      letterSpacing: '0.05em'
                    }}>
                      进行中需求
                    </Typography>
                    <Typography variant="h6" sx={{ 
                      color: '#1a1a1a',
                      fontWeight: 600,
                      fontSize: '1.5rem',
                      mt: 0.5
                    }}>
                      5
                    </Typography>
                  </Box>
                </Box>
              </Grid>
            </Grid>
          </Box>
        </Container>
      </Box>

      {/* 主要内容区域 */}
      <Box sx={{ backgroundColor: 'white' }}>
        <Container maxWidth="xl">
          <Box sx={{ py: 5 }}>
            <Grid container spacing={4}>
              {/* 公告栏 */}
              <Grid item xs={12} lg={6}>
                <Card sx={{ 
                  borderRadius: 2,
                  border: '1px solid #e5e7eb',
                  boxShadow: 'none',
                  height: '100%',
                  minHeight: 400,
                  maxHeight: 500,
                  display: 'flex',
                  flexDirection: 'column'
                }}>
                  <CardContent sx={{ p: 2, height: '100%', display: 'flex', flexDirection: 'column' }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Avatar sx={{ 
                          backgroundColor: '#eff6ff',
                          width: 28,
                          height: 28
                        }}>
                          <NotificationsActive sx={{ color: '#3b82f6', fontSize: 18 }} />
                        </Avatar>
                        <Box>
                          <Typography variant="subtitle1" sx={{ 
                            fontWeight: 600,
                            color: '#1a1a1a',
                            fontSize: '1rem'
                          }}>
                            系统公告
                          </Typography>
                        </Box>
                      </Box>
                      <Button
                        size="small"
                        variant="text"
                        sx={{ color: '#6b7280', fontSize: '0.75rem' }}
                        onClick={() => {

                          // 使用传入的导航函数跳转到公告页面
                          if (onNavigate) {
                            onNavigate('announcements');
                          } else {
                            // 备用方案：直接导航
                            navigate('/control-center/enterprise#announcements');
                          }
                        }}
                      >
                        查看全部
                      </Button>
                    </Box>

                    {/* 公告列表 */}
                    <List sx={{ p: 0, flex: 1, overflowY: 'auto' }}>
                      {loading ? (
                        // 加载状态
                        <Box sx={{ display: 'flex', justifyContent: 'center', py: 3 }}>
                          <Typography variant="body2" color="text.secondary">
                            加载中...
                          </Typography>
                        </Box>
                      ) : announcements.length === 0 ? (
                        // 空状态
                        <Box sx={{ display: 'flex', justifyContent: 'center', py: 3 }}>
                          <Typography variant="body2" color="text.secondary">
                            暂无公告
                          </Typography>
                        </Box>
                      ) : (
                        // 公告列表
                        announcements.slice(0, 3).map((announcement, index) => (
                        <React.Fragment key={announcement.id}>
                          <ListItem
                            sx={{
                              px: 1,
                              py: 0.5,
                              borderRadius: 1,
                              mb: 0.5,
                              backgroundColor: getAnnouncementColor(announcement.type),
                              '&:hover': {
                                backgroundColor: announcement.type === 'warning' ? '#fde68a' :
                                               announcement.type === 'info' ? '#bfdbfe' :
                                               announcement.type === 'success' ? '#a7f3d0' :
                                               announcement.type === 'announcement' ? '#ddd6fe' :
                                               '#e5e7eb',
                                cursor: 'pointer'
                              }
                            }}
                            onClick={() => handleAnnouncementClick(announcement)}
                          >
                            <ListItemIcon sx={{ minWidth: 32 }}>
                              {React.cloneElement(getAnnouncementIcon(announcement.type), { sx: { fontSize: 18 } })}
                            </ListItemIcon>
                            <ListItemText
                              primary={announcement.title + (announcement.priority === 'high' ? ' [重要]' : '')}
                              secondary={
                                `${announcement.content.length > 60 ? announcement.content.substring(0, 60) + '...' : announcement.content} • ${announcement.date.split(' ')[0]}`
                              }
                              primaryTypographyProps={{
                                variant: 'body2',
                                sx: {
                                  fontWeight: 600,
                                  color: announcement.priority === 'high' ? '#ef4444' : '#1a1a1a',
                                  fontSize: '0.875rem'
                                }
                              }}
                              secondaryTypographyProps={{
                                variant: 'caption',
                                sx: {
                                  color: '#4b5563',
                                  fontSize: '0.75rem'
                                }
                              }}
                            />
                          </ListItem>
                          {index < 2 && (
                            <Divider variant="inset" component="li" sx={{ my: 0.25 }} />
                          )}
                        </React.Fragment>
                        ))
                      )}
                    </List>

                  </CardContent>
                </Card>
              </Grid>

              {/* 最近文章列表 */}
              <Grid item xs={12} lg={6}>
                <Card sx={{ 
                  borderRadius: 2,
                  border: '1px solid #e5e7eb',
                  boxShadow: 'none',
                  height: '100%',
                  minHeight: 400,
                  maxHeight: 500,
                  display: 'flex',
                  flexDirection: 'column'
                }}>
                  <CardContent sx={{ p: 2, height: '100%', display: 'flex', flexDirection: 'column' }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Avatar sx={{ 
                          backgroundColor: '#f0fdf4',
                          width: 28,
                          height: 28
                        }}>
                          <Article sx={{ color: '#10b981', fontSize: 18 }} />
                        </Avatar>
                        <Typography variant="subtitle1" sx={{ 
                          fontWeight: 600,
                          color: '#1a1a1a',
                          fontSize: '1rem'
                        }}>
                          最近文章
                        </Typography>
                      </Box>
                      <Button 
                        variant="text" 
                        sx={{ 
                          color: '#6b7280',
                          fontSize: '0.75rem'
                        }}
                      >
                        查看全部
                      </Button>
                    </Box>

                    <List sx={{ p: 0, flex: 1, overflowY: 'auto' }}>
                      {recentArticles.map((article, index) => (
                        <React.Fragment key={article.id}>
                          <ListItem
                            sx={{ 
                              px: 1,
                              py: 1,
                              '&:hover': {
                                backgroundColor: '#f9fafb',
                                cursor: 'pointer'
                              }
                            }}
                          >
                            <ListItemText
                              primary={
                                `${article.title.length > 30 ? article.title.substring(0, 30) + '...' : article.title} [${article.status}]`
                              }
                              secondary={
                                `${article.platform} • ${article.views} 浏览 • ${article.date}`
                              }
                              primaryTypographyProps={{
                                variant: 'body2',
                                sx: {
                                  fontWeight: 500,
                                  color: '#1a1a1a',
                                  fontSize: '0.875rem'
                                }
                              }}
                              secondaryTypographyProps={{
                                variant: 'caption',
                                sx: {
                                  color: '#6b7280',
                                  fontSize: '0.7rem'
                                }
                              }}
                            />
                          </ListItem>
                          {index < recentArticles.length - 1 && (
                            <Divider sx={{ my: 0.25 }} />
                          )}
                        </React.Fragment>
                      ))}
                    </List>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </Box>
        </Container>
      </Box>

      {/* 公告详情弹窗 */}
      <Dialog
        open={announcementDetailOpen}
        onClose={handleCloseAnnouncementDetail}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: { borderRadius: 2 }
        }}
      >
        <DialogTitle sx={{
          borderBottom: '1px solid #e5e7eb',
          pb: 2,
          display: 'flex',
          alignItems: 'center',
          gap: 2
        }}>
          {selectedAnnouncement && (
            <>
              {getAnnouncementIcon(selectedAnnouncement.type)}
              <Box>
                <Typography variant="h6" component="div">
                  {selectedAnnouncement.title}
                  {selectedAnnouncement.priority === 'high' && (
                    <Chip
                      label="重要"
                      size="small"
                      color="error"
                      sx={{ ml: 1 }}
                    />
                  )}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {selectedAnnouncement.author} • {selectedAnnouncement.date}
                  {selectedAnnouncement.viewCount > 0 && ` • 阅读 ${selectedAnnouncement.viewCount} 次`}
                </Typography>
              </Box>
            </>
          )}
        </DialogTitle>

        <DialogContent sx={{ py: 3 }}>
          {detailLoading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
              <CircularProgress />
            </Box>
          ) : selectedAnnouncement ? (
            <Box>
              {/* 公告摘要 */}
              {selectedAnnouncement.summary && selectedAnnouncement.summary !== selectedAnnouncement.content && (
                <Box sx={{ mb: 3, p: 2, backgroundColor: '#f8f9fa', borderRadius: 1 }}>
                  <Typography variant="subtitle2" color="primary" gutterBottom>
                    摘要
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {selectedAnnouncement.summary}
                  </Typography>
                </Box>
              )}

              {/* 公告内容 */}
              <Typography variant="body1" sx={{ lineHeight: 1.8, whiteSpace: 'pre-wrap' }}>
                {selectedAnnouncement.content}
              </Typography>

              {/* 公告信息 */}
              <Box sx={{ mt: 3, pt: 2, borderTop: '1px solid #e5e7eb' }}>
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">
                      <strong>目标受众：</strong>
                      {selectedAnnouncement.targetAudience === 'all' ? '全部用户' :
                       selectedAnnouncement.targetAudience === 'enterprise' ? '企业用户' :
                       selectedAnnouncement.targetAudience === 'channel' ? '渠道商' :
                       selectedAnnouncement.targetAudience === 'agent' ? '代理商' :
                       selectedAnnouncement.targetAudience}
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">
                      <strong>状态：</strong>
                      {selectedAnnouncement.status === 'published' ? '已发布' :
                       selectedAnnouncement.status === 'draft' ? '草稿' :
                       selectedAnnouncement.status}
                    </Typography>
                  </Grid>
                  {selectedAnnouncement.expireTime && (
                    <Grid item xs={12}>
                      <Typography variant="body2" color="text.secondary">
                        <strong>过期时间：</strong>{selectedAnnouncement.expireTime}
                      </Typography>
                    </Grid>
                  )}
                </Grid>
              </Box>
            </Box>
          ) : null}
        </DialogContent>

        <DialogActions sx={{ px: 3, pb: 2 }}>
          <Button onClick={handleCloseAnnouncementDetail}>
            关闭
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}

export default EnterpriseDashboardNew;