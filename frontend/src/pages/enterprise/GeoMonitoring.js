import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Tabs,
  Tab,
  Card,
  Grid,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Chip,
  Avatar,
  IconButton,
  Button,
  LinearProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TableSortLabel,
  Tooltip,
  Alert,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  ListItemSecondaryAction,
  CircularProgress,
  Rating,
  ToggleButton,
  ToggleButtonGroup,
} from '@mui/material';
import {
  TrendingUp,
  TrendingDown,
  LocationOn,
  Analytics,
  Assessment,
  Timeline,
  BarChartOutlined,
  PieChartOutlined,
  ShowChart,
  Groups,
  Language,
  Speed,
  Visibility,
  Search,
  FilterList,
  Download,
  Refresh,
  ArrowUpward,
  ArrowDownward,
  Remove,
  Star,
  Public,
  LocalOffer,
  Psychology,
  EmojiEvents,
  Warning,
  CheckCircle,
  Error,
  Info,
  CalendarToday,
  AccessTime,
  CompareArrows,
  Insights,
  AutoGraph,
  QueryStats,
  Leaderboard,
  Monitor,
  Domain,
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';
import ApiService from '../../services/api';

const StyledContainer = styled(Box)(({ theme }) => ({
  width: '100%',
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  backgroundColor: '#ffffff',
  position: 'relative',
}));

const HeaderSection = styled(Box)(({ theme }) => ({
  padding: theme.spacing(2, 3),
  backgroundColor: '#ffffff',
  borderBottom: '1px solid #e5e7eb',
}));

const ContentSection = styled(Box)(({ theme }) => ({
  flex: '1 1 auto',
  overflowY: 'auto',
  overflowX: 'hidden',
  padding: theme.spacing(3),
  backgroundColor: '#ffffff',
  '&::-webkit-scrollbar': {
    width: '6px',
  },
  '&::-webkit-scrollbar-track': {
    background: 'transparent',
  },
  '&::-webkit-scrollbar-thumb': {
    background: '#d1d5db',
    borderRadius: '3px',
  },
}));

const StyledTabs = styled(Tabs)(({ theme }) => ({
  borderBottom: '1px solid #e5e7eb',
  backgroundColor: '#ffffff',
  '& .MuiTabs-indicator': {
    height: 2,
    backgroundColor: '#3b82f6',
  },
}));

const StyledTab = styled(Tab)(({ theme }) => ({
  minHeight: 48,
  textTransform: 'none',
  fontSize: '0.875rem',
  fontWeight: 500,
  color: '#6b7280',
  '&.Mui-selected': {
    fontWeight: 600,
    color: '#3b82f6',
  },
  '&:hover': {
    color: '#1f2937',
  },
}));

const MetricCard = styled(Box)(({ theme }) => ({
  height: '100%',
  minHeight: '100px',
  border: '1px solid #e5e7eb',
  borderRadius: '4px',
  backgroundColor: '#ffffff',
  transition: 'all 0.2s ease',
  '&:hover': {
    borderColor: '#d1d5db',
  },
}));

const ChartCard = styled(Card)(({ theme }) => ({
  height: '100%',
  minHeight: '300px',
  borderRadius: theme.spacing(1.5),
  border: `1px solid ${theme.palette.divider}`,
  boxShadow: '0 1px 4px rgba(0,0,0,0.05)',
  display: 'flex',
  flexDirection: 'column',
  backgroundColor: theme.palette.background.paper,
}));

// 图表颜色配置
const COLORS = {
  primary: '#1976d2',
  secondary: '#42a5f5',
  success: '#4caf50',
  warning: '#ff9800',
  error: '#f44336',
  info: '#00bcd4',
  purple: '#666666',
  pink: '#e91e63',
  indigo: '#3f51b5',
  teal: '#009688',
};

const CHART_COLORS = [
  COLORS.primary,
  COLORS.secondary,
  COLORS.success,
  COLORS.warning,
  COLORS.purple,
  COLORS.pink,
  COLORS.indigo,
  COLORS.teal,
];

function GeoMonitoring() {
  const [currentTab, setCurrentTab] = useState(0);
  const [selectedProject, setSelectedProject] = useState('all');
  const [timeRange, setTimeRange] = useState('7d');
  const [projects, setProjects] = useState([
    { id: 'all', name: '所有项目' },
    { id: 'proj1', name: 'AI智能助手' },
    { id: 'proj2', name: '数字化营销平台' },
    { id: 'proj3', name: '企业管理系统' },
  ]);
  const [loading, setLoading] = useState(false);
  const [orderBy, setOrderBy] = useState('keyword');
  const [order, setOrder] = useState('asc');

  // 数据概览数据
  const overviewData = {
    totalTraffic: 285430,
    totalTrafficChange: 12.5,
    avgRanking: 3.2,
    avgRankingChange: -0.8,
    totalKeywords: 1543,
    totalKeywordsChange: 8.3,
    conversionRate: 4.8,
    conversionRateChange: 1.2,
  };

  // 流量趋势数据
  const trafficTrendData = [
    { date: '1月1日', organic: 4500, paid: 2300, social: 1200, direct: 3200 },
    { date: '1月2日', organic: 5200, paid: 2500, social: 1400, direct: 3500 },
    { date: '1月3日', organic: 4800, paid: 2400, social: 1300, direct: 3300 },
    { date: '1月4日', organic: 5500, paid: 2700, social: 1500, direct: 3700 },
    { date: '1月5日', organic: 6200, paid: 2900, social: 1700, direct: 4000 },
    { date: '1月6日', organic: 5800, paid: 2800, social: 1600, direct: 3800 },
    { date: '1月7日', organic: 6500, paid: 3100, social: 1800, direct: 4200 },
  ];

  // 地域分布数据
  const geoDistributionData = [
    { name: '北京', value: 4500, percentage: 28 },
    { name: '上海', value: 3800, percentage: 24 },
    { name: '广州', value: 2200, percentage: 14 },
    { name: '深圳', value: 1800, percentage: 11 },
    { name: '杭州', value: 1500, percentage: 9 },
    { name: '成都', value: 1200, percentage: 8 },
    { name: '其他', value: 950, percentage: 6 },
  ];

  // 关键词排名数据
  const [keywordRankingData] = useState([
    { keyword: 'AI内容生成', position: 2, change: 1, volume: 12500, difficulty: 85 },
    { keyword: 'SEO优化工具', position: 3, change: -1, volume: 8900, difficulty: 72 },
    { keyword: '企业数字化', position: 5, change: 2, volume: 15600, difficulty: 68 },
    { keyword: '智能营销平台', position: 4, change: 0, volume: 6700, difficulty: 75 },
    { keyword: '内容管理系统', position: 8, change: -2, volume: 9800, difficulty: 70 },
    { keyword: 'AI写作助手', position: 1, change: 3, volume: 18900, difficulty: 82 },
  ]);

  // 排序处理函数
  const handleRequestSort = (property) => {
    const isAsc = orderBy === property && order === 'asc';
    setOrder(isAsc ? 'desc' : 'asc');
    setOrderBy(property);
  };

  // 获取排序后的数据
  const getSortedData = (data) => {
    return [...data].sort((a, b) => {
      let aValue = a[orderBy];
      let bValue = b[orderBy];
      
      if (order === 'desc') {
        return bValue > aValue ? 1 : bValue < aValue ? -1 : 0;
      }
      return aValue > bValue ? 1 : aValue < bValue ? -1 : 0;
    });
  };

  // AI平台性能数据
  const aiPerformanceData = [
    { metric: '响应时间', value: 95, target: 90, unit: 'ms' },
    { metric: '准确率', value: 92, target: 85, unit: '%' },
    { metric: '用户满意度', value: 88, target: 80, unit: '%' },
    { metric: '内容质量', value: 90, target: 85, unit: '分' },
    { metric: 'API可用性', value: 99.9, target: 99, unit: '%' },
    { metric: '处理速度', value: 87, target: 80, unit: '条/秒' },
  ];

  // 竞争对手数据
  const competitorData = [
    { 
      name: '竞争对手A',
      traffic: 320000,
      keywords: 2100,
      domainRating: 85,
      backlinks: 45000,
      contentScore: 88,
      trend: 'up'
    },
    {
      name: '竞争对手B',
      traffic: 280000,
      keywords: 1800,
      domainRating: 78,
      backlinks: 38000,
      contentScore: 82,
      trend: 'down'
    },
    {
      name: '我们',
      traffic: 285430,
      keywords: 1543,
      domainRating: 80,
      backlinks: 41000,
      contentScore: 85,
      trend: 'up'
    },
    {
      name: '竞争对手C',
      traffic: 195000,
      keywords: 1200,
      domainRating: 72,
      backlinks: 28000,
      contentScore: 78,
      trend: 'stable'
    },
  ];

  // 竞争力雷达图数据
  const competitiveRadarData = [
    { subject: '流量', A: 95, B: 85, Us: 88, fullMark: 100 },
    { subject: '关键词', A: 92, B: 88, Us: 78, fullMark: 100 },
    { subject: '内容质量', A: 88, B: 82, Us: 85, fullMark: 100 },
    { subject: '用户体验', A: 85, B: 78, Us: 90, fullMark: 100 },
    { subject: '技术SEO', A: 90, B: 85, Us: 87, fullMark: 100 },
    { subject: '外链质量', A: 87, B: 80, Us: 83, fullMark: 100 },
  ];

  const handleTabChange = (event, newValue) => {
    setCurrentTab(newValue);
  };

  const handleProjectChange = (event) => {
    setSelectedProject(event.target.value);
    // 这里可以触发数据重新加载
  };

  const handleTimeRangeChange = (event, newRange) => {
    if (newRange !== null) {
      setTimeRange(newRange);
    }
  };

  // 渲染数据概览
  const renderDataOverview = () => (
    <Box>
      {/* 核心指标卡片 */}
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard>
            <Box sx={{ p: 2 }}>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" variant="body2" gutterBottom>
                    总流量
                  </Typography>
                  <Typography variant="h4" fontWeight="bold">
                    {overviewData.totalTraffic.toLocaleString()}
                  </Typography>
                  <Box display="flex" alignItems="center" mt={1}>
                    {overviewData.totalTrafficChange > 0 ? (
                      <TrendingUp sx={{ color: COLORS.success, fontSize: 20, mr: 0.5 }} />
                    ) : (
                      <TrendingDown sx={{ color: COLORS.error, fontSize: 20, mr: 0.5 }} />
                    )}
                    <Typography
                      variant="body2"
                      color={overviewData.totalTrafficChange > 0 ? 'success.main' : 'error.main'}
                    >
                      {Math.abs(overviewData.totalTrafficChange)}%
                    </Typography>
                  </Box>
                </Box>
                <Avatar sx={{ bgcolor: COLORS.primary, width: 44, height: 44 }}>
                  <Analytics />
                </Avatar>
              </Box>
            </Box>
          </MetricCard>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <MetricCard>
            <Box sx={{ p: 2 }}>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" variant="body2" gutterBottom>
                    平均排名
                  </Typography>
                  <Typography variant="h4" fontWeight="bold">
                    {overviewData.avgRanking}
                  </Typography>
                  <Box display="flex" alignItems="center" mt={1}>
                    {overviewData.avgRankingChange < 0 ? (
                      <TrendingUp sx={{ color: COLORS.success, fontSize: 20, mr: 0.5 }} />
                    ) : (
                      <TrendingDown sx={{ color: COLORS.error, fontSize: 20, mr: 0.5 }} />
                    )}
                    <Typography
                      variant="body2"
                      color={overviewData.avgRankingChange < 0 ? 'success.main' : 'error.main'}
                    >
                      {Math.abs(overviewData.avgRankingChange)} 位
                    </Typography>
                  </Box>
                </Box>
                <Avatar sx={{ bgcolor: COLORS.success, width: 44, height: 44 }}>
                  <Leaderboard />
                </Avatar>
              </Box>
            </Box>
          </MetricCard>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <MetricCard>
            <Box sx={{ p: 2 }}>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" variant="body2" gutterBottom>
                    关键词总数
                  </Typography>
                  <Typography variant="h4" fontWeight="bold">
                    {overviewData.totalKeywords.toLocaleString()}
                  </Typography>
                  <Box display="flex" alignItems="center" mt={1}>
                    <TrendingUp sx={{ color: COLORS.success, fontSize: 20, mr: 0.5 }} />
                    <Typography variant="body2" color="success.main">
                      {overviewData.totalKeywordsChange}%
                    </Typography>
                  </Box>
                </Box>
                <Avatar sx={{ bgcolor: COLORS.warning, width: 44, height: 44 }}>
                  <LocalOffer />
                </Avatar>
              </Box>
            </Box>
          </MetricCard>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <MetricCard>
            <Box sx={{ p: 2 }}>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" variant="body2" gutterBottom>
                    转化率
                  </Typography>
                  <Typography variant="h4" fontWeight="bold">
                    {overviewData.conversionRate}%
                  </Typography>
                  <Box display="flex" alignItems="center" mt={1}>
                    <TrendingUp sx={{ color: COLORS.success, fontSize: 20, mr: 0.5 }} />
                    <Typography variant="body2" color="success.main">
                      {overviewData.conversionRateChange}%
                    </Typography>
                  </Box>
                </Box>
                <Avatar sx={{ bgcolor: COLORS.purple, width: 44, height: 44 }}>
                  <EmojiEvents />
                </Avatar>
              </Box>
            </Box>
          </MetricCard>
        </Grid>
      </Grid>

      {/* 流量趋势图表 */}
      <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
        <Box sx={{ flex: 1 }}>
          <ChartCard>
            <Box sx={{ p: 2 }}>
              <Typography variant="h6" gutterBottom fontWeight="600">
                流量趋势分析
              </Typography>
              <Box sx={{ 
                width: '100%', 
                height: 260, 
                mt: 1,
                display: 'flex', 
                alignItems: 'center', 
                justifyContent: 'center',
                border: '1px solid #e5e7eb',
                borderRadius: '4px',
                bgcolor: '#f9fafb'
              }}>
                <Typography variant="body2" color="text.secondary">
                  流量趋势分析图表
                </Typography>
              </Box>
            </Box>
          </ChartCard>
        </Box>

        <Box sx={{ flex: 1 }}>
          <ChartCard>
            <Box sx={{ p: 2 }}>
              <Typography variant="h6" gutterBottom fontWeight="600">
                地域分布
              </Typography>
              <Box sx={{ 
                width: '100%', 
                height: 260, 
                mt: 1,
                display: 'flex', 
                alignItems: 'center', 
                justifyContent: 'center',
                border: '1px solid #e5e7eb',
                borderRadius: '4px',
                bgcolor: '#f9fafb'
              }}>
                <Typography variant="body2" color="text.secondary">
                  地域分布图表
                </Typography>
              </Box>
            </Box>
          </ChartCard>
        </Box>
      </Box>

      {/* 设备和浏览器分布 */}
      <Grid container spacing={2}>
        <Grid item xs={12} md={6}>
          <ChartCard>
            <Box sx={{ p: 2 }}>
              <Typography variant="h6" gutterBottom fontWeight="600">
                设备类型分布
              </Typography>
              <Box sx={{ 
                width: '100%', 
                height: 240, 
                mt: 1,
                display: 'flex', 
                alignItems: 'center', 
                justifyContent: 'center',
                border: '1px solid #e5e7eb',
                borderRadius: '4px',
                bgcolor: '#f9fafb'
              }}>
                <Typography variant="body2" color="text.secondary">
                  设备类型分布图表
                </Typography>
              </Box>
            </Box>
          </ChartCard>
        </Grid>

        <Grid item xs={12} md={6}>
          <ChartCard>
            <Box sx={{ p: 2 }}>
              <Typography variant="h6" gutterBottom fontWeight="600">
                页面性能指标
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemAvatar>
                    <Avatar sx={{ bgcolor: COLORS.success }}>
                      <Speed />
                    </Avatar>
                  </ListItemAvatar>
                  <ListItemText
                    primary="页面加载速度"
                    secondary="平均 2.3 秒"
                  />
                  <ListItemSecondaryAction>
                    <Chip label="优秀" color="success" size="small" />
                  </ListItemSecondaryAction>
                </ListItem>
                <ListItem>
                  <ListItemAvatar>
                    <Avatar sx={{ bgcolor: COLORS.warning }}>
                      <AccessTime />
                    </Avatar>
                  </ListItemAvatar>
                  <ListItemText
                    primary="平均停留时间"
                    secondary="4分32秒"
                  />
                  <ListItemSecondaryAction>
                    <Chip label="良好" color="warning" size="small" />
                  </ListItemSecondaryAction>
                </ListItem>
                <ListItem>
                  <ListItemAvatar>
                    <Avatar sx={{ bgcolor: COLORS.info }}>
                      <Visibility />
                    </Avatar>
                  </ListItemAvatar>
                  <ListItemText
                    primary="跳出率"
                    secondary="32.5%"
                  />
                  <ListItemSecondaryAction>
                    <Chip label="正常" color="info" size="small" />
                  </ListItemSecondaryAction>
                </ListItem>
              </List>
            </Box>
          </ChartCard>
        </Grid>
      </Grid>
    </Box>
  );

  // 渲染排名趋势
  const renderRankingAnalysis = () => {
    const sortedData = getSortedData(keywordRankingData);
    
    return (
      <Box>
        {/* 关键词排名表格 - 直接显示，不再有4个监控卡片 */}
        <ChartCard sx={{ mb: 3 }}>
          <Box sx={{ p: 2 }}>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
              <Typography variant="h6" fontWeight="600">
                关键词排名监控
              </Typography>
              <Box display="flex" gap={1}>
                <Button variant="text" size="small" startIcon={<FilterList />} sx={{ color: '#6b7280' }}>
                  筛选
                </Button>
                <Button variant="text" size="small" startIcon={<Download />} sx={{ color: '#6b7280' }}>
                  导出
                </Button>
              </Box>
            </Box>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>
                      <TableSortLabel
                        active={orderBy === 'keyword'}
                        direction={orderBy === 'keyword' ? order : 'asc'}
                        onClick={() => handleRequestSort('keyword')}
                      >
                        关键词
                      </TableSortLabel>
                    </TableCell>
                    <TableCell align="center">
                      <TableSortLabel
                        active={orderBy === 'position'}
                        direction={orderBy === 'position' ? order : 'asc'}
                        onClick={() => handleRequestSort('position')}
                      >
                        当前排名
                      </TableSortLabel>
                    </TableCell>
                    <TableCell align="center">
                      <TableSortLabel
                        active={orderBy === 'change'}
                        direction={orderBy === 'change' ? order : 'asc'}
                        onClick={() => handleRequestSort('change')}
                      >
                        变化
                      </TableSortLabel>
                    </TableCell>
                    <TableCell align="center">
                      <TableSortLabel
                        active={orderBy === 'volume'}
                        direction={orderBy === 'volume' ? order : 'asc'}
                        onClick={() => handleRequestSort('volume')}
                      >
                        搜索量
                      </TableSortLabel>
                    </TableCell>
                    <TableCell align="center">
                      <TableSortLabel
                        active={orderBy === 'difficulty'}
                        direction={orderBy === 'difficulty' ? order : 'asc'}
                        onClick={() => handleRequestSort('difficulty')}
                      >
                        难度
                      </TableSortLabel>
                    </TableCell>
                    <TableCell align="center">状态</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {sortedData.map((row, index) => (
                  <TableRow key={index}>
                    <TableCell>
                      <Typography variant="body2" fontWeight="500">
                        {row.keyword}
                      </Typography>
                    </TableCell>
                    <TableCell align="center">
                      <Chip
                        label={`#${row.position}`}
                        size="small"
                        color={row.position <= 3 ? 'success' : row.position <= 10 ? 'warning' : 'default'}
                      />
                    </TableCell>
                    <TableCell align="center">
                      <Box display="flex" alignItems="center" justifyContent="center">
                        {row.change > 0 ? (
                          <ArrowUpward sx={{ color: COLORS.success, fontSize: 16 }} />
                        ) : row.change < 0 ? (
                          <ArrowDownward sx={{ color: COLORS.error, fontSize: 16 }} />
                        ) : (
                          <Remove sx={{ color: 'text.secondary', fontSize: 16 }} />
                        )}
                        <Typography
                          variant="body2"
                          color={row.change > 0 ? 'success.main' : row.change < 0 ? 'error.main' : 'text.secondary'}
                        >
                          {Math.abs(row.change)}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell align="center">{row.volume.toLocaleString()}</TableCell>
                    <TableCell align="center">
                      <LinearProgress
                        variant="determinate"
                        value={row.difficulty}
                        sx={{
                          height: 6,
                          borderRadius: '4px',
                          backgroundColor: '#e0e0e0',
                          '& .MuiLinearProgress-bar': {
                            backgroundColor: row.difficulty > 70 ? COLORS.error : row.difficulty > 40 ? COLORS.warning : COLORS.success,
                          },
                        }}
                      />
                    </TableCell>
                    <TableCell align="center">
                      {row.position <= 3 ? (
                        <CheckCircle sx={{ color: COLORS.success }} />
                      ) : row.position <= 10 ? (
                        <Info sx={{ color: COLORS.warning }} />
                      ) : (
                        <Warning sx={{ color: COLORS.error }} />
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Box>
      </ChartCard>

        {/* 排名趋势图表 */}
        <Grid container spacing={2}>
          <Grid item xs={12} lg={8}>
            <ChartCard>
              <Box sx={{ p: 2 }}>
                <Typography variant="h6" gutterBottom fontWeight="600">
                  Top 10 关键词排名趋势
                </Typography>
                <Box sx={{ 
                  width: '100%', 
                  height: 400, 
                  mt: 2,
                  display: 'flex', 
                  alignItems: 'center', 
                  justifyContent: 'center',
                  border: '1px solid #e5e7eb',
                  borderRadius: '4px',
                  bgcolor: '#f9fafb'
                }}>
                  <Typography variant="body2" color="text.secondary">
                    Top 10 关键词排名趋势图表
                  </Typography>
                </Box>
              </Box>
            </ChartCard>
          </Grid>

          <Grid item xs={12} lg={4}>
            <ChartCard>
              <Box sx={{ p: 2 }}>
                <Typography variant="h6" gutterBottom fontWeight="600">
                  排名分布
                </Typography>
                <Box sx={{ 
                  width: '100%', 
                  height: 400, 
                  mt: 2,
                  display: 'flex', 
                  alignItems: 'center', 
                  justifyContent: 'center',
                  border: '1px solid #e5e7eb',
                  borderRadius: '4px',
                  bgcolor: '#f9fafb'
                }}>
                  <Typography variant="body2" color="text.secondary">
                    排名分布图表
                  </Typography>
                </Box>
              </Box>
            </ChartCard>
          </Grid>
        </Grid>
      </Box>
    );
  };

  // 渲染AI平台分析
  const renderAIPlatformOverview = () => (
    <Box>
      {/* AI性能指标 */}
      <Grid container spacing={2} sx={{ mb: 3 }}>
        {aiPerformanceData.map((metric, index) => (
          <Grid item xs={12} sm={6} md={4} key={index}>
            <MetricCard>
              <Box sx={{ p: 2 }}>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  {metric.metric}
                </Typography>
                <Box display="flex" alignItems="baseline" mb={2}>
                  <Typography variant="h4" fontWeight="bold">
                    {metric.value}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" ml={1}>
                    {metric.unit}
                  </Typography>
                </Box>
                <Box position="relative">
                  <LinearProgress
                    variant="determinate"
                    value={(metric.value / metric.target) * 100}
                    sx={{
                      height: 8,
                      borderRadius: 4,
                      backgroundColor: '#e0e0e0',
                      '& .MuiLinearProgress-bar': {
                        backgroundColor: metric.value >= metric.target ? COLORS.success : COLORS.warning,
                      },
                    }}
                  />
                  <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                    目标: {metric.target}{metric.unit}
                  </Typography>
                </Box>
              </Box>
            </MetricCard>
          </Grid>
        ))}
      </Grid>

      {/* AI使用分析 */}
      <Grid container spacing={2}>
        <Grid item xs={12} lg={6}>
          <ChartCard>
            <Box sx={{ p: 2 }}>
              <Typography variant="h6" gutterBottom fontWeight="600">
                AI功能使用分布
              </Typography>
              <Box sx={{ 
                width: '100%', 
                height: 350, 
                mt: 2,
                display: 'flex', 
                alignItems: 'center', 
                justifyContent: 'center',
                border: '1px solid #e5e7eb',
                borderRadius: '4px',
                bgcolor: '#f9fafb'
              }}>
                <Typography variant="body2" color="text.secondary">
                  AI功能使用分布图表
                </Typography>
              </Box>
            </Box>
          </ChartCard>
        </Grid>

        <Grid item xs={12} lg={6}>
          <ChartCard>
            <Box sx={{ p: 2 }}>
              <Typography variant="h6" gutterBottom fontWeight="600">
                AI模型性能对比
              </Typography>
              <Box sx={{ 
                width: '100%', 
                height: 350, 
                mt: 2,
                display: 'flex', 
                alignItems: 'center', 
                justifyContent: 'center',
                border: '1px solid #e5e7eb',
                borderRadius: '4px',
                bgcolor: '#f9fafb'
              }}>
                <Typography variant="body2" color="text.secondary">
                  AI模型性能对比图表
                </Typography>
              </Box>
            </Box>
          </ChartCard>
        </Grid>

        {/* AI内容质量分析 */}
        <Grid item xs={12}>
          <ChartCard>
            <Box sx={{ p: 2 }}>
              <Typography variant="h6" gutterBottom fontWeight="600">
                AI生成内容质量趋势
              </Typography>
              <Box sx={{ 
                width: '100%', 
                height: 300, 
                mt: 2,
                display: 'flex', 
                alignItems: 'center', 
                justifyContent: 'center',
                border: '1px solid #e5e7eb',
                borderRadius: '4px',
                bgcolor: '#f9fafb'
              }}>
                <Typography variant="body2" color="text.secondary">
                  AI生成内容质量趋势图表
                </Typography>
              </Box>
            </Box>
          </ChartCard>
        </Grid>
      </Grid>
    </Box>
  );

  // 渲染竞争对手分析
  const renderCompetitorAnalysis = () => (
    <Box sx={{ p: 3 }}>
      {/* 竞争对手对比表 */}
      <ChartCard sx={{ mb: 3 }}>
        <Box sx={{ p: 2 }}>
          <Typography variant="h6" gutterBottom fontWeight="600">
            竞争对手综合对比
          </Typography>
          <TableContainer sx={{ mt: 2 }}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>竞争对手</TableCell>
                  <TableCell align="center">月流量</TableCell>
                  <TableCell align="center">关键词数</TableCell>
                  <TableCell align="center">域名评分</TableCell>
                  <TableCell align="center">外链数</TableCell>
                  <TableCell align="center">内容评分</TableCell>
                  <TableCell align="center">趋势</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {competitorData.map((competitor, index) => (
                  <TableRow 
                    key={index}
                    sx={{ 
                      backgroundColor: competitor.name === '我们' ? 'action.selected' : 'transparent',
                      fontWeight: competitor.name === '我们' ? 600 : 400,
                    }}
                  >
                    <TableCell>
                      <Box display="flex" alignItems="center">
                        <Avatar sx={{ mr: 2, bgcolor: competitor.name === '我们' ? COLORS.primary : COLORS.secondary }}>
                          {competitor.name[0]}
                        </Avatar>
                        <Typography variant="body2" fontWeight={competitor.name === '我们' ? 600 : 400}>
                          {competitor.name}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell align="center">{competitor.traffic.toLocaleString()}</TableCell>
                    <TableCell align="center">{competitor.keywords.toLocaleString()}</TableCell>
                    <TableCell align="center">
                      <Chip label={competitor.domainRating} size="small" color={competitor.domainRating > 80 ? 'success' : 'default'} />
                    </TableCell>
                    <TableCell align="center">{competitor.backlinks.toLocaleString()}</TableCell>
                    <TableCell align="center">
                      <Rating value={competitor.contentScore / 20} precision={0.5} readOnly size="small" />
                    </TableCell>
                    <TableCell align="center">
                      {competitor.trend === 'up' && <TrendingUp sx={{ color: COLORS.success }} />}
                      {competitor.trend === 'down' && <TrendingDown sx={{ color: COLORS.error }} />}
                      {competitor.trend === 'stable' && <Remove sx={{ color: 'text.secondary' }} />}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Box>
      </ChartCard>

      {/* 竞争力分析图表 */}
      <Grid container spacing={2}>
        <Grid item xs={12} lg={6}>
          <ChartCard>
            <Box sx={{ p: 2 }}>
              <Typography variant="h6" gutterBottom fontWeight="600">
                竞争力雷达图
              </Typography>
              <Box sx={{ 
                width: '100%', 
                height: 400, 
                mt: 2,
                display: 'flex', 
                alignItems: 'center', 
                justifyContent: 'center',
                border: '1px solid #e5e7eb',
                borderRadius: '4px',
                bgcolor: '#f9fafb'
              }}>
                <Typography variant="body2" color="text.secondary">
                  竞争力雷达图
                </Typography>
              </Box>
            </Box>
          </ChartCard>
        </Grid>

        <Grid item xs={12} lg={6}>
          <ChartCard>
            <Box sx={{ p: 2 }}>
              <Typography variant="h6" gutterBottom fontWeight="600">
                市场份额分析
              </Typography>
              <Box sx={{ 
                width: '100%', 
                height: 400, 
                mt: 2,
                display: 'flex', 
                alignItems: 'center', 
                justifyContent: 'center',
                border: '1px solid #e5e7eb',
                borderRadius: '4px',
                bgcolor: '#f9fafb'
              }}>
                <Typography variant="body2" color="text.secondary">
                  市场份额分析图表
                </Typography>
              </Box>
            </Box>
          </ChartCard>
        </Grid>

        {/* 关键指标对比 */}
        <Grid item xs={12}>
          <ChartCard>
            <Box sx={{ p: 2 }}>
              <Typography variant="h6" gutterBottom fontWeight="600">
                关键指标月度对比
              </Typography>
              <Box sx={{ 
                width: '100%', 
                height: 350, 
                mt: 2,
                display: 'flex', 
                alignItems: 'center', 
                justifyContent: 'center',
                border: '1px solid #e5e7eb',
                borderRadius: '4px',
                bgcolor: '#f9fafb'
              }}>
                <Typography variant="body2" color="text.secondary">
                  关键指标月度对比图表
                </Typography>
              </Box>
            </Box>
          </ChartCard>
        </Grid>
      </Grid>
    </Box>
  );

  return (
    <StyledContainer>
      {/* 头部区域 */}
      <HeaderSection>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Box display="flex" alignItems="center" gap={2}>
            <Avatar sx={{ bgcolor: COLORS.primary, width: 48, height: 48 }}>
              <Public />
            </Avatar>
            <Box>
              <Typography variant="h5" fontWeight="bold">
                GEO监控中心
              </Typography>
              <Typography variant="body2" color="text.secondary">
                实时监控全球SEO表现和竞争态势
              </Typography>
            </Box>
          </Box>
          <Box display="flex" gap={2} alignItems="center">
            {/* 项目选择器 */}
            <FormControl size="small" sx={{ minWidth: 180 }}>
              <InputLabel>选择项目</InputLabel>
              <Select
                value={selectedProject}
                label="选择项目"
                onChange={handleProjectChange}
              >
                {projects.map((project) => (
                  <MenuItem key={project.id} value={project.id}>
                    {project.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            {/* 时间范围选择 */}
            <ToggleButtonGroup
              value={timeRange}
              exclusive
              onChange={handleTimeRangeChange}
              size="small"
            >
              <ToggleButton value="24h">24小时</ToggleButton>
              <ToggleButton value="7d">7天</ToggleButton>
              <ToggleButton value="30d">30天</ToggleButton>
              <ToggleButton value="90d">90天</ToggleButton>
            </ToggleButtonGroup>

            {/* 刷新按钮 */}
            <IconButton onClick={() => setLoading(true)}>
              <Refresh />
            </IconButton>
          </Box>
        </Box>

        {/* 标签导航 */}
        <StyledTabs value={currentTab} onChange={handleTabChange}>
          <StyledTab icon={<Assessment />} iconPosition="start" label="数据概览" />
          <StyledTab icon={<Leaderboard />} iconPosition="start" label="排名分析" />
          <StyledTab icon={<Psychology />} iconPosition="start" label="AI平台全景" />
          <StyledTab icon={<CompareArrows />} iconPosition="start" label="竞争对手分析" />
        </StyledTabs>
      </HeaderSection>

      {/* 内容区域 */}
      <ContentSection>
        {currentTab === 0 && renderDataOverview()}
        {currentTab === 1 && renderRankingAnalysis()}
        {currentTab === 2 && renderAIPlatformOverview()}
        {currentTab === 3 && renderCompetitorAnalysis()}
      </ContentSection>
    </StyledContainer>
  );
}

export default GeoMonitoring;