import React, { useEffect, useState, Suspense } from 'react';
import {
  Box,
  Container,
  Typo<PERSON>,
  <PERSON>ton,
  Card,
  CardContent,
  Avatar,
  Chip,
  Grid,
  Stack,
  IconButton,
  CircularProgress,
} from '@mui/material';
import {
  Psychology,
  CheckCircle,
  Rocket,
  Timeline,
  ArrowBackIos,
  ArrowForwardIos,
  Create,
  TrendingUp,
  Analytics,
  AutoAwesome,
  Speed,
  Publish,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { handleReferralVisit, buildReferralUrl, getReferralParams } from '../utils/referralUtils';
import Footer from '../components/layout/Footer';
import { lazyWithRetry } from '../utils/lazyWithRetry';

// 懒加载组件
const HomeAIContentDemo = lazyWithRetry(() => import('../components/home/<USER>'));
const KeywordRanking = lazyWithRetry(() => import('../components/home/<USER>'));
const OneClickPublish = lazyWithRetry(() => import('../components/home/<USER>'));
const HelpDocumentCenter = lazyWithRetry(() => import('../components/home/<USER>'));

function Home() {
  const navigate = useNavigate();
  const { isAuthenticated } = useAuth();
  const [stats, setStats] = useState({
    users: 0,
    content: 0,
    improvement: 0,
    platforms: 0,
  });
  const [currentFeature, setCurrentFeature] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);

  // 处理推广链接参数
  useEffect(() => {
    // 处理推广链接访问
    handleReferralVisit('');
  }, []);

  // Animate counters
  useEffect(() => {
    const targets = { users: 10000, content: 50000, improvement: 95, platforms: 5 };
    const duration = 2000;
    const steps = 60;
    const stepTime = duration / steps;

    const intervals = Object.keys(targets).map(key => {
      const target = targets[key];
      const step = target / steps;
      let current = 0;

      return setInterval(() => {
        current += step;
        if (current >= target) {
          current = target;
          clearInterval(intervals.find(i => i === intervals[key]));
        }
        setStats(prev => ({ ...prev, [key]: Math.floor(current) }));
      }, stepTime);
    });

    return () => intervals.forEach(clearInterval);
  }, []);

  const handleGetStarted = () => {
    if (isAuthenticated) {
      navigate('/dashboard');
    } else {
      // 保留URL中的推广参数
      const referralParams = getReferralParams();

      let registerUrl = '/auth/register';
      if (referralParams.ref) {
        registerUrl = buildReferralUrl(registerUrl, referralParams.ref, referralParams.campaign);
      }

      navigate(registerUrl);
    }
  };

  const handleLearnMore = () => {
    navigate('/product');
  };

  // 功能特性轮播数据
  const features = [
    {
      id: 'ai-analysis',
      title: 'AI内容分析引擎',
      subtitle: '实时分析中...',
      icon: Psychology,
      score: 85,
      color: '#3b82f6',
      component: 'default', // 使用默认的分析界面
      progress: [
        { label: '关键词优化', value: 100, status: '完成', color: '#10b981' },
        { label: '语义分析', value: 85, status: '85%', color: '#3b82f6' },
        { label: 'AI友好度', value: 70, status: '70%', color: '#f59e0b' }
      ],
      features: [
        { icon: CheckCircle, text: '提升语义相关性 +25%', bgColor: '#f0fdf4', iconColor: '#10b981' },
        { icon: CheckCircle, text: '优化关键词密度 +15%', bgColor: '#eff6ff', iconColor: '#3b82f6' },
        { icon: Timeline, text: '增强结构化数据', bgColor: '#fefce8', iconColor: '#f59e0b' }
      ]
    },
    {
      id: 'ai-creator',
      title: 'AI智能内容创作',
      subtitle: '一键生成优质内容',
      icon: Create,
      score: 92,
      color: '#10b981',
      component: HomeAIContentDemo,
    },
    {
      id: 'keyword-ranking',
      title: '关键词排名监控',
      subtitle: '全网排名追踪',
      icon: TrendingUp,
      score: 78,
      color: '#3b82f6',
      component: KeywordRanking,
    },
    {
      id: 'one-click-publish',
      title: '一键发布通道',
      subtitle: '多平台同步发布',
      icon: Publish,
      score: 95,
      color: '#8b5cf6',
      component: OneClickPublish,
    },
    {
      id: 'smart-analysis',
      title: '智能数据分析',
      subtitle: 'AI驱动的洞察',
      icon: Rocket,
      score: 88,
      color: '#ef4444',
      component: 'default', // 使用默认的分析界面
      progress: [
        { label: '流量增长', value: 120, status: '+120%', color: '#10b981' },
        { label: '转化率', value: 35, status: '+35%', color: '#f59e0b' },
        { label: 'ROI提升', value: 280, status: '+280%', color: '#ef4444' }
      ],
      features: [
        { icon: CheckCircle, text: '智能预测分析', bgColor: '#fee2e2', iconColor: '#ef4444' },
        { icon: Timeline, text: '用户行为追踪', bgColor: '#fef3c7', iconColor: '#f59e0b' },
        { icon: Rocket, text: '自动化报告生成', bgColor: '#dcfce7', iconColor: '#10b981' }
      ]
    }
  ];

  // 切换动画函数
  const changeFeature = (newIndex) => {
    setIsAnimating(true);
    setTimeout(() => {
      setCurrentFeature(newIndex);
      setTimeout(() => {
        setIsAnimating(false);
      }, 50);
    }, 300);
  };

  // 自动轮播
  useEffect(() => {
    const interval = setInterval(() => {
      changeFeature((currentFeature + 1) % features.length);
    }, 5000); // 每5秒切换一次

    return () => clearInterval(interval);
  }, [currentFeature, features.length]);

  const handlePrevFeature = () => {
    const newIndex = (currentFeature - 1 + features.length) % features.length;
    changeFeature(newIndex);
  };

  const handleNextFeature = () => {
    const newIndex = (currentFeature + 1) % features.length;
    changeFeature(newIndex);
  };

  const handleIndicatorClick = (index) => {
    if (index !== currentFeature) {
      changeFeature(index);
    }
  };



  return (
    <Box>
      {/* Hero Section */}
      <Box
        sx={{
          background: 'linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #334155 50%, #475569 75%, #64748b 100%)',
          minHeight: '80vh',
          display: 'flex',
          alignItems: 'center',
          position: 'relative',
          overflow: 'hidden',
          py: 8,
        }}
      >
        {/* 动态背景装饰 */}
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: `
              radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.15) 0%, transparent 50%),
              radial-gradient(circle at 80% 20%, rgba(16, 185, 129, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 40% 40%, rgba(200, 200, 200, 0.1) 0%, transparent 50%)
            `,
            pointerEvents: 'none',
          }}
        />

        {/* 网格背景 */}
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundImage: `
              linear-gradient(rgba(255,255,255,0.02) 1px, transparent 1px),
              linear-gradient(90deg, rgba(255,255,255,0.02) 1px, transparent 1px)
            `,
            backgroundSize: '50px 50px',
            pointerEvents: 'none',
          }}
        />
        <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 1 }}>
          <Grid container spacing={6} alignItems="center">
            <Grid size={{ xs: 12, md: 6 }}>
              <Box sx={{ color: 'white' }}>
                <Chip
                  label="🚀 AI搜索优化领导者"
                  sx={{
                    mb: 3,
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    color: '#60a5fa',
                    border: '1px solid rgba(59, 130, 246, 0.2)',
                    fontWeight: 600,
                  }}
                />

                <Typography
                  variant="h1"
                  sx={{
                    fontSize: { xs: '2.5rem', md: '4rem' },
                    fontWeight: 800,
                    lineHeight: 1.1,
                    mb: 3,
                    background: 'linear-gradient(135deg, #ffffff 0%, #e2e8f0 50%, #cbd5e1 100%)',
                    backgroundClip: 'text',
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                  }}
                >
                  AI搜索时代的
                  <br />
                  <Box component="span" sx={{ color: '#3b82f6' }}>基础设施</Box>
                </Typography>

                <Typography
                  variant="h5"
                  sx={{
                    fontSize: { xs: '1.1rem', md: '1.4rem' },
                    fontWeight: 400,
                    lineHeight: 1.6,
                    mb: 4,
                    color: '#cbd5e1',
                    maxWidth: '500px',
                  }}
                >
                  为企业提供专业的AI搜索排名优化服务，
                  <Box component="span" sx={{ color: '#60a5fa', fontWeight: 600 }}>
                    帮助内容提供商建立盈利渠道
                  </Box>
                  。让您的品牌在ChatGPT、Claude、DeepSeek等AI平台获得优先推荐。
                </Typography>
                <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} sx={{ mb: 5 }}>
                  <Button
                    variant="contained"
                    size="large"
                    onClick={handleGetStarted}
                    startIcon={<Rocket />}
                    sx={{
                      px: 4,
                      py: 1.5,
                      fontSize: '1.1rem',
                      fontWeight: 600,
                      borderRadius: 2,
                      background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
                      boxShadow: '0 10px 40px rgba(59, 130, 246, 0.3)',
                      '&:hover': {
                        background: 'linear-gradient(135deg, #2563eb 0%, #1e40af 100%)',
                        transform: 'translateY(-2px)',
                        boxShadow: '0 15px 50px rgba(59, 130, 246, 0.4)',
                      },
                      transition: 'all 0.3s ease',
                    }}
                  >
                    免费开始优化
                  </Button>

                  <Button
                    variant="outlined"
                    size="large"
                    onClick={handleLearnMore}
                    sx={{
                      px: 4,
                      py: 1.5,
                      fontSize: '1.1rem',
                      fontWeight: 600,
                      borderRadius: 2,
                      borderColor: 'rgba(255, 255, 255, 0.2)',
                      color: 'white',
                      backdropFilter: 'blur(10px)',
                      '&:hover': {
                        borderColor: '#3b82f6',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        transform: 'translateY(-2px)',
                      },
                      transition: 'all 0.3s ease',
                    }}
                  >
                    查看演示
                  </Button>
                </Stack>

                {/* 实时统计数据 */}
                <Box
                  sx={{
                    background: 'rgba(255, 255, 255, 0.05)',
                    backdropFilter: 'blur(10px)',
                    borderRadius: 3,
                    p: 3,
                    border: '1px solid rgba(255, 255, 255, 0.1)',
                  }}
                >
                  <Typography variant="body2" sx={{ color: '#94a3b8', mb: 2, textAlign: 'center' }}>
                    实时数据统计
                  </Typography>
                  <Grid container spacing={3}>
                    <Grid size={{ xs: 6, sm: 3 }}>
                      <Box sx={{ textAlign: 'center' }}>
                        <Typography variant="h4" sx={{ fontWeight: 700, mb: 0.5, color: '#3b82f6' }}>
                          {stats.users.toLocaleString()}+
                        </Typography>
                        <Typography variant="body2" sx={{ color: '#cbd5e1' }}>
                          企业用户
                        </Typography>
                      </Box>
                    </Grid>
                    <Grid size={{ xs: 6, sm: 3 }}>
                      <Box sx={{ textAlign: 'center' }}>
                        <Typography variant="h4" sx={{ fontWeight: 700, mb: 0.5, color: '#10b981' }}>
                          {stats.content.toLocaleString()}+
                        </Typography>
                        <Typography variant="body2" sx={{ color: '#cbd5e1' }}>
                          优化内容
                        </Typography>
                      </Box>
                    </Grid>
                    <Grid size={{ xs: 6, sm: 3 }}>
                      <Box sx={{ textAlign: 'center' }}>
                        <Typography variant="h4" sx={{ fontWeight: 700, mb: 0.5, color: '#f59e0b' }}>
                          {stats.improvement}%
                        </Typography>
                        <Typography variant="body2" sx={{ color: '#cbd5e1' }}>
                          平均提升
                        </Typography>
                      </Box>
                    </Grid>
                    <Grid size={{ xs: 6, sm: 3 }}>
                      <Box sx={{ textAlign: 'center' }}>
                        <Typography variant="h4" sx={{ fontWeight: 700, mb: 0.5, color: '#8b5cf6' }}>
                          {stats.platforms}+
                        </Typography>
                        <Typography variant="body2" sx={{ color: '#cbd5e1' }}>
                          AI平台
                        </Typography>
                      </Box>
                    </Grid>
                  </Grid>
                </Box>
              </Box>
            </Grid>

            <Grid size={{ xs: 12, md: 6 }}>
              <Box
                sx={{
                  position: 'relative',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  height: '600px',
                }}
              >
                {/* 轮播控制按钮 */}
                <IconButton
                  onClick={handlePrevFeature}
                  sx={{
                    position: 'absolute',
                    left: -20,
                    zIndex: 10,
                    backgroundColor: 'rgba(255, 255, 255, 0.9)',
                    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                    '&:hover': {
                      backgroundColor: 'rgba(255, 255, 255, 1)',
                      transform: 'scale(1.1)',
                    },
                  }}
                >
                  <ArrowBackIos />
                </IconButton>
                
                <IconButton
                  onClick={handleNextFeature}
                  sx={{
                    position: 'absolute',
                    right: -20,
                    zIndex: 10,
                    backgroundColor: 'rgba(255, 255, 255, 0.9)',
                    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                    '&:hover': {
                      backgroundColor: 'rgba(255, 255, 255, 1)',
                      transform: 'scale(1.1)',
                    },
                  }}
                >
                  <ArrowForwardIos />
                </IconButton>

                {/* 轮播指示器 */}
                <Box
                  sx={{
                    position: 'absolute',
                    bottom: 20,
                    display: 'flex',
                    gap: 1,
                    zIndex: 10,
                  }}
                >
                  {features.map((_, index) => (
                    <Box
                      key={index}
                      onClick={() => handleIndicatorClick(index)}
                      sx={{
                        width: index === currentFeature ? 24 : 8,
                        height: 8,
                        borderRadius: 4,
                        backgroundColor: index === currentFeature ? features[currentFeature].color : 'rgba(255, 255, 255, 0.5)',
                        cursor: 'pointer',
                        transition: 'all 0.3s ease',
                      }}
                    />
                  ))}
                </Box>

                {/* 功能特性卡片 */}
                <Card
                  sx={{
                    maxWidth: 450,
                    minHeight: 500,
                    background: 'rgba(255, 255, 255, 0.95)',
                    backdropFilter: 'blur(20px)',
                    borderRadius: 4,
                    boxShadow: '0 25px 80px rgba(0, 0, 0, 0.15)',
                    border: '1px solid rgba(255, 255, 255, 0.2)',
                    transform: isAnimating 
                      ? 'perspective(1000px) rotateY(90deg) scale(0.8)' 
                      : 'perspective(1000px) rotateY(-8deg) rotateX(5deg)',
                    opacity: isAnimating ? 0 : 1,
                    transition: 'all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55)',
                    '&:hover': {
                      transform: isAnimating 
                        ? 'perspective(1000px) rotateY(90deg) scale(0.8)'
                        : 'perspective(1000px) rotateY(0deg) rotateX(0deg) translateY(-10px)',
                      boxShadow: '0 35px 100px rgba(0, 0, 0, 0.2)',
                    },
                  }}
                >
                  <CardContent sx={{ p: 4, height: '100%', display: 'flex', flexDirection: 'column' }}>
                    {/* 根据component类型渲染不同内容 */}
                    {features[currentFeature].component === 'default' ? (
                      // 默认的分析界面
                      <>
                        {/* 头部 */}
                        <Box sx={{ 
                          display: 'flex', 
                          alignItems: 'center', 
                          mb: 3,
                          animation: !isAnimating ? 'slideInFromTop 0.6s ease-out' : 'none',
                          '@keyframes slideInFromTop': {
                            '0%': { opacity: 0, transform: 'translateY(-20px)' },
                            '100%': { opacity: 1, transform: 'translateY(0)' }
                          }
                        }}>
                          <Avatar
                            sx={{
                              width: 48,
                              height: 48,
                              background: `linear-gradient(135deg, ${features[currentFeature].color} 0%, ${features[currentFeature].color}dd 100%)`,
                              mr: 2,
                              animation: !isAnimating ? 'rotateIn 0.8s ease-out' : 'none',
                              '@keyframes rotateIn': {
                                '0%': { transform: 'rotate(-180deg) scale(0)', opacity: 0 },
                                '100%': { transform: 'rotate(0) scale(1)', opacity: 1 }
                              }
                            }}
                          >
                            {React.createElement(features[currentFeature].icon)}
                          </Avatar>
                          <Box sx={{ flex: 1 }}>
                            <Typography variant="h6" sx={{ 
                              fontWeight: 700, 
                              color: '#1e293b',
                              animation: !isAnimating ? 'fadeInLeft 0.7s ease-out' : 'none',
                              '@keyframes fadeInLeft': {
                                '0%': { opacity: 0, transform: 'translateX(-30px)' },
                                '100%': { opacity: 1, transform: 'translateX(0)' }
                              }
                            }}>
                              {features[currentFeature].title}
                            </Typography>
                            <Typography variant="body2" sx={{ 
                              color: features[currentFeature].color,
                              animation: !isAnimating ? 'fadeInLeft 0.9s ease-out' : 'none',
                            }}>
                              ● {features[currentFeature].subtitle}
                            </Typography>
                          </Box>
                          <Chip
                            label={`${features[currentFeature].score}%`}
                            sx={{
                              background: `linear-gradient(135deg, ${features[currentFeature].color} 0%, ${features[currentFeature].color}dd 100%)`,
                              color: 'white',
                              fontWeight: 700,
                              animation: !isAnimating ? 'bounceIn 1s ease-out' : 'none',
                              '@keyframes bounceIn': {
                                '0%': { transform: 'scale(0)', opacity: 0 },
                                '50%': { transform: 'scale(1.2)' },
                                '100%': { transform: 'scale(1)', opacity: 1 }
                              }
                            }}
                          />
                        </Box>

                        {/* 分析进度 */}
                        <Box sx={{ mb: 3 }}>
                          <Typography variant="body2" sx={{ 
                            color: '#64748b', 
                            mb: 2,
                            animation: !isAnimating ? 'fadeIn 0.8s ease-out' : 'none',
                            '@keyframes fadeIn': {
                              '0%': { opacity: 0 },
                              '100%': { opacity: 1 }
                            }
                          }}>
                            优化进度
                          </Typography>
                          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                            {features[currentFeature].progress && features[currentFeature].progress.map((item, index) => (
                              <Box key={index} sx={{
                                animation: !isAnimating ? `slideInFromRight ${0.5 + index * 0.2}s ease-out` : 'none',
                                '@keyframes slideInFromRight': {
                                  '0%': { opacity: 0, transform: 'translateX(50px)' },
                                  '100%': { opacity: 1, transform: 'translateX(0)' }
                                }
                              }}>
                                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                                  <Typography variant="body2" sx={{ fontWeight: 600 }}>{item.label}</Typography>
                                  <Typography variant="body2" sx={{ color: item.color }}>{item.status}</Typography>
                                </Box>
                                <Box sx={{ height: 6, backgroundColor: '#f1f5f9', borderRadius: 3, overflow: 'hidden' }}>
                                  <Box sx={{ 
                                    height: '100%', 
                                    width: !isAnimating ? `${Math.min(item.value, 100)}%` : '0%', 
                                    backgroundColor: item.color, 
                                    borderRadius: 3,
                                    transition: 'width 1.5s ease-out',
                                    transitionDelay: !isAnimating ? `${0.3 + index * 0.1}s` : '0s'
                                  }} />
                                </Box>
                              </Box>
                            ))}
                          </Box>
                        </Box>

                        {/* 优化建议 */}
                        <Box sx={{ mb: 3 }}>
                          <Typography variant="body2" sx={{ 
                            color: '#64748b', 
                            mb: 2, 
                            fontWeight: 600,
                            animation: !isAnimating ? 'fadeIn 1s ease-out' : 'none',
                          }}>
                            AI优化建议
                          </Typography>
                          <Stack spacing={1}>
                            {features[currentFeature].features && features[currentFeature].features.map((feature, index) => (
                              <Box 
                                key={index}
                                sx={{ 
                                  display: 'flex', 
                                  alignItems: 'center', 
                                  p: 1.5, 
                                  backgroundColor: feature.bgColor, 
                                  borderRadius: 2,
                                  transition: 'all 0.3s ease',
                                  animation: !isAnimating ? `fadeInUp ${1 + index * 0.15}s ease-out` : 'none',
                                  '@keyframes fadeInUp': {
                                    '0%': { opacity: 0, transform: 'translateY(20px)' },
                                    '100%': { opacity: 1, transform: 'translateY(0)' }
                                  },
                                  '&:hover': {
                                    transform: 'translateX(5px)',
                                  }
                                }}
                              >
                                {React.createElement(feature.icon, { 
                                  sx: { 
                                    color: feature.iconColor, 
                                    fontSize: 16, 
                                    mr: 1,
                                    animation: !isAnimating ? 'rotateIcon 0.5s ease-out' : 'none',
                                    animationDelay: `${1 + index * 0.15}s`,
                                    '@keyframes rotateIcon': {
                                      '0%': { transform: 'rotate(0deg)' },
                                      '100%': { transform: 'rotate(360deg)' }
                                    }
                                  } 
                                })}
                                <Typography variant="body2" sx={{ fontWeight: 500 }}>{feature.text}</Typography>
                              </Box>
                            ))}
                          </Stack>
                        </Box>

                        <Box sx={{ mt: 'auto' }}>
                          <Button
                            variant="contained"
                            fullWidth
                            onClick={handleGetStarted}
                            sx={{
                              py: 1.5,
                              borderRadius: 2,
                              background: `linear-gradient(135deg, ${features[currentFeature].color} 0%, ${features[currentFeature].color}dd 100%)`,
                              fontWeight: 600,
                              transition: 'all 0.5s ease',
                              animation: !isAnimating ? 'pulseButton 1.5s ease-out' : 'none',
                              '@keyframes pulseButton': {
                                '0%': { transform: 'scale(0.9)', opacity: 0 },
                                '50%': { transform: 'scale(1.05)' },
                                '100%': { transform: 'scale(1)', opacity: 1 }
                              },
                              '&:hover': {
                                background: `linear-gradient(135deg, ${features[currentFeature].color}dd 0%, ${features[currentFeature].color} 100%)`,
                                transform: 'translateY(-2px)',
                                boxShadow: `0 10px 25px ${features[currentFeature].color}40`,
                              },
                            }}
                          >
                            立即体验
                          </Button>
                        </Box>
                      </>
                    ) : (
                      // 自定义组件
                      <Suspense 
                        fallback={
                          <Box sx={{ 
                            display: 'flex', 
                            alignItems: 'center', 
                            justifyContent: 'center',
                            height: '100%'
                          }}>
                            <CircularProgress />
                          </Box>
                        }
                      >
                        <Box sx={{
                          animation: !isAnimating ? 'fadeIn 0.5s ease-out' : 'none',
                          '@keyframes fadeIn': {
                            '0%': { opacity: 0 },
                            '100%': { opacity: 1 }
                          }
                        }}>
                          {React.createElement(features[currentFeature].component)}
                        </Box>
                      </Suspense>
                    )}
                  </CardContent>
                </Card>

                {/* 浮动统计卡片 */}
                <Box
                  sx={{
                    position: 'absolute',
                    top: 50,
                    right: -30,
                    background: 'rgba(255, 255, 255, 0.95)',
                    borderRadius: 3,
                    p: 2.5,
                    boxShadow: '0 15px 40px rgba(0, 0, 0, 0.1)',
                    border: '1px solid rgba(255, 255, 255, 0.2)',
                    animation: 'float 4s ease-in-out infinite',
                    '@keyframes float': {
                      '0%, 100%': { transform: 'translateY(0px)' },
                      '50%': { transform: 'translateY(-15px)' },
                    },
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
                    <Avatar sx={{ width: 32, height: 32, backgroundColor: '#dcfce7' }}>
                      <Rocket sx={{ color: '#10b981', fontSize: 18 }} />
                    </Avatar>
                    <Box>
                      <Typography variant="h6" sx={{ fontWeight: 700, color: '#1e293b', lineHeight: 1 }}>
                        +300%
                      </Typography>
                      <Typography variant="body2" sx={{ color: '#64748b', fontSize: '0.75rem' }}>
                        AI搜索排名
                      </Typography>
                    </Box>
                  </Box>
                </Box>

                <Box
                  sx={{
                    position: 'absolute',
                    bottom: 80,
                    left: -40,
                    background: 'rgba(255, 255, 255, 0.95)',
                    borderRadius: 3,
                    p: 2.5,
                    boxShadow: '0 15px 40px rgba(0, 0, 0, 0.1)',
                    border: '1px solid rgba(255, 255, 255, 0.2)',
                    animation: 'float 4s ease-in-out infinite 2s',
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
                    <Avatar sx={{ width: 32, height: 32, backgroundColor: '#dbeafe' }}>
                      <Timeline sx={{ color: '#3b82f6', fontSize: 18 }} />
                    </Avatar>
                    <Box>
                      <Typography variant="h6" sx={{ fontWeight: 700, color: '#1e293b', lineHeight: 1 }}>
                        +500%
                      </Typography>
                      <Typography variant="body2" sx={{ color: '#64748b', fontSize: '0.75rem' }}>
                        曝光增长
                      </Typography>
                    </Box>
                  </Box>
                </Box>
              </Box>
            </Grid>
          </Grid>
        </Container>
      </Box>

      {/* Trusted by Companies Section */}
      <Box sx={{ py: 6, bgcolor: 'background.paper' }}>
        <Container maxWidth="lg">
          <Box sx={{ textAlign: 'center', mb: 4 }}>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
              支持各行业
            </Typography>
            <Box sx={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              gap: { xs: 3, md: 6 },
              flexWrap: 'wrap',
              opacity: 0.7
            }}>
              <Typography variant="h6" sx={{ fontWeight: 600, color: 'text.secondary' }}>
                电商
              </Typography>
              <Typography variant="h6" sx={{ fontWeight: 600, color: 'text.secondary' }}>
                教育
              </Typography>
              <Typography variant="h6" sx={{ fontWeight: 600, color: 'text.secondary' }}>
                制造
              </Typography>
              <Typography variant="h6" sx={{ fontWeight: 600, color: 'text.secondary' }}>
                科技
              </Typography>
              <Typography variant="h6" sx={{ fontWeight: 600, color: 'text.secondary' }}>
                餐饮
              </Typography>
              <Typography variant="h6" sx={{ fontWeight: 600, color: 'text.secondary' }}>
                旅游
              </Typography>
              <Typography variant="h6" sx={{ fontWeight: 600, color: 'text.secondary' }}>
                零售
              </Typography>
              <Typography variant="h6" sx={{ fontWeight: 600, color: 'text.secondary' }}>
                娱乐
              </Typography>
            </Box>
          </Box>
        </Container>
      </Box>

      {/* AI Optimization Features */}
      <Box sx={{ py: 12, bgcolor: '#f8fafc' }}>
        <Container maxWidth="lg">
          <Grid container spacing={8} alignItems="center">
            <Grid size={{ xs: 12, md: 6 }}>
              <Box>
                <Typography variant="h3" sx={{ fontWeight: 700, mb: 3, color: '#1e293b' }}>
                  智能预测，精准优化
                </Typography>
                <Typography variant="h6" sx={{ mb: 4, color: '#64748b', lineHeight: 1.6 }}>
                  AI搜索优化平台会根据上下文预测您的下一步优化策略，让您轻松完成内容改进。
                </Typography>
                <Box sx={{
                  background: 'rgba(59, 130, 246, 0.05)',
                  borderRadius: 2,
                  p: 3,
                  border: '1px solid rgba(59, 130, 246, 0.1)'
                }}>
                  <Typography variant="body1" sx={{ fontFamily: 'monospace', color: '#3b82f6' }}>
                    输入关键词，生成标题，优化内容
                  </Typography>
                </Box>
              </Box>
            </Grid>
            <Grid size={{ xs: 12, md: 6 }}>
              <Box sx={{
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                borderRadius: 3,
                p: 4,
                color: 'white',
                position: 'relative',
                overflow: 'hidden'
              }}>
                <Typography variant="h6" sx={{ mb: 2 }}>
                  AI内容优化建议
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.9 }}>
                  基于深度学习算法，实时分析您的内容质量，提供个性化优化建议
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </Container>
      </Box>

      <Box sx={{ py: 12, bgcolor: 'background.paper' }}>
        <Container maxWidth="lg">
          <Grid container spacing={8} alignItems="center">
            <Grid size={{ xs: 12, md: 6 }} sx={{ order: { xs: 2, md: 1 } }}>
              <Box sx={{
                background: 'linear-gradient(135deg, #11998e 0%, #38ef7d 100%)',
                borderRadius: 3,
                p: 4,
                color: 'white',
                position: 'relative',
                overflow: 'hidden'
              }}>
                <Typography variant="h6" sx={{ mb: 2 }}>
                  智能内容分析
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.9 }}>
                  从您的内容库或文档中获取洞察，还能引用具体文件进行优化
                </Typography>
              </Box>
            </Grid>
            <Grid size={{ xs: 12, md: 6 }} sx={{ order: { xs: 1, md: 2 } }}>
              <Box>
                <Typography variant="h3" sx={{ fontWeight: 700, mb: 3, color: '#1e293b' }}>
                  深度理解您的内容
                </Typography>
                <Typography variant="h6" sx={{ mb: 4, color: '#64748b', lineHeight: 1.6 }}>
                  从您的内容库或文档中获取答案，还能引用具体文件。一键即可应用AI生成的优化建议。
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </Container>
      </Box>

      <Box sx={{ py: 12, bgcolor: '#f8fafc' }}>
        <Container maxWidth="lg">
          <Grid container spacing={8} alignItems="center">
            <Grid size={{ xs: 12, md: 6 }}>
              <Box>
                <Typography variant="h3" sx={{ fontWeight: 700, mb: 3, color: '#1e293b' }}>
                  用自然语言编辑
                </Typography>
                <Typography variant="h6" sx={{ mb: 4, color: '#64748b', lineHeight: 1.6 }}>
                  借助AI搜索优化平台，您可以通过简单指令来优化或修改内容。仅需一个提示，就能更新整段文章或产品描述。
                </Typography>
              </Box>
            </Grid>
            <Grid size={{ xs: 12, md: 6 }}>
              <Box sx={{
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                borderRadius: 3,
                p: 4,
                color: 'white',
                position: 'relative',
                overflow: 'hidden'
              }}>
                <Typography variant="h6" sx={{ mb: 2 }}>
                  自然语言处理
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.9 }}>
                  "优化这段产品描述，让它更容易被AI搜索引擎理解和推荐"
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </Container>
      </Box>

      {/* Customer Reviews Section */}
      <Box sx={{ py: 12, bgcolor: 'background.paper', overflow: 'hidden' }}>
        <Container maxWidth="lg">
          <Box sx={{ textAlign: 'center', mb: 8 }}>
            <Typography variant="h3" sx={{ fontWeight: 700, mb: 3, color: '#1e293b' }}>
              深受喜爱
            </Typography>
            <Typography variant="body1" sx={{ color: '#64748b' }}>
              众多企业和内容创作者都选择使用极优云创。
            </Typography>
          </Box>

          {/* 动态评价墙 */}
          <Box sx={{
            position: 'relative',
            height: '600px',
            overflow: 'hidden',
            '&::before': {
              content: '""',
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              height: '50px',
              background: 'linear-gradient(to bottom, rgba(255,255,255,1), rgba(255,255,255,0))',
              zIndex: 2
            },
            '&::after': {
              content: '""',
              position: 'absolute',
              bottom: 0,
              left: 0,
              right: 0,
              height: '50px',
              background: 'linear-gradient(to top, rgba(255,255,255,1), rgba(255,255,255,0))',
              zIndex: 2
            }
          }}>
            {/* 左侧列 */}
            <Box sx={{
              position: 'absolute',
              left: '5%',
              width: '28%',
              animation: 'slideUp 40s linear infinite',
              '@keyframes slideUp': {
                '0%': { transform: 'translateY(0%)' },
                '100%': { transform: 'translateY(-50%)' }
              }
            }}>
              {/* 评价卡片 1 */}
              <Card sx={{
                p: 3,
                mb: 3,
                border: '1px solid #e5e7eb',
                borderRadius: 3,
                bgcolor: 'white',
                cursor: 'pointer',
                '&:hover': {
                  bgcolor: '#f8fafc',
                  borderColor: '#3b82f6',
                  transform: 'scale(1.02)',
                  boxShadow: '0 10px 25px rgba(59, 130, 246, 0.15)'
                },
                transition: 'all 0.3s ease'
              }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Avatar sx={{ width: 40, height: 40, mr: 2, bgcolor: '#e3f2fd' }}>
                    <Typography variant="body2" sx={{ color: '#1976d2', fontWeight: 600 }}>
                      A
                    </Typography>
                  </Avatar>
                  <Box>
                    <Typography variant="subtitle2" sx={{ fontWeight: 600, color: '#1e293b' }}>
                      张明
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      电商运营总监
                    </Typography>
                  </Box>
                </Box>
                <Typography variant="body2" color="text.secondary" sx={{ lineHeight: 1.6 }}>
                  极优云创的SEO优化服务让我们的产品页面排名提升了300%，转化率也大幅增长，真正实现了精准获客。
                </Typography>
              </Card>

              {/* 评价卡片 2 */}
              <Card sx={{
                p: 3,
                mb: 3,
                border: '1px solid #e5e7eb',
                borderRadius: 3,
                bgcolor: 'white',
                cursor: 'pointer',
                '&:hover': {
                  bgcolor: '#f0fdf4',
                  borderColor: '#22c55e',
                  transform: 'scale(1.02)',
                  boxShadow: '0 10px 25px rgba(34, 197, 94, 0.15)'
                },
                transition: 'all 0.3s ease'
              }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Avatar sx={{ width: 40, height: 40, mr: 2, bgcolor: '#dcfce7' }}>
                    <Typography variant="body2" sx={{ color: '#16a34a', fontWeight: 600 }}>
                      Z
                    </Typography>
                  </Avatar>
                  <Box>
                    <Typography variant="subtitle2" sx={{ fontWeight: 600, color: '#1e293b' }}>
                      李华
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      创业公司CEO
                    </Typography>
                  </Box>
                </Box>
                <Typography variant="body2" color="text.secondary" sx={{ lineHeight: 1.6 }}>
                  使用极优云创的内容优化服务后，我们网站的自然流量增长了5倍，获客成本降低了60%。
                </Typography>
              </Card>

              {/* 评价卡片 3 */}
              <Card sx={{
                p: 3,
                mb: 3,
                border: '1px solid #e5e7eb',
                borderRadius: 3,
                bgcolor: 'white',
                cursor: 'pointer',
                '&:hover': {
                  bgcolor: '#fef3c7',
                  borderColor: '#f59e0b',
                  transform: 'scale(1.02)',
                  boxShadow: '0 10px 25px rgba(245, 158, 11, 0.15)'
                },
                transition: 'all 0.3s ease'
              }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Avatar sx={{ width: 40, height: 40, mr: 2, bgcolor: '#fef3c7' }}>
                    <Typography variant="body2" sx={{ color: '#d97706', fontWeight: 600 }}>
                      王
                    </Typography>
                  </Avatar>
                  <Box>
                    <Typography variant="subtitle2" sx={{ fontWeight: 600, color: '#1e293b' }}>
                      王小明
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      营销总监
                    </Typography>
                  </Box>
                </Box>
                <Typography variant="body2" color="text.secondary" sx={{ lineHeight: 1.6 }}>
                  极优云创的AI内容优化让我们的文章在搜索引擎中的表现大幅提升，点击率提高了200%。
                </Typography>
              </Card>

              {/* 评价卡片 4 */}
              <Card sx={{
                p: 3,
                mb: 3,
                border: '1px solid #e5e7eb',
                borderRadius: 3,
                bgcolor: 'white',
                cursor: 'pointer',
                '&:hover': {
                  bgcolor: '#f8fafc',
                  borderColor: '#3b82f6',
                  transform: 'scale(1.02)',
                  boxShadow: '0 10px 25px rgba(59, 130, 246, 0.15)'
                },
                transition: 'all 0.3s ease'
              }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Avatar sx={{ width: 40, height: 40, mr: 2, bgcolor: '#e3f2fd' }}>
                    <Typography variant="body2" sx={{ color: '#1976d2', fontWeight: 600 }}>
                      陈
                    </Typography>
                  </Avatar>
                  <Box>
                    <Typography variant="subtitle2" sx={{ fontWeight: 600, color: '#1e293b' }}>
                      陈佳
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      产品经理
                    </Typography>
                  </Box>
                </Box>
                <Typography variant="body2" color="text.secondary" sx={{ lineHeight: 1.6 }}>
                  通过极优云创的关键词优化策略，我们的产品页面在百度首页的排名从无到有，现在稳定在前3位。
                </Typography>
              </Card>
            </Box>

            {/* 中间列 */}
            <Box sx={{
              position: 'absolute',
              left: '36%',
              width: '28%',
              animation: 'slideDown 45s linear infinite',
              '@keyframes slideDown': {
                '0%': { transform: 'translateY(-50%)' },
                '100%': { transform: 'translateY(50%)' }
              }
            }}>
              {/* 评价卡片 3 */}
              <Card sx={{
                p: 3,
                mb: 3,
                border: '1px solid #e5e7eb',
                borderRadius: 3,
                bgcolor: 'white',
                cursor: 'pointer',
                '&:hover': {
                  bgcolor: '#fef3c7',
                  borderColor: '#f59e0b',
                  transform: 'scale(1.02)',
                  boxShadow: '0 10px 25px rgba(245, 158, 11, 0.15)'
                },
                transition: 'all 0.3s ease'
              }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Avatar sx={{ width: 40, height: 40, mr: 2, bgcolor: '#fef3c7' }}>
                    <Typography variant="body2" sx={{ color: '#d97706', fontWeight: 600 }}>
                      S
                    </Typography>
                  </Avatar>
                  <Box>
                    <Typography variant="subtitle2" sx={{ fontWeight: 600, color: '#1e293b' }}>
                      刘强
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      互联网公司CTO
                    </Typography>
                  </Box>
                </Box>
                <Typography variant="body2" color="text.secondary" sx={{ lineHeight: 1.6 }}>
                  极优云创的技术SEO优化服务帮助我们解决了网站加载速度和结构化数据的问题，搜索排名显著提升。
                </Typography>
              </Card>

              {/* 评价卡片 4 */}
              <Card sx={{
                p: 3,
                mb: 3,
                border: '1px solid #e5e7eb',
                borderRadius: 3,
                bgcolor: 'white',
                cursor: 'pointer',
                '&:hover': {
                  bgcolor: '#fdf2f8',
                  borderColor: '#ec4899',
                  transform: 'scale(1.02)',
                  boxShadow: '0 10px 25px rgba(236, 72, 153, 0.15)'
                },
                transition: 'all 0.3s ease'
              }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Avatar sx={{ width: 40, height: 40, mr: 2, bgcolor: '#fdf2f8' }}>
                    <Typography variant="body2" sx={{ color: '#be185d', fontWeight: 600 }}>
                      W
                    </Typography>
                  </Avatar>
                  <Box>
                    <Typography variant="subtitle2" sx={{ fontWeight: 600, color: '#1e293b' }}>
                      赵敏
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      内容运营主管
                    </Typography>
                  </Box>
                </Box>
                <Typography variant="body2" color="text.secondary" sx={{ lineHeight: 1.6 }}>
                  极优云创的内容优化建议非常精准，我们按照建议调整后，文章的搜索曝光量增长了400%，用户参与度也大幅提升。
                </Typography>
              </Card>

              {/* 评价卡片 5 */}
              <Card sx={{
                p: 3,
                mb: 3,
                border: '1px solid #e5e7eb',
                borderRadius: 3,
                bgcolor: 'white',
                cursor: 'pointer',
                '&:hover': {
                  bgcolor: '#f0fdf4',
                  borderColor: '#22c55e',
                  transform: 'scale(1.02)',
                  boxShadow: '0 10px 25px rgba(34, 197, 94, 0.15)'
                },
                transition: 'all 0.3s ease'
              }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Avatar sx={{ width: 40, height: 40, mr: 2, bgcolor: '#dcfce7' }}>
                    <Typography variant="body2" sx={{ color: '#16a34a', fontWeight: 600 }}>
                      孙
                    </Typography>
                  </Avatar>
                  <Box>
                    <Typography variant="subtitle2" sx={{ fontWeight: 600, color: '#1e293b' }}>
                      孙丽
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      数字营销专家
                    </Typography>
                  </Box>
                </Box>
                <Typography variant="body2" color="text.secondary" sx={{ lineHeight: 1.6 }}>
                  极优云创的竞争对手分析报告让我们找到了市场空白，制定的SEO策略帮助我们在细分领域占据了领先地位。
                </Typography>
              </Card>

              {/* 评价卡片 6 */}
              <Card sx={{
                p: 3,
                mb: 3,
                border: '1px solid #e5e7eb',
                borderRadius: 3,
                bgcolor: 'white',
                cursor: 'pointer',
                '&:hover': {
                  bgcolor: '#fef3c7',
                  borderColor: '#f59e0b',
                  transform: 'scale(1.02)',
                  boxShadow: '0 10px 25px rgba(245, 158, 11, 0.15)'
                },
                transition: 'all 0.3s ease'
              }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Avatar sx={{ width: 40, height: 40, mr: 2, bgcolor: '#fef3c7' }}>
                    <Typography variant="body2" sx={{ color: '#d97706', fontWeight: 600 }}>
                      周
                    </Typography>
                  </Avatar>
                  <Box>
                    <Typography variant="subtitle2" sx={{ fontWeight: 600, color: '#1e293b' }}>
                      周杰
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      电商平台负责人
                    </Typography>
                  </Box>
                </Box>
                <Typography variant="body2" color="text.secondary" sx={{ lineHeight: 1.6 }}>
                  使用极优云创的本地SEO服务后，我们在本地搜索结果中的排名大幅提升，线下门店的客流量增长了150%。
                </Typography>
              </Card>
            </Box>

            {/* 右侧列 */}
            <Box sx={{
              position: 'absolute',
              right: '5%',
              width: '28%',
              animation: 'slideUp 50s linear infinite',
              '@keyframes slideUp': {
                '0%': { transform: 'translateY(0%)' },
                '100%': { transform: 'translateY(-50%)' }
              }
            }}>
              {/* 评价卡片 5 */}
              <Card sx={{
                p: 3,
                mb: 3,
                border: '1px solid #e5e7eb',
                borderRadius: 3,
                bgcolor: 'white',
                cursor: 'pointer',
                '&:hover': {
                  bgcolor: '#f0f9ff',
                  borderColor: '#0ea5e9',
                  transform: 'scale(1.02)',
                  boxShadow: '0 10px 25px rgba(14, 165, 233, 0.15)'
                },
                transition: 'all 0.3s ease'
              }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Avatar sx={{ width: 40, height: 40, mr: 2, bgcolor: '#f0f9ff' }}>
                    <Typography variant="body2" sx={{ color: '#0284c7', fontWeight: 600 }}>
                      K
                    </Typography>
                  </Avatar>
                  <Box>
                    <Typography variant="subtitle2" sx={{ fontWeight: 600, color: '#1e293b' }}>
                      吴磊
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      科技公司创始人
                    </Typography>
                  </Box>
                </Box>
                <Typography variant="body2" color="text.secondary" sx={{ lineHeight: 1.6 }}>
                  极优云创的移动端SEO优化让我们的APP在应用商店的排名大幅提升，下载量增长了300%，用户质量也明显提高。
                </Typography>
              </Card>

              {/* 评价卡片 6 */}
              <Card sx={{
                p: 3,
                mb: 3,
                border: '1px solid #e5e7eb',
                borderRadius: 3,
                bgcolor: 'white',
                cursor: 'pointer',
                '&:hover': {
                  bgcolor: '#f5f3ff',
                  borderColor: '#8b5cf6',
                  transform: 'scale(1.02)',
                  boxShadow: '0 10px 25px rgba(139, 92, 246, 0.15)'
                },
                transition: 'all 0.3s ease'
              }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Avatar sx={{ width: 40, height: 40, mr: 2, bgcolor: '#f5f3ff' }}>
                    <Typography variant="body2" sx={{ color: '#7c3aed', fontWeight: 600 }}>
                      S
                    </Typography>
                  </Avatar>
                  <Box>
                    <Typography variant="subtitle2" sx={{ fontWeight: 600, color: '#1e293b' }}>
                      郑雪
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      品牌营销总监
                    </Typography>
                  </Box>
                </Box>
                <Typography variant="body2" color="text.secondary" sx={{ lineHeight: 1.6 }}>
                  极优云创的品牌SEO策略帮助我们在行业关键词搜索中建立了强势地位，品牌知名度和影响力都得到了显著提升。
                </Typography>
              </Card>

              {/* 评价卡片 7 */}
              <Card sx={{
                p: 3,
                mb: 3,
                border: '1px solid #e5e7eb',
                borderRadius: 3,
                bgcolor: 'white',
                cursor: 'pointer',
                '&:hover': {
                  bgcolor: '#fdf2f8',
                  borderColor: '#ec4899',
                  transform: 'scale(1.02)',
                  boxShadow: '0 10px 25px rgba(236, 72, 153, 0.15)'
                },
                transition: 'all 0.3s ease'
              }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Avatar sx={{ width: 40, height: 40, mr: 2, bgcolor: '#fdf2f8' }}>
                    <Typography variant="body2" sx={{ color: '#be185d', fontWeight: 600 }}>
                      黄
                    </Typography>
                  </Avatar>
                  <Box>
                    <Typography variant="subtitle2" sx={{ fontWeight: 600, color: '#1e293b' }}>
                      黄伟
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      跨境电商负责人
                    </Typography>
                  </Box>
                </Box>
                <Typography variant="body2" color="text.secondary" sx={{ lineHeight: 1.6 }}>
                  极优云创的多语言SEO服务帮助我们在海外市场快速建立了品牌影响力，国际订单量增长了500%。
                </Typography>
              </Card>

              {/* 评价卡片 8 */}
              <Card sx={{
                p: 3,
                mb: 3,
                border: '1px solid #e5e7eb',
                borderRadius: 3,
                bgcolor: 'white',
                cursor: 'pointer',
                '&:hover': {
                  bgcolor: '#f0f9ff',
                  borderColor: '#0ea5e9',
                  transform: 'scale(1.02)',
                  boxShadow: '0 10px 25px rgba(14, 165, 233, 0.15)'
                },
                transition: 'all 0.3s ease'
              }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Avatar sx={{ width: 40, height: 40, mr: 2, bgcolor: '#f0f9ff' }}>
                    <Typography variant="body2" sx={{ color: '#0284c7', fontWeight: 600 }}>
                      林
                    </Typography>
                  </Avatar>
                  <Box>
                    <Typography variant="subtitle2" sx={{ fontWeight: 600, color: '#1e293b' }}>
                      林晓
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      内容创作者
                    </Typography>
                  </Box>
                </Box>
                <Typography variant="body2" color="text.secondary" sx={{ lineHeight: 1.6 }}>
                  通过极优云创的内容策略指导，我的文章在各大平台的阅读量和互动率都有了质的飞跃，粉丝增长了10倍。
                </Typography>
              </Card>
            </Box>
          </Box>
        </Container>
      </Box>

      {/* Help Document Center */}
      <Suspense fallback={<Box sx={{ py: 8, textAlign: 'center' }}><Typography>加载中...</Typography></Box>}>
        <HelpDocumentCenter />
      </Suspense>

      {/* Footer */}
      <Footer />
    </Box>
  );
}

export default Home;
