import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Typography,
  Chip,
  Paper,
  List,
  ListItemButton,
  <PERSON>lapse,
  Drawer,
  IconButton,
  Divider,
  Button,
  Alert,
  CircularProgress,
} from '@mui/material';
import {
  Visibility,
  AccessTime,
  ExpandMore,
  ExpandLess,
  Category,
  Menu,
  Close,
  ArrowBack,
  NavigateBefore,
  NavigateNext,
} from '@mui/icons-material';
import { useNavigate, useSearchParams } from 'react-router-dom';
import axios from 'axios';
import { API_BASE_URL } from '../config/api-config';
import MarkdownRenderer from '../components/MarkdownRenderer';

const HelpCenter = () => {
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  
  const [categories, setCategories] = useState([]);
  const [documents, setDocuments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchKeyword, setSearchKeyword] = useState(searchParams.get('q') || '');
  const [selectedCategory, setSelectedCategory] = useState(
    searchParams.get('category') ? parseInt(searchParams.get('category')) : null
  );
  const [currentPage, setCurrentPage] = useState(
    searchParams.get('page') ? parseInt(searchParams.get('page')) : 1
  );
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [expandedCategories, setExpandedCategories] = useState({});
  const [selectedDocument, setSelectedDocument] = useState(null);
  const [mobileOpen, setMobileOpen] = useState(false);
  const [documentContent, setDocumentContent] = useState(null);
  const [documentLoading, setDocumentLoading] = useState(false);

  // 获取分类列表
  const fetchCategories = async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/api/v1/help/categories`);
      setCategories(response.data || []);
    } catch (err) {
      console.error('获取分类失败:', err);
    }
  };

  // 获取文档列表
  const fetchDocuments = async () => {
    try {
      setLoading(true);
      const params = {
        page: currentPage,
        page_size: 12,
      };
      
      if (selectedCategory) {
        params.category_id = selectedCategory;
      }
      
      if (searchKeyword.trim()) {
        params.keyword = searchKeyword.trim();
      }

      const response = await axios.get(`${API_BASE_URL}/api/v1/help/documents`, { params });
      const data = response.data;
      
      setDocuments(data.items || []);
      setTotalCount(data.total || 0);
      setTotalPages(Math.ceil((data.total || 0) / 12));
    } catch (err) {
      console.error('获取文档列表失败:', err);
      setError('获取文档列表失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCategories();
  }, []);

  useEffect(() => {
    fetchDocuments();

    // 更新URL参数
    const params = new URLSearchParams();
    if (selectedCategory) params.set('category', selectedCategory.toString());
    if (searchKeyword.trim()) params.set('q', searchKeyword.trim());
    if (currentPage > 1) params.set('page', currentPage.toString());

    setSearchParams(params);
  }, [selectedCategory, searchKeyword, currentPage]);

  // 自动选择第一个文档
  useEffect(() => {
    if (documents.length > 0 && !selectedDocument && !documentContent) {
      const firstDocument = documents[0];
      handleDocumentClick(firstDocument.id);
    }
  }, [documents, selectedDocument, documentContent]);





  const handleSearchSubmit = (event) => {
    event.preventDefault();
    setCurrentPage(1);
    fetchDocuments();
  };

  const handlePageChange = (event, page) => {
    setCurrentPage(page);
  };

  const handleCategoryToggle = (categoryId) => {
    setExpandedCategories(prev => ({
      ...prev,
      [categoryId]: !prev[categoryId]
    }));
  };



  // 获取当前文档在列表中的索引
  const getCurrentDocumentIndex = () => {
    return documents.findIndex(doc => doc.id === selectedDocument);
  };

  // 获取上一个文档
  const getPreviousDocument = () => {
    const currentIndex = getCurrentDocumentIndex();
    if (currentIndex > 0) {
      return documents[currentIndex - 1];
    }
    return null;
  };

  // 获取下一个文档
  const getNextDocument = () => {
    const currentIndex = getCurrentDocumentIndex();
    if (currentIndex >= 0 && currentIndex < documents.length - 1) {
      return documents[currentIndex + 1];
    }
    return null;
  };

  const handleDocumentClick = useCallback(async (documentId) => {
    setSelectedDocument(documentId);
    setDocumentLoading(true);
    try {
      const response = await axios.get(`${API_BASE_URL}/api/v1/help/documents/${documentId}`);
      const documentData = response.data;

      // 不需要处理URL，MarkdownRenderer会自动处理
      setDocumentContent(documentData);
    } catch (err) {
      console.error('获取文档内容失败:', err);
      setError('获取文档内容失败');
    } finally {
      setDocumentLoading(false);
    }
  }, []);

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  // 侧边栏内容组件
  const SidebarContent = ({ isMobile = false }) => (
    <Box sx={{
      width: isMobile ? 240 : '100%',
      height: '100%',
      bgcolor: 'linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%)',
      position: 'relative',
      '&::before': {
        content: '""',
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        height: '4px',
        background: 'linear-gradient(90deg, #3b82f6 0%, #1d4ed8 50%, #7c3aed 100%)',
      }
    }}>
      {/* 移动端头部 */}
      {isMobile && (
        <Box sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          p: 3,
          pt: 4,
          borderBottom: '1px solid rgba(148, 163, 184, 0.2)',
          bgcolor: 'rgba(255, 255, 255, 0.95)',
          backdropFilter: 'blur(10px)',
        }}>
          <Box>
            <Typography variant="h6" sx={{
              fontWeight: 800,
              fontSize: '1.3rem',
              color: '#0f172a',
              mb: 0.5,
            }}>
              📚 帮助文档
            </Typography>
            <Typography variant="caption" sx={{
              color: '#64748b',
              fontSize: '0.75rem',
            }}>
              快速找到您需要的答案
            </Typography>
          </Box>
          <IconButton
            onClick={handleDrawerToggle}
            size="small"
            sx={{
              color: '#64748b',
              bgcolor: 'rgba(148, 163, 184, 0.1)',
              borderRadius: '50%',
              '&:hover': {
                bgcolor: 'rgba(239, 68, 68, 0.1)',
                color: '#ef4444',
                transform: 'scale(1.05)',
              },
              transition: 'all 0.2s ease',
            }}
          >
            <Close />
          </IconButton>
        </Box>
      )}

      {/* 桌面端头部 */}
      {!isMobile && (
        <Box sx={{
          p: 4,
          pt: 5,
          borderBottom: '1px solid rgba(148, 163, 184, 0.2)',
          bgcolor: 'rgba(255, 255, 255, 0.95)',
          backdropFilter: 'blur(10px)',
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Box sx={{
              width: 40,
              height: 40,
              borderRadius: '12px',
              bgcolor: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              mr: 2,
              boxShadow: '0 4px 12px rgba(59, 130, 246, 0.3)',
            }}>
              <Typography sx={{ fontSize: '1.2rem' }}>📚</Typography>
            </Box>
            <Box>
              <Typography variant="h6" sx={{
                fontWeight: 800,
                fontSize: '1.3rem',
                color: '#0f172a',
                lineHeight: 1.2,
              }}>
                帮助文档
              </Typography>
            </Box>
          </Box>
          <Typography variant="body2" sx={{
            color: '#64748b',
            fontSize: '0.85rem',
            lineHeight: 1.5,
          }}>
            快速找到您需要的答案
          </Typography>
        </Box>
      )}

      {/* 分类树形结构 */}
      <List sx={{ p: 2, pb: 2 }}>
        {/* 具体分类 */}
        {categories.map((category) => (
          <Box key={category.id} sx={{ mb: 0.5 }}>
            <ListItemButton
              onClick={() => handleCategoryToggle(category.id)}
              sx={{
                px: 0,
                py: 0.5,
                borderRadius: 0,
                mx: 0,
                bgcolor: 'transparent',
                border: 'none',
                minHeight: 'auto',
                '&:hover': {
                  bgcolor: 'transparent',
                  '& .category-text': {
                    color: '#3b82f6',
                  },
                  '& .expand-icon': {
                    color: '#3b82f6',
                  },
                },
                '&:focus': {
                  bgcolor: 'transparent',
                },
              }}
            >
              <Box sx={{
                display: 'flex',
                alignItems: 'center',
                width: '100%',
                py: 1,
                px: 1,
              }}>
                <Box
                  className="expand-icon"
                  sx={{
                    color: '#94a3b8',
                    transition: 'all 0.2s ease',
                    transform: expandedCategories[category.id] ? 'rotate(90deg)' : 'rotate(0deg)',
                    mr: 1,
                    cursor: 'pointer',
                  }}
                >
                  <ExpandMore fontSize="small" />
                </Box>
                <Typography
                  className="category-text"
                  variant="body2"
                  sx={{
                    fontWeight: 500,
                    color: '#374151',
                    fontSize: '0.875rem',
                    lineHeight: 1.4,
                    transition: 'all 0.2s ease',
                    cursor: 'pointer',
                    flex: 1,
                  }}
                >
                  {category.name}
                </Typography>
              </Box>
            </ListItemButton>

            <Collapse in={expandedCategories[category.id]} timeout="auto" unmountOnExit>
              <Box sx={{
                mt: 1,
                ml: 2,
                position: 'relative',
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  left: 8,
                  top: 0,
                  bottom: 0,
                  width: '1px',
                  background: 'rgba(59, 130, 246, 0.2)',
                },
              }}>
                <List component="div" disablePadding sx={{ pl: 0 }}>
                  {/* 显示该分类下的文档 */}
                  {documents
                    .filter(doc => doc.category_id === category.id)
                    .slice(0, 5) // 只显示前5个文档
                    .map((doc) => (
                      <ListItemButton
                        key={doc.id}
                        selected={selectedDocument === doc.id}
                        onClick={() => handleDocumentClick(doc.id)}
                        sx={{
                          pl: 3,
                          pr: 2,
                          py: 1,
                          mb: 0.5,
                          borderRadius: 1,
                          bgcolor: 'transparent',
                          position: 'relative',
                          transition: 'all 0.2s ease',
                          '&::before': {
                            content: '""',
                            position: 'absolute',
                            left: 8,
                            top: '50%',
                            transform: 'translateY(-50%)',
                            width: 4,
                            height: 4,
                            borderRadius: '50%',
                            bgcolor: selectedDocument === doc.id ? '#3b82f6' : '#cbd5e1',
                            transition: 'all 0.2s ease',
                          },
                          '&.Mui-selected': {
                            bgcolor: 'rgba(59, 130, 246, 0.08)',
                            '&::before': {
                              bgcolor: '#3b82f6',
                            },
                            '& .doc-title': {
                              color: '#1e293b',
                              fontWeight: 600,
                            },
                          },
                          '&:hover': {
                            bgcolor: 'rgba(59, 130, 246, 0.05)',
                            '&::before': {
                              bgcolor: '#3b82f6',
                            },
                            '& .doc-title': {
                              color: '#1e293b',
                            },
                          },
                        }}
                      >
                        <Typography
                          className="doc-title"
                          variant="body2"
                          sx={{
                            fontSize: '0.8rem',
                            fontWeight: selectedDocument === doc.id ? 600 : 400,
                            color: selectedDocument === doc.id ? '#1e293b' : '#64748b',
                            lineHeight: 1.4,
                            transition: 'all 0.2s ease',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            whiteSpace: 'nowrap',
                          }}
                        >
                          {doc.title}
                        </Typography>
                      </ListItemButton>
                    ))}
                </List>
              </Box>
            </Collapse>
          </Box>
        ))}
      </List>
    </Box>
  );

  if (error) {
    return (
      <Box sx={{ minHeight: '100vh', bgcolor: '#f8fafc' }}>
        <Box sx={{ height: 64 }} />
        <Box sx={{ p: 4 }}>
          <Alert severity="error">{error}</Alert>
        </Box>
      </Box>
    );
  }

  return (
    <Box sx={{
      height: '100vh',
      bgcolor: '#f5f7fa',
      overflow: 'hidden',
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
    }}>
      {/* Fixed top spacer for header */}
      <Box sx={{ height: 64 }} />

      <Box sx={{
        display: 'flex',
        position: 'relative',
        height: 'calc(100vh - 64px)',
        overflow: 'hidden',
      }}>
        {/* Desktop Sidebar */}
        <Box
          sx={{
            display: { xs: 'none', md: 'block' },
            width: 240,
            flexShrink: 0,
            position: 'fixed',
            top: 64, // 紧贴顶部导航栏
            left: 0,
            height: 'calc(100vh - 64px)',
            overflowY: 'auto',
            bgcolor: 'white',
            zIndex: 1000,
            borderRight: '1px solid #e2e8f0',
            // 隐藏滚动条
            '&::-webkit-scrollbar': {
              display: 'none',
            },
            '-ms-overflow-style': 'none',
            'scrollbar-width': 'none',
          }}
        >
          <SidebarContent />
        </Box>

        {/* Mobile Drawer */}
        <Drawer
          variant="temporary"
          open={mobileOpen}
          onClose={handleDrawerToggle}
          ModalProps={{
            keepMounted: true,
          }}
          sx={{
            display: { xs: 'block', md: 'none' },
            '& .MuiDrawer-paper': {
              boxSizing: 'border-box',
              width: 240,
              bgcolor: 'white',
            },
          }}
        >
          <SidebarContent isMobile />
        </Drawer>

        {/* Main Content */}
        <Box
          sx={{
            flexGrow: 1,
            ml: { xs: 0, md: '240px' },
            minHeight: 'calc(100vh - 64px)',
          }}
        >
            {/* Mobile Menu Button */}
            <Box sx={{
              display: { xs: 'block', md: 'none' },
              position: 'absolute',
              top: 10,
              left: 10,
              zIndex: 1001,
            }}>
              <IconButton
                color="inherit"
                aria-label="open drawer"
                edge="start"
                onClick={handleDrawerToggle}
                sx={{
                  bgcolor: 'white',
                  border: '1px solid #e2e8f0',
                  borderRadius: 2,
                  '&:hover': {
                    bgcolor: '#f8fafc',
                  },
                }}
              >
                <Menu />
              </IconButton>
            </Box>

            {/* Content Area */}
            <Paper
              elevation={0}
              sx={{
                p: { xs: 3, md: 4, lg: 5 },
                borderRadius: 0,
                border: 'none',
                boxShadow: 'none',
                bgcolor: 'background.paper',
                minHeight: 'calc(100vh - 64px)',
                maxHeight: 'calc(100vh - 64px)',
                width: '100%',
                m: 0,
                overflow: 'auto',
                // 隐藏滚动条
                '&::-webkit-scrollbar': {
                  display: 'none',
                },
                '-ms-overflow-style': 'none',
                'scrollbar-width': 'none',
              }}
            >
              {documentContent ? (
                <Box>
                  <Typography
                    variant="h4"
                    sx={{
                      mb: 4,
                      fontWeight: 700,
                      fontSize: { xs: '1.75rem', md: '2.25rem' },
                      color: '#1a1a1a',
                      position: 'relative',
                      '&::after': {
                        content: '""',
                        position: 'absolute',
                        bottom: -16,
                        left: 0,
                        width: 60,
                        height: 4,
                        background: 'linear-gradient(90deg, #1976d2 0%, #42a5f5 100%)',
                        borderRadius: 2,
                      }
                    }}
                  >
                    {documentContent.title}
                  </Typography>

                  {/* 文档元信息 */}
                  <Box sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: 3,
                    mb: 4,
                    pb: 3,
                    borderBottom: '1px solid #e2e8f0',
                    flexWrap: 'wrap'
                  }}>
                    {documentContent.category_name && (
                      <Chip
                        label={documentContent.category_name}
                        size="small"
                        sx={{
                          bgcolor: '#eff6ff',
                          color: '#1d4ed8',
                          fontSize: '0.75rem',
                        }}
                      />
                    )}
                    <Typography variant="body2" color="text.secondary" sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                      <Visibility sx={{ fontSize: 16 }} />
                      浏览 {documentContent.view_count || 0} 次
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                      <AccessTime sx={{ fontSize: 16 }} />
                      更新于 {new Date(documentContent.updated_at).toLocaleDateString()}
                    </Typography>
                  </Box>

                  {/* 文档内容 */}
                  <Box sx={{ mb: 4 }}>
                    {documentContent.content ? (
                      <MarkdownRenderer content={documentContent.content} />
                    ) : (
                      <Typography variant="body1" color="text.secondary" sx={{ textAlign: 'center', py: 4 }}>
                        暂无内容
                      </Typography>
                    )}
                  </Box>

                  {/* 文档导航 */}
                  <Divider sx={{ my: 4 }} />

                  {/* 上一节/下一节导航 */}
                  <Box sx={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    mb: 3,
                    gap: 2,
                  }}>
                    {getPreviousDocument() ? (
                      <Button
                        variant="outlined"
                        size="small"
                        onClick={() => handleDocumentClick(getPreviousDocument().id)}
                        startIcon={<NavigateBefore />}
                        sx={{
                          textTransform: 'none',
                          px: 2,
                          py: 1,
                          borderRadius: 1,
                          fontSize: '0.8rem',
                          maxWidth: '200px',
                        }}
                      >
                        <Box sx={{ textAlign: 'left' }}>
                          <Typography variant="caption" color="text.secondary" display="block" sx={{ fontSize: '0.7rem' }}>
                            上一节
                          </Typography>
                          <Typography variant="body2" sx={{
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            whiteSpace: 'nowrap',
                            fontSize: '0.8rem',
                            maxWidth: '150px',
                          }}>
                            {getPreviousDocument().title}
                          </Typography>
                        </Box>
                      </Button>
                    ) : (
                      <Box />
                    )}

                    {getNextDocument() ? (
                      <Button
                        variant="outlined"
                        size="small"
                        onClick={() => handleDocumentClick(getNextDocument().id)}
                        endIcon={<NavigateNext />}
                        sx={{
                          textTransform: 'none',
                          px: 2,
                          py: 1,
                          borderRadius: 1,
                          fontSize: '0.8rem',
                          maxWidth: '200px',
                        }}
                      >
                        <Box sx={{ textAlign: 'right' }}>
                          <Typography variant="caption" color="text.secondary" display="block" sx={{ fontSize: '0.7rem' }}>
                            下一节
                          </Typography>
                          <Typography variant="body2" sx={{
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            whiteSpace: 'nowrap',
                            fontSize: '0.8rem',
                            maxWidth: '150px',
                          }}>
                            {getNextDocument().title}
                          </Typography>
                        </Box>
                      </Button>
                    ) : (
                      <Box />
                    )}
                  </Box>


                </Box>
              ) : documentLoading ? (
                <Box sx={{ textAlign: 'center', py: 8 }}>
                  <CircularProgress />
                  <Typography variant="h6" color="text.secondary" sx={{ mt: 2 }}>
                    加载文档内容中...
                  </Typography>
                </Box>
              ) : (
                <Box sx={{ textAlign: 'center', py: 8 }}>
                  <Typography
                    variant="h6"
                    sx={{
                      color: '#64748b',
                      maxWidth: '600px',
                      mx: 'auto',
                      lineHeight: 1.6,
                      mb: 4,
                    }}
                  >
                    正在加载文档内容...
                  </Typography>
                </Box>
              )}
            </Paper>
        </Box>
      </Box>
    </Box>
  );
};

export default HelpCenter;
