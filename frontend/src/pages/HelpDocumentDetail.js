import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Paper,
  Breadcrumbs,
  Link,
  Chip,
  Divider,
  IconButton,
  Tooltip,
  Alert,
  Skeleton,
  Stack,
  Button,
} from '@mui/material';
import {
  Home,
  Article,
  Visibility,
  AccessTime,
  Share,
  Print,
  ArrowBack,
  ThumbUp,
  ThumbDown,
} from '@mui/icons-material';
import { useParams, useNavigate } from 'react-router-dom';
import axios from 'axios';
import { API_BASE_URL } from '../config/api-config';

const HelpDocumentDetail = () => {
  const { documentId } = useParams();
  const navigate = useNavigate();
  const [document, setDocument] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // 处理文档内容中的图片URL
  const processDocumentContent = (content) => {
    if (!content) return content;

    // 替换相对路径的图片为完整URL
    return content.replace(
      /src="([^"]*help-docs[^"]*)"/g,
      (match, imagePath) => {
        // 如果已经是完整URL，直接返回
        if (imagePath.startsWith('http')) {
          return match;
        }
        // 使用公开的媒体文件端点
        const fullUrl = `${API_BASE_URL}/api/v1/help/media/${imagePath}`;
        return `src="${fullUrl}"`;
      }
    );
  };

  useEffect(() => {
    const fetchDocument = async () => {
      try {
        setLoading(true);
        const response = await axios.get(`${API_BASE_URL}/api/v1/help/documents/${documentId}`);
        const documentData = response.data;

        // 处理文档内容中的图片URL
        if (documentData.content) {
          documentData.content = processDocumentContent(documentData.content);
        }

        setDocument(documentData);
      } catch (err) {
        console.error('获取文档详情失败:', err);
        if (err.response?.status === 404) {
          setError('文档不存在或已被删除');
        } else if (err.response?.status === 403) {
          setError('文档未发布，暂时无法访问');
        } else {
          setError('获取文档详情失败，请稍后重试');
        }
      } finally {
        setLoading(false);
      }
    };

    if (documentId) {
      fetchDocument();
    }
  }, [documentId]);

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: document.title,
          text: `查看帮助文档：${document.title}`,
          url: window.location.href,
        });
      } catch (err) {
        // 分享取消，不需要处理
      }
    } else {
      // 复制链接到剪贴板
      try {
        await navigator.clipboard.writeText(window.location.href);
        alert('链接已复制到剪贴板');
      } catch (err) {
        // 复制失败，静默处理
      }
    }
  };

  const handlePrint = () => {
    window.print();
  };

  if (loading) {
    return (
      <Box sx={{ minHeight: '100vh', bgcolor: '#f8fafc' }}>
        <Box sx={{ height: 64 }} />
        <Container maxWidth="lg" sx={{ py: 4 }}>
          <Skeleton variant="text" width={300} height={40} sx={{ mb: 2 }} />
          <Skeleton variant="text" width="100%" height={60} sx={{ mb: 3 }} />
          <Skeleton variant="rectangular" height={400} sx={{ borderRadius: 2 }} />
        </Container>
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ minHeight: '100vh', bgcolor: '#f8fafc' }}>
        <Box sx={{ height: 64 }} />
        <Container maxWidth="lg" sx={{ py: 4 }}>
          <Alert severity="error" sx={{ mb: 4 }}>
            {error}
          </Alert>
          <Button
            variant="contained"
            onClick={() => navigate('/help')}
            startIcon={<ArrowBack />}
          >
            返回帮助中心
          </Button>
        </Container>
      </Box>
    );
  }

  if (!document) {
    return null;
  }

  return (
    <Box sx={{ minHeight: '100vh', bgcolor: '#f8fafc' }}>
      {/* Fixed top spacer for header */}
      <Box sx={{ height: 64 }} />
      
      <Container maxWidth="lg" sx={{ py: 4 }}>
        {/* 面包屑导航 */}
        <Breadcrumbs sx={{ mb: 3 }}>
          <Link
            component="button"
            variant="body2"
            onClick={() => navigate('/')}
            sx={{
              display: 'flex',
              alignItems: 'center',
              textDecoration: 'none',
              color: 'inherit',
              border: 'none',
              background: 'none',
              cursor: 'pointer',
              '&:hover': { textDecoration: 'underline' },
            }}
          >
            <Home sx={{ mr: 0.5, fontSize: 16 }} />
            首页
          </Link>
          <Link
            component="button"
            variant="body2"
            onClick={() => navigate('/help')}
            sx={{
              textDecoration: 'none',
              color: 'inherit',
              border: 'none',
              background: 'none',
              cursor: 'pointer',
              '&:hover': { textDecoration: 'underline' },
            }}
          >
            帮助中心
          </Link>
          {document.category_name && (
            <Link
              component="button"
              variant="body2"
              onClick={() => navigate(`/help?category=${document.category_id}`)}
              sx={{
                textDecoration: 'none',
                color: 'inherit',
                border: 'none',
                background: 'none',
                cursor: 'pointer',
                '&:hover': { textDecoration: 'underline' },
              }}
            >
              {document.category_name}
            </Link>
          )}
          <Typography variant="body2" color="text.primary">
            {document.title}
          </Typography>
        </Breadcrumbs>

        {/* 文档内容 */}
        <Paper
          sx={{
            p: 4,
            borderRadius: 3,
            border: '1px solid #e2e8f0',
            bgcolor: 'white',
          }}
        >
          {/* 文档头部 */}
          <Box sx={{ mb: 4 }}>
            <Box sx={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between', mb: 2 }}>
              <Typography
                variant="h3"
                sx={{
                  fontWeight: 700,
                  color: '#1e293b',
                  lineHeight: 1.2,
                  flex: 1,
                  mr: 2,
                }}
              >
                {document.title}
              </Typography>
              
              {/* 操作按钮 */}
              <Stack direction="row" spacing={1}>
                <Tooltip title="分享文档">
                  <IconButton onClick={handleShare} size="small">
                    <Share />
                  </IconButton>
                </Tooltip>
                <Tooltip title="打印文档">
                  <IconButton onClick={handlePrint} size="small">
                    <Print />
                  </IconButton>
                </Tooltip>
              </Stack>
            </Box>

            {/* 文档元信息 */}
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3, flexWrap: 'wrap' }}>
              {document.category_name && (
                <Chip
                  label={document.category_name}
                  sx={{
                    bgcolor: '#eff6ff',
                    color: '#1d4ed8',
                    fontWeight: 500,
                  }}
                />
              )}
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5, color: '#64748b' }}>
                <Visibility sx={{ fontSize: 16 }} />
                <Typography variant="body2">
                  {document.view_count || 0} 次浏览
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5, color: '#64748b' }}>
                <AccessTime sx={{ fontSize: 16 }} />
                <Typography variant="body2">
                  更新于 {new Date(document.updated_at).toLocaleDateString()}
                </Typography>
              </Box>
            </Box>

            <Divider />
          </Box>

          {/* 文档正文 */}
          <Box
            sx={{
              '& h1, & h2, & h3, & h4, & h5, & h6': {
                color: '#1e293b',
                fontWeight: 600,
                mt: 3,
                mb: 2,
              },
              '& h1': { fontSize: '2rem' },
              '& h2': { fontSize: '1.5rem' },
              '& h3': { fontSize: '1.25rem' },
              '& p': {
                lineHeight: 1.7,
                mb: 2,
                color: '#374151',
              },
              '& ul, & ol': {
                pl: 3,
                mb: 2,
              },
              '& li': {
                mb: 0.5,
                lineHeight: 1.6,
                color: '#374151',
              },
              '& code': {
                bgcolor: '#f1f5f9',
                color: '#e11d48',
                px: 1,
                py: 0.5,
                borderRadius: 1,
                fontSize: '0.875rem',
                fontFamily: 'monospace',
              },
              '& pre': {
                bgcolor: '#f8fafc',
                p: 2,
                borderRadius: 2,
                overflow: 'auto',
                border: '1px solid #e2e8f0',
                mb: 2,
              },
              '& blockquote': {
                borderLeft: '4px solid #3b82f6',
                pl: 2,
                ml: 0,
                fontStyle: 'italic',
                color: '#64748b',
                bgcolor: '#f8fafc',
                py: 1,
                mb: 2,
              },
              '& img': {
                maxWidth: '100%',
                height: 'auto',
                borderRadius: 2,
                mb: 2,
              },
              '& table': {
                width: '100%',
                borderCollapse: 'collapse',
                mb: 2,
                border: '1px solid #e2e8f0',
              },
              '& th, & td': {
                border: '1px solid #e2e8f0',
                p: 1,
                textAlign: 'left',
              },
              '& th': {
                bgcolor: '#f8fafc',
                fontWeight: 600,
              },
            }}
            dangerouslySetInnerHTML={{ __html: document.content }}
          />

          {/* 文档底部 */}
          <Divider sx={{ my: 4 }} />
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Button
              variant="outlined"
              onClick={() => navigate('/help')}
              startIcon={<ArrowBack />}
              sx={{ textTransform: 'none' }}
            >
              返回帮助中心
            </Button>
            
            <Typography variant="body2" color="text.secondary">
              文档编号: #{document.id}
            </Typography>
          </Box>
        </Paper>
      </Container>
    </Box>
  );
};

export default HelpDocumentDetail;
