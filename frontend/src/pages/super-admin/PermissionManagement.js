import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  TextField,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  IconButton,
  Button,
  CircularProgress,
  Alert,
  Snackbar,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  Tooltip,
  Paper
} from '@mui/material';
import {
  Search,
  FilterList,
  Refresh,
  Add,
  Visibility,
  Edit,
  Delete,
  Security,
  Settings,
  AdminPanelSettings,
  CheckCircle
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import { ApiConfig } from '../../config/api-config';

function PermissionManagement() {
  const { token } = useAuth();
  
  // 状态管理
  const [permissions, setPermissions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [search, setSearch] = useState('');
  const [moduleFilter, setModuleFilter] = useState('');
  const [actionFilter, setActionFilter] = useState('');
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });

  // 统计数据
  const [stats, setStats] = useState({
    totalPermissions: 0,
    systemPermissions: 0,
    customPermissions: 0,
    totalModules: 0
  });

  // 筛选选项数据
  const [moduleOptions, setModuleOptions] = useState([]);
  const [actionOptions, setActionOptions] = useState([]);
  
  // 对话框状态
  const [detailDialog, setDetailDialog] = useState({ open: false, permission: null });
  const [createDialog, setCreateDialog] = useState(false);
  const [editDialog, setEditDialog] = useState({ open: false, permission: null });
  const [deleteDialog, setDeleteDialog] = useState({ open: false, permission: null });
  
  // 表单数据
  const [formData, setFormData] = useState({
    permission_name: '',
    permission_code: '',
    module: '',
    action: '',
    resource: '',
    description: ''
  });

  // 获取模块统计数据
  const fetchModuleStatistics = async () => {
    try {
      const response = await fetch(`${ApiConfig.baseURL}/api/v1/permissions/modules/statistics`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        const modules = data.modules || [];
        setModuleOptions(modules.map(m => m.module));
      } else {
        console.error('获取模块统计失败');
      }
    } catch (error) {
      console.error('获取模块统计错误:', error);
    }
  };

  // 获取操作统计数据
  const fetchActionStatistics = async () => {
    try {
      const response = await fetch(`${ApiConfig.baseURL}/api/v1/permissions/actions/statistics`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        const actions = data.actions || [];
        setActionOptions(actions.map(a => a.action));
      } else {
        console.error('获取操作统计失败');
      }
    } catch (error) {
      console.error('获取操作统计错误:', error);
    }
  };

  // 获取权限列表
  const fetchPermissions = async () => {
    try {
      setLoading(true);
      
      const params = new URLSearchParams({
        page: (page + 1).toString(),
        page_size: pageSize.toString()
      });
      
      if (search) params.append('search', search);
      if (moduleFilter) params.append('module', moduleFilter);
      if (actionFilter) params.append('action', actionFilter);

      const response = await fetch(`${ApiConfig.baseURL}/api/v1/permissions/?${params}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        const permissionList = data.permissions || [];
        setPermissions(permissionList);
        setTotal(data.total || 0);

        // 计算统计数据
        const systemCount = permissionList.filter(p => p.is_system_permission).length;
        const customCount = permissionList.filter(p => !p.is_system_permission).length;
        const moduleSet = new Set(permissionList.map(p => p.module));

        setStats({
          totalPermissions: data.total || 0,
          systemPermissions: systemCount,
          customPermissions: customCount,
          totalModules: moduleSet.size
        });
      } else {
        throw new Error('获取权限列表失败');
      }
    } catch (error) {
      console.error('获取权限列表错误:', error);
      showSnackbar('获取权限列表失败', 'error');
    } finally {
      setLoading(false);
    }
  };

  // 显示提示消息
  const showSnackbar = (message, severity = 'success') => {
    setSnackbar({ open: true, message, severity });
  };

  // 关闭提示消息
  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  // 页面加载时获取数据
  useEffect(() => {
    fetchPermissions();
  }, [page, pageSize, search, moduleFilter, actionFilter]);

  // 页面初始化时获取筛选选项
  useEffect(() => {
    fetchModuleStatistics();
    fetchActionStatistics();
  }, []);

  // 处理分页变化
  const handlePageChange = (event, newPage) => {
    setPage(newPage);
  };

  const handlePageSizeChange = (event) => {
    setPageSize(parseInt(event.target.value, 10));
    setPage(0);
  };

  // 处理搜索
  const handleSearchChange = (event) => {
    setSearch(event.target.value);
    setPage(0);
  };

  // 处理筛选
  const handleModuleFilterChange = (event) => {
    setModuleFilter(event.target.value);
    setPage(0);
  };

  const handleActionFilterChange = (event) => {
    setActionFilter(event.target.value);
    setPage(0);
  };

  // 创建权限
  const handleCreatePermission = async () => {
    try {
      const response = await fetch(`${ApiConfig.baseURL}/api/v1/permissions/`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        const data = await response.json();
        showSnackbar('权限创建成功', 'success');
        setCreateDialog(false);
        // 重置表单数据
        setFormData({
          permission_name: '',
          permission_code: '',
          module: '',
          action: '',
          resource: '',
          description: ''
        });
        // 刷新权限列表
        fetchPermissions();
      } else {
        const errorData = await response.json().catch(() => ({}));
        showSnackbar(errorData.detail || '创建权限失败', 'error');
      }
    } catch (error) {
      console.error('创建权限错误:', error);
      showSnackbar('创建权限失败', 'error');
    }
  };

  // 更新权限
  const handleUpdatePermission = async () => {
    try {
      const response = await fetch(`${ApiConfig.baseURL}/api/v1/permissions/${editDialog.permission.id}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        const data = await response.json();
        showSnackbar('权限更新成功', 'success');
        setEditDialog({ open: false, permission: null });
        // 重置表单数据
        setFormData({
          permission_name: '',
          permission_code: '',
          module: '',
          action: '',
          resource: '',
          description: ''
        });
        // 刷新权限列表
        fetchPermissions();
      } else {
        const errorData = await response.json().catch(() => ({}));
        showSnackbar(errorData.detail || '更新权限失败', 'error');
      }
    } catch (error) {
      console.error('更新权限错误:', error);
      showSnackbar('更新权限失败', 'error');
    }
  };

  // 删除权限
  const handleDeletePermission = async () => {
    try {
      const response = await fetch(`${ApiConfig.baseURL}/api/v1/permissions/${deleteDialog.permission.id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        showSnackbar('权限删除成功', 'success');
        setDeleteDialog({ open: false, permission: null });
        // 刷新权限列表
        fetchPermissions();
      } else {
        const errorData = await response.json().catch(() => ({}));
        showSnackbar(errorData.detail || '删除权限失败', 'error');
      }
    } catch (error) {
      console.error('删除权限错误:', error);
      showSnackbar('删除权限失败', 'error');
    }
  };

  // 打开编辑对话框
  const handleEditPermission = (permission) => {
    setFormData({
      permission_name: permission.permission_name || '',
      permission_code: permission.permission_code || '',
      module: permission.module || '',
      action: permission.action || '',
      resource: permission.resource || '',
      description: permission.description || ''
    });
    setEditDialog({ open: true, permission });
  };

  // 查看权限详情
  const handleViewPermission = async (permission) => {
    try {
      const response = await fetch(`${ApiConfig.baseURL}/api/v1/permissions/${permission.id}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setDetailDialog({ open: true, permission: data });
      } else {
        throw new Error('获取权限详情失败');
      }
    } catch (error) {
      console.error('获取权限详情错误:', error);
      showSnackbar('获取权限详情失败', 'error');
    }
  };



  // 统计卡片数据
  const statsCards = [
    {
      title: '总权限数',
      value: stats.totalPermissions,
      color: 'primary',
      icon: <Security />,
    },
    {
      title: '系统权限',
      value: stats.systemPermissions,
      color: 'warning',
      icon: <AdminPanelSettings />,
    },
    {
      title: '权限模块',
      value: stats.totalModules,
      color: 'info',
      icon: <Settings />,
    },
  ];

  return (
    <Box sx={{ width: '100%', height: '100%', p: 3 }}>
      {/* Header Section */}
      <Box sx={{ mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
          <Box>
            <Typography variant="h4" sx={{ fontWeight: 700, mb: 1, color: 'text.primary' }}>
              权限管理
            </Typography>
            <Typography variant="body1" color="text.secondary" sx={{ fontSize: '1.1rem' }}>
              管理系统权限、查看权限分配和控制访问控制
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Tooltip title="刷新数据">
              <IconButton
                onClick={() => {
                  fetchPermissions();
                  fetchModuleStatistics();
                  fetchActionStatistics();
                }}
                disabled={loading}
                sx={{
                  backgroundColor: 'primary.light',
                  color: 'primary.main',
                  '&:hover': { backgroundColor: 'primary.main', color: 'white' }
                }}
              >
                <Refresh />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>
      </Box>



          {/* 统计卡片 */}
          <Box sx={{ mb: 4 }}>
            <Typography variant="h6" sx={{ fontWeight: 600, mb: 3, color: 'primary.main' }}>
              权限统计
            </Typography>
            <Grid container spacing={3}>
              {statsCards.map((stat, index) => (
                <Grid item xs={12} sm={6} md={4} key={index}>
                  <Paper sx={{ p: 3, borderRadius: 2, backgroundColor: 'background.default', textAlign: 'center' }}>
                    <Box
                      sx={{
                        width: 48,
                        height: 48,
                        borderRadius: '50%',
                        backgroundColor: `${stat.color}.main`,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        mx: 'auto',
                        mb: 2,
                      }}
                    >
                      {React.cloneElement(stat.icon, { sx: { color: 'white', fontSize: 24 } })}
                    </Box>
                    <Typography variant="h4" sx={{ fontWeight: 700, mb: 1 }}>
                      {loading ? '...' : stat.value}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {stat.title}
                    </Typography>
                  </Paper>
                </Grid>
              ))}
            </Grid>
          </Box>

          {/* 搜索和筛选区域 */}
          <Box sx={{ mb: 4 }}>
            <Paper
              sx={{
                p: 3,
                borderRadius: 3,
                backgroundColor: 'background.default',
                border: '1px solid',
                borderColor: 'divider',
                boxShadow: '0 2px 8px rgba(0, 0, 0, 0.04)',
              }}
            >
              <Typography variant="h6" sx={{ fontWeight: 600, mb: 3, color: 'primary.main' }}>
                搜索和筛选
              </Typography>
              <Grid container spacing={3} alignItems="center">
                <Grid item xs={12} md={4}>
                  <TextField
                    fullWidth
                    placeholder="权限名称和权限代码"
                    value={search}
                    onChange={handleSearchChange}
                    variant="outlined"
                    size="medium"
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 2,
                        backgroundColor: 'background.paper',
                        '&:hover .MuiOutlinedInput-notchedOutline': {
                          borderColor: 'primary.main',
                        },
                        '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                          borderColor: 'primary.main',
                          borderWidth: 2,
                        },
                      },
                    }}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Search sx={{ color: 'text.secondary' }} />
                        </InputAdornment>
                      ),
                    }}
                  />
                </Grid>
                <Grid item xs={12} md={3}>
                  <FormControl fullWidth>
                    <InputLabel
                      shrink={true}
                      sx={{
                        color: 'text.secondary',
                        '&.Mui-focused': {
                          color: 'primary.main',
                        }
                      }}
                    >
                      模块筛选
                    </InputLabel>
                    <Select
                      value={moduleFilter}
                      label="模块筛选"
                      onChange={handleModuleFilterChange}
                      sx={{
                        borderRadius: 2,
                        backgroundColor: 'background.paper',
                        transition: 'all 0.3s ease',
                        '& .MuiOutlinedInput-notchedOutline': {
                          borderColor: 'divider',
                          transition: 'border-color 0.3s ease',
                        },
                        '&:hover .MuiOutlinedInput-notchedOutline': {
                          borderColor: 'primary.main',
                          boxShadow: '0 0 0 1px rgba(25, 118, 210, 0.1)',
                        },
                        '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                          borderColor: 'primary.main',
                          borderWidth: 2,
                          boxShadow: '0 0 0 3px rgba(25, 118, 210, 0.1)',
                        },
                        '& .MuiSelect-select': {
                          minWidth: '120px',
                          display: 'flex',
                          alignItems: 'center',
                          gap: 1,
                        },
                      }}

                    >
                      <MenuItem value=""></MenuItem>
                      {moduleOptions.map(module => (
                        <MenuItem key={module} value={module}>{module}</MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} md={3}>
                  <FormControl fullWidth>
                    <InputLabel
                      shrink={true}
                      sx={{
                        color: 'text.secondary',
                        '&.Mui-focused': {
                          color: 'primary.main',
                        }
                      }}
                    >
                      操作筛选
                    </InputLabel>
                    <Select
                      value={actionFilter}
                      label="操作筛选"
                      onChange={handleActionFilterChange}
                      sx={{
                        borderRadius: 2,
                        backgroundColor: 'background.paper',
                        transition: 'all 0.3s ease',
                        '& .MuiOutlinedInput-notchedOutline': {
                          borderColor: 'divider',
                          transition: 'border-color 0.3s ease',
                        },
                        '&:hover .MuiOutlinedInput-notchedOutline': {
                          borderColor: 'primary.main',
                          boxShadow: '0 0 0 1px rgba(25, 118, 210, 0.1)',
                        },
                        '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                          borderColor: 'primary.main',
                          borderWidth: 2,
                          boxShadow: '0 0 0 3px rgba(25, 118, 210, 0.1)',
                        },
                        '& .MuiSelect-select': {
                          minWidth: '120px',
                          display: 'flex',
                          alignItems: 'center',
                          gap: 1,
                        },
                      }}

                    >
                      <MenuItem value=""></MenuItem>
                      {actionOptions.map(action => (
                        <MenuItem key={action} value={action}>{action}</MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} md={2}>
                  <Button
                    fullWidth
                    variant="contained"
                    startIcon={<Add />}
                    onClick={() => setCreateDialog(true)}
                    sx={{
                      borderRadius: 2,
                      py: 1.5,
                      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                      boxShadow: '0 4px 12px rgba(102, 126, 234, 0.3)',
                      '&:hover': {
                        background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',
                        boxShadow: '0 6px 16px rgba(102, 126, 234, 0.4)',
                        transform: 'translateY(-1px)',
                      },
                      transition: 'all 0.3s ease',
                    }}
                  >
                    添加权限
                  </Button>
                </Grid>
              </Grid>
            </Paper>
          </Box>

          {/* 权限列表 */}
          <Box>
            <Typography variant="h6" sx={{ fontWeight: 600, mb: 3, color: 'primary.main' }}>
              权限列表 ({total})
            </Typography>

          <TableContainer component={Paper} variant="outlined">
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>权限名称</TableCell>
                  <TableCell>权限代码</TableCell>
                  <TableCell>模块</TableCell>
                  <TableCell>操作</TableCell>
                  <TableCell>资源</TableCell>
                  <TableCell>角色数量</TableCell>
                  <TableCell>系统权限</TableCell>
                  <TableCell>操作</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={8} align="center">
                      <CircularProgress />
                    </TableCell>
                  </TableRow>
                ) : permissions.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={8} align="center">
                      暂无权限数据
                    </TableCell>
                  </TableRow>
                ) : (
                  permissions.map((permission) => (
                    <TableRow key={permission.id} hover>
                      <TableCell>
                        <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                          {permission.permission_name}
                        </Typography>
                        {permission.description && (
                          <Typography variant="caption" color="text.secondary">
                            {permission.description}
                          </Typography>
                        )}
                      </TableCell>
                      <TableCell>
                        <Chip label={permission.permission_code} variant="outlined" size="small" />
                      </TableCell>
                      <TableCell>
                        <Chip label={permission.module} color="primary" size="small" />
                      </TableCell>
                      <TableCell>
                        <Chip label={permission.action} color="secondary" size="small" />
                      </TableCell>
                      <TableCell>{permission.resource || '-'}</TableCell>
                      <TableCell>{permission.role_count || 0}</TableCell>
                      <TableCell>
                        <Chip 
                          label={permission.is_system_permission ? '是' : '否'} 
                          color={permission.is_system_permission ? 'warning' : 'default'}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Tooltip title="查看详情">
                          <IconButton size="small" onClick={() => handleViewPermission(permission)}>
                            <Visibility fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="编辑">
                          <IconButton
                            size="small"
                            disabled={permission.is_system_permission}
                            onClick={() => handleEditPermission(permission)}
                          >
                            <Edit fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="删除">
                          <IconButton 
                            size="small" 
                            color="error"
                            disabled={permission.is_system_permission}
                            onClick={() => setDeleteDialog({ open: true, permission })}
                          >
                            <Delete fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>

            <TablePagination
              component="div"
              count={total}
              page={page}
              onPageChange={handlePageChange}
              rowsPerPage={pageSize}
              onRowsPerPageChange={handlePageSizeChange}
              rowsPerPageOptions={[5, 10, 25, 50]}
              labelRowsPerPage="每页显示:"
              labelDisplayedRows={({ from, to, count }) => `${from}-${to} 共 ${count} 条`}
            />
          </Box>

      {/* 权限详情对话框 */}
      <Dialog open={detailDialog.open} onClose={() => setDetailDialog({ open: false, permission: null })} maxWidth="md" fullWidth>
        <DialogTitle>权限详情</DialogTitle>
        <DialogContent>
          {detailDialog.permission && (
            <Box sx={{ pt: 2 }}>
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary">权限名称:</Typography>
                  <Typography variant="body1" sx={{ fontWeight: 600 }}>{detailDialog.permission.permission_name}</Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary">权限代码:</Typography>
                  <Typography variant="body1">{detailDialog.permission.permission_code}</Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary">所属模块:</Typography>
                  <Typography variant="body1">{detailDialog.permission.module}</Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary">操作类型:</Typography>
                  <Typography variant="body1">{detailDialog.permission.action}</Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary">资源类型:</Typography>
                  <Typography variant="body1">{detailDialog.permission.resource || '无'}</Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary">角色数量:</Typography>
                  <Typography variant="body1">{detailDialog.permission.role_count || 0}</Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary">系统权限:</Typography>
                  <Chip
                    label={detailDialog.permission.is_system_permission ? '是' : '否'}
                    color={detailDialog.permission.is_system_permission ? 'warning' : 'default'}
                    size="small"
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary">创建时间:</Typography>
                  <Typography variant="body1">
                    {detailDialog.permission.created_at ? new Date(detailDialog.permission.created_at).toLocaleString() : '无'}
                  </Typography>
                </Grid>
                {detailDialog.permission.description && (
                  <Grid item xs={12}>
                    <Typography variant="body2" color="text.secondary">权限描述:</Typography>
                    <Typography variant="body1">{detailDialog.permission.description}</Typography>
                  </Grid>
                )}
              </Grid>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => setDetailDialog({ open: false, permission: null })}
            variant="contained"
            sx={{
              backgroundColor: 'grey.500',
              '&:hover': {
                backgroundColor: 'grey.600',
              }
            }}
          >
            关闭
          </Button>
        </DialogActions>
      </Dialog>

      {/* 创建权限对话框 */}
      <Dialog open={createDialog} onClose={() => setCreateDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>创建权限</DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="权限名称"
                  value={formData.permission_name}
                  onChange={(e) => setFormData({...formData, permission_name: e.target.value})}
                  required
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="权限代码"
                  value={formData.permission_code}
                  onChange={(e) => setFormData({...formData, permission_code: e.target.value})}
                  required
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="所属模块"
                  value={formData.module}
                  onChange={(e) => setFormData({...formData, module: e.target.value})}
                  required
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="操作类型"
                  value={formData.action}
                  onChange={(e) => setFormData({...formData, action: e.target.value})}
                  required
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="资源类型（可选）"
                  value={formData.resource}
                  onChange={(e) => setFormData({...formData, resource: e.target.value})}
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="权限描述（可选）"
                  value={formData.description}
                  onChange={(e) => setFormData({...formData, description: e.target.value})}
                  multiline
                  rows={3}
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCreateDialog(false)}>取消</Button>
          <Button
            variant="contained"
            onClick={handleCreatePermission}
            disabled={!formData.permission_name || !formData.permission_code || !formData.module || !formData.action}
          >
            创建
          </Button>
        </DialogActions>
      </Dialog>

      {/* 编辑权限对话框 */}
      <Dialog open={editDialog.open} onClose={() => setEditDialog({ open: false, permission: null })} maxWidth="md" fullWidth>
        <DialogTitle>编辑权限</DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="权限名称"
                  value={formData.permission_name}
                  onChange={(e) => setFormData({...formData, permission_name: e.target.value})}
                  required
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="权限代码"
                  value={formData.permission_code}
                  onChange={(e) => setFormData({...formData, permission_code: e.target.value})}
                  required
                  disabled={editDialog.permission?.is_system_permission}
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="所属模块"
                  value={formData.module}
                  onChange={(e) => setFormData({...formData, module: e.target.value})}
                  required
                  disabled={editDialog.permission?.is_system_permission}
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="操作类型"
                  value={formData.action}
                  onChange={(e) => setFormData({...formData, action: e.target.value})}
                  required
                  disabled={editDialog.permission?.is_system_permission}
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="资源类型（可选）"
                  value={formData.resource}
                  onChange={(e) => setFormData({...formData, resource: e.target.value})}
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="权限描述（可选）"
                  value={formData.description}
                  onChange={(e) => setFormData({...formData, description: e.target.value})}
                  multiline
                  rows={3}
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditDialog({ open: false, permission: null })}>取消</Button>
          <Button
            variant="contained"
            onClick={handleUpdatePermission}
            disabled={!formData.permission_name || !formData.permission_code || !formData.module || !formData.action}
          >
            更新
          </Button>
        </DialogActions>
      </Dialog>

      {/* 删除权限确认对话框 */}
      <Dialog open={deleteDialog.open} onClose={() => setDeleteDialog({ open: false, permission: null })} maxWidth="sm" fullWidth>
        <DialogTitle>确认删除权限</DialogTitle>
        <DialogContent>
          {deleteDialog.permission && (
            <Box sx={{ pt: 2 }}>
              <Alert severity="warning" sx={{ mb: 2 }}>
                删除权限后将无法恢复，请确认是否继续？
              </Alert>
              <Typography variant="body1" sx={{ mb: 1 }}>
                权限名称: <strong>{deleteDialog.permission.permission_name}</strong>
              </Typography>
              <Typography variant="body1" sx={{ mb: 1 }}>
                权限代码: <strong>{deleteDialog.permission.permission_code}</strong>
              </Typography>
              {deleteDialog.permission.role_count > 0 && (
                <Typography variant="body2" color="error" sx={{ mt: 2 }}>
                  注意：该权限当前被 {deleteDialog.permission.role_count} 个角色使用
                </Typography>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialog({ open: false, permission: null })}>取消</Button>
          <Button
            onClick={handleDeletePermission}
            variant="contained"
            color="error"
          >
            确认删除
          </Button>
        </DialogActions>
      </Dialog>

      {/* 提示消息 */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
}

export default PermissionManagement;
