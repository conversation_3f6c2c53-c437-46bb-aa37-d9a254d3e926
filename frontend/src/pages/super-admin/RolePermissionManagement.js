import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  CircularProgress,
  Alert,
  Snackbar,
  Paper,
  IconButton,
  Tooltip,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Checkbox,
  FormControlLabel,
  TextField,
  InputAdornment,
} from '@mui/material';
import {
  Security,
  AdminPanelSettings,
  Settings,
  Group,
  RemoveCircleOutline,
  Add,
  Search,
} from '@mui/icons-material';
import ApiConfig from '../../config/api-config';
import { useAuth } from '../../contexts/AuthContext';

function RolePermissionManagement() {
  const { token } = useAuth();
  
  // 状态管理
  const [roles, setRoles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedRoleId, setSelectedRoleId] = useState('');
  const [rolePermissions, setRolePermissions] = useState([]);
  const [rolePermissionsLoading, setRolePermissionsLoading] = useState(false);
  const [selectedRoleInfo, setSelectedRoleInfo] = useState(null);
  
  // 提示消息
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success'
  });

  // 添加权限对话框相关状态
  const [addPermissionDialog, setAddPermissionDialog] = useState(false);
  const [availablePermissions, setAvailablePermissions] = useState([]);
  const [availablePermissionsLoading, setAvailablePermissionsLoading] = useState(false);
  const [selectedPermissions, setSelectedPermissions] = useState([]);
  const [permissionSearch, setPermissionSearch] = useState('');
  
  // 显示提示消息
  const showSnackbar = (message, severity = 'success') => {
    setSnackbar({ open: true, message, severity });
  };
  
  // 关闭提示消息
  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };
  
  // 获取角色列表
  const fetchRoles = async () => {
    try {
      setLoading(true);
      const response = await fetch(`${ApiConfig.baseURL}/api/v1/roles/?page=1&page_size=100`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });
      
      if (response.ok) {
        const data = await response.json();
        setRoles(data.roles || []);
      } else {
        throw new Error('获取角色列表失败');
      }
    } catch (error) {
      console.error('获取角色列表错误:', error);
      showSnackbar('获取角色列表失败', 'error');
    } finally {
      setLoading(false);
    }
  };
  
  // 获取角色权限列表
  const fetchRolePermissions = async (roleId) => {
    if (!roleId) {
      setRolePermissions([]);
      setSelectedRoleInfo(null);
      return;
    }
    
    try {
      setRolePermissionsLoading(true);
      const response = await fetch(`${ApiConfig.baseURL}/api/v1/roles/${roleId}/permissions`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });
      
      if (response.ok) {
        const data = await response.json();
        setRolePermissions(data.permissions || []);
        setSelectedRoleInfo(data.role);
      } else {
        throw new Error('获取角色权限失败');
      }
    } catch (error) {
      console.error('获取角色权限错误:', error);
      showSnackbar('获取角色权限失败', 'error');
      setRolePermissions([]);
      setSelectedRoleInfo(null);
    } finally {
      setRolePermissionsLoading(false);
    }
  };
  
  // 处理角色选择变化
  const handleRoleSelectionChange = (event) => {
    const roleId = event.target.value;
    setSelectedRoleId(roleId);
    fetchRolePermissions(roleId);
  };

  // 移除角色权限
  const handleRemovePermission = async (permissionId) => {
    if (!selectedRoleId) {
      showSnackbar('请先选择角色', 'error');
      return;
    }

    try {
      const response = await fetch(`${ApiConfig.baseURL}/api/v1/permissions/remove?role_id=${selectedRoleId}&permission_id=${permissionId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        showSnackbar('权限移除成功', 'success');
        // 重新获取角色权限列表
        fetchRolePermissions(selectedRoleId);
      } else {
        const errorData = await response.json().catch(() => ({}));
        showSnackbar(errorData.detail || '移除权限失败', 'error');
      }
    } catch (error) {
      console.error('移除权限错误:', error);
      showSnackbar('移除权限失败', 'error');
    }
  };

  // 获取可用权限列表（当前角色未拥有的权限）
  const fetchAvailablePermissions = async () => {
    if (!selectedRoleId) {
      showSnackbar('请先选择角色', 'error');
      return;
    }

    try {
      setAvailablePermissionsLoading(true);

      const response = await fetch(`${ApiConfig.baseURL}/api/v1/permissions/roles/${selectedRoleId}/available`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        // 直接使用后端返回的未拥有权限列表
        setAvailablePermissions(data.available_permissions || []);
      } else {
        const errorData = await response.json().catch(() => ({}));
        console.error('API错误:', response.status, errorData);
        throw new Error(errorData.detail || `获取可用权限列表失败 (${response.status})`);
      }
    } catch (error) {
      console.error('获取可用权限列表错误:', error);
      showSnackbar(error.message || '获取可用权限列表失败', 'error');
    } finally {
      setAvailablePermissionsLoading(false);
    }
  };

  // 打开添加权限对话框
  const handleOpenAddPermissionDialog = () => {
    if (!selectedRoleId) {
      showSnackbar('请先选择角色', 'error');
      return;
    }
    setSelectedPermissions([]);
    setPermissionSearch('');
    setAddPermissionDialog(true);
    fetchAvailablePermissions();
  };

  // 处理权限选择
  const handlePermissionToggle = (permissionId) => {
    setSelectedPermissions(prev =>
      prev.includes(permissionId)
        ? prev.filter(id => id !== permissionId)
        : [...prev, permissionId]
    );
  };

  // 添加选中的权限
  const handleAddSelectedPermissions = async () => {
    if (selectedPermissions.length === 0) {
      showSnackbar('请选择要添加的权限', 'error');
      return;
    }

    try {
      const promises = selectedPermissions.map(permissionId =>
        fetch(`${ApiConfig.baseURL}/api/v1/permissions/assign?role_id=${selectedRoleId}&permission_id=${permissionId}`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        })
      );

      const results = await Promise.all(promises);
      const failedCount = results.filter(r => !r.ok).length;

      if (failedCount === 0) {
        showSnackbar(`成功添加 ${selectedPermissions.length} 个权限`, 'success');
      } else {
        showSnackbar(`添加完成，${failedCount} 个权限添加失败`, 'warning');
      }

      setAddPermissionDialog(false);
      setSelectedPermissions([]);
      // 重新获取角色权限列表
      fetchRolePermissions(selectedRoleId);
    } catch (error) {
      console.error('添加权限错误:', error);
      showSnackbar('添加权限失败', 'error');
    }
  };
  
  // 页面加载时获取数据
  useEffect(() => {
    fetchRoles();
  }, []);
  
  return (
    <Box sx={{ p: 3 }}>
      {/* 页面标题 */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" sx={{ fontWeight: 700, color: 'primary.main', mb: 1 }}>
          角色权限管理
        </Typography>
        <Typography variant="body1" color="text.secondary">
          查看和管理各个角色的权限分配情况
        </Typography>
      </Box>
      

          {/* 角色选择器 */}
          <Box sx={{ mb: 4 }}>
            <Typography variant="h6" sx={{ fontWeight: 600, color: 'primary.main', mb: 3 }}>
              选择角色
            </Typography>
            <FormControl sx={{ minWidth: 400 }}>
              <InputLabel
                shrink={true}
                sx={{
                  color: 'text.secondary',
                  '&.Mui-focused': {
                    color: 'primary.main',
                  }
                }}
              >
                选择要查看的角色
              </InputLabel>
              <Select
                value={selectedRoleId}
                label="选择要查看的角色"
                onChange={handleRoleSelectionChange}
                disabled={loading}
                sx={{
                  borderRadius: 2,
                  backgroundColor: 'background.paper',
                  transition: 'all 0.3s ease',
                  '& .MuiOutlinedInput-notchedOutline': {
                    borderColor: 'divider',
                    transition: 'border-color 0.3s ease',
                  },
                  '&:hover .MuiOutlinedInput-notchedOutline': {
                    borderColor: 'primary.main',
                    boxShadow: '0 0 0 1px rgba(25, 118, 210, 0.1)',
                  },
                  '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                    borderColor: 'primary.main',
                    borderWidth: 2,
                    boxShadow: '0 0 0 3px rgba(25, 118, 210, 0.1)',
                  },
                }}
              >
                <MenuItem value=""></MenuItem>
                {roles.map(role => (
                  <MenuItem key={role.id} value={role.id}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                      <Typography variant="body1" sx={{ fontWeight: 500 }}>
                        {role.role_name}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        ({role.role_code})
                      </Typography>
                      <Chip
                        label={role.role_type}
                        size="small"
                        color="primary"
                        variant="outlined"
                      />
                    </Box>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>
          
          {/* 权限列表标题 */}
          {selectedRoleInfo && (
            <Box sx={{ mb: 3 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6" sx={{ fontWeight: 600, color: 'primary.main' }}>
                  权限列表
                </Typography>
                <Button
                  variant="contained"
                  startIcon={<Add />}
                  onClick={handleOpenAddPermissionDialog}
                  sx={{
                    transition: 'all 0.2s ease',
                    '&:hover': {
                      transform: 'translateY(-1px)',
                      boxShadow: '0 4px 12px rgba(25, 118, 210, 0.3)',
                    },
                  }}
                >
                  添加权限
                </Button>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                <Typography variant="body1">
                  <strong>{selectedRoleInfo.role_name}</strong> 的权限列表
                </Typography>
                <Chip
                  label={`共 ${rolePermissions.length} 个权限`}
                  color="primary"
                  size="small"
                />
              </Box>
            </Box>
          )}

          {/* 权限列表表格 */}
          <TableContainer
            component={Paper}
            variant="outlined"
            sx={{
              borderRadius: 2,
              transition: 'all 0.2s ease',
              '&:hover': {
                boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
              },
            }}
          >
            <Table>
              <TableHead>
                <TableRow sx={{ backgroundColor: 'grey.50' }}>
                  <TableCell sx={{ fontWeight: 600 }}>权限名称</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>权限代码</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>模块</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>操作</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>资源</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>描述</TableCell>
                  <TableCell sx={{ fontWeight: 600, textAlign: 'center' }}>操作</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {rolePermissionsLoading ? (
                  <TableRow>
                    <TableCell colSpan={7} sx={{ textAlign: 'center', py: 4 }}>
                      <CircularProgress />
                    </TableCell>
                  </TableRow>
                ) : !selectedRoleId ? (
                  <TableRow>
                    <TableCell colSpan={7} sx={{ textAlign: 'center', py: 4 }}>
                      <Typography color="text.secondary">请选择一个角色查看其权限</Typography>
                    </TableCell>
                  </TableRow>
                ) : rolePermissions.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} sx={{ textAlign: 'center', py: 4 }}>
                      <Typography color="text.secondary">该角色暂无权限</Typography>
                    </TableCell>
                  </TableRow>
                ) : (
                  rolePermissions.map((permission) => (
                    <TableRow key={permission.id} hover>
                      <TableCell>
                        <Typography variant="body2" sx={{ fontWeight: 500 }}>
                          {permission.permission_name}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" color="text.secondary">
                          {permission.permission_code}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={permission.module}
                          color="primary"
                          size="small"
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={permission.action}
                          color="secondary"
                          size="small"
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" color="text.secondary">
                          {permission.resource || '-'}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" color="text.secondary">
                          {permission.description || '-'}
                        </Typography>
                      </TableCell>
                      <TableCell sx={{ textAlign: 'center' }}>
                        <Tooltip title="移除权限">
                          <IconButton
                            size="small"
                            color="error"
                            onClick={() => handleRemovePermission(permission.id)}
                            sx={{
                              transition: 'all 0.2s ease',
                              '&:hover': {
                                transform: 'scale(1.1)',
                                backgroundColor: 'error.light',
                                color: 'white',
                              },
                            }}
                          >
                            <RemoveCircleOutline fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>

      {/* 添加权限对话框 */}
      <Dialog open={addPermissionDialog} onClose={() => setAddPermissionDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>为角色添加权限</DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            {selectedRoleInfo && (
              <Typography variant="body1" sx={{ mb: 3 }}>
                为 <strong>{selectedRoleInfo.role_name}</strong> 添加权限
              </Typography>
            )}

            {/* 搜索框 - 用于在未拥有的权限中筛选 */}
            <TextField
              fullWidth
              placeholder="在可添加的权限中搜索..."
              value={permissionSearch}
              onChange={(e) => setPermissionSearch(e.target.value)}
              variant="outlined"
              size="medium"
              sx={{ mb: 3 }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Search sx={{ color: 'text.secondary' }} />
                  </InputAdornment>
                ),
              }}
            />

            {/* 权限列表 */}
            <Box sx={{ maxHeight: 400, overflowY: 'auto' }}>
              {availablePermissionsLoading ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
                  <CircularProgress />
                </Box>
              ) : (() => {
                // 根据搜索关键词筛选权限
                const filteredPermissions = availablePermissions.filter(permission => {
                  if (!permissionSearch) return true;
                  const searchLower = permissionSearch.toLowerCase();
                  return (
                    permission.permission_name.toLowerCase().includes(searchLower) ||
                    permission.permission_code.toLowerCase().includes(searchLower) ||
                    (permission.description && permission.description.toLowerCase().includes(searchLower)) ||
                    permission.module.toLowerCase().includes(searchLower) ||
                    permission.action.toLowerCase().includes(searchLower)
                  );
                });

                if (filteredPermissions.length === 0) {
                  return (
                    <Typography color="text.secondary" sx={{ textAlign: 'center', py: 4 }}>
                      {permissionSearch ? '没有找到匹配的权限' : '该角色已拥有所有权限'}
                    </Typography>
                  );
                }

                return filteredPermissions.map((permission) => (
                  <FormControlLabel
                    key={permission.id}
                    control={
                      <Checkbox
                        checked={selectedPermissions.includes(permission.id)}
                        onChange={() => handlePermissionToggle(permission.id)}
                      />
                    }
                    label={
                      <Box sx={{ ml: 1 }}>
                        <Typography variant="body2" sx={{ fontWeight: 500 }}>
                          {permission.permission_name}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {permission.permission_code} | {permission.module}.{permission.action}
                        </Typography>
                        {permission.description && (
                          <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
                            {permission.description}
                          </Typography>
                        )}
                      </Box>
                    }
                    sx={{
                      display: 'block',
                      mb: 2,
                      p: 1,
                      border: '1px solid',
                      borderColor: 'divider',
                      borderRadius: 1,
                      '&:hover': {
                        backgroundColor: 'action.hover',
                      }
                    }}
                  />
                ));
              })()}
            </Box>

            {selectedPermissions.length > 0 && (
              <Box sx={{ mt: 2, p: 2, backgroundColor: 'primary.light', borderRadius: 1 }}>
                <Typography variant="body2" color="primary.main">
                  已选择 {selectedPermissions.length} 个权限
                </Typography>
              </Box>
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setAddPermissionDialog(false)}>取消</Button>
          <Button
            variant="contained"
            onClick={handleAddSelectedPermissions}
            disabled={selectedPermissions.length === 0}
          >
            添加选中权限 ({selectedPermissions.length})
          </Button>
        </DialogActions>
      </Dialog>

      {/* 提示消息 */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity} sx={{ width: '100%' }}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
}

export default RolePermissionManagement;
