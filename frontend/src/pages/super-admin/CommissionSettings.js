import React, { useState, useEffect } from 'react';
import {
  Container,
  Paper,
  Typography,
  Box,
  Grid,
  Card,
  CardContent,
  TextField,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Chip,
  Alert,
  Snackbar,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  InputAdornment,
  Tabs,
  Tab,
  Divider,
} from '@mui/material';
import {
  Edit,
  Save,
  Cancel,
  Add,
  Delete,
  Settings,
  MonetizationOn,
  CardMembership,
  TrendingUp,
  Info,
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';

function CommissionSettings() {
  const { user } = useAuth();
  const [tabValue, setTabValue] = useState(0);
  const [agentLevels, setAgentLevels] = useState([]);
  const [editingId, setEditingId] = useState(null);
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [newLevel, setNewLevel] = useState({
    name: '',
    level: '',
    subscriptionCommission: '',
    contentCommission: '',
    minSales: '',
    description: '',
  });
  const [alert, setAlert] = useState({
    open: false,
    message: '',
    severity: 'success',
  });

  // 模拟数据 - 实际应从后端获取
  useEffect(() => {
    loadAgentLevels();
  }, []);

  const loadAgentLevels = () => {
    // 模拟从后端加载数据
    const mockData = [
      {
        id: 1,
        name: '初级代理',
        level: 1,
        subscriptionCommission: 10,
        contentCommission: 5,
        minSales: 0,
        description: '新加入的代理商',
        agentCount: 150,
        totalRevenue: 50000,
      },
      {
        id: 2,
        name: '中级代理',
        level: 2,
        subscriptionCommission: 15,
        contentCommission: 8,
        minSales: 100000,
        description: '销售额达到10万的代理商',
        agentCount: 80,
        totalRevenue: 120000,
      },
      {
        id: 3,
        name: '高级代理',
        level: 3,
        subscriptionCommission: 20,
        contentCommission: 12,
        minSales: 500000,
        description: '销售额达到50万的代理商',
        agentCount: 30,
        totalRevenue: 280000,
      },
      {
        id: 4,
        name: '钻石代理',
        level: 4,
        subscriptionCommission: 25,
        contentCommission: 15,
        minSales: 1000000,
        description: '销售额达到100万的代理商',
        agentCount: 10,
        totalRevenue: 450000,
      },
    ];
    setAgentLevels(mockData);
  };

  const handleEdit = (level) => {
    setEditingId(level.id);
  };

  const handleSave = (level) => {
    // 保存编辑的数据
    setAlert({
      open: true,
      message: '佣金设置已更新',
      severity: 'success',
    });
    setEditingId(null);
  };

  const handleCancel = () => {
    setEditingId(null);
    loadAgentLevels(); // 重新加载数据以恢复原始值
  };

  const handleFieldChange = (levelId, field, value) => {
    setAgentLevels(prevLevels =>
      prevLevels.map(level =>
        level.id === levelId ? { ...level, [field]: value } : level
      )
    );
  };

  const handleAddLevel = () => {
    if (!newLevel.name || !newLevel.level || !newLevel.subscriptionCommission || !newLevel.contentCommission) {
      setAlert({
        open: true,
        message: '请填写所有必填字段',
        severity: 'error',
      });
      return;
    }

    const newLevelData = {
      id: Math.max(...agentLevels.map(l => l.id)) + 1,
      ...newLevel,
      subscriptionCommission: parseFloat(newLevel.subscriptionCommission),
      contentCommission: parseFloat(newLevel.contentCommission),
      minSales: parseFloat(newLevel.minSales) || 0,
      agentCount: 0,
      totalRevenue: 0,
    };

    setAgentLevels([...agentLevels, newLevelData]);
    setShowAddDialog(false);
    setNewLevel({
      name: '',
      level: '',
      subscriptionCommission: '',
      contentCommission: '',
      minSales: '',
      description: '',
    });
    setAlert({
      open: true,
      message: '新等级已添加',
      severity: 'success',
    });
  };

  const handleDeleteLevel = (levelId) => {
    setAgentLevels(agentLevels.filter(level => level.id !== levelId));
    setAlert({
      open: true,
      message: '等级已删除',
      severity: 'success',
    });
  };

  const getLevelColor = (level) => {
    const colors = {
      1: '#9e9e9e',
      2: '#2196f3',
      3: '#ff9800',
      4: '#666666',
    };
    return colors[level] || '#757575';
  };

  return (
    <Container maxWidth="xl" sx={{ py: 3 }}>
      {/* 页面标题 */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" sx={{ fontWeight: 600, color: '#1a1a1a', mb: 1 }}>
          佣金设置
        </Typography>
        <Typography variant="body1" sx={{ color: '#666' }}>
          管理不同等级代理商的佣金比例设置
        </Typography>
      </Box>

      {/* 统计卡片 */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={3}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Settings sx={{ color: '#1976d2', mr: 1 }} />
                <Typography variant="h6">等级数量</Typography>
              </Box>
              <Typography variant="h3" sx={{ fontWeight: 700, color: '#1976d2' }}>
                {agentLevels.length}
              </Typography>
              <Typography variant="body2" sx={{ color: '#666', mt: 1 }}>
                个代理等级
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <CardMembership sx={{ color: '#ff9800', mr: 1 }} />
                <Typography variant="h6">订阅佣金</Typography>
              </Box>
              <Typography variant="h3" sx={{ fontWeight: 700, color: '#ff9800' }}>
                {Math.max(...agentLevels.map(l => l.subscriptionCommission))}%
              </Typography>
              <Typography variant="body2" sx={{ color: '#666', mt: 1 }}>
                最高佣金比例
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <MonetizationOn sx={{ color: '#4caf50', mr: 1 }} />
                <Typography variant="h6">内容佣金</Typography>
              </Box>
              <Typography variant="h3" sx={{ fontWeight: 700, color: '#4caf50' }}>
                {Math.max(...agentLevels.map(l => l.contentCommission))}%
              </Typography>
              <Typography variant="body2" sx={{ color: '#666', mt: 1 }}>
                最高佣金比例
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <TrendingUp sx={{ color: '#666666', mr: 1 }} />
                <Typography variant="h6">总收益</Typography>
              </Box>
              <Typography variant="h3" sx={{ fontWeight: 700, color: '#666666' }}>
                ¥{(agentLevels.reduce((sum, l) => sum + l.totalRevenue, 0) / 10000).toFixed(1)}万
              </Typography>
              <Typography variant="body2" sx={{ color: '#666', mt: 1 }}>
                所有代理总收益
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* 选项卡 */}
      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={tabValue}
          onChange={(e, newValue) => setTabValue(newValue)}
          sx={{ borderBottom: 1, borderColor: 'divider' }}
        >
          <Tab label="订阅产品佣金" />
          <Tab label="内容订单佣金" />
          <Tab label="等级管理" />
        </Tabs>
      </Paper>

      {/* 佣金设置表格 */}
      <Paper sx={{ p: 3 }}>
        <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h6" sx={{ fontWeight: 600 }}>
            {tabValue === 0 ? '订阅产品佣金设置' : tabValue === 1 ? '内容订单佣金设置' : '代理等级管理'}
          </Typography>
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={() => setShowAddDialog(true)}
          >
            添加等级
          </Button>
        </Box>

        <Alert severity="info" sx={{ mb: 2 }}>
          <Typography variant="body2">
            佣金比例说明：代理商获得其推广产生的订单金额的相应比例作为佣金。
            {tabValue === 0 && '订阅产品包括基础版、专业版、企业版等套餐。'}
            {tabValue === 1 && '内容订单包括AI内容生成、SEO优化等服务。'}
            {tabValue === 2 && '根据代理商的销售业绩自动升级等级。'}
          </Typography>
        </Alert>

        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>等级</TableCell>
                <TableCell>等级名称</TableCell>
                {(tabValue === 0 || tabValue === 2) && <TableCell align="center">订阅产品佣金(%)</TableCell>}
                {(tabValue === 1 || tabValue === 2) && <TableCell align="center">内容订单佣金(%)</TableCell>}
                {tabValue === 2 && <TableCell align="right">最低销售额要求</TableCell>}
                <TableCell align="center">代理商数量</TableCell>
                <TableCell align="right">累计收益</TableCell>
                <TableCell>描述</TableCell>
                <TableCell align="center">操作</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {agentLevels.map((level) => (
                <TableRow key={level.id}>
                  <TableCell>
                    <Chip
                      label={`L${level.level}`}
                      size="small"
                      sx={{
                        backgroundColor: getLevelColor(level.level),
                        color: '#fff',
                        fontWeight: 600,
                      }}
                    />
                  </TableCell>
                  <TableCell>
                    {editingId === level.id ? (
                      <TextField
                        value={level.name}
                        onChange={(e) => handleFieldChange(level.id, 'name', e.target.value)}
                        size="small"
                        fullWidth
                      />
                    ) : (
                      <Typography variant="body2" sx={{ fontWeight: 600 }}>
                        {level.name}
                      </Typography>
                    )}
                  </TableCell>
                  {(tabValue === 0 || tabValue === 2) && (
                    <TableCell align="center">
                      {editingId === level.id ? (
                        <TextField
                          value={level.subscriptionCommission}
                          onChange={(e) => handleFieldChange(level.id, 'subscriptionCommission', e.target.value)}
                          size="small"
                          type="number"
                          InputProps={{
                            endAdornment: <InputAdornment position="end">%</InputAdornment>,
                          }}
                          sx={{ width: 100 }}
                        />
                      ) : (
                        <Typography variant="body1" sx={{ fontWeight: 600, color: '#ff9800' }}>
                          {level.subscriptionCommission}%
                        </Typography>
                      )}
                    </TableCell>
                  )}
                  {(tabValue === 1 || tabValue === 2) && (
                    <TableCell align="center">
                      {editingId === level.id ? (
                        <TextField
                          value={level.contentCommission}
                          onChange={(e) => handleFieldChange(level.id, 'contentCommission', e.target.value)}
                          size="small"
                          type="number"
                          InputProps={{
                            endAdornment: <InputAdornment position="end">%</InputAdornment>,
                          }}
                          sx={{ width: 100 }}
                        />
                      ) : (
                        <Typography variant="body1" sx={{ fontWeight: 600, color: '#4caf50' }}>
                          {level.contentCommission}%
                        </Typography>
                      )}
                    </TableCell>
                  )}
                  {tabValue === 2 && (
                    <TableCell align="right">
                      {editingId === level.id ? (
                        <TextField
                          value={level.minSales}
                          onChange={(e) => handleFieldChange(level.id, 'minSales', e.target.value)}
                          size="small"
                          type="number"
                          InputProps={{
                            startAdornment: <InputAdornment position="start">¥</InputAdornment>,
                          }}
                          sx={{ width: 150 }}
                        />
                      ) : (
                        <Typography variant="body2">
                          ¥{level.minSales.toLocaleString()}
                        </Typography>
                      )}
                    </TableCell>
                  )}
                  <TableCell align="center">
                    <Typography variant="body2">{level.agentCount}</Typography>
                  </TableCell>
                  <TableCell align="right">
                    <Typography variant="body2" sx={{ color: '#1976d2', fontWeight: 600 }}>
                      ¥{level.totalRevenue.toLocaleString()}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    {editingId === level.id ? (
                      <TextField
                        value={level.description}
                        onChange={(e) => handleFieldChange(level.id, 'description', e.target.value)}
                        size="small"
                        fullWidth
                        multiline
                      />
                    ) : (
                      <Typography variant="body2" sx={{ color: '#666' }}>
                        {level.description}
                      </Typography>
                    )}
                  </TableCell>
                  <TableCell align="center">
                    {editingId === level.id ? (
                      <Box>
                        <IconButton
                          size="small"
                          color="primary"
                          onClick={() => handleSave(level)}
                        >
                          <Save />
                        </IconButton>
                        <IconButton
                          size="small"
                          color="default"
                          onClick={handleCancel}
                        >
                          <Cancel />
                        </IconButton>
                      </Box>
                    ) : (
                      <Box>
                        <IconButton
                          size="small"
                          color="primary"
                          onClick={() => handleEdit(level)}
                        >
                          <Edit />
                        </IconButton>
                        <IconButton
                          size="small"
                          color="error"
                          onClick={() => handleDeleteLevel(level.id)}
                        >
                          <Delete />
                        </IconButton>
                      </Box>
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>

        {/* 说明信息 */}
        <Box sx={{ mt: 3, p: 2, backgroundColor: '#f5f5f5', borderRadius: 1 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
            <Info sx={{ color: '#1976d2', mr: 1, fontSize: 20 }} />
            <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
              佣金计算说明
            </Typography>
          </Box>
          <Typography variant="body2" sx={{ color: '#666', lineHeight: 1.8 }}>
            1. 订阅产品佣金：代理商推广的用户购买订阅套餐时，代理商获得订单金额的相应比例作为佣金。
            <br />
            2. 内容订单佣金：代理商推广的用户购买内容服务时，代理商获得订单金额的相应比例作为佣金。
            <br />
            3. 等级升级：代理商的累计销售额达到相应等级的要求后，系统自动升级其等级，享受更高的佣金比例。
            <br />
            4. 佣金结算：佣金每月结算一次，结算后可申请提现。
          </Typography>
        </Box>
      </Paper>

      {/* 添加等级对话框 */}
      <Dialog open={showAddDialog} onClose={() => setShowAddDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>添加代理等级</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                label="等级名称"
                value={newLevel.name}
                onChange={(e) => setNewLevel({ ...newLevel, name: e.target.value })}
                fullWidth
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                label="等级数字"
                type="number"
                value={newLevel.level}
                onChange={(e) => setNewLevel({ ...newLevel, level: e.target.value })}
                fullWidth
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                label="订阅产品佣金(%)"
                type="number"
                value={newLevel.subscriptionCommission}
                onChange={(e) => setNewLevel({ ...newLevel, subscriptionCommission: e.target.value })}
                fullWidth
                required
                InputProps={{
                  endAdornment: <InputAdornment position="end">%</InputAdornment>,
                }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                label="内容订单佣金(%)"
                type="number"
                value={newLevel.contentCommission}
                onChange={(e) => setNewLevel({ ...newLevel, contentCommission: e.target.value })}
                fullWidth
                required
                InputProps={{
                  endAdornment: <InputAdornment position="end">%</InputAdornment>,
                }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                label="最低销售额要求"
                type="number"
                value={newLevel.minSales}
                onChange={(e) => setNewLevel({ ...newLevel, minSales: e.target.value })}
                fullWidth
                InputProps={{
                  startAdornment: <InputAdornment position="start">¥</InputAdornment>,
                }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                label="等级描述"
                value={newLevel.description}
                onChange={(e) => setNewLevel({ ...newLevel, description: e.target.value })}
                fullWidth
                multiline
                rows={2}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowAddDialog(false)}>取消</Button>
          <Button onClick={handleAddLevel} variant="contained">
            添加
          </Button>
        </DialogActions>
      </Dialog>

      {/* 提示消息 */}
      <Snackbar
        open={alert.open}
        autoHideDuration={3000}
        onClose={() => setAlert({ ...alert, open: false })}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert
          onClose={() => setAlert({ ...alert, open: false })}
          severity={alert.severity}
          sx={{ width: '100%' }}
        >
          {alert.message}
        </Alert>
      </Snackbar>
    </Container>
  );
}

export default CommissionSettings;