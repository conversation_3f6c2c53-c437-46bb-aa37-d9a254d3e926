import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Card,
  CardContent,
  Grid,
  Paper,
  Switch,
  FormControlLabel,
  Button,
  Divider,
  Alert,
  Chip,
  LinearProgress,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  Settings,
  Storage,
  Memory,
  Speed,
  Security,
  Backup,
  Update,
  Refresh,
  Warning,
  CheckCircle,
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';

function SystemManagement() {
  const { user } = useAuth();
  const [systemConfig, setSystemConfig] = useState({
    maintenanceMode: false,
    autoBackup: true,
    emailNotifications: true,
    debugMode: false,
    cacheEnabled: true,
  });

  const [systemStats, setSystemStats] = useState({
    uptime: '15天 8小时 32分钟',
    totalRequests: 1234567,
    errorRate: 0.02,
    avgResponseTime: 245,
    diskUsage: 45,
    memoryUsage: 62,
    cpuUsage: 38,
    activeConnections: 156,
  });

  const handleConfigChange = (key) => (event) => {
    setSystemConfig(prev => ({
      ...prev,
      [key]: event.target.checked
    }));
  };

  const handleRefreshStats = () => {
    // 模拟刷新系统统计数据
    // 这里可以添加实际的API调用
  };

  const systemHealthCards = [
    {
      title: 'CPU使用率',
      value: `${systemStats.cpuUsage}%`,
      color: systemStats.cpuUsage > 80 ? 'error' : 'primary',
      icon: <Speed />,
      progress: systemStats.cpuUsage,
    },
    {
      title: '内存使用率',
      value: `${systemStats.memoryUsage}%`,
      color: systemStats.memoryUsage > 80 ? 'warning' : 'success',
      icon: <Memory />,
      progress: systemStats.memoryUsage,
    },
    {
      title: '磁盘使用率',
      value: `${systemStats.diskUsage}%`,
      color: systemStats.diskUsage > 80 ? 'error' : 'info',
      icon: <Storage />,
      progress: systemStats.diskUsage,
    },
    {
      title: '活跃连接',
      value: systemStats.activeConnections,
      color: 'secondary',
      icon: <Security />,
      progress: (systemStats.activeConnections / 500) * 100,
    },
  ];

  return (
    <Box sx={{ width: '100%', height: '100%', p: 3 }}>
      {/* Header Section */}
      <Box sx={{ mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
          <Box>
            <Typography variant="h4" sx={{ fontWeight: 700, mb: 1, color: 'text.primary' }}>
              系统管理
            </Typography>
            <Typography variant="body1" color="text.secondary" sx={{ fontSize: '1.1rem' }}>
              管理系统配置、监控系统状态和维护系统健康
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Tooltip title="刷新数据">
              <IconButton
                onClick={handleRefreshStats}
                sx={{
                  backgroundColor: 'primary.light',
                  color: 'primary.main',
                  '&:hover': { backgroundColor: 'primary.main', color: 'white' }
                }}
              >
                <Refresh />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>
      </Box>



          {/* 系统状态概览 */}
          <Box sx={{ mb: 4 }}>
            <Typography variant="h6" sx={{ fontWeight: 600, mb: 3, color: 'primary.main' }}>
              系统状态概览
            </Typography>
            <Grid container spacing={3}>
              {systemHealthCards.map((card, index) => (
                <Grid item xs={12} sm={6} md={3} key={index}>
                  <Paper sx={{ p: 3, borderRadius: 2, backgroundColor: 'background.default', textAlign: 'center' }}>
                    <Box
                      sx={{
                        width: 48,
                        height: 48,
                        borderRadius: '50%',
                        backgroundColor: `${card.color}.main`,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        mx: 'auto',
                        mb: 2,
                      }}
                    >
                      {React.cloneElement(card.icon, { sx: { color: 'white', fontSize: 24 } })}
                    </Box>
                    <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                      {card.value}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      {card.title}
                    </Typography>
                    <LinearProgress
                      variant="determinate"
                      value={card.progress}
                      sx={{
                        height: 6,
                        borderRadius: 3,
                        backgroundColor: 'rgba(0,0,0,0.1)',
                        '& .MuiLinearProgress-bar': {
                          backgroundColor: `${card.color}.main`,
                        },
                      }}
                    />
                  </Paper>
                </Grid>
              ))}
            </Grid>
          </Box>

          <Divider sx={{ my: 4 }} />

          {/* 系统配置 */}
          <Box sx={{ mb: 4 }}>
            <Typography variant="h6" sx={{ fontWeight: 600, mb: 3, color: 'primary.main' }}>
              系统配置
            </Typography>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Paper sx={{ p: 3, borderRadius: 2, backgroundColor: 'background.default' }}>
                  <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2 }}>
                    基础设置
                  </Typography>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={systemConfig.maintenanceMode}
                          onChange={handleConfigChange('maintenanceMode')}
                          color="warning"
                        />
                      }
                      label="维护模式"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={systemConfig.autoBackup}
                          onChange={handleConfigChange('autoBackup')}
                          color="success"
                        />
                      }
                      label="自动备份"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={systemConfig.emailNotifications}
                          onChange={handleConfigChange('emailNotifications')}
                          color="info"
                        />
                      }
                      label="邮件通知"
                    />
                  </Box>
                </Paper>
              </Grid>
              <Grid item xs={12} md={6}>
                <Paper sx={{ p: 3, borderRadius: 2, backgroundColor: 'background.default' }}>
                  <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2 }}>
                    高级设置
                  </Typography>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={systemConfig.debugMode}
                          onChange={handleConfigChange('debugMode')}
                          color="error"
                        />
                      }
                      label="调试模式"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={systemConfig.cacheEnabled}
                          onChange={handleConfigChange('cacheEnabled')}
                          color="primary"
                        />
                      }
                      label="缓存启用"
                    />
                  </Box>
                </Paper>
              </Grid>
            </Grid>
          </Box>

          <Divider sx={{ my: 4 }} />

          {/* 系统信息 */}
          <Box>
            <Typography variant="h6" sx={{ fontWeight: 600, mb: 3, color: 'primary.main' }}>
              系统信息
            </Typography>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Paper sx={{ p: 3, borderRadius: 2, backgroundColor: 'background.default' }}>
                  <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2 }}>
                    运行状态
                  </Typography>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Typography variant="body2" color="text.secondary">系统运行时间:</Typography>
                      <Typography variant="body2">{systemStats.uptime}</Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Typography variant="body2" color="text.secondary">总请求数:</Typography>
                      <Typography variant="body2">{systemStats.totalRequests.toLocaleString()}</Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Typography variant="body2" color="text.secondary">错误率:</Typography>
                      <Chip
                        label={`${systemStats.errorRate}%`}
                        size="small"
                        color={systemStats.errorRate > 1 ? 'error' : 'success'}
                      />
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Typography variant="body2" color="text.secondary">平均响应时间:</Typography>
                      <Typography variant="body2">{systemStats.avgResponseTime}ms</Typography>
                    </Box>
                  </Box>
                </Paper>
              </Grid>
              <Grid item xs={12} md={6}>
                <Paper sx={{ p: 3, borderRadius: 2, backgroundColor: 'background.default' }}>
                  <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2 }}>
                    快速操作
                  </Typography>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                    <Button
                      variant="outlined"
                      startIcon={<Backup />}
                      fullWidth
                      sx={{ justifyContent: 'flex-start' }}
                    >
                      立即备份
                    </Button>
                    <Button
                      variant="outlined"
                      startIcon={<Update />}
                      fullWidth
                      sx={{ justifyContent: 'flex-start' }}
                    >
                      检查更新
                    </Button>
                    <Button
                      variant="outlined"
                      startIcon={<Refresh />}
                      fullWidth
                      sx={{ justifyContent: 'flex-start' }}
                    >
                      重启服务
                    </Button>
                  </Box>
                </Paper>
              </Grid>
            </Grid>
          </Box>
    </Box>
  );
}

export default SystemManagement;
