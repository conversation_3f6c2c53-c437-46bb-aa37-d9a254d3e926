import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  TextField,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  IconButton,
  Button,
  CircularProgress,
  Alert,
  Snackbar,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  Paper,
  Switch,
  FormControlLabel,
  Tooltip,
} from '@mui/material';
import {
  Search,
  Add,
  Edit,
  Delete,
  Visibility,
  Refresh,
  Security,
  AdminPanelSettings,
  Settings,
  Group,
} from '@mui/icons-material';
import ApiConfig from '../../config/api-config';
import { useAuth } from '../../contexts/AuthContext';

function RoleManagement() {
  const { token } = useAuth();
  
  // 状态管理
  const [roles, setRoles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [total, setTotal] = useState(0);
  const [search, setSearch] = useState('');
  const [roleTypeFilter, setRoleTypeFilter] = useState('');
  const [isActiveFilter, setIsActiveFilter] = useState('');
  
  // 对话框状态
  const [createDialog, setCreateDialog] = useState(false);
  const [editDialog, setEditDialog] = useState({ open: false, role: null });
  const [deleteDialog, setDeleteDialog] = useState({ open: false, role: null });
  const [detailDialog, setDetailDialog] = useState({ open: false, role: null });
  
  // 表单数据
  const [formData, setFormData] = useState({
    role_name: '',
    role_code: '',
    role_type: '',
    description: '',
    is_active: true
  });
  
  // 提示消息
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success'
  });
  
  // 动态角色类型选项
  const [roleTypeOptions, setRoleTypeOptions] = useState([]);


  
  // 显示提示消息
  const showSnackbar = (message, severity = 'success') => {
    setSnackbar({ open: true, message, severity });
  };
  
  // 关闭提示消息
  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };
  
  // 获取角色列表
  const fetchRoles = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: (page + 1).toString(),
        page_size: rowsPerPage.toString(),
      });
      
      if (search) params.append('search', search);
      if (roleTypeFilter) params.append('role_type', roleTypeFilter);
      if (isActiveFilter !== '') params.append('is_active', isActiveFilter);
      
      const response = await fetch(`${ApiConfig.baseURL}/api/v1/roles/?${params}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });
      
      if (response.ok) {
        const data = await response.json();
        setRoles(data.roles || []);
        setTotal(data.total || 0);

        // 从角色数据中提取唯一的角色类型
        const uniqueRoleTypes = [...new Set((data.roles || []).map(role => role.role_type))];
        const typeOptions = uniqueRoleTypes.map(type => ({
          value: type,
          label: type
        }));
        setRoleTypeOptions(typeOptions);
      } else {
        throw new Error('获取角色列表失败');
      }
    } catch (error) {
      console.error('获取角色列表错误:', error);
      showSnackbar('获取角色列表失败', 'error');
    } finally {
      setLoading(false);
    }
  };
  
  // 页面加载时获取数据
  useEffect(() => {
    fetchRoles();
  }, [page, rowsPerPage, search, roleTypeFilter, isActiveFilter]);
  
  // 搜索处理
  const handleSearchChange = (event) => {
    setSearch(event.target.value);
    setPage(0); // 重置到第一页
  };
  
  // 筛选处理
  const handleRoleTypeFilterChange = (event) => {
    setRoleTypeFilter(event.target.value);
    setPage(0);
  };
  
  const handleIsActiveFilterChange = (event) => {
    setIsActiveFilter(event.target.value);
    setPage(0);
  };
  
  // 分页处理
  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };
  
  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };
  
  // 获取角色类型标签颜色
  const getRoleTypeColor = (roleType) => {
    switch (roleType) {
      case 'business': return 'primary';
      case 'admin': return 'warning';
      case 'system': return 'error';
      default: return 'default';
    }
  };
  
  // 获取角色类型显示名称
  const getRoleTypeLabel = (roleType) => {
    return roleType;
  };

  // 创建角色
  const handleCreateRole = async () => {
    try {
      const response = await fetch(`${ApiConfig.baseURL}/api/v1/roles/`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        showSnackbar('角色创建成功', 'success');
        setCreateDialog(false);
        setFormData({
          role_name: '',
          role_code: '',
          role_type: '',
          description: '',
          is_active: true
        });
        fetchRoles();
      } else {
        const errorData = await response.json().catch(() => ({}));
        showSnackbar(errorData.detail || '创建角色失败', 'error');
      }
    } catch (error) {
      console.error('创建角色错误:', error);
      showSnackbar('创建角色失败', 'error');
    }
  };

  // 编辑角色
  const handleEditRole = (role) => {
    setFormData({
      role_name: role.role_name || '',
      role_code: role.role_code || '',
      role_type: role.role_type || '',
      description: role.description || '',
      is_active: role.is_active !== false
    });
    setEditDialog({ open: true, role });
  };

  // 更新角色
  const handleUpdateRole = async () => {
    try {
      const response = await fetch(`${ApiConfig.baseURL}/api/v1/roles/${editDialog.role.id}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        showSnackbar('角色更新成功', 'success');
        setEditDialog({ open: false, role: null });
        setFormData({
          role_name: '',
          role_code: '',
          role_type: '',
          description: '',
          is_active: true
        });
        fetchRoles();
      } else {
        const errorData = await response.json().catch(() => ({}));
        showSnackbar(errorData.detail || '更新角色失败', 'error');
      }
    } catch (error) {
      console.error('更新角色错误:', error);
      showSnackbar('更新角色失败', 'error');
    }
  };

  // 删除角色
  const handleDeleteRole = async () => {
    try {
      const response = await fetch(`${ApiConfig.baseURL}/api/v1/roles/${deleteDialog.role.id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        showSnackbar('角色删除成功', 'success');
        setDeleteDialog({ open: false, role: null });
        fetchRoles();
      } else {
        const errorData = await response.json().catch(() => ({}));
        showSnackbar(errorData.detail || '删除角色失败', 'error');
      }
    } catch (error) {
      console.error('删除角色错误:', error);
      showSnackbar('删除角色失败', 'error');
    }
  };
  
  return (
    <Box sx={{ p: 3 }}>
      {/* 页面标题 */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" sx={{ fontWeight: 700, color: 'primary.main', mb: 1 }}>
          角色管理
        </Typography>
        <Typography variant="body1" color="text.secondary">
          管理系统中的所有角色，包括创建、编辑、删除和查看角色详情
        </Typography>
      </Box>
      

          {/* 操作栏 */}
          <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="h6" sx={{ fontWeight: 600, color: 'primary.main' }}>
              角色列表
            </Typography>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Button
                variant="contained"
                startIcon={<Add />}
                onClick={() => setCreateDialog(true)}
                sx={{
                  transition: 'all 0.2s ease',
                  '&:hover': {
                    transform: 'translateY(-1px)',
                    boxShadow: '0 4px 12px rgba(25, 118, 210, 0.3)',
                  },
                }}
              >
                创建角色
              </Button>
              <IconButton
                onClick={fetchRoles}
                disabled={loading}
                sx={{
                  backgroundColor: 'primary.light',
                  color: 'primary.main',
                  '&:hover': { backgroundColor: 'primary.main', color: 'white' }
                }}
              >
                <Refresh />
              </IconButton>
            </Box>
          </Box>
          
          {/* 搜索和筛选栏 */}
          <Grid container spacing={3} sx={{ mb: 3 }}>
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                placeholder="角色名称和代码"
                value={search}
                onChange={handleSearchChange}
                variant="outlined"
                size="medium"
                sx={{
                  borderRadius: 2,
                  backgroundColor: 'background.paper',
                }}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Search sx={{ color: 'text.secondary' }} />
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel
                  shrink={true}
                  sx={{
                    color: 'text.secondary',
                    '&.Mui-focused': {
                      color: 'primary.main',
                    }
                  }}
                >
                  角色类型筛选
                </InputLabel>
                <Select
                  value={roleTypeFilter}
                  label="角色类型筛选"
                  onChange={handleRoleTypeFilterChange}
                  sx={{
                    borderRadius: 2,
                    backgroundColor: 'background.paper',
                    transition: 'all 0.3s ease',
                    '& .MuiOutlinedInput-notchedOutline': {
                      borderColor: 'divider',
                      transition: 'border-color 0.3s ease',
                    },
                    '&:hover .MuiOutlinedInput-notchedOutline': {
                      borderColor: 'primary.main',
                      boxShadow: '0 0 0 1px rgba(25, 118, 210, 0.1)',
                    },
                    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                      borderColor: 'primary.main',
                      borderWidth: 2,
                      boxShadow: '0 0 0 3px rgba(25, 118, 210, 0.1)',
                    },
                    '& .MuiSelect-select': {
                      minWidth: '120px',
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1,
                    },
                  }}
                >
                  <MenuItem value=""></MenuItem>
                  {roleTypeOptions.map(option => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel
                  shrink={true}
                  sx={{
                    color: 'text.secondary',
                    '&.Mui-focused': {
                      color: 'primary.main',
                    }
                  }}
                >
                  状态筛选
                </InputLabel>
                <Select
                  value={isActiveFilter}
                  label="状态筛选"
                  onChange={handleIsActiveFilterChange}
                  sx={{
                    borderRadius: 2,
                    backgroundColor: 'background.paper',
                    transition: 'all 0.3s ease',
                    '& .MuiOutlinedInput-notchedOutline': {
                      borderColor: 'divider',
                      transition: 'border-color 0.3s ease',
                    },
                    '&:hover .MuiOutlinedInput-notchedOutline': {
                      borderColor: 'primary.main',
                      boxShadow: '0 0 0 1px rgba(25, 118, 210, 0.1)',
                    },
                    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                      borderColor: 'primary.main',
                      borderWidth: 2,
                      boxShadow: '0 0 0 3px rgba(25, 118, 210, 0.1)',
                    },
                    '& .MuiSelect-select': {
                      minWidth: '120px',
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1,
                    },
                  }}
                >
                  <MenuItem value=""></MenuItem>
                  <MenuItem value="true">启用</MenuItem>
                  <MenuItem value="false">禁用</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>

          {/* 角色表格 */}
          <TableContainer
            component={Paper}
            variant="outlined"
            sx={{
              borderRadius: 2,
              transition: 'all 0.2s ease',
              '&:hover': {
                boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
              },
            }}
          >
            <Table>
              <TableHead>
                <TableRow sx={{ backgroundColor: 'grey.50' }}>
                  <TableCell sx={{ fontWeight: 600 }}>角色名称</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>角色代码</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>角色类型</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>权限数量</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>状态</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>创建时间</TableCell>
                  <TableCell sx={{ fontWeight: 600, textAlign: 'center' }}>操作</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={7} sx={{ textAlign: 'center', py: 4 }}>
                      <CircularProgress />
                    </TableCell>
                  </TableRow>
                ) : roles.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} sx={{ textAlign: 'center', py: 4 }}>
                      <Typography color="text.secondary">暂无角色数据</Typography>
                    </TableCell>
                  </TableRow>
                ) : (
                  roles.map((role) => (
                    <TableRow key={role.id} hover>
                      <TableCell>
                        <Typography variant="body2" sx={{ fontWeight: 500 }}>
                          {role.role_name}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" color="text.secondary">
                          {role.role_code}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={getRoleTypeLabel(role.role_type)}
                          color={getRoleTypeColor(role.role_type)}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {role.permission_count || 0}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={role.is_active ? '启用' : '禁用'}
                          color={role.is_active ? 'success' : 'default'}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" color="text.secondary">
                          {role.created_at ? new Date(role.created_at).toLocaleDateString() : '-'}
                        </Typography>
                      </TableCell>
                      <TableCell sx={{ textAlign: 'center' }}>
                        <Tooltip title="查看详情">
                          <IconButton
                            size="small"
                            onClick={() => setDetailDialog({ open: true, role })}
                            sx={{ mr: 1 }}
                          >
                            <Visibility fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="编辑">
                          <IconButton
                            size="small"
                            disabled={role.is_system_role}
                            onClick={() => handleEditRole(role)}
                            sx={{ mr: 1 }}
                          >
                            <Edit fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="删除">
                          <IconButton
                            size="small"
                            color="error"
                            disabled={role.is_system_role}
                            onClick={() => setDeleteDialog({ open: true, role })}
                          >
                            <Delete fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>

          {/* 分页 */}
          <TablePagination
            component="div"
            count={total}
            page={page}
            onPageChange={handleChangePage}
            rowsPerPage={rowsPerPage}
            onRowsPerPageChange={handleChangeRowsPerPage}
            rowsPerPageOptions={[5, 10, 25, 50]}
            labelRowsPerPage="每页显示:"
            labelDisplayedRows={({ from, to, count }) => `${from}-${to} 共 ${count} 条`}
          />

      {/* 创建角色对话框 */}
      <Dialog open={createDialog} onClose={() => setCreateDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>创建角色</DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <TextField
              fullWidth
              label="角色名称"
              value={formData.role_name}
              onChange={(e) => setFormData({...formData, role_name: e.target.value})}
              required
              InputLabelProps={{ shrink: true }}
              sx={{ mb: 2 }}
            />
            <TextField
              fullWidth
              label="角色代码"
              value={formData.role_code}
              onChange={(e) => setFormData({...formData, role_code: e.target.value})}
              required
              InputLabelProps={{ shrink: true }}
              sx={{ mb: 2 }}
            />
            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel shrink={true}>角色类型</InputLabel>
              <Select
                value={formData.role_type}
                label="角色类型"
                onChange={(e) => setFormData({...formData, role_type: e.target.value})}
                required
              >
                {roleTypeOptions.map(option => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            <TextField
              fullWidth
              label="角色描述（可选）"
              value={formData.description}
              onChange={(e) => setFormData({...formData, description: e.target.value})}
              multiline
              rows={3}
              InputLabelProps={{ shrink: true }}
              sx={{ mb: 2 }}
            />
            <FormControlLabel
              control={
                <Switch
                  checked={formData.is_active}
                  onChange={(e) => setFormData({...formData, is_active: e.target.checked})}
                />
              }
              label="启用角色"
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCreateDialog(false)}>取消</Button>
          <Button
            variant="contained"
            onClick={handleCreateRole}
            disabled={!formData.role_name || !formData.role_code || !formData.role_type}
          >
            创建
          </Button>
        </DialogActions>
      </Dialog>

      {/* 编辑角色对话框 */}
      <Dialog open={editDialog.open} onClose={() => setEditDialog({ open: false, role: null })} maxWidth="sm" fullWidth>
        <DialogTitle>编辑角色</DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <TextField
              fullWidth
              label="角色名称"
              value={formData.role_name}
              onChange={(e) => setFormData({...formData, role_name: e.target.value})}
              required
              InputLabelProps={{ shrink: true }}
              sx={{ mb: 2 }}
            />
            <TextField
              fullWidth
              label="角色代码"
              value={formData.role_code}
              onChange={(e) => setFormData({...formData, role_code: e.target.value})}
              required
              disabled={editDialog.role?.is_system_role}
              InputLabelProps={{ shrink: true }}
              sx={{ mb: 2 }}
            />
            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel shrink={true}>角色类型</InputLabel>
              <Select
                value={formData.role_type}
                label="角色类型"
                onChange={(e) => setFormData({...formData, role_type: e.target.value})}
                required
                disabled={editDialog.role?.is_system_role}
              >
                {roleTypeOptions.map(option => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            <TextField
              fullWidth
              label="角色描述（可选）"
              value={formData.description}
              onChange={(e) => setFormData({...formData, description: e.target.value})}
              multiline
              rows={3}
              InputLabelProps={{ shrink: true }}
              sx={{ mb: 2 }}
            />
            <FormControlLabel
              control={
                <Switch
                  checked={formData.is_active}
                  onChange={(e) => setFormData({...formData, is_active: e.target.checked})}
                />
              }
              label="启用角色"
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditDialog({ open: false, role: null })}>取消</Button>
          <Button
            variant="contained"
            onClick={handleUpdateRole}
            disabled={!formData.role_name || !formData.role_code || !formData.role_type}
          >
            更新
          </Button>
        </DialogActions>
      </Dialog>

      {/* 删除角色确认对话框 */}
      <Dialog open={deleteDialog.open} onClose={() => setDeleteDialog({ open: false, role: null })} maxWidth="sm" fullWidth>
        <DialogTitle>确认删除角色</DialogTitle>
        <DialogContent>
          {deleteDialog.role && (
            <Box sx={{ pt: 2 }}>
              <Alert severity="warning" sx={{ mb: 2 }}>
                删除角色后将无法恢复，请确认是否继续？
              </Alert>
              <Typography variant="body1" sx={{ mb: 1 }}>
                角色名称: <strong>{deleteDialog.role.role_name}</strong>
              </Typography>
              <Typography variant="body1" sx={{ mb: 1 }}>
                角色代码: <strong>{deleteDialog.role.role_code}</strong>
              </Typography>
              <Typography variant="body1" sx={{ mb: 1 }}>
                角色类型: <strong>{getRoleTypeLabel(deleteDialog.role.role_type)}</strong>
              </Typography>
              {deleteDialog.role.permission_count > 0 && (
                <Typography variant="body2" color="error" sx={{ mt: 2 }}>
                  注意：该角色当前拥有 {deleteDialog.role.permission_count} 个权限
                </Typography>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialog({ open: false, role: null })}>取消</Button>
          <Button
            onClick={handleDeleteRole}
            variant="contained"
            color="error"
          >
            确认删除
          </Button>
        </DialogActions>
      </Dialog>

      {/* 角色详情对话框 */}
      <Dialog open={detailDialog.open} onClose={() => setDetailDialog({ open: false, role: null })} maxWidth="md" fullWidth>
        <DialogTitle>角色详情</DialogTitle>
        <DialogContent>
          {detailDialog.role && (
            <Box sx={{ pt: 2 }}>
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary">角色名称:</Typography>
                  <Typography variant="body1" sx={{ fontWeight: 600 }}>{detailDialog.role.role_name}</Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary">角色代码:</Typography>
                  <Typography variant="body1">{detailDialog.role.role_code}</Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary">角色类型:</Typography>
                  <Chip
                    label={getRoleTypeLabel(detailDialog.role.role_type)}
                    color={getRoleTypeColor(detailDialog.role.role_type)}
                    size="small"
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary">权限数量:</Typography>
                  <Typography variant="body1">{detailDialog.role.permission_count || 0}</Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary">状态:</Typography>
                  <Chip
                    label={detailDialog.role.is_active ? '启用' : '禁用'}
                    color={detailDialog.role.is_active ? 'success' : 'default'}
                    size="small"
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary">系统角色:</Typography>
                  <Chip
                    label={detailDialog.role.is_system_role ? '是' : '否'}
                    color={detailDialog.role.is_system_role ? 'warning' : 'default'}
                    size="small"
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary">创建时间:</Typography>
                  <Typography variant="body1">
                    {detailDialog.role.created_at ? new Date(detailDialog.role.created_at).toLocaleString() : '无'}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary">更新时间:</Typography>
                  <Typography variant="body1">
                    {detailDialog.role.updated_at ? new Date(detailDialog.role.updated_at).toLocaleString() : '无'}
                  </Typography>
                </Grid>
                {detailDialog.role.description && (
                  <Grid item xs={12}>
                    <Typography variant="body2" color="text.secondary">角色描述:</Typography>
                    <Typography variant="body1">{detailDialog.role.description}</Typography>
                  </Grid>
                )}
              </Grid>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => setDetailDialog({ open: false, role: null })}
            variant="contained"
            sx={{
              backgroundColor: 'grey.500',
              '&:hover': {
                backgroundColor: 'grey.600',
              }
            }}
          >
            关闭
          </Button>
        </DialogActions>
      </Dialog>

      {/* 提示消息 */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity} sx={{ width: '100%' }}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
}

export default RoleManagement;
