import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  CardActions,
  Paper,
  Button,
  IconButton,
  Tabs,
  Tab,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  AlertTitle,
  CircularProgress,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction,
  Divider,
  InputAdornment,
  Stack,
  Avatar,
  Switch,
  FormControlLabel,
  FormGroup,
  FormHelperText,
  Slider,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Badge,
  Stepper,
  Step,
  StepLabel,
  ToggleButton,
  ToggleButtonGroup,
  Collapse,
  Radio,
  RadioGroup,
  Fab,
  SpeedDial,
  SpeedDialAction,
  Snackbar,
} from '@mui/material';
import {
  Payment,
  CreditCard,
  AccountBalance,
  AttachMoney,
  Subscriptions,
  CardGiftcard,
  Email,
  Sms,
  Notifications,
  NotificationsActive,
  Rule,
  Gavel,
  BusinessCenter,
  Webhook,
  Link,
  LinkOff,
  Settings,
  Save,
  Restore,
  History,
  Upload,
  Download,
  Edit,
  Delete,
  Add,
  Remove,
  Check,
  Close,
  Warning,
  Error,
  Info,
  ExpandMore,
  ContentCopy,
  Visibility,
  VisibilityOff,
  Send,
  PlayArrow,
  Pause,
  Refresh,
  Security,
  Lock,
  LockOpen,
  MoneyOff,
  TrendingUp,
  TrendingDown,
  Timeline,
  Code,
  Api,
  Cloud,
  CloudOff,
  Sync,
  SyncDisabled,
  CheckCircle,
  Cancel,
  Schedule,
  Timer,
  AlarmOn,
  Person,
  Group,
  AdminPanelSettings,
  ManageAccounts,
  VerifiedUser,
  Shield,
  Policy,
  Description,
  Article,
  Template,
} from '@mui/icons-material';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { useSnackbar } from 'notistack';
import systemConfigService from '../../services/systemConfigService';

// Tab panel component
function TabPanel({ children, value, index, ...other }) {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`config-tabpanel-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

// Payment method card component
const PaymentMethodCard = ({ method, onToggle, onEdit }) => {
  const getIcon = () => {
    switch (method.icon) {
      case 'alipay': return <Payment />;
      case 'wechat': return <Payment />;
      case 'bank': return <AccountBalance />;
      case 'credit_card': return <CreditCard />;
      default: return <AttachMoney />;
    }
  };

  return (
    <Card sx={{ height: '100%' }}>
      <CardContent>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'start', mb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Avatar sx={{ bgcolor: method.enabled ? 'primary.main' : 'grey.500' }}>
              {getIcon()}
            </Avatar>
            <Box>
              <Typography variant="h6">{method.name}</Typography>
              <Chip
                label={method.enabled ? '已启用' : '已禁用'}
                color={method.enabled ? 'success' : 'default'}
                size="small"
              />
            </Box>
          </Box>
          <Switch
            checked={method.enabled}
            onChange={() => onToggle(method.id)}
            color="primary"
          />
        </Box>
        
        <Grid container spacing={1}>
          <Grid item xs={6}>
            <Typography variant="caption" color="text.secondary">手续费</Typography>
            <Typography variant="body2">
              {method.fee_type === 'percentage' ? `${method.fee_rate}%` : `¥${method.fee_rate}`}
            </Typography>
          </Grid>
          <Grid item xs={6}>
            <Typography variant="caption" color="text.secondary">限额</Typography>
            <Typography variant="body2">
              ¥{method.min_amount} - ¥{method.max_amount}
            </Typography>
          </Grid>
          <Grid item xs={12}>
            <Typography variant="caption" color="text.secondary">日限额</Typography>
            <Typography variant="body2">¥{method.daily_limit}</Typography>
          </Grid>
        </Grid>
      </CardContent>
      <CardActions>
        <Button size="small" startIcon={<Edit />} onClick={() => onEdit(method)}>
          配置
        </Button>
      </CardActions>
    </Card>
  );
};

// Subscription plan card component
const SubscriptionPlanCard = ({ plan, onEdit }) => {
  return (
    <Card sx={{ height: '100%', position: 'relative' }}>
      {plan.is_default && (
        <Chip
          label="默认套餐"
          color="primary"
          size="small"
          sx={{ position: 'absolute', top: 8, right: 8 }}
        />
      )}
      <CardContent>
        <Typography variant="h6" gutterBottom>{plan.name}</Typography>
        <Typography variant="h4" color="primary" gutterBottom>
          ¥{plan.price}
          <Typography variant="caption" color="text.secondary">
            /{plan.billing_cycle === 'monthly' ? '月' : plan.billing_cycle === 'yearly' ? '年' : '永久'}
          </Typography>
        </Typography>
        
        {plan.trial_days > 0 && (
          <Chip
            label={`${plan.trial_days}天试用`}
            size="small"
            variant="outlined"
            sx={{ mb: 2 }}
          />
        )}
        
        <Typography variant="subtitle2" gutterBottom>功能特性</Typography>
        <List dense>
          {plan.features.slice(0, 3).map((feature, index) => (
            <ListItem key={index} disablePadding>
              <ListItemIcon sx={{ minWidth: 30 }}>
                <Check fontSize="small" color="success" />
              </ListItemIcon>
              <ListItemText primary={feature} primaryTypographyProps={{ variant: 'body2' }} />
            </ListItem>
          ))}
        </List>
        
        <Divider sx={{ my: 1 }} />
        
        <Grid container spacing={1}>
          <Grid item xs={6}>
            <Typography variant="caption" color="text.secondary">项目数</Typography>
            <Typography variant="body2">
              {plan.limits.projects === -1 ? '无限' : plan.limits.projects}
            </Typography>
          </Grid>
          <Grid item xs={6}>
            <Typography variant="caption" color="text.secondary">用户数</Typography>
            <Typography variant="body2">
              {plan.limits.users === -1 ? '无限' : plan.limits.users}
            </Typography>
          </Grid>
        </Grid>
      </CardContent>
      <CardActions>
        <Button size="small" fullWidth startIcon={<Edit />} onClick={() => onEdit(plan)}>
          编辑套餐
        </Button>
      </CardActions>
    </Card>
  );
};

const SystemConfig = () => {
  const { enqueueSnackbar } = useSnackbar();
  
  // State management
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [activeTab, setActiveTab] = useState(0);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  
  // Configuration states
  const [paymentConfig, setPaymentConfig] = useState(null);
  const [subscriptionConfig, setSubscriptionConfig] = useState(null);
  const [notificationConfig, setNotificationConfig] = useState(null);
  const [businessRules, setBusinessRules] = useState(null);
  const [webhookConfig, setWebhookConfig] = useState(null);
  const [configHistory, setConfigHistory] = useState([]);
  
  // Dialog states
  const [paymentEditDialog, setPaymentEditDialog] = useState(false);
  const [selectedPayment, setSelectedPayment] = useState(null);
  const [planEditDialog, setPlanEditDialog] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState(null);
  const [templateEditDialog, setTemplateEditDialog] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [webhookEditDialog, setWebhookEditDialog] = useState(false);
  const [selectedWebhook, setSelectedWebhook] = useState(null);
  const [historyDialog, setHistoryDialog] = useState(false);
  
  // Load initial data
  useEffect(() => {
    loadAllConfigs();
  }, []);

  // Warn about unsaved changes
  useEffect(() => {
    const handleBeforeUnload = (e) => {
      if (hasUnsavedChanges) {
        e.preventDefault();
        e.returnValue = '';
      }
    };
    
    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [hasUnsavedChanges]);

  // Load all configurations
  const loadAllConfigs = async () => {
    setLoading(true);
    try {
      const [payment, subscription, notification, rules, webhook, history] = await Promise.all([
        systemConfigService.getPaymentConfig(),
        systemConfigService.getSubscriptionConfig(),
        systemConfigService.getNotificationConfig(),
        systemConfigService.getBusinessRules(),
        systemConfigService.getWebhookConfig(),
        systemConfigService.getConfigHistory()
      ]);
      
      setPaymentConfig(payment);
      setSubscriptionConfig(subscription);
      setNotificationConfig(notification);
      setBusinessRules(rules);
      setWebhookConfig(webhook);
      setConfigHistory(history);
    } catch (error) {
      enqueueSnackbar('加载配置失败', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  // Save configurations
  const handleSaveConfig = async () => {
    setSaving(true);
    try {
      switch (activeTab) {
        case 0:
          await systemConfigService.updatePaymentConfig(paymentConfig);
          break;
        case 1:
          await systemConfigService.updateSubscriptionConfig(subscriptionConfig);
          break;
        case 2:
          await systemConfigService.updateNotificationConfig(notificationConfig);
          break;
        case 3:
          await systemConfigService.updateBusinessRules(businessRules);
          break;
        case 4:
          await systemConfigService.updateWebhookConfig(webhookConfig);
          break;
      }
      
      enqueueSnackbar('配置保存成功', { variant: 'success' });
      setHasUnsavedChanges(false);
      loadAllConfigs(); // Reload to get latest version
    } catch (error) {
      enqueueSnackbar('保存配置失败', { variant: 'error' });
    } finally {
      setSaving(false);
    }
  };

  // Export configuration
  const handleExportConfig = async () => {
    try {
      const blob = await systemConfigService.exportConfig();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `system-config-${format(new Date(), 'yyyyMMdd-HHmmss')}.json`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      enqueueSnackbar('配置导出成功', { variant: 'success' });
    } catch (error) {
      enqueueSnackbar('导出配置失败', { variant: 'error' });
    }
  };

  // Import configuration
  const handleImportConfig = async (event) => {
    const file = event.target.files[0];
    if (!file) return;
    
    try {
      await systemConfigService.importConfig(file);
      enqueueSnackbar('配置导入成功', { variant: 'success' });
      loadAllConfigs();
    } catch (error) {
      enqueueSnackbar('导入配置失败', { variant: 'error' });
    }
  };

  // Payment configuration handlers
  const handleTogglePayment = (paymentId) => {
    setPaymentConfig(prev => ({
      ...prev,
      payment_methods: prev.payment_methods.map(method =>
        method.id === paymentId ? { ...method, enabled: !method.enabled } : method
      )
    }));
    setHasUnsavedChanges(true);
  };

  const handleEditPayment = (payment) => {
    setSelectedPayment(payment);
    setPaymentEditDialog(true);
  };

  const handleUpdatePayment = (updatedPayment) => {
    setPaymentConfig(prev => ({
      ...prev,
      payment_methods: prev.payment_methods.map(method =>
        method.id === updatedPayment.id ? updatedPayment : method
      )
    }));
    setHasUnsavedChanges(true);
    setPaymentEditDialog(false);
  };

  // Test webhook
  const handleTestWebhook = async (webhookId) => {
    try {
      const result = await systemConfigService.testWebhook(webhookId);
      enqueueSnackbar(
        result.success ? 'Webhook测试成功' : 'Webhook测试失败',
        { variant: result.success ? 'success' : 'error' }
      );
    } catch (error) {
      enqueueSnackbar('测试失败', { variant: 'error' });
    }
  };

  // Render payment configuration tab
  const renderPaymentConfig = () => (
    <Box>
      <Typography variant="h6" gutterBottom>支付方式配置</Typography>
      
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {paymentConfig?.payment_methods?.map((method) => (
          <Grid item xs={12} md={6} lg={3} key={method.id}>
            <PaymentMethodCard
              method={method}
              onToggle={handleTogglePayment}
              onEdit={handleEditPayment}
            />
          </Grid>
        ))}
      </Grid>

      <Divider sx={{ my: 3 }} />

      <Typography variant="h6" gutterBottom>全局支付设置</Typography>
      
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="subtitle1" gutterBottom>自动确认设置</Typography>
              <TextField
                fullWidth
                label="自动确认阈值"
                type="number"
                value={paymentConfig?.global_settings?.auto_confirm_threshold || 0}
                onChange={(e) => {
                  setPaymentConfig(prev => ({
                    ...prev,
                    global_settings: {
                      ...prev.global_settings,
                      auto_confirm_threshold: parseFloat(e.target.value)
                    }
                  }));
                  setHasUnsavedChanges(true);
                }}
                InputProps={{
                  startAdornment: <InputAdornment position="start">¥</InputAdornment>
                }}
                helperText="低于此金额的订单自动确认"
                sx={{ mb: 2 }}
              />
              
              <FormControlLabel
                control={
                  <Switch
                    checked={paymentConfig?.global_settings?.manual_review_required || false}
                    onChange={(e) => {
                      setPaymentConfig(prev => ({
                        ...prev,
                        global_settings: {
                          ...prev.global_settings,
                          manual_review_required: e.target.checked
                        }
                      }));
                      setHasUnsavedChanges(true);
                    }}
                  />
                }
                label="需要人工审核"
              />
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="subtitle1" gutterBottom>退款设置</Typography>
              <FormGroup>
                <FormControlLabel
                  control={
                    <Switch
                      checked={paymentConfig?.global_settings?.refund_enabled || false}
                      onChange={(e) => {
                        setPaymentConfig(prev => ({
                          ...prev,
                          global_settings: {
                            ...prev.global_settings,
                            refund_enabled: e.target.checked
                          }
                        }));
                        setHasUnsavedChanges(true);
                      }}
                    />
                  }
                  label="启用退款功能"
                />
                
                <FormControlLabel
                  control={
                    <Switch
                      checked={paymentConfig?.global_settings?.partial_refund_enabled || false}
                      onChange={(e) => {
                        setPaymentConfig(prev => ({
                          ...prev,
                          global_settings: {
                            ...prev.global_settings,
                            partial_refund_enabled: e.target.checked
                          }
                        }));
                        setHasUnsavedChanges(true);
                      }}
                    />
                  }
                  label="允许部分退款"
                />
              </FormGroup>
              
              <TextField
                fullWidth
                label="退款时限"
                type="number"
                value={paymentConfig?.global_settings?.refund_time_limit || 7}
                onChange={(e) => {
                  setPaymentConfig(prev => ({
                    ...prev,
                    global_settings: {
                      ...prev.global_settings,
                      refund_time_limit: parseInt(e.target.value)
                    }
                  }));
                  setHasUnsavedChanges(true);
                }}
                InputProps={{
                  endAdornment: <InputAdornment position="end">天</InputAdornment>
                }}
                sx={{ mt: 2 }}
              />
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );

  // Render subscription configuration tab
  const renderSubscriptionConfig = () => (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h6">订阅套餐配置</Typography>
        <Button startIcon={<Add />} variant="contained" onClick={() => setPlanEditDialog(true)}>
          添加套餐
        </Button>
      </Box>
      
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {subscriptionConfig?.plans?.map((plan) => (
          <Grid item xs={12} md={6} lg={3} key={plan.id}>
            <SubscriptionPlanCard plan={plan} onEdit={() => {
              setSelectedPlan(plan);
              setPlanEditDialog(true);
            }} />
          </Grid>
        ))}
      </Grid>

      <Divider sx={{ my: 3 }} />

      <Typography variant="h6" gutterBottom>订阅设置</Typography>
      
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="subtitle1" gutterBottom>试用设置</Typography>
              <FormGroup>
                <FormControlLabel
                  control={
                    <Switch
                      checked={subscriptionConfig?.settings?.trial_enabled || false}
                      onChange={(e) => {
                        setSubscriptionConfig(prev => ({
                          ...prev,
                          settings: {
                            ...prev.settings,
                            trial_enabled: e.target.checked
                          }
                        }));
                        setHasUnsavedChanges(true);
                      }}
                    />
                  }
                  label="启用试用期"
                />
                
                <TextField
                  fullWidth
                  label="默认试用天数"
                  type="number"
                  value={subscriptionConfig?.settings?.default_trial_days || 14}
                  onChange={(e) => {
                    setSubscriptionConfig(prev => ({
                      ...prev,
                      settings: {
                        ...prev.settings,
                        default_trial_days: parseInt(e.target.value)
                      }
                    }));
                    setHasUnsavedChanges(true);
                  }}
                  disabled={!subscriptionConfig?.settings?.trial_enabled}
                  sx={{ mt: 2 }}
                />
              </FormGroup>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="subtitle1" gutterBottom>续订设置</Typography>
              <FormGroup>
                <FormControlLabel
                  control={
                    <Switch
                      checked={subscriptionConfig?.settings?.auto_renew_enabled || false}
                      onChange={(e) => {
                        setSubscriptionConfig(prev => ({
                          ...prev,
                          settings: {
                            ...prev.settings,
                            auto_renew_enabled: e.target.checked
                          }
                        }));
                        setHasUnsavedChanges(true);
                      }}
                    />
                  }
                  label="自动续订"
                />
                
                <FormControlLabel
                  control={
                    <Switch
                      checked={subscriptionConfig?.settings?.upgrade_proration || false}
                      onChange={(e) => {
                        setSubscriptionConfig(prev => ({
                          ...prev,
                          settings: {
                            ...prev.settings,
                            upgrade_proration: e.target.checked
                          }
                        }));
                        setHasUnsavedChanges(true);
                      }}
                    />
                  }
                  label="升级按比例计费"
                />
                
                <TextField
                  fullWidth
                  label="宽限期"
                  type="number"
                  value={subscriptionConfig?.settings?.grace_period_days || 3}
                  onChange={(e) => {
                    setSubscriptionConfig(prev => ({
                      ...prev,
                      settings: {
                        ...prev.settings,
                        grace_period_days: parseInt(e.target.value)
                      }
                    }));
                    setHasUnsavedChanges(true);
                  }}
                  InputProps={{
                    endAdornment: <InputAdornment position="end">天</InputAdornment>
                  }}
                  sx={{ mt: 2 }}
                />
              </FormGroup>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="subtitle1" gutterBottom>折扣配置</Typography>
              <TableContainer>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>折扣名称</TableCell>
                      <TableCell>类型</TableCell>
                      <TableCell>折扣值</TableCell>
                      <TableCell>适用套餐</TableCell>
                      <TableCell>条件</TableCell>
                      <TableCell align="center">操作</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {subscriptionConfig?.discounts?.map((discount) => (
                      <TableRow key={discount.id}>
                        <TableCell>{discount.name}</TableCell>
                        <TableCell>
                          <Chip
                            label={discount.type === 'percentage' ? '百分比' : '固定金额'}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          {discount.type === 'percentage' ? `${discount.value}%` : `¥${discount.value}`}
                        </TableCell>
                        <TableCell>
                          {discount.applicable_plans.join(', ')}
                        </TableCell>
                        <TableCell>
                          {discount.conditions.valid_until || discount.conditions.min_billing_cycle || '-'}
                        </TableCell>
                        <TableCell align="center">
                          <IconButton size="small">
                            <Edit />
                          </IconButton>
                          <IconButton size="small" color="error">
                            <Delete />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );

  // Render notification configuration tab
  const renderNotificationConfig = () => (
    <Box>
      <Typography variant="h6" gutterBottom>通知渠道配置</Typography>
      
      <Grid container spacing={3}>
        {/* Email configuration */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="subtitle1">邮件通知</Typography>
                <Switch
                  checked={notificationConfig?.email?.enabled || false}
                  onChange={(e) => {
                    setNotificationConfig(prev => ({
                      ...prev,
                      email: {
                        ...prev.email,
                        enabled: e.target.checked
                      }
                    }));
                    setHasUnsavedChanges(true);
                  }}
                />
              </Box>
              
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    size="small"
                    label="SMTP服务器"
                    value={notificationConfig?.email?.config?.host || ''}
                    onChange={(e) => {
                      setNotificationConfig(prev => ({
                        ...prev,
                        email: {
                          ...prev.email,
                          config: {
                            ...prev.email.config,
                            host: e.target.value
                          }
                        }
                      }));
                      setHasUnsavedChanges(true);
                    }}
                  />
                </Grid>
                <Grid item xs={6}>
                  <TextField
                    fullWidth
                    size="small"
                    label="端口"
                    type="number"
                    value={notificationConfig?.email?.config?.port || ''}
                    onChange={(e) => {
                      setNotificationConfig(prev => ({
                        ...prev,
                        email: {
                          ...prev.email,
                          config: {
                            ...prev.email.config,
                            port: parseInt(e.target.value)
                          }
                        }
                      }));
                      setHasUnsavedChanges(true);
                    }}
                  />
                </Grid>
                <Grid item xs={6}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={notificationConfig?.email?.config?.secure || false}
                        onChange={(e) => {
                          setNotificationConfig(prev => ({
                            ...prev,
                            email: {
                              ...prev.email,
                              config: {
                                ...prev.email.config,
                                secure: e.target.checked
                              }
                            }
                          }));
                          setHasUnsavedChanges(true);
                        }}
                      />
                    }
                    label="SSL/TLS"
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    size="small"
                    label="发件人邮箱"
                    value={notificationConfig?.email?.config?.from_email || ''}
                    onChange={(e) => {
                      setNotificationConfig(prev => ({
                        ...prev,
                        email: {
                          ...prev.email,
                          config: {
                            ...prev.email.config,
                            from_email: e.target.value
                          }
                        }
                      }));
                      setHasUnsavedChanges(true);
                    }}
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* SMS configuration */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="subtitle1">短信通知</Typography>
                <Switch
                  checked={notificationConfig?.sms?.enabled || false}
                  onChange={(e) => {
                    setNotificationConfig(prev => ({
                      ...prev,
                      sms: {
                        ...prev.sms,
                        enabled: e.target.checked
                      }
                    }));
                    setHasUnsavedChanges(true);
                  }}
                />
              </Box>
              
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <FormControl fullWidth size="small">
                    <InputLabel>服务商</InputLabel>
                    <Select
                      value={notificationConfig?.sms?.provider || 'aliyun'}
                      onChange={(e) => {
                        setNotificationConfig(prev => ({
                          ...prev,
                          sms: {
                            ...prev.sms,
                            provider: e.target.value
                          }
                        }));
                        setHasUnsavedChanges(true);
                      }}
                    >
                      <MenuItem value="aliyun">阿里云</MenuItem>
                      <MenuItem value="tencent">腾讯云</MenuItem>
                      <MenuItem value="huawei">华为云</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    size="small"
                    label="Access Key"
                    type="password"
                    value={notificationConfig?.sms?.config?.access_key || ''}
                    onChange={(e) => {
                      setNotificationConfig(prev => ({
                        ...prev,
                        sms: {
                          ...prev.sms,
                          config: {
                            ...prev.sms.config,
                            access_key: e.target.value
                          }
                        }
                      }));
                      setHasUnsavedChanges(true);
                    }}
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    size="small"
                    label="签名"
                    value={notificationConfig?.sms?.config?.sign_name || ''}
                    onChange={(e) => {
                      setNotificationConfig(prev => ({
                        ...prev,
                        sms: {
                          ...prev.sms,
                          config: {
                            ...prev.sms.config,
                            sign_name: e.target.value
                          }
                        }
                      }));
                      setHasUnsavedChanges(true);
                    }}
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    size="small"
                    label="日发送限制"
                    type="number"
                    value={notificationConfig?.sms?.config?.daily_limit || 1000}
                    onChange={(e) => {
                      setNotificationConfig(prev => ({
                        ...prev,
                        sms: {
                          ...prev.sms,
                          config: {
                            ...prev.sms.config,
                            daily_limit: parseInt(e.target.value)
                          }
                        }
                      }));
                      setHasUnsavedChanges(true);
                    }}
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Email templates */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="subtitle1">邮件模板</Typography>
                <Button size="small" startIcon={<Add />}>添加模板</Button>
              </Box>
              
              <TableContainer>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>模板名称</TableCell>
                      <TableCell>主题</TableCell>
                      <TableCell>变量</TableCell>
                      <TableCell>状态</TableCell>
                      <TableCell align="center">操作</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {notificationConfig?.email?.templates?.map((template) => (
                      <TableRow key={template.id}>
                        <TableCell>{template.name}</TableCell>
                        <TableCell>{template.subject}</TableCell>
                        <TableCell>
                          <Stack direction="row" spacing={0.5}>
                            {template.variables.slice(0, 2).map(v => (
                              <Chip key={v} label={v} size="small" />
                            ))}
                            {template.variables.length > 2 && (
                              <Chip label={`+${template.variables.length - 2}`} size="small" />
                            )}
                          </Stack>
                        </TableCell>
                        <TableCell>
                          <Switch
                            size="small"
                            checked={template.enabled}
                            onChange={(e) => {
                              setNotificationConfig(prev => ({
                                ...prev,
                                email: {
                                  ...prev.email,
                                  templates: prev.email.templates.map(t =>
                                    t.id === template.id ? { ...t, enabled: e.target.checked } : t
                                  )
                                }
                              }));
                              setHasUnsavedChanges(true);
                            }}
                          />
                        </TableCell>
                        <TableCell align="center">
                          <IconButton size="small" onClick={() => {
                            setSelectedTemplate(template);
                            setTemplateEditDialog(true);
                          }}>
                            <Edit />
                          </IconButton>
                          <IconButton size="small">
                            <Send />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );

  // Render business rules tab
  const renderBusinessRules = () => (
    <Box>
      <Grid container spacing={3}>
        {/* Refund rules */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>退款规则</Typography>
              
              <FormGroup sx={{ mb: 2 }}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={businessRules?.refund?.enabled || false}
                      onChange={(e) => {
                        setBusinessRules(prev => ({
                          ...prev,
                          refund: {
                            ...prev.refund,
                            enabled: e.target.checked
                          }
                        }));
                        setHasUnsavedChanges(true);
                      }}
                    />
                  }
                  label="启用退款"
                />
              </FormGroup>
              
              <TextField
                fullWidth
                label="自动审批阈值"
                type="number"
                value={businessRules?.refund?.auto_approve_threshold || 0}
                onChange={(e) => {
                  setBusinessRules(prev => ({
                    ...prev,
                    refund: {
                      ...prev.refund,
                      auto_approve_threshold: parseFloat(e.target.value)
                    }
                  }));
                  setHasUnsavedChanges(true);
                }}
                InputProps={{
                  startAdornment: <InputAdornment position="start">¥</InputAdornment>
                }}
                helperText="低于此金额自动批准退款"
                sx={{ mb: 2 }}
              />
              
              <Typography variant="subtitle2" gutterBottom>退款原因</Typography>
              <List dense>
                {businessRules?.refund?.reasons?.map((reason) => (
                  <ListItem key={reason.id}>
                    <ListItemText primary={reason.name} />
                    <ListItemSecondaryAction>
                      <Chip
                        label={reason.auto_approve ? '自动批准' : '需审核'}
                        size="small"
                        color={reason.auto_approve ? 'success' : 'default'}
                      />
                    </ListItemSecondaryAction>
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Upgrade rules */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>升级规则</Typography>
              
              <FormGroup>
                <FormControlLabel
                  control={
                    <Switch
                      checked={businessRules?.upgrade?.enabled || false}
                      onChange={(e) => {
                        setBusinessRules(prev => ({
                          ...prev,
                          upgrade: {
                            ...prev.upgrade,
                            enabled: e.target.checked
                          }
                        }));
                        setHasUnsavedChanges(true);
                      }}
                    />
                  }
                  label="允许升级"
                />
                
                <FormControlLabel
                  control={
                    <Switch
                      checked={businessRules?.upgrade?.immediate_upgrade || false}
                      onChange={(e) => {
                        setBusinessRules(prev => ({
                          ...prev,
                          upgrade: {
                            ...prev.upgrade,
                            immediate_upgrade: e.target.checked
                          }
                        }));
                        setHasUnsavedChanges(true);
                      }}
                    />
                  }
                  label="立即生效"
                />
                
                <FormControlLabel
                  control={
                    <Switch
                      checked={businessRules?.upgrade?.downgrade_at_period_end || false}
                      onChange={(e) => {
                        setBusinessRules(prev => ({
                          ...prev,
                          upgrade: {
                            ...prev.upgrade,
                            downgrade_at_period_end: e.target.checked
                          }
                        }));
                        setHasUnsavedChanges(true);
                      }}
                    />
                  }
                  label="降级在期末生效"
                />
              </FormGroup>
              
              <Divider sx={{ my: 2 }} />
              
              <Typography variant="subtitle2" gutterBottom>升级路径</Typography>
              <List dense>
                {businessRules?.upgrade?.rules?.map((rule, index) => (
                  <ListItem key={index}>
                    <ListItemText
                      primary={`${rule.from_plan} → ${rule.to_plan}`}
                      secondary={rule.requirements.length > 0 ? `需要: ${rule.requirements.join(', ')}` : '无限制'}
                    />
                    <ListItemSecondaryAction>
                      <Switch
                        size="small"
                        checked={rule.allowed}
                        onChange={(e) => {
                          setBusinessRules(prev => ({
                            ...prev,
                            upgrade: {
                              ...prev.upgrade,
                              rules: prev.upgrade.rules.map((r, i) =>
                                i === index ? { ...r, allowed: e.target.checked } : r
                              )
                            }
                          }));
                          setHasUnsavedChanges(true);
                        }}
                      />
                    </ListItemSecondaryAction>
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Commission rules */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>佣金规则</Typography>
              
              <FormControlLabel
                control={
                  <Switch
                    checked={businessRules?.commission?.enabled || false}
                    onChange={(e) => {
                      setBusinessRules(prev => ({
                        ...prev,
                        commission: {
                          ...prev.commission,
                          enabled: e.target.checked
                        }
                      }));
                      setHasUnsavedChanges(true);
                    }}
                  />
                }
                label="启用佣金系统"
                sx={{ mb: 2 }}
              />
              
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" gutterBottom>分级佣金</Typography>
                  <TableContainer>
                    <Table size="small">
                      <TableHead>
                        <TableRow>
                          <TableCell>销售额范围</TableCell>
                          <TableCell>佣金率</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {businessRules?.commission?.tiers?.map((tier, index) => (
                          <TableRow key={index}>
                            <TableCell>
                              ¥{tier.min} - {tier.max ? `¥${tier.max}` : '以上'}
                            </TableCell>
                            <TableCell>{tier.rate}%</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" gutterBottom>特殊费率</Typography>
                  <TableContainer>
                    <Table size="small">
                      <TableHead>
                        <TableRow>
                          <TableCell>合作伙伴类型</TableCell>
                          <TableCell>佣金率</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {businessRules?.commission?.special_rates?.map((rate) => (
                          <TableRow key={rate.partner_type}>
                            <TableCell>
                              <Chip label={rate.partner_type} size="small" color="primary" />
                            </TableCell>
                            <TableCell>{rate.rate}%</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );

  // Render webhook configuration tab
  const renderWebhookConfig = () => (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h6">Webhook配置</Typography>
        <Button startIcon={<Add />} variant="contained" onClick={() => setWebhookEditDialog(true)}>
          添加Webhook
        </Button>
      </Box>

      <Grid container spacing={3}>
        {webhookConfig?.endpoints?.map((webhook) => (
          <Grid item xs={12} key={webhook.id}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'start' }}>
                  <Box>
                    <Typography variant="h6">{webhook.name}</Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      {webhook.url}
                    </Typography>
                    
                    <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
                      {webhook.events.slice(0, 3).map(event => (
                        <Chip key={event} label={event} size="small" />
                      ))}
                      {webhook.events.length > 3 && (
                        <Chip label={`+${webhook.events.length - 3}`} size="small" />
                      )}
                    </Box>
                    
                    <Grid container spacing={2}>
                      <Grid item xs={3}>
                        <Typography variant="caption" color="text.secondary">状态</Typography>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Chip
                            label={webhook.active ? '活跃' : '暂停'}
                            color={webhook.active ? 'success' : 'default'}
                            size="small"
                          />
                        </Box>
                      </Grid>
                      <Grid item xs={3}>
                        <Typography variant="caption" color="text.secondary">成功率</Typography>
                        <Typography variant="body2">{webhook.success_rate}%</Typography>
                      </Grid>
                      <Grid item xs={3}>
                        <Typography variant="caption" color="text.secondary">最后触发</Typography>
                        <Typography variant="body2">{webhook.last_triggered || '-'}</Typography>
                      </Grid>
                      <Grid item xs={3}>
                        <Typography variant="caption" color="text.secondary">重试</Typography>
                        <Typography variant="body2">
                          {webhook.retry_enabled ? `最多${webhook.max_retries}次` : '禁用'}
                        </Typography>
                      </Grid>
                    </Grid>
                  </Box>
                  
                  <Box>
                    <Stack direction="row" spacing={1}>
                      <IconButton onClick={() => handleTestWebhook(webhook.id)}>
                        <PlayArrow />
                      </IconButton>
                      <IconButton onClick={() => {
                        setSelectedWebhook(webhook);
                        setWebhookEditDialog(true);
                      }}>
                        <Edit />
                      </IconButton>
                      <IconButton color="error">
                        <Delete />
                      </IconButton>
                      <Switch
                        checked={webhook.active}
                        onChange={(e) => {
                          setWebhookConfig(prev => ({
                            ...prev,
                            endpoints: prev.endpoints.map(w =>
                              w.id === webhook.id ? { ...w, active: e.target.checked } : w
                            )
                          }));
                          setHasUnsavedChanges(true);
                        }}
                      />
                    </Stack>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      <Divider sx={{ my: 3 }} />

      <Typography variant="h6" gutterBottom>全局设置</Typography>
      
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="subtitle1" gutterBottom>请求设置</Typography>
              <TextField
                fullWidth
                label="超时时间"
                type="number"
                value={webhookConfig?.global_settings?.timeout_seconds || 30}
                onChange={(e) => {
                  setWebhookConfig(prev => ({
                    ...prev,
                    global_settings: {
                      ...prev.global_settings,
                      timeout_seconds: parseInt(e.target.value)
                    }
                  }));
                  setHasUnsavedChanges(true);
                }}
                InputProps={{
                  endAdornment: <InputAdornment position="end">秒</InputAdornment>
                }}
                sx={{ mb: 2 }}
              />
              
              <TextField
                fullWidth
                label="最大负载大小"
                type="number"
                value={(webhookConfig?.global_settings?.max_payload_size || 5242880) / 1024 / 1024}
                onChange={(e) => {
                  setWebhookConfig(prev => ({
                    ...prev,
                    global_settings: {
                      ...prev.global_settings,
                      max_payload_size: parseFloat(e.target.value) * 1024 * 1024
                    }
                  }));
                  setHasUnsavedChanges(true);
                }}
                InputProps={{
                  endAdornment: <InputAdornment position="end">MB</InputAdornment>
                }}
              />
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="subtitle1" gutterBottom>安全设置</Typography>
              <TextField
                fullWidth
                label="签名密钥"
                type="password"
                value={webhookConfig?.global_settings?.signing_secret || ''}
                onChange={(e) => {
                  setWebhookConfig(prev => ({
                    ...prev,
                    global_settings: {
                      ...prev.global_settings,
                      signing_secret: e.target.value
                    }
                  }));
                  setHasUnsavedChanges(true);
                }}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton size="small">
                        <ContentCopy />
                      </IconButton>
                    </InputAdornment>
                  )
                }}
                helperText="用于验证webhook请求的签名"
              />
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );

  // Helper component
  const Avatar = ({ children, sx }) => (
    <Box
      sx={{
        width: 48,
        height: 48,
        borderRadius: '50%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        ...sx
      }}
    >
      {children}
    </Box>
  );

  return (
    <Container maxWidth={false} sx={{ py: 3 }}>
      <Box sx={{ mb: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box>
            <Typography variant="h4" gutterBottom>
              系统配置
            </Typography>
            <Typography variant="body1" color="text.secondary">
              管理系统级配置和业务规则
            </Typography>
          </Box>
          
          <Stack direction="row" spacing={2}>
            <Button
              variant="outlined"
              startIcon={<History />}
              onClick={() => setHistoryDialog(true)}
            >
              历史记录
            </Button>
            <Button
              variant="outlined"
              startIcon={<Download />}
              onClick={handleExportConfig}
            >
              导出配置
            </Button>
            <Button
              variant="outlined"
              startIcon={<Upload />}
              component="label"
            >
              导入配置
              <input
                type="file"
                hidden
                accept=".json"
                onChange={handleImportConfig}
              />
            </Button>
            <Button
              variant="contained"
              startIcon={saving ? <CircularProgress size={20} /> : <Save />}
              onClick={handleSaveConfig}
              disabled={!hasUnsavedChanges || saving}
            >
              保存更改
            </Button>
          </Stack>
        </Box>
      </Box>

      {hasUnsavedChanges && (
        <Alert severity="warning" sx={{ mb: 2 }}>
          您有未保存的更改，请记得保存
        </Alert>
      )}

      <Paper sx={{ mb: 2 }}>
        <Tabs
          value={activeTab}
          onChange={(e, newValue) => setActiveTab(newValue)}
          variant="scrollable"
          scrollButtons="auto"
        >
          <Tab icon={<Payment />} label="支付配置" />
          <Tab icon={<Subscriptions />} label="订阅配置" />
          <Tab icon={<Notifications />} label="通知配置" />
          <Tab icon={<Gavel />} label="业务规则" />
          <Tab icon={<Webhook />} label="Webhook配置" />
        </Tabs>
      </Paper>

      <Paper>
        {loading ? (
          <Box sx={{ p: 3, textAlign: 'center' }}>
            <CircularProgress />
          </Box>
        ) : (
          <>
            <TabPanel value={activeTab} index={0}>
              {renderPaymentConfig()}
            </TabPanel>
            <TabPanel value={activeTab} index={1}>
              {renderSubscriptionConfig()}
            </TabPanel>
            <TabPanel value={activeTab} index={2}>
              {renderNotificationConfig()}
            </TabPanel>
            <TabPanel value={activeTab} index={3}>
              {renderBusinessRules()}
            </TabPanel>
            <TabPanel value={activeTab} index={4}>
              {renderWebhookConfig()}
            </TabPanel>
          </>
        )}
      </Paper>

      {/* History dialog */}
      <Dialog
        open={historyDialog}
        onClose={() => setHistoryDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>配置历史</DialogTitle>
        <DialogContent>
          <List>
            {configHistory.map((history) => (
              <React.Fragment key={history.id}>
                <ListItem>
                  <ListItemIcon>
                    <History />
                  </ListItemIcon>
                  <ListItemText
                    primary={history.changes.join(', ')}
                    secondary={`${history.user} - ${history.timestamp} - v${history.version}`}
                  />
                  <ListItemSecondaryAction>
                    <IconButton size="small">
                      <Restore />
                    </IconButton>
                  </ListItemSecondaryAction>
                </ListItem>
                <Divider />
              </React.Fragment>
            ))}
          </List>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setHistoryDialog(false)}>关闭</Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default SystemConfig;