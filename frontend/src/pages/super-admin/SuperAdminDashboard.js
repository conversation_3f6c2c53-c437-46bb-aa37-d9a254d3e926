import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  Chip,
  Divider,
  Alert,
  Paper,
} from '@mui/material';
import {
  Dashboard,
  People,
  Security,
  Warning,
  Error,
  VpnKey,
  Assignment,
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';

function SuperAdminDashboard() {
  const { user } = useAuth();
  const navigate = useNavigate();






  const quickActions = [
    {
      title: '用户管理',
      description: '管理所有用户账户',
      icon: <People />,
      color: 'primary',
      path: '/super-admin/users',
    },
    {
      title: '角色管理',
      description: '配置系统角色',
      icon: <Security />,
      color: 'info',
      path: '/super-admin/roles',
    },
    {
      title: '角色权限管理',
      description: '管理角色与权限关联',
      icon: <Assignment />,
      color: 'success',
      path: '/super-admin/role-permissions',
    },
    {
      title: '权限管理',
      description: '配置系统权限',
      icon: <VpnKey />,
      color: 'warning',
      path: '/super-admin/permissions',
    },
  ];



  return (
    <Box sx={{ width: '100%', p: 3, backgroundColor: '#f5f5f5', minHeight: '100vh' }}>
      {/* 页面标题和操作按钮区域 */}
      <Box sx={{
        backgroundColor: '#fff',
        borderRadius: 3,
        border: '1px solid #e0e0e0',
        p: 4,
        mb: 3,
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
      }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Dashboard sx={{ fontSize: 32, color: '#1976d2' }} />
            <Box>
              <Typography variant="h4" sx={{ fontWeight: 700, color: '#1a1a1a', mb: 0.5 }}>
                超级管理员控制台
              </Typography>
              <Typography variant="body1" sx={{ color: '#666' }}>
                欢迎回来，{user?.name || user?.username}！系统核心管理中心
              </Typography>
            </Box>
          </Box>
        </Box>
      </Box>

      {/* 系统概览卡片 */}
      <Card sx={{
        borderRadius: 3,
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        mb: 3,
        backgroundColor: '#fff'
      }}>
        <CardContent sx={{ p: 3 }}>
          <Typography variant="h6" sx={{ fontWeight: 600, mb: 3, color: 'primary.main' }}>
            系统概览
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={6} sm={3}>
              <Card sx={{
                borderRadius: 3,
                boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                backgroundColor: '#fff',
                height: '100%'
              }}>
                <CardContent sx={{ textAlign: 'center', p: 2 }}>
                  <People sx={{ fontSize: 32, color: '#1976d2', mb: 1 }} />
                  <Typography variant="h4" sx={{ fontWeight: 700, mb: 0.5, color: '#1976d2' }}>
                    1,248
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    总用户数
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={6} sm={3}>
              <Card sx={{
                borderRadius: 3,
                boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                backgroundColor: '#fff',
                height: '100%'
              }}>
                <CardContent sx={{ textAlign: 'center', p: 2 }}>
                  <Security sx={{ fontSize: 32, color: '#4caf50', mb: 1 }} />
                  <Typography variant="h4" sx={{ fontWeight: 700, mb: 0.5, color: '#4caf50' }}>
                    8
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    系统角色
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={6} sm={3}>
              <Card sx={{
                borderRadius: 3,
                boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                backgroundColor: '#fff',
                height: '100%'
              }}>
                <CardContent sx={{ textAlign: 'center', p: 2 }}>
                  <VpnKey sx={{ fontSize: 32, color: '#ff9800', mb: 1 }} />
                  <Typography variant="h4" sx={{ fontWeight: 700, mb: 0.5, color: '#ff9800' }}>
                    24
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    系统权限
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={6} sm={3}>
              <Card sx={{
                borderRadius: 3,
                boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                backgroundColor: '#fff',
                height: '100%'
              }}>
                <CardContent sx={{ textAlign: 'center', p: 2 }}>
                  <Warning sx={{ fontSize: 32, color: '#f44336', mb: 1 }} />
                  <Typography variant="h4" sx={{ fontWeight: 700, mb: 0.5, color: '#f44336' }}>
                    3
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    系统警告
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* 快速操作卡片 */}
      <Card sx={{
        borderRadius: 3,
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        backgroundColor: '#fff'
      }}>
        <CardContent sx={{ p: 3 }}>
          <Typography variant="h6" sx={{ fontWeight: 600, mb: 3, color: 'primary.main' }}>
            快速操作
          </Typography>
            <Grid container spacing={3}>
              {quickActions.map((action, index) => (
                <Grid item xs={12} sm={6} md={3} key={index}>
                  <Paper
                    sx={{
                      p: 3,
                      borderRadius: 2,
                      backgroundColor: 'background.default',
                      cursor: 'pointer',
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        transform: 'translateY(-4px)',
                        boxShadow: '0 8px 24px rgba(0, 0, 0, 0.12)',
                      },
                    }}
                    onClick={() => navigate(action.path)}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <Box
                        sx={{
                          width: 48,
                          height: 48,
                          borderRadius: '50%',
                          backgroundColor: `${action.color}.main`,
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          mr: 2,
                        }}
                      >
                        {React.cloneElement(action.icon, { sx: { color: 'white', fontSize: 24 } })}
                      </Box>
                      <Box sx={{ flex: 1 }}>
                        <Typography variant="h6" sx={{ fontWeight: 600, mb: 0.5 }}>
                          {action.title}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {action.description}
                        </Typography>
                      </Box>
                    </Box>
                  </Paper>
                </Grid>
              ))}
            </Grid>
        </CardContent>
      </Card>


    </Box>
  );
}

export default SuperAdminDashboard;
