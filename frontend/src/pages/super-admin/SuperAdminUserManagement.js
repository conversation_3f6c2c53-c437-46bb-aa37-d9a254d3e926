import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Card,
  CardContent,
  Grid,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Avatar,
  Chip,
  IconButton,
  Button,
  TextField,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import {
  People,
  Search,
  FilterList,
  Add,
  Edit,
  Delete,
  Visibility,
  Business,
  Person,
  Group,
  Article,
  CheckCircle,
  Cancel,
  Refresh,
} from '@mui/icons-material';
import { CircularProgress } from '@mui/material';
import { useAuth } from '../../contexts/AuthContext';
import { ApiConfig } from '../../config/api-config';
import { AppConfig } from '../../config/app-config';

function UserManagement() {
  const { user } = useAuth();
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);

  const [searchTerm, setSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [selectedUser, setSelectedUser] = useState(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [availableRoles, setAvailableRoles] = useState([]);

  // 创建/编辑用户对话框状态
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [editingUser, setEditingUser] = useState(null);
  const [formData, setFormData] = useState({
    email: '',
    full_name: '',
    phone: '',
    password: '',
    role: 'regular_user',
    status: 'active',
    send_notification: true,
    force_password_reset: true
  });

  // 删除确认对话框状态
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [userToDelete, setUserToDelete] = useState(null);

  // 获取用户列表数据
  const fetchUsers = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem(`${AppConfig.storage.prefix}${AppConfig.storage.tokenKey}`);
      console.log('Token from localStorage:', token);

      const response = await fetch(`${ApiConfig.baseURL}/api/v1/users?page=1&size=100`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      console.log('API Response Status:', response.status);

      if (response.ok) {
        const data = await response.json();
        console.log('API Response Data:', data);

        // 转换API数据格式以匹配前端组件期望的格式
        const formattedUsers = data.data?.users?.map(apiUser => ({
          id: apiUser.id,
          name: apiUser.full_name || apiUser.email,
          email: apiUser.email,
          role: apiUser.roles && apiUser.roles.length > 0 ? apiUser.roles[0].role : 'regular_user',
          status: apiUser.status === 'active' ? 'active' : 'inactive',
          company: apiUser.company_name || '未设置',
          usageCount: apiUser.statistics?.total_orders || 0,
          lastLogin: apiUser.last_login_at ? new Date(apiUser.last_login_at).toLocaleString() : '从未登录',
          createdAt: apiUser.created_at ? new Date(apiUser.created_at).toLocaleDateString() : '未知',
        })) || [];

        console.log('Formatted Users:', formattedUsers);
        setUsers(formattedUsers);

        // 收集所有可用的角色
        const roles = new Set();
        data.data?.users?.forEach(user => {
          if (user.roles && user.roles.length > 0) {
            user.roles.forEach(roleInfo => {
              roles.add(roleInfo.role);
            });
          }
        });
        setAvailableRoles(Array.from(roles));
      } else {
        const errorData = await response.json().catch(() => ({ message: 'Unknown error' }));
        console.error('API Error Response:', errorData);
        console.error('Response status:', response.status);
        console.error('Response statusText:', response.statusText);
      }
    } catch (error) {
      console.error('获取用户数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 页面加载时获取数据
  useEffect(() => {
    fetchUsers();
  }, []);

  const filteredUsers = users.filter(user => {
    const matchesSearch = user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesRole = !roleFilter || user.role === roleFilter;
    const matchesStatus = !statusFilter || user.status === statusFilter;
    return matchesSearch && matchesRole && matchesStatus;
  });

  const getRoleLabel = (role) => {
    const roleMap = {
      super_admin: '超级管理员',
      admin: '管理员',
      enterprise_user: '企业用户',
      channel_user: '渠道商',
      agent_user: '代理商',
      regular_user: '普通用户',
      // 兼容旧的角色名称
      enterprise: '企业用户',
      agent: '代理商',
      channel: '内容提供商',
    };
    return roleMap[role] || role;
  };

  const getRoleColor = (role) => {
    const colorMap = {
      super_admin: 'error',
      admin: 'warning',
      enterprise_user: 'primary',
      channel_user: 'success',
      agent_user: 'info',
      regular_user: 'default',
      // 兼容旧的角色名称
      enterprise: 'primary',
      agent: 'success',
      channel: 'warning',
    };
    return colorMap[role] || 'default';
  };

  const getStatusColor = (status) => {
    const colorMap = {
      active: 'success',
      pending: 'warning',
      inactive: 'error',
    };
    return colorMap[status] || 'default';
  };

  const getStatusLabel = (status) => {
    const statusMap = {
      active: '活跃',
      pending: '待审核',
      inactive: '未激活',
      banned: '已禁用',
      deleted: '已删除',
    };
    return statusMap[status] || status;
  };

  const handleViewUser = (user) => {
    setSelectedUser(user);
    setDialogOpen(true);
  };

  const handleUserAction = (action, userId) => {
    if (action === 'edit') {
      const user = users.find(u => u.id === userId);
      if (user) {
        setEditingUser(user);
        setFormData({
          email: user.email,
          full_name: user.name,
          phone: user.phone || '',
          password: '',
          role: user.role,
          status: user.status,
          send_notification: false,
          force_password_reset: false
        });
        setEditDialogOpen(true);
      }
    } else if (action === 'delete') {
      const user = users.find(u => u.id === userId);
      if (user) {
        setUserToDelete(user);
        setDeleteDialogOpen(true);
      }
    } else {
      console.log(`执行操作: ${action} 用户ID: ${userId}`);
    }
  };

  // 删除用户
  const handleDeleteUser = async () => {
    if (!userToDelete) return;

    try {
      const token = localStorage.getItem(`${AppConfig.storage.prefix}${AppConfig.storage.tokenKey}`);

      console.log('删除用户:', userToDelete.id);

      const response = await fetch(`${ApiConfig.baseURL}/api/v1/users/${userToDelete.id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        console.log('用户删除成功:', data);
        setDeleteDialogOpen(false);
        setUserToDelete(null);
        fetchUsers(); // 刷新用户列表
        // 可以添加成功提示
      } else {
        const errorData = await response.json().catch(() => ({ message: 'Unknown error' }));
        console.error('删除用户失败:');
        console.error('响应状态:', response.status);
        console.error('响应状态文本:', response.statusText);
        console.error('错误详情:', errorData);

        // 显示具体的错误信息
        if (errorData.detail) {
          alert(`删除失败: ${errorData.detail}`);
        } else {
          alert('删除用户失败，请稍后重试');
        }
      }
    } catch (error) {
      console.error('删除用户请求失败:', error);
      alert('删除用户失败，请检查网络连接');
    }
  };

  // 创建用户
  const handleCreateUser = async () => {
    try {
      const token = localStorage.getItem(`${AppConfig.storage.prefix}${AppConfig.storage.tokenKey}`);

      // 准备发送的数据，过滤掉空值
      const createData = {
        email: formData.email,
        full_name: formData.full_name,
        role: formData.role,
        status: formData.status,
        send_notification: formData.send_notification,
        force_password_reset: formData.force_password_reset
      };

      // 只有在有值时才添加可选字段
      if (formData.phone && formData.phone.trim()) {
        createData.phone = formData.phone.trim();
      }

      if (formData.password && formData.password.trim()) {
        createData.password = formData.password.trim();
      }

      console.log('发送创建用户数据:', createData);

      const response = await fetch(`${ApiConfig.baseURL}/api/v1/users/create`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(createData),
      });

      if (response.ok) {
        const data = await response.json();
        console.log('用户创建成功:', data);
        setCreateDialogOpen(false);
        resetFormData();
        fetchUsers(); // 刷新用户列表
        // 可以添加成功提示
      } else {
        const errorData = await response.json().catch(() => ({ message: 'Unknown error' }));
        console.error('创建用户失败:');
        console.error('响应状态:', response.status);
        console.error('响应状态文本:', response.statusText);
        console.error('错误详情:', errorData);

        // 显示具体的错误信息
        if (errorData.detail) {
          if (Array.isArray(errorData.detail)) {
            console.error('验证错误:', errorData.detail);
            errorData.detail.forEach(err => {
              console.error(`字段 ${err.loc?.join('.')} 错误: ${err.msg}`);
            });
          } else {
            console.error('错误信息:', errorData.detail);
          }
        }
        // 可以添加错误提示
      }
    } catch (error) {
      console.error('创建用户请求失败:', error);
    }
  };

  // 更新用户状态
  const handleUpdateUser = async () => {
    try {
      const token = localStorage.getItem(`${AppConfig.storage.prefix}${AppConfig.storage.tokenKey}`);

      // 更新用户状态
      if (editingUser && formData.status !== editingUser.status) {
        const statusData = {
          status: formData.status,
          reason: '管理员更新用户状态',
          notify_user: false
        };

        const statusResponse = await fetch(`${ApiConfig.baseURL}/api/v1/users/${editingUser.id}/status`, {
          method: 'PUT',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(statusData),
        });

        if (!statusResponse.ok) {
          const errorData = await statusResponse.json();
          console.error('更新用户状态失败:', errorData);
          return;
        }
      }

      // 更新用户角色
      if (editingUser && formData.role !== editingUser.role) {
        const roleData = {
          roles: [formData.role],
          reason: '管理员更新用户角色',
          effective_immediately: true,
          notify_user: false
        };

        const roleResponse = await fetch(`${ApiConfig.baseURL}/api/v1/users/${editingUser.id}/roles`, {
          method: 'PUT',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(roleData),
        });

        if (!roleResponse.ok) {
          const errorData = await roleResponse.json();
          console.error('更新用户角色失败:', errorData);
          return;
        }
      }

      console.log('用户更新成功');
      setEditDialogOpen(false);
      resetFormData();
      fetchUsers(); // 刷新用户列表
      // 可以添加成功提示
    } catch (error) {
      console.error('更新用户请求失败:', error);
    }
  };

  // 重置表单数据
  const resetFormData = () => {
    setFormData({
      email: '',
      full_name: '',
      phone: '',
      password: '',
      role: 'regular_user',
      status: 'active',
      send_notification: true,
      force_password_reset: true
    });
    setEditingUser(null);
  };

  // 处理表单输入变化
  const handleFormChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <Box sx={{ width: '100%', p: 3, backgroundColor: '#f5f5f5', minHeight: '100vh' }}>
      {/* Header Section */}
      <Box sx={{ mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
          <Box>
            <Typography variant="h4" sx={{ fontWeight: 700, mb: 1, color: 'text.primary' }}>
              用户管理
            </Typography>
            <Typography variant="body1" color="text.secondary" sx={{ fontSize: '1.1rem' }}>
              管理所有用户账户、角色权限和用户状态
            </Typography>
          </Box>

        </Box>
      </Box>

      {/* 搜索和筛选区域 */}
          <Box sx={{ mb: 4 }}>
            <Paper
              sx={{
                p: 3,
                borderRadius: 3,
                border: '1px solid',
                borderColor: 'divider',
                boxShadow: '0 2px 8px rgba(0, 0, 0, 0.04)',
              }}
            >
              <Typography variant="h6" sx={{ fontWeight: 600, mb: 3, color: 'primary.main' }}>
                搜索和筛选
              </Typography>
              <Grid container spacing={3} alignItems="center">
                <Grid item xs={12} md={4}>
                  <TextField
                    fullWidth
                    placeholder="搜索用户名或邮箱..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    sx={{
                      borderRadius: 2,
                      backgroundColor: 'background.paper',
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 2,
                        transition: 'all 0.3s ease',
                        '&:hover .MuiOutlinedInput-notchedOutline': {
                          borderColor: 'primary.light',
                        },
                        '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                          borderColor: 'primary.main',
                          borderWidth: 2,
                          boxShadow: '0 0 0 3px rgba(25, 118, 210, 0.1)',
                        },
                      },
                    }}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Search sx={{ color: 'text.secondary' }} />
                        </InputAdornment>
                      ),
                    }}
                  />
                </Grid>
                <Grid item xs={12} md={3}>
                  <FormControl fullWidth>
                    <InputLabel
                      shrink={true}
                      sx={{
                        color: 'text.secondary',
                        '&.Mui-focused': {
                          color: 'primary.main',
                        }
                      }}
                    >
                      角色筛选
                    </InputLabel>
                    <Select
                      value={roleFilter}
                      label="角色筛选"
                      onChange={(e) => setRoleFilter(e.target.value)}
                      sx={{
                        borderRadius: 2,
                        backgroundColor: 'background.paper',
                        transition: 'all 0.3s ease',
                        '&:hover .MuiOutlinedInput-notchedOutline': {
                          borderColor: 'primary.light',
                        },
                        '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                          borderColor: 'primary.main',
                          borderWidth: 2,
                          boxShadow: '0 0 0 3px rgba(25, 118, 210, 0.1)',
                        },
                        '& .MuiSelect-select': {
                          minWidth: '120px',
                          display: 'flex',
                          alignItems: 'center',
                          gap: 1,
                        },
                      }}
                    >
                      <MenuItem value="">全部角色</MenuItem>
                      {availableRoles.map(role => (
                        <MenuItem key={role} value={role}>
                          {getRoleLabel(role)}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} md={3}>
                  <FormControl fullWidth>
                    <InputLabel
                      shrink={true}
                      sx={{
                        color: 'text.secondary',
                        '&.Mui-focused': {
                          color: 'primary.main',
                        }
                      }}
                    >
                      状态筛选
                    </InputLabel>
                    <Select
                      value={statusFilter}
                      label="状态筛选"
                      onChange={(e) => setStatusFilter(e.target.value)}
                      sx={{
                        borderRadius: 2,
                        backgroundColor: 'background.paper',
                        transition: 'all 0.3s ease',
                        '&:hover .MuiOutlinedInput-notchedOutline': {
                          borderColor: 'primary.light',
                        },
                        '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                          borderColor: 'primary.main',
                          borderWidth: 2,
                          boxShadow: '0 0 0 3px rgba(25, 118, 210, 0.1)',
                        },
                        '& .MuiSelect-select': {
                          minWidth: '120px',
                          display: 'flex',
                          alignItems: 'center',
                          gap: 1,
                        },
                      }}
                    >
                      <MenuItem value="">全部状态</MenuItem>
                      <MenuItem value="active">活跃</MenuItem>
                      <MenuItem value="inactive">未激活</MenuItem>
                      <MenuItem value="banned">已禁用</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} md={1.5}>
                  <Button
                    fullWidth
                    variant="contained"
                    startIcon={<Add />}
                    onClick={() => {
                      resetFormData();
                      setCreateDialogOpen(true);
                    }}
                    sx={{
                      height: 56,
                      borderRadius: 2,
                      fontWeight: 600,
                      textTransform: 'none',
                      boxShadow: '0 4px 12px rgba(25, 118, 210, 0.3)',
                      '&:hover': {
                        boxShadow: '0 6px 16px rgba(25, 118, 210, 0.4)',
                        transform: 'translateY(-1px)',
                      },
                      transition: 'all 0.3s ease',
                    }}
                  >
                    添加用户
                  </Button>
                </Grid>
                <Grid item xs={12} md={0.5}>
                  <Tooltip title="刷新数据">
                    <IconButton
                      onClick={fetchUsers}
                      disabled={loading}
                      sx={{
                        height: 56,
                        width: 56,
                        backgroundColor: 'primary.light',
                        color: 'primary.main',
                        borderRadius: 2,
                        transition: 'all 0.3s ease',
                        boxShadow: '0 4px 12px rgba(25, 118, 210, 0.2)',
                        '&:hover': {
                          backgroundColor: 'primary.main',
                          color: 'white',
                          transform: 'translateY(-1px)',
                          boxShadow: '0 6px 16px rgba(25, 118, 210, 0.3)',
                        },
                      }}
                    >
                      <Refresh />
                    </IconButton>
                  </Tooltip>
                </Grid>
              </Grid>
            </Paper>
          </Box>

          {/* 用户列表 */}
          <Box>
            <Typography variant="h6" sx={{ fontWeight: 600, mb: 3, color: 'primary.main' }}>
              用户列表 ({filteredUsers.length})
            </Typography>
            <TableContainer component={Paper} variant="outlined">
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>用户</TableCell>
                    <TableCell>角色</TableCell>
                    <TableCell>状态</TableCell>
                    <TableCell>公司</TableCell>
                    <TableCell>使用次数</TableCell>
                    <TableCell>最后登录</TableCell>
                    <TableCell>操作</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {loading ? (
                    <TableRow>
                      <TableCell colSpan={7} sx={{ textAlign: 'center', py: 8 }}>
                        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 2 }}>
                          <CircularProgress size={40} />
                          <Typography variant="body1" color="text.secondary">
                            正在加载用户数据...
                          </Typography>
                        </Box>
                      </TableCell>
                    </TableRow>
                  ) : filteredUsers.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={7} sx={{ textAlign: 'center', py: 8 }}>
                        <Typography variant="body1" color="text.secondary">
                          暂无用户数据
                        </Typography>
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredUsers.map((user) => (
                      <TableRow key={user.id} hover>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Avatar sx={{ width: 40, height: 40, mr: 2 }}>
                            {user.name.charAt(0)}
                          </Avatar>
                          <Box>
                            <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                              {user.name}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              {user.email}
                            </Typography>
                          </Box>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={getRoleLabel(user.role)}
                          color={getRoleColor(user.role)}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={getStatusLabel(user.status)}
                          color={getStatusColor(user.status)}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>{user.company}</TableCell>
                      <TableCell>{user.usageCount}</TableCell>
                      <TableCell>{user.lastLogin}</TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', gap: 0.5 }}>
                          <IconButton
                            size="small"
                            onClick={() => handleViewUser(user)}
                            color="primary"
                          >
                            <Visibility fontSize="small" />
                          </IconButton>
                          <IconButton
                            size="small"
                            onClick={() => handleUserAction('edit', user.id)}
                            color="info"
                          >
                            <Edit fontSize="small" />
                          </IconButton>
                          <IconButton
                            size="small"
                            onClick={() => handleUserAction('delete', user.id)}
                            color="error"
                          >
                            <Delete fontSize="small" />
                          </IconButton>
                        </Box>
                      </TableCell>
                    </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </Box>

      {/* 用户详情对话框 */}
      <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>用户详情</DialogTitle>
        <DialogContent>
          {selectedUser && (
            <Box sx={{ pt: 2 }}>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary">用户名:</Typography>
                  <Typography variant="body1">{selectedUser.name}</Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary">邮箱:</Typography>
                  <Typography variant="body1">{selectedUser.email}</Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary">角色:</Typography>
                  <Typography variant="body1">{getRoleLabel(selectedUser.role)}</Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary">状态:</Typography>
                  <Typography variant="body1">{getStatusLabel(selectedUser.status)}</Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary">公司:</Typography>
                  <Typography variant="body1">{selectedUser.company}</Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary">使用次数:</Typography>
                  <Typography variant="body1">{selectedUser.usageCount}</Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary">最后登录:</Typography>
                  <Typography variant="body1">{selectedUser.lastLogin}</Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary">注册时间:</Typography>
                  <Typography variant="body1">{selectedUser.createdAt}</Typography>
                </Grid>
              </Grid>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDialogOpen(false)}>关闭</Button>
        </DialogActions>
      </Dialog>

      {/* 创建用户对话框 */}
      <Dialog open={createDialogOpen} onClose={() => setCreateDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>创建用户</DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="邮箱地址"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleFormChange('email', e.target.value)}
                  required
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="真实姓名"
                  value={formData.full_name}
                  onChange={(e) => handleFormChange('full_name', e.target.value)}
                  required
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="手机号"
                  value={formData.phone}
                  onChange={(e) => handleFormChange('phone', e.target.value)}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="密码"
                  type="password"
                  value={formData.password}
                  onChange={(e) => handleFormChange('password', e.target.value)}
                  helperText="留空则系统自动生成"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>用户角色</InputLabel>
                  <Select
                    value={formData.role}
                    label="用户角色"
                    onChange={(e) => handleFormChange('role', e.target.value)}
                  >
                    <MenuItem value="regular_user">普通用户</MenuItem>
                    <MenuItem value="admin">管理员</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>用户状态</InputLabel>
                  <Select
                    value={formData.status}
                    label="用户状态"
                    onChange={(e) => handleFormChange('status', e.target.value)}
                  >
                    <MenuItem value="active">活跃</MenuItem>
                    <MenuItem value="inactive">未激活</MenuItem>
                    <MenuItem value="banned">已禁用</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCreateDialogOpen(false)}>取消</Button>
          <Button onClick={handleCreateUser} variant="contained">创建用户</Button>
        </DialogActions>
      </Dialog>

      {/* 编辑用户对话框 */}
      <Dialog open={editDialogOpen} onClose={() => setEditDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>编辑用户</DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="邮箱地址"
                  type="email"
                  value={formData.email}
                  disabled
                  helperText="邮箱地址不可修改"
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="真实姓名"
                  value={formData.full_name}
                  disabled
                  helperText="姓名信息不可修改"
                />
              </Grid>
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <InputLabel>用户角色</InputLabel>
                  <Select
                    value={formData.role}
                    label="用户角色"
                    onChange={(e) => handleFormChange('role', e.target.value)}
                  >
                    <MenuItem value="regular_user">普通用户</MenuItem>
                    <MenuItem value="admin">管理员</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <InputLabel>用户状态</InputLabel>
                  <Select
                    value={formData.status}
                    label="用户状态"
                    onChange={(e) => handleFormChange('status', e.target.value)}
                  >
                    <MenuItem value="active">活跃</MenuItem>
                    <MenuItem value="inactive">未激活</MenuItem>
                    <MenuItem value="banned">已禁用</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditDialogOpen(false)}>取消</Button>
          <Button onClick={handleUpdateUser} variant="contained">更新用户</Button>
        </DialogActions>
      </Dialog>

      {/* 删除确认对话框 */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle sx={{ color: 'error.main' }}>确认删除用户</DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            {userToDelete && (
              <>
                <Typography variant="body1" sx={{ mb: 2 }}>
                  您确定要删除以下用户吗？此操作不可撤销。
                </Typography>
                <Box sx={{
                  p: 2,
                  backgroundColor: 'grey.50',
                  borderRadius: 1,
                  border: '1px solid',
                  borderColor: 'grey.200'
                }}>
                  <Typography variant="body2" color="text.secondary">用户信息：</Typography>
                  <Typography variant="body1" sx={{ fontWeight: 600 }}>
                    {userToDelete.name}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {userToDelete.email}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    角色：{getRoleLabel(userToDelete.role)}
                  </Typography>
                </Box>
              </>
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => {
            setDeleteDialogOpen(false);
            setUserToDelete(null);
          }}>
            取消
          </Button>
          <Button
            onClick={handleDeleteUser}
            variant="contained"
            color="error"
            sx={{ ml: 1 }}
          >
            确认删除
          </Button>
        </DialogActions>
      </Dialog>


    </Box>
  );
}

export default UserManagement;
