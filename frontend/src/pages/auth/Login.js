import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Paper,
  TextField,
  Button,
  Typography,
  Link,
  Alert,
  CircularProgress,
  Divider,
  IconButton,
  InputAdornment,
  Grid,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  FormControlLabel,
  Checkbox,
} from '@mui/material';
import {
  Email,
  Lock,
  CheckCircle,
  Speed,
  Security,
  TrendingUp,
} from '@mui/icons-material';
import { useNavigate, useLocation, Link as RouterLink } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import Footer from '../../components/layout/Footer';

function Login() {
  const navigate = useNavigate();
  const location = useLocation();
  const { login, logout, error, isLoading, clearError, user } = useAuth();

  const [formData, setFormData] = useState({
    email: location.state?.email || '',
    password: '',
  });

  const [formErrors, setFormErrors] = useState({});
  const [successMessage, setSuccessMessage] = useState(location.state?.message || '');

  // Get redirect path from location state
  const from = location.state?.from?.pathname || '/dashboard';

  // Auto-hide error after 3 seconds
  useEffect(() => {
    if (error) {
      const timer = setTimeout(() => {
        clearError();
      }, 3000);

      return () => clearTimeout(timer);
    }
  }, [error, clearError]);

  // Auto-hide success message after 5 seconds
  useEffect(() => {
    if (successMessage) {
      const timer = setTimeout(() => {
        setSuccessMessage('');
      }, 5000);

      return () => clearTimeout(timer);
    }
  }, [successMessage]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));

    // Clear field error when user starts typing
    if (formErrors[name]) {
      setFormErrors(prev => ({
        ...prev,
        [name]: '',
      }));
    }

    // Clear auth error
    if (error) {
      clearError();
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !isLoading) {
      e.preventDefault();
      e.stopPropagation();
      handleSubmit();
    }
  };



  const validateForm = () => {
    const errors = {};

    if (!formData.email.trim()) {
      errors.email = '请输入邮箱地址';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = '请输入有效的邮箱地址';
    }

    if (!formData.password) {
      errors.password = '请输入密码';
    } else if (formData.password.length < 6) {
      errors.password = '密码至少需要6位字符';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    // Clear any previous errors
    if (error) {
      clearError();
    }

    try {
      // Login and get the result
      const loginResult = await login(formData);
      
      // Check if the logged in user has admin or super_admin role
      if (loginResult && loginResult.user && loginResult.user.role) {
        const userRole = loginResult.user.role;
        
        // Check if user is admin or super_admin
        if (userRole === 'admin' || userRole === 'super_admin') {
          // Immediately logout the admin/super_admin user
          logout();
          
          // Show error message
          setFormErrors({ 
            email: '登录失败，请检查您的账户信息' 
          });
          
          // Also set a general error message
          setSuccessMessage('');
          
          // Add an alert for better visibility
          setTimeout(() => {
            alert('管理员账户不能从此处登录，请使用管理后台入口。\n\n如需访问管理后台，请联系系统管理员获取正确的登录地址。');
          }, 100);
          
          return;
        }
      }
      
      // Only navigate if login is successful and not admin/super_admin
      navigate(from, { replace: true });
    } catch (err) {
      // Error is handled by the auth context, don't navigate
      console.error('Login failed:', err);
      // Stay on the login page to show the error
    }
  };



  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', minHeight: '100vh' }}>
      {/* Main Content */}
      <Box
        sx={{
          flex: 1,
          display: 'flex',
          alignItems: 'center',
          bgcolor: 'grey.50',
          py: 8,
        }}
      >
        <Container maxWidth="xl">
          <Grid container spacing={6} alignItems="center">
            {/* Left Side - Login Description */}
            <Grid size={{ xs: 12, md: 6 }}>
              <Box sx={{ pr: { md: 4 } }}>
                <Box sx={{ mb: 3 }}>
                  <Typography variant="h4" sx={{ fontWeight: 700, color: 'text.primary' }}>
                    AI搜索优化平台
                  </Typography>
                </Box>
                
                <Typography variant="h5" sx={{ fontWeight: 600, mb: 3, color: 'text.primary' }}>
                  用户控制中心
                </Typography>
                
                <Typography variant="body1" sx={{ mb: 4, color: 'text.secondary', lineHeight: 1.8 }}>
                  登录您的用户账户，访问个人控制中心，管理和优化您的内容在AI搜索中的表现。此登录入口专为普通用户设计。
                </Typography>

                <List sx={{ mb: 4 }}>
                  <ListItem sx={{ px: 0 }}>
                    <ListItemIcon>
                      <CheckCircle sx={{ color: 'success.main' }} />
                    </ListItemIcon>
                    <ListItemText 
                      primary="智能内容分析"
                      secondary="实时分析您的内容在AI搜索中的表现"
                    />
                  </ListItem>
                  <ListItem sx={{ px: 0 }}>
                    <ListItemIcon>
                      <Speed sx={{ color: 'primary.main' }} />
                    </ListItemIcon>
                    <ListItemText 
                      primary="快速优化建议"
                      secondary="基于AI算法提供精准的优化方案"
                    />
                  </ListItem>
                  <ListItem sx={{ px: 0 }}>
                    <ListItemIcon>
                      <Security sx={{ color: 'info.main' }} />
                    </ListItemIcon>
                    <ListItemText 
                      primary="数据安全保障"
                      secondary="企业级安全标准，保护您的数据隐私"
                    />
                  </ListItem>
                  <ListItem sx={{ px: 0 }}>
                    <ListItemIcon>
                      <TrendingUp sx={{ color: 'warning.main' }} />
                    </ListItemIcon>
                    <ListItemText 
                      primary="持续跟踪改进"
                      secondary="监控排名变化，持续优化内容策略"
                    />
                  </ListItem>
                </List>

                <Box sx={{ 
                  p: 3, 
                  bgcolor: 'primary.50',
                  borderRadius: 2,
                  border: '1px solid',
                  borderColor: 'primary.100'
                }}>
                  <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 1, color: 'primary.main' }}>
                    💡 温馨提示
                  </Typography>
                  <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                    如果您是新用户，建议先注册账号体验我们的免费试用服务，了解AI搜索优化如何帮助您提升内容影响力。
                  </Typography>
                </Box>
              </Box>
            </Grid>

            {/* Right Side - Login Form */}
            <Grid size={{ xs: 12, md: 6 }}>
              <Paper
                elevation={0}
                sx={{
                  p: 6,
                  borderRadius: 3,
                  maxWidth: 500,
                  mx: 'auto',
                  bgcolor: 'background.paper',
                  border: '1px solid',
                  borderColor: 'divider',
                  position: 'relative',
                  overflow: 'hidden',
                  '&::before': {
                    content: '""',
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    height: '3px',
                    background: 'linear-gradient(90deg, #1e293b, #475569, #64748b)',
                  },
                }}
              >
                {/* Decorative background pattern */}
                <Box
                  sx={{
                    position: 'absolute',
                    top: -100,
                    right: -100,
                    width: 200,
                    height: 200,
                    borderRadius: '50%',
                    background: 'radial-gradient(circle, rgba(30, 41, 59, 0.03) 0%, transparent 70%)',
                    pointerEvents: 'none',
                  }}
                />
                <Box
                  sx={{
                    position: 'absolute',
                    bottom: -50,
                    left: -50,
                    width: 150,
                    height: 150,
                    borderRadius: '50%',
                    background: 'radial-gradient(circle, rgba(71, 85, 105, 0.03) 0%, transparent 70%)',
                    pointerEvents: 'none',
                  }}
                />

                {/* Header */}
                <Box sx={{ textAlign: 'center', mb: 5, position: 'relative', zIndex: 1 }}>
                  <Box
                    sx={{
                      width: 80,
                      height: 80,
                      borderRadius: '50%',
                      background: 'linear-gradient(135deg, #1e293b 0%, #475569 100%)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      mx: 'auto',
                      mb: 3,
                      boxShadow: '0 10px 30px rgba(30, 41, 59, 0.2)',
                    }}
                  >
                    <Lock sx={{ fontSize: 40, color: 'white' }} />
                  </Box>
                  <Typography variant="h4" sx={{ fontWeight: 800, mb: 1, color: '#1e293b' }}>
                    欢迎回来
                  </Typography>
                  <Typography variant="body1" sx={{ color: '#64748b' }}>
                    登录您的账户，管理AI搜索优化服务
                  </Typography>
                </Box>


                {/* Success Alert */}
                {successMessage && (
                  <Alert
                    severity="success"
                    sx={{
                      mb: 3,
                      justifyContent: 'center',
                    }}
                  >
                    {successMessage}
                  </Alert>
                )}

                {/* Error Alert */}
                {error && (
                  <Alert
                    severity="error"
                    sx={{
                      mb: 3,
                      justifyContent: 'center',
                    }}
                  >
                    {error}
                  </Alert>
                )}

                {/* Login Form */}
                <Box sx={{ position: 'relative', zIndex: 1 }}>
                  <TextField
                    fullWidth
                    name="email"
                    label="邮箱地址"
                    placeholder="<EMAIL>"
                    type="email"
                    value={formData.email}
                    onChange={handleChange}
                    onKeyPress={handleKeyPress}
                    error={!!formErrors.email}
                    helperText={formErrors.email}
                    disabled={isLoading}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Email sx={{ color: '#64748b' }} />
                        </InputAdornment>
                      ),
                    }}
                    sx={{
                      mb: 3,
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 2,
                        backgroundColor: 'rgba(248, 250, 252, 0.5)',
                        '&:hover fieldset': {
                          borderColor: '#475569',
                        },
                        '&.Mui-focused fieldset': {
                          borderColor: '#1e293b',
                          borderWidth: 2,
                        },
                      },
                      '& .MuiInputLabel-root.Mui-focused': {
                        color: '#1e293b',
                      },
                    }}
                  />

                  <TextField
                    fullWidth
                    name="password"
                    label="密码"
                    placeholder="输入您的密码"
                    type="password"
                    value={formData.password}
                    onChange={handleChange}
                    onKeyPress={handleKeyPress}
                    error={!!formErrors.password}
                    helperText={formErrors.password}
                    disabled={isLoading}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Lock sx={{ color: '#64748b' }} />
                        </InputAdornment>
                      ),
                    }}
                    sx={{
                      mb: 1,
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 2,
                        backgroundColor: 'rgba(248, 250, 252, 0.5)',
                        '&:hover fieldset': {
                          borderColor: '#475569',
                        },
                        '&.Mui-focused fieldset': {
                          borderColor: '#1e293b',
                          borderWidth: 2,
                        },
                      },
                      '& .MuiInputLabel-root.Mui-focused': {
                        color: '#1e293b',
                      },
                    }}
                  />

                  {/* Remember me and Forgot password row */}
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                    <FormControlLabel
                      control={
                        <Checkbox
                          size="small"
                          sx={{
                            color: '#64748b',
                            '&.Mui-checked': {
                              color: '#1e293b',
                            },
                          }}
                        />
                      }
                      label={
                        <Typography variant="body2" sx={{ color: '#64748b' }}>
                          记住我
                        </Typography>
                      }
                    />
                    <Link
                      component={RouterLink}
                      to="/auth/forgot-password"
                      variant="body2"
                      sx={{
                        textDecoration: 'none',
                        color: '#475569',
                        fontWeight: 500,
                        '&:hover': {
                          textDecoration: 'underline',
                          color: '#1e293b',
                        },
                      }}
                    >
                      忘记密码？
                    </Link>
                  </Box>

                  <Button
                    fullWidth
                    type="button"
                    variant="contained"
                    size="large"
                    disabled={isLoading}
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      handleSubmit();
                    }}
                    sx={{
                      mb: 3,
                      py: 1.8,
                      fontSize: '1.05rem',
                      fontWeight: 700,
                      height: 56,
                      borderRadius: 3,
                      background: isLoading 
                        ? '#94a3b8'
                        : 'linear-gradient(135deg, #1e293b 0%, #334155 100%)',
                      boxShadow: isLoading 
                        ? 'none'
                        : '0 4px 15px rgba(30, 41, 59, 0.25)',
                      textTransform: 'none',
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        background: isLoading
                          ? '#94a3b8'
                          : 'linear-gradient(135deg, #0f172a 0%, #1e293b 100%)',
                        boxShadow: isLoading
                          ? 'none'
                          : '0 6px 20px rgba(30, 41, 59, 0.35)',
                        transform: isLoading ? 'none' : 'translateY(-2px)',
                      },
                    }}
                  >
                    {isLoading ? (
                      <CircularProgress size={24} color="inherit" />
                    ) : (
                      '登录账户'
                    )}
                  </Button>

                  {/* Divider */}
                  <Box sx={{ position: 'relative', my: 4 }}>
                    <Divider>
                      <Typography 
                        variant="body2" 
                        sx={{ 
                          color: '#94a3b8',
                          px: 2,
                          bgcolor: 'transparent',
                        }}
                      >
                        或者
                      </Typography>
                    </Divider>
                  </Box>
                  
                  {/* Register Link */}
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="body1" sx={{ color: '#64748b', mb: 1 }}>
                      还没有账户？
                    </Typography>
                    <Link
                      component={RouterLink}
                      to="/auth/register"
                      sx={{
                        display: 'inline-block',
                        textDecoration: 'none',
                        color: '#1e293b',
                        fontWeight: 600,
                        fontSize: '1rem',
                        padding: '12px 32px',
                        border: '2px solid #1e293b',
                        borderRadius: 3,
                        transition: 'all 0.3s ease',
                        '&:hover': {
                          backgroundColor: '#1e293b',
                          color: 'white',
                          transform: 'translateY(-2px)',
                          boxShadow: '0 4px 12px rgba(30, 41, 59, 0.25)',
                        },
                      }}
                    >
                      创建新账户
                    </Link>
                  </Box>
                </Box>
              </Paper>
            </Grid>
          </Grid>
        </Container>
      </Box>

      {/* Footer */}
      <Footer />
    </Box>
  );
}

export default Login;
