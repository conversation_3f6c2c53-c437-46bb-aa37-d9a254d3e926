import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Paper,
  TextField,
  Button,
  Typography,
  Link,
  Alert,
  CircularProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  IconButton,
  InputAdornment,
  Checkbox,
  FormControlLabel,
  Grid,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
} from '@mui/material';
import {
  Person,
  Email,
  Lock,
  Phone,
  Send,
  Rocket,
  Shield,
  AutoAwesome,
  Groups,
} from '@mui/icons-material';
import { useNavigate, Link as RouterLink } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import apiService from '../../services/api';
import { handleReferralVisit, getReferralFromStorage } from '../../utils/referralUtils';
import { AppConfig } from '../../config/app-config';
import Footer from '../../components/layout/Footer';

function Register() {
  const navigate = useNavigate();
  const { register, error, isLoading, clearError } = useAuth();

  const [formData, setFormData] = useState({
    email: '',
    phone: '',
    password: '',
    confirmPassword: '',
    realName: '',
    referralCode: '',
    emailVerificationCode: '',
    agreeToTerms: false,
  });

  const [formErrors, setFormErrors] = useState({});
  const [isCodeSending, setIsCodeSending] = useState(false);
  const [codeCountdown, setCodeCountdown] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // 处理URL参数，自动填入推荐码
  useEffect(() => {
    // 处理推广链接访问
    const referralParams = handleReferralVisit('auth/register');

    if (referralParams.ref) {
      setFormData(prev => ({
        ...prev,
        referralCode: referralParams.ref
      }));
    } else {
      // 如果URL中没有推荐码，尝试从localStorage获取
      const storedReferral = getReferralFromStorage();
      if (storedReferral.ref) {
        setFormData(prev => ({
          ...prev,
          referralCode: storedReferral.ref
        }));
      }
    }
  }, []);

  // 发送邮箱验证码
  const handleSendVerificationCode = async () => {
    if (!formData.email) {
      setFormErrors({ ...formErrors, email: '请先输入邮箱地址' });
      return;
    }

    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      setFormErrors({ ...formErrors, email: '请输入有效的邮箱地址' });
      return;
    }

    setIsCodeSending(true);
    try {
      // 调用发送验证码的API
      await apiService.sendVerificationCode(formData.email, 'register');

      // 发送成功，开始倒计时
      setCodeCountdown(60);
      const timer = setInterval(() => {
        setCodeCountdown((prev) => {
          if (prev <= 1) {
            clearInterval(timer);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

      // 清除邮箱错误信息
      setFormErrors({ ...formErrors, email: '' });
    } catch (error) {
      // 显示错误信息
      const errorMessage = error.response?.data?.detail || '发送验证码失败，请稍后重试';
      setFormErrors({ ...formErrors, email: errorMessage });
    } finally {
      setIsCodeSending(false);
    }
  };

  const handleChange = (e) => {
    const { name, value, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'agreeToTerms' ? checked : value,
    }));
    
    // Clear field error when user starts typing
    if (formErrors[name]) {
      setFormErrors(prev => ({
        ...prev,
        [name]: '',
      }));
    }
    
    // Clear auth error
    if (error) {
      clearError();
    }
  };



  const validateForm = () => {
    const errors = {};

    // Email validation
    if (!formData.email.trim()) {
      errors.email = '请输入邮箱地址';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = '请输入有效的邮箱地址';
    }

    // Phone validation
    if (!formData.phone.trim()) {
      errors.phone = '请输入手机号码';
    } else if (!/^1[3-9]\d{9}$/.test(formData.phone)) {
      errors.phone = '请输入有效的手机号码';
    }

    // Password validation
    if (!formData.password) {
      errors.password = '请输入密码';
    } else if (formData.password.length < 8 || formData.password.length > 20) {
      errors.password = '密码长度必须在8-20位之间';
    } else if (!/(?=.*[a-zA-Z])(?=.*\d)/.test(formData.password)) {
      errors.password = '密码必须包含字母和数字';
    }

    // Confirm password validation
    if (!formData.confirmPassword) {
      errors.confirmPassword = '请再次输入密码';
    } else if (formData.password !== formData.confirmPassword) {
      errors.confirmPassword = '两次输入的密码不一致';
    }

    // Real name validation
    if (!formData.realName.trim()) {
      errors.realName = '请输入您的真实姓名';
    }

    // Email verification code validation
    if (!formData.emailVerificationCode.trim()) {
      errors.emailVerificationCode = '请输入验证码';
    }

    // Terms agreement validation
    if (!formData.agreeToTerms) {
      errors.agreeToTerms = '请同意服务条款和隐私政策';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    // 清除之前的错误
    clearError();
    setFormErrors({});

    setIsSubmitting(true);
    try {
      // 直接调用注册API，让后端验证验证码
      const registerData = {
        email: formData.email,
        password: formData.password,
        full_name: formData.realName,
        phone: formData.phone || null,
        referral_code: formData.referralCode || null,
        verification_code: formData.emailVerificationCode
      };

      // 调用注册API
      const registerResponse = await apiService.register(registerData);

      if (registerResponse.success) {
        // 注册成功后跳转到登录页面
        navigate('/auth/login', {
          state: {
            message: '注册成功！请使用您的邮箱和密码登录',
            email: formData.email
          }
        });
      }
    } catch (err) {
      // 显示具体的错误信息
      const errorMessage = err.response?.data?.detail || err.message || '注册失败，请稍后重试';

      // 如果是验证码相关的错误，显示在验证码字段下
      if (errorMessage.includes('验证码')) {
        setFormErrors({
          ...formErrors,
          emailVerificationCode: errorMessage,
          general: ''
        });
      } else {
        // 其他错误显示在顶部
        setFormErrors({
          ...formErrors,
          emailVerificationCode: '',
          general: errorMessage
        });
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', minHeight: '100vh' }}>
      {/* Main Content */}
      <Box
        sx={{
          flex: 1,
          display: 'flex',
          alignItems: 'center',
          bgcolor: 'grey.50',
          py: 8,
        }}
      >
        <Container maxWidth="xl">
          <Grid container spacing={6} alignItems="flex-start">
            {/* Left Side - Register Description */}
            <Grid item xs={12} md={5}>
              <Box sx={{ pr: { md: 4 }, pt: { md: 5 } }}>
                <Typography variant="h4" sx={{ fontWeight: 700, color: 'text.primary', mb: 3 }}>
                  AI搜索优化平台
                </Typography>
                
                <Typography variant="h5" sx={{ fontWeight: 600, mb: 3, color: 'text.primary' }}>
                  开启AI搜索优化之旅
                </Typography>
                
                <Typography variant="body1" sx={{ mb: 4, color: 'text.secondary', lineHeight: 1.8 }}>
                  注册账户，立即体验专业的AI搜索优化服务。我们帮助您的内容在各大AI平台获得更高的曝光和引用。
                </Typography>

                <List sx={{ mb: 4 }}>
                  <ListItem sx={{ px: 0 }}>
                    <ListItemIcon>
                      <Rocket sx={{ color: 'primary.main' }} />
                    </ListItemIcon>
                    <ListItemText 
                      primary="免费试用"
                      secondary="新用户可免费体验所有功能30天"
                    />
                  </ListItem>
                  <ListItem sx={{ px: 0 }}>
                    <ListItemIcon>
                      <Shield sx={{ color: 'success.main' }} />
                    </ListItemIcon>
                    <ListItemText 
                      primary="企业级安全"
                      secondary="银行级加密保护您的数据安全"
                    />
                  </ListItem>
                  <ListItem sx={{ px: 0 }}>
                    <ListItemIcon>
                      <AutoAwesome sx={{ color: 'warning.main' }} />
                    </ListItemIcon>
                    <ListItemText 
                      primary="AI智能优化"
                      secondary="基于最新AI算法，实时优化内容"
                    />
                  </ListItem>
                  <ListItem sx={{ px: 0 }}>
                    <ListItemIcon>
                      <Groups sx={{ color: 'info.main' }} />
                    </ListItemIcon>
                    <ListItemText 
                      primary="专业团队支持"
                      secondary="7×24小时专业团队为您服务"
                    />
                  </ListItem>
                </List>

                <Box sx={{ 
                  p: 3, 
                  bgcolor: 'success.50',
                  borderRadius: 2,
                  border: '1px solid',
                  borderColor: 'success.100'
                }}>
                  <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 1, color: 'success.main' }}>
                    🎁 新用户专享
                  </Typography>
                  <Typography variant="body2" sx={{ color: 'text.secondary', mb: 2 }}>
                    现在注册即可获得：
                  </Typography>
                  <List dense sx={{ pl: 2 }}>
                    <ListItem sx={{ px: 0, py: 0.5 }}>
                      <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                        • 30天免费试用专业版功能
                      </Typography>
                    </ListItem>
                    <ListItem sx={{ px: 0, py: 0.5 }}>
                      <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                        • 100次AI优化分析额度
                      </Typography>
                    </ListItem>
                    <ListItem sx={{ px: 0, py: 0.5 }}>
                      <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                        • 专属客户经理一对一指导
                      </Typography>
                    </ListItem>
                  </List>
                </Box>
              </Box>
            </Grid>

            {/* Right Side - Register Form */}
            <Grid item xs={12} md={7}>
              <Paper
                elevation={0}
                sx={{
                  p: 5,
                  borderRadius: 3,
                  border: '1px solid',
                  borderColor: 'divider',
                  maxWidth: 700,
                  mx: 'auto',
                  bgcolor: 'background.paper',
                  position: 'relative',
                  overflow: 'hidden',
                  '&::before': {
                    content: '""',
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    height: '3px',
                    background: 'linear-gradient(90deg, #1e293b, #475569, #64748b)',
                  },
                }}
              >
                {/* Decorative background pattern */}
                <Box
                  sx={{
                    position: 'absolute',
                    top: -100,
                    right: -100,
                    width: 200,
                    height: 200,
                    borderRadius: '50%',
                    background: 'radial-gradient(circle, rgba(30, 41, 59, 0.03) 0%, transparent 70%)',
                    pointerEvents: 'none',
                  }}
                />

                {/* Header */}
                <Box sx={{ textAlign: 'center', mb: 4, position: 'relative', zIndex: 1 }}>
                  <Box
                    sx={{
                      width: 80,
                      height: 80,
                      borderRadius: '50%',
                      background: 'linear-gradient(135deg, #1e293b 0%, #475569 100%)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      mx: 'auto',
                      mb: 3,
                      boxShadow: '0 10px 30px rgba(30, 41, 59, 0.2)',
                    }}
                  >
                    <Person sx={{ fontSize: 40, color: 'white' }} />
                  </Box>
                  <Typography variant="h4" sx={{ fontWeight: 700, mb: 1, color: '#1e293b' }}>
                    创建新账户
                  </Typography>
                  <Typography variant="body1" sx={{ color: '#64748b' }}>
                    加入我们，开启AI搜索优化之旅
                  </Typography>
                </Box>

                {/* Error Alert */}
                {(error || formErrors.general) && (
                  <Alert severity="error" sx={{ mb: 3 }}>
                    {error || formErrors.general}
                  </Alert>
                )}

                {/* Register Form */}
                <Box component="form" onSubmit={handleSubmit}>
            {/* 邮箱地址和手机号码并排 */}
            <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
              <TextField
                fullWidth
                name="email"
                placeholder="邮箱地址"
                type="email"
                value={formData.email}
                onChange={handleChange}
                error={!!formErrors.email}
                helperText={formErrors.email}
                disabled={isSubmitting || isLoading}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Email sx={{ color: '#64748b', fontSize: 20 }} />
                    </InputAdornment>
                  ),
                }}
                sx={{ 
                  '& .MuiInputBase-root': { 
                    height: 48,
                    backgroundColor: 'rgba(248, 250, 252, 0.5)',
                    borderRadius: 2,
                  },
                  '& .MuiOutlinedInput-root': {
                    '&:hover fieldset': {
                      borderColor: '#475569',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: '#1e293b',
                      borderWidth: 2,
                    },
                  },
                  '& .MuiInputLabel-root.Mui-focused': {
                    color: '#1e293b',
                  },
                }}
              />
              <TextField
                fullWidth
                name="phone"
                placeholder="手机号码"
                value={formData.phone}
                onChange={handleChange}
                error={!!formErrors.phone}
                helperText={formErrors.phone}
                disabled={isSubmitting || isLoading}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Phone sx={{ color: '#64748b', fontSize: 20 }} />
                    </InputAdornment>
                  ),
                }}
                sx={{ 
                  '& .MuiInputBase-root': { 
                    height: 48,
                    backgroundColor: 'rgba(248, 250, 252, 0.5)',
                    borderRadius: 2,
                  },
                  '& .MuiOutlinedInput-root': {
                    '&:hover fieldset': {
                      borderColor: '#475569',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: '#1e293b',
                      borderWidth: 2,
                    },
                  },
                }}
              />
            </Box>

            {/* 密码和确认密码并排 */}
            <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
              <TextField
                fullWidth
                name="password"
                placeholder="密码"
                type="password"
                value={formData.password}
                onChange={handleChange}
                error={!!formErrors.password}
                helperText={formErrors.password}
                disabled={isSubmitting || isLoading}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Lock sx={{ color: '#64748b', fontSize: 20 }} />
                    </InputAdornment>
                  ),
                }}
                sx={{ 
                  '& .MuiInputBase-root': { 
                    height: 48,
                    backgroundColor: 'rgba(248, 250, 252, 0.5)',
                    borderRadius: 2,
                  },
                  '& .MuiOutlinedInput-root': {
                    '&:hover fieldset': {
                      borderColor: '#475569',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: '#1e293b',
                      borderWidth: 2,
                    },
                  },
                }}
              />
              <TextField
                fullWidth
                name="confirmPassword"
                placeholder="确认密码"
                type="password"
                value={formData.confirmPassword}
                onChange={handleChange}
                error={!!formErrors.confirmPassword}
                helperText={formErrors.confirmPassword}
                disabled={isSubmitting || isLoading}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Lock sx={{ color: '#64748b', fontSize: 20 }} />
                    </InputAdornment>
                  ),
                }}
                sx={{ 
                  '& .MuiInputBase-root': { 
                    height: 48,
                    backgroundColor: 'rgba(248, 250, 252, 0.5)',
                    borderRadius: 2,
                  },
                  '& .MuiOutlinedInput-root': {
                    '&:hover fieldset': {
                      borderColor: '#475569',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: '#1e293b',
                      borderWidth: 2,
                    },
                  },
                }}
              />
            </Box>

            {/* 真实姓名和推荐码并排 */}
            <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
              <TextField
                fullWidth
                name="realName"
                placeholder="真实姓名"
                value={formData.realName}
                onChange={handleChange}
                error={!!formErrors.realName}
                helperText={formErrors.realName}
                disabled={isSubmitting || isLoading}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Person sx={{ color: '#64748b', fontSize: 20 }} />
                    </InputAdornment>
                  ),
                }}
                sx={{ 
                  '& .MuiInputBase-root': { 
                    height: 48,
                    backgroundColor: 'rgba(248, 250, 252, 0.5)',
                    borderRadius: 2,
                  },
                  '& .MuiOutlinedInput-root': {
                    '&:hover fieldset': {
                      borderColor: '#475569',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: '#1e293b',
                      borderWidth: 2,
                    },
                  },
                }}
              />
              <TextField
                fullWidth
                name="referralCode"
                placeholder="推荐码（可选）"
                value={formData.referralCode}
                onChange={handleChange}
                disabled={isSubmitting || isLoading}
                sx={{ 
                  '& .MuiInputBase-root': { 
                    height: 48,
                    backgroundColor: 'rgba(248, 250, 252, 0.5)',
                    borderRadius: 2,
                  },
                  '& .MuiOutlinedInput-root': {
                    '&:hover fieldset': {
                      borderColor: '#475569',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: '#1e293b',
                      borderWidth: 2,
                    },
                  },
                }}
              />
            </Box>

            {/* 邮箱验证码 */}
            <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
              <TextField
                name="emailVerificationCode"
                placeholder="邮箱验证码"
                value={formData.emailVerificationCode}
                onChange={handleChange}
                error={!!formErrors.emailVerificationCode}
                helperText={formErrors.emailVerificationCode}
                disabled={isSubmitting || isLoading}
                sx={{
                  flex: '1 1 50%',
                  '& .MuiInputBase-root': { 
                    height: 48,
                    backgroundColor: 'rgba(248, 250, 252, 0.5)',
                    borderRadius: 2,
                  },
                  '& .MuiOutlinedInput-root': {
                    '&:hover fieldset': {
                      borderColor: '#475569',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: '#1e293b',
                      borderWidth: 2,
                    },
                  },
                }}
              />
              <Box sx={{ flex: '1 1 50%', display: 'flex', justifyContent: 'flex-end' }}>
                <Button
                  variant="outlined"
                  onClick={handleSendVerificationCode}
                  disabled={isSubmitting || isLoading || isCodeSending || codeCountdown > 0}
                  sx={{
                    minWidth: 130,
                    height: 48,
                    whiteSpace: 'nowrap',
                    fontSize: '0.875rem',
                    fontWeight: 500,
                    borderColor: '#1e293b',
                    color: '#1e293b',
                    '&:hover': {
                      borderColor: '#0f172a',
                      backgroundColor: 'rgba(30, 41, 59, 0.04)',
                    },
                  }}
                  startIcon={<Send sx={{ fontSize: 18 }} />}
                >
                  {codeCountdown > 0 ? `${codeCountdown}s` : '发送验证码'}
                </Button>
              </Box>
            </Box>

            <FormControlLabel
              control={
                <Checkbox
                  name="agreeToTerms"
                  checked={formData.agreeToTerms}
                  onChange={handleChange}
                  disabled={isSubmitting || isLoading}
                />
              }
              label={
                <Typography variant="body2">
                  我同意{' '}
                  <Link component={RouterLink} to="/terms" target="_blank">
                    服务条款
                  </Link>
                  {' '}和{' '}
                  <Link component={RouterLink} to="/privacy" target="_blank">
                    隐私政策
                  </Link>
                </Typography>
              }
              sx={{ mb: 2 }}
            />

            {formErrors.agreeToTerms && (
              <Typography variant="body2" color="error" sx={{ mb: 2 }}>
                {formErrors.agreeToTerms}
              </Typography>
            )}

            <Button
              type="submit"
              fullWidth
              variant="contained"
              size="large"
              disabled={isSubmitting || isLoading}
              sx={{
                mb: 3,
                py: 1.5,
                fontSize: '1rem',
                fontWeight: 700,
                height: 52,
                borderRadius: 3,
                background: (isSubmitting || isLoading)
                  ? '#94a3b8'
                  : 'linear-gradient(135deg, #1e293b 0%, #334155 100%)',
                boxShadow: (isSubmitting || isLoading)
                  ? 'none'
                  : '0 4px 15px rgba(30, 41, 59, 0.25)',
                textTransform: 'none',
                transition: 'all 0.3s ease',
                '&:hover': {
                  background: (isSubmitting || isLoading)
                    ? '#94a3b8'
                    : 'linear-gradient(135deg, #0f172a 0%, #1e293b 100%)',
                  boxShadow: (isSubmitting || isLoading)
                    ? 'none'
                    : '0 6px 20px rgba(30, 41, 59, 0.35)',
                  transform: (isSubmitting || isLoading) ? 'none' : 'translateY(-2px)',
                },
              }}
              endIcon={!isSubmitting && !isLoading ? <Send sx={{ fontSize: 20 }} /> : null}
            >
              {(isSubmitting || isLoading) ? (
                <CircularProgress size={24} color="inherit" />
              ) : (
                '立即注册'
              )}
            </Button>

                  {/* Login Link */}
                  <Divider sx={{ my: 3 }}>
                    <Typography variant="body2" color="text.secondary">
                      已有账户？
                    </Typography>
                  </Divider>
                  
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="body1" sx={{ color: '#64748b', mb: 2 }}>
                      已经拥有账户？
                    </Typography>
                    <Link
                      component={RouterLink}
                      to="/auth/login"
                      sx={{
                        display: 'inline-block',
                        textDecoration: 'none',
                        color: '#1e293b',
                        fontWeight: 600,
                        fontSize: '1rem',
                        padding: '12px 32px',
                        border: '2px solid #1e293b',
                        borderRadius: 3,
                        transition: 'all 0.3s ease',
                        '&:hover': {
                          backgroundColor: '#1e293b',
                          color: 'white',
                          transform: 'translateY(-2px)',
                          boxShadow: '0 4px 12px rgba(30, 41, 59, 0.25)',
                        },
                      }}
                    >
                      立即登录
                    </Link>
                  </Box>
                </Box>
              </Paper>
            </Grid>
          </Grid>
        </Container>
      </Box>

      {/* Footer */}
      <Footer />
    </Box>
  );
}

export default Register;
