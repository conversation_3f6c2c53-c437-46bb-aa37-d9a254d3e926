import React, { useState } from 'react';
import {
  Box,
  Container,
  Paper,
  TextField,
  Button,
  Typography,
  Link,
  Alert,
  CircularProgress,
  InputAdornment,
  IconButton,
  Grid,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
} from '@mui/material';
import {
  Email,
  ArrowBack,
  CheckCircle,
  Lock,
  Security,
  Shield,
  Key,
  Timer,
  VerifiedUser,
} from '@mui/icons-material';
import { Link as RouterLink, useNavigate } from 'react-router-dom';
import apiService from '../../services/api';
import Footer from '../../components/layout/Footer';

function ForgotPassword() {
  const navigate = useNavigate();
  const [step, setStep] = useState(1); // 1: 验证账户, 2: 设置新密码
  const [email, setEmail] = useState('');
  const [verificationCode, setVerificationCode] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [emailError, setEmailError] = useState('');
  const [codeError, setCodeError] = useState('');
  const [passwordError, setPasswordError] = useState('');

  const [codeCountdown, setCodeCountdown] = useState(0);
  const [isCodeSending, setIsCodeSending] = useState(false);

  const handleChange = (e) => {
    const { name, value } = e.target;

    if (name === 'email') {
      setEmail(value);
      if (emailError) setEmailError('');
    } else if (name === 'verificationCode') {
      setVerificationCode(value);
      if (codeError) setCodeError('');
    } else if (name === 'newPassword') {
      setNewPassword(value);
      if (passwordError) setPasswordError('');
    } else if (name === 'confirmPassword') {
      setConfirmPassword(value);
      if (passwordError) setPasswordError('');
    }

    if (error) {
      setError('');
    }
  };

  const validateEmail = () => {
    if (!email.trim()) {
      setEmailError('请输入邮箱地址');
      return false;
    }

    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      setEmailError('请输入有效的邮箱地址');
      return false;
    }

    return true;
  };

  const validateStep2 = () => {
    let isValid = true;

    if (!verificationCode.trim()) {
      setCodeError('请输入验证码');
      isValid = false;
    }

    if (!newPassword.trim()) {
      setPasswordError('请输入新密码');
      isValid = false;
    } else if (newPassword.length < 6) {
      setPasswordError('密码长度至少6位');
      isValid = false;
    }

    if (newPassword !== confirmPassword) {
      setPasswordError('两次输入的密码不一致');
      isValid = false;
    }

    return isValid;
  };

  const handleStep1Submit = async (e) => {
    e.preventDefault();

    if (!validateEmail()) {
      return;
    }

    // 只验证邮箱格式，然后直接跳转到第二步
    // 不在这里发送验证码，等用户在第二步手动点击"获取验证码"按钮
    setError('');
    setStep(2);
  };

  const handleStep2Submit = async (e) => {
    e.preventDefault();

    if (!validateStep2()) {
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      // 第一步：验证验证码并获取验证令牌
      const verifyResponse = await apiService.verifyCode(email, verificationCode, 'reset_password');

      if (verifyResponse.success && verifyResponse.data.verification_token) {
        // 第二步：使用验证令牌重置密码
        const resetData = {
          email: email,
          new_password: newPassword,
          verification_token: verifyResponse.data.verification_token
        };

        const resetResponse = await apiService.resetPassword(resetData);

        if (resetResponse.success) {
          // Success - redirect to login with success message
          navigate('/auth/login', {
            state: { message: '密码重置成功！请使用新密码登录。' }
          });
        }
      }
    } catch (err) {
      const errorMessage = err.response?.data?.detail || '密码重置失败，请检查验证码是否正确';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSendCode = async () => {
    if (!validateEmail()) {
      return;
    }

    setIsCodeSending(true);
    setError('');

    try {
      // 调用发送验证码的API
      await apiService.sendVerificationCode(email, 'reset_password');

      // 发送成功，开始倒计时
      setCodeCountdown(60);
      const timer = setInterval(() => {
        setCodeCountdown((prev) => {
          if (prev <= 1) {
            clearInterval(timer);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

      alert('验证码已发送到您的邮箱');
    } catch (err) {
      const errorMessage = err.response?.data?.detail || '发送验证码失败，请稍后重试';
      setError(errorMessage);
    } finally {
      setIsCodeSending(false);
    }
  };

  const handleBackToStep1 = () => {
    setStep(1);
    setVerificationCode('');
    setNewPassword('');
    setConfirmPassword('');
    setCodeError('');
    setPasswordError('');
    setError('');
  };

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', minHeight: '100vh' }}>
      {/* Main Content */}
      <Box
        sx={{
          flex: 1,
          display: 'flex',
          alignItems: 'center',
          bgcolor: 'grey.50',
          py: 8,
        }}
      >
        <Container maxWidth="xl">
          <Grid container spacing={6} alignItems="flex-start">
            {/* Left Side - Description */}
            <Grid item xs={12} md={5}>
              <Box sx={{ pr: { md: 4 }, pt: { md: 5 } }}>
                <Typography variant="h4" sx={{ fontWeight: 700, color: 'text.primary', mb: 3 }}>
                  密码重置
                </Typography>
                
                <Typography variant="h5" sx={{ fontWeight: 600, mb: 3, color: 'text.primary' }}>
                  快速找回您的账户
                </Typography>
                
                <Typography variant="body1" sx={{ mb: 4, color: 'text.secondary', lineHeight: 1.8 }}>
                  忘记密码不用担心，我们为您提供简单快捷的密码重置服务。只需几步即可重新获得账户访问权限。
                </Typography>

                <List sx={{ mb: 4 }}>
                  <ListItem sx={{ px: 0 }}>
                    <ListItemIcon>
                      <Shield sx={{ color: 'primary.main' }} />
                    </ListItemIcon>
                    <ListItemText 
                      primary="安全验证"
                      secondary="通过邮箱验证码确保账户安全"
                    />
                  </ListItem>
                  <ListItem sx={{ px: 0 }}>
                    <ListItemIcon>
                      <Timer sx={{ color: 'success.main' }} />
                    </ListItemIcon>
                    <ListItemText 
                      primary="快速重置"
                      secondary="验证码即时发送，快速完成重置"
                    />
                  </ListItem>
                  <ListItem sx={{ px: 0 }}>
                    <ListItemIcon>
                      <Key sx={{ color: 'warning.main' }} />
                    </ListItemIcon>
                    <ListItemText 
                      primary="新密码规则"
                      secondary="密码长度至少6位，包含字母和数字"
                    />
                  </ListItem>
                  <ListItem sx={{ px: 0 }}>
                    <ListItemIcon>
                      <VerifiedUser sx={{ color: 'info.main' }} />
                    </ListItemIcon>
                    <ListItemText 
                      primary="账户保护"
                      secondary="重置后立即生效，保护账户安全"
                    />
                  </ListItem>
                </List>

                <Box sx={{ 
                  p: 3, 
                  bgcolor: 'info.50',
                  borderRadius: 2,
                  border: '1px solid',
                  borderColor: 'info.100'
                }}>
                  <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 1, color: 'info.main' }}>
                    💡 安全提示
                  </Typography>
                  <Typography variant="body2" sx={{ color: 'text.secondary', lineHeight: 1.8 }}>
                    • 请确保使用注册时的邮箱地址
                    <br />
                    • 验证码有效期为10分钟
                    <br />
                    • 新密码设置后请妥善保管
                    <br />
                    • 如遇问题请联系客服支持
                  </Typography>
                </Box>
              </Box>
            </Grid>

            {/* Right Side - Reset Form */}
            <Grid item xs={12} md={7}>
              <Box sx={{ 
                display: 'flex', 
                alignItems: 'center', 
                justifyContent: 'flex-end',
                minHeight: '650px',
                pt: { md: 2 },
                pr: { md: 0 },
                pl: { md: 4 }
              }}>
                <Paper
                  elevation={0}
                  sx={{
                    p: { xs: 4, md: 7 },
                    borderRadius: 3,
                    border: '1px solid',
                    borderColor: 'divider',
                    maxWidth: 650,
                    width: '95%',
                    mr: { md: 0 },
                    bgcolor: 'background.paper',
                    position: 'relative',
                    overflow: 'hidden',
                    transform: { md: 'translateX(-5%)' },
                    '&::before': {
                      content: '""',
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      height: '3px',
                      background: 'linear-gradient(90deg, #1e293b, #475569, #64748b)',
                    },
                  }}
                >
                {/* Header */}
                <Box sx={{ textAlign: 'center', mb: 4, position: 'relative', zIndex: 1 }}>
                  <Box
                    sx={{
                      width: 80,
                      height: 80,
                      borderRadius: '50%',
                      background: 'linear-gradient(135deg, #1e293b 0%, #475569 100%)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      mx: 'auto',
                      mb: 3,
                      boxShadow: '0 10px 30px rgba(30, 41, 59, 0.2)',
                    }}
                  >
                    <Lock sx={{ fontSize: 40, color: 'white' }} />
                  </Box>
                  <Typography variant="h4" sx={{ fontWeight: 700, mb: 1, color: '#1e293b' }}>
                    {step === 1 ? '验证账户' : '重置密码'}
                  </Typography>
                  <Typography variant="body1" sx={{ color: '#64748b' }}>
                    {step === 1 ? '请输入您的注册邮箱' : '设置您的新密码'}
                  </Typography>
                </Box>

                {/* Error Alert */}
                {error && (
                  <Alert severity="error" sx={{ mb: 3 }}>
                    {error}
                  </Alert>
                )}

                {/* Step 1: Email Verification */}
                {step === 1 && (
                  <Box component="form" onSubmit={handleStep1Submit}>
                    <TextField
                      fullWidth
                      name="email"
                      label="邮箱地址"
                      placeholder="<EMAIL>"
                      type="email"
                      value={email}
                      onChange={handleChange}
                      error={!!emailError}
                      helperText={emailError}
                      disabled={isLoading}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Email sx={{ color: '#64748b', fontSize: 20 }} />
                          </InputAdornment>
                        ),
                      }}
                      sx={{ 
                        mb: 3,
                        '& .MuiInputBase-root': { 
                          height: 48,
                          backgroundColor: 'rgba(248, 250, 252, 0.5)',
                          borderRadius: 2,
                        },
                        '& .MuiOutlinedInput-root': {
                          '&:hover fieldset': {
                            borderColor: '#475569',
                          },
                          '&.Mui-focused fieldset': {
                            borderColor: '#1e293b',
                            borderWidth: 2,
                          },
                        },
                      }}
                    />

                    <Button
                      type="submit"
                      fullWidth
                      variant="contained"
                      size="large"
                      disabled={isLoading}
                      sx={{
                        mb: 3,
                        py: 1.5,
                        fontSize: '1rem',
                        fontWeight: 700,
                        height: 52,
                        borderRadius: 3,
                        background: isLoading
                          ? '#94a3b8'
                          : 'linear-gradient(135deg, #1e293b 0%, #334155 100%)',
                        boxShadow: isLoading
                          ? 'none'
                          : '0 4px 15px rgba(30, 41, 59, 0.25)',
                        textTransform: 'none',
                        '&:hover': {
                          background: isLoading
                            ? '#94a3b8'
                            : 'linear-gradient(135deg, #0f172a 0%, #1e293b 100%)',
                        },
                      }}
                    >
                      {isLoading ? (
                        <CircularProgress size={24} color="inherit" />
                      ) : (
                        '下一步'
                      )}
                    </Button>

                    <Box sx={{ textAlign: 'center' }}>
                      <Link
                        component={RouterLink}
                        to="/auth/login"
                        sx={{
                          display: 'inline-flex',
                          alignItems: 'center',
                          gap: 0.5,
                          textDecoration: 'none',
                          color: '#1e293b',
                          fontWeight: 600,
                          '&:hover': {
                            color: '#0f172a',
                          },
                        }}
                      >
                        <ArrowBack sx={{ fontSize: 16 }} />
                        返回登录
                      </Link>
                    </Box>
                  </Box>
                )}

                {/* Step 2: Reset Password */}
                {step === 2 && (
                  <Box component="form" onSubmit={handleStep2Submit}>
                    {/* Email Display */}
                    <Box sx={{ mb: 3 }}>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                        邮箱地址
                      </Typography>
                      <Box
                        sx={{
                          p: 1.5,
                          bgcolor: 'grey.100',
                          borderRadius: 2,
                          fontSize: '0.875rem',
                        }}
                      >
                        {email}
                      </Box>
                    </Box>

                    {/* Verification Code */}
                    <Box sx={{ mb: 3 }}>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                        验证码
                      </Typography>
                      <Box sx={{ display: 'flex', gap: 2 }}>
                        <TextField
                          name="verificationCode"
                          placeholder="请输入验证码"
                          value={verificationCode}
                          onChange={handleChange}
                          error={!!codeError}
                          disabled={isLoading}
                          fullWidth
                          InputProps={{
                            startAdornment: (
                              <InputAdornment position="start">
                                <Security sx={{ color: '#64748b', fontSize: 20 }} />
                              </InputAdornment>
                            ),
                          }}
                          sx={{ 
                            flex: 1,
                            '& .MuiInputBase-root': { 
                              height: 48,
                              backgroundColor: 'rgba(248, 250, 252, 0.5)',
                              borderRadius: 2,
                            },
                          }}
                        />
                        <Button
                          variant="outlined"
                          onClick={handleSendCode}
                          disabled={isLoading || isCodeSending || codeCountdown > 0}
                          sx={{
                            minWidth: 120,
                            height: 48,
                            borderColor: '#1e293b',
                            color: '#1e293b',
                            '&:hover': {
                              borderColor: '#0f172a',
                              backgroundColor: 'rgba(30, 41, 59, 0.04)',
                            },
                          }}
                        >
                          {isCodeSending ? (
                            <CircularProgress size={20} color="inherit" />
                          ) : codeCountdown > 0 ? (
                            `${codeCountdown}s`
                          ) : (
                            '发送验证码'
                          )}
                        </Button>
                      </Box>
                      {codeError && (
                        <Typography variant="caption" color="error" sx={{ mt: 0.5, display: 'block' }}>
                          {codeError}
                        </Typography>
                      )}
                    </Box>

                    {/* New Password */}
                    <TextField
                      fullWidth
                      name="newPassword"
                      label="新密码"
                      placeholder="请输入新密码"
                      type="password"
                      value={newPassword}
                      onChange={handleChange}
                      error={!!passwordError}
                      disabled={isLoading}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Lock sx={{ color: '#64748b', fontSize: 20 }} />
                          </InputAdornment>
                        ),
                      }}
                      sx={{ 
                        mb: 3,
                        '& .MuiInputBase-root': { 
                          height: 48,
                          backgroundColor: 'rgba(248, 250, 252, 0.5)',
                          borderRadius: 2,
                        },
                      }}
                    />

                    {/* Confirm Password */}
                    <TextField
                      fullWidth
                      name="confirmPassword"
                      label="确认新密码"
                      placeholder="请再次输入新密码"
                      type="password"
                      value={confirmPassword}
                      onChange={handleChange}
                      error={!!passwordError}
                      helperText={passwordError}
                      disabled={isLoading}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Lock sx={{ color: '#64748b', fontSize: 20 }} />
                          </InputAdornment>
                        ),
                      }}
                      sx={{ 
                        mb: 3,
                        '& .MuiInputBase-root': { 
                          height: 48,
                          backgroundColor: 'rgba(248, 250, 252, 0.5)',
                          borderRadius: 2,
                        },
                      }}
                    />

                    <Button
                      type="submit"
                      fullWidth
                      variant="contained"
                      size="large"
                      disabled={isLoading}
                      sx={{
                        mb: 3,
                        py: 1.5,
                        fontSize: '1rem',
                        fontWeight: 700,
                        height: 52,
                        borderRadius: 3,
                        background: isLoading
                          ? '#94a3b8'
                          : 'linear-gradient(135deg, #1e293b 0%, #334155 100%)',
                        boxShadow: isLoading
                          ? 'none'
                          : '0 4px 15px rgba(30, 41, 59, 0.25)',
                        textTransform: 'none',
                        '&:hover': {
                          background: isLoading
                            ? '#94a3b8'
                            : 'linear-gradient(135deg, #0f172a 0%, #1e293b 100%)',
                        },
                      }}
                    >
                      {isLoading ? (
                        <CircularProgress size={24} color="inherit" />
                      ) : (
                        '重置密码'
                      )}
                    </Button>

                    <Box sx={{ textAlign: 'center' }}>
                      <Link
                        component="button"
                        type="button"
                        onClick={handleBackToStep1}
                        sx={{
                          display: 'inline-flex',
                          alignItems: 'center',
                          gap: 0.5,
                          textDecoration: 'none',
                          color: '#1e293b',
                          fontWeight: 600,
                          border: 'none',
                          background: 'none',
                          cursor: 'pointer',
                          '&:hover': {
                            color: '#0f172a',
                          },
                        }}
                      >
                        <ArrowBack sx={{ fontSize: 16 }} />
                        返回上一步
                      </Link>
                    </Box>
                  </Box>
                )}
              </Paper>
              </Box>
            </Grid>
          </Grid>
        </Container>
      </Box>
      
      {/* Footer */}
      <Footer />
    </Box>
  );
}

export default ForgotPassword;