import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Paper,
  TextField,
  Button,
  Typography,
  Alert,
  CircularProgress,
  InputAdornment,
  IconButton,
  Divider,
} from '@mui/material';
import {
  Email,
  Lock,
  Visibility,
  VisibilityOff,
  AdminPanelSettings,
  Security,
  Shield,
  VpnKey,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';

function AdminLogin() {
  const navigate = useNavigate();
  const { login, logout, error, isLoading, clearError, user } = useAuth();

  const [formData, setFormData] = useState({
    email: '',
    password: '',
  });

  const [formErrors, setFormErrors] = useState({});
  const [showPassword, setShowPassword] = useState(false);
  const [loginAttempts, setLoginAttempts] = useState(0);
  const [isBlocked, setIsBlocked] = useState(false);

  // Check if user is already logged in as admin
  useEffect(() => {
    if (user && (user.role === 'admin' || user.role === 'super_admin')) {
      navigate(user.role === 'super_admin' ? '/super-admin/dashboard' : '/admin/dashboard');
    }
  }, [user, navigate]);

  // Auto-clear error after 5 seconds
  useEffect(() => {
    if (error) {
      const timer = setTimeout(() => {
        clearError();
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [error, clearError]);

  // Block login after too many failed attempts
  useEffect(() => {
    if (loginAttempts >= 5) {
      setIsBlocked(true);
      const timer = setTimeout(() => {
        setIsBlocked(false);
        setLoginAttempts(0);
      }, 300000); // Block for 5 minutes
      return () => clearTimeout(timer);
    }
  }, [loginAttempts]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));

    // Clear field error when user starts typing
    if (formErrors[name]) {
      setFormErrors(prev => ({
        ...prev,
        [name]: '',
      }));
    }

    // Clear auth error
    if (error) {
      clearError();
    }
  };

  const validateForm = () => {
    const errors = {};

    if (!formData.email.trim()) {
      errors.email = '请输入管理员邮箱';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = '请输入有效的邮箱地址';
    }

    if (!formData.password) {
      errors.password = '请输入管理员密码';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (isBlocked) {
      setFormErrors({
        email: '登录尝试次数过多，请5分钟后再试'
      });
      return;
    }

    if (!validateForm()) {
      return;
    }

    // Clear any previous errors
    if (error) {
      clearError();
    }

    try {
      // Login and get the result
      console.log('Attempting login with:', formData.email);
      const loginResult = await login(formData);
      console.log('Login result:', loginResult);
      
      // Check if the logged in user has admin or super_admin role
      if (loginResult && loginResult.user && loginResult.user.role) {
        const userRole = loginResult.user.role;
        console.log('User role:', userRole);
        
        // Check if user is NOT admin or super_admin
        if (userRole !== 'admin' && userRole !== 'super_admin') {
          // Log out non-admin users
          await logout();
          
          // Increment failed attempts
          setLoginAttempts(prev => prev + 1);
          
          // Show error message
          setFormErrors({ 
            email: '权限不足：此入口仅供管理员使用' 
          });
          
          // Alert for better visibility
          setTimeout(() => {
            alert('您不是管理员账户，无法从此处登录。\n\n普通用户请使用用户登录入口。');
          }, 100);
          
          return;
        }

        // Successful admin/super_admin login - navigate to appropriate dashboard
        if (userRole === 'super_admin') {
          navigate('/super-admin/dashboard');
        } else if (userRole === 'admin') {
          navigate('/admin/dashboard');
        }
      }
    } catch (err) {
      // Increment failed attempts
      setLoginAttempts(prev => prev + 1);

    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !isLoading && !isBlocked) {
      handleSubmit(e);
    }
  };

  return (
    <Box sx={{
      minHeight: '100vh',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      background: 'linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%)',
      position: 'relative',
      overflow: 'hidden'
    }}>
      {/* Animated Background Pattern */}
      <Box sx={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        opacity: 0.1,
        background: `
          radial-gradient(circle at 20% 80%, #3b82f6 0%, transparent 50%),
          radial-gradient(circle at 80% 20%, #cccccc 0%, transparent 50%),
          radial-gradient(circle at 40% 40%, #10b981 0%, transparent 50%)
        `,
        animation: 'pulse 10s ease-in-out infinite',
        '@keyframes pulse': {
          '0%, 100%': { opacity: 0.1 },
          '50%': { opacity: 0.2 }
        }
      }} />

      {/* Security Grid Pattern */}
      <Box sx={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundImage: `
          linear-gradient(rgba(255,255,255,0.02) 1px, transparent 1px),
          linear-gradient(90deg, rgba(255,255,255,0.02) 1px, transparent 1px)
        `,
        backgroundSize: '50px 50px',
        animation: 'grid 20s linear infinite',
        '@keyframes grid': {
          '0%': { transform: 'translate(0, 0)' },
          '100%': { transform: 'translate(50px, 50px)' }
        }
      }} />

      <Container maxWidth="sm" sx={{ position: 'relative', zIndex: 1 }}>
        <Paper
          elevation={24}
          sx={{
            p: 6,
            borderRadius: 3,
            background: 'rgba(255, 255, 255, 0.98)',
            backdropFilter: 'blur(20px)',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.5)',
          }}
        >
          {/* Header with Shield Icon */}
          <Box sx={{ textAlign: 'center', mb: 5 }}>
            <Box sx={{
              width: 100,
              height: 100,
              borderRadius: '50%',
              background: 'linear-gradient(135deg, #1e293b 0%, #0f172a 100%)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              mx: 'auto',
              mb: 3,
              boxShadow: '0 15px 35px rgba(15, 23, 42, 0.3)',
              position: 'relative',
            }}>
              <Shield sx={{ fontSize: 50, color: 'white' }} />
              <Box sx={{
                position: 'absolute',
                top: -5,
                right: -5,
                width: 30,
                height: 30,
                borderRadius: '50%',
                backgroundColor: '#10b981',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                border: '3px solid white',
              }}>
                <VpnKey sx={{ fontSize: 14, color: 'white' }} />
              </Box>
            </Box>

            <Typography variant="h3" sx={{
              fontWeight: 800,
              background: 'linear-gradient(135deg, #0f172a 0%, #1e293b 100%)',
              backgroundClip: 'text',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              mb: 1
            }}>
              管理后台
            </Typography>
            <Typography variant="body1" sx={{ color: '#64748b', mb: 2 }}>
              AI搜索优化平台 - 管理员控制中心
            </Typography>

            {/* Security Badges */}
            <Box sx={{ display: 'flex', justifyContent: 'center', gap: 2 }}>
              <Box sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 0.5,
                px: 1.5,
                py: 0.5,
                backgroundColor: '#f0f9ff',
                borderRadius: 1,
                border: '1px solid #0284c7',
              }}>
                <Security sx={{ fontSize: 16, color: '#0284c7' }} />
                <Typography variant="caption" sx={{ color: '#0284c7', fontWeight: 600 }}>
                  SSL加密
                </Typography>
              </Box>
              <Box sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 0.5,
                px: 1.5,
                py: 0.5,
                backgroundColor: '#f0fdf4',
                borderRadius: 1,
                border: '1px solid #16a34a',
              }}>
                <AdminPanelSettings sx={{ fontSize: 16, color: '#16a34a' }} />
                <Typography variant="caption" sx={{ color: '#16a34a', fontWeight: 600 }}>
                  管理员专用
                </Typography>
              </Box>
            </Box>
          </Box>

          {/* Warning Alert */}
          <Alert
            severity="warning"
            sx={{
              mb: 3,
              backgroundColor: '#fef3c7',
              color: '#92400e',
              '& .MuiAlert-icon': {
                color: '#f59e0b',
              },
            }}
          >
            <Typography variant="body2" sx={{ fontWeight: 600 }}>
              安全提醒
            </Typography>
            <Typography variant="caption">
              此入口仅供管理员使用，所有登录活动将被记录和审计
            </Typography>
          </Alert>

          {/* Error Alert */}
          {error && (
            <Alert
              severity="error"
              sx={{
                mb: 3,
                animation: 'shake 0.5s',
                '@keyframes shake': {
                  '0%, 100%': { transform: 'translateX(0)' },
                  '10%, 30%, 50%, 70%, 90%': { transform: 'translateX(-2px)' },
                  '20%, 40%, 60%, 80%': { transform: 'translateX(2px)' },
                },
              }}
            >
              {error}
            </Alert>
          )}

          {/* Form Errors */}
          {(formErrors.email || formErrors.password) && (
            <Alert
              severity="error"
              sx={{ mb: 3 }}
            >
              {formErrors.email || formErrors.password}
            </Alert>
          )}

          {/* Blocked Alert */}
          {isBlocked && (
            <Alert
              severity="error"
              sx={{
                mb: 3,
                backgroundColor: '#fef2f2',
                color: '#991b1b',
              }}
            >
              登录已被暂时锁定，请5分钟后重试
            </Alert>
          )}

          {/* Login Form */}
          <form onSubmit={handleSubmit}>
            <TextField
              fullWidth
              name="email"
              label="管理员邮箱"
              placeholder="<EMAIL>"
              type="email"
              value={formData.email}
              onChange={handleChange}
              onKeyPress={handleKeyPress}
              error={!!formErrors.email}
              disabled={isLoading || isBlocked}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Email sx={{ color: '#64748b' }} />
                  </InputAdornment>
                ),
              }}
              sx={{
                mb: 3,
                '& .MuiOutlinedInput-root': {
                  borderRadius: 2,
                  backgroundColor: '#f8fafc',
                  '&:hover fieldset': {
                    borderColor: '#1e293b',
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: '#0f172a',
                    borderWidth: 2,
                  },
                },
                '& .MuiInputLabel-root.Mui-focused': {
                  color: '#0f172a',
                },
              }}
            />

            <TextField
              fullWidth
              name="password"
              label="管理员密码"
              placeholder="输入您的管理员密码"
              type={showPassword ? 'text' : 'password'}
              value={formData.password}
              onChange={handleChange}
              onKeyPress={handleKeyPress}
              error={!!formErrors.password}
              disabled={isLoading || isBlocked}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Lock sx={{ color: '#64748b' }} />
                  </InputAdornment>
                ),
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      onClick={() => setShowPassword(!showPassword)}
                      edge="end"
                      disabled={isLoading || isBlocked}
                    >
                      {showPassword ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  </InputAdornment>
                ),
              }}
              sx={{
                mb: 4,
                '& .MuiOutlinedInput-root': {
                  borderRadius: 2,
                  backgroundColor: '#f8fafc',
                  '&:hover fieldset': {
                    borderColor: '#1e293b',
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: '#0f172a',
                    borderWidth: 2,
                  },
                },
                '& .MuiInputLabel-root.Mui-focused': {
                  color: '#0f172a',
                },
              }}
            />

            <Button
              fullWidth
              type="submit"
              variant="contained"
              size="large"
              disabled={isLoading || isBlocked}
              sx={{
                mb: 3,
                py: 2,
                fontSize: '1.1rem',
                fontWeight: 700,
                borderRadius: 2,
                background: isLoading || isBlocked
                  ? '#94a3b8'
                  : 'linear-gradient(135deg, #0f172a 0%, #1e293b 100%)',
                boxShadow: isLoading || isBlocked
                  ? 'none'
                  : '0 10px 25px rgba(15, 23, 42, 0.3)',
                textTransform: 'none',
                transition: 'all 0.3s ease',
                '&:hover': {
                  background: isLoading || isBlocked
                    ? '#94a3b8'
                    : 'linear-gradient(135deg, #020617 0%, #0f172a 100%)',
                  boxShadow: isLoading || isBlocked
                    ? 'none'
                    : '0 15px 35px rgba(15, 23, 42, 0.4)',
                  transform: isLoading || isBlocked ? 'none' : 'translateY(-2px)',
                },
              }}
            >
              {isLoading ? (
                <CircularProgress size={26} color="inherit" />
              ) : isBlocked ? (
                '登录已锁定'
              ) : (
                '管理员登录'
              )}
            </Button>
          </form>

          <Divider sx={{ my: 3 }}>
            <Typography variant="caption" sx={{ color: '#94a3b8', px: 2 }}>
              安全信息
            </Typography>
          </Divider>

          {/* Security Info */}
          <Box sx={{ textAlign: 'center' }}>
            <Typography variant="caption" sx={{ color: '#64748b', display: 'block', mb: 1 }}>
              登录即表示您同意我们的安全政策和使用条款
            </Typography>
            <Typography variant="caption" sx={{ color: '#94a3b8' }}>
              IP地址: {window.location.hostname} | 
              {loginAttempts > 0 && ` 失败尝试: ${loginAttempts}/5 |`}
              {' '}时间: {new Date().toLocaleTimeString('zh-CN')}
            </Typography>
          </Box>

          {/* Back to User Login */}
          <Box sx={{ mt: 3, pt: 3, borderTop: '1px solid #e2e8f0', textAlign: 'center' }}>
            <Button
              variant="text"
              onClick={() => navigate('/auth/login')}
              sx={{
                color: '#64748b',
                textTransform: 'none',
                '&:hover': {
                  color: '#1e293b',
                  backgroundColor: 'transparent',
                },
              }}
            >
              ← 返回用户登录
            </Button>
          </Box>
        </Paper>

        {/* Copyright */}
        <Typography
          variant="caption"
          sx={{
            display: 'block',
            textAlign: 'center',
            mt: 3,
            color: 'rgba(255, 255, 255, 0.6)',
          }}
        >
          © 2024 AI搜索优化平台 - 管理后台 v2.0
        </Typography>
      </Container>
    </Box>
  );
}

export default AdminLogin;