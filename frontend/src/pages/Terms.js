import React from 'react';
import {
  Box,
  Container,
  Typography,
  Paper,
  Divider,
  List,
  ListItem,
  ListItemText,
} from '@mui/material';
import Footer from '../components/layout/Footer';

function Terms() {
  const sections = [
    {
      title: '1. 服务条款的接受',
      content: [
        '欢迎使用AI搜索优化平台（以下简称"本平台"）。通过访问或使用本平台，您同意受本服务条款的约束。',
        '如果您不同意本服务条款的任何部分，请不要使用本平台。',
        '我们保留随时修改本服务条款的权利，修改后的条款将在本页面发布。'
      ]
    },
    {
      title: '2. 服务描述',
      content: [
        '本平台提供AI搜索引擎优化服务，包括但不限于内容创作、关键词优化、数据分析等功能。',
        '我们致力于提供高质量的服务，但不保证服务不会中断或完全没有错误。',
        '用户需要自行负责维护访问本平台所需的设备和网络连接。'
      ]
    },
    {
      title: '3. 用户账户',
      content: [
        '用户注册时必须提供真实、准确、完整的信息。',
        '用户应妥善保管账户信息，对账户下的所有活动负责。',
        '发现账户被未经授权使用时，应立即通知本平台。',
        '本平台不对因用户疏忽导致的账户被盗用承担责任。'
      ]
    },
    {
      title: '4. 使用规范',
      content: [
        '用户不得利用本平台从事任何违法违规活动。',
        '禁止发布虚假、误导性或侵权内容。',
        '不得干扰或破坏本平台的正常运行。',
        '不得未经授权访问其他用户的账户或数据。',
        '禁止使用自动化工具恶意访问本平台。'
      ]
    },
    {
      title: '5. 知识产权',
      content: [
        '本平台的所有内容，包括但不限于文字、图片、标识、软件等，均受知识产权法保护。',
        '用户在本平台创建的内容，其知识产权归用户所有。',
        '用户授予本平台使用其内容的非独占性许可，用于提供和改进服务。',
        '未经许可，不得复制、修改或分发本平台的任何内容。'
      ]
    },
    {
      title: '6. 付费服务',
      content: [
        '付费服务的价格和计费方式以平台公布的为准。',
        '用户应按时支付服务费用，逾期可能导致服务暂停。',
        '退款政策根据具体服务类型而定，详见各服务说明。',
        '本平台保留调整价格的权利，但会提前通知用户。'
      ]
    },
    {
      title: '7. 隐私保护',
      content: [
        '我们重视用户隐私，具体隐私保护措施请参见《隐私政策》。',
        '我们会采取合理措施保护用户数据安全。',
        '用户同意本平台按照隐私政策收集和使用其信息。'
      ]
    },
    {
      title: '8. 免责声明',
      content: [
        '本平台不对用户使用服务产生的结果做任何保证。',
        '对于因不可抗力导致的服务中断，本平台不承担责任。',
        '用户应自行承担使用本平台服务的风险。',
        '本平台不对第三方链接的内容负责。'
      ]
    },
    {
      title: '9. 服务终止',
      content: [
        '用户可以随时注销账户，终止使用本平台服务。',
        '本平台有权因用户违反服务条款而终止其账户。',
        '服务终止后，用户仍需支付已产生的费用。',
        '账户终止后，本平台可能保留必要的用户信息以符合法律要求。'
      ]
    },
    {
      title: '10. 争议解决',
      content: [
        '本服务条款受中华人民共和国法律管辖。',
        '因本服务条款产生的争议，双方应友好协商解决。',
        '协商不成的，应提交本平台所在地有管辖权的法院解决。'
      ]
    }
  ];

  return (
    <Box sx={{ minHeight: '100vh', bgcolor: '#f5f7fa', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Box
        sx={{
          background: 'linear-gradient(135deg, #1e293b 0%, #334155 100%)',
          py: 8,
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        <Container maxWidth="lg">
          <Typography
            variant="h2"
            sx={{
              fontWeight: 700,
              color: 'white',
              textAlign: 'center',
              mb: 2,
              fontSize: { xs: '2rem', md: '3rem' },
            }}
          >
            服务条款
          </Typography>
          <Typography
            variant="h6"
            sx={{
              color: 'rgba(255, 255, 255, 0.8)',
              textAlign: 'center',
              maxWidth: 600,
              mx: 'auto',
            }}
          >
            请仔细阅读我们的服务条款，了解您的权利和义务
          </Typography>
        </Container>
      </Box>

      {/* Content */}
      <Container maxWidth="lg" sx={{ py: 6, flex: 1 }}>
        <Paper
          elevation={0}
          sx={{
            p: { xs: 3, md: 6 },
            borderRadius: 3,
            border: '1px solid #e2e8f0',
          }}
        >
          <Box sx={{ mb: 4 }}>
            <Typography variant="body1" sx={{ color: '#64748b', mb: 3 }}>
              最后更新日期：2024年12月
            </Typography>
            <Typography variant="body1" sx={{ color: '#475569', lineHeight: 1.8 }}>
              欢迎使用AI搜索优化平台。本服务条款（以下简称"条款"）是您与本平台之间的法律协议。
              请在使用本平台服务前仔细阅读本条款。通过注册、访问或使用本平台，即表示您同意接受本条款的约束。
            </Typography>
          </Box>

          <Divider sx={{ my: 4 }} />

          {sections.map((section, index) => (
            <Box key={index} sx={{ mb: 4 }}>
              <Typography
                variant="h5"
                sx={{
                  fontWeight: 600,
                  color: '#1e293b',
                  mb: 2,
                }}
              >
                {section.title}
              </Typography>
              <List>
                {section.content.map((item, idx) => (
                  <ListItem key={idx} sx={{ px: 0, py: 1 }}>
                    <ListItemText
                      primary={item}
                      primaryTypographyProps={{
                        sx: { color: '#475569', lineHeight: 1.8 }
                      }}
                    />
                  </ListItem>
                ))}
              </List>
            </Box>
          ))}

          <Divider sx={{ my: 4 }} />

          <Box sx={{ mt: 4 }}>
            <Typography variant="h5" sx={{ fontWeight: 600, color: '#1e293b', mb: 2 }}>
              11. 联系我们
            </Typography>
            <Typography variant="body1" sx={{ color: '#475569', lineHeight: 1.8 }}>
              如果您对本服务条款有任何疑问，请通过以下方式联系我们：
            </Typography>
            <List>
              <ListItem sx={{ px: 0, py: 1 }}>
                <ListItemText
                  primary="邮箱：<EMAIL>"
                  primaryTypographyProps={{
                    sx: { color: '#475569' }
                  }}
                />
              </ListItem>
              <ListItem sx={{ px: 0, py: 1 }}>
                <ListItemText
                  primary="电话：400-888-8888"
                  primaryTypographyProps={{
                    sx: { color: '#475569' }
                  }}
                />
              </ListItem>
            </List>
          </Box>
        </Paper>
      </Container>

      {/* Footer */}
      <Footer />
    </Box>
  );
}

export default Terms;