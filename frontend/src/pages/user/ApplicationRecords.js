import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Grid,
  Chip,
  Button,
  Alert,
  CircularProgress,
  Tabs,
  Tab,
  Container,
  IconButton,
} from '@mui/material';
import {
  Assignment,
  Pending,
  CheckCircle,
  Cancel,
  Refresh,
  Add,
  Business,
  Store,
  Campaign,
  AccessTime,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import apiService from '../../services/api';

function ApplicationRecords() {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [applications, setApplications] = useState([]);
  const [error, setError] = useState('');
  const [activeTab, setActiveTab] = useState(0);
  const [lastFetchTime, setLastFetchTime] = useState(null);

  // 统计数据
  const [stats, setStats] = useState({
    total: 0,
    pending: 0,
    approved: 0,
    rejected: 0,
  });

  useEffect(() => {
    // 尝试从本地存储加载缓存数据
    const loadCachedData = () => {
      try {
        const cachedData = localStorage.getItem('applicationRecords');
        const cachedTime = localStorage.getItem('applicationRecordsTime');

        if (cachedData && cachedTime) {
          const now = Date.now();
          const cacheTime = 5 * 60 * 1000; // 5分钟缓存
          const lastTime = parseInt(cachedTime);

          if ((now - lastTime) < cacheTime) {
            // 使用缓存数据
            const data = JSON.parse(cachedData);
            setApplications(data.applications || []);
            setStats(data.stats || { total: 0, pending: 0, approved: 0, rejected: 0 });
            setLastFetchTime(lastTime);
            setLoading(false);
            return true;
          }
        }
      } catch (error) {
        console.warn('加载缓存数据失败:', error);
      }
      return false;
    };

    // 如果没有缓存或缓存过期，则获取新数据
    if (!loadCachedData()) {
      fetchApplications();
    }
  }, []);

  const fetchApplications = async (isRefresh = false) => {
    try {
      if (isRefresh) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }
      setError('');

      // 并行获取各种角色的申请记录，优化性能
      const apiCalls = [
        apiService.get('/companies/me').catch(() => ({ status: 'rejected' })),
        apiService.get('/channels/me').catch(() => ({ status: 'rejected' })),
        apiService.get('/agents/me').catch(() => ({ status: 'rejected' })),
      ];

      const [enterpriseRes, channelRes, agentRes] = await Promise.allSettled(apiCalls);

      const allApplications = [];

      // 处理企业用户申请
      if (enterpriseRes.status === 'fulfilled' && enterpriseRes.value?.data) {
        allApplications.push({
          id: enterpriseRes.value.data.id,
          type: 'enterprise_user',
          typeName: '企业用户',
          iconType: 'Business',
          name: enterpriseRes.value.data.company_name,
          status: enterpriseRes.value.data.verification_status || 'pending',
          createdAt: enterpriseRes.value.data.created_at,
          updatedAt: enterpriseRes.value.data.updated_at,
          data: enterpriseRes.value.data,
        });
      }

      // 处理渠道商申请
      if (channelRes.status === 'fulfilled' && channelRes.value?.data) {
        allApplications.push({
          id: channelRes.value.data.id,
          type: 'channel_user',
          typeName: '渠道商',
          iconType: 'Store',
          name: channelRes.value.data.provider_name,
          status: channelRes.value.data.verification_status || 'pending',
          createdAt: channelRes.value.data.created_at,
          updatedAt: channelRes.value.data.updated_at,
          data: channelRes.value.data,
        });
      }

      // 处理代理商申请
      if (agentRes.status === 'fulfilled' && agentRes.value?.data) {
        allApplications.push({
          id: agentRes.value.data.id,
          type: 'agent_user',
          typeName: '代理商',
          iconType: 'Campaign',
          name: agentRes.value.data.agent_name,
          status: agentRes.value.data.verification_status || 'pending',
          createdAt: agentRes.value.data.created_at,
          updatedAt: agentRes.value.data.updated_at,
          data: agentRes.value.data,
        });
      }

      setApplications(allApplications);

      // 计算统计数据
      const newStats = {
        total: allApplications.length,
        pending: allApplications.filter(app => app.status === 'pending').length,
        approved: allApplications.filter(app => app.status === 'verified').length,
        rejected: allApplications.filter(app => app.status === 'rejected').length,
      };
      setStats(newStats);

      // 更新最后获取时间
      const now = Date.now();
      setLastFetchTime(now);

      // 缓存数据到本地存储
      try {
        const cacheData = {
          applications: allApplications,
          stats: newStats,
        };
        localStorage.setItem('applicationRecords', JSON.stringify(cacheData));
        localStorage.setItem('applicationRecordsTime', now.toString());
      } catch (error) {

      }

    } catch (error) {

      setError('获取申请记录失败，请稍后重试');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending':
        return 'warning';
      case 'verified':
        return 'success';
      case 'rejected':
        return 'error';
      default:
        return 'default';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'pending':
        return '审核中';
      case 'verified':
        return '已通过';
      case 'rejected':
        return '已拒绝';
      default:
        return '未知';
    }
  };

  const getApplicationIcon = (iconType) => {
    const iconColor = 
      iconType === 'Business' ? '#3b82f6' :
      iconType === 'Store' ? '#10b981' :
      iconType === 'Campaign' ? '#f59e0b' :
      '#6b7280';
    
    switch (iconType) {
      case 'Business':
        return <Business sx={{ fontSize: 24, color: iconColor }} />;
      case 'Store':
        return <Store sx={{ fontSize: 24, color: iconColor }} />;
      case 'Campaign':
        return <Campaign sx={{ fontSize: 24, color: iconColor }} />;
      default:
        return <Assignment sx={{ fontSize: 24, color: iconColor }} />;
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'pending':
        return <Pending sx={{ fontSize: 40, color: 'warning.main' }} />;
      case 'verified':
        return <CheckCircle sx={{ fontSize: 40, color: 'success.main' }} />;
      case 'rejected':
        return <Cancel sx={{ fontSize: 40, color: 'error.main' }} />;
      default:
        return <Assignment sx={{ fontSize: 40, color: 'text.secondary' }} />;
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return '未知';
    return new Date(dateString).toLocaleString('zh-CN');
  };

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const getFilteredApplications = () => {
    switch (activeTab) {
      case 1:
        return applications.filter(app => app.status === 'pending');
      case 2:
        return applications.filter(app => app.status === 'verified');
      case 3:
        return applications.filter(app => app.status === 'rejected');
      default:
        return applications;
    }
  };

  const handleNewApplication = () => {
    navigate('/user/role-application');
  };

  const handleRefresh = () => {
    fetchApplications(true);
  };

  if (loading) {
    return (
      <Box sx={{
        backgroundColor: 'white',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        py: 8
      }}>
        <Box sx={{ textAlign: 'center' }}>
          <CircularProgress size={40} sx={{ color: '#1a1a1a' }} />
          <Typography variant="body2" sx={{ mt: 2, color: '#6b7280' }}>
            正在加载申请记录...
          </Typography>
        </Box>
      </Box>
    );
  }

  return (
    <Box sx={{
      backgroundColor: 'white',
      width: '100%'
    }}>
      {/* 顶部标题区域 */}
      <Box sx={{
        borderBottom: '1px solid #f0f0f0',
        backgroundColor: 'white'
      }}>
        <Container maxWidth="lg">
          <Box sx={{ py: 5 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Box>
                <Typography variant="h3" sx={{
                  fontWeight: 700,
                  color: '#1a1a1a',
                  mb: 1,
                  fontSize: { xs: '2rem', md: '2.5rem' }
                }}>
                  申请记录
                </Typography>
                <Typography variant="body1" sx={{
                  color: '#6b7280',
                  fontSize: '1.125rem'
                }}>
                  查看您的角色申请状态和审核进度
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', gap: 2 }}>
                <IconButton 
                  onClick={handleRefresh} 
                  disabled={refreshing}
                  sx={{
                    border: '1px solid #e5e7eb',
                    borderRadius: 1.5,
                    p: 1,
                    '&:hover': {
                      backgroundColor: '#f9fafb'
                    }
                  }}
                >
                  <Refresh sx={{
                    color: '#6b7280',
                    animation: refreshing ? 'spin 1s linear infinite' : 'none',
                    '@keyframes spin': {
                      '0%': { transform: 'rotate(0deg)' },
                      '100%': { transform: 'rotate(360deg)' },
                    }
                  }} />
                </IconButton>
                <Button
                  variant="contained"
                  startIcon={<Add />}
                  onClick={handleNewApplication}
                  sx={{
                    backgroundColor: '#1a1a1a',
                    color: 'white',
                    borderRadius: 1.5,
                    textTransform: 'none',
                    fontWeight: 600,
                    px: 3,
                    boxShadow: 'none',
                    '&:hover': {
                      backgroundColor: '#374151',
                      boxShadow: 'none',
                    }
                  }}
                >
                  新建申请
                </Button>
              </Box>
            </Box>
          </Box>
        </Container>
      </Box>

      {/* 错误提示 */}
      {error && (
        <Container maxWidth="lg" sx={{ mt: 3 }}>
          <Alert
            severity="error"
            sx={{ mb: 3, borderRadius: 2 }}
            onClose={() => setError('')}
          >
            {error}
          </Alert>
        </Container>
      )}

      {/* 统计卡片 */}
      <Box sx={{
        borderBottom: '1px solid #f0f0f0',
        backgroundColor: '#fafafa'
      }}>
        <Container maxWidth="lg">
          <Box sx={{ pt: 2, pb: 4 }}>
            <Typography variant="h5" sx={{
              fontWeight: 600,
              color: '#1a1a1a',
              mb: 3
            }}>
              申请统计
            </Typography>
            <Grid container spacing={3}>
              <Grid item xs={6} sm={3}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Box sx={{
                    width: 44,
                    height: 44,
                    borderRadius: '12px',
                    backgroundColor: '#eff6ff',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <Assignment sx={{ color: '#3b82f6', fontSize: 22 }} />
                  </Box>
                  <Box>
                    <Typography variant="caption" sx={{ 
                      color: '#9ca3af', 
                      fontSize: '0.75rem',
                      fontWeight: 500,
                      textTransform: 'uppercase',
                      letterSpacing: '0.05em'
                    }}>
                      全部申请
                    </Typography>
                    <Typography variant="h6" sx={{ 
                      color: '#1a1a1a',
                      fontWeight: 600,
                      fontSize: '1.5rem',
                      mt: 0.5
                    }}>
                      {stats.total}
                    </Typography>
                  </Box>
                </Box>
              </Grid>
              <Grid item xs={6} sm={3}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Box sx={{
                    width: 44,
                    height: 44,
                    borderRadius: '12px',
                    backgroundColor: '#fef3c7',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <Pending sx={{ color: '#f59e0b', fontSize: 22 }} />
                  </Box>
                  <Box>
                    <Typography variant="caption" sx={{ 
                      color: '#9ca3af', 
                      fontSize: '0.75rem',
                      fontWeight: 500,
                      textTransform: 'uppercase',
                      letterSpacing: '0.05em'
                    }}>
                      审核中
                    </Typography>
                    <Typography variant="h6" sx={{ 
                      color: '#1a1a1a',
                      fontWeight: 600,
                      fontSize: '1.5rem',
                      mt: 0.5
                    }}>
                      {stats.pending}
                    </Typography>
                  </Box>
                </Box>
              </Grid>
              <Grid item xs={6} sm={3}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Box sx={{
                    width: 44,
                    height: 44,
                    borderRadius: '12px',
                    backgroundColor: '#f0fdf4',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <CheckCircle sx={{ color: '#10b981', fontSize: 22 }} />
                  </Box>
                  <Box>
                    <Typography variant="caption" sx={{ 
                      color: '#9ca3af', 
                      fontSize: '0.75rem',
                      fontWeight: 500,
                      textTransform: 'uppercase',
                      letterSpacing: '0.05em'
                    }}>
                      已通过
                    </Typography>
                    <Typography variant="h6" sx={{ 
                      color: '#1a1a1a',
                      fontWeight: 600,
                      fontSize: '1.5rem',
                      mt: 0.5
                    }}>
                      {stats.approved}
                    </Typography>
                  </Box>
                </Box>
              </Grid>
              <Grid item xs={6} sm={3}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Box sx={{
                    width: 44,
                    height: 44,
                    borderRadius: '12px',
                    backgroundColor: '#fef2f2',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <Cancel sx={{ color: '#ef4444', fontSize: 22 }} />
                  </Box>
                  <Box>
                    <Typography variant="caption" sx={{ 
                      color: '#9ca3af', 
                      fontSize: '0.75rem',
                      fontWeight: 500,
                      textTransform: 'uppercase',
                      letterSpacing: '0.05em'
                    }}>
                      已拒绝
                    </Typography>
                    <Typography variant="h6" sx={{ 
                      color: '#1a1a1a',
                      fontWeight: 600,
                      fontSize: '1.5rem',
                      mt: 0.5
                    }}>
                      {stats.rejected}
                    </Typography>
                  </Box>
                </Box>
              </Grid>
            </Grid>
          </Box>
        </Container>
      </Box>

      {/* 我的申请区域 */}
      <Box sx={{ backgroundColor: 'white' }}>
        <Container maxWidth="lg">
          <Box sx={{ py: 4 }}>
            <Typography variant="h5" sx={{
              fontWeight: 600,
              color: '#1a1a1a',
              mb: 3
            }}>
              我的申请
            </Typography>

            {/* 筛选标签 */}
            <Box sx={{ borderBottom: '1px solid #e5e7eb', mb: 3 }}>
              <Tabs
                value={activeTab}
                onChange={handleTabChange}
                sx={{
                  '& .MuiTab-root': {
                    textTransform: 'none',
                    fontWeight: 600,
                    minHeight: 48,
                    color: '#6b7280',
                    '&.Mui-selected': {
                      color: '#1a1a1a',
                    }
                  },
                  '& .MuiTabs-indicator': {
                    backgroundColor: '#1a1a1a',
                  }
                }}
              >
                <Tab label={`全部 (${stats.total})`} />
                <Tab label={`待审核 (${stats.pending})`} />
                <Tab label={`已通过 (${stats.approved})`} />
                <Tab label={`已拒绝 (${stats.rejected})`} />
              </Tabs>
            </Box>

            {/* 申请列表 */}
            {getFilteredApplications().length === 0 ? (
              <Box sx={{
                textAlign: 'center',
                py: 8,
                backgroundColor: '#fafafa',
                borderRadius: 2,
                border: '1px solid #e5e7eb'
              }}>
                <Box sx={{
                  width: 64,
                  height: 64,
                  borderRadius: '16px',
                  backgroundColor: '#f3f4f6',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  mx: 'auto',
                  mb: 2
                }}>
                  <Assignment sx={{ fontSize: 32, color: '#9ca3af' }} />
                </Box>
                <Typography variant="h6" sx={{ 
                  mb: 1,
                  color: '#1a1a1a',
                  fontWeight: 600
                }}>
                  暂无申请记录
                </Typography>
                <Typography variant="body2" sx={{ 
                  mb: 3,
                  color: '#6b7280'
                }}>
                  您还没有提交过任何角色申请
                </Typography>
                <Button
                  variant="contained"
                  startIcon={<Add />}
                  onClick={handleNewApplication}
                  sx={{
                    backgroundColor: '#1a1a1a',
                    color: 'white',
                    borderRadius: 1.5,
                    textTransform: 'none',
                    fontWeight: 600,
                    px: 3,
                    boxShadow: 'none',
                    '&:hover': {
                      backgroundColor: '#374151',
                      boxShadow: 'none',
                    }
                  }}
                >
                  创建新申请
                </Button>
              </Box>
            ) : (
              <Grid container spacing={2}>
                {getFilteredApplications().map((application) => (
                  <Grid item xs={12} key={application.id}>
                    <Box sx={{
                      p: 3,
                      border: '1px solid #e5e7eb',
                      borderRadius: 2,
                      backgroundColor: 'white',
                      transition: 'all 0.2s ease',
                      '&:hover': {
                        borderColor: '#9ca3af',
                        backgroundColor: '#fafafa',
                      }
                    }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 3 }}>
                          {/* 图标 */}
                          <Box sx={{
                            width: 48,
                            height: 48,
                            borderRadius: '12px',
                            backgroundColor: 
                              application.iconType === 'Business' ? 'rgba(59, 130, 246, 0.08)' :
                              application.iconType === 'Store' ? 'rgba(16, 185, 129, 0.08)' :
                              'rgba(245, 158, 11, 0.08)',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center'
                          }}>
                            {getApplicationIcon(application.iconType)}
                          </Box>
                          
                          {/* 信息 */}
                          <Box sx={{ flex: 1 }}>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5, mb: 1 }}>
                              <Typography variant="h6" sx={{ 
                                fontWeight: 600,
                                color: '#1a1a1a',
                                fontSize: '1.125rem'
                              }}>
                                {application.name || application.typeName}
                              </Typography>
                              <Chip
                                label={application.typeName}
                                size="small"
                                sx={{
                                  height: 22,
                                  backgroundColor: '#f3f4f6',
                                  color: '#374151',
                                  fontWeight: 600,
                                  fontSize: '0.75rem',
                                  '& .MuiChip-label': {
                                    px: 1.5,
                                  }
                                }}
                              />
                            </Box>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 3 }}>
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                                <AccessTime sx={{ fontSize: 14, color: '#9ca3af' }} />
                                <Typography variant="body2" sx={{ 
                                  color: '#6b7280',
                                  fontSize: '0.875rem'
                                }}>
                                  申请时间：{formatDate(application.createdAt)}
                                </Typography>
                              </Box>
                              {application.updatedAt !== application.createdAt && (
                                <Typography variant="body2" sx={{ 
                                  color: '#6b7280',
                                  fontSize: '0.875rem'
                                }}>
                                  更新：{formatDate(application.updatedAt)}
                                </Typography>
                              )}
                            </Box>
                          </Box>
                        </Box>
                        
                        {/* 状态 */}
                        <Chip
                          label={getStatusText(application.status)}
                          size="small"
                          sx={{
                            backgroundColor: 
                              application.status === 'pending' ? '#fef3c7' :
                              application.status === 'verified' ? '#f0fdf4' :
                              '#fef2f2',
                            color: 
                              application.status === 'pending' ? '#92400e' :
                              application.status === 'verified' ? '#166534' :
                              '#991b1b',
                            fontWeight: 600,
                            fontSize: '0.875rem',
                            height: 28,
                            '& .MuiChip-label': {
                              px: 2,
                            }
                          }}
                        />
                      </Box>
                    </Box>
                  </Grid>
                ))}
              </Grid>
            )}
          </Box>
        </Container>
      </Box>
    </Box>
  );
}

export default ApplicationRecords;
