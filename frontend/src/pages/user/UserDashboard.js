import React, { useState } from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Button,
  Chip,
  Paper,
  Divider,
  Alert,
  IconButton,
} from '@mui/material';
import { useAuth } from '../../contexts/AuthContext';
import {
  Business,
  Campaign,
  Store,
  Email,
  CalendarToday,
  CheckCircle,
  ArrowForward,
  PersonOutline,
  VerifiedUser,
  AccessTime,
  ManageAccounts,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';

function UserDashboard() {
  const { user } = useAuth();
  const navigate = useNavigate();

  // 获取用户角色信息
  const getUserRoles = () => {
    // 统一角色数据结构
    if (Array.isArray(user?.roles)) return user.roles;
    if (user?.role && user.role !== 'regular_user') return [user.role];
    return ['regular_user'];
  };

  // 获取用户显示名称
  const getUserDisplayName = () => {
    return user?.full_name || user?.name || user?.username || '用户';
  };

  // 获取上次登录时间
  const getLastLoginDate = () => {
    return user?.last_login_at ?
      new Date(user.last_login_at).toLocaleDateString('zh-CN') :
      '未知';
  };

  // 角色快捷链接
  const roleQuickLinks = [
    {
      role: 'enterprise_user',
      title: '企业用户中心',
      icon: <Business />,
      description: '企业级API和批量处理',
      color: '#3b82f6',
      bgColor: 'rgba(59, 130, 246, 0.04)',
      hoverColor: 'rgba(59, 130, 246, 0.08)',
      link: '/control-center/enterprise',
      active: getUserRoles().includes('enterprise_user'),
    },
    {
      role: 'channel_user',
      title: '渠道商中心',
      icon: <Store />,
      description: '内容上传与收益管理',
      color: '#10b981',
      bgColor: 'rgba(16, 185, 129, 0.04)',
      hoverColor: 'rgba(16, 185, 129, 0.08)',
      link: '/control-center/channel',
      active: getUserRoles().includes('channel_user'),
    },
    {
      role: 'agent_user',
      title: '代理商中心',
      icon: <Campaign />,
      description: '推广链接与佣金管理',
      color: '#f59e0b',
      bgColor: 'rgba(245, 158, 11, 0.04)',
      hoverColor: 'rgba(245, 158, 11, 0.08)',
      link: '/control-center/agent',
      active: getUserRoles().includes('agent_user'),
    },
  ];

  return (
    <Box sx={{
      minHeight: '100vh',
      backgroundColor: 'white',
      width: '100%'
    }}>
      {/* 顶部欢迎区域 */}
      <Box sx={{
        borderBottom: '1px solid #f0f0f0',
        backgroundColor: 'white',
        pt: 2
      }}>
        <Container maxWidth="lg">
          <Box sx={{
            py: 5,
          }}>
            <Typography variant="h3" sx={{
              fontWeight: 700,
              color: '#1a1a1a',
              mb: 1,
              fontSize: { xs: '2rem', md: '2.5rem' }
            }}>
              欢迎回来，{getUserDisplayName()}
            </Typography>
            <Typography variant="body1" sx={{
              color: '#6b7280',
              fontSize: '1.125rem'
            }}>
              管理您的账户信息，访问各个控制中心
            </Typography>
          </Box>
        </Container>
      </Box>

      {/* 账户信息统计栏 */}
      <Box sx={{
        borderBottom: '1px solid #f0f0f0',
        backgroundColor: '#fafafa'
      }}>
        <Container maxWidth="lg">
          <Box sx={{ py: 4 }}>
            <Grid container spacing={4}>
              <Grid item xs={12} sm={6} md={3}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Box sx={{
                    width: 44,
                    height: 44,
                    borderRadius: '12px',
                    backgroundColor: '#f0fdf4',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <CheckCircle sx={{ color: '#10b981', fontSize: 22 }} />
                  </Box>
                  <Box>
                    <Typography variant="caption" sx={{ 
                      color: '#9ca3af', 
                      fontSize: '0.75rem',
                      fontWeight: 500,
                      textTransform: 'uppercase',
                      letterSpacing: '0.05em'
                    }}>
                      账户状态
                    </Typography>
                    <Typography variant="h6" sx={{ 
                      color: '#1a1a1a',
                      fontWeight: 600,
                      fontSize: '1.125rem',
                      mt: 0.5
                    }}>
                      正常
                    </Typography>
                  </Box>
                </Box>
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Box sx={{
                    width: 44,
                    height: 44,
                    borderRadius: '12px',
                    backgroundColor: '#eff6ff',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <Email sx={{ color: '#3b82f6', fontSize: 22 }} />
                  </Box>
                  <Box>
                    <Typography variant="caption" sx={{ 
                      color: '#9ca3af', 
                      fontSize: '0.75rem',
                      fontWeight: 500,
                      textTransform: 'uppercase',
                      letterSpacing: '0.05em'
                    }}>
                      当前邮箱
                    </Typography>
                    <Typography variant="body1" sx={{ 
                      color: '#1a1a1a',
                      fontWeight: 500,
                      fontSize: '0.95rem',
                      mt: 0.5,
                      wordBreak: 'break-all'
                    }}>
                      {user?.email || '<EMAIL>'}
                    </Typography>
                  </Box>
                </Box>
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Box sx={{
                    width: 44,
                    height: 44,
                    borderRadius: '12px',
                    backgroundColor: '#fef3c7',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <CalendarToday sx={{ color: '#f59e0b', fontSize: 22 }} />
                  </Box>
                  <Box>
                    <Typography variant="caption" sx={{
                      color: '#9ca3af',
                      fontSize: '0.75rem',
                      fontWeight: 500,
                      textTransform: 'uppercase',
                      letterSpacing: '0.05em'
                    }}>
                      上次登录时间
                    </Typography>
                    <Typography variant="h6" sx={{
                      color: '#1a1a1a',
                      fontWeight: 600,
                      fontSize: '1.125rem',
                      mt: 0.5
                    }}>
                      {getLastLoginDate()}
                    </Typography>
                  </Box>
                </Box>
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Box sx={{
                    width: 44,
                    height: 44,
                    borderRadius: '12px',
                    backgroundColor: '#f3f4f6',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <VerifiedUser sx={{ color: '#6b7280', fontSize: 22 }} />
                  </Box>
                  <Box>
                    <Typography variant="caption" sx={{ 
                      color: '#9ca3af', 
                      fontSize: '0.75rem',
                      fontWeight: 500,
                      textTransform: 'uppercase',
                      letterSpacing: '0.05em'
                    }}>
                      已申请身份
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap', mt: 0.5 }}>
                      {(() => {
                        // 过滤掉 regular_user，只显示特殊角色
                        const specialRoles = getUserRoles().filter(role => 
                          role !== 'regular_user' && role !== 'user'
                        );
                        
                        if (specialRoles.length > 0) {
                          return specialRoles.map((role) => (
                            <Chip
                              key={role}
                              label={
                                role === 'enterprise_user' ? '企业' :
                                role === 'channel_user' ? '渠道' :
                                role === 'agent_user' ? '代理' : 
                                role === 'admin' ? '管理员' :
                                role === 'super_admin' ? '超级管理员' : role
                              }
                              size="small"
                              sx={{
                                height: 20,
                                backgroundColor: '#1a1a1a',
                                color: 'white',
                                fontWeight: 600,
                                fontSize: '0.7rem',
                                '& .MuiChip-label': {
                                  px: 1,
                                }
                              }}
                            />
                          ));
                        } else {
                          return (
                            <Typography variant="body2" sx={{ 
                              color: '#9ca3af',
                              fontSize: '0.875rem'
                            }}>
                              暂无
                            </Typography>
                          );
                        }
                      })()}
                    </Box>
                  </Box>
                </Box>
              </Grid>
            </Grid>
          </Box>
        </Container>
      </Box>

      {/* 快捷访问区域 */}
      <Box sx={{ backgroundColor: 'white' }}>
        <Container maxWidth="lg">
          <Box sx={{ py: 5 }}>
            {/* 标题栏 */}
            <Box sx={{ 
              mb: 4
            }}>
              <Typography variant="h5" sx={{
                fontWeight: 700,
                color: '#1a1a1a',
                mb: 0.5
              }}>
                快捷访问
              </Typography>
              <Typography variant="body2" sx={{
                color: '#6b7280'
              }}>
                选择角色进入对应的控制中心
              </Typography>
            </Box>

            {/* 角色卡片网格 */}
            <Grid container spacing={3}>
              {roleQuickLinks.map((item) => (
                <Grid item xs={12} md={4} key={item.role}>
                  <Box
                    sx={{
                      p: 3,
                      height: '100%',
                      border: '1px solid',
                      borderColor: item.active ? item.color : '#e5e7eb',
                      borderRadius: 2,
                      backgroundColor: item.active ? item.bgColor : 'white',
                      transition: 'all 0.2s ease',
                      cursor: 'pointer',
                      position: 'relative',
                      '&:hover': {
                        backgroundColor: item.active ? item.hoverColor : '#f9fafb',
                        borderColor: item.active ? item.color : '#d1d5db',
                        transform: 'translateY(-2px)',
                        boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
                      }
                    }}
                  >
                    {/* 激活标记 */}
                    {item.active && (
                      <Box sx={{
                        position: 'absolute',
                        top: 16,
                        right: 16,
                        width: 8,
                        height: 8,
                        borderRadius: '50%',
                        backgroundColor: item.color,
                      }} />
                    )}

                    {/* 图标 */}
                    <Box sx={{
                      width: 48,
                      height: 48,
                      borderRadius: '12px',
                      backgroundColor: item.active ? item.color : '#f3f4f6',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      mb: 2
                    }}>
                      {React.cloneElement(item.icon, { 
                        sx: { 
                          color: item.active ? 'white' : '#9ca3af',
                          fontSize: 24
                        } 
                      })}
                    </Box>

                    {/* 标题和描述 */}
                    <Typography variant="h6" sx={{
                      fontWeight: 600,
                      color: item.active ? '#1a1a1a' : '#6b7280',
                      mb: 1,
                      fontSize: '1.125rem'
                    }}>
                      {item.title}
                    </Typography>
                    <Typography variant="body2" sx={{
                      color: '#6b7280',
                      fontSize: '0.875rem',
                      lineHeight: 1.5,
                      mb: 3
                    }}>
                      {item.description}
                    </Typography>

                    {/* 操作按钮 */}
                    {item.active ? (
                      <Button
                        fullWidth
                        variant="contained"
                        size="small"
                        endIcon={<ArrowForward sx={{ fontSize: 16 }} />}
                        onClick={() => navigate(item.link)}
                        sx={{
                          backgroundColor: item.color,
                          color: 'white',
                          borderRadius: 1.5,
                          py: 1,
                          fontSize: '0.875rem',
                          fontWeight: 600,
                          textTransform: 'none',
                          boxShadow: 'none',
                          '&:hover': {
                            backgroundColor: item.color,
                            filter: 'brightness(0.95)',
                            boxShadow: 'none',
                          }
                        }}
                      >
                        进入中心
                      </Button>
                    ) : (
                      <Button
                        fullWidth
                        variant="outlined"
                        size="small"
                        onClick={() => navigate(`/user/role-application?role=${item.role}`)}
                        sx={{
                          borderColor: '#e5e7eb',
                          color: '#6b7280',
                          borderRadius: 1.5,
                          py: 1,
                          fontSize: '0.875rem',
                          fontWeight: 600,
                          textTransform: 'none',
                          '&:hover': {
                            borderColor: '#9ca3af',
                            backgroundColor: '#f9fafb'
                          }
                        }}
                      >
                        申请开通
                      </Button>
                    )}
                  </Box>
                </Grid>
              ))}
            </Grid>


            {/* 底部提示 */}
            <Box sx={{
              mt: 4,
              p: 3,
              backgroundColor: '#f9fafb',
              borderRadius: 2,
              border: '1px solid #e5e7eb'
            }}>
              <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2 }}>
                <Box sx={{
                  width: 36,
                  height: 36,
                  borderRadius: '10px',
                  backgroundColor: '#fef3c7',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  flexShrink: 0,
                  mt: 0.5
                }}>
                  <Typography sx={{ fontSize: '1rem' }}>💡</Typography>
                </Box>
                <Box sx={{ flex: 1 }}>
                  <Typography variant="body2" sx={{
                    color: '#1a1a1a',
                    fontWeight: 600,
                    mb: 0.5,
                    fontSize: '0.95rem'
                  }}>
                    温馨提示
                  </Typography>
                  <Typography variant="body2" sx={{
                    color: '#6b7280',
                    lineHeight: 1.6,
                    fontSize: '0.875rem'
                  }}>
                    您可以申请开通不同的角色权限来访问相应的功能模块。每个角色都有独立的控制中心和专属功能。
                    申请通过后，相应的入口将会激活。如需帮助，请联系客服。
                  </Typography>
                </Box>
              </Box>
            </Box>
          </Box>
        </Container>
      </Box>

    </Box>
  );
}

export default UserDashboard;