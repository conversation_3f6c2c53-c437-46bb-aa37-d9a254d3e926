import React, { useState, useEffect, useMemo, useCallback } from 'react';
import {
  Box,
  Typography,
  Grid,
  Container,
  TextField,
  Button,
  Alert,
  CircularProgress,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Divider,
} from '@mui/material';
import {
  Person,
  Email,
  Phone,
  Business,
  Edit,
  Save,
  Cancel,
  Security,
  CheckCircle,
  Schedule,
  Warning,
  Info,
  VpnKey,
  Badge,
  CalendarToday,
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import apiService from '../../services/api';

function UserProfile() {
  const { user } = useAuth();
  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [userInfo, setUserInfo] = useState(null);
  const [detailedInfoLoaded, setDetailedInfoLoaded] = useState(false);
  const [passwordDialog, setPasswordDialog] = useState(false);

  const [formData, setFormData] = useState({
    full_name: '',
    phone: '',
  });

  const [passwordData, setPasswordData] = useState({
    current_password: '',
    new_password: '',
    confirm_password: '',
  });

  // 获取详细用户信息
  const fetchDetailedUserInfo = useCallback(async (force = false) => {
    try {
      if (detailedInfoLoaded && !force) {
        return;
      }

      setIsLoading(true);
      setError('');

      const responseData = await apiService.get('/users/me');

      if (responseData && responseData.success) {
        const userData = responseData.data;
        setUserInfo(userData);
        setDetailedInfoLoaded(true);
        setFormData({
          full_name: userData.full_name || '',
          phone: userData.phone || '',
        });
      } else {
        setError('获取用户信息失败');
      }
    } catch (error) {
      console.error('获取用户信息失败:', error);
      setError(`获取用户信息失败: ${error.response?.data?.detail || error.message}`);
    } finally {
      setIsLoading(false);
    }
  }, [detailedInfoLoaded]);

  useEffect(() => {
    if (user) {
      setFormData({
        full_name: user.full_name || user.name || '',
        phone: user.phone || '',
      });
      fetchDetailedUserInfo();
    }
  }, [user, fetchDetailedUserInfo]);

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handlePasswordChange = (field, value) => {
    setPasswordData(prev => ({
      ...prev,
      [field]: value
    }));

    if (error) setError('');
    if (success) setSuccess('');
  };

  const handleSave = async () => {
    try {
      setIsSaving(true);
      setError('');

      const responseData = await apiService.put('/users/me', formData);

      if (responseData && responseData.success) {
        setSuccess('个人信息更新成功');
        setIsEditing(false);
        setUserInfo(prev => ({
          ...prev,
          full_name: formData.full_name,
          phone: formData.phone,
        }));
      } else {
        setError('更新失败');
      }
    } catch (error) {
      console.error('更新失败:', error);
      setError(error.response?.data?.detail || error.message || '更新失败，请重试');
    } finally {
      setIsSaving(false);
    }
  };

  const handlePasswordSave = async () => {
    if (passwordData.new_password !== passwordData.confirm_password) {
      setError('新密码和确认密码不匹配');
      return;
    }

    if (passwordData.new_password.length < 8) {
      setError('新密码长度至少8位');
      return;
    }

    try {
      setIsSaving(true);
      setError('');

      const passwordPayload = {
        current_password: passwordData.current_password,
        new_password: passwordData.new_password,
        confirm_password: passwordData.confirm_password,
      };

      const responseData = await apiService.put('/users/me/password', passwordPayload);

      if (responseData && responseData.success) {
        setSuccess('密码修改成功');
        setPasswordDialog(false);
        setPasswordData({
          current_password: '',
          new_password: '',
          confirm_password: '',
        });
      } else {
        setError('密码修改失败');
      }
    } catch (error) {
      console.error('修改密码失败:', error);
      setError(error.response?.data?.detail || error.message || '修改密码失败，请重试');
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    if (userInfo) {
      setFormData({
        full_name: userInfo.full_name || '',
        phone: userInfo.phone || '',
      });
    }
    setIsEditing(false);
    setError('');
  };

  const handleEdit = () => {
    setIsEditing(true);
    setError('');
    setSuccess('');
  };

  const formatDate = (dateString) => {
    if (!dateString) return '未知';
    return new Date(dateString).toLocaleString('zh-CN');
  };

  return (
    <>
    <Box sx={{
      minHeight: '100vh',
      backgroundColor: 'white',
      width: '100%'
    }}>
      {/* 顶部标题区域 */}
      <Box sx={{
        borderBottom: '1px solid #f0f0f0',
        backgroundColor: 'white',
        pt: 2
      }}>
        <Container maxWidth="lg">
          <Box sx={{ py: 3 }}>
            <Typography variant="h3" sx={{
              fontWeight: 700,
              color: '#1a1a1a',
              mb: 1,
              fontSize: { xs: '2rem', md: '2.5rem' }
            }}>
              个人资料
            </Typography>
            <Typography variant="body1" sx={{
              color: '#6b7280',
              fontSize: '1.125rem'
            }}>
              管理您的个人信息和账户设置
            </Typography>
          </Box>
        </Container>
      </Box>

      {/* 提示信息和个人信息区域 */}
      <Container maxWidth="lg" sx={{ mt: 0 }}>
        {success && (
          <Alert severity="success" sx={{ mt: 2, mb: 2, borderRadius: 2 }} onClose={() => setSuccess('')}>
            {success}
          </Alert>
        )}
        {error && (
          <Alert severity="error" sx={{ mt: 2, mb: 2, borderRadius: 2 }} onClose={() => setError('')}>
            {error}
          </Alert>
        )}
        
        <Box sx={{ py: 3, backgroundColor: 'white' }}>
            <Box sx={{ 
              display: 'flex', 
              justifyContent: 'space-between', 
              alignItems: 'center',
              mb: 3
            }}>
              <Typography variant="h5" sx={{
                fontWeight: 600,
                color: '#1a1a1a'
              }}>
                基本信息
              </Typography>
              {!isEditing && !isLoading && (
                <Button
                  variant="text"
                  startIcon={<Edit />}
                  onClick={handleEdit}
                  sx={{
                    color: '#6b7280',
                    textTransform: 'none',
                    fontWeight: 600,
                    '&:hover': {
                      backgroundColor: 'rgba(0,0,0,0.04)',
                      color: '#1a1a1a'
                    }
                  }}
                >
                  编辑信息
                </Button>
              )}
            </Box>

            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Box>
                  <Typography variant="caption" sx={{ 
                    color: '#9ca3af', 
                    fontSize: '0.75rem',
                    fontWeight: 500,
                    textTransform: 'uppercase',
                    letterSpacing: '0.05em',
                    mb: 1,
                    display: 'block'
                  }}>
                    真实姓名
                  </Typography>
                  <TextField
                    fullWidth
                    value={formData.full_name}
                    onChange={(e) => handleInputChange('full_name', e.target.value)}
                    disabled={!isEditing}
                    variant="outlined"
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        backgroundColor: isEditing ? 'white' : 'transparent',
                        '& fieldset': {
                          borderColor: isEditing ? '#d1d5db' : 'transparent',
                        },
                        '&:hover fieldset': {
                          borderColor: isEditing ? '#9ca3af' : 'transparent',
                        },
                      },
                      '& .MuiInputBase-input.Mui-disabled': {
                        color: '#1a1a1a',
                        WebkitTextFillColor: '#1a1a1a',
                      },
                    }}
                  />
                </Box>
              </Grid>

              <Grid item xs={12} md={6}>
                <Box>
                  <Typography variant="caption" sx={{ 
                    color: '#9ca3af', 
                    fontSize: '0.75rem',
                    fontWeight: 500,
                    textTransform: 'uppercase',
                    letterSpacing: '0.05em',
                    mb: 1,
                    display: 'block'
                  }}>
                    手机号码
                  </Typography>
                  <TextField
                    fullWidth
                    value={formData.phone}
                    onChange={(e) => handleInputChange('phone', e.target.value)}
                    disabled={!isEditing}
                    variant="outlined"
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        backgroundColor: isEditing ? 'white' : 'transparent',
                        '& fieldset': {
                          borderColor: isEditing ? '#d1d5db' : 'transparent',
                        },
                        '&:hover fieldset': {
                          borderColor: isEditing ? '#9ca3af' : 'transparent',
                        },
                      },
                      '& .MuiInputBase-input.Mui-disabled': {
                        color: '#1a1a1a',
                        WebkitTextFillColor: '#1a1a1a',
                      },
                    }}
                  />
                </Box>
              </Grid>

              <Grid item xs={12} md={6}>
                <Box>
                  <Typography variant="caption" sx={{ 
                    color: '#9ca3af', 
                    fontSize: '0.75rem',
                    fontWeight: 500,
                    textTransform: 'uppercase',
                    letterSpacing: '0.05em',
                    mb: 1,
                    display: 'block'
                  }}>
                    邮箱地址
                  </Typography>
                  <TextField
                    fullWidth
                    value={userInfo?.email || user?.email || ''}
                    disabled
                    variant="outlined"
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        backgroundColor: 'transparent',
                        '& fieldset': {
                          borderColor: 'transparent',
                        },
                      },
                      '& .MuiInputBase-input.Mui-disabled': {
                        color: '#1a1a1a',
                        WebkitTextFillColor: '#1a1a1a',
                      },
                    }}
                  />
                </Box>
              </Grid>

              <Grid item xs={12} md={6}>
                <Box>
                  <Typography variant="caption" sx={{ 
                    color: '#9ca3af', 
                    fontSize: '0.75rem',
                    fontWeight: 500,
                    textTransform: 'uppercase',
                    letterSpacing: '0.05em',
                    mb: 1,
                    display: 'block'
                  }}>
                    推荐码
                  </Typography>
                  <TextField
                    fullWidth
                    value={userInfo?.referral_code || user?.referral_code || ''}
                    disabled
                    variant="outlined"
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        backgroundColor: 'transparent',
                        '& fieldset': {
                          borderColor: 'transparent',
                        },
                      },
                      '& .MuiInputBase-input.Mui-disabled': {
                        color: '#1a1a1a',
                        WebkitTextFillColor: '#1a1a1a',
                      },
                    }}
                  />
                </Box>
              </Grid>
            </Grid>

            {isEditing && (
              <Box sx={{ display: 'flex', gap: 2, mt: 4, justifyContent: 'flex-end' }}>
                <Button
                  variant="outlined"
                  onClick={handleCancel}
                  disabled={isSaving}
                  sx={{
                    borderColor: '#e5e7eb',
                    color: '#6b7280',
                    borderRadius: 1.5,
                    textTransform: 'none',
                    fontWeight: 600,
                    px: 3,
                    '&:hover': {
                      borderColor: '#9ca3af',
                      backgroundColor: '#f9fafb'
                    }
                  }}
                >
                  取消
                </Button>
                <Button
                  variant="contained"
                  onClick={handleSave}
                  disabled={isSaving}
                  startIcon={isSaving ? <CircularProgress size={16} /> : <Save />}
                  sx={{
                    backgroundColor: '#1a1a1a',
                    color: 'white',
                    borderRadius: 1.5,
                    textTransform: 'none',
                    fontWeight: 600,
                    px: 3,
                    boxShadow: 'none',
                    '&:hover': {
                      backgroundColor: '#374151',
                      boxShadow: 'none',
                    }
                  }}
                >
                  {isSaving ? '保存中...' : '保存更改'}
                </Button>
              </Box>
            )}
        </Box>

        {/* 账户安全区域 */}
        <Box sx={{ py: 4, borderTop: '1px solid #f0f0f0' }}>
            <Typography variant="h5" sx={{
              fontWeight: 600,
              color: '#1a1a1a',
              mb: 3
            }}>
              账户安全
            </Typography>

            <Grid container spacing={3}>
              <Grid item xs={12} md={8}>
                <Box sx={{
                  p: 3,
                  border: '1px solid #e5e7eb',
                  borderRadius: 2,
                  backgroundColor: 'white',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between'
                }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Box sx={{
                      width: 44,
                      height: 44,
                      borderRadius: '12px',
                      backgroundColor: '#eff6ff',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}>
                      <VpnKey sx={{ color: '#3b82f6', fontSize: 22 }} />
                    </Box>
                    <Box>
                      <Typography variant="body1" sx={{
                        fontWeight: 600,
                        color: '#1a1a1a',
                        mb: 0.5
                      }}>
                        登录密码
                      </Typography>
                      <Typography variant="body2" sx={{
                        color: '#6b7280',
                        fontSize: '0.875rem'
                      }}>
                        定期更换密码提高安全性
                      </Typography>
                    </Box>
                  </Box>
                  <Button
                    variant="text"
                    size="small"
                    onClick={() => setPasswordDialog(true)}
                    sx={{
                      color: '#6b7280',
                      textTransform: 'none',
                      fontWeight: 600,
                      '&:hover': {
                        backgroundColor: '#f9fafb'
                      }
                    }}
                  >
                    修改
                  </Button>
                </Box>
              </Grid>

              <Grid item xs={12} md={4}>
                <Box sx={{
                  p: 3,
                  backgroundColor: '#f0f9ff',
                  borderRadius: 2,
                  border: '1px solid #bfdbfe'
                }}>
                  <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1.5 }}>
                    <Info sx={{ color: '#3b82f6', fontSize: 20, mt: 0.2 }} />
                    <Box>
                      <Typography variant="body2" sx={{
                        fontWeight: 600,
                        color: '#1e40af',
                        mb: 1
                      }}>
                        密码安全提示
                      </Typography>
                      <Typography variant="body2" sx={{
                        color: '#3730a3',
                        fontSize: '0.813rem',
                        lineHeight: 1.6
                      }}>
                        • 建议每3个月更换一次密码<br/>
                        • 使用8位以上包含字母数字组合<br/>
                        • 避免使用生日、姓名等个人信息<br/>
                        • 定期更换密码可有效保护账户安全
                      </Typography>
                    </Box>
                  </Box>
                </Box>
              </Grid>
            </Grid>
          </Box>

        {/* 账户信息区域 */}
        <Box sx={{ py: 4, borderTop: '1px solid #f0f0f0' }}>
            <Typography variant="h5" sx={{
              fontWeight: 600,
              color: '#1a1a1a',
              mb: 3
            }}>
              账户信息
            </Typography>

            <Grid container spacing={3}>
              <Grid item xs={12} sm={6}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Box sx={{
                    width: 44,
                    height: 44,
                    borderRadius: '12px',
                    backgroundColor: '#f3f4f6',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <CalendarToday sx={{ color: '#6b7280', fontSize: 22 }} />
                  </Box>
                  <Box>
                    <Typography variant="caption" sx={{ 
                      color: '#9ca3af', 
                      fontSize: '0.75rem',
                      fontWeight: 500,
                      textTransform: 'uppercase',
                      letterSpacing: '0.05em'
                    }}>
                      注册时间
                    </Typography>
                    <Typography variant="h6" sx={{ 
                      color: '#1a1a1a',
                      fontWeight: 600,
                      fontSize: '1rem',
                      mt: 0.5
                    }}>
                      {formatDate(userInfo?.created_at)}
                    </Typography>
                  </Box>
                </Box>
              </Grid>

              <Grid item xs={12} sm={6}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Box sx={{
                    width: 44,
                    height: 44,
                    borderRadius: '12px',
                    backgroundColor: '#f3f4f6',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <Schedule sx={{ color: '#6b7280', fontSize: 22 }} />
                  </Box>
                  <Box>
                    <Typography variant="caption" sx={{ 
                      color: '#9ca3af', 
                      fontSize: '0.75rem',
                      fontWeight: 500,
                      textTransform: 'uppercase',
                      letterSpacing: '0.05em'
                    }}>
                      最后登录
                    </Typography>
                    <Typography variant="h6" sx={{ 
                      color: '#1a1a1a',
                      fontWeight: 600,
                      fontSize: '1rem',
                      mt: 0.5
                    }}>
                      {formatDate(userInfo?.last_login_at)}
                    </Typography>
                  </Box>
                </Box>
              </Grid>
            </Grid>
          </Box>
        </Container>
      </Box>

      {/* 密码修改对话框 */}
      <Dialog
        open={passwordDialog}
        onClose={() => {
          setPasswordDialog(false);
          setError('');
          setSuccess('');
          setPasswordData({
            current_password: '',
            new_password: '',
            confirm_password: '',
          });
        }}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 2,
          }
        }}
      >
        <DialogTitle sx={{ pb: 2, fontWeight: 600 }}>修改密码</DialogTitle>
        <DialogContent>
          {error && (
            <Alert severity="error" sx={{ mb: 2, borderRadius: 1.5 }} onClose={() => setError('')}>
              {error}
            </Alert>
          )}
          {success && (
            <Alert severity="success" sx={{ mb: 2, borderRadius: 1.5 }} onClose={() => setSuccess('')}>
              {success}
            </Alert>
          )}

          <TextField
            fullWidth
            type="password"
            label="当前密码"
            value={passwordData.current_password}
            onChange={(e) => handlePasswordChange('current_password', e.target.value)}
            disabled={isSaving}
            sx={{ mb: 2 }}
          />
          <TextField
            fullWidth
            type="password"
            label="新密码"
            value={passwordData.new_password}
            onChange={(e) => handlePasswordChange('new_password', e.target.value)}
            disabled={isSaving}
            sx={{ mb: 2 }}
            helperText="密码长度至少8位"
          />
          <TextField
            fullWidth
            type="password"
            label="确认新密码"
            value={passwordData.confirm_password}
            onChange={(e) => handlePasswordChange('confirm_password', e.target.value)}
            disabled={isSaving}
          />
        </DialogContent>
        <DialogActions sx={{ px: 3, pb: 3 }}>
          <Button
            onClick={() => {
              setPasswordDialog(false);
              setError('');
              setSuccess('');
              setPasswordData({
                current_password: '',
                new_password: '',
                confirm_password: '',
              });
            }}
            disabled={isSaving}
            sx={{
              color: '#6b7280',
              textTransform: 'none',
              fontWeight: 600
            }}
          >
            取消
          </Button>
          <Button
            onClick={handlePasswordSave}
            variant="contained"
            disabled={isSaving}
            startIcon={isSaving ? <CircularProgress size={16} /> : null}
            sx={{
              backgroundColor: '#1a1a1a',
              color: 'white',
              textTransform: 'none',
              fontWeight: 600,
              boxShadow: 'none',
              '&:hover': {
                backgroundColor: '#374151',
                boxShadow: 'none',
              }
            }}
          >
            {isSaving ? '修改中...' : '确认修改'}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}

export default UserProfile;