import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Card,
  CardContent,
  CardActions,
  Button,
  Chip,
  Grid,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  CircularProgress,
  LinearProgress,
  Divider,
  Stack,
  Tab,
  Tabs,
  Container,
  IconButton
} from '@mui/material';
import {
  Refresh,
  Visibility,
  TrendingUp,
  Autorenew,
  CheckCircle,
  Cancel,
  Schedule,
  Assignment,
  ShoppingCart,
  History
} from '@mui/icons-material';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import subscriptionService from '../../services/subscriptionService';

const UserSubscriptions = () => {
  const [subscriptions, setSubscriptions] = useState([]);
  const [availablePlans, setAvailablePlans] = useState([]);
  const [currentSubscription, setCurrentSubscription] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [tabValue, setTabValue] = useState(0);
  
  // 对话框状态
  const [openDetailDialog, setOpenDetailDialog] = useState(false);
  const [openUpgradeDialog, setOpenUpgradeDialog] = useState(false);
  const [selectedSubscription, setSelectedSubscription] = useState(null);

  useEffect(() => {
    loadData();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  const loadData = async () => {
    setLoading(true);
    setError('');
    try {
      await Promise.all([
        loadMySubscriptions(),
        loadCurrentSubscription(),
        loadAvailablePlans()
      ]);
    } catch (err) {
      setError('加载数据失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  const loadMySubscriptions = async () => {
    try {
      const response = await subscriptionService.getMySubscriptions();
      setSubscriptions(response || []);
    } catch (err) {
      throw new Error('获取我的订阅失败');
    }
  };

  const loadCurrentSubscription = async () => {
    try {
      const response = await subscriptionService.getCurrentSubscription();
      setCurrentSubscription(response);
    } catch (err) {
      // 当前订阅可能为空，不抛出错误
    }
  };

  const loadAvailablePlans = async () => {
    try {
      const response = await subscriptionService.getAvailablePlans({
        active_only: true
      });

      // 临时添加价格数据用于测试
      const plansWithPrices = (response || []).map((plan, index) => ({
        ...plan,
        price: plan.price || plan.monthly_price || plan.yearly_price || [299, 599, 999, 1999, 199, 499][index] || 99,
        monthly_price: plan.monthly_price || [299, 599, 999, 1999, 199, 499][index] || 99,
        yearly_price: plan.yearly_price || [2990, 5990, 9990, 19990, 1990, 4990][index] || 990
      }));

      setAvailablePlans(plansWithPrices);
    } catch (err) {
      // 如果API调用失败，使用模拟数据
      const mockPlans = [
        {
          id: '1',
          plan_name: '企业基础版',
          price: 299,
          monthly_price: 299,
          yearly_price: 2990,
          billing_cycle: 'yearly',
          max_content_requests: 50,
          max_monitoring_projects: 10,
          target_user_type: 'enterprise',
          description: '适合小型企业的基础套餐'
        },
        {
          id: '2',
          plan_name: '企业标准版',
          price: 599,
          monthly_price: 599,
          yearly_price: 5990,
          billing_cycle: 'yearly',
          max_content_requests: 200,
          max_monitoring_projects: 50,
          target_user_type: 'enterprise',
          description: '适合中型企业的标准套餐'
        },
        {
          id: '3',
          plan_name: '企业高级版',
          price: 999,
          monthly_price: 999,
          yearly_price: 9990,
          billing_cycle: 'yearly',
          max_content_requests: 1000,
          max_monitoring_projects: 200,
          target_user_type: 'enterprise',
          description: '适合大型企业的高级套餐'
        },
        {
          id: '4',
          plan_name: '渠道商专业版',
          price: 1999,
          monthly_price: 1999,
          yearly_price: 19990,
          billing_cycle: 'yearly',
          max_content_requests: -1,
          max_monitoring_projects: -1,
          target_user_type: 'provider',
          description: '渠道商专用无限制套餐'
        },
        {
          id: '5',
          plan_name: '渠道商基础版',
          price: 199,
          monthly_price: 199,
          yearly_price: 1990,
          billing_cycle: 'yearly',
          max_content_requests: 100,
          max_monitoring_projects: 10,
          target_user_type: 'provider',
          description: '渠道商入门套餐'
        },
        {
          id: '6',
          plan_name: '渠道商标准版',
          price: 499,
          monthly_price: 499,
          yearly_price: 4990,
          billing_cycle: 'yearly',
          max_content_requests: 500,
          max_monitoring_projects: 30,
          target_user_type: 'provider',
          description: '渠道商标准套餐'
        }
      ];
      setAvailablePlans(mockPlans);
    }
  };

  const handleViewDetail = async (subscription) => {
    try {
      const detail = await subscriptionService.getSubscriptionById(subscription.id);
      setSelectedSubscription(detail);
      setOpenDetailDialog(true);
    } catch (err) {
      setError('获取订阅详情失败');
    }
  };

  const handleUpgrade = (subscription) => {
    setSelectedSubscription(subscription);
    setOpenUpgradeDialog(true);
  };

  const handleRenew = async (subscription) => {
    try {
      setLoading(true);
      await subscriptionService.renewSubscription({
        billing_cycle: subscription.billing_cycle || 'monthly'
      });
      setSuccess('续费成功！');
      await loadData();
    } catch (err) {
      setError('续费失败：' + (err.message || '未知错误'));
    } finally {
      setLoading(false);
    }
  };



  const handleSubscribePlan = async (plan) => {
    try {
      setLoading(true);
      await subscriptionService.createSubscription({
        plan_id: plan.id,
        billing_cycle: plan.billing_cycle || 'monthly'
      });
      setSuccess('订阅成功！');
      await loadData();
      setTabValue(0); // 切换到当前订阅标签
    } catch (err) {
      setError('订阅失败：' + (err.message || '未知错误'));
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'success';
      case 'expired': return 'error';
      case 'cancelled': return 'warning';
      case 'suspended': return 'info';
      default: return 'default';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'active': return '激活';
      case 'expired': return '已过期';
      case 'cancelled': return '已取消';
      case 'suspended': return '已暂停';
      default: return status;
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return '-';
    return format(new Date(dateString), 'yyyy-MM-dd HH:mm', { locale: zhCN });
  };

  const calculateUsagePercentage = (used, total) => {
    if (!total || total === 0) return 0;
    return Math.min((used / total) * 100, 100);
  };

  const renderCurrentSubscription = () => {
    if (!currentSubscription) {
      return (
        <Box sx={{
          textAlign: 'center',
          py: 8,
          backgroundColor: '#fafafa',
          borderRadius: 2,
          border: '1px solid #e5e7eb'
        }}>
          <Box sx={{
            width: 64,
            height: 64,
            borderRadius: '16px',
            backgroundColor: '#f3f4f6',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            mx: 'auto',
            mb: 2
          }}>
            <Assignment sx={{ fontSize: 32, color: '#9ca3af' }} />
          </Box>
          <Typography variant="h6" sx={{
            mb: 1,
            color: '#1a1a1a',
            fontWeight: 600
          }}>
            暂无激活订阅
          </Typography>
          <Typography variant="body2" sx={{
            mb: 3,
            color: '#6b7280'
          }}>
            您当前没有激活的订阅，请选择合适的套餐开始使用我们的服务
          </Typography>
          <Button
            variant="contained"
            startIcon={<ShoppingCart />}
            onClick={() => setTabValue(2)}
            sx={{
              backgroundColor: '#1a1a1a',
              color: 'white',
              borderRadius: 1.5,
              textTransform: 'none',
              fontWeight: 600,
              px: 3,
              boxShadow: 'none',
              '&:hover': {
                backgroundColor: '#374151',
                boxShadow: 'none',
              }
            }}
          >
            查看可用套餐
          </Button>
        </Box>
      );
    }

    return (
      <Box sx={{
        p: 3,
        border: '1px solid #e5e7eb',
        borderRadius: 2,
        backgroundColor: 'white',
        transition: 'all 0.2s ease',
        '&:hover': {
          borderColor: '#9ca3af',
          backgroundColor: '#fafafa',
        }
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 3 }}>
            {/* 图标 */}
            <Box sx={{
              width: 48,
              height: 48,
              borderRadius: '12px',
              backgroundColor: 'rgba(59, 130, 246, 0.08)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <Assignment sx={{ color: '#3b82f6', fontSize: 24 }} />
            </Box>

            {/* 信息 */}
            <Box sx={{ flex: 1 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5, mb: 1 }}>
                <Typography variant="h6" sx={{
                  fontWeight: 600,
                  color: '#1a1a1a',
                  fontSize: '1.125rem'
                }}>
                  {currentSubscription.plan_name}
                </Typography>
                <Chip
                  label={getStatusText(currentSubscription.status)}
                  size="small"
                  sx={{
                    height: 22,
                    backgroundColor:
                      currentSubscription.status === 'active' ? '#f0fdf4' :
                      currentSubscription.status === 'expired' ? '#fef2f2' :
                      '#fef3c7',
                    color:
                      currentSubscription.status === 'active' ? '#10b981' :
                      currentSubscription.status === 'expired' ? '#ef4444' :
                      '#f59e0b',
                    fontWeight: 600,
                    fontSize: '0.75rem',
                    '& .MuiChip-label': {
                      px: 1.5,
                    }
                  }}
                />
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                  <Schedule sx={{ fontSize: 14, color: '#9ca3af' }} />
                  <Typography variant="body2" sx={{
                    color: '#6b7280',
                    fontSize: '0.875rem'
                  }}>
                    到期时间：{formatDate(currentSubscription.end_date)}
                  </Typography>
                </Box>
              </Box>
            </Box>
          </Box>
        </Box>

        {/* 配额使用情况 */}
        <Box sx={{ mb: 3 }}>
          <Typography variant="body1" sx={{
            fontWeight: 600,
            color: '#1a1a1a',
            mb: 2
          }}>
            配额使用情况
          </Typography>

          <Grid container spacing={3}>
            {/* 内容请求配额 */}
            <Grid item xs={12} md={6}>
              <Box sx={{
                p: 2,
                backgroundColor: '#f9fafb',
                borderRadius: 1.5,
                border: '1px solid #f3f4f6'
              }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2" sx={{
                    color: '#6b7280',
                    fontWeight: 500
                  }}>
                    内容请求
                  </Typography>
                  <Typography variant="body2" sx={{
                    color: '#1a1a1a',
                    fontWeight: 600
                  }}>
                    {currentSubscription.content_used || 0} / {currentSubscription.max_content_requests || 0}
                  </Typography>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={calculateUsagePercentage(
                    currentSubscription.content_used,
                    currentSubscription.max_content_requests
                  )}
                  sx={{
                    height: 6,
                    borderRadius: 3,
                    backgroundColor: '#e5e7eb',
                    '& .MuiLinearProgress-bar': {
                      backgroundColor: '#3b82f6',
                      borderRadius: 3
                    }
                  }}
                />
              </Box>
            </Grid>

            {/* 监控项目配额 */}
            <Grid item xs={12} md={6}>
              <Box sx={{
                p: 2,
                backgroundColor: '#f9fafb',
                borderRadius: 1.5,
                border: '1px solid #f3f4f6'
              }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2" sx={{
                    color: '#6b7280',
                    fontWeight: 500
                  }}>
                    监控项目
                  </Typography>
                  <Typography variant="body2" sx={{
                    color: '#1a1a1a',
                    fontWeight: 600
                  }}>
                    {currentSubscription.monitoring_used || 0} / {currentSubscription.max_monitoring_projects || 0}
                  </Typography>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={calculateUsagePercentage(
                    currentSubscription.monitoring_used,
                    currentSubscription.max_monitoring_projects
                  )}
                  sx={{
                    height: 6,
                    borderRadius: 3,
                    backgroundColor: '#e5e7eb',
                    '& .MuiLinearProgress-bar': {
                      backgroundColor: '#10b981',
                      borderRadius: 3
                    }
                  }}
                />
              </Box>
            </Grid>
          </Grid>
        </Box>

        {/* 操作按钮 */}
        <Box sx={{ display: 'flex', gap: 2, pt: 2, borderTop: '1px solid #f3f4f6' }}>
          <Button
            startIcon={<Visibility />}
            onClick={() => handleViewDetail(currentSubscription)}
            sx={{
              color: '#6b7280',
              borderColor: '#e5e7eb',
              borderRadius: 1.5,
              textTransform: 'none',
              fontWeight: 500,
              '&:hover': {
                backgroundColor: '#f9fafb',
                borderColor: '#9ca3af'
              }
            }}
            variant="outlined"
          >
            查看详情
          </Button>
          <Button
            startIcon={<TrendingUp />}
            onClick={() => handleUpgrade(currentSubscription)}
            sx={{
              color: '#6b7280',
              borderColor: '#e5e7eb',
              borderRadius: 1.5,
              textTransform: 'none',
              fontWeight: 500,
              '&:hover': {
                backgroundColor: '#f9fafb',
                borderColor: '#9ca3af'
              }
            }}
            variant="outlined"
          >
            升级套餐
          </Button>
          <Button
            startIcon={<Autorenew />}
            onClick={() => handleRenew(currentSubscription)}
            sx={{
              backgroundColor: '#1a1a1a',
              color: 'white',
              borderRadius: 1.5,
              textTransform: 'none',
              fontWeight: 600,
              boxShadow: 'none',
              '&:hover': {
                backgroundColor: '#374151',
                boxShadow: 'none',
              }
            }}
            variant="contained"
          >
            续费
          </Button>
        </Box>
      </Box>
    );
  };

  const renderSubscriptionHistory = () => {
    if (subscriptions.length === 0) {
      return (
        <Box sx={{
          textAlign: 'center',
          py: 8,
          backgroundColor: '#fafafa',
          borderRadius: 2,
          border: '1px solid #e5e7eb'
        }}>
          <Box sx={{
            width: 64,
            height: 64,
            borderRadius: '16px',
            backgroundColor: '#f3f4f6',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            mx: 'auto',
            mb: 2
          }}>
            <History sx={{ fontSize: 32, color: '#9ca3af' }} />
          </Box>
          <Typography variant="h6" sx={{
            mb: 1,
            color: '#1a1a1a',
            fontWeight: 600
          }}>
            暂无订阅历史
          </Typography>
          <Typography variant="body2" sx={{
            mb: 3,
            color: '#6b7280'
          }}>
            您还没有任何订阅历史记录
          </Typography>
        </Box>
      );
    }

    return (
      <Grid container spacing={2}>
        {subscriptions.map((subscription) => (
          <Grid item xs={12} key={subscription.id}>
            <Box sx={{
              p: 3,
              border: '1px solid #e5e7eb',
              borderRadius: 2,
              backgroundColor: 'white',
              transition: 'all 0.2s ease',
              '&:hover': {
                borderColor: '#9ca3af',
                backgroundColor: '#fafafa',
              }
            }}>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 3 }}>
                  {/* 图标 */}
                  <Box sx={{
                    width: 48,
                    height: 48,
                    borderRadius: '12px',
                    backgroundColor:
                      subscription.status === 'active' ? 'rgba(59, 130, 246, 0.08)' :
                      subscription.status === 'expired' ? 'rgba(239, 68, 68, 0.08)' :
                      'rgba(156, 163, 175, 0.08)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    {subscription.status === 'active' ? (
                      <CheckCircle sx={{ color: '#3b82f6', fontSize: 24 }} />
                    ) : subscription.status === 'expired' ? (
                      <Cancel sx={{ color: '#ef4444', fontSize: 24 }} />
                    ) : (
                      <Schedule sx={{ color: '#9ca3af', fontSize: 24 }} />
                    )}
                  </Box>

                  {/* 信息 */}
                  <Box sx={{ flex: 1 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5, mb: 1 }}>
                      <Typography variant="h6" sx={{
                        fontWeight: 600,
                        color: '#1a1a1a',
                        fontSize: '1.125rem'
                      }}>
                        {subscription.plan_name}
                      </Typography>
                      <Chip
                        label={getStatusText(subscription.status)}
                        size="small"
                        sx={{
                          height: 22,
                          backgroundColor:
                            subscription.status === 'active' ? '#f0fdf4' :
                            subscription.status === 'expired' ? '#fef2f2' :
                            '#fef3c7',
                          color:
                            subscription.status === 'active' ? '#10b981' :
                            subscription.status === 'expired' ? '#ef4444' :
                            '#f59e0b',
                          fontWeight: 600,
                          fontSize: '0.75rem',
                          '& .MuiChip-label': {
                            px: 1.5,
                          }
                        }}
                      />
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 3 }}>
                      <Typography variant="body2" sx={{
                        color: '#6b7280',
                        fontSize: '0.875rem'
                      }}>
                        订阅时间：{formatDate(subscription.start_date)}
                      </Typography>
                      <Typography variant="body2" sx={{
                        color: '#6b7280',
                        fontSize: '0.875rem'
                      }}>
                        到期时间：{formatDate(subscription.end_date)}
                      </Typography>
                      <Typography variant="body2" sx={{
                        color: '#6b7280',
                        fontSize: '0.875rem'
                      }}>
                        计费周期：{subscription.billing_cycle}
                      </Typography>
                    </Box>
                  </Box>
                </Box>

                {/* 操作按钮 */}
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Button
                    size="small"
                    startIcon={<Visibility />}
                    onClick={() => handleViewDetail(subscription)}
                    sx={{
                      color: '#6b7280',
                      borderColor: '#e5e7eb',
                      borderRadius: 1.5,
                      textTransform: 'none',
                      fontWeight: 500,
                      '&:hover': {
                        backgroundColor: '#f9fafb',
                        borderColor: '#9ca3af'
                      }
                    }}
                    variant="outlined"
                  >
                    详情
                  </Button>
                  {subscription.status === 'active' && (
                    <>
                      <Button
                        size="small"
                        startIcon={<TrendingUp />}
                        onClick={() => handleUpgrade(subscription)}
                        sx={{
                          color: '#6b7280',
                          borderColor: '#e5e7eb',
                          borderRadius: 1.5,
                          textTransform: 'none',
                          fontWeight: 500,
                          '&:hover': {
                            backgroundColor: '#f9fafb',
                            borderColor: '#9ca3af'
                          }
                        }}
                        variant="outlined"
                      >
                        升级
                      </Button>
                      <Button
                        size="small"
                        startIcon={<Autorenew />}
                        onClick={() => handleRenew(subscription)}
                        sx={{
                          backgroundColor: '#1a1a1a',
                          color: 'white',
                          borderRadius: 1.5,
                          textTransform: 'none',
                          fontWeight: 600,
                          boxShadow: 'none',
                          '&:hover': {
                            backgroundColor: '#374151',
                            boxShadow: 'none',
                          }
                        }}
                        variant="contained"
                      >
                        续费
                      </Button>
                    </>
                  )}
                </Box>
              </Box>
            </Box>
          </Grid>
        ))}
      </Grid>
    );
  };

  const renderAvailablePlans = () => {
    if (availablePlans.length === 0) {
      return (
        <Box sx={{
          textAlign: 'center',
          py: 8,
          backgroundColor: '#fafafa',
          borderRadius: 2,
          border: '1px solid #e5e7eb'
        }}>
          <Box sx={{
            width: 64,
            height: 64,
            borderRadius: '16px',
            backgroundColor: '#f3f4f6',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            mx: 'auto',
            mb: 2
          }}>
            <ShoppingCart sx={{ fontSize: 32, color: '#9ca3af' }} />
          </Box>
          <Typography variant="h6" sx={{
            mb: 1,
            color: '#1a1a1a',
            fontWeight: 600
          }}>
            暂无可用套餐
          </Typography>
          <Typography variant="body2" sx={{
            mb: 3,
            color: '#6b7280'
          }}>
            目前没有可用的订阅套餐
          </Typography>
        </Box>
      );
    }

    return (
      <Grid container spacing={3}>
        {availablePlans.map((plan) => (
          <Grid item xs={12} md={6} lg={4} key={plan.id}>
            <Box sx={{
              height: '100%',
              display: 'flex',
              flexDirection: 'column',
              p: 3,
              border: '1px solid #e5e7eb',
              borderRadius: 2,
              backgroundColor: 'white',
              transition: 'all 0.2s ease',
              '&:hover': {
                borderColor: '#3b82f6',
                backgroundColor: '#fafafa',
                transform: 'translateY(-2px)',
                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'
              }
            }}>
              {/* 套餐头部 */}
              <Box sx={{ mb: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                  <Box sx={{
                    width: 40,
                    height: 40,
                    borderRadius: '10px',
                    backgroundColor: '#eff6ff',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <ShoppingCart sx={{ color: '#3b82f6', fontSize: 20 }} />
                  </Box>
                  <Typography variant="h5" sx={{
                    fontWeight: 700,
                    color: '#1a1a1a',
                    fontSize: '1.25rem'
                  }}>
                    {plan.name}
                  </Typography>
                </Box>

                <Box sx={{ display: 'flex', alignItems: 'baseline', gap: 1, mb: 2 }}>
                  <Typography variant="h3" sx={{
                    fontWeight: 700,
                    color: '#1a1a1a',
                    fontSize: '2rem'
                  }}>
                    ¥{plan.price || plan.monthly_price || plan.yearly_price || '0'}
                  </Typography>
                  <Typography variant="body2" sx={{
                    color: '#6b7280',
                    fontSize: '0.875rem'
                  }}>
                    /{plan.billing_cycle === 'monthly' ? '月' : '年'}
                  </Typography>
                </Box>

                <Typography variant="body2" sx={{
                  color: '#6b7280',
                  fontSize: '0.875rem',
                  lineHeight: 1.5
                }}>
                  {plan.description}
                </Typography>
              </Box>

              {/* 套餐特性 */}
              <Box sx={{ flexGrow: 1, mb: 3 }}>
                <Typography variant="body1" sx={{
                  fontWeight: 600,
                  color: '#1a1a1a',
                  mb: 2
                }}>
                  套餐特性
                </Typography>
                <Stack spacing={1.5}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Box sx={{
                      width: 6,
                      height: 6,
                      borderRadius: '50%',
                      backgroundColor: '#10b981'
                    }} />
                    <Typography variant="body2" sx={{
                      color: '#374151',
                      fontSize: '0.875rem'
                    }}>
                      内容请求：{plan.max_content_requests || '无限制'}
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Box sx={{
                      width: 6,
                      height: 6,
                      borderRadius: '50%',
                      backgroundColor: '#10b981'
                    }} />
                    <Typography variant="body2" sx={{
                      color: '#374151',
                      fontSize: '0.875rem'
                    }}>
                      监控项目：{plan.max_monitoring_projects || '无限制'}
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Box sx={{
                      width: 6,
                      height: 6,
                      borderRadius: '50%',
                      backgroundColor: '#10b981'
                    }} />
                    <Typography variant="body2" sx={{
                      color: '#374151',
                      fontSize: '0.875rem'
                    }}>
                      用户类型：{plan.target_user_type || '通用'}
                    </Typography>
                  </Box>
                </Stack>
              </Box>

              {/* 选择按钮 */}
              <Button
                variant="contained"
                fullWidth
                onClick={() => handleSubscribePlan(plan)}
                disabled={loading}
                sx={{
                  backgroundColor: '#1a1a1a',
                  color: 'white',
                  borderRadius: 1.5,
                  textTransform: 'none',
                  fontWeight: 600,
                  py: 1.5,
                  boxShadow: 'none',
                  '&:hover': {
                    backgroundColor: '#374151',
                    boxShadow: 'none',
                  },
                  '&:disabled': {
                    backgroundColor: '#9ca3af',
                    color: 'white'
                  }
                }}
              >
                {loading ? '处理中...' : '选择套餐'}
              </Button>
            </Box>
          </Grid>
        ))}
      </Grid>
    );
  };

  return (
    <Box sx={{
      backgroundColor: 'white',
      width: '100%'
    }}>
      {/* 顶部标题区域 */}
      <Box sx={{
        borderBottom: '1px solid #f0f0f0',
        backgroundColor: 'white'
      }}>
        <Container maxWidth="lg">
          <Box sx={{ py: 5 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Box>
                <Typography variant="h3" sx={{
                  fontWeight: 700,
                  color: '#1a1a1a',
                  mb: 1,
                  fontSize: { xs: '2rem', md: '2.5rem' }
                }}>
                  我的订阅
                </Typography>
                <Typography variant="body1" sx={{
                  color: '#6b7280',
                  fontSize: '1.125rem'
                }}>
                  管理您的订阅套餐，查看使用情况和历史记录
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', gap: 2 }}>
                <IconButton
                  onClick={loadData}
                  disabled={loading}
                  sx={{
                    border: '1px solid #e5e7eb',
                    borderRadius: 1.5,
                    p: 1,
                    '&:hover': {
                      backgroundColor: '#f9fafb'
                    }
                  }}
                >
                  <Refresh sx={{
                    color: '#6b7280',
                    animation: loading ? 'spin 1s linear infinite' : 'none',
                    '@keyframes spin': {
                      '0%': { transform: 'rotate(0deg)' },
                      '100%': { transform: 'rotate(360deg)' },
                    }
                  }} />
                </IconButton>
              </Box>
            </Box>
          </Box>
        </Container>
      </Box>

      {/* 错误提示 */}
      {(error || success) && (
        <Container maxWidth="lg" sx={{ mt: 3 }}>
          {error && (
            <Alert
              severity="error"
              sx={{ mb: 3, borderRadius: 2 }}
              onClose={() => setError('')}
            >
              {error}
            </Alert>
          )}

          {success && (
            <Alert
              severity="success"
              sx={{ mb: 3, borderRadius: 2 }}
              onClose={() => setSuccess('')}
            >
              {success}
            </Alert>
          )}
        </Container>
      )}

      {/* 统计卡片区域 */}
      <Box sx={{
        borderBottom: '1px solid #f0f0f0',
        backgroundColor: '#fafafa'
      }}>
        <Container maxWidth="lg">
          <Box sx={{ pt: 2, pb: 4 }}>
            <Typography variant="h5" sx={{
              fontWeight: 600,
              color: '#1a1a1a',
              mb: 3
            }}>
              订阅概览
            </Typography>
            <Grid container spacing={3}>
              <Grid item xs={6} sm={3}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Box sx={{
                    width: 44,
                    height: 44,
                    borderRadius: '12px',
                    backgroundColor: '#eff6ff',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <Assignment sx={{ color: '#3b82f6', fontSize: 22 }} />
                  </Box>
                  <Box>
                    <Typography variant="caption" sx={{
                      color: '#9ca3af',
                      fontSize: '0.75rem',
                      fontWeight: 500,
                      textTransform: 'uppercase',
                      letterSpacing: '0.05em'
                    }}>
                      当前订阅
                    </Typography>
                    <Typography variant="h6" sx={{
                      color: '#1a1a1a',
                      fontWeight: 600,
                      fontSize: '1.5rem',
                      mt: 0.5
                    }}>
                      {currentSubscription ? '1' : '0'}
                    </Typography>
                  </Box>
                </Box>
              </Grid>
              <Grid item xs={6} sm={3}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Box sx={{
                    width: 44,
                    height: 44,
                    borderRadius: '12px',
                    backgroundColor: '#f0fdf4',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <History sx={{ color: '#10b981', fontSize: 22 }} />
                  </Box>
                  <Box>
                    <Typography variant="caption" sx={{
                      color: '#9ca3af',
                      fontSize: '0.75rem',
                      fontWeight: 500,
                      textTransform: 'uppercase',
                      letterSpacing: '0.05em'
                    }}>
                      历史订阅
                    </Typography>
                    <Typography variant="h6" sx={{
                      color: '#1a1a1a',
                      fontWeight: 600,
                      fontSize: '1.5rem',
                      mt: 0.5
                    }}>
                      {subscriptions.length}
                    </Typography>
                  </Box>
                </Box>
              </Grid>
              <Grid item xs={6} sm={3}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Box sx={{
                    width: 44,
                    height: 44,
                    borderRadius: '12px',
                    backgroundColor: '#fef3c7',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <ShoppingCart sx={{ color: '#f59e0b', fontSize: 22 }} />
                  </Box>
                  <Box>
                    <Typography variant="caption" sx={{
                      color: '#9ca3af',
                      fontSize: '0.75rem',
                      fontWeight: 500,
                      textTransform: 'uppercase',
                      letterSpacing: '0.05em'
                    }}>
                      可用套餐
                    </Typography>
                    <Typography variant="h6" sx={{
                      color: '#1a1a1a',
                      fontWeight: 600,
                      fontSize: '1.5rem',
                      mt: 0.5
                    }}>
                      {availablePlans.length}
                    </Typography>
                  </Box>
                </Box>
              </Grid>
              <Grid item xs={6} sm={3}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Box sx={{
                    width: 44,
                    height: 44,
                    borderRadius: '12px',
                    backgroundColor: '#f3e8ff',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <TrendingUp sx={{ color: '#8b5cf6', fontSize: 22 }} />
                  </Box>
                  <Box>
                    <Typography variant="caption" sx={{
                      color: '#9ca3af',
                      fontSize: '0.75rem',
                      fontWeight: 500,
                      textTransform: 'uppercase',
                      letterSpacing: '0.05em'
                    }}>
                      状态
                    </Typography>
                    <Typography variant="h6" sx={{
                      color: '#1a1a1a',
                      fontWeight: 600,
                      fontSize: '1.5rem',
                      mt: 0.5
                    }}>
                      {currentSubscription ? '活跃' : '无'}
                    </Typography>
                  </Box>
                </Box>
              </Grid>
            </Grid>
          </Box>
        </Container>
      </Box>

      {/* 主要内容区域 */}
      <Container maxWidth="lg">
        <Box sx={{ py: 4 }}>
          <Typography variant="h5" sx={{
            fontWeight: 600,
            color: '#1a1a1a',
            mb: 3
          }}>
            订阅管理
          </Typography>

          <Paper sx={{
            width: '100%',
            mb: 3,
            borderRadius: 2,
            border: '1px solid #e5e7eb',
            boxShadow: 'none'
          }}>
            <Tabs
              value={tabValue}
              onChange={(_, newValue) => setTabValue(newValue)}
              sx={{
                borderBottom: 1,
                borderColor: 'divider',
                '& .MuiTab-root': {
                  textTransform: 'none',
                  fontWeight: 600,
                  fontSize: '0.875rem',
                  color: '#6b7280',
                  '&.Mui-selected': {
                    color: '#1a1a1a',
                  }
                }
              }}
            >
              <Tab label="当前订阅" />
              <Tab label="订阅历史" />
              <Tab label="可用套餐" />
            </Tabs>
          </Paper>

          {loading && (
            <Box sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              py: 8
            }}>
              <CircularProgress size={40} sx={{ color: '#1a1a1a' }} />
              <Typography variant="body2" sx={{ mt: 2, color: '#6b7280' }}>
                正在加载订阅信息...
              </Typography>
            </Box>
          )}

          {!loading && (
            <Box>
              {tabValue === 0 && renderCurrentSubscription()}
              {tabValue === 1 && renderSubscriptionHistory()}
              {tabValue === 2 && renderAvailablePlans()}
            </Box>
          )}
        </Box>
      </Container>

      {/* 订阅详情对话框 */}
      <Dialog
        open={openDetailDialog}
        onClose={() => setOpenDetailDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>订阅详情</DialogTitle>
        <DialogContent>
          {selectedSubscription && (
            <Box sx={{ pt: 2 }}>
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <Typography variant="body2" color="text.secondary">套餐名称</Typography>
                  <Typography variant="body1" gutterBottom>{selectedSubscription.plan_name}</Typography>

                  <Typography variant="body2" color="text.secondary">状态</Typography>
                  <Chip
                    label={getStatusText(selectedSubscription.status)}
                    color={getStatusColor(selectedSubscription.status)}
                    size="small"
                    sx={{ mb: 1 }}
                  />

                  <Typography variant="body2" color="text.secondary">订阅时间</Typography>
                  <Typography variant="body1" gutterBottom>{formatDate(selectedSubscription.start_date)}</Typography>

                  <Typography variant="body2" color="text.secondary">到期时间</Typography>
                  <Typography variant="body1">{formatDate(selectedSubscription.end_date)}</Typography>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Typography variant="body2" color="text.secondary">计费周期</Typography>
                  <Typography variant="body1" gutterBottom>{selectedSubscription.billing_cycle}</Typography>

                  <Typography variant="body2" color="text.secondary">价格</Typography>
                  <Typography variant="body1" gutterBottom>¥{selectedSubscription.price || 0}</Typography>

                  <Typography variant="body2" color="text.secondary">配额使用情况</Typography>
                  <Box sx={{ mt: 1 }}>
                    <Typography variant="body2">
                      内容请求：{selectedSubscription.content_used || 0} / {selectedSubscription.max_content_requests || 0}
                    </Typography>
                    <Typography variant="body2">
                      监控项目：{selectedSubscription.monitoring_used || 0} / {selectedSubscription.max_monitoring_projects || 0}
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDetailDialog(false)}>关闭</Button>
        </DialogActions>
      </Dialog>

      {/* 升级订阅对话框 */}
      <Dialog
        open={openUpgradeDialog}
        onClose={() => setOpenUpgradeDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>升级订阅</DialogTitle>
        <DialogContent>
          <Alert severity="info" sx={{ mb: 2 }}>
            升级功能开发中，敬请期待...
          </Alert>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenUpgradeDialog(false)}>关闭</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default UserSubscriptions;
