import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>ton,
  Grid,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  CircularProgress,
  Container,
  Chip,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  Divider,
  FormHelperText,
} from '@mui/material';
import {
  Business,
  Store,
  Campaign,
  Send,
  Close,
  CheckCircle,
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import apiService from '../../services/api';
import FileUpload from '../../components/FileUpload';

function RoleApplication() {
  const { user } = useAuth();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedRole, setSelectedRole] = useState('');
  const [applicationReason, setApplicationReason] = useState('');
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // 企业用户表单
  const [enterpriseForm, setEnterpriseForm] = useState({
    company_name: '',
    company_name_en: '',
    company_code: '',
    legal_person: '',
    contact_phone: '',
    contact_email: '',
    headquarters_location: '',
    industry: '',
    company_size: '',
    founded_year: '',
    business_scope: '',
    company_description: '',
    official_website: '',
    business_license_url: '',
    other_files: [],
    social_media_links: {}
  });

  // 渠道商表单
  const [channelForm, setChannelForm] = useState({
    provider_name: '',
    provider_type: '',
    real_name: '',
    id_card_number: '',
    company_name: '',
    business_license: '',
    contact_phone: '',
    contact_email: '',
    contact_address: '',
    business_description: '',
    service_categories: [],
    platform_accounts: {},
    portfolio_urls: [],
    qualification_files: []
  });

  // 代理商表单
  const [agentForm, setAgentForm] = useState({
    agent_name: '',
    contact_phone: '',
    contact_email: '',
    office_address: '',
    agent_description: '',
    service_regions: [],
    specialties: []
  });

  // 枚举值定义
  const industryOptions = [
    '互联网/科技',
    '金融/保险',
    '电商/零售',
    '教育/培训',
    '医疗/健康',
    '房地产/建筑',
    '制造业',
    '文化/传媒',
    '旅游/酒店',
    '物流/运输',
    '咨询/服务',
    '政府/非营利',
    '其他'
  ];

  const companySizeOptions = [
    { value: 'startup', label: '创业公司 (1-50人)' },
    { value: 'small', label: '小型企业 (50-200人)' },
    { value: 'medium', label: '中型企业 (200-1000人)' },
    { value: 'large', label: '大型企业 (1000-5000人)' },
    { value: 'enterprise', label: '超大型企业 (5000人以上)' }
  ];

  const providerTypeOptions = [
    { value: 'individual', label: '个人' },
    { value: 'company', label: '公司' },
    { value: 'studio', label: '工作室' },
    { value: 'mcn', label: 'MCN机构' }
  ];

  const serviceCategoryOptions = [
    '内容创作',
    '视频制作',
    '图文设计',
    '营销推广',
    'SEO优化',
    '技术开发',
    '数据分析',
    '咨询服务'
  ];

  const serviceRegionOptions = [
    '全国',
    '华北地区',
    '华东地区',
    '华南地区',
    '华中地区',
    '西南地区',
    '西北地区',
    '东北地区',
    '港澳台地区',
    '海外地区'
  ];

  const specialtyOptions = [
    'SEO优化',
    '内容营销',
    '社交媒体营销',
    '搜索引擎营销',
    '电商推广',
    '品牌建设',
    '数据分析',
    '技术支持'
  ];

  // 角色信息配置
  const roleInfo = {
    enterprise_user: {
      name: '企业用户',
      description: '享受企业级服务，批量处理，专属支持',
      icon: <Business />,
      color: '#3b82f6',
      bgColor: 'rgba(59, 130, 246, 0.08)',
      benefits: ['企业级API接口', '批量内容处理', '专属客服支持', '定制化解决方案'],
    },
    channel_user: {
      name: '渠道商',
      description: '内容供应商，上传优质内容，获得收益',
      icon: <Store />,
      color: '#10b981',
      bgColor: 'rgba(16, 185, 129, 0.08)',
      benefits: ['内容上传权限', '收益分成机制', '数据分析工具', '推广支持'],
    },
    agent_user: {
      name: '代理商',
      description: '推广平台服务，发展用户，获得佣金',
      icon: <Campaign />,
      color: '#f59e0b',
      bgColor: 'rgba(245, 158, 11, 0.08)',
      benefits: ['推广链接生成', '多级佣金体系', '推广素材支持', '业绩统计分析'],
    },
  };

  // 处理URL参数
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const roleParam = urlParams.get('role');
    if (roleParam && roleInfo[roleParam]) {
      setSelectedRole(roleParam);
      setOpenDialog(true);
    }
  }, []);

  const handleOpenDialog = (role) => {
    setSelectedRole(role);
    setOpenDialog(true);
    setError('');
    setSuccess('');
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedRole('');
    setApplicationReason('');
    setError('');
    // 重置表单
    setEnterpriseForm({
      company_name: '',
      company_name_en: '',
      company_code: '',
      legal_person: '',
      contact_phone: '',
      contact_email: '',
      headquarters_location: '',
      industry: '',
      company_size: '',
      founded_year: '',
      business_scope: '',
      company_description: '',
      official_website: '',
      business_license_url: '',
      other_files: [],
      social_media_links: {}
    });
    setChannelForm({
      provider_name: '',
      provider_type: '',
      real_name: '',
      id_card_number: '',
      company_name: '',
      business_license: '',
      contact_phone: '',
      contact_email: '',
      contact_address: '',
      business_description: '',
      service_categories: [],
      platform_accounts: {},
      portfolio_urls: [],
      qualification_files: []
    });
    setAgentForm({
      agent_name: '',
      contact_phone: '',
      contact_email: '',
      office_address: '',
      agent_description: '',
      service_regions: [],
      specialties: []
    });
  };

  const handleSubmit = async () => {
    if (!selectedRole) {
      setError('请选择要申请的角色');
      return;
    }

    // 根据不同角色验证表单数据
    let formData = {};
    switch (selectedRole) {
      case 'enterprise_user':
        // 验证必填字段
        const missingFields = [];
        if (!enterpriseForm.company_name) missingFields.push('企业名称');
        if (!enterpriseForm.company_code) missingFields.push('统一社会信用代码');
        if (!enterpriseForm.legal_person) missingFields.push('法人姓名');
        if (!enterpriseForm.contact_email) missingFields.push('联系邮箱');
        if (!enterpriseForm.headquarters_location) missingFields.push('总部地址');
        if (!enterpriseForm.industry) missingFields.push('所属行业');
        if (!enterpriseForm.company_size) missingFields.push('企业规模');

        if (missingFields.length > 0) {
          setError(`请填写以下必填字段：${missingFields.join('、')}`);
          return;
        }

        // 验证统一社会信用代码
        if (enterpriseForm.company_code.length !== 18) {
          setError('统一社会信用代码必须是18位');
          return;
        }

        // 验证总部地址
        if (enterpriseForm.headquarters_location.length < 2) {
          setError('总部地址至少需要2个字符');
          return;
        }

        // 验证行业信息
        if (enterpriseForm.industry.length < 2) {
          setError('行业信息至少需要2个字符');
          return;
        }

        // 处理表单数据
        formData = {
          ...enterpriseForm,
          // 如果官方网站为空，则不发送该字段
          ...(enterpriseForm.official_website ? { official_website: enterpriseForm.official_website } : {}),
          // 确保founded_year是整数类型
          founded_year: enterpriseForm.founded_year ? parseInt(enterpriseForm.founded_year, 10) : null
        };
        break;
      case 'channel_user':
        // 验证必填字段
        if (!channelForm.provider_name || !channelForm.provider_type || !channelForm.contact_email) {
          setError('请填写所有必填字段');
          return;
        }
        
        // 个人提供商需要填写真实姓名
        if (channelForm.provider_type === 'individual' && !channelForm.real_name) {
          setError('个人提供商必须填写真实姓名');
          return;
        }

        // 验证身份证号码格式（如果填写了的话）
        if (channelForm.id_card_number && channelForm.id_card_number.length !== 18) {
          setError('身份证号码必须是18位');
          return;
        }

        // 机构提供商需要填写公司名称
        if ((channelForm.provider_type === 'company' || channelForm.provider_type === 'mcn') && !channelForm.company_name) {
          setError('机构提供商必须填写公司名称');
          return;
        }
        
        // 清理表单数据，移除空值和不符合后端要求的字段
        formData = {
          provider_name: channelForm.provider_name,
          provider_type: channelForm.provider_type,
          contact_email: channelForm.contact_email,
          // 只在有值时添加可选字段
          ...(channelForm.real_name ? { real_name: channelForm.real_name } : {}),
          ...(channelForm.id_card_number ? { id_card_number: channelForm.id_card_number } : {}),
          ...(channelForm.company_name ? { company_name: channelForm.company_name } : {}),
          ...(channelForm.business_license ? { business_license: channelForm.business_license } : {}),
          ...(channelForm.contact_phone ? { contact_phone: channelForm.contact_phone } : {}),
          ...(channelForm.contact_address ? { contact_address: channelForm.contact_address } : {}),
          ...(channelForm.business_description ? { business_description: channelForm.business_description } : {}),
          // 只在有元素时添加数组字段
          ...(channelForm.service_categories.length > 0 ? { service_categories: channelForm.service_categories } : {}),
          ...(channelForm.portfolio_urls.length > 0 ? { portfolio_urls: channelForm.portfolio_urls } : {}),
          ...(channelForm.qualification_files.length > 0 ? { qualification_files: channelForm.qualification_files } : {}),
          // 只在有属性时添加对象字段
          ...(Object.keys(channelForm.platform_accounts).length > 0 ? { platform_accounts: channelForm.platform_accounts } : {})
        };
        break;
      case 'agent_user':
        // 验证必填字段
        if (!agentForm.agent_name || !agentForm.contact_email) {
          setError('请填写所有必填字段');
          return;
        }
        formData = agentForm;
        break;
      default:
        setError('未知的角色类型');
        return;
    }

    try {
      setIsSubmitting(true);

      // 根据不同角色调用不同的API接口
      let apiEndpoint = '';
      switch (selectedRole) {
        case 'enterprise_user':
          apiEndpoint = '/companies/profile';
          break;
        case 'channel_user':
          apiEndpoint = '/channels/profile';
          break;
        case 'agent_user':
          apiEndpoint = '/agents/profile';
          break;
        default:
          throw new Error('未知的角色类型');
      }

      await apiService.post(apiEndpoint, formData);

      setSuccess('角色申请提交成功，请等待审核');
      setTimeout(() => {
        handleCloseDialog();
        window.location.href = '/#/user/application-records';
      }, 2000);
    } catch (error) {
      console.error('提交申请失败:', error);
      console.error('错误响应:', error.response);

      // 处理不同类型的错误响应
      let errorMessage = '提交申请失败';
      if (error.response?.data?.detail) {
        const detail = error.response.data.detail;
        console.log('错误详情:', detail);
        if (typeof detail === 'string') {
          errorMessage = detail;
        } else if (Array.isArray(detail)) {
          // 处理验证错误数组
          errorMessage = detail.map(err => {
            if (typeof err === 'string') return err;
            return err.msg || err.message || err.type || '验证失败';
          }).join(', ');
        } else if (typeof detail === 'object') {
          // 处理对象类型的错误
          errorMessage = detail.msg || detail.message || JSON.stringify(detail);
        }
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      setError(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  // 渲染表单内容
  const renderFormContent = () => {
    switch (selectedRole) {
      case 'enterprise_user':
        return (
          <Box>
            {/* 必填项区域 */}
            <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2, color: '#1a1a1a' }}>
              必填信息
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="企业名称"
                  value={enterpriseForm.company_name}
                  onChange={(e) => setEnterpriseForm({...enterpriseForm, company_name: e.target.value})}
                  required
                  error={!enterpriseForm.company_name && isSubmitting}
                  helperText={!enterpriseForm.company_name && isSubmitting ? '此项为必填' : ''}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="统一社会信用代码/营业执照号"
                  value={enterpriseForm.company_code}
                  onChange={(e) => setEnterpriseForm({...enterpriseForm, company_code: e.target.value})}
                  required
                  placeholder="18位统一社会信用代码"
                  error={!enterpriseForm.company_code && isSubmitting}
                  helperText={!enterpriseForm.company_code && isSubmitting ? '此项为必填（18位）' : '请输入18位代码'}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="法人姓名"
                  value={enterpriseForm.legal_person}
                  onChange={(e) => setEnterpriseForm({...enterpriseForm, legal_person: e.target.value})}
                  required
                  error={!enterpriseForm.legal_person && isSubmitting}
                  helperText={!enterpriseForm.legal_person && isSubmitting ? '此项为必填' : ''}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="联系邮箱"
                  value={enterpriseForm.contact_email}
                  onChange={(e) => setEnterpriseForm({...enterpriseForm, contact_email: e.target.value})}
                  required
                  type="email"
                  error={!enterpriseForm.contact_email && isSubmitting}
                  helperText={!enterpriseForm.contact_email && isSubmitting ? '此项为必填' : ''}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="总部地址"
                  value={enterpriseForm.headquarters_location}
                  onChange={(e) => setEnterpriseForm({...enterpriseForm, headquarters_location: e.target.value})}
                  required
                  error={(!enterpriseForm.headquarters_location || enterpriseForm.headquarters_location.length < 5) && isSubmitting}
                  helperText={
                    !enterpriseForm.headquarters_location && isSubmitting ? '此项为必填' :
                    enterpriseForm.headquarters_location && enterpriseForm.headquarters_location.length < 5 && isSubmitting ? '总部地址至少需要5个字符' :
                    '请填写详细的总部地址'
                  }
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth required>
                  <InputLabel 
                    shrink={true}
                    sx={{
                      backgroundColor: 'white',
                      px: 0.5,
                    }}
                  >
                    所属行业
                  </InputLabel>
                  <Select
                    value={enterpriseForm.industry}
                    onChange={(e) => setEnterpriseForm({...enterpriseForm, industry: e.target.value})}
                    label="所属行业"
                    error={!enterpriseForm.industry && isSubmitting}
                    displayEmpty
                    notched={true}
                    sx={{
                      minWidth: 200,
                      '& .MuiSelect-select': {
                        py: 1.5,
                        display: 'flex',
                        alignItems: 'center',
                        minHeight: '24px'
                      }
                    }}
                    renderValue={(selected) => {
                      if (!selected) {
                        return <span style={{ color: '#9ca3af' }}>请选择您企业的所属行业</span>;
                      }
                      return selected;
                    }}
                  >
                    {industryOptions.map((option) => (
                      <MenuItem key={option} value={option}>
                        {option}
                      </MenuItem>
                    ))}
                  </Select>
                  {!enterpriseForm.industry && isSubmitting && (
                    <FormHelperText error>此项为必填</FormHelperText>
                  )}
                  {enterpriseForm.industry && (
                    <FormHelperText>已选择: {enterpriseForm.industry}</FormHelperText>
                  )}
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth required>
                  <InputLabel 
                    shrink={true}
                    sx={{
                      backgroundColor: 'white',
                      px: 0.5,
                    }}
                  >
                    企业规模
                  </InputLabel>
                  <Select
                    value={enterpriseForm.company_size}
                    onChange={(e) => setEnterpriseForm({...enterpriseForm, company_size: e.target.value})}
                    label="企业规模"
                    error={!enterpriseForm.company_size && isSubmitting}
                    displayEmpty
                    notched={true}
                    sx={{
                      minWidth: 200,
                      '& .MuiSelect-select': {
                        py: 1.5,
                        display: 'flex',
                        alignItems: 'center',
                        minHeight: '24px'
                      }
                    }}
                    renderValue={(selected) => {
                      if (!selected) {
                        return <span style={{ color: '#9ca3af' }}>请选择您企业的人员规模</span>;
                      }
                      const selectedOption = companySizeOptions.find(option => option.value === selected);
                      return selectedOption ? selectedOption.label : selected;
                    }}
                  >
                    {companySizeOptions.map((option) => (
                      <MenuItem key={option.value} value={option.value}>
                        {option.label}
                      </MenuItem>
                    ))}
                  </Select>
                  {!enterpriseForm.company_size && isSubmitting && (
                    <FormHelperText error>此项为必填</FormHelperText>
                  )}
                  {enterpriseForm.company_size && (
                    <FormHelperText>已选择: {companySizeOptions.find(option => option.value === enterpriseForm.company_size)?.label || enterpriseForm.company_size}</FormHelperText>
                  )}
                </FormControl>
              </Grid>
            </Grid>

            {/* 分隔线 */}
            <Divider sx={{ my: 3 }} />

            {/* 选填项区域 */}
            <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2, color: '#6b7280' }}>
              选填信息
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="英文名称"
                  value={enterpriseForm.company_name_en}
                  onChange={(e) => setEnterpriseForm({...enterpriseForm, company_name_en: e.target.value})}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="联系电话"
                  value={enterpriseForm.contact_phone}
                  onChange={(e) => setEnterpriseForm({...enterpriseForm, contact_phone: e.target.value})}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="成立年份"
                  value={enterpriseForm.founded_year}
                  onChange={(e) => setEnterpriseForm({...enterpriseForm, founded_year: e.target.value})}
                  placeholder="如：2020"
                  type="number"
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="官方网站"
                  value={enterpriseForm.official_website}
                  onChange={(e) => setEnterpriseForm({...enterpriseForm, official_website: e.target.value})}
                  placeholder="https://www.example.com"
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="经营范围"
                  value={enterpriseForm.business_scope}
                  onChange={(e) => setEnterpriseForm({...enterpriseForm, business_scope: e.target.value})}
                  multiline
                  rows={2}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="企业描述"
                  value={enterpriseForm.company_description}
                  onChange={(e) => setEnterpriseForm({...enterpriseForm, company_description: e.target.value})}
                  multiline
                  rows={3}
                  placeholder="请简要描述您的企业..."
                />
              </Grid>
              <Grid item xs={12}>
                <FileUpload
                  label="营业执照"
                  accept="image/*,.pdf"
                  maxSize={10 * 1024 * 1024}
                  businessType="business_license"
                  helperText="请上传营业执照扫描件或照片，支持 JPG、PNG、PDF 格式，最大 10MB"
                  value={enterpriseForm.business_license_url}
                  onUploadSuccess={(url) => {
                    setEnterpriseForm({...enterpriseForm, business_license_url: url});
                  }}
                  onUploadError={(error) => {
                    setError('营业执照上传失败：' + error.message);
                  }}
                />
              </Grid>
            </Grid>
          </Box>
        );

      case 'channel_user':
        return (
          <Box>
            {/* 必填项区域 */}
            <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2, color: '#1a1a1a' }}>
              必填信息
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="提供商名称"
                  value={channelForm.provider_name}
                  onChange={(e) => setChannelForm({...channelForm, provider_name: e.target.value})}
                  required
                  error={!channelForm.provider_name && isSubmitting}
                  helperText={!channelForm.provider_name && isSubmitting ? '此项为必填' : ''}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth required>
                  <InputLabel 
                    shrink={true}
                    sx={{
                      backgroundColor: 'white',
                      px: 0.5,
                    }}
                  >
                    提供商类型
                  </InputLabel>
                  <Select
                    value={channelForm.provider_type}
                    onChange={(e) => setChannelForm({...channelForm, provider_type: e.target.value})}
                    label="提供商类型"
                    error={!channelForm.provider_type && isSubmitting}
                    displayEmpty
                    notched={true}
                    sx={{
                      minWidth: 200,
                      '& .MuiSelect-select': {
                        py: 1.5,
                        display: 'flex',
                        alignItems: 'center',
                        minHeight: '24px'
                      }
                    }}
                    renderValue={(selected) => {
                      if (!selected) {
                        return <span style={{ color: '#9ca3af' }}>请选择提供商类型</span>;
                      }
                      const option = providerTypeOptions.find(opt => opt.value === selected);
                      return option ? option.label : selected;
                    }}
                  >
                    {providerTypeOptions.map((option) => (
                      <MenuItem key={option.value} value={option.value}>
                        {option.label}
                      </MenuItem>
                    ))}
                  </Select>
                  {!channelForm.provider_type && isSubmitting && (
                    <FormHelperText error>此项为必填</FormHelperText>
                  )}
                  {channelForm.provider_type && (
                    <FormHelperText>已选择: {providerTypeOptions.find(opt => opt.value === channelForm.provider_type)?.label}</FormHelperText>
                  )}
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="联系邮箱"
                  value={channelForm.contact_email}
                  onChange={(e) => setChannelForm({...channelForm, contact_email: e.target.value})}
                  required
                  type="email"
                  error={!channelForm.contact_email && isSubmitting}
                  helperText={!channelForm.contact_email && isSubmitting ? '此项为必填' : ''}
                />
              </Grid>
              
              {/* 根据提供商类型显示条件必填项 */}
              {channelForm.provider_type === 'individual' && (
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="真实姓名"
                    value={channelForm.real_name}
                    onChange={(e) => setChannelForm({...channelForm, real_name: e.target.value})}
                    required
                    error={!channelForm.real_name && isSubmitting}
                    helperText={!channelForm.real_name && isSubmitting ? '个人提供商必填' : ''}
                  />
                </Grid>
              )}
              
              {(channelForm.provider_type === 'company' || channelForm.provider_type === 'mcn' || channelForm.provider_type === 'studio') && (
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="公司名称"
                    value={channelForm.company_name}
                    onChange={(e) => setChannelForm({...channelForm, company_name: e.target.value})}
                    required
                    error={!channelForm.company_name && isSubmitting}
                    helperText={!channelForm.company_name && isSubmitting ? '机构提供商必填' : ''}
                  />
                </Grid>
              )}
            </Grid>

            {/* 分隔线 */}
            <Divider sx={{ my: 3 }} />

            {/* 选填项区域 */}
            <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2, color: '#6b7280' }}>
              选填信息
            </Typography>
            <Grid container spacing={2}>
              {channelForm.provider_type === 'individual' && (
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="身份证号"
                    value={channelForm.id_card_number}
                    onChange={(e) => setChannelForm({...channelForm, id_card_number: e.target.value})}
                    placeholder="请输入18位身份证号码"
                    error={channelForm.id_card_number && channelForm.id_card_number.length !== 18}
                    helperText={channelForm.id_card_number && channelForm.id_card_number.length !== 18 ? '身份证号码必须是18位' : ''}
                    inputProps={{ maxLength: 18 }}
                  />
                </Grid>
              )}
              
              {(channelForm.provider_type === 'company' || channelForm.provider_type === 'mcn' || channelForm.provider_type === 'studio') && (
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="营业执照号"
                    value={channelForm.business_license}
                    onChange={(e) => setChannelForm({...channelForm, business_license: e.target.value})}
                  />
                </Grid>
              )}
              
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="联系电话"
                  value={channelForm.contact_phone}
                  onChange={(e) => setChannelForm({...channelForm, contact_phone: e.target.value})}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="联系地址"
                  value={channelForm.contact_address}
                  onChange={(e) => setChannelForm({...channelForm, contact_address: e.target.value})}
                />
              </Grid>
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <InputLabel 
                    shrink={true}
                    sx={{
                      backgroundColor: 'white',
                      px: 0.5,
                    }}
                  >
                    服务类别
                  </InputLabel>
                  <Select
                    multiple
                    value={channelForm.service_categories}
                    onChange={(e) => setChannelForm({...channelForm, service_categories: e.target.value})}
                    label="服务类别"
                    displayEmpty
                    notched={true}
                    sx={{
                      minWidth: 200,
                      '& .MuiSelect-select': {
                        py: 1.5,
                        minHeight: '24px'
                      }
                    }}
                    renderValue={(selected) => {
                      if (!Array.isArray(selected) || selected.length === 0) {
                        return <span style={{ color: '#9ca3af' }}>请选择服务类别（可多选）</span>;
                      }
                      return (
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                          {selected.map((value) => (
                            <Chip key={value} label={value} size="small" />
                          ))}
                        </Box>
                      );
                    }}
                  >
                    {serviceCategoryOptions.map((option) => (
                      <MenuItem key={option} value={option}>
                        {option}
                      </MenuItem>
                    ))}
                  </Select>
                  <FormHelperText>可多选，已选择 {Array.isArray(channelForm.service_categories) ? channelForm.service_categories.length : 0} 项</FormHelperText>
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="业务描述"
                  value={channelForm.business_description}
                  onChange={(e) => setChannelForm({...channelForm, business_description: e.target.value})}
                  multiline
                  rows={3}
                  placeholder="请简要说明您的内容资源和业务情况..."
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="作品集链接"
                  value={Array.isArray(channelForm.portfolio_urls) ? channelForm.portfolio_urls.join(', ') : ''}
                  onChange={(e) => setChannelForm({...channelForm, portfolio_urls: e.target.value.split(', ').filter(url => url)})}
                  placeholder="多个链接用英文逗号分隔"
                />
              </Grid>
            </Grid>
          </Box>
        );

      case 'agent_user':
        return (
          <Box>
            {/* 必填项区域 */}
            <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2, color: '#1a1a1a' }}>
              必填信息
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="代理商名称"
                  value={agentForm.agent_name}
                  onChange={(e) => setAgentForm({...agentForm, agent_name: e.target.value})}
                  required
                  error={!agentForm.agent_name && isSubmitting}
                  helperText={!agentForm.agent_name && isSubmitting ? '此项为必填' : ''}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="联系邮箱"
                  value={agentForm.contact_email}
                  onChange={(e) => setAgentForm({...agentForm, contact_email: e.target.value})}
                  required
                  type="email"
                  error={!agentForm.contact_email && isSubmitting}
                  helperText={!agentForm.contact_email && isSubmitting ? '此项为必填' : ''}
                />
              </Grid>
            </Grid>

            {/* 分隔线 */}
            <Divider sx={{ my: 3 }} />

            {/* 选填项区域 */}
            <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2, color: '#6b7280' }}>
              选填信息
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="联系电话"
                  value={agentForm.contact_phone}
                  onChange={(e) => setAgentForm({...agentForm, contact_phone: e.target.value})}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="办公地址"
                  value={agentForm.office_address}
                  onChange={(e) => setAgentForm({...agentForm, office_address: e.target.value})}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel 
                    shrink={true}
                    sx={{
                      backgroundColor: 'white',
                      px: 0.5,
                    }}
                  >
                    服务区域
                  </InputLabel>
                  <Select
                    multiple
                    value={agentForm.service_regions}
                    onChange={(e) => setAgentForm({...agentForm, service_regions: e.target.value})}
                    label="服务区域"
                    displayEmpty
                    notched={true}
                    sx={{
                      minWidth: 200,
                      '& .MuiSelect-select': {
                        py: 1.5,
                        minHeight: '24px'
                      }
                    }}
                    renderValue={(selected) => {
                      if (!Array.isArray(selected) || selected.length === 0) {
                        return <span style={{ color: '#9ca3af' }}>请选择服务区域（可多选）</span>;
                      }
                      return (
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                          {selected.map((value) => (
                            <Chip key={value} label={value} size="small" />
                          ))}
                        </Box>
                      );
                    }}
                  >
                    {serviceRegionOptions.map((option) => (
                      <MenuItem key={option} value={option}>
                        {option}
                      </MenuItem>
                    ))}
                  </Select>
                  <FormHelperText>可多选，已选择 {Array.isArray(agentForm.service_regions) ? agentForm.service_regions.length : 0} 项</FormHelperText>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel 
                    shrink={true}
                    sx={{
                      backgroundColor: 'white',
                      px: 0.5,
                    }}
                  >
                    专业领域
                  </InputLabel>
                  <Select
                    multiple
                    value={agentForm.specialties}
                    onChange={(e) => setAgentForm({...agentForm, specialties: e.target.value})}
                    label="专业领域"
                    displayEmpty
                    notched={true}
                    sx={{
                      minWidth: 200,
                      '& .MuiSelect-select': {
                        py: 1.5,
                        minHeight: '24px'
                      }
                    }}
                    renderValue={(selected) => {
                      if (!Array.isArray(selected) || selected.length === 0) {
                        return <span style={{ color: '#9ca3af' }}>请选择专业领域（可多选）</span>;
                      }
                      return (
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                          {selected.map((value) => (
                            <Chip key={value} label={value} size="small" />
                          ))}
                        </Box>
                      );
                    }}
                  >
                    {specialtyOptions.map((option) => (
                      <MenuItem key={option} value={option}>
                        {option}
                      </MenuItem>
                    ))}
                  </Select>
                  <FormHelperText>可多选，已选择 {Array.isArray(agentForm.specialties) ? agentForm.specialties.length : 0} 项</FormHelperText>
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="代理描述"
                  value={agentForm.agent_description}
                  onChange={(e) => setAgentForm({...agentForm, agent_description: e.target.value})}
                  multiline
                  rows={3}
                  placeholder="请简要描述您的推广计划和预期目标..."
                />
              </Grid>
            </Grid>
          </Box>
        );

      default:
        return null;
    }
  };

  return (
    <Box sx={{
      minHeight: '100vh',
      backgroundColor: 'white',
      width: '100%'
    }}>
      {/* 顶部标题区域 */}
      <Box sx={{
        borderBottom: '1px solid #f0f0f0',
        backgroundColor: 'white',
        pt: 2
      }}>
        <Container maxWidth="lg">
          <Box sx={{ py: 5 }}>
            <Typography variant="h3" sx={{
              fontWeight: 700,
              color: '#1a1a1a',
              mb: 1,
              fontSize: { xs: '2rem', md: '2.5rem' }
            }}>
              角色申请
            </Typography>
            <Typography variant="body1" sx={{
              color: '#6b7280',
              fontSize: '1.125rem'
            }}>
              选择并申请适合您的角色权限
            </Typography>
          </Box>
        </Container>
      </Box>

      {/* 提示信息 */}
      <Container maxWidth="lg" sx={{ mt: 3 }}>
        {success && (
          <Alert severity="success" sx={{ mb: 3, borderRadius: 2 }} onClose={() => setSuccess('')}>
            {success}
          </Alert>
        )}
        {error && (
          <Alert severity="error" sx={{ mb: 3, borderRadius: 2 }} onClose={() => setError('')}>
            {error}
          </Alert>
        )}
      </Container>

      {/* 角色卡片区域 */}
      <Container maxWidth="lg">
        <Box sx={{ py: 5 }}>
          <Grid container spacing={4}>
            {Object.entries(roleInfo).map(([key, role]) => (
              <Grid item xs={12} md={4} key={key}>
                <Box
                  sx={{
                    p: 4,
                    border: '1px solid #e5e7eb',
                    borderRadius: 2,
                    backgroundColor: 'white',
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    transition: 'all 0.2s ease',
                    '&:hover': {
                      borderColor: role.color,
                      transform: 'translateY(-2px)',
                      boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
                    }
                  }}
                >
                  {/* 图标 */}
                  <Box sx={{
                    width: 56,
                    height: 56,
                    borderRadius: '12px',
                    backgroundColor: role.bgColor,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    mb: 3
                  }}>
                    {React.cloneElement(role.icon, { 
                      sx: { 
                        color: role.color,
                        fontSize: 28
                      } 
                    })}
                  </Box>

                  {/* 标题和描述 */}
                  <Typography variant="h5" sx={{
                    fontWeight: 700,
                    color: '#1a1a1a',
                    mb: 1
                  }}>
                    {role.name}
                  </Typography>
                  <Typography variant="body2" sx={{
                    color: '#6b7280',
                    mb: 3,
                    lineHeight: 1.6
                  }}>
                    {role.description}
                  </Typography>

                  {/* 权益列表 */}
                  <Box sx={{ mb: 3, flex: 1 }}>
                    {role.benefits.map((benefit, index) => (
                      <Box key={index} sx={{ 
                        display: 'flex', 
                        alignItems: 'center', 
                        gap: 1,
                        mb: 1.5
                      }}>
                        <CheckCircle sx={{ 
                          color: role.color, 
                          fontSize: 16 
                        }} />
                        <Typography variant="body2" sx={{ 
                          color: '#374151',
                          fontSize: '0.875rem'
                        }}>
                          {benefit}
                        </Typography>
                      </Box>
                    ))}
                  </Box>

                  {/* 申请按钮 */}
                  <Button
                    fullWidth
                    variant="contained"
                    onClick={() => handleOpenDialog(key)}
                    sx={{
                      backgroundColor: role.color,
                      color: 'white',
                      borderRadius: 1.5,
                      py: 1.2,
                      fontSize: '0.95rem',
                      fontWeight: 600,
                      textTransform: 'none',
                      boxShadow: 'none',
                      '&:hover': {
                        backgroundColor: role.color,
                        filter: 'brightness(0.95)',
                        boxShadow: 'none',
                      }
                    }}
                  >
                    立即申请
                  </Button>
                </Box>
              </Grid>
            ))}
          </Grid>

          {/* 底部提示 */}
          <Box sx={{
            mt: 5,
            p: 3,
            backgroundColor: '#f9fafb',
            borderRadius: 2,
            border: '1px solid #e5e7eb'
          }}>
            <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2 }}>
              <Box sx={{
                width: 36,
                height: 36,
                borderRadius: '10px',
                backgroundColor: '#fef3c7',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                flexShrink: 0,
                mt: 0.5
              }}>
                <Typography sx={{ fontSize: '1rem' }}>💡</Typography>
              </Box>
              <Box sx={{ flex: 1 }}>
                <Typography variant="body2" sx={{
                  color: '#1a1a1a',
                  fontWeight: 600,
                  mb: 0.5,
                  fontSize: '0.95rem'
                }}>
                  申请须知
                </Typography>
                <Typography variant="body2" sx={{
                  color: '#6b7280',
                  lineHeight: 1.6,
                  fontSize: '0.875rem'
                }}>
                  • 每个账户可以申请多个角色权限
                  <br />
                  • 申请提交后，我们将在1-3个工作日内完成审核
                  <br />
                  • 审核结果将通过邮件和站内信通知您
                  <br />
                  • 如有疑问，请联系客服获取帮助
                </Typography>
              </Box>
            </Box>
          </Box>
        </Box>
      </Container>

      {/* 申请对话框 */}
      <Dialog
        open={openDialog}
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 2,
            maxHeight: '90vh',
          }
        }}
      >
        <DialogTitle sx={{
          pb: 2,
          fontWeight: 600,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        }}>
          申请{selectedRole && roleInfo[selectedRole]?.name}
          <Button
            onClick={handleCloseDialog}
            sx={{
              minWidth: 'auto',
              p: 0.5,
              color: '#6b7280'
            }}
          >
            <Close />
          </Button>
        </DialogTitle>
        
        <DialogContent sx={{ overflowY: 'auto' }}>
          {renderFormContent()}
        </DialogContent>

        <DialogActions sx={{ px: 3, pb: 3 }}>
          <Button
            onClick={handleCloseDialog}
            disabled={isSubmitting}
            sx={{
              color: '#6b7280',
              textTransform: 'none',
              fontWeight: 600
            }}
          >
            取消
          </Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            disabled={isSubmitting}
            startIcon={isSubmitting ? <CircularProgress size={16} /> : <Send />}
            sx={{
              backgroundColor: '#1a1a1a',
              color: 'white',
              textTransform: 'none',
              fontWeight: 600,
              px: 3,
              boxShadow: 'none',
              '&:hover': {
                backgroundColor: '#374151',
                boxShadow: 'none',
              }
            }}
          >
            {isSubmitting ? '提交中...' : '提交申请'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}

export default RoleApplication;