import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';

function Dashboard() {
  const { user, isAuthenticated } = useAuth();

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    return <Navigate to="/auth/login" replace />;
  }

  // Redirect to role-specific dashboard
  if (user) {
    const roleRoutes = {
      super_admin: '/super-admin/dashboard',
      admin: '/admin/dashboard',
      regular_user: '/user/dashboard',
      enterprise_user: '/enterprise/dashboard',
      channel_user: '/channel/dashboard',
      agent_user: '/agent/dashboard',
    };

    const targetRoute = roleRoutes[user.role];
    if (targetRoute) {
      return <Navigate to={targetRoute} replace />;
    }
  }

  // Fallback to user dashboard
  return <Navigate to="/user/dashboard" replace />;
}

export default Dashboard;
