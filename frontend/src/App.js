import React from 'react';
import { HashRouter as Router } from 'react-router-dom';
import { ThemeProvider } from './contexts/ThemeContext';
import { AuthProvider } from './contexts/AuthContext';
import { CustomerServiceProvider } from './contexts/CustomerServiceContext';
import { SnackbarProvider } from 'notistack';
import AppRouter from './components/router/AppRouter';

function App() {
  return (
    <Router>
      <ThemeProvider>
        <SnackbarProvider maxSnack={3} anchorOrigin={{ vertical: 'top', horizontal: 'right' }}>
          <AuthProvider>
            <CustomerServiceProvider>
              <AppRouter />
            </CustomerServiceProvider>
          </AuthProvider>
        </SnackbarProvider>
      </ThemeProvider>
    </Router>
  );
}

export default App;
