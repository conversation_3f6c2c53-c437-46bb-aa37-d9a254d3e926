import { lazy } from 'react';

const MAX_RETRY_COUNT = 3;
const RETRY_DELAY = 1000; // 1 second

/**
 * Enhanced lazy loading with retry logic for chunk loading failures
 * @param {Function} componentImport - Function that returns a dynamic import
 * @returns {React.LazyExoticComponent} - Lazy loaded component
 */
export function lazyWithRetry(componentImport) {
  return lazy(async () => {
    const pageHasAlreadyBeenForceRefreshed = JSON.parse(
      window.sessionStorage.getItem('page-has-been-force-refreshed') || 'false'
    );

    try {
      const component = await componentImport();
      if (!component || !component.default) {

        throw new Error('Component does not have a default export');
      }
      window.sessionStorage.setItem('page-has-been-force-refreshed', 'false');
      return component;
    } catch (error) {
      if (!pageHasAlreadyBeenForceRefreshed) {
        // Chunk loading has failed, force refresh the page once
        window.sessionStorage.setItem('page-has-been-force-refreshed', 'true');
        return window.location.reload();
      }

      // If we've already refreshed once, try retrying the import
      let retryCount = 0;
      
      const tryImport = async () => {
        try {
          return await componentImport();
        } catch (err) {
          retryCount++;
          
          if (retryCount < MAX_RETRY_COUNT) {
            console.warn(`Retrying chunk load... (${retryCount}/${MAX_RETRY_COUNT})`);
            await new Promise(resolve => setTimeout(resolve, RETRY_DELAY * retryCount));
            return tryImport();
          }
          
          // If all retries failed, show error message
          console.error('Failed to load chunk after multiple retries:', err);
          
          // Return a fallback component
          return {
            default: () => (
              <div style={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                minHeight: '50vh',
                padding: '20px',
                textAlign: 'center'
              }}>
                <h2 style={{ color: '#f44336', marginBottom: '16px' }}>
                  页面加载失败
                </h2>
                <p style={{ marginBottom: '16px', color: '#666' }}>
                  抱歉，页面资源加载失败。这可能是由于网络问题导致的。
                </p>
                <button
                  onClick={() => window.location.reload()}
                  style={{
                    padding: '10px 20px',
                    backgroundColor: '#1976d2',
                    color: 'white',
                    border: 'none',
                    borderRadius: '4px',
                    cursor: 'pointer',
                    fontSize: '16px'
                  }}
                >
                  刷新页面
                </button>
              </div>
            )
          };
        }
      };
      
      return tryImport();
    }
  });
}

/**
 * Clear the force refresh flag when the app unmounts or on manual clear
 */
export function clearForceRefreshFlag() {
  window.sessionStorage.removeItem('page-has-been-force-refreshed');
}