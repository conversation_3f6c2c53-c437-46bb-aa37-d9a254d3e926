/**
 * 推广链接处理工具
 */
import { ApiConfig } from '../config/api-config';

/**
 * 跟踪推广链接点击
 * @param {string} referralCode - 推荐码
 * @param {string} targetPage - 目标页面
 * @param {string} campaign - 活动名称（可选）
 * @returns {Promise<boolean>} - 跟踪是否成功
 */
export const trackReferralClick = async (referralCode, targetPage, campaign = null) => {

  try {
    const requestData = {
      referral_code: referralCode,
      target_page: targetPage,
      campaign: campaign,
      user_agent: navigator.userAgent,
    };

    const response = await fetch('/api/v1/agents/referral-links/track-click', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestData),
    });

    if (response.ok) {
      const result = await response.json();
      if (process.env.NODE_ENV === 'development') {
        console.log('✅ 推广链接点击跟踪成功:', result);
      }
      return true;
    } else {
      const errorText = await response.text();
      if (process.env.NODE_ENV === 'development') {
        console.warn('❌ 推广链接点击跟踪失败:', {
          status: response.status,
          statusText: response.statusText,
          error: errorText
        });
      }
      return false;
    }
  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
      console.error('💥 推广链接点击跟踪异常:', error);
    }
    return false;
  }
};

/**
 * 从URL中获取推广参数
 * @returns {Object} - 包含ref和campaign的对象
 */
export const getReferralParams = () => {
  const urlParams = new URLSearchParams(window.location.search);
  return {
    ref: urlParams.get('ref'),
    campaign: urlParams.get('campaign'),
  };
};

/**
 * 构建带推广参数的URL
 * @param {string} baseUrl - 基础URL
 * @param {string} ref - 推荐码
 * @param {string} campaign - 活动名称（可选）
 * @returns {string} - 完整的URL
 */
export const buildReferralUrl = (baseUrl, ref, campaign = null) => {
  const url = new URL(baseUrl, window.location.origin);
  if (ref) {
    url.searchParams.set('ref', ref);
  }
  if (campaign) {
    url.searchParams.set('campaign', campaign);
  }
  return url.toString();
};

/**
 * 处理推广链接访问
 * @param {string} targetPage - 目标页面名称
 * @returns {Object} - 推广参数对象
 */
export const handleReferralVisit = (targetPage) => {
  const params = getReferralParams();

  if (params.ref) {
    // 根据当前路径确定实际的目标页面
    let actualTargetPage = targetPage;
    const currentPath = window.location.pathname;

    // 根据实际路径确定目标页面，确保与后端数据库中的值匹配
    if (currentPath === '/auth/register') {
      actualTargetPage = 'auth/register';
    } else if (currentPath === '/' || currentPath === '') {
      actualTargetPage = '';  // 首页用空字符串
    } else {
      // 对于其他页面，使用传入的targetPage参数
      actualTargetPage = targetPage;
    }

    console.log('🔍 推广链接跟踪:', {
      currentPath,
      inputTargetPage: targetPage,
      actualTargetPage,
      referralCode: params.ref,
      campaign: params.campaign
    });

    // 跟踪点击
    trackReferralClick(params.ref, actualTargetPage, params.campaign);

    // 可以在这里添加其他处理逻辑
    // 比如设置localStorage、显示特殊提示等
    localStorage.setItem('referral_code', params.ref);
    if (params.campaign) {
      localStorage.setItem('referral_campaign', params.campaign);
    }
  }

  return params;
};

/**
 * 从localStorage获取推广信息
 * @returns {Object} - 推广信息对象
 */
export const getReferralFromStorage = () => {
  return {
    ref: localStorage.getItem('referral_code'),
    campaign: localStorage.getItem('referral_campaign'),
  };
};

/**
 * 清除推广信息
 */
export const clearReferralStorage = () => {
  localStorage.removeItem('referral_code');
  localStorage.removeItem('referral_campaign');
};
