/**
 * Configure webpack public path dynamically
 * This helps resolve chunk loading issues in different deployment environments
 */

function setWebpackPublicPath() {
  // Get the current script's base URL
  const scripts = document.getElementsByTagName('script');
  const currentScript = scripts[scripts.length - 1];
  
  if (currentScript && currentScript.src) {
    const scriptUrl = new URL(currentScript.src);
    const baseUrl = `${scriptUrl.protocol}//${scriptUrl.host}`;
    
    // Set webpack public path if not already set
    if (!window.__webpack_public_path__) {
      window.__webpack_public_path__ = baseUrl + '/';
    }
  }
}

// Auto-configure on load
if (typeof window !== 'undefined') {
  setWebpackPublicPath();
}

export default setWebpackPublicPath;