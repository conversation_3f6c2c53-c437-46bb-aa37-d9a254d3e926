/**
 * Global error handler for chunk loading failures
 */

class ChunkErrorHandler {
  constructor() {
    this.failedChunks = new Set();
    this.maxRetries = 3;
    this.retryDelay = 1000;
    this.setupErrorHandling();
  }

  setupErrorHandling() {
    // Handle unhandled promise rejections (including chunk load errors)
    window.addEventListener('unhandledrejection', (event) => {
      if (this.isChunkLoadError(event.reason)) {
        event.preventDefault();
        this.handleChunkError(event.reason);
      }
    });

    // Handle general errors
    window.addEventListener('error', (event) => {
      if (this.isChunkLoadError(event.error)) {
        event.preventDefault();
        this.handleChunkError(event.error);
      }
    });
  }

  isChunkLoadError(error) {
    return error && (
      error.name === 'ChunkLoadError' ||
      (error.message && (
        error.message.includes('Loading chunk') ||
        error.message.includes('Loading CSS chunk') ||
        error.message.includes('Failed to fetch dynamically imported module')
      ))
    );
  }

  handleChunkError(error) {
    if (process.env.NODE_ENV === 'development') {
      console.warn('Chunk loading error detected:', error);
    }
    
    // Extract chunk name from error if possible
    const chunkMatch = error.message && error.message.match(/chunk (\S+)/);
    const chunkName = chunkMatch ? chunkMatch[1] : 'unknown';
    
    if (!this.failedChunks.has(chunkName)) {
      this.failedChunks.add(chunkName);
      
      // Show user-friendly message
      this.showErrorNotification();
      
      // Try to recover
      this.attemptRecovery();
    }
  }

  showErrorNotification() {
    // Check if notification already exists
    if (document.getElementById('chunk-error-notification')) {
      return;
    }

    const notification = document.createElement('div');
    notification.id = 'chunk-error-notification';
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: #ff9800;
      color: white;
      padding: 16px 24px;
      border-radius: 4px;
      box-shadow: 0 2px 5px rgba(0,0,0,0.2);
      z-index: 9999;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
      display: flex;
      align-items: center;
      gap: 12px;
      max-width: 400px;
    `;

    notification.innerHTML = `
      <div style="flex: 1;">
        <div style="font-weight: 600; margin-bottom: 4px;">加载资源出错</div>
        <div style="font-size: 14px;">正在尝试重新加载...</div>
      </div>
      <button onclick="window.location.reload()" style="
        background: white;
        color: #ff9800;
        border: none;
        padding: 8px 16px;
        border-radius: 4px;
        cursor: pointer;
        font-weight: 500;
      ">刷新页面</button>
    `;

    document.body.appendChild(notification);

    // Auto-remove after 10 seconds
    setTimeout(() => {
      const elem = document.getElementById('chunk-error-notification');
      if (elem) {
        elem.remove();
      }
    }, 10000);
  }

  attemptRecovery() {
    // Clear module cache if available
    if (window.__webpack_require__ && window.__webpack_require__.cache) {
      for (const moduleId in window.__webpack_require__.cache) {
        delete window.__webpack_require__.cache[moduleId];
      }
    }

    // Try to reload after a delay
    setTimeout(() => {
      // Only reload if we haven't done so recently
      const lastReload = sessionStorage.getItem('last-chunk-error-reload');
      const now = Date.now();
      
      if (!lastReload || now - parseInt(lastReload) > 60000) {
        sessionStorage.setItem('last-chunk-error-reload', now.toString());
        if (process.env.NODE_ENV === 'development') {
          console.log('Attempting automatic page refresh to resolve chunk loading issues...');
        }
        
        // Give user time to see the notification
        setTimeout(() => {
          window.location.reload();
        }, 2000);
      }
    }, this.retryDelay);
  }

  clearErrors() {
    this.failedChunks.clear();
    sessionStorage.removeItem('last-chunk-error-reload');
  }
}

// Initialize error handler
const chunkErrorHandler = new ChunkErrorHandler();

// Export for use in other modules if needed
export default chunkErrorHandler;