/**
 * 套餐类型相关工具函数
 */

// 套餐类型映射
export const PLAN_TYPE_MAP = {
  'free': '免费版',
  'basic': '基础版',
  'pro': '专业版',
  'enterprise': '企业版',
  'custom': '定制版'
};

// 用户类型映射
export const USER_TYPE_MAP = {
  'enterprise': '企业用户',
  'provider': '服务商',
  'both': '通用'
};

// 计费周期映射
export const BILLING_CYCLE_MAP = {
  'monthly': '月付',
  'quarterly': '季付',
  'semi_annual': '半年付',
  'yearly': '年付'
};

// 功能特性映射
export const FEATURE_MAP = {
  // 支持类型
  'support': '客服支持',
  'email': '邮件支持',
  'phone': '电话支持',
  'chat': '在线客服',
  'priority': '优先支持',

  // 分析功能
  'analytics': '数据分析',
  'basic': '基础分析',
  'advanced': '高级分析',
  'premium': '专业分析',
  'realtime': '实时分析',

  // 功能特性
  'api': 'API接口',
  'unlimited': '无限制',
  'custom': '定制功能',
  'backup': '数据备份',
  'security': '安全保护',
  'monitoring': '监控服务',
  'reporting': '报告生成',
  'integration': '第三方集成',
  'automation': '自动化',
  'collaboration': '团队协作'
};

/**
 * 获取套餐类型中文名称
 * @param {string} planType - 套餐类型英文值
 * @returns {string} 中文名称
 */
export const getPlanTypeLabel = (planType) => {
  return PLAN_TYPE_MAP[planType] || planType || '-';
};

/**
 * 获取用户类型中文名称
 * @param {string} userType - 用户类型英文值
 * @returns {string} 中文名称
 */
export const getUserTypeLabel = (userType) => {
  return USER_TYPE_MAP[userType] || userType || '-';
};

/**
 * 获取计费周期中文名称
 * @param {string} billingCycle - 计费周期英文值
 * @returns {string} 中文名称
 */
export const getBillingCycleLabel = (billingCycle) => {
  return BILLING_CYCLE_MAP[billingCycle] || billingCycle || '-';
};

/**
 * 获取功能特性中文名称
 * @param {string} feature - 功能特性英文值
 * @returns {string} 中文名称
 */
export const getFeatureLabel = (feature) => {
  return FEATURE_MAP[feature] || feature;
};

/**
 * 格式化功能特性显示
 * @param {string} key - 功能特性键
 * @param {string} value - 功能特性值
 * @returns {string} 格式化后的中文显示
 */
export const formatFeatureDisplay = (key, value) => {
  const keyLabel = getFeatureLabel(key);

  // 如果没有值或值为空，只返回键的中文
  if (!value || value === '') {
    return keyLabel;
  }

  const valueLabel = getFeatureLabel(value);
  return `${keyLabel}: ${valueLabel}`;
};

/**
 * 获取所有套餐类型选项
 * @returns {Array} 套餐类型选项数组
 */
export const getPlanTypeOptions = () => {
  return Object.entries(PLAN_TYPE_MAP).map(([value, label]) => ({
    value,
    label
  }));
};

/**
 * 获取所有用户类型选项
 * @returns {Array} 用户类型选项数组
 */
export const getUserTypeOptions = () => {
  return Object.entries(USER_TYPE_MAP).map(([value, label]) => ({
    value,
    label
  }));
};

/**
 * 获取所有计费周期选项
 * @returns {Array} 计费周期选项数组
 */
export const getBillingCycleOptions = () => {
  return Object.entries(BILLING_CYCLE_MAP).map(([value, label]) => ({
    value,
    label
  }));
};
