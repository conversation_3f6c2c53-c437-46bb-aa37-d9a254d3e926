/**
 * 文件下载工具函数
 * 提供多种下载方式，确保文件能够正确下载而不是在浏览器中打开
 */

/**
 * 通过URL下载文件（使用fetch + blob）
 * @param {string} url - 文件URL
 * @param {string} filename - 文件名
 * @returns {Promise<void>}
 */
export async function downloadFileByUrl(url, filename = 'download') {
  try {
    // 使用fetch获取文件
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    // 获取文件内容
    const blob = await response.blob();
    
    // 创建下载链接
    const downloadUrl = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = filename;
    
    // 触发下载
    document.body.appendChild(link);
    link.click();
    
    // 清理
    document.body.removeChild(link);
    window.URL.revokeObjectURL(downloadUrl);
  } catch (error) {
    console.error('Fetch下载失败:', error);
    // 回退到传统方式
    downloadFileByLink(url, filename);
  }
}

/**
 * 通过创建a标签下载文件（传统方式）
 * @param {string} url - 文件URL
 * @param {string} filename - 文件名
 */
export function downloadFileByLink(url, filename = 'download') {
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  
  // 不设置target="_blank"，确保是下载而不是打开
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}

/**
 * 下载Blob对象
 * @param {Blob} blob - Blob对象
 * @param {string} filename - 文件名
 */
export function downloadBlob(blob, filename = 'download') {
  const url = window.URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  
  document.body.appendChild(link);
  link.click();
  
  document.body.removeChild(link);
  window.URL.revokeObjectURL(url);
}

/**
 * 下载文本内容
 * @param {string} content - 文本内容
 * @param {string} filename - 文件名
 * @param {string} mimeType - MIME类型
 */
export function downloadText(content, filename = 'download.txt', mimeType = 'text/plain') {
  const blob = new Blob([content], { type: mimeType });
  downloadBlob(blob, filename);
}

/**
 * 下载JSON数据
 * @param {Object} data - JSON数据
 * @param {string} filename - 文件名
 */
export function downloadJson(data, filename = 'data.json') {
  const content = JSON.stringify(data, null, 2);
  downloadText(content, filename, 'application/json');
}

/**
 * 智能下载文件
 * 根据文件信息自动选择最佳下载方式
 * @param {Object|string} file - 文件信息对象或URL字符串
 * @param {string} fallbackFilename - 备用文件名
 * @returns {Promise<void>}
 */
export async function smartDownloadFile(file, fallbackFilename = 'download') {
  try {
    let url = '';
    let filename = fallbackFilename;
    
    if (typeof file === 'string') {
      // 如果是字符串，直接作为URL使用
      url = file;
      filename = file.split('/').pop() || fallbackFilename;
    } else if (file && typeof file === 'object') {
      // 如果是对象，提取URL和文件名
      url = file.url || file.access_url || file.download_url || file.storage_url || '';
      filename = file.name || file.filename || file.original_name || fallbackFilename;
    }
    
    if (!url) {
      throw new Error('无效的文件URL');
    }
    
    // 检查是否是有效的HTTP URL
    if (url.startsWith('http://') || url.startsWith('https://')) {
      await downloadFileByUrl(url, filename);
    } else {
      throw new Error('不支持的URL格式');
    }
  } catch (error) {
    console.error('智能下载失败:', error);
    throw error;
  }
}

/**
 * 检查浏览器是否支持下载功能
 * @returns {boolean}
 */
export function isDownloadSupported() {
  const link = document.createElement('a');
  return typeof link.download !== 'undefined';
}

/**
 * 获取文件扩展名
 * @param {string} filename - 文件名
 * @returns {string} 文件扩展名
 */
export function getFileExtension(filename) {
  if (!filename) return '';
  const lastDot = filename.lastIndexOf('.');
  return lastDot > 0 ? filename.substring(lastDot + 1).toLowerCase() : '';
}

/**
 * 根据MIME类型推断文件扩展名
 * @param {string} mimeType - MIME类型
 * @returns {string} 文件扩展名
 */
export function getExtensionFromMimeType(mimeType) {
  const mimeMap = {
    'text/plain': 'txt',
    'text/html': 'html',
    'text/css': 'css',
    'text/javascript': 'js',
    'application/json': 'json',
    'application/pdf': 'pdf',
    'application/zip': 'zip',
    'image/jpeg': 'jpg',
    'image/png': 'png',
    'image/gif': 'gif',
    'image/svg+xml': 'svg',
    'video/mp4': 'mp4',
    'audio/mp3': 'mp3',
    'audio/wav': 'wav'
  };
  
  return mimeMap[mimeType] || '';
}

/**
 * 确保文件名有正确的扩展名
 * @param {string} filename - 文件名
 * @param {string} mimeType - MIME类型（可选）
 * @returns {string} 带扩展名的文件名
 */
export function ensureFileExtension(filename, mimeType = '') {
  if (!filename) return 'download';
  
  const hasExtension = getFileExtension(filename);
  if (hasExtension) return filename;
  
  const extension = getExtensionFromMimeType(mimeType);
  return extension ? `${filename}.${extension}` : filename;
}

export default {
  downloadFileByUrl,
  downloadFileByLink,
  downloadBlob,
  downloadText,
  downloadJson,
  smartDownloadFile,
  isDownloadSupported,
  getFileExtension,
  getExtensionFromMimeType,
  ensureFileExtension
};
