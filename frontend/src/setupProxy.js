const { createProxyMiddleware } = require('http-proxy-middleware');

module.exports = function(app) {
  // Only proxy API requests to backend
  app.use(
    '/api',
    createProxyMiddleware({
      target: 'http://localhost:8000',
      changeOrigin: true,
      onError: (err, req, res) => {
        if (process.env.NODE_ENV === 'development') {
          console.error('Proxy error:', err);
        }
        res.status(500).send('Proxy error');
      }
    })
  );
};