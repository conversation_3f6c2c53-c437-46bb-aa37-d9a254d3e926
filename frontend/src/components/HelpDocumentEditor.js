import React, { useState, useRef } from 'react';
import {
  Box,
  Paper,
  Toolbar,
  IconButton,
  Divider,
  Typography,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  LinearProgress,
  Tooltip,
  Menu,
  MenuItem
} from '@mui/material';
import {
  FormatBold,
  FormatItalic,
  FormatUnderlined,
  FormatListBulleted,
  FormatListNumbered,
  Image,
  VideoLibrary,
  Link,
  Code,
  FormatQuote,
  Undo,
  Redo,
  Preview
} from '@mui/icons-material';
import { useSnackbar } from 'notistack';
import { ApiConfig } from '../config/api-config';
import MarkdownRenderer from './MarkdownRenderer';

const HelpDocumentEditor = ({
  value = '',
  onChange,
  documentId = null,
  placeholder = '请输入文档内容（支持Markdown格式）',
  height = 400,
  axiosInstance = null,
  onMediaDeleted = null // 媒体文件删除回调
}) => {
  const { enqueueSnackbar } = useSnackbar();
  const textareaRef = useRef(null);
  const fileInputRef = useRef(null);
  const videoInputRef = useRef(null);
  
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [linkDialog, setLinkDialog] = useState(false);
  const [linkText, setLinkText] = useState('');
  const [linkUrl, setLinkUrl] = useState('');
  const [previewDialog, setPreviewDialog] = useState(false);
  const [formatMenu, setFormatMenu] = useState(null);

  // 获取当前光标位置
  const getCursorPosition = () => {
    const textarea = textareaRef.current;
    if (!textarea) return { start: 0, end: 0 };

    return {
      start: textarea.selectionStart || 0,
      end: textarea.selectionEnd || 0
    };
  };

  // 在光标位置插入文本
  const insertText = (text, selectText = false) => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    const { start, end } = getCursorPosition();
    const currentValue = value || '';
    const newValue = currentValue.slice(0, start) + text + currentValue.slice(end);

    onChange(newValue);

    // 设置新的光标位置
    setTimeout(() => {
      try {
        if (textarea && typeof textarea.setSelectionRange === 'function') {
          if (selectText) {
            textarea.setSelectionRange(start, start + text.length);
          } else {
            textarea.setSelectionRange(start + text.length, start + text.length);
          }
          textarea.focus();
        }
      } catch (error) {
        console.warn('Failed to set cursor position:', error);
      }
    }, 0);
  };

  // 包装选中文本
  const wrapText = (prefix, suffix = '') => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    const { start, end } = getCursorPosition();
    const currentValue = value || '';
    const selectedText = currentValue.slice(start, end);

    if (selectedText) {
      const wrappedText = prefix + selectedText + (suffix || prefix);
      const newValue = currentValue.slice(0, start) + wrappedText + currentValue.slice(end);
      onChange(newValue);

      setTimeout(() => {
        try {
          if (textarea && typeof textarea.setSelectionRange === 'function') {
            textarea.setSelectionRange(start + prefix.length, start + prefix.length + selectedText.length);
            textarea.focus();
          }
        } catch (error) {
          console.warn('Failed to set cursor position:', error);
        }
      }, 0);
    } else {
      insertText(prefix + (suffix || prefix));
    }
  };

  // 格式化按钮处理
  const handleFormat = (type) => {
    switch (type) {
      case 'bold':
        wrapText('**');
        break;
      case 'italic':
        wrapText('*');
        break;
      case 'underline':
        wrapText('<u>', '</u>');
        break;
      case 'code':
        wrapText('`');
        break;
      case 'quote':
        insertText('\n> ');
        break;
      case 'ul':
        insertText('\n- ');
        break;
      case 'ol':
        insertText('\n1. ');
        break;
      case 'h1':
        insertText('\n# ');
        break;
      case 'h2':
        insertText('\n## ');
        break;
      case 'h3':
        insertText('\n### ');
        break;
      default:
        break;
    }
    setFormatMenu(null);
  };

  // 处理图片上传
  const handleImageUpload = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    if (!documentId) {
      enqueueSnackbar('请先保存文档后再上传图片', { variant: 'warning' });
      return;
    }

    if (!axiosInstance) {
      enqueueSnackbar('网络配置错误，无法上传文件', { variant: 'error' });
      return;
    }

    // 验证文件类型
    if (!file.type.startsWith('image/')) {
      enqueueSnackbar('只能上传图片文件', { variant: 'error' });
      return;
    }

    // 验证文件大小 (5MB)
    if (file.size > 5 * 1024 * 1024) {
      enqueueSnackbar('图片大小不能超过5MB', { variant: 'error' });
      return;
    }

    setUploading(true);
    setUploadProgress(0);

    try {
      const formData = new FormData();
      formData.append('document_id', documentId);
      formData.append('file', file);

      // 模拟上传进度
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => Math.min(prev + 10, 90));
      }, 200);

      const response = await axiosInstance.post(
        `/api/${ApiConfig.version}/admin/help/media/upload/image`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      );

      clearInterval(progressInterval);
      setUploadProgress(100);

      if (response.data.success) {
        const imageUrl = response.data.data.access_url;
        const imageMarkdown = `\n![${file.name}](${imageUrl})\n`;
        insertText(imageMarkdown);
        enqueueSnackbar('图片上传成功', { variant: 'success' });
      } else {
        enqueueSnackbar(response.data.message || '图片上传失败', { variant: 'error' });
      }
    } catch (error) {
      console.error('图片上传失败:', error);
      enqueueSnackbar(error.response?.data?.message || '图片上传失败', { variant: 'error' });
    } finally {
      setUploading(false);
      setUploadProgress(0);
      event.target.value = '';
    }
  };

  // 处理视频上传
  const handleVideoUpload = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    if (!documentId) {
      enqueueSnackbar('请先保存文档后再上传视频', { variant: 'warning' });
      return;
    }

    if (!axiosInstance) {
      enqueueSnackbar('网络配置错误，无法上传文件', { variant: 'error' });
      return;
    }

    // 验证文件类型
    const allowedTypes = ['video/mp4', 'video/avi', 'video/quicktime'];
    if (!allowedTypes.includes(file.type)) {
      enqueueSnackbar('只支持MP4、AVI、MOV格式的视频', { variant: 'error' });
      return;
    }

    // 验证文件大小 (100MB)
    if (file.size > 100 * 1024 * 1024) {
      enqueueSnackbar('视频大小不能超过100MB', { variant: 'error' });
      return;
    }

    setUploading(true);
    setUploadProgress(0);

    try {
      const formData = new FormData();
      formData.append('document_id', documentId);
      formData.append('file', file);

      // 模拟上传进度
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => Math.min(prev + 5, 90));
      }, 500);

      const response = await axiosInstance.post(
        `/api/${ApiConfig.version}/admin/help/media/upload/video`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      );

      clearInterval(progressInterval);
      setUploadProgress(100);

      if (response.data.success) {
        const videoUrl = response.data.data.access_url;
        const videoMarkdown = `\n<video controls width="100%">\n  <source src="${videoUrl}" type="${file.type}">\n  您的浏览器不支持视频播放。\n</video>\n`;
        insertText(videoMarkdown);
        enqueueSnackbar('视频上传成功', { variant: 'success' });
      } else {
        enqueueSnackbar(response.data.message || '视频上传失败', { variant: 'error' });
      }
    } catch (error) {
      console.error('视频上传失败:', error);
      enqueueSnackbar(error.response?.data?.message || '视频上传失败', { variant: 'error' });
    } finally {
      setUploading(false);
      setUploadProgress(0);
      event.target.value = '';
    }
  };

  // 插入链接
  const handleInsertLink = () => {
    if (linkText && linkUrl) {
      const linkMarkdown = `[${linkText}](${linkUrl})`;
      insertText(linkMarkdown);
      setLinkDialog(false);
      setLinkText('');
      setLinkUrl('');
    }
  };

  // 删除媒体文件
  const handleDeleteMedia = async (mediaId) => {
    if (!axiosInstance || !mediaId) return;

    try {
      const response = await axiosInstance.delete(
        `/api/${ApiConfig.version}/admin/help/media/${mediaId}`
      );

      if (response.data.success) {
        enqueueSnackbar('媒体文件删除成功', { variant: 'success' });
        if (onMediaDeleted) {
          onMediaDeleted(mediaId);
        }
      } else {
        enqueueSnackbar(response.data.message || '删除媒体文件失败', { variant: 'error' });
      }
    } catch (error) {
      console.error('删除媒体文件失败:', error);
      enqueueSnackbar(error.response?.data?.message || '删除媒体文件失败', { variant: 'error' });
    }
  };

  return (
    <Paper sx={{ border: '1px solid #e0e0e0', borderRadius: 1, overflow: 'hidden' }}>
      {/* 工具栏 */}
      <Toolbar 
        variant="dense" 
        sx={{ 
          minHeight: 48, 
          backgroundColor: '#f8f9fa', 
          borderBottom: '1px solid #e0e0e0',
          gap: 0.5
        }}
      >
        {/* 格式化按钮 */}
        <Tooltip title="粗体">
          <IconButton size="small" onClick={() => handleFormat('bold')}>
            <FormatBold fontSize="small" />
          </IconButton>
        </Tooltip>
        
        <Tooltip title="斜体">
          <IconButton size="small" onClick={() => handleFormat('italic')}>
            <FormatItalic fontSize="small" />
          </IconButton>
        </Tooltip>
        
        <Tooltip title="下划线">
          <IconButton size="small" onClick={() => handleFormat('underline')}>
            <FormatUnderlined fontSize="small" />
          </IconButton>
        </Tooltip>

        <Divider orientation="vertical" flexItem sx={{ mx: 0.5 }} />

        {/* 标题按钮 */}
        <Button
          size="small"
          onClick={(e) => setFormatMenu(e.currentTarget)}
          sx={{ minWidth: 'auto', px: 1, fontSize: '12px' }}
        >
          标题
        </Button>

        <Divider orientation="vertical" flexItem sx={{ mx: 0.5 }} />

        <Tooltip title="无序列表">
          <IconButton size="small" onClick={() => handleFormat('ul')}>
            <FormatListBulleted fontSize="small" />
          </IconButton>
        </Tooltip>
        
        <Tooltip title="有序列表">
          <IconButton size="small" onClick={() => handleFormat('ol')}>
            <FormatListNumbered fontSize="small" />
          </IconButton>
        </Tooltip>

        <Tooltip title="引用">
          <IconButton size="small" onClick={() => handleFormat('quote')}>
            <FormatQuote fontSize="small" />
          </IconButton>
        </Tooltip>

        <Tooltip title="代码">
          <IconButton size="small" onClick={() => handleFormat('code')}>
            <Code fontSize="small" />
          </IconButton>
        </Tooltip>

        <Divider orientation="vertical" flexItem sx={{ mx: 0.5 }} />

        {/* 媒体按钮 */}
        <Tooltip title="插入图片">
          <IconButton 
            size="small" 
            onClick={() => fileInputRef.current?.click()}
            disabled={uploading}
          >
            <Image fontSize="small" />
          </IconButton>
        </Tooltip>
        
        <Tooltip title="插入视频">
          <IconButton 
            size="small" 
            onClick={() => videoInputRef.current?.click()}
            disabled={uploading}
          >
            <VideoLibrary fontSize="small" />
          </IconButton>
        </Tooltip>

        <Tooltip title="插入链接">
          <IconButton size="small" onClick={() => setLinkDialog(true)}>
            <Link fontSize="small" />
          </IconButton>
        </Tooltip>

        <Divider orientation="vertical" flexItem sx={{ mx: 0.5 }} />

        <Tooltip title="预览">
          <IconButton size="small" onClick={() => setPreviewDialog(true)}>
            <Preview fontSize="small" />
          </IconButton>
        </Tooltip>

        {/* 上传进度 */}
        {uploading && (
          <Box sx={{ ml: 'auto', minWidth: 100 }}>
            <Typography variant="caption" sx={{ mr: 1 }}>
              上传中...
            </Typography>
            <LinearProgress 
              variant="determinate" 
              value={uploadProgress} 
              sx={{ width: 80, height: 4 }}
            />
          </Box>
        )}
      </Toolbar>

      {/* 编辑器 */}
      <TextField
        inputRef={textareaRef}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        multiline
        rows={height / 20}
        fullWidth
        placeholder={placeholder}
        variant="outlined"
        sx={{
          '& .MuiOutlinedInput-root': {
            '& fieldset': {
              border: 'none',
            },
          },
          '& .MuiInputBase-input': {
            fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
            fontSize: '14px',
            lineHeight: '1.5',
          },
        }}
      />

      {/* 隐藏的文件输入 */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        style={{ display: 'none' }}
        onChange={handleImageUpload}
      />
      
      <input
        ref={videoInputRef}
        type="file"
        accept="video/mp4,video/avi,video/quicktime"
        style={{ display: 'none' }}
        onChange={handleVideoUpload}
      />

      {/* 标题菜单 */}
      <Menu
        anchorEl={formatMenu}
        open={Boolean(formatMenu)}
        onClose={() => setFormatMenu(null)}
      >
        <MenuItem onClick={() => handleFormat('h1')}>标题 1</MenuItem>
        <MenuItem onClick={() => handleFormat('h2')}>标题 2</MenuItem>
        <MenuItem onClick={() => handleFormat('h3')}>标题 3</MenuItem>
      </Menu>

      {/* 链接对话框 */}
      <Dialog open={linkDialog} onClose={() => setLinkDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>插入链接</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="链接文本"
            fullWidth
            variant="outlined"
            value={linkText}
            onChange={(e) => setLinkText(e.target.value)}
            sx={{ mb: 2 }}
          />
          <TextField
            margin="dense"
            label="链接地址"
            fullWidth
            variant="outlined"
            value={linkUrl}
            onChange={(e) => setLinkUrl(e.target.value)}
            placeholder="https://example.com"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setLinkDialog(false)}>取消</Button>
          <Button onClick={handleInsertLink} variant="contained">插入</Button>
        </DialogActions>
      </Dialog>

      {/* 预览对话框 */}
      <Dialog open={previewDialog} onClose={() => setPreviewDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>内容预览</DialogTitle>
        <DialogContent>
          <Box
            sx={{
              p: 2,
              border: '1px solid #e0e0e0',
              borderRadius: 1,
              backgroundColor: '#fff',
              minHeight: 300,
              maxHeight: 500,
              overflow: 'auto'
            }}
          >
            {value ? (
              <MarkdownRenderer content={value} />
            ) : (
              <Typography variant="body1" color="text.secondary" sx={{ textAlign: 'center', py: 4 }}>
                暂无内容
              </Typography>
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPreviewDialog(false)}>关闭</Button>
        </DialogActions>
      </Dialog>
    </Paper>
  );
};

export default HelpDocumentEditor;
