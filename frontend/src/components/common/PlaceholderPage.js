import React from 'react';
import {
  Container,
  Typography,
  Paper,
  Button,
} from '@mui/material';
import {
  Construction,
  ArrowBack,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import Footer from '../layout/Footer';

function PlaceholderPage({ 
  title = '页面开发中', 
  description = '此页面正在开发中，敬请期待。',
  showBackButton = true 
}) {
  const navigate = useNavigate();

  const handleGoBack = () => {
    navigate(-1);
  };

  return (
    <>
      <Container maxWidth="md" sx={{ py: 8 }}>
        <Paper
          elevation={0}
          sx={{
            p: 6,
            textAlign: 'center',
            border: 1,
            borderColor: 'divider',
            borderRadius: 3,
          }}
        >
          <Construction
            sx={{
              fontSize: 80,
              color: 'warning.main',
              mb: 3,
            }}
          />

          <Typography variant="h4" sx={{ fontWeight: 600, mb: 2 }}>
            {title}
          </Typography>

          <Typography variant="body1" color="text.secondary" sx={{ mb: 4, maxWidth: 500, mx: 'auto' }}>
            {description}
          </Typography>

          {showBackButton && (
            <Button
              variant="outlined"
              startIcon={<ArrowBack />}
              onClick={handleGoBack}
              size="large"
            >
              返回上页
            </Button>
          )}
        </Paper>
      </Container>

      {/* Footer */}
      <Footer />
    </>
  );
}

export default PlaceholderPage;
