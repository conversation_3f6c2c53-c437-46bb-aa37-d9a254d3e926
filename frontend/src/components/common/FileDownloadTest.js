import React, { useState } from 'react';
import {
  Box,
  Typography,
  Button,
  TextField,
  Paper,
  Alert,
  CircularProgress,
  Divider
} from '@mui/material';
import {
  Download,
  Upload,
  TestTube
} from '@mui/icons-material';
import uploadService from '../../services/uploadService';

/**
 * 文件下载测试组件
 * 用于测试文件上传和下载功能
 */
function FileDownloadTest() {
  const [fileId, setFileId] = useState('');
  const [fileName, setFileName] = useState('');
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [messageType, setMessageType] = useState('info');
  const [uploadedFile, setUploadedFile] = useState(null);

  // 显示消息
  const showMessage = (text, type = 'info') => {
    setMessage(text);
    setMessageType(type);
    setTimeout(() => setMessage(''), 5000);
  };

  // 测试文件上传
  const handleTestUpload = async () => {
    try {
      setLoading(true);
      
      // 创建测试文件
      const testContent = `测试文件内容
创建时间: ${new Date().toLocaleString()}
文件用途: 下载功能测试
随机数: ${Math.random()}`;
      
      const blob = new Blob([testContent], { type: 'text/plain' });
      const testFile = new File([blob], 'download-test.txt', { type: 'text/plain' });
      
      console.log('开始上传测试文件...');
      const result = await uploadService.uploadFile(testFile, 'content', null, false, null);
      
      console.log('上传结果:', result);
      setUploadedFile(result);
      setFileId(result.file_id || '');
      setFileName('download-test.txt');
      
      showMessage('测试文件上传成功！文件ID: ' + (result.file_id || '未知'), 'success');
    } catch (error) {
      console.error('上传失败:', error);
      showMessage('上传失败: ' + error.message, 'error');
    } finally {
      setLoading(false);
    }
  };

  // 测试文件下载
  const handleTestDownload = async () => {
    if (!fileId.trim()) {
      showMessage('请先输入文件ID或上传测试文件', 'warning');
      return;
    }

    try {
      setLoading(true);
      console.log('开始测试下载，文件ID:', fileId);
      
      await uploadService.downloadFile(fileId, fileName || 'download-test.txt');
      showMessage('文件下载成功！', 'success');
    } catch (error) {
      console.error('下载失败:', error);
      showMessage('下载失败: ' + error.message, 'error');
    } finally {
      setLoading(false);
    }
  };

  // 测试获取下载链接
  const handleGetDownloadUrl = async () => {
    if (!fileId.trim()) {
      showMessage('请先输入文件ID或上传测试文件', 'warning');
      return;
    }

    try {
      setLoading(true);
      console.log('获取下载链接，文件ID:', fileId);
      
      const downloadInfo = await uploadService.getFileDownloadUrl(fileId, 1);
      console.log('下载信息:', downloadInfo);
      
      showMessage(`获取下载链接成功！URL: ${downloadInfo.download_url}`, 'success');
    } catch (error) {
      console.error('获取下载链接失败:', error);
      showMessage('获取下载链接失败: ' + error.message, 'error');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box sx={{ p: 3, maxWidth: 800, mx: 'auto' }}>
      <Typography variant="h4" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
        <TestTube />
        文件下载功能测试
      </Typography>

      {/* 消息显示 */}
      {message && (
        <Alert severity={messageType} sx={{ mb: 3 }}>
          {message}
        </Alert>
      )}

      {/* 上传测试区域 */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
          <Upload />
          1. 上传测试文件
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          点击按钮上传一个测试文件，用于后续的下载测试
        </Typography>
        <Button
          variant="contained"
          onClick={handleTestUpload}
          disabled={loading}
          startIcon={loading ? <CircularProgress size={20} /> : <Upload />}
        >
          上传测试文件
        </Button>
        
        {uploadedFile && (
          <Box sx={{ mt: 2, p: 2, bgcolor: 'grey.100', borderRadius: 1 }}>
            <Typography variant="body2">
              <strong>上传成功:</strong><br />
              文件ID: {uploadedFile.file_id}<br />
              访问URL: {uploadedFile.access_url}
            </Typography>
          </Box>
        )}
      </Paper>

      <Divider sx={{ my: 3 }} />

      {/* 下载测试区域 */}
      <Paper sx={{ p: 3 }}>
        <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
          <Download />
          2. 下载测试
        </Typography>
        
        <Box sx={{ mb: 3 }}>
          <TextField
            fullWidth
            label="文件ID"
            value={fileId}
            onChange={(e) => setFileId(e.target.value)}
            placeholder="输入要下载的文件ID"
            sx={{ mb: 2 }}
          />
          <TextField
            fullWidth
            label="文件名（可选）"
            value={fileName}
            onChange={(e) => setFileName(e.target.value)}
            placeholder="下载时使用的文件名"
          />
        </Box>

        <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
          <Button
            variant="outlined"
            onClick={handleGetDownloadUrl}
            disabled={loading}
            startIcon={loading ? <CircularProgress size={20} /> : <Download />}
          >
            获取下载链接
          </Button>
          
          <Button
            variant="contained"
            onClick={handleTestDownload}
            disabled={loading}
            startIcon={loading ? <CircularProgress size={20} /> : <Download />}
          >
            下载文件
          </Button>
        </Box>
      </Paper>

      {/* 使用说明 */}
      <Paper sx={{ p: 3, mt: 3, bgcolor: 'grey.50' }}>
        <Typography variant="h6" sx={{ mb: 2 }}>
          使用说明
        </Typography>
        <Typography variant="body2" component="div">
          <ol>
            <li>点击"上传测试文件"按钮创建一个测试文件</li>
            <li>上传成功后，文件ID会自动填入下方的输入框</li>
            <li>点击"获取下载链接"测试API是否能正确返回下载URL</li>
            <li>点击"下载文件"测试完整的下载流程</li>
            <li>检查浏览器的下载文件夹，确认文件是否正确下载</li>
          </ol>
        </Typography>
      </Paper>
    </Box>
  );
}

export default FileDownloadTest;
