import React, { useState, useEffect } from 'react';
import { Box, Toolbar } from '@mui/material';
import { useLocation } from 'react-router-dom';
import Header from './Header';
import Sidebar from './Sidebar';
import CustomerServiceChat, { CustomerServiceFab } from '../common/CustomerServiceChat';
import { useAuth } from '../../contexts/AuthContext';
import { useCustomerService } from '../../contexts/CustomerServiceContext';
import { AppConfig } from '../../config/app-config';

function Layout({ children }) {
  const { isAuthenticated } = useAuth();
  const { isChatOpen, hasNewMessage, openChat, closeChat } = useCustomerService();
  const location = useLocation();
  const [isMobile, setIsMobile] = useState(false);

  // Check if current route should show sidebar
  const shouldShowSidebar = isAuthenticated && !isPublicRoute(location.pathname);
  
  // Check if current route should hide header (for user dashboard)
  const shouldHideHeader = location.pathname.startsWith('/user/') || 
                          location.pathname.startsWith('/admin/') ||
                          location.pathname.startsWith('/super-admin/');

  // Handle responsive behavior
  useEffect(() => {
    const handleResize = () => {
      const mobile = window.innerWidth < 768;
      setIsMobile(mobile);
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);



  return (
    <Box sx={{ display: 'flex', minHeight: '100vh' }}>
      {/* Header */}
      {!shouldHideHeader && <Header />}

      <Box sx={{ display: 'flex', flexGrow: 1, pt: shouldHideHeader ? '0' : '64px' }}>
        {/* Sidebar */}
        {shouldShowSidebar && (
          <Sidebar
            isOpen={true}
            onClose={() => {}}
            width={AppConfig.layout.sidebarWidth}
          />
        )}

        {/* Main content */}
        <Box
          component="main"
          sx={{
            flexGrow: 1,
            height: shouldHideHeader ? '100vh' : 'calc(100vh - 64px)',
            overflow: 'hidden',
            display: 'flex',
            flexDirection: 'column',
          }}
        >

          {/* Page content */}
          <Box
            sx={{
              flex: 1,
              overflow: 'auto',
              '&::-webkit-scrollbar': {
                width: '8px',
              },
              '&::-webkit-scrollbar-track': {
                backgroundColor: 'rgba(0,0,0,0.1)',
                borderRadius: '4px',
              },
              '&::-webkit-scrollbar-thumb': {
                backgroundColor: 'rgba(0,0,0,0.3)',
                borderRadius: '4px',
                '&:hover': {
                  backgroundColor: 'rgba(0,0,0,0.5)',
                },
              },
            }}
          >
            {children}
          </Box>


        </Box>
      </Box>

      {/* Customer Service Chat */}
      <CustomerServiceChat open={isChatOpen} onClose={closeChat} />

      {/* Customer Service Floating Button */}
      <CustomerServiceFab onClick={openChat} hasNewMessage={hasNewMessage} />
    </Box>
  );
}

// Helper function to check if route is public
function isPublicRoute(pathname) {
  const publicRoutes = [
    '/',
    '/about',
    '/product',
    '/solutions',
    '/pricing',
    '/contact',
    '/help',
    '/docs',
    '/terms',
    '/privacy',
    '/auth/login',
    '/auth/register',
    '/auth/forgot-password',
    '/auth/reset-password',
  ];

  return publicRoutes.some(route => 
    pathname === route || pathname.startsWith(route + '/')
  );
}

export default Layout;
