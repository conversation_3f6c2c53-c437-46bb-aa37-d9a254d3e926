import React, { useState } from 'react';
import {
  <PERSON><PERSON>Bar,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>graphy,
  IconButton,
  Menu,
  MenuItem,
  Avatar,
  Box,
  Button,
  Tooltip,
} from '@mui/material';
import {
  Menu as MenuIcon,
  AccountCircle,
  Logout,
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import { useNavigate, useLocation } from 'react-router-dom';

function Header() {
  const { user, logout, isAuthenticated } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  
  const [anchorEl, setAnchorEl] = useState(null);
  const [mobileMenuAnchorEl, setMobileMenuAnchorEl] = useState(null);

  // Check if we're in dashboard/control panel area
  const isDashboardArea = location.pathname.includes('/dashboard') || 
                          location.pathname.includes('/admin') ||
                          location.pathname.includes('/user') ||
                          location.pathname.includes('/enterprise') ||
                          location.pathname.includes('/channel') ||
                          location.pathname.includes('/agent') ||
                          location.pathname.includes('/super-admin');

  const handleProfileMenuOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleProfileMenuClose = () => {
    setAnchorEl(null);
  };


  const handleLogout = async () => {
    try {
      handleProfileMenuClose();
      await logout();
      navigate('/');
    } catch (error) {
      console.error('Logout failed:', error);
      // Even if logout fails, still navigate to home page
      navigate('/');
    }
  };



  const handleLogin = () => {
    navigate('/auth/login');
  };

  const handleRegister = () => {
    navigate('/auth/register');
  };

  const handleMobileMenuOpen = (event) => {
    setMobileMenuAnchorEl(event.currentTarget);
  };

  const handleMobileMenuClose = () => {
    setMobileMenuAnchorEl(null);
  };

  const handleNavigation = (path) => {
    navigate(path);
    // Scroll to top after navigation
    setTimeout(() => {
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }, 100);
  };

  const handleMobileNavigation = (path) => {
    handleMobileMenuClose();
    handleNavigation(path);
  };

  return (
    <AppBar
      position="fixed"
      sx={{
        zIndex: (theme) => theme.zIndex.drawer + 1,
        backgroundColor: (theme) => theme.palette.mode === 'dark'
          ? 'rgba(30, 41, 59, 0.95)'
          : 'rgba(255, 255, 255, 0.95)',
        backdropFilter: 'blur(10px)',
        borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
        boxShadow: 'none',
        color: (theme) => theme.palette.text.primary,
      }}
    >
      <Toolbar sx={{ minHeight: '64px !important' }}>


        {/* Logo and title */}
        <Box sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }} onClick={() => handleNavigation('/')}>
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              mr: 1.5,
            }}
          >
            <svg
              width="40"
              height="40"
              viewBox="0 0 100 100"
              xmlns="http://www.w3.org/2000/svg"
            >
              {/* 外圆渐变背景 */}
              <defs>
                <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" stopColor="#3b82f6" />
                  <stop offset="100%" stopColor="#1e40af" />
                </linearGradient>
                <linearGradient id="innerGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" stopColor="#60a5fa" />
                  <stop offset="100%" stopColor="#3b82f6" />
                </linearGradient>
              </defs>
              
              {/* 主圆形背景 */}
              <circle cx="50" cy="50" r="45" fill="url(#bgGradient)" />
              
              {/* AI图标设计 - 类似脑神经网络 */}
              <g transform="translate(50, 50)">
                {/* 中心核心 */}
                <circle cx="0" cy="0" r="8" fill="white" opacity="0.95" />
                
                {/* 连接线 - 模拟神经网络 */}
                <g stroke="white" strokeWidth="2" fill="none" opacity="0.7">
                  <line x1="0" y1="0" x2="-20" y2="-15" />
                  <line x1="0" y1="0" x2="20" y2="-15" />
                  <line x1="0" y1="0" x2="-20" y2="15" />
                  <line x1="0" y1="0" x2="20" y2="15" />
                  <line x1="0" y1="0" x2="0" y2="-25" />
                  <line x1="0" y1="0" x2="0" y2="25" />
                </g>
                
                {/* 外围节点 */}
                <g fill="white" opacity="0.9">
                  <circle cx="-20" cy="-15" r="5" />
                  <circle cx="20" cy="-15" r="5" />
                  <circle cx="-20" cy="15" r="5" />
                  <circle cx="20" cy="15" r="5" />
                  <circle cx="0" cy="-25" r="5" />
                  <circle cx="0" cy="25" r="5" />
                </g>
                
                {/* 装饰性光环 */}
                <circle cx="0" cy="0" r="30" stroke="white" strokeWidth="1" fill="none" opacity="0.3" />
                <circle cx="0" cy="0" r="35" stroke="white" strokeWidth="0.5" fill="none" opacity="0.2" />
              </g>
            </svg>
          </Box>
          <Typography
            variant="h6"
            component="div"
            sx={{
              fontWeight: 600,
              background: 'linear-gradient(135deg, #3b82f6 0%, #1e40af 100%)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              backgroundClip: 'text',
            }}
          >
            极优云创
          </Typography>
        </Box>

        {/* Navigation Menu - only show on non-dashboard pages */}
        {!isDashboardArea && (
          <Box sx={{
            display: { xs: 'none', md: 'flex' },
            alignItems: 'center',
            gap: 4,
            flexGrow: 1,
            justifyContent: 'center',
            ml: 4
          }}>
            <Button
              color="inherit"
              onClick={() => handleNavigation('/')}
              sx={{
                textTransform: 'none',
                fontWeight: 500,
                '&:hover': { backgroundColor: 'rgba(0, 0, 0, 0.04)' }
              }}
            >
              首页
            </Button>
            <Button
              color="inherit"
              onClick={() => handleNavigation('/product')}
              sx={{
                textTransform: 'none',
                fontWeight: 500,
                '&:hover': { backgroundColor: 'rgba(0, 0, 0, 0.04)' }
              }}
            >
              产品介绍
            </Button>
            <Button
              color="inherit"
              onClick={() => handleNavigation('/pricing')}
              sx={{
                textTransform: 'none',
                fontWeight: 500,
                '&:hover': { backgroundColor: 'rgba(0, 0, 0, 0.04)' }
              }}
            >
              价格方案
            </Button>
            <Button
              color="inherit"
              onClick={() => handleNavigation('/about')}
              sx={{
                textTransform: 'none',
                fontWeight: 500,
                '&:hover': { backgroundColor: 'rgba(0, 0, 0, 0.04)' }
              }}
            >
              关于我们
            </Button>

            <Button
              color="inherit"
              onClick={() => handleNavigation('/help')}
              sx={{
                textTransform: 'none',
                fontWeight: 500,
                '&:hover': { backgroundColor: 'rgba(0, 0, 0, 0.04)' }
              }}
            >
              帮助文档
            </Button>
          </Box>
        )}
        
        {/* Spacer for dashboard area to keep header layout consistent */}
        {isDashboardArea && (
          <Box sx={{ flexGrow: 1 }} />
        )}

        {/* Right side actions */}
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          {/* Mobile menu button - only show on non-dashboard pages */}
          {!isDashboardArea && (
            <IconButton
              color="inherit"
              onClick={handleMobileMenuOpen}
              sx={{ display: { xs: 'block', md: 'none' } }}
            >
              <MenuIcon />
            </IconButton>
          )}

          {isAuthenticated ? (
            <>
              {/* Dashboard/Home Button - show different button based on area */}
              {isDashboardArea ? (
                <Button
                  variant="outlined"
                  color="inherit"
                  onClick={() => navigate('/')}
                  sx={{
                    mr: 2,
                    textTransform: 'none',
                    fontWeight: 500,
                    borderColor: 'currentColor',
                    '&:hover': {
                      borderColor: 'currentColor',
                      backgroundColor: 'rgba(255, 255, 255, 0.1)',
                    },
                  }}
                >
                  返回首页
                </Button>
              ) : (
                <Button
                  variant="contained"
                  color="primary"
                  onClick={() => navigate('/dashboard')}
                  sx={{
                    mr: 2,
                    textTransform: 'none',
                    fontWeight: 500,
                  }}
                >
                  控制台
                </Button>
              )}

              {/* Profile menu */}
              <Tooltip title="个人中心">
                <IconButton
                  color="inherit"
                  onClick={handleProfileMenuOpen}
                  sx={{ ml: 1 }}
                >
                  {user?.avatar ? (
                    <Avatar
                      src={user.avatar}
                      alt={user.name}
                      sx={{ width: 32, height: 32 }}
                    />
                  ) : (
                    <AccountCircle />
                  )}
                </IconButton>
              </Tooltip>
            </>
          ) : (
            <>
              {/* Login and Register buttons for non-authenticated users */}
              <Button
                color="inherit"
                onClick={handleLogin}
                sx={{ mr: 1 }}
              >
                登录
              </Button>
              <Button
                variant="outlined"
                color="inherit"
                onClick={handleRegister}
                sx={{
                  borderColor: 'currentColor',
                  '&:hover': {
                    borderColor: 'currentColor',
                    backgroundColor: 'rgba(255, 255, 255, 0.1)',
                  },
                }}
              >
                注册
              </Button>
            </>
          )}
        </Box>

        {/* Profile Menu */}
        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleProfileMenuClose}
          PaperProps={{
            elevation: 0,
            sx: {
              overflow: 'visible',
              filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.32))',
              mt: 1.5,
              minWidth: 200,
              '& .MuiAvatar-root': {
                width: 32,
                height: 32,
                ml: -0.5,
                mr: 1,
              },
              '&:before': {
                content: '""',
                display: 'block',
                position: 'absolute',
                top: 0,
                right: 14,
                width: 10,
                height: 10,
                bgcolor: 'background.paper',
                transform: 'translateY(-50%) rotate(45deg)',
                zIndex: 0,
              },
            },
          }}
          transformOrigin={{ horizontal: 'right', vertical: 'top' }}
          anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
        >
          {/* User Info */}
          <MenuItem disabled sx={{ opacity: '1 !important' }}>
            <Box>
              <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                {user?.name || user?.email || '用户'}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {user?.email || ''}
              </Typography>
            </Box>
          </MenuItem>
          
          {/* Divider */}
          <Box sx={{ mx: 1, my: 0.5, borderBottom: '1px solid', borderColor: 'divider' }} />
          
          {/* Logout */}
          <MenuItem onClick={handleLogout}>
            <Logout sx={{ mr: 2, fontSize: 20 }} />
            <Typography>退出登录</Typography>
          </MenuItem>
        </Menu>


        {/* Mobile Navigation Menu - only show on non-dashboard pages */}
        {!isDashboardArea && (
          <Menu
            anchorEl={mobileMenuAnchorEl}
            open={Boolean(mobileMenuAnchorEl)}
            onClose={handleMobileMenuClose}
            PaperProps={{
              elevation: 0,
              sx: {
                overflow: 'visible',
                filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.32))',
                mt: 1.5,
                minWidth: 200,
                '&:before': {
                  content: '""',
                  display: 'block',
                  position: 'absolute',
                  top: 0,
                  right: 14,
                  width: 10,
                  height: 10,
                  bgcolor: 'background.paper',
                  transform: 'translateY(-50%) rotate(45deg)',
                  zIndex: 0,
                },
              },
            }}
          >
            <MenuItem onClick={() => handleMobileNavigation('/')}>
              首页
            </MenuItem>
            <MenuItem onClick={() => handleMobileNavigation('/product')}>
              产品介绍
            </MenuItem>
            <MenuItem onClick={() => handleMobileNavigation('/pricing')}>
              价格方案
            </MenuItem>
            <MenuItem onClick={() => handleMobileNavigation('/about')}>
              关于我们
            </MenuItem>

            <MenuItem onClick={() => handleMobileNavigation('/help')}>
              帮助文档
            </MenuItem>
          </Menu>
        )}
      </Toolbar>
    </AppBar>
  );
}

export default Header;
