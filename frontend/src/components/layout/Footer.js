import React from 'react';
import {
  Box,
  Container,
  Typo<PERSON>,
  Link,
  IconButton,
  Grid,
} from '@mui/material';
import {
  Email,
  Phone,
  Chat,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';


function Footer() {
  const navigate = useNavigate();

  const handleNavigation = (path) => {
    if (path === '/#pricing') {
      // Check if we're already on the home page
      if (window.location.pathname === '/') {
        const pricingSection = document.getElementById('pricing');
        if (pricingSection) {
          pricingSection.scrollIntoView({ behavior: 'smooth' });
        }
      } else {
        // Navigate to home page first, then scroll to pricing
        navigate('/');
        setTimeout(() => {
          const pricingSection = document.getElementById('pricing');
          if (pricingSection) {
            pricingSection.scrollIntoView({ behavior: 'smooth' });
          }
        }, 200);
      }
    } else {
      navigate(path);
      // Scroll to top after navigation
      setTimeout(() => {
        window.scrollTo({ top: 0, behavior: 'smooth' });
      }, 100);
    }
  };

  const footerLinks = {
    product: [
      { title: '产品介绍', path: '/product' },
      { title: '价格方案', path: '/#pricing' },
    ],
    support: [
      { title: '帮助中心', path: '/help' },
      { title: '服务条款', path: '/terms' },
      { title: '隐私政策', path: '/privacy' },
    ],
    company: [
      { title: '关于我们', path: '/about' },
    ],
  };

  return (
    <Box
      component="footer"
      sx={{
        bgcolor: (theme) => theme.palette.mode === 'dark'
          ? '#0f172a'
          : '#1e293b',
        color: '#f1f5f9',
        py: 6,
        mt: 'auto',
        position: 'relative',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          height: '1px',
          background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent)',
        },
      }}
    >
      <Container maxWidth={false} sx={{ px: { xs: 3, sm: 6, md: 8, lg: 12 } }}>
        <Grid container spacing={4} sx={{ justifyContent: 'space-between' }}>
          {/* Company info */}
          <Grid size={{ xs: 12, md: 5 }}>
            <Box sx={{ mb: 2 }}>
              <Typography variant="h6" component="div" sx={{ fontWeight: 500 }}>
                AI搜索优化平台
              </Typography>
            </Box>
            <Typography variant="body2" sx={{ mb: 3, opacity: 0.8, lineHeight: 1.6 }}>
              专业的AI搜索引擎优化平台，让您的内容在AI回答中脱颖而出。
              支持豆包、Kimi、DeepSeek、GPT等主流AI平台。
            </Typography>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <IconButton
                sx={{
                  color: 'inherit',
                  bgcolor: 'rgba(255, 255, 255, 0.1)',
                  '&:hover': {
                    bgcolor: 'primary.main',
                    transform: 'translateY(-2px)',
                  },
                  transition: 'all 0.2s ease',
                }}
              >
                <Email />
              </IconButton>
              <IconButton
                sx={{
                  color: 'inherit',
                  bgcolor: 'rgba(255, 255, 255, 0.1)',
                  '&:hover': {
                    bgcolor: 'primary.main',
                    transform: 'translateY(-2px)',
                  },
                  transition: 'all 0.2s ease',
                }}
              >
                <Phone />
              </IconButton>
              <IconButton
                sx={{
                  color: 'inherit',
                  bgcolor: 'rgba(255, 255, 255, 0.1)',
                  '&:hover': {
                    bgcolor: 'primary.main',
                    transform: 'translateY(-2px)',
                  },
                  transition: 'all 0.2s ease',
                }}
              >
                <Chat />
              </IconButton>
            </Box>
          </Grid>

          {/* Right side navigation and contact */}
          <Grid size={{ xs: 12, md: 7 }}>
            <Grid container spacing={3} sx={{ justifyContent: 'flex-end' }}>
              {/* Product links */}
              <Grid size={{ xs: 6, sm: 3, md: 2.4 }}>
                <Typography variant="h6" sx={{ mb: 2, fontWeight: 500 }}>
                  产品
                </Typography>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                  {footerLinks.product.map((link, index) => (
                    <Link
                      key={index}
                      component="button"
                      variant="body2"
                      onClick={() => handleNavigation(link.path)}
                      sx={{
                        color: 'rgba(255, 255, 255, 0.8)',
                        textDecoration: 'none',
                        textAlign: 'left',
                        border: 'none',
                        background: 'none',
                        cursor: 'pointer',
                        p: 0,
                        '&:hover': {
                          color: 'background.paper',
                          textDecoration: 'underline',
                        },
                        transition: 'color 0.2s ease',
                      }}
                    >
                      {link.title}
                    </Link>
                  ))}
                </Box>
              </Grid>

              {/* Support links */}
              <Grid size={{ xs: 6, sm: 3, md: 2.4 }}>
                <Typography variant="h6" sx={{ mb: 2, fontWeight: 500 }}>
                  支持
                </Typography>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                  {footerLinks.support.map((link, index) => (
                    <Link
                      key={index}
                      component="button"
                      variant="body2"
                      onClick={() => handleNavigation(link.path)}
                      sx={{
                        color: 'rgba(255, 255, 255, 0.8)',
                        textDecoration: 'none',
                        textAlign: 'left',
                        border: 'none',
                        background: 'none',
                        cursor: 'pointer',
                        p: 0,
                        '&:hover': {
                          color: 'background.paper',
                          textDecoration: 'underline',
                        },
                        transition: 'color 0.2s ease',
                      }}
                    >
                      {link.title}
                    </Link>
                  ))}
                </Box>
              </Grid>

              {/* Company links */}
              <Grid size={{ xs: 6, sm: 3, md: 2.4 }}>
                <Typography variant="h6" sx={{ mb: 2, fontWeight: 500 }}>
                  公司
                </Typography>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                  {footerLinks.company.map((link, index) => (
                    <Link
                      key={index}
                      component="button"
                      variant="body2"
                      onClick={() => handleNavigation(link.path)}
                      sx={{
                        color: 'rgba(255, 255, 255, 0.8)',
                        textDecoration: 'none',
                        textAlign: 'left',
                        border: 'none',
                        background: 'none',
                        cursor: 'pointer',
                        p: 0,
                        '&:hover': {
                          color: 'background.paper',
                          textDecoration: 'underline',
                        },
                        transition: 'color 0.2s ease',
                      }}
                    >
                      {link.title}
                    </Link>
                  ))}
                </Box>
              </Grid>

              {/* Contact info */}
              <Grid size={{ xs: 6, sm: 3, md: 2.4 }}>
                <Typography variant="h6" sx={{ mb: 2, fontWeight: 500 }}>
                  联系方式
                </Typography>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                  <Typography variant="body2" sx={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                    客服邮箱：
                  </Typography>
                  <Typography variant="body2" sx={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                    <EMAIL>
                  </Typography>
                  <Typography variant="body2" sx={{ color: 'rgba(255, 255, 255, 0.8)', mt: 1 }}>
                    客服电话：
                  </Typography>
                  <Typography variant="body2" sx={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                    400-888-8888
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </Grid>
        </Grid>


      </Container>
    </Box>
  );
}

export default Footer;
