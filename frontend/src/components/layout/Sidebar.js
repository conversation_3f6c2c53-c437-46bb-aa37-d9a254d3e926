import React, { useState } from 'react';
import {
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Toolbar,
  Box,
  Typography,
  Chip,
  Avatar,
  Card,
  CardContent,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Grid,
  Alert,
  CardActions,
  Snackbar,
  ToggleButton,
  ToggleButtonGroup,
  Collapse,
} from '@mui/material';
import {
  Dashboard,
  People,
  Business,
  ContentPaste,
  ShoppingCart,
  Analytics,
  Settings,
  SmartToy,
  AccountBalance,
  Work,
  MonetizationOn,
  Person,
  Campaign,
  Assignment,
  Security,
  VpnKey,
  Store,
  CalendarMonth,
  AccessTime,
  WorkspacePremium,
  Upgrade,
  Diamond,
  Article,
  Announcement,
  ExpandLess,
  ExpandMore,
  Category,
  Description,
  Help,
  CardMembership,
  Logout,
  Payment,
} from '@mui/icons-material';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { AppConfig } from '../../config/app-config';

// Icon mapping
const iconMap = {
  Dashboard,
  People,
  Business,
  ContentPaste,
  ShoppingCart,
  Analytics,
  Settings,
  SmartToy,
  AccountBalance,
  Work,
  MonetizationOn,
  Person,
  Campaign,
  Assignment,
  Security,
  VpnKey,
  Store,
  Article,
  Announcement,
  Category,
  Description,
  Help,
  WorkspacePremium,
  CardMembership,
  Payment,
};

function Sidebar({ isOpen, onClose, width = 280 }) {
  const navigate = useNavigate();
  const location = useLocation();
  const { user, getNavigation, logout } = useAuth();
  const [showUpgradeDialog, setShowUpgradeDialog] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState('');
  const [selectedDuration, setSelectedDuration] = useState('1');
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [expandedMenus, setExpandedMenus] = useState({});

  const navigationItems = getNavigation();
  
  // 如果是超级管理员，确保包含佣金设置菜单
  if (user?.role === 'super_admin') {
    // 检查是否已经包含佣金设置菜单
    const hasCommission = navigationItems.some(item => item.path === '/super-admin/commission');
    if (!hasCommission) {
      navigationItems.push({
        path: '/super-admin/commission',
        title: '佣金设置',
        icon: 'MonetizationOn'
      });
    }
  }

  // 获取用户订阅信息（这里使用模拟数据，实际应该从用户信息中获取）
  const getSubscriptionInfo = () => {
    // 根据用户角色返回不同的套餐信息
    const userRoles = user?.roles || [];
    
    if (userRoles.includes('enterprise_user')) {
      return {
        planName: '企业版',
        planType: 'enterprise',
        expiryDate: '2024-12-31',
        daysLeft: 45,
        isExpiring: false,
      };
    } else if (userRoles.includes('channel_user') || userRoles.includes('agent_user')) {
      return {
        planName: '专业版',
        planType: 'professional', 
        expiryDate: '2024-10-15',
        daysLeft: 28,
        isExpiring: false,
      };
    } else {
      return {
        planName: '基础版',
        planType: 'basic',
        expiryDate: '2024-08-30',
        daysLeft: 7,
        isExpiring: true,
      };
    }
  };

  const subscriptionInfo = getSubscriptionInfo();

  const handleNavigation = (path) => {
    navigate(path);
    // Close drawer on mobile
    if (window.innerWidth < 768) {
      onClose();
    }
  };

  const isSelected = (path) => {
    return location.pathname === path || location.pathname.startsWith(path + '/');
  };

  const getIcon = (iconName) => {
    const IconComponent = iconMap[iconName];
    return IconComponent ? <IconComponent /> : <Dashboard />;
  };
  
  const handleToggleMenu = (menuTitle) => {
    setExpandedMenus(prev => ({
      ...prev,
      [menuTitle]: !prev[menuTitle]
    }));
  };
  
  const isMenuExpanded = (menuTitle) => {
    return expandedMenus[menuTitle] || false;
  };
  
  const hasSelectedChild = (children) => {
    if (!children) return false;
    return children.some(child => isSelected(child.path));
  };

  const drawerContent = (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* 侧边栏头部 */}
      <Box sx={{
        p: 3,
        borderBottom: '1px solid #e0e0e0'
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Dashboard sx={{ fontSize: 28, color: '#1976d2' }} />
          <Typography variant="h6" sx={{ fontWeight: 700, color: '#1a1a1a' }}>
            {user?.role === 'super_admin' ? '超级管理中心' : 
             user?.role === 'admin' ? '管理控制中心' : 
             '用户控制中心'}
          </Typography>
        </Box>
      </Box>

      {/* 用户信息区域 */}
      {user && (
        <Box sx={{
          p: 2,
          borderBottom: '1px solid #e0e0e0',
          backgroundColor: '#f8f9fa'
        }}>
          {/* 用户头像和基本信息 */}
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: user?.role === 'admin' || user?.role === 'super_admin' ? 0 : 2 }}>
            <Avatar 
              sx={{ 
                width: 48, 
                height: 48, 
                bgcolor: '#1976d2',
                fontSize: '1.2rem',
                fontWeight: 600 
              }}
            >
              {user?.name ? user.name[0] : user?.username?.[0] || 'U'}
            </Avatar>
            <Box sx={{ flex: 1 }}>
              <Typography variant="subtitle1" sx={{ fontWeight: 600, color: '#1a1a1a' }}>
                {user?.name || user?.username || '用户名称'}
              </Typography>
              <Typography variant="body2" sx={{ color: '#666' }}>
                {user?.email || '<EMAIL>'}
              </Typography>
            </Box>
          </Box>

        </Box>
      )}

      {/* Navigation menu */}
      <Box sx={{ flexGrow: 1, overflow: 'auto', py: 2 }}>
        <List>
          {navigationItems.map((item, index) => (
            <Box key={index}>
              {/* 如果有子菜单，渲染为可展开的菜单 */}
              {item.children ? (
                <>
                  <ListItemButton
                    onClick={() => handleToggleMenu(item.title)}
                    sx={{
                      mx: 2,
                      mb: 0.5,
                      borderRadius: 2,
                      backgroundColor: hasSelectedChild(item.children) ? 'rgba(25, 118, 210, 0.05)' : 'transparent',
                      '&:hover': {
                        backgroundColor: 'rgba(25, 118, 210, 0.05)',
                      }
                    }}
                  >
                    <ListItemIcon sx={{
                      color: hasSelectedChild(item.children) ? '#1976d2' : 'inherit',
                      minWidth: 40
                    }}>
                      {getIcon(item.icon)}
                    </ListItemIcon>
                    <ListItemText
                      primary={item.title}
                      sx={{
                        '& .MuiListItemText-primary': {
                          fontWeight: hasSelectedChild(item.children) ? 600 : 400,
                          color: hasSelectedChild(item.children) ? '#1976d2' : 'inherit'
                        }
                      }}
                    />
                    {isMenuExpanded(item.title) ? <ExpandLess /> : <ExpandMore />}
                  </ListItemButton>
                  <Collapse in={isMenuExpanded(item.title)} timeout="auto" unmountOnExit>
                    <List component="div" disablePadding>
                      {item.children.map((child, childIndex) => (
                        <ListItemButton
                          key={childIndex}
                          onClick={() => handleNavigation(child.path)}
                          sx={{
                            pl: 4,
                            mx: 2,
                            mb: 0.5,
                            borderRadius: 2,
                            backgroundColor: isSelected(child.path) ? 'rgba(25, 118, 210, 0.1)' : 'transparent',
                            '&:hover': {
                              backgroundColor: 'rgba(25, 118, 210, 0.05)',
                            }
                          }}
                        >
                          <ListItemIcon sx={{
                            color: isSelected(child.path) ? '#1976d2' : 'inherit',
                            minWidth: 40
                          }}>
                            {getIcon(child.icon)}
                          </ListItemIcon>
                          <ListItemText
                            primary={child.title}
                            sx={{
                              '& .MuiListItemText-primary': {
                                fontWeight: isSelected(child.path) ? 600 : 400,
                                color: isSelected(child.path) ? '#1976d2' : 'inherit',
                                fontSize: '0.9rem'
                              }
                            }}
                          />
                        </ListItemButton>
                      ))}
                    </List>
                  </Collapse>
                </>
              ) : (
                /* 如果没有子菜单，渲染为普通菜单项 */
                <ListItemButton
                  onClick={() => handleNavigation(item.path)}
                  sx={{
                    mx: 2,
                    mb: 1,
                    borderRadius: 2,
                    backgroundColor: isSelected(item.path) ? 'rgba(25, 118, 210, 0.1)' : 'transparent',
                    '&:hover': {
                      backgroundColor: 'rgba(25, 118, 210, 0.05)',
                    }
                  }}
                >
                  <ListItemIcon sx={{
                    color: isSelected(item.path) ? '#1976d2' : 'inherit',
                    minWidth: 40
                  }}>
                    {getIcon(item.icon)}
                  </ListItemIcon>
                  <ListItemText
                    primary={item.title}
                    sx={{
                      '& .MuiListItemText-primary': {
                        fontWeight: isSelected(item.path) ? 600 : 400,
                        color: isSelected(item.path) ? '#1976d2' : 'inherit'
                      }
                    }}
                  />
                </ListItemButton>
              )}
            </Box>
          ))}
        </List>
      </Box>

      {/* 登出按钮 */}
      <Box sx={{ 
        p: 2, 
        borderTop: '1px solid #e0e0e0',
        mt: 'auto'
      }}>
        <ListItemButton
          onClick={() => {
            logout();
            navigate('/auth/login');
          }}
          sx={{
            borderRadius: 2,
            color: '#dc2626',
            transition: 'all 0.2s',
            '&:hover': {
              backgroundColor: 'rgba(220, 38, 38, 0.05)',
              color: '#b91c1c',
            }
          }}
        >
          <ListItemIcon sx={{ color: 'inherit', minWidth: 40 }}>
            <Logout />
          </ListItemIcon>
          <ListItemText
            primary="退出登录"
            primaryTypographyProps={{
              fontWeight: 500
            }}
          />
        </ListItemButton>
      </Box>

    </Box>
  );

  return (
    <>
      {/* Desktop drawer */}
      <Drawer
        variant="permanent"
        anchor="left"
        sx={{
          display: { xs: 'none', md: 'block' },
          width: width,
          flexShrink: 0,
          '& .MuiDrawer-paper': {
            width: width,
            boxSizing: 'border-box',
            backgroundColor: '#fff',
            borderRight: '1px solid #e0e0e0',
            boxShadow: '2px 0 8px rgba(0,0,0,0.1)',
            top: '0',
            height: '100vh',
          },
        }}
      >
        {drawerContent}
      </Drawer>

      {/* Mobile drawer */}
      <Drawer
        variant="temporary"
        anchor="left"
        open={isOpen}
        onClose={onClose}
        ModalProps={{
          keepMounted: true, // Better open performance on mobile
        }}
        sx={{
          display: { xs: 'block', md: 'none' },
          '& .MuiDrawer-paper': {
            width: width,
            boxSizing: 'border-box',
            backgroundColor: '#fff',
            borderRight: '1px solid #e0e0e0',
            boxShadow: '2px 0 8px rgba(0,0,0,0.1)'
          },
        }}
      >
        {drawerContent}
      </Drawer>

      {/* 套餐升级对话框 */}
      <Dialog 
        open={showUpgradeDialog} 
        onClose={() => setShowUpgradeDialog(false)}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: {
            backgroundColor: '#ffffff',
            borderRadius: 2,
          }
        }}
      >
        <DialogTitle sx={{ backgroundColor: '#f8f9fa', borderBottom: '1px solid #e3e8ef' }}>
          <Box display="flex" alignItems="center" gap={2}>
            <WorkspacePremium sx={{ color: '#2563eb' }} />
            <Typography variant="h6" fontWeight="600" sx={{ color: '#1e293b' }}>
              升级套餐
            </Typography>
          </Box>
        </DialogTitle>
        <DialogContent sx={{ backgroundColor: '#ffffff', pt: 3 }}>
          {/* 当前套餐信息 */}
          <Alert 
            severity="info" 
            sx={{ 
              mb: 3,
              backgroundColor: '#eff6ff',
              border: '1px solid #3b82f6',
              '& .MuiAlert-icon': {
                color: '#3b82f6'
              }
            }}
          >
            <Typography variant="body2" sx={{ color: '#1e293b' }}>
              当前套餐：<strong>{subscriptionInfo.planName}</strong>，到期时间：{subscriptionInfo.expiryDate}
            </Typography>
          </Alert>

          {/* 套餐选项 */}
          <Typography variant="h6" sx={{ mb: 2, fontWeight: 600, color: '#1e293b' }}>
            选择套餐
          </Typography>
          <Grid container spacing={3}>
            {/* 基础版 */}
            <Grid item xs={12} md={4}>
              <Card 
                sx={{ 
                  border: selectedPlan === 'basic' ? '2px solid #3b82f6' : '1px solid #e2e8f0',
                  height: '100%',
                  position: 'relative',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  backgroundColor: '#ffffff',
                  '&:hover': {
                    borderColor: '#3b82f6',
                    boxShadow: '0 4px 12px rgba(59, 130, 246, 0.15)'
                  }
                }}
                onClick={() => setSelectedPlan('basic')}
              >
                {subscriptionInfo.planType === 'basic' && (
                  <Chip 
                    label="当前套餐" 
                    color="primary" 
                    size="small"
                    sx={{ position: 'absolute', top: 16, right: 16 }}
                  />
                )}
                <CardContent sx={{ textAlign: 'center', minHeight: 320 }}>
                  <Typography variant="h6" fontWeight="600" gutterBottom sx={{ color: '#1e293b' }}>
                    基础版
                  </Typography>
                  <Typography variant="h3" fontWeight="700" sx={{ color: '#3b82f6', mb: 2 }}>
                    ¥99
                    <Typography component="span" variant="body2" sx={{ color: '#64748b' }}>
                      /月
                    </Typography>
                  </Typography>
                  <List dense>
                    <ListItem>
                      <ListItemText primary="每月100次AI生成" />
                    </ListItem>
                    <ListItem>
                      <ListItemText primary="基础模板库" />
                    </ListItem>
                    <ListItem>
                      <ListItemText primary="基础数据分析" />
                    </ListItem>
                    <ListItem>
                      <ListItemText primary="邮件支持" />
                    </ListItem>
                  </List>
                </CardContent>
                <CardActions sx={{ justifyContent: 'center', p: 2 }}>
                  <Button 
                    variant={selectedPlan === 'basic' ? 'contained' : 'outlined'}
                    fullWidth
                    sx={{
                      borderColor: '#3b82f6',
                      color: selectedPlan === 'basic' ? '#ffffff' : '#3b82f6',
                      backgroundColor: selectedPlan === 'basic' ? '#3b82f6' : 'transparent',
                      '&:hover': {
                        backgroundColor: selectedPlan === 'basic' ? '#2563eb' : 'rgba(59, 130, 246, 0.04)'
                      }
                    }}
                  >
                    {subscriptionInfo.planType === 'basic' ? '当前套餐' : '选择基础版'}
                  </Button>
                </CardActions>
              </Card>
            </Grid>

            {/* 专业版 */}
            <Grid item xs={12} md={4}>
              <Card 
                sx={{ 
                  border: selectedPlan === 'professional' ? '2px solid #3b82f6' : '1px solid #e2e8f0',
                  height: '100%',
                  position: 'relative',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  backgroundColor: '#ffffff',
                  '&:hover': {
                    borderColor: '#3b82f6',
                    boxShadow: '0 4px 12px rgba(59, 130, 246, 0.15)'
                  }
                }}
                onClick={() => setSelectedPlan('professional')}
              >
                {subscriptionInfo.planType === 'professional' && (
                  <Chip 
                    label="当前套餐" 
                    color="primary" 
                    size="small"
                    sx={{ position: 'absolute', top: 16, right: 16 }}
                  />
                )}
                <Chip 
                  label="推荐" 
                  size="small"
                  sx={{ 
                    position: 'absolute', 
                    top: subscriptionInfo.planType === 'professional' ? 50 : 16, 
                    left: 16,
                    backgroundColor: '#fef3c7',
                    color: '#d97706',
                    fontWeight: 600
                  }}
                />
                <CardContent sx={{ textAlign: 'center', minHeight: 320 }}>
                  <Typography variant="h6" fontWeight="600" gutterBottom sx={{ color: '#1e293b' }}>
                    专业版
                  </Typography>
                  <Typography variant="h3" fontWeight="700" sx={{ color: '#3b82f6', mb: 2 }}>
                    ¥299
                    <Typography component="span" variant="body2" sx={{ color: '#64748b' }}>
                      /月
                    </Typography>
                  </Typography>
                  <List dense>
                    <ListItem>
                      <ListItemText primary="无限制AI生成" />
                    </ListItem>
                    <ListItem>
                      <ListItemText primary="全套模板库" />
                    </ListItem>
                    <ListItem>
                      <ListItemText primary="GEO监控功能" />
                    </ListItem>
                    <ListItem>
                      <ListItemText primary="高级数据分析" />
                    </ListItem>
                    <ListItem>
                      <ListItemText primary="优先技术支持" />
                    </ListItem>
                  </List>
                </CardContent>
                <CardActions sx={{ justifyContent: 'center', p: 2 }}>
                  <Button 
                    variant={selectedPlan === 'professional' ? 'contained' : 'outlined'}
                    fullWidth
                    sx={{
                      borderColor: '#3b82f6',
                      color: selectedPlan === 'professional' ? '#ffffff' : '#3b82f6',
                      backgroundColor: selectedPlan === 'professional' ? '#3b82f6' : 'transparent',
                      '&:hover': {
                        backgroundColor: selectedPlan === 'professional' ? '#2563eb' : 'rgba(59, 130, 246, 0.04)'
                      }
                    }}
                  >
                    {subscriptionInfo.planType === 'professional' ? '当前套餐' : '升级到专业版'}
                  </Button>
                </CardActions>
              </Card>
            </Grid>

            {/* 企业版 */}
            <Grid item xs={12} md={4}>
              <Card 
                sx={{ 
                  border: selectedPlan === 'enterprise' ? '2px solid #3b82f6' : '1px solid #e2e8f0',
                  height: '100%',
                  position: 'relative',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  backgroundColor: '#ffffff',
                  '&:hover': {
                    borderColor: '#3b82f6',
                    boxShadow: '0 4px 12px rgba(59, 130, 246, 0.15)'
                  }
                }}
                onClick={() => setSelectedPlan('enterprise')}
              >
                {subscriptionInfo.planType === 'enterprise' && (
                  <Chip 
                    label="当前套餐" 
                    color="primary" 
                    size="small"
                    sx={{ position: 'absolute', top: 16, right: 16 }}
                  />
                )}
                <CardContent sx={{ textAlign: 'center', minHeight: 320 }}>
                  <Typography variant="h6" fontWeight="600" gutterBottom sx={{ color: '#1e293b' }}>
                    企业版
                  </Typography>
                  <Typography variant="h3" fontWeight="700" sx={{ color: '#3b82f6', mb: 2 }}>
                    ¥599
                    <Typography component="span" variant="body2" sx={{ color: '#64748b' }}>
                      /月
                    </Typography>
                  </Typography>
                  <List dense>
                    <ListItem>
                      <ListItemText primary="所有专业版功能" />
                    </ListItem>
                    <ListItem>
                      <ListItemText primary="多用户协作" />
                    </ListItem>
                    <ListItem>
                      <ListItemText primary="API接口" />
                    </ListItem>
                    <ListItem>
                      <ListItemText primary="定制化服务" />
                    </ListItem>
                    <ListItem>
                      <ListItemText primary="专属客户经理" />
                    </ListItem>
                  </List>
                </CardContent>
                <CardActions sx={{ justifyContent: 'center', p: 2 }}>
                  <Button 
                    variant={selectedPlan === 'enterprise' ? 'contained' : 'outlined'}
                    fullWidth
                    sx={{
                      borderColor: '#3b82f6',
                      color: selectedPlan === 'enterprise' ? '#ffffff' : '#3b82f6',
                      backgroundColor: selectedPlan === 'enterprise' ? '#3b82f6' : 'transparent',
                      '&:hover': {
                        backgroundColor: selectedPlan === 'enterprise' ? '#2563eb' : 'rgba(59, 130, 246, 0.04)'
                      }
                    }}
                  >
                    {subscriptionInfo.planType === 'enterprise' ? '当前套餐' : '升级到企业版'}
                  </Button>
                </CardActions>
              </Card>
            </Grid>
          </Grid>

          {/* 续费选项 */}
          <Box sx={{ mt: 4 }}>
            <Typography variant="h6" gutterBottom fontWeight="600" sx={{ color: '#1e293b', mb: 2 }}>
              选择付款周期
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={4}>
                <Card 
                  variant="outlined"
                  sx={{ 
                    border: selectedDuration === '1' ? '2px solid #3b82f6' : '1px solid #e2e8f0',
                    cursor: 'pointer',
                    transition: 'all 0.3s ease',
                    backgroundColor: selectedDuration === '1' ? '#eff6ff' : '#ffffff',
                    '&:hover': {
                      borderColor: '#3b82f6',
                      backgroundColor: '#f8fafc'
                    }
                  }}
                  onClick={() => setSelectedDuration('1')}
                >
                  <CardContent sx={{ textAlign: 'center', py: 3, minHeight: 120 }}>
                    <Typography variant="h6" gutterBottom sx={{ color: '#1e293b', fontWeight: 600 }}>
                      月付
                    </Typography>
                    <Typography variant="body2" sx={{ color: '#64748b', mb: 1 }}>
                      1个月
                    </Typography>
                    <Typography variant="h5" sx={{ color: '#3b82f6', fontWeight: 700 }}>
                      原价
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} sm={4}>
                <Card 
                  variant="outlined" 
                  sx={{ 
                    border: selectedDuration === '6' ? '2px solid #3b82f6' : '1px solid #e2e8f0',
                    cursor: 'pointer',
                    transition: 'all 0.3s ease',
                    backgroundColor: selectedDuration === '6' ? '#eff6ff' : '#ffffff',
                    position: 'relative',
                    '&:hover': {
                      borderColor: '#3b82f6',
                      backgroundColor: '#f8fafc'
                    }
                  }}
                  onClick={() => setSelectedDuration('6')}
                >
                  <Chip 
                    label="热门" 
                    size="small" 
                    sx={{ 
                      position: 'absolute', 
                      top: -10, 
                      right: 16,
                      backgroundColor: '#fbbf24',
                      color: '#78350f',
                      fontWeight: 600
                    }} 
                  />
                  <CardContent sx={{ textAlign: 'center', py: 3, minHeight: 120 }}>
                    <Typography variant="h6" gutterBottom sx={{ color: '#1e293b', fontWeight: 600 }}>
                      半年付
                    </Typography>
                    <Typography variant="body2" sx={{ color: '#64748b', mb: 1 }}>
                      6个月
                    </Typography>
                    <Typography variant="h5" sx={{ color: '#f59e0b', fontWeight: 700 }}>
                      9折优惠
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} sm={4}>
                <Card 
                  variant="outlined" 
                  sx={{ 
                    border: selectedDuration === '12' ? '2px solid #3b82f6' : '1px solid #e2e8f0',
                    cursor: 'pointer',
                    transition: 'all 0.3s ease',
                    backgroundColor: selectedDuration === '12' ? '#eff6ff' : '#ffffff',
                    position: 'relative',
                    '&:hover': {
                      borderColor: '#3b82f6',
                      backgroundColor: '#f8fafc'
                    }
                  }}
                  onClick={() => setSelectedDuration('12')}
                >
                  <Chip 
                    label="最优惠" 
                    size="small" 
                    sx={{ 
                      position: 'absolute', 
                      top: -10, 
                      right: 16,
                      backgroundColor: '#10b981',
                      color: '#ffffff',
                      fontWeight: 600
                    }} 
                  />
                  <CardContent sx={{ textAlign: 'center', py: 3, minHeight: 120 }}>
                    <Typography variant="h6" gutterBottom sx={{ color: '#1e293b', fontWeight: 600 }}>
                      年付
                    </Typography>
                    <Typography variant="body2" sx={{ color: '#64748b', mb: 1 }}>
                      12个月
                    </Typography>
                    <Typography variant="h5" sx={{ color: '#10b981', fontWeight: 700 }}>
                      8折优惠
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions sx={{ p: 3, pt: 1, backgroundColor: '#f8f9fa', borderTop: '1px solid #e3e8ef' }}>
          <Button 
            onClick={() => setShowUpgradeDialog(false)}
            sx={{ 
              color: '#64748b',
              '&:hover': {
                backgroundColor: 'rgba(100, 116, 139, 0.04)'
              }
            }}
          >
            取消
          </Button>
          <Button 
            variant="contained" 
            startIcon={<Diamond />}
            onClick={() => {
              if (selectedPlan && selectedDuration) {
                const planNames = {
                  'basic': '基础版',
                  'professional': '专业版',
                  'enterprise': '企业版'
                };
                const durationNames = {
                  '1': '1个月',
                  '6': '6个月',
                  '12': '12个月'
                };
                setSuccessMessage(`成功订阅${planNames[selectedPlan]}（${durationNames[selectedDuration]}）！`);
                setShowSuccessMessage(true);
                setShowUpgradeDialog(false);
                setSelectedPlan('');
                setSelectedDuration('1');
              }
            }}
            disabled={!selectedPlan || !selectedDuration}
            sx={{
              backgroundColor: '#3b82f6',
              '&:hover': {
                backgroundColor: '#2563eb'
              },
              '&:disabled': {
                backgroundColor: '#cbd5e1'
              }
            }}
          >
            确认订阅
          </Button>
        </DialogActions>
      </Dialog>

      {/* 付款成功提示 */}
      <Snackbar
        open={showSuccessMessage}
        autoHideDuration={3000}
        onClose={() => setShowSuccessMessage(false)}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert 
          onClose={() => setShowSuccessMessage(false)} 
          severity="success" 
          sx={{ 
            width: '100%',
            backgroundColor: '#10b981',
            color: '#ffffff',
            '& .MuiAlert-icon': {
              color: '#ffffff'
            }
          }}
        >
          {successMessage}
        </Alert>
      </Snackbar>
    </>
  );
}

export default Sidebar;
