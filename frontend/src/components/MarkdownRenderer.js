import React from 'react';
import { Box, Typography } from '@mui/material';

const MarkdownRenderer = ({ content, sx = {} }) => {
  // 简单的Markdown解析函数
  const parseMarkdown = (text) => {
    if (!text) return [];

    const lines = text.split('\n');
    const elements = [];
    let currentIndex = 0;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const trimmedLine = line.trim();

      // 跳过空行
      if (!trimmedLine) {
        elements.push({ type: 'br', key: currentIndex++ });
        continue;
      }

      // 标题
      if (trimmedLine.startsWith('# ')) {
        elements.push({
          type: 'h1',
          content: trimmedLine.substring(2),
          key: currentIndex++
        });
      } else if (trimmedLine.startsWith('## ')) {
        elements.push({
          type: 'h2',
          content: trimmedLine.substring(3),
          key: currentIndex++
        });
      } else if (trimmedLine.startsWith('### ')) {
        elements.push({
          type: 'h3',
          content: trimmedLine.substring(4),
          key: currentIndex++
        });
      }
      // 图片
      else if (trimmedLine.match(/!\[.*?\]\(.*?\)/)) {
        const match = trimmedLine.match(/!\[(.*?)\]\((.*?)\)/);
        if (match) {
          elements.push({
            type: 'image',
            alt: match[1],
            src: match[2],
            key: currentIndex++
          });
        }
      }
      // 视频 (HTML video标签)
      else if (trimmedLine.includes('<video') && trimmedLine.includes('</video>')) {
        // 提取video标签中的src
        const srcMatch = trimmedLine.match(/src="([^"]+)"/);
        if (srcMatch) {
          elements.push({
            type: 'video',
            src: srcMatch[1],
            key: currentIndex++
          });
        }
      }
      // 引用
      else if (trimmedLine.startsWith('> ')) {
        elements.push({
          type: 'quote',
          content: trimmedLine.substring(2),
          key: currentIndex++
        });
      }
      // 无序列表
      else if (trimmedLine.startsWith('- ')) {
        elements.push({
          type: 'ul',
          content: trimmedLine.substring(2),
          key: currentIndex++
        });
      }
      // 有序列表
      else if (trimmedLine.match(/^\d+\. /)) {
        const match = trimmedLine.match(/^\d+\. (.+)/);
        if (match) {
          elements.push({
            type: 'ol',
            content: match[1],
            key: currentIndex++
          });
        }
      }
      // 代码块
      else if (trimmedLine.startsWith('```')) {
        // 查找代码块结束
        let codeContent = [];
        let j = i + 1;
        while (j < lines.length && !lines[j].trim().startsWith('```')) {
          codeContent.push(lines[j]);
          j++;
        }
        elements.push({
          type: 'code',
          content: codeContent.join('\n'),
          key: currentIndex++
        });
        i = j; // 跳过代码块内容
      }
      // 普通段落
      else {
        // 处理内联格式
        let processedContent = trimmedLine;
        
        // 粗体 **text**
        processedContent = processedContent.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
        
        // 斜体 *text*
        processedContent = processedContent.replace(/\*(.*?)\*/g, '<em>$1</em>');
        
        // 下划线 <u>text</u>
        processedContent = processedContent.replace(/<u>(.*?)<\/u>/g, '<u>$1</u>');
        
        // 内联代码 `code`
        processedContent = processedContent.replace(/`(.*?)`/g, '<code>$1</code>');
        
        // 链接 [text](url)
        processedContent = processedContent.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>');

        elements.push({
          type: 'paragraph',
          content: processedContent,
          key: currentIndex++
        });
      }
    }

    return elements;
  };

  // 渲染元素
  const renderElement = (element) => {
    switch (element.type) {
      case 'h1':
        return (
          <Typography 
            key={element.key} 
            variant="h4" 
            component="h1" 
            sx={{ mb: 2, mt: 3, fontWeight: 'bold' }}
          >
            {element.content}
          </Typography>
        );
      
      case 'h2':
        return (
          <Typography 
            key={element.key} 
            variant="h5" 
            component="h2" 
            sx={{ mb: 2, mt: 2.5, fontWeight: 'bold' }}
          >
            {element.content}
          </Typography>
        );
      
      case 'h3':
        return (
          <Typography 
            key={element.key} 
            variant="h6" 
            component="h3" 
            sx={{ mb: 1.5, mt: 2, fontWeight: 'bold' }}
          >
            {element.content}
          </Typography>
        );
      
      case 'image':
        return (
          <Box key={element.key} sx={{ my: 2, textAlign: 'center' }}>
            <img
              src={element.src}
              alt={element.alt}
              style={{
                maxWidth: '100%',
                height: 'auto',
                borderRadius: '8px',
                boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
              }}
              onLoad={(e) => {
                console.log('图片加载成功:', element.src);
              }}
              onError={(e) => {
                console.error('图片加载失败:', element.src);
                e.target.style.display = 'none';
                e.target.nextSibling.style.display = 'block';
              }}
            />
            <Typography
              variant="body2"
              color="error"
              sx={{ display: 'none', mt: 1, p: 2, backgroundColor: '#ffebee', borderRadius: 1 }}
            >
              图片加载失败: {element.alt}
              <br />
              <Typography variant="caption" component="span">
                URL: {element.src}
              </Typography>
            </Typography>
          </Box>
        );
      
      case 'video':
        return (
          <Box key={element.key} sx={{ my: 2, textAlign: 'center' }}>
            <video
              controls
              style={{
                maxWidth: '100%',
                height: 'auto',
                borderRadius: '8px',
                boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
              }}
              onError={(e) => {
                e.target.style.display = 'none';
                e.target.nextSibling.style.display = 'block';
              }}
            >
              <source src={element.src} />
              您的浏览器不支持视频播放。
            </video>
            <Typography 
              variant="body2" 
              color="error" 
              sx={{ display: 'none', mt: 1 }}
            >
              视频加载失败
            </Typography>
          </Box>
        );
      
      case 'quote':
        return (
          <Box 
            key={element.key} 
            sx={{ 
              borderLeft: '4px solid #e0e0e0',
              pl: 2,
              py: 1,
              my: 1,
              backgroundColor: '#f9f9f9',
              fontStyle: 'italic'
            }}
          >
            <Typography variant="body1">{element.content}</Typography>
          </Box>
        );
      
      case 'ul':
        return (
          <Box key={element.key} sx={{ ml: 2, my: 0.5 }}>
            <Typography variant="body1" component="li">
              {element.content}
            </Typography>
          </Box>
        );
      
      case 'ol':
        return (
          <Box key={element.key} sx={{ ml: 2, my: 0.5 }}>
            <Typography variant="body1" component="li">
              {element.content}
            </Typography>
          </Box>
        );
      
      case 'code':
        return (
          <Box 
            key={element.key} 
            sx={{ 
              backgroundColor: '#f5f5f5',
              border: '1px solid #e0e0e0',
              borderRadius: 1,
              p: 2,
              my: 2,
              overflow: 'auto'
            }}
          >
            <Typography 
              variant="body2" 
              component="pre"
              sx={{ 
                fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
                margin: 0,
                whiteSpace: 'pre-wrap'
              }}
            >
              {element.content}
            </Typography>
          </Box>
        );
      
      case 'paragraph':
        return (
          <Typography 
            key={element.key} 
            variant="body1" 
            sx={{ mb: 1, lineHeight: 1.6 }}
            dangerouslySetInnerHTML={{ __html: element.content }}
          />
        );
      
      case 'br':
        return <br key={element.key} />;
      
      default:
        return null;
    }
  };

  const elements = parseMarkdown(content);

  return (
    <Box sx={{ 
      '& a': {
        color: 'primary.main',
        textDecoration: 'none',
        '&:hover': {
          textDecoration: 'underline'
        }
      },
      '& code': {
        backgroundColor: '#f5f5f5',
        padding: '2px 4px',
        borderRadius: '4px',
        fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
        fontSize: '0.9em'
      },
      ...sx 
    }}>
      {elements.map(renderElement)}
    </Box>
  );
};

export default MarkdownRenderer;
