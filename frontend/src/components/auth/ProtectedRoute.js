import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { Box, CircularProgress, Typography } from '@mui/material';
import { useAuth } from '../../contexts/AuthContext';

function ProtectedRoute({ children, roles = [] }) {
  const { isAuthenticated, isLoading, user } = useAuth();
  const location = useLocation();

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: '50vh',
          gap: 2,
        }}
      >
        <CircularProgress size={40} />
        <Typography variant="body2" color="text.secondary">
          正在验证身份...
        </Typography>
      </Box>
    );
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    return (
      <Navigate
        to="/auth/login"
        state={{ from: location }}
        replace
      />
    );
  }

  // Check role-based access
  if (roles.length > 0 && user) {
    // Check if user has any of the required roles
    const userRoles = user.roles || [user.role]; // Support both roles array and single role
    const hasRequiredRole = roles.some(requiredRole => userRoles.includes(requiredRole));

    if (!hasRequiredRole) {
      return <Navigate to="/error/403" replace />;
    }
  }

  return children;
}

export default ProtectedRoute;
