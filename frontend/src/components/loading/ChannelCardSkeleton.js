import React from 'react';
import {
  Box,
  Card,
  CardContent,
  Skeleton,
  Typography
} from '@mui/material';
import { styled } from '@mui/material/styles';

const SkeletonCard = styled(Card)(() => ({
  width: '320px',
  height: '300px',
  display: 'flex',
  flexDirection: 'column',
  borderRadius: '0',
  border: '1px solid #e5e5e5',
  boxShadow: 'none',
  backgroundColor: '#fff',
  overflow: 'hidden',
}));

const ChannelCardSkeleton = () => {
  return (
    <SkeletonCard>
      <CardContent sx={{ p: 2.5, height: '100%', position: 'relative' }}>
        {/* 标题区域骨架 */}
        <Box sx={{ height: '60px', mb: 1.5 }}>
          <Skeleton 
            variant="text" 
            width="85%" 
            height={24} 
            sx={{ mb: 0.5 }}
            animation="wave"
          />
          <Skeleton 
            variant="text" 
            width="60%" 
            height={20}
            animation="wave"
          />
          <Box sx={{ display: 'flex', gap: 0.5, mt: 0.5 }}>
            <Skeleton 
              variant="rounded" 
              width={60} 
              height={20}
              animation="wave"
            />
            <Skeleton 
              variant="circular" 
              width={16} 
              height={16}
              animation="wave"
            />
          </Box>
        </Box>

        {/* 分类和平台信息骨架 */}
        <Box sx={{ height: '40px', mb: 1.5 }}>
          <Box sx={{ display: 'flex', gap: 0.5 }}>
            <Skeleton 
              variant="rounded" 
              width={80} 
              height={24}
              animation="wave"
            />
            <Skeleton 
              variant="rounded" 
              width={70} 
              height={24}
              animation="wave"
            />
          </Box>
        </Box>

        {/* 描述区域骨架 */}
        <Box sx={{ height: '40px', mb: 1.5 }}>
          <Skeleton 
            variant="text" 
            width="100%" 
            height={16}
            animation="wave"
          />
          <Skeleton 
            variant="text" 
            width="75%" 
            height={16}
            animation="wave"
          />
        </Box>

        {/* 统计信息骨架 */}
        <Box sx={{ height: '30px', mb: 1.5, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
            <Skeleton 
              variant="circular" 
              width={14} 
              height={14}
              animation="wave"
            />
            <Skeleton 
              variant="text" 
              width={40} 
              height={16}
              animation="wave"
            />
          </Box>
          <Skeleton 
            variant="text" 
            width={60} 
            height={16}
            animation="wave"
          />
        </Box>

        {/* 底部操作区域骨架 */}
        <Box sx={{
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          height: '50px',
          backgroundColor: '#fff',
          borderTop: '1px solid #f0f0f0',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          px: 2.5
        }}>
          <Skeleton 
            variant="rounded" 
            width={80} 
            height={32}
            animation="wave"
          />
        </Box>
      </CardContent>
    </SkeletonCard>
  );
};

// 骨架屏网格组件
export const ChannelSkeletonGrid = ({ count = 6 }) => {
  return (
    <Box
      sx={{
        display: 'flex',
        flexWrap: 'wrap',
        gap: '24px',
        justifyContent: 'flex-start',
        alignItems: 'flex-start',
        width: '100%',
        '& > *': {
          flexShrink: 0,
          flexGrow: 0,
        }
      }}
    >
      {Array.from({ length: count }).map((_, index) => (
        <ChannelCardSkeleton key={index} />
      ))}
    </Box>
  );
};

export default ChannelCardSkeleton;
