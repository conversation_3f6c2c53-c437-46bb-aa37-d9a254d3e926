import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  Card,
  CardContent,
  Chip,
  IconButton,
} from '@mui/material';
import { Store, Info } from '@mui/icons-material';
import channelService from '../../services/channelService';

function ChannelBindingForm({ open, onClose, onSubmit, loading, boundServices = [] }) {
  const [formData, setFormData] = useState({
    // 服务选择
    category_id: '',
    service_id: '',

    // 服务配置选项
    custom_price: '不限',
    custom_delivery_time: '不限',
    custom_revision_count: '',
    custom_coverage_area: [],
    custom_channel_category: '不限',  // 频道类别（对应 channel_type 字段）
    custom_portal_type: '不限',       // 综合门户（对应 portal_type 字段）
    custom_service_specs: {},
    custom_platform_specs: {},

    // 绑定设置
    is_active: true
  });

  const [categories, setCategories] = useState([]);
  const [loadingCategories, setLoadingCategories] = useState(false);
  const [services, setServices] = useState([]);
  const [loadingServices, setLoadingServices] = useState(false);
  const [selectedService, setSelectedService] = useState(null);
  const [serviceDetailOpen, setServiceDetailOpen] = useState(false);
  const [viewingService, setViewingService] = useState(null);

  // 可选配置项
  const [availableCoverageAreas] = useState([
    '全国', '华北', '华东', '华南', '华中', '西南', '西北', '东北',
    '北京', '上海', '广州', '深圳', '杭州', '南京', '成都', '武汉', '西安'
  ]);

  // 频道类别（对应数据库最后一个字段）
  const [availableChannelCategories] = useState([
    '新闻资讯', '娱乐内容', '科技数码', '财经商业', '教育培训', '健康养生',
    '美食旅游', '时尚生活', '体育运动', '汽车房产', '母婴亲子', '游戏动漫'
  ]);

  // 综合门户（对应 portal_type 字段）
  const [availablePortalTypes] = useState([
    '微信公众号', '微博', '抖音', '快手', '小红书', 'B站', '知乎', '今日头条',
    '新闻门户', '行业门户', '地方门户', '企业门户', '电商平台', '社交平台'
  ]);





  // 加载渠道分类数据
  const loadCategories = async () => {
    setLoadingCategories(true);
    try {
      const response = await channelService.getChannelCategories();
      if (response.success && response.data && response.data.categories) {
        const allCategories = response.data.categories;
        setCategories(allCategories);
      } else {
        setCategories([]);
      }
    } catch (error) {
      setCategories([]);
    } finally {
      setLoadingCategories(false);
    }
  };

  // 根据筛选条件加载服务数据
  const loadServicesWithFilters = async (categoryId, filters = {}) => {
    if (!categoryId) {
      setServices([]);
      return;
    }

    setLoadingServices(true);
    try {
      // 构建查询参数
      const params = {
        category_id: categoryId,
        ...filters
      };

      // 获取系统服务列表
      const response = await channelService.getSystemServices(params);
      if (response.success && response.data && response.data.items) {
        // 过滤掉已绑定的服务
        const boundServiceIds = boundServices.map(service => service.serviceId);

        const availableServices = response.data.items.filter(service =>
          !boundServiceIds.includes(service.id)
        );

        setServices(availableServices);
      } else {
        setServices([]);
      }
    } catch (error) {
      setServices([]);
    } finally {
      setLoadingServices(false);
    }
  };



  // 当对话框打开时加载分类数据
  useEffect(() => {
    if (open) {
      loadCategories();
      // 重置所有数据
      setServices([]);
      setSelectedService(null);
      setFormData({
        category_id: '',
        service_id: '',
        custom_price: '不限',
        custom_delivery_time: '不限',
        custom_revision_count: '',
        custom_coverage_area: [],
        custom_channel_category: '不限',
        custom_portal_type: '不限',
        custom_service_specs: {},
        custom_platform_specs: {},
        is_active: true
      });
    }
  }, [open]);

  // 当已绑定服务列表变化时，重新应用筛选条件加载服务
  useEffect(() => {
    if (formData.category_id && services.length > 0) {
      // 过滤掉已绑定的服务
      const boundServiceIds = boundServices.map(service => service.serviceId);
      const availableServices = services.filter(service =>
        !boundServiceIds.includes(service.id)
      );
      setServices(availableServices);
    }
  }, [boundServices]);

  const handleChange = (field, value) => {
    const newFormData = {
      ...formData,
      [field]: value
    };

    setFormData(newFormData);

    // 当渠道分类改变时，清空服务选择并自动加载服务
    if (field === 'category_id') {
      setFormData(prev => ({ ...prev, service_id: '' }));
      setSelectedService(null);
      // 自动加载该分类下的所有服务
      setTimeout(() => {
        loadServicesWithFilters(value, {});
      }, 100);
    }

    // 当筛选条件改变时，自动重新加载服务
    if (['custom_channel_category', 'custom_portal_type', 'custom_price', 'custom_delivery_time', 'custom_coverage_area'].includes(field)) {
      if (newFormData.category_id) {
        setTimeout(() => {
          applyFiltersWithFormData(newFormData);
        }, 100);
      }
    }

    // 当服务改变时，设置选中的服务信息
    if (field === 'service_id') {
      const service = services.find(s => s.id === value);
      setSelectedService(service || null);
    }
  };

  // 使用指定的表单数据应用筛选条件
  const applyFiltersWithFormData = (formData) => {
    if (!formData.category_id) return;

    const filters = {};

    // 价格筛选
    if (formData.custom_price && formData.custom_price !== '不限') {
      if (formData.custom_price === '100以下') {
        filters.max_price = 100;
      } else if (formData.custom_price === '100-300') {
        filters.min_price = 100;
        filters.max_price = 300;
      } else if (formData.custom_price === '300-500') {
        filters.min_price = 300;
        filters.max_price = 500;
      } else if (formData.custom_price === '500-1000') {
        filters.min_price = 500;
        filters.max_price = 1000;
      } else if (formData.custom_price === '1000以上') {
        filters.min_price = 1000;
      }
    }

    // 交付时间筛选
    if (formData.custom_delivery_time && formData.custom_delivery_time !== '不限') {
      if (formData.custom_delivery_time === '24小时内') {
        filters.max_delivery_time = 24;
      } else if (formData.custom_delivery_time === '3天内') {
        filters.max_delivery_time = 72;
      } else if (formData.custom_delivery_time === '7天内') {
        filters.max_delivery_time = 168;
      } else if (formData.custom_delivery_time === '15天内') {
        filters.max_delivery_time = 360;
      } else if (formData.custom_delivery_time === '30天内') {
        filters.max_delivery_time = 720;
      }
    }

    // 覆盖区域筛选
    if (formData.custom_coverage_area && formData.custom_coverage_area.length > 0) {
      filters.coverage_area = formData.custom_coverage_area.join(',');
    }

    // 频道类别筛选（对应 channel_type 字段）
    if (formData.custom_channel_category && formData.custom_channel_category !== '不限') {
      filters.channel_type = formData.custom_channel_category;
    }

    // 综合门户筛选（对应 portal_type 字段）
    if (formData.custom_portal_type && formData.custom_portal_type !== '不限') {
      filters.portal_type = formData.custom_portal_type;
    }

    loadServicesWithFilters(formData.category_id, filters);
  };



  const handleSubmit = () => {
    // 只发送后端需要的字段
    const bindingData = {
      service_id: formData.service_id,
      is_active: formData.is_active
    };

    onSubmit(bindingData);
  };

  // 查看服务详情
  const handleViewServiceDetail = async (service) => {
    try {
      // 获取完整的服务详情
      const response = await channelService.getServiceDetail(service.id);
      if (response && response.success) {
        setViewingService(response.data);
        setServiceDetailOpen(true);
      }
    } catch (error) {
      // 获取服务详情失败，静默处理
    }
  };

  const handleClose = () => {
    // Reset form on close
    setFormData({
      category_id: '',
      service_id: '',
      custom_price: '不限',
      custom_delivery_time: '不限',
      custom_revision_count: '',
      custom_coverage_area: [],
      custom_channel_category: '不限',
      custom_portal_type: '不限',
      custom_service_specs: {},
      custom_platform_specs: {},
      is_active: true
    });
    setServices([]);
    setSelectedService(null);
    onClose();
  };

  return (
    <>
      <Dialog
        open={open}
        onClose={handleClose}
        maxWidth="lg"
        fullWidth
      >
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Store sx={{ color: '#FF6B35' }} />
          绑定渠道服务
        </Box>
      </DialogTitle>

      <DialogContent>
        <Box sx={{ pt: 2 }}>
          {/* 渠道分类标签 */}
          <Box sx={{ mb: 4 }}>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 600, color: '#333' }}>
              选择渠道
            </Typography>
            <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
              {categories.map((category) => (
                <Chip
                  key={category.id}
                  label={category.category_name}
                  clickable
                  color={formData.category_id === category.id ? 'primary' : 'default'}
                  variant={formData.category_id === category.id ? 'filled' : 'outlined'}
                  onClick={() => handleChange('category_id', category.id)}
                  sx={{
                    borderRadius: '16px',
                    fontSize: '14px',
                    height: '36px',
                    '&:hover': {
                      backgroundColor: formData.category_id === category.id ? undefined : '#f5f5f5'
                    }
                  }}
                />
              ))}
            </Box>
          </Box>

          {/* 筛选条件 */}
          {formData.category_id && (
            <Box sx={{ mb: 4 }}>
              <Typography variant="h6" sx={{ mb: 2, fontWeight: 600, color: '#333' }}>
                筛选条件
              </Typography>

              {/* 频道类别 */}
              <Box sx={{ mb: 3 }}>
                <Typography variant="subtitle2" sx={{ mb: 1, color: '#666' }}>
                  频道类别：
                </Typography>
                <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                  {['不限', ...availableChannelCategories].map((category) => (
                    <Chip
                      key={category}
                      label={category}
                      clickable
                      size="small"
                      color={formData.custom_channel_category === category ? 'primary' : 'default'}
                      variant={formData.custom_channel_category === category ? 'filled' : 'outlined'}
                      onClick={() => handleChange('custom_channel_category', formData.custom_channel_category === category ? '不限' : category)}
                      sx={{ borderRadius: '12px' }}
                    />
                  ))}
                </Box>
              </Box>

              {/* 综合门户 */}
              <Box sx={{ mb: 3 }}>
                <Typography variant="subtitle2" sx={{ mb: 1, color: '#666' }}>
                  综合门户：
                </Typography>
                <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                  {['不限', ...availablePortalTypes].map((type) => (
                    <Chip
                      key={type}
                      label={type}
                      clickable
                      size="small"
                      color={formData.custom_portal_type === type ? 'primary' : 'default'}
                      variant={formData.custom_portal_type === type ? 'filled' : 'outlined'}
                      onClick={() => handleChange('custom_portal_type', formData.custom_portal_type === type ? '不限' : type)}
                      sx={{ borderRadius: '12px' }}
                    />
                  ))}
                </Box>
              </Box>

              {/* 价格范围 */}
              <Box sx={{ mb: 3 }}>
                <Typography variant="subtitle2" sx={{ mb: 1, color: '#666' }}>
                  价格范围：
                </Typography>
                <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                  {['不限', '100以下', '100-300', '300-500', '500-1000', '1000以上'].map((price) => (
                    <Chip
                      key={price}
                      label={price}
                      clickable
                      size="small"
                      color={formData.custom_price === price ? 'primary' : 'default'}
                      variant={formData.custom_price === price ? 'filled' : 'outlined'}
                      onClick={() => handleChange('custom_price', formData.custom_price === price ? '不限' : price)}
                      sx={{ borderRadius: '12px' }}
                    />
                  ))}
                </Box>
              </Box>

              {/* 交付时间 */}
              <Box sx={{ mb: 3 }}>
                <Typography variant="subtitle2" sx={{ mb: 1, color: '#666' }}>
                  交付时间：
                </Typography>
                <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                  {['不限', '24小时内', '3天内', '7天内', '15天内', '30天内'].map((time) => (
                    <Chip
                      key={time}
                      label={time}
                      clickable
                      size="small"
                      color={formData.custom_delivery_time === time ? 'primary' : 'default'}
                      variant={formData.custom_delivery_time === time ? 'filled' : 'outlined'}
                      onClick={() => handleChange('custom_delivery_time', formData.custom_delivery_time === time ? '不限' : time)}
                      sx={{ borderRadius: '12px' }}
                    />
                  ))}
                </Box>
              </Box>

              {/* 覆盖区域 */}
              <Box sx={{ mb: 3 }}>
                <Typography variant="subtitle2" sx={{ mb: 1, color: '#666' }}>
                  覆盖区域：
                </Typography>
                <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                  {/* 不限选项 */}
                  <Chip
                    key="不限"
                    label="不限"
                    clickable
                    size="small"
                    color={formData.custom_coverage_area.length === 0 ? 'primary' : 'default'}
                    variant={formData.custom_coverage_area.length === 0 ? 'filled' : 'outlined'}
                    onClick={() => handleChange('custom_coverage_area', [])}
                    sx={{ borderRadius: '12px' }}
                  />
                  {availableCoverageAreas.map((area) => (
                    <Chip
                      key={area}
                      label={area}
                      clickable
                      size="small"
                      color={formData.custom_coverage_area.includes(area) ? 'primary' : 'default'}
                      variant={formData.custom_coverage_area.includes(area) ? 'filled' : 'outlined'}
                      onClick={() => {
                        const currentAreas = formData.custom_coverage_area || [];
                        const newAreas = currentAreas.includes(area)
                          ? currentAreas.filter(a => a !== area)
                          : [...currentAreas, area];
                        handleChange('custom_coverage_area', newAreas);
                      }}
                      sx={{ borderRadius: '12px' }}
                    />
                  ))}
                </Box>
              </Box>


            </Box>
          )}

          {/* 服务列表 */}
          {formData.category_id && (
            <Box sx={{ mb: 4 }}>
              <Typography variant="h6" sx={{ mb: 2, fontWeight: 600, color: '#333' }}>
                选择服务
              </Typography>

              {loadingServices ? (
                <Box sx={{ textAlign: 'center', py: 4 }}>
                  <Typography>正在加载服务...</Typography>
                </Box>
              ) : services.length === 0 ? (
                <Box sx={{ textAlign: 'center', py: 4 }}>
                  <Typography color="text.secondary">
                    没有符合条件的服务，请调整筛选条件
                  </Typography>
                </Box>
              ) : (
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, maxHeight: '400px', overflow: 'auto' }}>
                  {services.map((service) => (
                    <Card
                      key={service.id}
                      sx={{
                        border: formData.service_id === service.id ? '2px solid #1976d2' : '1px solid #e0e0e0',
                        cursor: 'pointer',
                        '&:hover': { boxShadow: 2 }
                      }}
                      onClick={() => handleChange('service_id', service.id)}
                    >
                      <CardContent sx={{ p: 2 }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                          <Box sx={{ flex: 1, minWidth: 0, mr: 2 }}>
                            <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 0.5 }}>
                              {service.service_name}
                            </Typography>
                            <Typography
                              variant="body2"
                              color="text.secondary"
                              sx={{
                                mb: 1,
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                whiteSpace: 'nowrap'
                              }}
                            >
                              {service.service_description}
                            </Typography>
                          </Box>

                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, flexShrink: 0 }}>
                            <IconButton
                              size="small"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleViewServiceDetail(service);
                              }}
                              sx={{
                                color: '#1976d2',
                                '&:hover': { backgroundColor: '#e3f2fd' }
                              }}
                            >
                              <Info fontSize="small" />
                            </IconButton>
                            <Typography variant="h6" color="primary" sx={{ fontWeight: 600, minWidth: 'fit-content' }}>
                              ¥{service.base_price}
                            </Typography>
                          </Box>
                        </Box>

                        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                          <Chip size="small" label={`${service.delivery_time}小时交付`} />
                          <Chip size="small" label={`${service.revision_count}次修改`} />
                          {service.platform_type && (
                            <Chip size="small" label={service.platform_type} />
                          )}
                          {service.is_featured && (
                            <Chip size="small" label="推荐" color="success" />
                          )}
                          {service.service_features && service.service_features.length > 0 && (
                            <Chip size="small" label={`${service.service_features.length}个特色`} color="info" />
                          )}
                        </Box>
                      </CardContent>
                    </Card>
                  ))}
                </Box>
              )}
            </Box>
          )}


        </Box>
      </DialogContent>

      <DialogActions>
        <Button onClick={handleClose}>
          取消
        </Button>

        <Button
          onClick={handleSubmit}
          variant="contained"
          disabled={loading || !formData.service_id}
          sx={{
            backgroundColor: '#FF6B35',
            '&:hover': { backgroundColor: '#E55A2B' }
          }}
        >
          {loading ? '绑定中...' : '确认绑定'}
        </Button>
      </DialogActions>
    </Dialog>

    {/* 服务详情对话框 */}
    <Dialog
      open={serviceDetailOpen}
      onClose={() => setServiceDetailOpen(false)}
      maxWidth="md"
      fullWidth
    >
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Info sx={{ color: '#1976d2' }} />
          服务详情
        </Box>
      </DialogTitle>

      <DialogContent>
        {viewingService && (
          <Box sx={{ pt: 2 }}>
            {/* 基本信息 */}
            <Box sx={{ mb: 3 }}>
              <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
                {viewingService.service_name}
              </Typography>
              <Typography variant="body1" color="primary" sx={{ fontWeight: 600, mb: 2 }}>
                ¥{viewingService.base_price}
                {viewingService.discount_price && (
                  <Typography component="span" variant="body2" color="text.secondary" sx={{ ml: 1, textDecoration: 'line-through' }}>
                    ¥{viewingService.discount_price}
                  </Typography>
                )}
              </Typography>
            </Box>

            {/* 服务描述 */}
            {viewingService.service_description && (
              <Box sx={{ mb: 3 }}>
                <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 1 }}>
                  服务描述
                </Typography>
                <Typography variant="body2" sx={{ lineHeight: 1.6, color: '#666' }}>
                  {viewingService.service_description}
                </Typography>
              </Box>
            )}

            {/* 服务特色 */}
            {viewingService.service_features && viewingService.service_features.length > 0 && (
              <Box sx={{ mb: 3 }}>
                <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 1 }}>
                  服务特色
                </Typography>
                <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                  {viewingService.service_features.map((feature, index) => (
                    <Chip
                      key={index}
                      label={feature}
                      color="primary"
                      variant="outlined"
                      size="small"
                      sx={{ borderRadius: '12px' }}
                    />
                  ))}
                </Box>
              </Box>
            )}

            {/* 服务规格 */}
            <Box sx={{ mb: 3 }}>
              <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2 }}>
                服务规格
              </Typography>
              <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                <Chip label={`交付时间：${viewingService.delivery_time}小时`} variant="outlined" />
                <Chip label={`修改次数：${viewingService.revision_count}次`} variant="outlined" />
                {viewingService.platform_type && (
                  <Chip label={`平台类型：${viewingService.platform_type}`} variant="outlined" />
                )}
                {viewingService.channel_type && (
                  <Chip label={`频道类型：${viewingService.channel_type}`} variant="outlined" />
                )}
              </Box>
            </Box>

            {/* 覆盖区域 */}
            {viewingService.coverage_area && viewingService.coverage_area.length > 0 && (
              <Box sx={{ mb: 3 }}>
                <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 1 }}>
                  覆盖区域
                </Typography>
                <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                  {viewingService.coverage_area.map((area, index) => (
                    <Chip
                      key={index}
                      label={area}
                      color="secondary"
                      variant="outlined"
                      size="small"
                    />
                  ))}
                </Box>
              </Box>
            )}
          </Box>
        )}
      </DialogContent>

      <DialogActions>
        <Button onClick={() => setServiceDetailOpen(false)}>
          关闭
        </Button>
        <Button
          variant="contained"
          onClick={() => {
            if (viewingService) {
              handleChange('service_id', viewingService.id);
              setServiceDetailOpen(false);
            }
          }}
          sx={{
            backgroundColor: '#FF6B35',
            '&:hover': { backgroundColor: '#E55A2B' }
          }}
        >
          选择此服务
        </Button>
      </DialogActions>
    </Dialog>
    </>
  );
}

export default ChannelBindingForm;