import React, { useState, useEffect } from 'react';
import {
  <PERSON>alog,
  <PERSON>alogTitle,
  DialogContent,
  <PERSON>alogActions,
  Button,
  TextField,
  Grid,
  Box,
  Typography,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  InputAdornment,
  Card,
  CardContent,
  Divider,
} from '@mui/material';
import {
  Edit,
  Save,
  Close,
  Info,
  AttachMoney,
  Schedule,
  Category,
  Public,
} from '@mui/icons-material';
import channelService from '../../services/channelService';

function EditServiceForm({ open, onClose, service, onSuccess }) {
  const [loading, setLoading] = useState(false);
  const [categories, setCategories] = useState([]);
  const [error, setError] = useState('');

  const [formData, setFormData] = useState({
    service_name: '',
    service_code: '',
    service_description: '',
    service_features: [],
    base_price: '',
    discount_price: '',
    price_unit: '次',
    delivery_time: 24,
    revision_count: 2,
    channel_type: '',
    portal_type: '',
    coverage_area: []
  });

  // 初始化表单数据
  useEffect(() => {
    if (service && open) {
      setFormData({
        service_name: service.serviceName || '',
        service_code: service.serviceCode || '',
        service_description: service.description || '',
        service_features: service.serviceFeatures || [],
        base_price: service.basePrice || '',
        discount_price: service.discountPrice || '',
        price_unit: service.priceUnit || '次',
        delivery_time: service.deliveryTime || 24,
        revision_count: service.revisionCount || 2,
        channel_type: service.channelType || '',
        portal_type: service.portalType || '',
        coverage_area: service.coverageArea || []
      });
    }
  }, [service, open]);

  // 加载分类数据
  useEffect(() => {
    if (open) {
      loadCategories();
    }
  }, [open]);

  const loadCategories = async () => {
    try {
      const response = await channelService.getAvailableCategories();
      if (response.success && response.data && response.data.items) {
        setCategories(response.data.items);
      }
    } catch (error) {
      console.error('加载分类失败:', error);
    }
  };

  const handleChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleClose = () => {
    setFormData({
      service_name: '',
      service_code: '',
      service_description: '',
      service_features: [],
      base_price: '',
      discount_price: '',
      price_unit: '次',
      delivery_time: 24,
      revision_count: 2,
      channel_type: '',
      portal_type: '',
      coverage_area: []
    });
    setError('');
    onClose();
  };

  const handleSubmit = async () => {
    if (!formData.service_name.trim()) {
      setError('请输入服务名称');
      return;
    }

    if (!formData.base_price || parseFloat(formData.base_price) <= 0) {
      setError('请输入有效的基础价格');
      return;
    }

    setLoading(true);
    setError('');

    try {
      // 准备提交数据
      const submitData = {
        service_name: formData.service_name.trim(),
        service_code: formData.service_code.trim() || null,
        service_description: formData.service_description.trim() || null,
        service_features: formData.service_features.length > 0 ? formData.service_features : null,
        base_price: parseFloat(formData.base_price),
        discount_price: formData.discount_price ? parseFloat(formData.discount_price) : null,
        price_unit: formData.price_unit,
        delivery_time: parseInt(formData.delivery_time),
        revision_count: parseInt(formData.revision_count),
        channel_type: formData.channel_type || null,
        portal_type: formData.portal_type || null,
        coverage_area: formData.coverage_area.length > 0 ? formData.coverage_area : null
      };

      const response = await channelService.updateMyService(service.id, submitData);
      
      if (response.success) {
        onSuccess && onSuccess();
        handleClose();
      } else {
        setError(response.message || '更新服务失败');
      }
    } catch (error) {
      console.error('更新服务失败:', error);
      setError(error.response?.data?.detail || '更新服务失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="md"
      fullWidth
      scroll="paper"
      PaperProps={{
        sx: {
          borderRadius: 3,
          maxHeight: '95vh',
          margin: 1,
        }
      }}
    >
      <DialogTitle sx={{
        pb: 3,
        pt: 3,
        borderBottom: '1px solid #e0e0e0',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        color: 'white',
        display: 'flex',
        alignItems: 'center',
        gap: 2
      }}>
        <Box sx={{
          width: 48,
          height: 48,
          borderRadius: 3,
          bgcolor: 'rgba(255, 255, 255, 0.2)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backdropFilter: 'blur(10px)'
        }}>
          <Edit sx={{ color: 'white', fontSize: 24 }} />
        </Box>
        <Box>
          <Typography variant="h5" sx={{ fontWeight: 700, mb: 0.5 }}>
            编辑服务
          </Typography>
          <Typography variant="body2" sx={{ opacity: 0.9 }}>
            修改服务信息，保存后需要重新审核
          </Typography>
        </Box>
      </DialogTitle>

      <DialogContent sx={{ pt: 3, pb: 2, px: 3 }}>
        {error && (
          <Alert severity="error" sx={{ mb: 3, borderRadius: 2 }}>
            {error}
          </Alert>
        )}

        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
          {/* 基本信息卡片 */}
          <Card sx={{ borderRadius: 2, boxShadow: '0 2px 8px rgba(0,0,0,0.1)' }}>
            <CardContent sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 3 }}>
                <Info sx={{ color: '#1976d2', fontSize: 20 }} />
                <Typography variant="h6" sx={{ fontWeight: 600, color: '#1976d2' }}>
                  基本信息
                </Typography>
              </Box>

              <Grid container spacing={2.5}>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    required
                    label="服务名称"
                    value={formData.service_name}
                    onChange={(e) => handleChange('service_name', e.target.value)}
                    placeholder="例如：微信公众号代运营服务"
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 2,
                      }
                    }}
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="服务代码"
                    value={formData.service_code}
                    onChange={(e) => handleChange('service_code', e.target.value)}
                    placeholder="例如：WX_OPERATION"
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 2,
                      }
                    }}
                  />
                </Grid>
              </Grid>

              {/* 服务描述单独一行 */}
              <Box sx={{ mt: 2.5 }}>
                <TextField
                  fullWidth
                  multiline
                  rows={4}
                  label="服务描述"
                  value={formData.service_description}
                  onChange={(e) => handleChange('service_description', e.target.value)}
                  placeholder="详细描述您的服务内容、特色和优势..."
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 2,
                    },
                    '& .MuiInputLabel-root': {
                      fontWeight: 500,
                    }
                  }}
                />
              </Box>
            </CardContent>
          </Card>

          {/* 价格信息卡片 */}
          <Card sx={{ borderRadius: 2, boxShadow: '0 2px 8px rgba(0,0,0,0.1)' }}>
            <CardContent sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 3 }}>
                <AttachMoney sx={{ color: '#f59e0b', fontSize: 20 }} />
                <Typography variant="h6" sx={{ fontWeight: 600, color: '#f59e0b' }}>
                  价格信息
                </Typography>
              </Box>

              <Grid container spacing={2.5}>
                <Grid item xs={12} md={4}>
                  <TextField
                    fullWidth
                    required
                    type="number"
                    label="基础价格"
                    value={formData.base_price}
                    onChange={(e) => handleChange('base_price', e.target.value)}
                    InputProps={{
                      startAdornment: <InputAdornment position="start">¥</InputAdornment>,
                    }}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 2,
                      }
                    }}
                  />
                </Grid>

                <Grid item xs={12} md={4}>
                  <TextField
                    fullWidth
                    type="number"
                    label="优惠价格"
                    value={formData.discount_price}
                    onChange={(e) => handleChange('discount_price', e.target.value)}
                    InputProps={{
                      startAdornment: <InputAdornment position="start">¥</InputAdornment>,
                    }}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 2,
                      }
                    }}
                  />
                </Grid>

                <Grid item xs={12} md={4}>
                  <FormControl fullWidth>
                    <InputLabel>价格单位</InputLabel>
                    <Select
                      value={formData.price_unit}
                      label="价格单位"
                      onChange={(e) => handleChange('price_unit', e.target.value)}
                      sx={{
                        borderRadius: 2,
                      }}
                    >
                      <MenuItem value="次">次</MenuItem>
                      <MenuItem value="篇">篇</MenuItem>
                      <MenuItem value="个">个</MenuItem>
                      <MenuItem value="天">天</MenuItem>
                      <MenuItem value="月">月</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
              </Grid>
            </CardContent>
          </Card>

          {/* 服务规格卡片 */}
          <Card sx={{ borderRadius: 2, boxShadow: '0 2px 8px rgba(0,0,0,0.1)' }}>
            <CardContent sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 3 }}>
                <Schedule sx={{ color: '#10b981', fontSize: 20 }} />
                <Typography variant="h6" sx={{ fontWeight: 600, color: '#10b981' }}>
                  服务规格
                </Typography>
              </Box>

              <Grid container spacing={2.5}>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    type="number"
                    label="交付时间"
                    value={formData.delivery_time}
                    onChange={(e) => handleChange('delivery_time', e.target.value)}
                    InputProps={{
                      endAdornment: <InputAdornment position="end">小时</InputAdornment>,
                    }}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 2,
                      }
                    }}
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    type="number"
                    label="修改次数"
                    value={formData.revision_count}
                    onChange={(e) => handleChange('revision_count', e.target.value)}
                    InputProps={{
                      endAdornment: <InputAdornment position="end">次</InputAdornment>,
                    }}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 2,
                      }
                    }}
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>

          {/* 平台信息卡片 */}
          <Card sx={{ borderRadius: 2, boxShadow: '0 2px 8px rgba(0,0,0,0.1)' }}>
            <CardContent sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 3 }}>
                <Public sx={{ color: '#8b5cf6', fontSize: 20 }} />
                <Typography variant="h6" sx={{ fontWeight: 600, color: '#8b5cf6' }}>
                  平台信息
                </Typography>
              </Box>

              <Grid container spacing={2.5}>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="频道类别"
                    value={formData.channel_type}
                    onChange={(e) => handleChange('channel_type', e.target.value)}
                    placeholder="例如：新闻资讯、娱乐内容、科技数码等"
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 2,
                      }
                    }}
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="综合门户"
                    value={formData.portal_type}
                    onChange={(e) => handleChange('portal_type', e.target.value)}
                    placeholder="例如：微信公众号、微博、抖音、小红书等"
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 2,
                      }
                    }}
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Box>
      </DialogContent>

      <DialogActions sx={{
        p: 3,
        borderTop: '1px solid #e0e0e0',
        backgroundColor: '#fafafa',
        gap: 2,
        justifyContent: 'flex-end'
      }}>
        <Button
          onClick={handleClose}
          startIcon={<Close />}
          variant="outlined"
          sx={{
            color: '#666',
            borderColor: '#ddd',
            borderRadius: 2,
            px: 3,
            '&:hover': {
              borderColor: '#bbb',
              backgroundColor: '#f5f5f5'
            }
          }}
        >
          取消
        </Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          startIcon={<Save />}
          disabled={loading}
          sx={{
            backgroundColor: '#1976d2',
            borderRadius: 2,
            px: 3,
            boxShadow: '0 2px 8px rgba(25, 118, 210, 0.3)',
            '&:hover': {
              backgroundColor: '#1565c0',
              boxShadow: '0 4px 12px rgba(25, 118, 210, 0.4)'
            },
            '&:disabled': {
              backgroundColor: '#ccc',
              boxShadow: 'none'
            }
          }}
        >
          {loading ? '保存中...' : '保存修改'}
        </Button>
      </DialogActions>
    </Dialog>
  );
}

export default EditServiceForm;
