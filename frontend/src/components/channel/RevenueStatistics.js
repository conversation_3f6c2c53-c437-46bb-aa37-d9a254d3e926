import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tabs,
  Tab,
  InputAdornment,
  Alert,
  Snackbar,
  LinearProgress,
  Avatar,
  Tooltip,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ToggleButton,
  ToggleButtonGroup,
} from '@mui/material';
import {
  TrendingUp,
  TrendingDown,
  MonetizationOn,
  AccountBalance,
  AccessTime,
  GetApp,
  Visibility,
  FilterList,
  CalendarToday,
  PieChart,
  BarChart,
  ShowChart,
  Download,
  AccountBalanceWallet,
  Payment,
  CreditCard,
  Store,
  Campaign,
  Article,
  VideoLibrary,
  Image as ImageIcon,
  Person,
  Business,
  Star,
  CheckCircle,
  Pending,
  Error,
  Schedule,
  Receipt,
  Analytics,
  Assessment,
  Search,
  ChevronLeft,
  ChevronRight,
  DateRange,
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  PieChart as RechartsPieChart,
  Pie,
  Cell,
  ResponsiveContainer,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  BarChart as RechartsBarChart,
  Bar,
} from 'recharts';

const StyledContainer = styled(Box)(({ theme }) => ({
  height: '100%',
  minHeight: 'calc(100vh - 64px)',
  display: 'flex',
  flexDirection: 'column',
  backgroundColor: '#ffffff',
  overflow: 'hidden',
}));

const HeaderSection = styled(Box)(({ theme }) => ({
  padding: theme.spacing(2),
  backgroundColor: theme.palette.background.paper,
  borderBottom: `1px solid ${theme.palette.divider}`,
}));

const ContentSection = styled(Box)(({ theme }) => ({
  flex: 1,
  overflow: 'auto',
  padding: theme.spacing(2),
  backgroundColor: 'transparent',
  '&::-webkit-scrollbar': {
    width: '8px',
  },
  '&::-webkit-scrollbar-track': {
    background: 'transparent',
  },
  '&::-webkit-scrollbar-thumb': {
    background: theme.palette.divider,
    borderRadius: '4px',
  },
}));

const MetricCard = styled(Card)(({ theme }) => ({
  height: '100%',
  borderRadius: theme.spacing(2),
  border: `1px solid ${theme.palette.divider}`,
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-2px)',
    boxShadow: '0 8px 24px rgba(0,0,0,0.12)',
  },
}));

const ChartCard = styled(Card)(({ theme }) => ({
  height: '100%',
  borderRadius: theme.spacing(2),
  border: `1px solid ${theme.palette.divider}`,
  display: 'flex',
  flexDirection: 'column',
}));

// 图表颜色配置
const COLORS = {
  primary: '#1976d2',
  secondary: '#42a5f5',
  success: '#4caf50',
  warning: '#ff9800',
  error: '#f44336',
  info: '#00bcd4',
  purple: '#666666',
  pink: '#e91e63',
};

const CHART_COLORS = [
  COLORS.primary,
  COLORS.success,
  COLORS.warning,
  COLORS.purple,
  COLORS.pink,
  COLORS.info,
];

// 模拟数据
const mockData = {
  overview: {
    totalRevenue: 128456.78,
    monthlyRevenue: 23567.89,
    pendingSettlement: 5432.10,
    withdrawable: 18135.79,
    totalRevenueChange: 15.2,
    monthlyRevenueChange: 8.7,
    pendingSettlementChange: -12.3,
    withdrawableChange: 22.1,
  },
  trendData: [
    { month: '1月', revenue: 8500, orders: 25, avgPrice: 340 },
    { month: '2月', revenue: 12300, orders: 34, avgPrice: 362 },
    { month: '3月', revenue: 15600, orders: 42, avgPrice: 371 },
    { month: '4月', revenue: 18900, orders: 48, avgPrice: 394 },
    { month: '5月', revenue: 22400, orders: 55, avgPrice: 407 },
    { month: '6月', revenue: 19800, orders: 51, avgPrice: 388 },
    { month: '7月', revenue: 23567, orders: 58, avgPrice: 406 },
    { month: '8月', revenue: 21200, orders: 52, avgPrice: 408 },
    { month: '9月', revenue: 25800, orders: 61, avgPrice: 423 },
    { month: '10月', revenue: 27300, orders: 65, avgPrice: 420 },
    { month: '11月', revenue: 24900, orders: 59, avgPrice: 422 },
    { month: '12月', revenue: 28456, orders: 68, avgPrice: 418 },
  ],
  channelDistribution: [
    { name: '微信公众号', value: 45680, percentage: 35.6, color: COLORS.primary },
    { name: '抖音', value: 32140, percentage: 25.0, color: COLORS.success },
    { name: '小红书', value: 25890, percentage: 20.2, color: COLORS.warning },
    { name: '微博', value: 15420, percentage: 12.0, color: COLORS.purple },
    { name: '其他平台', value: 9326, percentage: 7.2, color: COLORS.pink },
  ],
  channelDetails: [
    {
      id: 1,
      channelName: '科技前沿观察',
      platform: '微信公众号',
      totalRevenue: 15678.90,
      monthlyRevenue: 2890.45,
      orderCount: 12,
      avgOrderValue: 1306.58,
      lastPayment: '2024-08-05',
      status: 'active',
      followers: 125000,
      rating: 4.8,
      type: '科技',
    },
    {
      id: 2,
      channelName: '时尚生活达人',
      platform: '小红书',
      totalRevenue: 12456.78,
      monthlyRevenue: 3456.12,
      orderCount: 18,
      avgOrderValue: 692.04,
      lastPayment: '2024-08-03',
      status: 'active',
      followers: 89000,
      rating: 4.9,
      type: '时尚',
    },
    {
      id: 3,
      channelName: '美食探店记',
      platform: '抖音',
      totalRevenue: 18934.56,
      monthlyRevenue: 4567.89,
      orderCount: 25,
      avgOrderValue: 757.38,
      lastPayment: '2024-08-01',
      status: 'active',
      followers: 156000,
      rating: 4.7,
      type: '美食',
    },
    {
      id: 4,
      channelName: '财经解读',
      platform: '微博',
      totalRevenue: 9876.54,
      monthlyRevenue: 1234.56,
      orderCount: 8,
      avgOrderValue: 1234.57,
      lastPayment: '2024-07-28',
      status: 'pending',
      followers: 67000,
      rating: 4.6,
      type: '财经',
    },
    {
      id: 5,
      channelName: '旅游攻略分享',
      platform: '小红书',
      totalRevenue: 7654.32,
      monthlyRevenue: 987.65,
      orderCount: 6,
      avgOrderValue: 1275.72,
      lastPayment: '2024-07-25',
      status: 'active',
      followers: 45000,
      rating: 4.5,
      type: '旅游',
    },
  ],
  withdrawHistory: [
    { id: 1, amount: 5000, date: '2024-07-15', status: 'completed', method: '支付宝' },
    { id: 2, amount: 3500, date: '2024-06-28', status: 'completed', method: '银行卡' },
    { id: 3, amount: 2800, date: '2024-06-10', status: 'completed', method: '微信' },
    { id: 4, amount: 4200, date: '2024-05-20', status: 'completed', method: '支付宝' },
  ],
};

function RevenueStatistics() {
  const [timeRange, setTimeRange] = useState('7d');
  const [selectedChannel, setSelectedChannel] = useState('all');
  const [showWithdrawDialog, setShowWithdrawDialog] = useState(false);
  const [withdrawAmount, setWithdrawAmount] = useState('');
  const [withdrawMethod, setWithdrawMethod] = useState('');
  const [loading, setLoading] = useState(false);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });
  const [currentTab, setCurrentTab] = useState(0);
  const [viewTab, setViewTab] = useState(0); // 0: 图表分析, 1: 详情列表
  const [currentPage, setCurrentPage] = useState(1); // 分页状态
  const [startDate, setStartDate] = useState(''); // 开始日期
  const [endDate, setEndDate] = useState(''); // 结束日期

  const handleTimeRangeChange = (event, newRange) => {
    if (newRange !== null) {
      setTimeRange(newRange);
    }
  };

  const handleWithdraw = async () => {
    if (!withdrawAmount || !withdrawMethod) {
      setSnackbar({ open: true, message: '请填写完整的提现信息', severity: 'error' });
      return;
    }

    const amount = parseFloat(withdrawAmount);
    if (amount <= 0 || amount > mockData.overview.withdrawable) {
      setSnackbar({ open: true, message: '提现金额无效', severity: 'error' });
      return;
    }

    setLoading(true);
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 2000));
      setShowWithdrawDialog(false);
      setWithdrawAmount('');
      setWithdrawMethod('');
      setSnackbar({ 
        open: true, 
        message: `提现申请已提交，金额：¥${amount}`, 
        severity: 'success' 
      });
    } catch (error) {
      setSnackbar({ open: true, message: '提现申请失败，请重试', severity: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const renderOverviewCards = () => (
    <Box sx={{
      borderBottom: '1px solid #f0f0f0',
      backgroundColor: '#fafafa',
      mx: -2,
      px: 2,
      py: 2,
      mb: 3
    }}>
      <Grid container spacing={2}>
        <Grid item xs={12} sm={6} md={3}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
            <Box sx={{
              width: 36,
              height: 36,
              borderRadius: '10px',
              backgroundColor: '#f0fdf4',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <MonetizationOn sx={{ color: '#10b981', fontSize: 18 }} />
            </Box>
            <Box>
              <Typography variant="caption" sx={{ 
                color: '#9ca3af', 
                fontSize: '0.7rem',
                fontWeight: 500,
                textTransform: 'uppercase',
                letterSpacing: '0.025em'
              }}>
                累计收益
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'baseline', gap: 0.5 }}>
                <Typography variant="h6" sx={{ 
                  color: '#1a1a1a',
                  fontWeight: 600,
                  fontSize: '1.25rem',
                  mt: 0.25
                }}>
                  ¥{mockData.overview.totalRevenue.toLocaleString()}
                </Typography>
                <Typography variant="caption" sx={{ color: '#10b981', fontWeight: 600, fontSize: '0.65rem' }}>
                  ↑{mockData.overview.totalRevenueChange}%
                </Typography>
              </Box>
            </Box>
          </Box>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
            <Box sx={{
              width: 36,
              height: 36,
              borderRadius: '10px',
              backgroundColor: '#dbeafe',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <TrendingUp sx={{ color: '#3b82f6', fontSize: 18 }} />
            </Box>
            <Box>
              <Typography variant="caption" sx={{ 
                color: '#9ca3af', 
                fontSize: '0.7rem',
                fontWeight: 500,
                textTransform: 'uppercase',
                letterSpacing: '0.025em'
              }}>
                本月收益
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'baseline', gap: 0.5 }}>
                <Typography variant="h6" sx={{ 
                  color: '#1a1a1a',
                  fontWeight: 600,
                  fontSize: '1.25rem',
                  mt: 0.25
                }}>
                  ¥{mockData.overview.monthlyRevenue.toLocaleString()}
                </Typography>
                <Typography variant="caption" sx={{ color: '#3b82f6', fontWeight: 600, fontSize: '0.65rem' }}>
                  ↑{mockData.overview.monthlyRevenueChange}%
                </Typography>
              </Box>
            </Box>
          </Box>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
            <Box sx={{
              width: 36,
              height: 36,
              borderRadius: '10px',
              backgroundColor: '#fef3c7',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <AccessTime sx={{ color: '#f59e0b', fontSize: 18 }} />
            </Box>
            <Box>
              <Typography variant="caption" sx={{ 
                color: '#9ca3af', 
                fontSize: '0.7rem',
                fontWeight: 500,
                textTransform: 'uppercase',
                letterSpacing: '0.025em'
              }}>
                待结算
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'baseline', gap: 0.5 }}>
                <Typography variant="h6" sx={{ 
                  color: '#1a1a1a',
                  fontWeight: 600,
                  fontSize: '1.25rem',
                  mt: 0.25
                }}>
                  ¥{mockData.overview.pendingSettlement.toLocaleString()}
                </Typography>
                <Typography variant="caption" sx={{ color: '#f59e0b', fontWeight: 600, fontSize: '0.65rem' }}>
                  处理中
                </Typography>
              </Box>
            </Box>
          </Box>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
            <Box sx={{
              width: 36,
              height: 36,
              borderRadius: '10px',
              backgroundColor: '#dcfce7',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <AccountBalanceWallet sx={{ color: '#16a34a', fontSize: 18 }} />
            </Box>
            <Box>
              <Typography variant="caption" sx={{ 
                color: '#9ca3af', 
                fontSize: '0.7rem',
                fontWeight: 500,
                textTransform: 'uppercase',
                letterSpacing: '0.025em'
              }}>
                可提现
              </Typography>
              <Typography variant="h6" sx={{ 
                color: '#1a1a1a',
                fontWeight: 600,
                fontSize: '1.25rem',
                mt: 0.25
              }}>
                ¥{mockData.overview.withdrawable.toLocaleString()}
              </Typography>
            </Box>
          </Box>
        </Grid>
      </Grid>
    </Box>
  );

  const renderTrendChart = () => (
    <Box sx={{ 
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      p: 3,
      backgroundColor: '#fff',
      borderRadius: 2,
      border: '1px solid #e5e7eb'
    }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Avatar sx={{ 
              backgroundColor: '#eff6ff',
              width: 36,
              height: 36
            }}>
              <ShowChart sx={{ color: '#3b82f6', fontSize: 22 }} />
            </Avatar>
            <Typography variant="h6" sx={{ 
              fontWeight: 600,
              color: '#1a1a1a',
              fontSize: '1.1rem'
            }}>
              收益趋势分析（12个月）
            </Typography>
          </Box>
          <ToggleButtonGroup
            value={timeRange}
            exclusive
            onChange={handleTimeRangeChange}
            size="small"
            sx={{
              '& .MuiToggleButton-root': {
                py: 0.5,
                px: 2,
                fontSize: '0.85rem',
                borderColor: '#e5e7eb',
                color: '#6b7280',
                '&.Mui-selected': {
                  backgroundColor: '#eff6ff',
                  color: '#3b82f6',
                  borderColor: '#3b82f6'
                }
              }
            }}
          >
            <ToggleButton value="7d">7天</ToggleButton>
            <ToggleButton value="30d">30天</ToggleButton>
            <ToggleButton value="90d">90天</ToggleButton>
            <ToggleButton value="1y">1年</ToggleButton>
          </ToggleButtonGroup>
        </Box>
        <Box sx={{ flex: 1, minHeight: 0, pb: 2 }}>
          <ResponsiveContainer width="100%" height="100%">
            <AreaChart data={mockData.trendData} margin={{ top: 10, right: 15, left: 0, bottom: 40 }}>
              <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
              <XAxis 
                dataKey="month" 
                stroke="#9ca3af" 
                fontSize={11}
                interval={0}
                angle={-30}
                textAnchor="end"
                height={50}
              />
              <YAxis stroke="#9ca3af" fontSize={12} />
              <RechartsTooltip 
                contentStyle={{ 
                  backgroundColor: 'rgba(255, 255, 255, 0.95)', 
                  border: '1px solid #e5e7eb',
                  borderRadius: 8,
                  fontSize: '0.95rem'
                }}
              />
              <Area
                type="monotone"
                dataKey="revenue"
                stroke="#3b82f6"
                fill="#3b82f6"
                fillOpacity={0.1}
                strokeWidth={2}
                name="收益金额"
              />
            </AreaChart>
          </ResponsiveContainer>
        </Box>
    </Box>
  );

  const renderChannelDistribution = () => (
    <Box sx={{ 
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      p: 3,
      overflow: 'hidden',
      backgroundColor: '#fff',
      borderRadius: 2,
      border: '1px solid #e5e7eb'
    }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Avatar sx={{ 
              backgroundColor: '#f0fdf4',
              width: 36,
              height: 36
            }}>
              <PieChart sx={{ color: '#10b981', fontSize: 22 }} />
            </Avatar>
            <Typography variant="h6" sx={{ 
              fontWeight: 600,
              color: '#1a1a1a',
              fontSize: '1.1rem'
            }}>
              收益来源分布
            </Typography>
          </Box>
        </Box>
        
        {/* 饼图 */}
        <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', overflow: 'hidden' }}>
          <Box sx={{ width: '100%', height: 260 }}>
            <ResponsiveContainer width="100%" height="100%">
              <RechartsPieChart>
                <Pie
                  data={mockData.channelDistribution}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percentage }) => `${percentage}%`}
                  outerRadius={85}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {mockData.channelDistribution.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <RechartsTooltip 
                  contentStyle={{ 
                    backgroundColor: 'rgba(255, 255, 255, 0.95)', 
                    border: '1px solid #e5e7eb',
                    borderRadius: 8,
                    fontSize: '0.95rem'
                  }}
                />
              </RechartsPieChart>
            </ResponsiveContainer>
          </Box>
          
          {/* 图例 */}
          <Box sx={{ mt: 1.5, width: '100%', maxHeight: 100, overflow: 'auto' }}>
            <Grid container spacing={0.5}>
              {mockData.channelDistribution.map((channel, index) => (
                <Grid item xs={12} key={index}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, px: 1, py: 0.25 }}>
                    <Box
                      sx={{
                        width: 8,
                        height: 8,
                        borderRadius: '50%',
                        backgroundColor: channel.color,
                        flexShrink: 0
                      }}
                    />
                    <Typography variant="caption" sx={{ 
                      color: '#6b7280', 
                      fontSize: '0.8rem',
                      flex: 1,
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap'
                    }}>
                      {channel.name}
                    </Typography>
                    <Typography variant="caption" sx={{ 
                      color: '#1a1a1a', 
                      fontSize: '0.85rem',
                      fontWeight: 600
                    }}>
                      ¥{(channel.value/1000).toFixed(1)}k ({channel.percentage}%)
                    </Typography>
                  </Box>
                </Grid>
              ))}
            </Grid>
          </Box>
        </Box>
    </Box>
  );

  const renderChannelDetailsTable = () => {
    const itemsPerPage = 3;
    const totalPages = Math.ceil(mockData.channelDetails.length / itemsPerPage);
    const paginatedChannels = mockData.channelDetails.slice(
      (currentPage - 1) * itemsPerPage,
      currentPage * itemsPerPage
    );

    return (
      <Box>
        {/* 筛选区域 */}
        <Box sx={{ mb: 3 }}>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={3}>
              <FormControl fullWidth size="small">
                <InputLabel>选择渠道</InputLabel>
                <Select
                  value={selectedChannel}
                  onChange={(e) => setSelectedChannel(e.target.value)}
                  label="选择渠道"
                  startAdornment={
                    <InputAdornment position="start">
                      <Store sx={{ color: '#9ca3af', fontSize: 20 }} />
                    </InputAdornment>
                  }
                >
                  <MenuItem value="all">全部渠道</MenuItem>
                  <MenuItem value="科技前沿观察">科技前沿观察</MenuItem>
                  <MenuItem value="时尚生活达人">时尚生活达人</MenuItem>
                  <MenuItem value="美食探店记">美食探店记</MenuItem>
                  <MenuItem value="财经解读">财经解读</MenuItem>
                  <MenuItem value="旅游攻略分享">旅游攻略分享</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={3}>
              <TextField
                fullWidth
                size="small"
                label="开始日期"
                type="date"
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
                InputLabelProps={{
                  shrink: true,
                }}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <DateRange sx={{ color: '#9ca3af', fontSize: 20 }} />
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <TextField
                fullWidth
                size="small"
                label="结束日期"
                type="date"
                value={endDate}
                onChange={(e) => setEndDate(e.target.value)}
                InputLabelProps={{
                  shrink: true,
                }}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <CalendarToday sx={{ color: '#9ca3af', fontSize: 20 }} />
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Download />}
                sx={{
                  borderColor: '#e5e7eb',
                  color: '#6b7280',
                  '&:hover': {
                    borderColor: '#d1d5db',
                    backgroundColor: '#f9fafb'
                  }
                }}
              >
                导出数据
              </Button>
            </Grid>
          </Grid>
        </Box>

        {/* 列表标题和分页 */}
        <Box sx={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center',
          mb: 1.5,
          px: 1
        }}>
          <Box sx={{ display: 'flex', alignItems: 'baseline', gap: 2 }}>
            <Typography variant="h6" sx={{
              fontWeight: 600,
              color: '#1a1a1a',
              fontSize: '1.1rem'
            }}>
              渠道列表
            </Typography>
            <Typography variant="body2" sx={{ color: '#6b7280', fontSize: '0.85rem' }}>
              共 {mockData.channelDetails.length} 个渠道
            </Typography>
          </Box>
          
          {/* 上一页/下一页按钮 */}
          {mockData.channelDetails.length > itemsPerPage && (
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Typography variant="body2" sx={{ color: '#6b7280', fontSize: '0.85rem', mr: 1 }}>
                第 {currentPage} / {totalPages} 页
              </Typography>
              <Button
                size="small"
                variant="outlined"
                startIcon={<ChevronLeft />}
                onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                disabled={currentPage === 1}
                sx={{
                  minWidth: 80,
                  borderColor: '#e5e7eb',
                  color: '#6b7280',
                  fontSize: '0.85rem',
                  py: 0.5,
                  '&:hover': {
                    borderColor: '#d1d5db',
                    backgroundColor: '#f9fafb'
                  },
                  '&:disabled': {
                    borderColor: '#f3f4f6',
                    color: '#d1d5db'
                  }
                }}
              >
                上一页
              </Button>
              <Button
                size="small"
                variant="outlined"
                endIcon={<ChevronRight />}
                onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                disabled={currentPage === totalPages}
                sx={{
                  minWidth: 80,
                  borderColor: '#e5e7eb',
                  color: '#6b7280',
                  fontSize: '0.85rem',
                  py: 0.5,
                  '&:hover': {
                    borderColor: '#d1d5db',
                    backgroundColor: '#f9fafb'
                  },
                  '&:disabled': {
                    borderColor: '#f3f4f6',
                    color: '#d1d5db'
                  }
                }}
              >
                下一页
              </Button>
            </Box>
          )}
        </Box>

        {/* 渠道列表 */}
        <Paper sx={{ 
          border: '1px solid #e5e7eb',
          borderRadius: 2,
          overflow: 'hidden'
        }}>
          {paginatedChannels.map((channel, index) => {
            const isLastItem = index === paginatedChannels.length - 1;
            return (
              <Box key={channel.id}>
                <Box
                  sx={{
                    px: 2.5,
                    py: 2,
                    borderBottom: !isLastItem ? '1px solid #e5e7eb' : 'none',
                    transition: 'all 0.2s ease',
                    '&:hover': {
                      backgroundColor: '#f9fafb',
                    },
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    {/* 左侧主要信息 */}
                    <Box sx={{ flex: '1 1 auto', minWidth: 0 }}>
                      <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2 }}>
                        {/* 平台图标 */}
                        <Avatar sx={{ 
                          bgcolor: channel.platform === '微信公众号' ? '#07c160' :
                                   channel.platform === '抖音' ? '#161823' :
                                   channel.platform === '小红书' ? '#ff2442' :
                                   channel.platform === '微博' ? '#ff8200' : '#6b7280',
                          width: 36,
                          height: 36
                        }}>
                          {channel.platform === '微信公众号' && <Article sx={{ fontSize: 20 }} />}
                          {channel.platform === '抖音' && <VideoLibrary sx={{ fontSize: 20 }} />}
                          {channel.platform === '小红书' && <ImageIcon sx={{ fontSize: 20 }} />}
                          {channel.platform === '微博' && <Campaign sx={{ fontSize: 20 }} />}
                        </Avatar>
                        
                        {/* 渠道信息 */}
                        <Box sx={{ flex: 1, minWidth: 0 }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
                            <Typography variant="subtitle1" sx={{ 
                              fontWeight: 600, 
                              color: '#1a1a1a',
                              fontSize: '1rem',
                            }}>
                              {channel.channelName}
                            </Typography>
                            <Chip 
                              label={channel.status === 'active' ? '正常' : '待处理'}
                              size="small" 
                              sx={{ 
                                height: 18,
                                fontSize: '0.8rem',
                                backgroundColor: channel.status === 'active' ? '#dcfce7' : '#fef3c7',
                                color: channel.status === 'active' ? '#166534' : '#92400e',
                              }} 
                            />
                          </Box>
                          
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, flexWrap: 'wrap' }}>
                            <Typography variant="body2" sx={{ color: '#6b7280' }}>
                              {channel.platform} · {channel.type}
                            </Typography>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                              <Person sx={{ fontSize: 14, color: '#9ca3af' }} />
                              <Typography variant="caption" sx={{ color: '#6b7280' }}>
                                {(channel.followers / 1000).toFixed(0)}K 粉丝
                              </Typography>
                            </Box>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                              <Star sx={{ fontSize: 14, color: '#fbbf24' }} />
                              <Typography variant="caption" sx={{ color: '#6b7280' }}>
                                {channel.rating}
                              </Typography>
                            </Box>
                            <Typography variant="caption" sx={{ color: '#9ca3af' }}>
                              最近付款: {channel.lastPayment}
                            </Typography>
                          </Box>
                        </Box>
                      </Box>
                    </Box>
                    
                    {/* 右侧收益信息和操作按钮 */}
                    <Box sx={{ 
                      display: 'flex', 
                      alignItems: 'center', 
                      gap: 3,
                      flexShrink: 0
                    }}>
                      {/* 收益信息 */}
                      <Box sx={{ textAlign: 'right' }}>
                        <Typography variant="caption" sx={{ color: '#9ca3af', fontSize: '0.7rem' }}>
                          累计收益
                        </Typography>
                        <Typography variant="h6" sx={{ 
                          fontWeight: 600, 
                          color: '#1a1a1a',
                          fontSize: '1.25rem'
                        }}>
                          ¥{channel.totalRevenue.toLocaleString()}
                        </Typography>
                        <Typography variant="caption" sx={{ 
                          color: '#10b981',
                          fontSize: '0.75rem',
                          fontWeight: 600
                        }}>
                          本月 +¥{channel.monthlyRevenue.toLocaleString()}
                        </Typography>
                      </Box>
                      
                      {/* 操作按钮 */}
                      <Box sx={{ display: 'flex', gap: 1 }}>
                        <IconButton
                          size="small"
                          sx={{ 
                            color: '#6b7280',
                            '&:hover': {
                              backgroundColor: '#f3f4f6'
                            }
                          }}
                        >
                          <Visibility sx={{ fontSize: 18 }} />
                        </IconButton>
                        <IconButton
                          size="small"
                          sx={{ 
                            color: '#6b7280',
                            '&:hover': {
                              backgroundColor: '#f3f4f6'
                            }
                          }}
                        >
                          <Receipt sx={{ fontSize: 18 }} />
                        </IconButton>
                      </Box>
                    </Box>
                  </Box>
                </Box>
              </Box>
            );
          })}
        </Paper>
      </Box>
    );
  };

  return (
    <StyledContainer>
      {/* 头部区域 */}
      <HeaderSection>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Box>
            <Typography variant="h4" fontWeight="bold" gutterBottom>
              收益统计
            </Typography>
            <Typography variant="body1" color="text.secondary">
              查看您的收益详情和趋势分析
            </Typography>
          </Box>
          <Button
            variant="contained"
            startIcon={<AccountBalance />}
            onClick={() => setShowWithdrawDialog(true)}
          >
            申请提现
          </Button>
        </Box>
      </HeaderSection>

      {/* 内容区域 */}
      <ContentSection>
        {/* 收益概览卡片 */}
        {renderOverviewCards()}

        {/* 标签切换 */}
        <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
          <Tabs value={viewTab} onChange={(e, v) => setViewTab(v)}>
            <Tab label="图表分析" />
            <Tab label="详情列表" />
          </Tabs>
        </Box>

        {/* 内容显示 */}
        {viewTab === 0 ? (
          /* 图表分析视图 */
          <Box sx={{ height: 'calc(100vh - 380px)', minHeight: 500 }}>
            <Grid container spacing={3} sx={{ height: '100%' }}>
              <Grid item xs={12} lg={7} sx={{ height: '100%' }}>
                {renderTrendChart()}
              </Grid>
              <Grid item xs={12} lg={5} sx={{ height: '100%' }}>
                {renderChannelDistribution()}
              </Grid>
            </Grid>
          </Box>
        ) : (
          /* 详情列表视图 */
          renderChannelDetailsTable()
        )}
      </ContentSection>

      {/* 提现对话框 */}
      <Dialog
        open={showWithdrawDialog}
        onClose={() => setShowWithdrawDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          <Box display="flex" alignItems="center" gap={2}>
            <AccountBalanceWallet />
            申请提现
          </Box>
        </DialogTitle>
        <DialogContent>
          <Alert severity="info" sx={{ mb: 3 }}>
            可提现金额：¥{mockData.overview.withdrawable.toLocaleString()}
          </Alert>
          
          <TextField
            autoFocus
            margin="dense"
            label="提现金额"
            type="number"
            fullWidth
            variant="outlined"
            value={withdrawAmount}
            onChange={(e) => setWithdrawAmount(e.target.value)}
            inputProps={{
              min: 1,
              max: mockData.overview.withdrawable,
              step: 0.01,
            }}
            sx={{ mb: 2 }}
          />

          <FormControl fullWidth variant="outlined" sx={{ mb: 2 }}>
            <InputLabel>提现方式</InputLabel>
            <Select
              value={withdrawMethod}
              onChange={(e) => setWithdrawMethod(e.target.value)}
              label="提现方式"
            >
              <MenuItem value="alipay">
                <Box display="flex" alignItems="center" gap={1}>
                  <Payment />
                  支付宝
                </Box>
              </MenuItem>
              <MenuItem value="wechat">
                <Box display="flex" alignItems="center" gap={1}>
                  <AccountBalance />
                  微信
                </Box>
              </MenuItem>
              <MenuItem value="bank">
                <Box display="flex" alignItems="center" gap={1}>
                  <CreditCard />
                  银行卡
                </Box>
              </MenuItem>
            </Select>
          </FormControl>

          <Alert severity="warning" sx={{ mb: 2 }}>
            提现申请提交后，通常在1-3个工作日内到账
          </Alert>

          {loading && <LinearProgress sx={{ mb: 2 }} />}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowWithdrawDialog(false)} disabled={loading}>
            取消
          </Button>
          <Button
            onClick={handleWithdraw}
            variant="contained"
            disabled={loading}
          >
            {loading ? '处理中...' : '确认提现'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={4000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          variant="filled"
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </StyledContainer>
  );
}

export default RevenueStatistics;