import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Typography,
  Grid,
  Button,
  Chip,
  TextField,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tabs,
  Tab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Snackbar,
  IconButton,
  Badge,
  LinearProgress,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from '@mui/material';
import {
  Search,
  Refresh,
  Business,
  Schedule,
  AttachMoney,
  Visibility,
  Assignment,
  Close,
  CalendarToday,
  HandshakeOutlined,
  AutoAwesome,
  Send,
  ChevronLeft,
  ChevronRight,
  AttachFile,
  Download,
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';
import ApiService from '../../services/api';
import { contentService } from '../../services/contentService';
import uploadService from '../../services/uploadService';

const StyledContainer = styled(Box)(({ theme }) => ({
  width: '100%',
  minHeight: '100vh',
  backgroundColor: 'white',
}));

const HeaderSection = styled(Box)(({ theme }) => ({
  backgroundColor: 'white',
  paddingBottom: 0,
}));

const ContentSection = styled(Box)(({ theme }) => ({
  backgroundColor: 'white',
}));


const StyledTabs = styled(Tabs)(({ theme }) => ({
  borderBottom: `1px solid ${theme.palette.divider}`,
  minHeight: 48,
  '& .MuiTab-root': {
    minHeight: 48,
    py: 1.5
  },
  '& .MuiTabs-indicator': {
    height: 3,
    borderRadius: '3px 3px 0 0',
  },
}));




function BrowseRequirements() {
  const [currentTab, setCurrentTab] = useState(0);
  const [requirements, setRequirements] = useState({ direct: [], creative: [] });
  const [filteredRequirements, setFilteredRequirements] = useState([]);
  const [filters, setFilters] = useState({
    contentType: 'all',
    priceRange: 'all',
    deadline: 'all',
    keyword: '',
  });
  const [selectedRequirement, setSelectedRequirement] = useState(null);
  const [showDetailDialog, setShowDetailDialog] = useState(false);
  const [showApplyDialog, setShowApplyDialog] = useState(false);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 4; // Show 4 items per page

  // 接单表单数据
  const [applyFormData, setApplyFormData] = useState({
    estimated_delivery_days: 7,
    accept_message: ''
  });

  // 下载附件文件
  const handleDownloadFile = async (file) => {
    try {
      let fileId = '';
      let fileName = 'download';

      // 处理不同的文件数据结构，提取文件ID和文件名
      if (typeof file === 'string') {
        fileName = file;
        setSnackbar({
          open: true,
          message: '无法下载文件：缺少文件ID',
          severity: 'error'
        });
        return;
      } else if (file && typeof file === 'object') {
        fileId = file.id || file.file_id;
        fileName = file.name || file.filename || file.original_name || 'download';
      }

      // 检查是否有文件ID
      if (!fileId) {
        setSnackbar({
          open: true,
          message: '无法下载文件：文件ID不存在',
          severity: 'error'
        });
        return;
      }

      // 使用后端API下载，确保权限验证和预签名URL
      try {
        await uploadService.downloadFile(fileId, fileName);
        setSnackbar({
          open: true,
          message: '文件下载成功',
          severity: 'success'
        });
      } catch (error) {
        setSnackbar({
          open: true,
          message: '下载文件失败：' + error.message,
          severity: 'error'
        });
      }
    } catch (error) {
      setSnackbar({
        open: true,
        message: '下载文件失败：' + error.message,
        severity: 'error'
      });
    }
  };

  // 获取状态文本的辅助函数
  const getStatusText = (status) => {
    const statusMap = {
      'pending': '待审核',
      'approved': '已发布',
      'in_progress': '进行中',
      'completed': '已完成',
      'cancelled': '已取消',
      'urgent': '紧急',
      'normal': '正常',
      'hot': '热门'
    };
    return statusMap[status] || '未知状态';
  };

  // 加载需求数据
  const loadRequirements = useCallback(async () => {
    try {
      setLoading(true);

      // 调用真实API获取内容需求列表，只获取待接单的需求
      const response = await ApiService.getChannelRequirements({
        status: 'PENDING'  // 只显示待接单的需求
      });

      if (response && response.success) {
        const requests = response.data?.items || [];

        const directRequirements = requests.filter(req => req.request_type === 'PUBLISH_CONTENT') || [];
        const creativeRequirements = requests.filter(req => req.request_type === 'CREATE_CONTENT') || [];

        // 转换数据格式以适配前端组件（只使用后端实际返回的字段）
        const formatRequirement = (req) => ({
          id: req.id,
          type: req.request_type === 'CREATE_CONTENT' ? 'creative' : 'direct',
          title: req.request_title || '未命名需求',
          company: req.company_info?.company_name || '未知公司',
          budget: parseFloat(req.fixed_price || 0),
          description: req.request_description || '暂无描述',
          deadline: req.deadline ? new Date(req.deadline).toLocaleDateString() : '待定',
          status: req.status || 'normal',
          statusText: getStatusText(req.status),
          tags: req.tags || [],
          requirements: req.creation_requirements || '暂无特殊要求',
          // 后端实际返回的字段
          providedContentTitle: req.provided_content_title,
          providedContentText: req.provided_content_text,
          providedContentFiles: req.provided_content_files,
          estimatedDeliveryDays: req.estimated_delivery_days,
          acceptMessage: req.accept_message,
          createdAt: req.created_at,
          updatedAt: req.updated_at,
          acceptedAt: req.accepted_at,
          companyInfo: req.company_info,
          providerInfo: req.provider_info
        });

        setRequirements({
          direct: directRequirements.map(formatRequirement),
          creative: creativeRequirements.map(formatRequirement)
        });

        setSnackbar({
          open: true,
          message: `成功加载 ${requests.length} 个需求`,
          severity: 'success'
        });
      } else {
        throw new Error(response?.message || '获取需求列表失败');
      }
    } catch (error) {
      console.error('加载需求数据失败:', error);
      setSnackbar({
        open: true,
        message: error.response?.data?.detail || error.message || '加载需求数据失败，请稍后重试',
        severity: 'error'
      });

      // 发生错误时设置空数据
      setRequirements({ direct: [], creative: [] });
    } finally {
      setLoading(false);
    }
  }, []);

  // 筛选函数 - 移到useEffect之前
  const applyFilters = useCallback(() => {
    const currentRequirements = currentTab === 0 ? requirements.direct : requirements.creative;
    let filtered = [...currentRequirements];

    // 关键词筛选
    if (filters.keyword) {
      filtered = filtered.filter(req =>
        req.title.toLowerCase().includes(filters.keyword.toLowerCase()) ||
        req.description.toLowerCase().includes(filters.keyword.toLowerCase()) ||
        req.tags.some(tag => tag.toLowerCase().includes(filters.keyword.toLowerCase()))
      );
    }

    // 价格范围筛选
    if (filters.priceRange !== 'all') {
      filtered = filtered.filter(req => {
        const budget = req.budget;
        switch (filters.priceRange) {
          case 'low': return budget < 1000;
          case 'medium': return budget >= 1000 && budget < 3000;
          case 'high': return budget >= 3000;
          default: return true;
        }
      });
    }

    // 截止时间筛选
    if (filters.deadline !== 'all') {
      const now = new Date();
      filtered = filtered.filter(req => {
        const deadline = new Date(req.deadline);
        const diffDays = Math.ceil((deadline - now) / (1000 * 60 * 60 * 24));
        switch (filters.deadline) {
          case 'today': return diffDays <= 1;
          case 'week': return diffDays <= 7;
          case 'month': return diffDays <= 30;
          default: return true;
        }
      });
    }

    setFilteredRequirements(filtered);
  }, [currentTab, requirements, filters]);

  useEffect(() => {
    loadRequirements();
  }, [loadRequirements]);

  useEffect(() => {
    applyFilters();
  }, [applyFilters]);

  const handleTabChange = (event, newValue) => {
    setCurrentTab(newValue);
    setCurrentPage(1); // Reset pagination when switching tabs
  };

  const handleApplyOrder = async (requirement) => {
    setSelectedRequirement(requirement);
    // 重置表单数据
    setApplyFormData({
      estimated_delivery_days: 7,
      accept_message: ''
    });
    setShowApplyDialog(true);
  };

  const handleCloseApplyDialog = () => {
    setShowApplyDialog(false);
    // 重置表单数据
    setApplyFormData({
      estimated_delivery_days: 7,
      accept_message: ''
    });
  };

  const handleConfirmApply = async () => {
    setLoading(true);
    try {
      // 调用真正的接单接口
      const response = await contentService.applyContentRequest(selectedRequirement.id, applyFormData);

      if (response.success) {
        setShowApplyDialog(false);
        setSnackbar({
          open: true,
          message: `成功申请「${selectedRequirement.title}」！订单已添加到您的任务列表。`,
          severity: 'success'
        });

        // 重置表单数据
        setApplyFormData({
          estimated_delivery_days: 7,
          accept_message: ''
        });

        // 可以在这里刷新需求列表或更新状态
        loadRequirements();
      } else {
        setSnackbar({
          open: true,
          message: response.message || '申请失败，请重试',
          severity: 'error'
        });
      }
    } catch (error) {
      console.error('申请接单失败:', error);
      setSnackbar({
        open: true,
        message: error.message || '申请失败，请重试',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };



  const renderRequirementItem = (req, index, isLastItem) => (
    <Box
      sx={{
        px: 2.5,
        py: 1.75,
        transition: 'all 0.2s ease',
      }}
    >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          {/* 左侧主要信息 - 固定宽度 */}
          <Box sx={{ flex: '1 1 auto', minWidth: 0 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              {/* 类型标识 */}
              <Box sx={{ flexShrink: 0 }}>
                <Chip
                  icon={req.type === 'direct' ? <Send sx={{ fontSize: 14 }} /> : <AutoAwesome sx={{ fontSize: 14 }} />}
                  label={req.type === 'direct' ? '直发' : '创作'}
                  size="small"
                  sx={{
                    height: 22,
                    fontSize: '0.75rem',
                    backgroundColor: req.type === 'direct' ? '#dbeafe' : '#fef3c7',
                    color: req.type === 'direct' ? '#1e40af' : '#a16207',
                    '& .MuiChip-icon': {
                      ml: 0.5
                    }
                  }}
                />
              </Box>
              
              {/* 标题和描述 */}
              <Box sx={{ flex: 1, minWidth: 0 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
                  <Typography variant="subtitle1" sx={{ 
                    fontWeight: 600, 
                    color: '#1a1a1a',
                    fontSize: '1rem',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                    flex: '0 1 auto'
                  }}>
                    {req.title}
                  </Typography>
                  {req.status === 'urgent' && (
                    <Chip 
                      label="紧急" 
                      size="small" 
                      sx={{ 
                        height: 18,
                        fontSize: '0.7rem',
                        backgroundColor: '#fee2e2',
                        color: '#dc2626',
                        flexShrink: 0
                      }} 
                    />
                  )}
                  {req.status === 'hot' && (
                    <Chip 
                      label="热门" 
                      size="small" 
                      sx={{ 
                        height: 18,
                        fontSize: '0.7rem',
                        backgroundColor: '#fef3c7',
                        color: '#f59e0b',
                        flexShrink: 0
                      }} 
                    />
                  )}
                  {req.priority === 'high' && (
                    <Chip 
                      label="高优先级" 
                      size="small" 
                      sx={{ 
                        height: 18,
                        fontSize: '0.7rem',
                        backgroundColor: '#dcfce7',
                        color: '#16a34a',
                        flexShrink: 0
                      }} 
                    />
                  )}
                </Box>
                
                <Typography variant="body2" sx={{ 
                  color: '#6b7280',
                  mb: 1,
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                }}>
                  {req.description}
                </Typography>
                
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, flexWrap: 'wrap' }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                    <Business sx={{ fontSize: 14, color: '#9ca3af' }} />
                    <Typography variant="caption" sx={{ color: '#6b7280' }}>
                      {req.company}
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                    <CalendarToday sx={{ fontSize: 14, color: '#9ca3af' }} />
                    <Typography variant="caption" sx={{ color: '#6b7280' }}>
                      {req.deadline}
                    </Typography>
                  </Box>
                </Box>
              </Box>
            </Box>
          </Box>
          
          {/* 右侧固定区域 - 价格和操作按钮 */}
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            gap: 3,
            flexShrink: 0
          }}>
            {/* 价格 - 固定宽度 */}
            <Box sx={{ 
              width: 100,
              textAlign: 'right'
            }}>
              <Typography variant="h6" sx={{ 
                fontWeight: 600, 
                color: '#10b981',
                fontSize: '1.25rem'
              }}>
                ¥{req.budget}
              </Typography>
            </Box>
            
            {/* 操作按钮 - 优化样式 */}
            <Box sx={{ display: 'flex', gap: 1.5, alignItems: 'center' }}>
              <Button
                variant="outlined"
                size="small"
                startIcon={<Visibility sx={{ fontSize: 16 }} />}
                onClick={() => {
                  setSelectedRequirement(req);
                  setShowDetailDialog(true);
                }}
                sx={{
                  minWidth: 80,
                  borderColor: '#e5e7eb',
                  color: '#6b7280',
                  borderRadius: 1.5,
                  fontSize: '0.8rem',
                  fontWeight: 500,
                  py: 0.75,
                  px: 2,
                  transition: 'all 0.2s ease',
                  '&:hover': {
                    borderColor: '#3b82f6',
                    backgroundColor: '#f0f9ff',
                    color: '#3b82f6',
                    transform: 'translateY(-1px)',
                    boxShadow: '0 2px 8px rgba(59, 130, 246, 0.15)'
                  }
                }}
              >
                查看
              </Button>
              <Button
                variant="contained"
                size="small"
                startIcon={<HandshakeOutlined sx={{ fontSize: 16 }} />}
                onClick={() => handleApplyOrder(req)}
                sx={{
                  minWidth: 100,
                  background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
                  color: 'white',
                  borderRadius: 1.5,
                  fontSize: '0.8rem',
                  fontWeight: 600,
                  py: 0.75,
                  px: 2.5,
                  boxShadow: '0 2px 8px rgba(59, 130, 246, 0.3)',
                  transition: 'all 0.2s ease',
                  '&:hover': {
                    background: 'linear-gradient(135deg, #2563eb 0%, #1e40af 100%)',
                    transform: 'translateY(-1px)',
                    boxShadow: '0 4px 12px rgba(59, 130, 246, 0.4)'
                  }
                }}
              >
                立即接单
              </Button>
            </Box>
          </Box>
        </Box>
      </Box>
  );

  return (
    <StyledContainer>
      {/* 顶部标题区域 */}
      <HeaderSection>
        <Box sx={{ px: 4, py: 3 }}>
          <Box>
            <Typography variant="h4" sx={{
              fontWeight: 700,
              color: '#1a1a1a',
              mb: 0.5,
              fontSize: { xs: '1.5rem', md: '1.75rem' }
            }}>
              浏览需求
            </Typography>
            <Typography variant="body2" sx={{
              color: '#6b7280',
              fontSize: '0.9rem'
            }}>
              发现优质内容需求，选择适合的项目开始赚钱
            </Typography>
          </Box>
        </Box>

        {/* 标签页 */}
        <StyledTabs value={currentTab} onChange={handleTabChange} sx={{ minHeight: 48, mb: 0 }}>
          <Tab
            icon={<Send />}
            iconPosition="start"
            label={
              <Box display="flex" alignItems="center" gap={1}>
                稿件直发
                <Badge
                  badgeContent={requirements.direct.length}
                  color="primary"
                  sx={{
                    '& .MuiBadge-badge': {
                      backgroundColor: '#3b82f6',
                      color: 'white',
                      fontSize: '0.75rem',
                      fontWeight: 600,
                      minWidth: '20px',
                      height: '20px'
                    }
                  }}
                >
                  <Box sx={{ width: 8 }} />
                </Badge>
              </Box>
            }
          />
          <Tab
            icon={<AutoAwesome />}
            iconPosition="start"
            label={
              <Box display="flex" alignItems="center" gap={1}>
                创作需求
                <Badge
                  badgeContent={requirements.creative.length}
                  color="secondary"
                  sx={{
                    '& .MuiBadge-badge': {
                      backgroundColor: '#f59e0b',
                      color: 'white',
                      fontSize: '0.75rem',
                      fontWeight: 600,
                      minWidth: '20px',
                      height: '20px'
                    }
                  }}
                >
                  <Box sx={{ width: 8 }} />
                </Badge>
              </Box>
            }
          />
        </StyledTabs>
      </HeaderSection>

      {/* 内容区域 */}
      <ContentSection>
        <Box sx={{ pt: 0, pb: 4 }}>

          {/* 分页控制 */}
          <Box sx={{
            display: 'flex',
            justifyContent: 'flex-end',
            alignItems: 'center',
            mb: 0,
            mt: 0,
            pt: 0,
            px: 4,
            backgroundColor: 'white',
            borderTop: '1px solid #e2e8f0'
          }}>
            
            {/* 上一页/下一页按钮 */}
            {filteredRequirements.length > itemsPerPage && (
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Typography variant="body2" sx={{ color: '#6b7280', fontSize: '0.85rem', mr: 1 }}>
                  第 {currentPage} / {Math.ceil(filteredRequirements.length / itemsPerPage)} 页
                </Typography>
                <Button
                  size="small"
                  variant="outlined"
                  startIcon={<ChevronLeft />}
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                  disabled={currentPage === 1}
                  sx={{
                    minWidth: 80,
                    borderColor: '#e5e7eb',
                    color: '#6b7280',
                    fontSize: '0.85rem',
                    py: 0.5,
                    '&:hover': {
                      borderColor: '#d1d5db',
                      backgroundColor: '#f9fafb'
                    },
                    '&:disabled': {
                      borderColor: '#f3f4f6',
                      color: '#d1d5db'
                    }
                  }}
                >
                  上一页
                </Button>
                <Button
                  size="small"
                  variant="outlined"
                  endIcon={<ChevronRight />}
                  onClick={() => setCurrentPage(prev => Math.min(Math.ceil(filteredRequirements.length / itemsPerPage), prev + 1))}
                  disabled={currentPage === Math.ceil(filteredRequirements.length / itemsPerPage)}
                  sx={{
                    minWidth: 80,
                    borderColor: '#e5e7eb',
                    color: '#6b7280',
                    fontSize: '0.85rem',
                    py: 0.5,
                    '&:hover': {
                      borderColor: '#d1d5db',
                      backgroundColor: '#f9fafb'
                    },
                    '&:disabled': {
                      borderColor: '#f3f4f6',
                      color: '#d1d5db'
                    }
                  }}
                >
                  下一页
                </Button>
              </Box>
            )}
          </Box>

          {/* 需求列表 - 表格格式 */}
          {loading ? (
            <Box
              display="flex"
              flexDirection="column"
              alignItems="center"
              justifyContent="center"
              height="400px"
              sx={{ backgroundColor: 'transparent' }}
            >
              <LinearProgress sx={{ width: '200px', mb: 2 }} />
              <Typography variant="body2" color="text.secondary">
                正在加载需求数据...
              </Typography>
            </Box>
          ) : filteredRequirements.length === 0 ? (
            <Box sx={{ px: 4 }}>
              <Box
                display="flex"
                flexDirection="column"
                alignItems="center"
                justifyContent="center"
                height="400px"
                sx={{ backgroundColor: 'transparent' }}
              >
              <Assignment sx={{ fontSize: 64, color: 'text.disabled', mb: 2 }} />
              <Typography variant="h6" color="text.secondary" gutterBottom>
                {filters.keyword || filters.priceRange !== 'all' || filters.deadline !== 'all'
                  ? '没有找到符合条件的需求'
                  : '暂无需求数据'
                }
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {filters.keyword || filters.priceRange !== 'all' || filters.deadline !== 'all'
                  ? '尝试调整筛选条件或搜索关键词'
                  : '暂时没有可接的需求，请稍后再来看看'
                }
              </Typography>
              </Box>
            </Box>
          ) : (
            <Box sx={{ px: 0, pt: 0 }}>
              <TableContainer component={Paper} sx={{
                border: 'none',
                borderRadius: 0,
                overflow: 'hidden',
                boxShadow: 'none'
              }}>
                {/* 搜索和筛选栏 - 作为表格的一部分 */}
                <Box sx={{
                  pl: 0,
                  pr: 4,
                  py: 3,
                  borderBottom: '1px solid #e5e7eb'
                }}>
                  <Box sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: 2,
                    flexWrap: 'wrap'
                  }}>
                    {/* 搜索框 */}
                    <TextField
                      size="small"
                      placeholder="搜索需求标题、描述或公司名称..."
                      value={filters.keyword}
                      onChange={(e) => setFilters(prev => ({ ...prev, keyword: e.target.value }))}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Search sx={{ color: '#64748b', fontSize: 20 }} />
                          </InputAdornment>
                        ),
                      }}
                      sx={{
                        flex: 1,
                        minWidth: 300,
                        maxWidth: 450,
                        '& .MuiOutlinedInput-root': {
                          backgroundColor: 'white',
                          borderRadius: 1.5,
                          '& fieldset': {
                            borderColor: '#cbd5e1',
                          },
                          '&:hover fieldset': {
                            borderColor: '#94a3b8',
                          },
                          '&.Mui-focused fieldset': {
                            borderColor: '#3b82f6',
                            borderWidth: 2,
                          }
                        }
                      }}
                    />

                    {/* 筛选器组 */}
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, flexWrap: 'wrap' }}>
                      {/* 价格范围筛选 */}
                      <FormControl size="small" sx={{ minWidth: 130 }}>
                        <InputLabel sx={{ color: '#64748b', fontSize: '0.875rem' }}>价格范围</InputLabel>
                        <Select
                          value={filters.priceRange}
                          label="价格范围"
                          onChange={(e) => setFilters(prev => ({ ...prev, priceRange: e.target.value }))}
                          sx={{
                            backgroundColor: 'white',
                            borderRadius: 1.5,
                            '& fieldset': {
                              borderColor: '#cbd5e1',
                            },
                            '&:hover fieldset': {
                              borderColor: '#94a3b8',
                            },
                            '&.Mui-focused fieldset': {
                              borderColor: '#3b82f6',
                              borderWidth: 2,
                            }
                          }}
                        >
                          <MenuItem value="all">不限价格</MenuItem>
                          <MenuItem value="low">￥1000以下</MenuItem>
                          <MenuItem value="medium">￥1000-3000</MenuItem>
                          <MenuItem value="high">￥3000以上</MenuItem>
                        </Select>
                      </FormControl>

                      {/* 截止时间筛选 */}
                      <FormControl size="small" sx={{ minWidth: 130 }}>
                        <InputLabel sx={{ color: '#64748b', fontSize: '0.875rem' }}>截止时间</InputLabel>
                        <Select
                          value={filters.deadline}
                          label="截止时间"
                          onChange={(e) => setFilters(prev => ({ ...prev, deadline: e.target.value }))}
                          sx={{
                            backgroundColor: 'white',
                            borderRadius: 1.5,
                            '& fieldset': {
                              borderColor: '#cbd5e1',
                            },
                            '&:hover fieldset': {
                              borderColor: '#94a3b8',
                            },
                            '&.Mui-focused fieldset': {
                              borderColor: '#3b82f6',
                              borderWidth: 2,
                            }
                          }}
                        >
                          <MenuItem value="all">不限时间</MenuItem>
                          <MenuItem value="today">今天截止</MenuItem>
                          <MenuItem value="week">本周截止</MenuItem>
                          <MenuItem value="month">本月截止</MenuItem>
                        </Select>
                      </FormControl>

                      {/* 操作按钮组 */}
                      <Box sx={{ display: 'flex', gap: 1 }}>
                        {/* 刷新按钮 */}
                        <Button
                          variant="outlined"
                          startIcon={<Refresh />}
                          onClick={loadRequirements}
                          disabled={loading}
                          sx={{
                            borderColor: '#cbd5e1',
                            color: '#64748b',
                            backgroundColor: 'white',
                            borderRadius: 1.5,
                            px: 2,
                            '&:hover': {
                              borderColor: '#94a3b8',
                              backgroundColor: '#f1f5f9'
                            },
                            '&:disabled': {
                              borderColor: '#e2e8f0',
                              color: '#94a3b8'
                            }
                          }}
                        >
                          刷新
                        </Button>

                        {/* 清空筛选按钮 */}
                        {(filters.keyword || filters.priceRange !== 'all' || filters.deadline !== 'all') && (
                          <Button
                            variant="text"
                            size="small"
                            onClick={() => setFilters({ keyword: '', priceRange: 'all', deadline: 'all' })}
                            sx={{
                              color: '#64748b',
                              borderRadius: 1.5,
                              px: 2,
                              '&:hover': {
                                backgroundColor: '#f1f5f9'
                              }
                            }}
                          >
                            清空
                          </Button>
                        )}
                      </Box>
                    </Box>
                  </Box>
                </Box>

              <Table sx={{ minWidth: 650 }}>
                <TableHead>
                  <TableRow sx={{ backgroundColor: '#f8fafc' }}>
                    <TableCell sx={{ fontWeight: 600, color: '#374151', fontSize: '0.875rem' }}>
                      类型
                    </TableCell>
                    <TableCell sx={{ fontWeight: 600, color: '#374151', fontSize: '0.875rem' }}>
                      需求标题
                    </TableCell>
                    <TableCell sx={{ fontWeight: 600, color: '#374151', fontSize: '0.875rem' }}>
                      公司
                    </TableCell>
                    <TableCell sx={{ fontWeight: 600, color: '#374151', fontSize: '0.875rem' }}>
                      预算
                    </TableCell>
                    <TableCell sx={{ fontWeight: 600, color: '#374151', fontSize: '0.875rem' }}>
                      截止时间
                    </TableCell>
                    <TableCell sx={{ fontWeight: 600, color: '#374151', fontSize: '0.875rem' }}>
                      操作
                    </TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {filteredRequirements
                    .slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage)
                    .map((req, index) => (
                      <TableRow
                        key={req.id}
                        sx={{
                          '&:hover': {
                            backgroundColor: '#f9fafb'
                          },
                          '&:last-child td': {
                            borderBottom: 0
                          }
                        }}
                      >
                        <TableCell>
                          <Chip
                            icon={req.type === 'direct' ? <Send sx={{ fontSize: 14 }} /> : <AutoAwesome sx={{ fontSize: 14 }} />}
                            label={req.type === 'direct' ? '直发' : '创作'}
                            size="small"
                            sx={{
                              height: 24,
                              fontSize: '0.75rem',
                              backgroundColor: req.type === 'direct' ? '#dbeafe' : '#fef3c7',
                              color: req.type === 'direct' ? '#1e40af' : '#a16207',
                            }}
                          />
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2" sx={{
                            fontWeight: 600,
                            color: '#1a1a1a',
                            fontSize: '0.875rem'
                          }}>
                            {req.title}
                          </Typography>
                          <Typography variant="caption" sx={{
                            color: '#6b7280',
                            fontSize: '0.75rem',
                            display: 'block',
                            mt: 0.5
                          }}>
                            {req.description?.length > 50 ? `${req.description.substring(0, 50)}...` : req.description}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2" sx={{ fontSize: '0.875rem' }}>
                            {req.company}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2" sx={{
                            fontWeight: 600,
                            color: '#059669',
                            fontSize: '0.875rem'
                          }}>
                            ¥{req.budget?.toLocaleString()}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2" sx={{ fontSize: '0.875rem' }}>
                            {req.deadline}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Button
                            variant="contained"
                            size="small"
                            onClick={() => {
                              setSelectedRequirement(req);
                              setShowDetailDialog(true);
                            }}
                            sx={{
                              backgroundColor: '#3b82f6',
                              fontSize: '0.75rem',
                              px: 2,
                              py: 0.5,
                              '&:hover': {
                                backgroundColor: '#2563eb'
                              }
                            }}
                          >
                            查看详情
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                </TableBody>
              </Table>
              </TableContainer>
            </Box>
          )}
        </Box>
      </ContentSection>

      {/* 详情对话框 */}
      <Dialog
        open={showDetailDialog}
        onClose={() => setShowDetailDialog(false)}
        maxWidth="lg"
        fullWidth
        sx={{
          '& .MuiDialog-paper': {
            borderRadius: 2,
            maxHeight: '90vh'
          }
        }}
      >
        {selectedRequirement && (
          <>
            {/* 头部区域 */}
            <Box sx={{
              backgroundColor: '#fff',
              borderBottom: '1px solid #e5e7eb',
              p: 2.5,
              position: 'relative'
            }}>
              <IconButton
                onClick={() => setShowDetailDialog(false)}
                sx={{
                  position: 'absolute',
                  right: 16,
                  top: 16,
                  color: '#6b7280',
                  '&:hover': { backgroundColor: '#f3f4f6' }
                }}
              >
                <Close />
              </IconButton>

              <Box sx={{ pr: 6 }}>
                <Typography variant="h5" sx={{ fontWeight: 600, mb: 1, color: '#1f2937' }}>
                  {selectedRequirement.title}
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                  <Chip
                    label={selectedRequirement.type === 'direct' ? '稿件直发' : '内容创作'}
                    sx={{
                      backgroundColor: selectedRequirement.type === 'direct' ? '#dbeafe' : '#fef3c7',
                      color: selectedRequirement.type === 'direct' ? '#1e40af' : '#a16207',
                      fontWeight: 600
                    }}
                  />
                  <Typography variant="body2" sx={{ color: '#6b7280' }}>
                    发布企业：{selectedRequirement.company}
                  </Typography>
                </Box>

                {/* 关键信息卡片 */}
                <Grid container spacing={2}>
                  <Grid size={6}>
                    <Box sx={{
                      textAlign: 'center',
                      p: 1.5,
                      backgroundColor: '#f8fafc',
                      borderRadius: 1,
                      border: '1px solid #e2e8f0'
                    }}>
                      <AttachMoney sx={{ fontSize: 24, mb: 0.5, color: '#10b981' }} />
                      <Typography variant="h6" sx={{ fontWeight: 600, color: '#1f2937' }}>
                        ¥{selectedRequirement.budget || 0}
                      </Typography>
                      <Typography variant="caption" sx={{ color: '#6b7280' }}>项目预算</Typography>
                    </Box>
                  </Grid>
                  <Grid size={6}>
                    <Box sx={{
                      textAlign: 'center',
                      p: 1.5,
                      backgroundColor: '#f8fafc',
                      borderRadius: 1,
                      border: '1px solid #e2e8f0'
                    }}>
                      <Schedule sx={{ fontSize: 24, mb: 0.5, color: '#f59e0b' }} />
                      <Typography variant="body2" sx={{ fontWeight: 600, color: '#1f2937' }}>
                        {selectedRequirement.deadline}
                      </Typography>
                      <Typography variant="caption" sx={{ color: '#6b7280' }}>截止时间</Typography>
                    </Box>
                  </Grid>
                </Grid>
              </Box>
            </Box>

            {/* 内容区域 */}
            <DialogContent sx={{ p: 0 }}>
              <Box sx={{ p: 3 }}>
                {/* 需求描述 */}
                {selectedRequirement.description && (
                  <Box sx={{ mb: 3 }}>
                    <Typography variant="h6" sx={{
                      fontWeight: 600,
                      color: '#1e293b',
                      mb: 2,
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1
                    }}>
                      <Box sx={{
                        width: 4,
                        height: 20,
                        backgroundColor: '#3b82f6',
                        borderRadius: 1
                      }} />
                      需求描述
                    </Typography>
                    <Paper sx={{
                      p: 3,
                      backgroundColor: '#f8fafc',
                      border: '1px solid #e2e8f0',
                      borderRadius: 2,
                      boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
                    }}>
                      <Typography variant="body1" sx={{
                        color: '#374151',
                        lineHeight: 1.7,
                        whiteSpace: 'pre-wrap',
                        fontSize: '0.95rem'
                      }}>
                        {selectedRequirement.description}
                      </Typography>
                    </Paper>
                  </Box>
                )}

                {/* 稿件标题 - 仅稿件直发显示 */}
                {selectedRequirement.providedContentTitle && (
                  <Box sx={{ mb: 3 }}>
                    <Typography variant="h6" sx={{
                      fontWeight: 600,
                      color: '#1e293b',
                      mb: 2,
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1
                    }}>
                      <Box sx={{
                        width: 4,
                        height: 20,
                        backgroundColor: '#8b5cf6',
                        borderRadius: 1
                      }} />
                      稿件标题
                    </Typography>
                    <Paper sx={{
                      p: 3,
                      backgroundColor: '#faf5ff',
                      border: '1px solid #e9d5ff',
                      borderRadius: 2,
                      boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
                    }}>
                      <Typography variant="h6" sx={{
                        color: '#374151',
                        fontWeight: 600,
                        fontSize: '1.1rem'
                      }}>
                        {selectedRequirement.providedContentTitle}
                      </Typography>
                    </Paper>
                  </Box>
                )}

                {/* 稿件内容 - 仅稿件直发显示 */}
                {selectedRequirement.providedContentText && (
                  <Box sx={{ mb: 3 }}>
                    <Typography variant="h6" sx={{
                      fontWeight: 600,
                      color: '#1e293b',
                      mb: 2,
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1
                    }}>
                      <Box sx={{
                        width: 4,
                        height: 20,
                        backgroundColor: '#10b981',
                        borderRadius: 1
                      }} />
                      稿件内容
                    </Typography>
                    <Paper sx={{
                      p: 3,
                      backgroundColor: '#f0fdf4',
                      border: '1px solid #bbf7d0',
                      borderRadius: 2,
                      boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
                    }}>
                      <Typography variant="body1" sx={{
                        color: '#374151',
                        lineHeight: 1.7,
                        whiteSpace: 'pre-wrap',
                        fontSize: '0.95rem'
                      }}>
                        {selectedRequirement.providedContentText}
                      </Typography>
                    </Paper>
                  </Box>
                )}

                {/* 附件文件 */}
                {selectedRequirement.providedContentFiles && selectedRequirement.providedContentFiles.length > 0 && (
                  <Box sx={{ mb: 3 }}>
                    <Typography variant="h6" sx={{
                      fontWeight: 600,
                      color: '#1e293b',
                      mb: 2,
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1
                    }}>
                      <Box sx={{
                        width: 4,
                        height: 20,
                        backgroundColor: '#f59e0b',
                        borderRadius: 1
                      }} />
                      附件文件
                    </Typography>
                    <Paper sx={{
                      p: 3,
                      backgroundColor: '#fffbeb',
                      border: '1px solid #fed7aa',
                      borderRadius: 2,
                      boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
                    }}>
                      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                        {selectedRequirement.providedContentFiles.map((file, index) => (
                          <Box
                            key={index}
                            sx={{
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'space-between',
                              p: 2,
                              backgroundColor: '#fff',
                              border: '1px solid #e5e7eb',
                              borderRadius: 1,
                              '&:hover': {
                                backgroundColor: '#f9fafb',
                                borderColor: '#d1d5db'
                              }
                            }}
                          >
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, flex: 1 }}>
                              <AttachFile sx={{ color: '#6b7280', fontSize: 20 }} />
                              <Box>
                                <Typography variant="body2" sx={{ fontWeight: 500, color: '#374151' }}>
                                  {file.name || file.filename || file.original_name || '未知文件'}
                                </Typography>
                                {file.size && (
                                  <Typography variant="caption" sx={{ color: '#6b7280' }}>
                                    {uploadService.formatFileSize(file.size)}
                                  </Typography>
                                )}
                              </Box>
                            </Box>
                            <Button
                              size="small"
                              variant="outlined"
                              startIcon={<Download />}
                              onClick={() => handleDownloadFile(file)}
                              sx={{
                                borderColor: '#e5e7eb',
                                color: '#6b7280',
                                '&:hover': {
                                  borderColor: '#3b82f6',
                                  backgroundColor: '#f0f9ff',
                                  color: '#3b82f6'
                                }
                              }}
                            >
                              下载
                            </Button>
                          </Box>
                        ))}
                      </Box>
                    </Paper>
                  </Box>
                )}

                {/* 创作要求 - 仅创作需求显示 */}
                {selectedRequirement.requirements && (
                  <Box sx={{ mb: 3 }}>
                    <Typography variant="h6" sx={{
                      fontWeight: 600,
                      color: '#1e293b',
                      mb: 2,
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1
                    }}>
                      <Box sx={{
                        width: 4,
                        height: 20,
                        backgroundColor: '#f59e0b',
                        borderRadius: 1
                      }} />
                      创作要求
                    </Typography>
                    <Paper sx={{
                      p: 3,
                      backgroundColor: '#fffbeb',
                      border: '1px solid #fed7aa',
                      borderRadius: 2,
                      boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
                    }}>
                      <Typography variant="body1" sx={{
                        color: '#374151',
                        lineHeight: 1.7,
                        whiteSpace: 'pre-wrap',
                        fontSize: '0.95rem'
                      }}>
                        {selectedRequirement.requirements}
                      </Typography>
                    </Paper>
                  </Box>
                )}

                {/* 标签展示 */}
                {selectedRequirement.tags && selectedRequirement.tags.length > 0 && (
                  <Box sx={{ mb: 3 }}>
                    <Typography variant="h6" sx={{
                      fontWeight: 600,
                      color: '#1e293b',
                      mb: 2,
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1
                    }}>
                      <Box sx={{
                        width: 4,
                        height: 20,
                        backgroundColor: '#06b6d4',
                        borderRadius: 1
                      }} />
                      相关标签
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                      {selectedRequirement.tags.map((tag, index) => (
                        <Chip
                          key={index}
                          label={tag}
                          size="medium"
                          sx={{
                            backgroundColor: '#eff6ff',
                            color: '#3b82f6',
                            border: '1px solid #bfdbfe',
                            fontWeight: 500,
                            '&:hover': {
                              backgroundColor: '#dbeafe',
                              transform: 'translateY(-1px)',
                              boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
                            },
                            transition: 'all 0.2s ease'
                          }}
                        />
                      ))}
                    </Box>
                  </Box>
                )}

                {/* 其他信息 */}
                <Grid container spacing={3}>
                  <Grid size={{ xs: 12, md: 6 }}>
                    <Paper sx={{ p: 3, height: '100%', border: '1px solid #e2e8f0' }}>
                      <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2, color: '#1f2937' }}>
                        发布信息
                      </Typography>
                      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                          <Typography variant="body2" color="text.secondary">创建时间：</Typography>
                          <Typography variant="body2">
                            {selectedRequirement.createdAt ? new Date(selectedRequirement.createdAt).toLocaleString() : '未知'}
                          </Typography>
                        </Box>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                          <Typography variant="body2" color="text.secondary">截止时间：</Typography>
                          <Typography variant="body2">{selectedRequirement.deadline}</Typography>
                        </Box>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                          <Typography variant="body2" color="text.secondary">需求状态：</Typography>
                          <Typography variant="body2">{selectedRequirement.statusText}</Typography>
                        </Box>
                      </Box>
                    </Paper>
                  </Grid>
                  <Grid size={{ xs: 12, md: 6 }}>
                    <Paper sx={{ p: 3, height: '100%', border: '1px solid #e2e8f0' }}>
                      <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2, color: '#1f2937' }}>
                        企业信息
                      </Typography>
                      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                          <Typography variant="body2" color="text.secondary">企业名称：</Typography>
                          <Typography variant="body2">{selectedRequirement.company}</Typography>
                        </Box>
                        {selectedRequirement.estimatedDeliveryDays && (
                          <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                            <Typography variant="body2" color="text.secondary">预计交付：</Typography>
                            <Typography variant="body2">{selectedRequirement.estimatedDeliveryDays} 天</Typography>
                          </Box>
                        )}
                        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                          <Typography variant="body2" color="text.secondary">需求类型：</Typography>
                          <Typography variant="body2">
                            {selectedRequirement.type === 'creative' ? '内容创作' : '稿件直发'}
                          </Typography>
                        </Box>
                      </Box>
                    </Paper>
                  </Grid>
                </Grid>
              </Box>
            </DialogContent>

            {/* 底部操作区域 */}
            <Box sx={{
              p: 3,
              backgroundColor: '#f8fafc',
              borderTop: '1px solid #e2e8f0',
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center'
            }}>
              <Box>
                <Typography variant="body2" color="text.secondary">
                  接单后请按时完成任务，维护良好的信用记录
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', gap: 2 }}>
                <Button
                  variant="outlined"
                  onClick={() => setShowDetailDialog(false)}
                  sx={{ minWidth: 100 }}
                >
                  关闭
                </Button>
                <Button
                  variant="contained"
                  size="large"
                  startIcon={<HandshakeOutlined />}
                  onClick={() => {
                    setShowDetailDialog(false);
                    handleApplyOrder(selectedRequirement);
                  }}
                  sx={{
                    minWidth: 120,
                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                    '&:hover': {
                      background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',
                    }
                  }}
                >
                  立即接单
                </Button>
              </Box>
            </Box>
          </>
        )}
      </Dialog>

      {/* 申请对话框 */}
      <Dialog
        open={showApplyDialog}
        onClose={handleCloseApplyDialog}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>确认接单</DialogTitle>
        <DialogContent>
          {selectedRequirement && (
            <Box>
              <Typography variant="h6" gutterBottom>
                {selectedRequirement.title}
              </Typography>
              <Typography color="text.secondary" paragraph>
                项目预算：¥{selectedRequirement.budget}
              </Typography>

              {/* 接单表单 */}
              <Box sx={{ mt: 3, mb: 2 }}>
                <FormControl fullWidth sx={{ mb: 2 }}>
                  <InputLabel>预计交付天数</InputLabel>
                  <Select
                    value={applyFormData.estimated_delivery_days}
                    label="预计交付天数"
                    onChange={(e) => setApplyFormData(prev => ({
                      ...prev,
                      estimated_delivery_days: e.target.value
                    }))}
                  >
                    {[1, 2, 3, 5, 7, 10, 15, 20, 30].map(days => (
                      <MenuItem key={days} value={days}>
                        {days} 天
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>

                <TextField
                  fullWidth
                  multiline
                  rows={3}
                  label="接单留言（可选）"
                  placeholder="请简要说明您的优势和计划..."
                  value={applyFormData.accept_message}
                  onChange={(e) => setApplyFormData(prev => ({
                    ...prev,
                    accept_message: e.target.value
                  }))}
                  slotProps={{
                    htmlInput: { maxLength: 500 }
                  }}
                  helperText={`${applyFormData.accept_message.length}/500`}
                />
              </Box>

              <Alert severity="warning" sx={{ mb: 2 }}>
                接单后请按时完成任务，维护良好的信用记录
              </Alert>
              {loading && <LinearProgress sx={{ mb: 2 }} />}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseApplyDialog} disabled={loading}>
            取消
          </Button>
          <Button
            variant="contained"
            onClick={handleConfirmApply}
            disabled={loading}
          >
            {loading ? '申请中...' : '确认接单'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={4000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          variant="filled"
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </StyledContainer>
  );
}

export default BrowseRequirements;