import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Box,
  Typography,

  Chip,
  Grid,
  InputAdornment,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material';
import { Add, Remove } from '@mui/icons-material';
import channelService from '../../services/channelService';

function CreateServiceForm({ open, onClose, onSuccess }) {
  const [loading, setLoading] = useState(false);
  const [categories, setCategories] = useState([]);
  const [error, setError] = useState('');

  const [formData, setFormData] = useState({
    category_id: '',
    service_name: '',
    service_code: '',
    service_description: '',
    service_features: [],
    base_price: '',
    discount_price: '',
    price_unit: '次',
    service_specs: {},
    delivery_time: 24,
    revision_count: 2,
    channel_type: '',
    portal_type: '',
    platform_specs: {},
    coverage_area: []
  });



  // 可选配置项
  const availableChannelTypes = [
    '新闻资讯', '娱乐内容', '科技数码', '财经商业', '教育培训', 
    '健康养生', '美食旅游', '时尚生活', '体育运动', '汽车房产', 
    '母婴亲子', '游戏动漫'
  ];

  const availablePortalTypes = [
    '微信公众号', '微博', '抖音', '快手', '小红书', 'B站', 
    '知乎', '今日头条', '新闻门户', '行业门户', '地方门户', 
    '企业门户', '电商平台', '社交平台'
  ];



  // 加载分类列表
  useEffect(() => {
    if (open) {
      loadCategories();
    }
  }, [open]);

  const loadCategories = async () => {
    try {
      const response = await channelService.getAvailableCategories();
      if (response.success && response.data) {
        // 后端返回的数据结构是 { categories: [...], pagination: {...} }
        const categoriesData = response.data.categories || response.data.items || [];
        setCategories(Array.isArray(categoriesData) ? categoriesData : []);
      } else {
        setCategories([]);
      }
    } catch (error) {
      console.error('加载分类失败:', error);
      setCategories([]);
    }
  };

  const handleChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    setError('');
  };



  const handleRemoveFeature = (feature) => {
    setFormData(prev => ({
      ...prev,
      service_features: prev.service_features.filter(f => f !== feature)
    }));
  };





  const handleSubmit = async () => {
    // 表单验证
    if (!formData.category_id) {
      setError('请选择服务分类');
      return;
    }
    if (!formData.service_name.trim()) {
      setError('请输入服务名称');
      return;
    }
    if (!formData.service_code.trim()) {
      setError('请输入服务代码');
      return;
    }
    if (!formData.service_description.trim()) {
      setError('请输入服务描述');
      return;
    }
    if (!formData.base_price || parseFloat(formData.base_price) <= 0) {
      setError('请输入有效的基础价格');
      return;
    }

    setLoading(true);
    setError('');

    try {
      // 准备提交数据
      const submitData = {
        ...formData,
        base_price: parseFloat(formData.base_price),
        discount_price: formData.discount_price ? parseFloat(formData.discount_price) : null,
        delivery_time: parseInt(formData.delivery_time),
        revision_count: parseInt(formData.revision_count)
      };

      const response = await channelService.createProviderService(submitData);
      
      if (response.success) {
        onSuccess && onSuccess();
        handleClose();
      } else {
        setError(response.message || '创建服务失败');
      }
    } catch (error) {
      console.error('创建服务失败:', error);
      setError(error.response?.data?.detail || '创建服务失败');
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setFormData({
      category_id: '',
      service_name: '',
      service_code: '',
      service_description: '',
      service_features: [],
      base_price: '',
      discount_price: '',
      price_unit: '次',
      service_specs: {},
      delivery_time: 24,
      revision_count: 2,
      channel_type: '',
      portal_type: '',
      platform_specs: {},
      coverage_area: []
    });
    setError('');
    onClose();
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="md"
      fullWidth
      scroll="paper"
      PaperProps={{
        sx: {
          borderRadius: 3,
          maxHeight: '95vh',
          margin: 1,
        }
      }}
    >
      <DialogTitle sx={{
        pb: 2,
        borderBottom: '1px solid #e0e0e0',
        display: 'flex',
        alignItems: 'center',
        gap: 2
      }}>
        <Box sx={{
          width: 40,
          height: 40,
          borderRadius: 2,
          bgcolor: '#1976d2',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}>
          <Add sx={{ color: 'white' }} />
        </Box>
        <Box>
          <Typography variant="h6" sx={{ fontWeight: 600 }}>
            创建渠道服务
          </Typography>
          <Typography variant="body2" color="text.secondary">
            创建您自己的渠道服务，提交后需要等待管理员审核
          </Typography>
        </Box>
      </DialogTitle>

      <DialogContent sx={{ pt: 3, pb: 2 }}>
        {error && (
          <Alert severity="error" sx={{ mb: 3, borderRadius: 2 }}>
            {error}
          </Alert>
        )}

        <Box sx={{ pt: 3, pb: 2 }}>
          {/* 基本信息 */}
          <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2, color: '#1976d2' }}>
            基本信息
          </Typography>
          <Grid container spacing={2} sx={{ mb: 2 }}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                required
                label="服务名称"
                value={formData.service_name}
                onChange={(e) => handleChange('service_name', e.target.value)}
                placeholder="例如：微信公众号代运营服务"
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                    '&:hover fieldset': {
                      borderColor: '#1976d2',
                    }
                  }
                }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                required
                label="服务代码"
                value={formData.service_code}
                onChange={(e) => handleChange('service_code', e.target.value)}
                placeholder="例如：wechat_operation"
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                    '&:hover fieldset': {
                      borderColor: '#1976d2',
                    }
                  }
                }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth required sx={{ minWidth: 200 }}>
                <InputLabel>渠道分类</InputLabel>
                <Select
                  value={formData.category_id}
                  onChange={(e) => handleChange('category_id', e.target.value)}
                  label="渠道分类"
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 2,
                      '&:hover fieldset': {
                        borderColor: '#1976d2',
                      }
                    }
                  }}
                >
                  {Array.isArray(categories) && categories.map((category) => (
                    <MenuItem key={category.id} value={category.id}>
                      {category.category_name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
          </Grid>

          {/* 服务描述单独一行 */}
          <TextField
            fullWidth
            required
            multiline
            rows={3}
            label="服务描述"
            value={formData.service_description}
            onChange={(e) => handleChange('service_description', e.target.value)}
            placeholder="详细描述您的服务内容、特色和优势..."
            sx={{
              mb: 3,
              '& .MuiOutlinedInput-root': {
                borderRadius: 2,
                '&:hover fieldset': {
                  borderColor: '#1976d2',
                }
              }
            }}
          />

          {/* 服务特色 */}
          <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2, color: '#1976d2' }}>
            服务特色
          </Typography>
          <Grid container spacing={2} sx={{ mb: 3 }}>
            <Grid item xs={12}>
              {/* 特色标签显示 */}
              <Box sx={{ mb: 2 }}>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                  {Array.isArray(formData.service_features) && formData.service_features.map((feature, index) => (
                    <Chip
                      key={index}
                      label={feature}
                      onDelete={() => handleRemoveFeature(feature)}
                      color="primary"
                      variant="outlined"
                      sx={{ borderRadius: '16px' }}
                    />
                  ))}
                </Box>

                {/* 添加特色输入框 */}
                <TextField
                  fullWidth
                  label="添加服务特色"
                  placeholder="输入特色后按回车添加"
                  variant="outlined"
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      const value = e.target.value.trim();
                      if (value && !formData.service_features.includes(value)) {
                        const newFeatures = [...(formData.service_features || []), value];
                        setFormData({ ...formData, service_features: newFeatures });
                        e.target.value = '';
                      }
                    }
                  }}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 2,
                      '&:hover fieldset': {
                        borderColor: '#1976d2',
                      }
                    }
                  }}
                />
              </Box>
            </Grid>
          </Grid>

          {/* 价格信息 */}
          <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2, color: '#1976d2' }}>
            价格信息
          </Typography>
          <Grid container spacing={2} sx={{ mb: 3 }}>
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                required
                type="number"
                label="基础价格"
                value={formData.base_price}
                onChange={(e) => handleChange('base_price', e.target.value)}
                InputProps={{
                  startAdornment: <InputAdornment position="start">¥</InputAdornment>,
                }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                    '&:hover fieldset': {
                      borderColor: '#1976d2',
                    }
                  }
                }}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                type="number"
                label="优惠价格"
                value={formData.discount_price}
                onChange={(e) => handleChange('discount_price', e.target.value)}
                InputProps={{
                  startAdornment: <InputAdornment position="start">¥</InputAdornment>,
                }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                    '&:hover fieldset': {
                      borderColor: '#1976d2',
                    }
                  }
                }}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="计费单位"
                value={formData.price_unit}
                onChange={(e) => handleChange('price_unit', e.target.value)}
                placeholder="例如：次、篇、个、小时、天、月"
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                    '&:hover fieldset': {
                      borderColor: '#1976d2',
                    }
                  }
                }}
              />
            </Grid>
          </Grid>

          {/* 服务数据 */}
          <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2, color: '#1976d2' }}>
            服务数据
          </Typography>
          <Grid container spacing={2} sx={{ mb: 3 }}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                type="number"
                label="交付时间"
                value={formData.delivery_time}
                onChange={(e) => handleChange('delivery_time', e.target.value)}
                InputProps={{
                  endAdornment: <Typography variant="body2" color="text.secondary">小时</Typography>
                }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                    '&:hover fieldset': {
                      borderColor: '#1976d2',
                    }
                  }
                }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                type="number"
                label="修改次数"
                value={formData.revision_count}
                onChange={(e) => handleChange('revision_count', e.target.value)}
                InputProps={{
                  endAdornment: <Typography variant="body2" color="text.secondary">次</Typography>
                }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                    '&:hover fieldset': {
                      borderColor: '#1976d2',
                    }
                  }
                }}
              />
            </Grid>
          </Grid>

          {/* 服务规格 */}
          <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2, color: '#1976d2' }}>
            服务规格
          </Typography>
          <Box sx={{ mb: 3 }}>
            <TextField
              fullWidth
              label="服务规格说明"
              placeholder="请输入服务规格说明"
              multiline
              rows={3}
              value={formData.service_specs?.description || ''}
              onChange={(e) => {
                const text = e.target.value;
                // 将文本转换为简单的JSON格式存储
                const specs = text ? { description: text } : {};
                setFormData({
                  ...formData,
                  service_specs: specs
                });
              }}
              variant="outlined"
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: 2,
                  '&:hover fieldset': {
                    borderColor: '#1976d2',
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: '#1976d2',
                  }
                },
                '& .MuiInputLabel-root.Mui-focused': {
                  color: '#1976d2',
                }
              }}
            />
          </Box>

          {/* 渠道信息 */}
          <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2, color: '#1976d2' }}>
            渠道信息
          </Typography>

          <Grid container spacing={2} sx={{ mb: 4 }}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="频道类别"
                value={formData.channel_type}
                onChange={(e) => handleChange('channel_type', e.target.value)}
                placeholder="例如：新闻资讯、娱乐内容、科技数码等"
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                    '&:hover fieldset': {
                      borderColor: '#1976d2',
                    }
                  }
                }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="综合门户"
                value={formData.portal_type}
                onChange={(e) => handleChange('portal_type', e.target.value)}
                placeholder="例如：微信公众号、微博、抖音、小红书等"
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                    '&:hover fieldset': {
                      borderColor: '#1976d2',
                    }
                  }
                }}
              />
            </Grid>
          </Grid>

          {/* 覆盖区域 */}
          <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2, color: '#1976d2' }}>
            覆盖区域
          </Typography>
          <Grid container spacing={2} sx={{ mb: 3 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="覆盖区域"
                placeholder="请输入覆盖区域，多个区域用逗号分隔，例如：全国,华北,北京,上海"
                value={Array.isArray(formData.coverage_area) ? formData.coverage_area.join(',') : ''}
                onChange={(e) => {
                  const value = e.target.value;
                  const areas = value ? value.split(',').map(area => area.trim()).filter(area => area) : [];
                  setFormData({ ...formData, coverage_area: areas });
                }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                    '&:hover fieldset': {
                      borderColor: '#1976d2',
                    }
                  }
                }}
              />
            </Grid>
          </Grid>


        </Box>
      </DialogContent>

      <DialogActions sx={{
        p: 3,
        borderTop: '1px solid #e0e0e0',
        gap: 2
      }}>
        <Button
          onClick={handleClose}
          disabled={loading}
          sx={{
            borderRadius: 2,
            px: 4,
            py: 1,
            color: '#666',
            '&:hover': {
              bgcolor: '#f5f5f5'
            }
          }}
        >
          取消
        </Button>
        <Button
          variant="contained"
          onClick={handleSubmit}
          disabled={loading || !formData.service_name.trim() || !formData.service_code.trim() || !formData.category_id.trim()}
          sx={{
            borderRadius: 2,
            px: 4,
            py: 1,
            background: 'linear-gradient(45deg, #1976d2 30%, #42a5f5 90%)',
            boxShadow: '0 3px 5px 2px rgba(25, 118, 210, .3)',
            '&:hover': {
              background: 'linear-gradient(45deg, #1565c0 30%, #1976d2 90%)',
            },
            '&:disabled': {
              background: '#e0e0e0',
              color: '#999'
            }
          }}
        >
          {loading ? '创建中...' : '创建服务'}
        </Button>
      </DialogActions>
    </Dialog>
  );
}

export default CreateServiceForm;
