import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  Grid,
  Chip,
  Divider,
  IconButton,
} from '@mui/material';
import {
  Close,
  Store,
  Star,
  MonetizationOn,
  Description,
  Assessment,
} from '@mui/icons-material';

const ServiceDetailDialog = ({ open, onClose, serviceData }) => {
  // 关闭对话框
  const handleClose = () => {
    onClose();
  };

  // 获取状态样式
  const getStatusStyle = (isActive) => {
    return isActive
      ? { bg: '#d1fae5', text: '#065f46', label: '运营中' }
      : { bg: '#fee2e2', text: '#991b1b', label: '已停用' };
  };

  // 从 serviceData 中提取服务信息
  // 根据数据类型处理不同的数据结构
  let service, mapping, category;

  if (serviceData?.type === 'created') {
    // 我创建的服务：service字段直接就是服务数据
    service = serviceData?.service || {};
    mapping = serviceData || {};
    category = service?.category || {};
    console.log('我创建的服务数据:', { serviceData, service, category });
  } else {
    // 已绑定服务：service字段嵌套在service属性中
    service = serviceData?.service || {};
    mapping = serviceData || {};
    category = service?.category || {};
    console.log('已绑定服务数据:', { serviceData, service, category });
  }

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="md"
      fullWidth
      sx={{
        '& .MuiDialog-paper': {
          borderRadius: 2,
          maxHeight: '90vh'
        }
      }}
    >
      <DialogTitle sx={{
        pb: 2,
        borderBottom: '1px solid #e0e0e0',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between'
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Box sx={{
            width: 40,
            height: 40,
            borderRadius: 2,
            bgcolor: '#3b82f6',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            <Store sx={{ color: 'white' }} />
          </Box>
          <Box>
            <Typography variant="h6" sx={{ fontWeight: 600 }}>
              服务详情
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {serviceData?.serviceName || '查看服务的详细信息'}
            </Typography>
          </Box>
        </Box>
        <IconButton onClick={handleClose} size="small">
          <Close />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ pt: 3 }}>
        {/* 服务详情内容 */}
        {serviceData && (
          <Box>
            {/* 基本信息 */}
            <Box sx={{ mb: 4 }}>
              <Typography variant="h6" sx={{ 
                fontWeight: 600, 
                mb: 3, 
                color: '#3b82f6', 
                display: 'flex', 
                alignItems: 'center', 
                gap: 1 
              }}>
                <Store sx={{ fontSize: 20 }} />
                基本信息
              </Typography>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Box sx={{ p: 2, bgcolor: '#f8f9fa', borderRadius: 2 }}>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                      服务名称
                    </Typography>
                    <Typography variant="body1" sx={{ fontWeight: 600 }}>
                      {service.service_name || '未知服务'}
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Box sx={{ p: 2, bgcolor: '#f8f9fa', borderRadius: 2 }}>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                      服务状态
                    </Typography>
                    <Chip
                      label={getStatusStyle(service.is_active).label}
                      sx={{
                        backgroundColor: getStatusStyle(service.is_active).bg,
                        color: getStatusStyle(service.is_active).text,
                        fontWeight: 500
                      }}
                      size="small"
                    />
                  </Box>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Box sx={{ p: 2, bgcolor: '#f8f9fa', borderRadius: 2 }}>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                      服务分类
                    </Typography>
                    <Typography variant="body1" sx={{ fontWeight: 600 }}>
                      {service.category?.category_name || '未分类'}
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Box sx={{ p: 2, bgcolor: '#f8f9fa', borderRadius: 2 }}>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                      平台类型
                    </Typography>
                    <Typography variant="body1" sx={{ fontWeight: 600 }}>
                      {service.portal_type || service.portalType || service.channel_type || service.channelType || service.platform_type || '未知平台'}
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </Box>

            <Divider sx={{ my: 3 }} />

            {/* 价格和交付信息 */}
            <Box sx={{ mb: 4 }}>
              <Typography variant="h6" sx={{ 
                fontWeight: 600, 
                mb: 3, 
                color: '#f59e0b', 
                display: 'flex', 
                alignItems: 'center', 
                gap: 1 
              }}>
                <MonetizationOn sx={{ fontSize: 20 }} />
                价格与交付
              </Typography>
              <Grid container spacing={3}>
                <Grid item xs={12} md={3}>
                  <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#f8f9fa', borderRadius: 2 }}>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                      基础价格
                    </Typography>
                    <Typography variant="h6" sx={{ fontWeight: 600, color: '#f59e0b' }}>
                      ¥{service.base_price || 0}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      /{service.price_unit || '次'}
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} md={3}>
                  <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#f8f9fa', borderRadius: 2 }}>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                      交付时长
                    </Typography>
                    <Typography variant="h6" sx={{ fontWeight: 600 }}>
                      {service.delivery_time || 0}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      小时
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} md={3}>
                  <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#f8f9fa', borderRadius: 2 }}>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                      修改次数
                    </Typography>
                    <Typography variant="h6" sx={{ fontWeight: 600 }}>
                      {service.revision_count || 0}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      次
                    </Typography>
                  </Box>
                </Grid>

              </Grid>
            </Box>

            <Divider sx={{ my: 3 }} />

            {/* 服务描述 */}
            {service.service_description && (
              <Box sx={{ mb: 4 }}>
                <Typography variant="h6" sx={{
                  fontWeight: 600,
                  mb: 3,
                  color: '#10b981',
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1
                }}>
                  <Description sx={{ fontSize: 20 }} />
                  服务描述
                </Typography>
                <Box sx={{ p: 3, bgcolor: '#f8f9fa', borderRadius: 2 }}>
                  <Typography variant="body1" sx={{ lineHeight: 1.6 }}>
                    {service.service_description}
                  </Typography>
                </Box>
              </Box>
            )}

            {/* 统计信息 */}
            <Box>
              <Typography variant="h6" sx={{ 
                fontWeight: 600, 
                mb: 3, 
                color: '#8b5cf6', 
                display: 'flex', 
                alignItems: 'center', 
                gap: 1 
              }}>
                <Assessment sx={{ fontSize: 20 }} />
                统计信息
              </Typography>
              <Grid container spacing={3}>

                <Grid item xs={12} md={4}>
                  <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#f8f9fa', borderRadius: 2 }}>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                      创建时间
                    </Typography>
                    <Typography variant="body1" sx={{ fontWeight: 600 }}>
                      {service.created_at ? new Date(service.created_at).toLocaleDateString('zh-CN') : '-'}
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} md={4}>
                  <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#f8f9fa', borderRadius: 2 }}>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                      更新时间
                    </Typography>
                    <Typography variant="body1" sx={{ fontWeight: 600 }}>
                      {service.updated_at ? new Date(service.updated_at).toLocaleDateString('zh-CN') : '-'}
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </Box>
          </Box>
        )}
      </DialogContent>

      <DialogActions sx={{ px: 3, py: 2, borderTop: '1px solid #e0e0e0' }}>
        <Button onClick={handleClose} variant="outlined">
          关闭
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ServiceDetailDialog;
