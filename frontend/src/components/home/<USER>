import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Button,
  TextField,
  Chip,
  LinearProgress,
  Stack,
  Avatar,
  Divider,
  IconButton,
  Paper,
} from '@mui/material';
import {
  AutoAwesome,
  ContentCopy,
  Refresh,
  TrendingUp,
  Psychology,
  Speed,
  CheckCircle,
} from '@mui/icons-material';

function AIContentCreator() {
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedText, setGeneratedText] = useState('');
  const [keywords, setKeywords] = useState(['AI搜索', 'SEO优化', '内容营销']);

  // 模拟打字机效果
  useEffect(() => {
    if (isGenerating) {
      const sampleText = `基于人工智能的搜索引擎优化策略正在革新数字营销领域。通过深度学习算法，我们能够精准预测用户搜索意图，优化内容结构，提升网站在搜索结果中的排名。

关键优化要点：
1. 语义化内容优化 - 确保内容与用户搜索意图高度匹配
2. 结构化数据标记 - 帮助搜索引擎更好理解页面内容
3. 用户体验优化 - 提升页面加载速度和交互体验

通过AI驱动的内容优化，企业可以实现搜索流量增长300%以上...`;

      let index = 0;
      const timer = setInterval(() => {
        if (index < sampleText.length) {
          setGeneratedText(sampleText.substring(0, index + 1));
          index++;
        } else {
          setIsGenerating(false);
          clearInterval(timer);
        }
      }, 30);

      return () => clearInterval(timer);
    }
  }, [isGenerating]);

  const handleGenerate = () => {
    setIsGenerating(true);
    setGeneratedText('');
  };

  const handleCopy = () => {
    navigator.clipboard.writeText(generatedText);
  };

  return (
    <Box sx={{ width: '100%', height: '100%' }}>
      {/* 头部标题 */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <Avatar
          sx={{
            width: 48,
            height: 48,
            background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
            mr: 2,
          }}
        >
          <AutoAwesome />
        </Avatar>
        <Box sx={{ flex: 1 }}>
          <Typography variant="h6" sx={{ fontWeight: 700, color: '#1e293b' }}>
            AI智能内容创作
          </Typography>
          <Typography variant="body2" sx={{ color: '#10b981' }}>
            ● 一键生成优质内容
          </Typography>
        </Box>
        <Chip
          label="PRO"
          sx={{
            background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
            color: 'white',
            fontWeight: 700,
          }}
        />
      </Box>

      {/* 输入区域 */}
      <Box sx={{ mb: 3 }}>
        <TextField
          fullWidth
          placeholder="输入文章标题或主题..."
          variant="outlined"
          size="small"
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          sx={{
            mb: 2,
            '& .MuiOutlinedInput-root': {
              borderRadius: 2,
            },
          }}
        />
        
        {/* 关键词标签 */}
        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', mb: 2 }}>
          {keywords.map((keyword, index) => (
            <Chip
              key={index}
              label={keyword}
              size="small"
              onDelete={() => setKeywords(keywords.filter((_, i) => i !== index))}
              sx={{
                backgroundColor: '#f0fdf4',
                color: '#059669',
                '& .MuiChip-deleteIcon': {
                  color: '#059669',
                },
              }}
            />
          ))}
          <Chip
            label="+ 添加关键词"
            size="small"
            variant="outlined"
            onClick={() => {}}
            sx={{
              borderColor: '#10b981',
              color: '#10b981',
              cursor: 'pointer',
            }}
          />
        </Box>

        {/* 生成按钮 */}
        <Button
          fullWidth
          variant="contained"
          startIcon={<AutoAwesome />}
          onClick={handleGenerate}
          disabled={isGenerating}
          sx={{
            background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
            py: 1.5,
            borderRadius: 2,
            fontWeight: 600,
          }}
        >
          {isGenerating ? '正在生成中...' : '开始智能创作'}
        </Button>
      </Box>

      {/* 生成进度 */}
      {isGenerating && (
        <Box sx={{ mb: 2 }}>
          <LinearProgress 
            sx={{
              height: 6,
              borderRadius: 3,
              backgroundColor: '#f0fdf4',
              '& .MuiLinearProgress-bar': {
                background: 'linear-gradient(90deg, #10b981 0%, #059669 100%)',
                borderRadius: 3,
              },
            }}
          />
          <Typography variant="caption" sx={{ color: '#64748b', mt: 1 }}>
            正在使用AI生成优质内容...
          </Typography>
        </Box>
      )}

      {/* 生成的内容 */}
      {generatedText && (
        <Paper
          elevation={0}
          sx={{
            p: 2,
            backgroundColor: '#f8fafc',
            border: '1px solid #e2e8f0',
            borderRadius: 2,
            maxHeight: 200,
            overflow: 'auto',
            position: 'relative',
          }}
        >
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
            <Typography variant="caption" sx={{ color: '#64748b', fontWeight: 600 }}>
              生成内容
            </Typography>
            <Box>
              <IconButton size="small" onClick={handleCopy}>
                <ContentCopy sx={{ fontSize: 16 }} />
              </IconButton>
              <IconButton size="small" onClick={handleGenerate}>
                <Refresh sx={{ fontSize: 16 }} />
              </IconButton>
            </Box>
          </Box>
          <Typography
            variant="body2"
            sx={{
              color: '#1e293b',
              lineHeight: 1.8,
              whiteSpace: 'pre-wrap',
              fontFamily: 'monospace',
            }}
          >
            {generatedText}
            {isGenerating && <span className="cursor">|</span>}
          </Typography>
        </Paper>
      )}

      {/* 功能特点 */}
      <Stack spacing={1} sx={{ mt: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', p: 1.5, backgroundColor: '#f0fdf4', borderRadius: 2 }}>
          <Psychology sx={{ color: '#10b981', fontSize: 16, mr: 1 }} />
          <Typography variant="body2" sx={{ fontWeight: 500 }}>
            GPT-4 驱动，内容质量超越人工
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', alignItems: 'center', p: 1.5, backgroundColor: '#eff6ff', borderRadius: 2 }}>
          <Speed sx={{ color: '#3b82f6', fontSize: 16, mr: 1 }} />
          <Typography variant="body2" sx={{ fontWeight: 500 }}>
            10秒生成千字长文
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', alignItems: 'center', p: 1.5, backgroundColor: '#fefce8', borderRadius: 2 }}>
          <TrendingUp sx={{ color: '#f59e0b', fontSize: 16, mr: 1 }} />
          <Typography variant="body2" sx={{ fontWeight: 500 }}>
            SEO优化度达95%
          </Typography>
        </Box>
      </Stack>
    </Box>
  );
}

export default AIContentCreator;