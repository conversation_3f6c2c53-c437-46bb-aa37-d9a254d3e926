import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Avatar,
  IconButton,
  LinearProgress,
  Button,
  Paper,
} from '@mui/material';
import {
  TrendingUp,
  TrendingDown,
  Remove,
  Visibility,
  MoreVert,
  Refresh,
  Download,
  ArrowUpward,
  ArrowDownward,
} from '@mui/icons-material';

function KeywordRanking() {
  const [keywords, setKeywords] = useState([
    {
      id: 1,
      keyword: 'AI搜索优化',
      position: 3,
      change: 2,
      volume: 12500,
      difficulty: 85,
      traffic: 3280,
      url: '/ai-seo-guide',
      trend: 'up',
      platforms: {
        baidu: 2,
        google: 5,
        bing: 3,
      }
    },
    {
      id: 2,
      keyword: 'ChatGPT SEO',
      position: 7,
      change: -1,
      volume: 8900,
      difficulty: 72,
      traffic: 1560,
      url: '/chatgpt-optimization',
      trend: 'down',
      platforms: {
        baidu: 8,
        google: 6,
        bing: 9,
      }
    },
    {
      id: 3,
      keyword: '内容营销策略',
      position: 12,
      change: 0,
      volume: 6700,
      difficulty: 68,
      traffic: 890,
      url: '/content-strategy',
      trend: 'stable',
      platforms: {
        baidu: 15,
        google: 10,
        bing: 11,
      }
    },
    {
      id: 4,
      keyword: 'SEO工具推荐',
      position: 5,
      change: 3,
      volume: 15600,
      difficulty: 90,
      traffic: 4200,
      url: '/seo-tools',
      trend: 'up',
      platforms: {
        baidu: 4,
        google: 6,
        bing: 5,
      }
    },
    {
      id: 5,
      keyword: '搜索引擎营销',
      position: 18,
      change: -5,
      volume: 4500,
      difficulty: 55,
      traffic: 320,
      url: '/sem-guide',
      trend: 'down',
      platforms: {
        baidu: 22,
        google: 15,
        bing: 19,
      }
    },
  ]);

  const [animatedRows, setAnimatedRows] = useState({});

  useEffect(() => {
    // 添加动画效果
    const timer = setTimeout(() => {
      const animated = {};
      keywords.forEach(k => {
        animated[k.id] = true;
      });
      setAnimatedRows(animated);
    }, 100);
    return () => clearTimeout(timer);
  }, []);

  const getTrendIcon = (trend, change) => {
    if (change > 0) {
      return <TrendingUp sx={{ fontSize: 16, color: '#10b981' }} />;
    } else if (change < 0) {
      return <TrendingDown sx={{ fontSize: 16, color: '#ef4444' }} />;
    }
    return <Remove sx={{ fontSize: 16, color: '#6b7280' }} />;
  };

  const getPositionColor = (position) => {
    if (position <= 3) return '#10b981';
    if (position <= 10) return '#3b82f6';
    if (position <= 20) return '#f59e0b';
    return '#ef4444';
  };

  const getDifficultyColor = (difficulty) => {
    if (difficulty >= 80) return '#ef4444';
    if (difficulty >= 60) return '#f59e0b';
    if (difficulty >= 40) return '#3b82f6';
    return '#10b981';
  };

  return (
    <Box sx={{ width: '100%', height: '100%' }}>
      {/* 头部 */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <Avatar
          sx={{
            width: 48,
            height: 48,
            background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
            mr: 2,
          }}
        >
          <TrendingUp />
        </Avatar>
        <Box sx={{ flex: 1 }}>
          <Typography variant="h6" sx={{ fontWeight: 700, color: '#1e293b' }}>
            关键词排名监控
          </Typography>
          <Typography variant="body2" sx={{ color: '#3b82f6' }}>
            ● 实时追踪排名变化
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <IconButton size="small">
            <Refresh />
          </IconButton>
          <IconButton size="small">
            <Download />
          </IconButton>
        </Box>
      </Box>

      {/* 统计概览 */}
      <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
        <Paper sx={{ 
          flex: 1, 
          p: 1.5, 
          backgroundColor: '#f0fdf4',
          border: '1px solid #10b98130',
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <ArrowUpward sx={{ fontSize: 16, color: '#10b981' }} />
            <Box>
              <Typography variant="caption" sx={{ color: '#059669' }}>
                上升
              </Typography>
              <Typography variant="h6" sx={{ fontWeight: 700, color: '#059669' }}>
                12
              </Typography>
            </Box>
          </Box>
        </Paper>
        <Paper sx={{ 
          flex: 1, 
          p: 1.5,
          backgroundColor: '#fee2e2',
          border: '1px solid #ef444430',
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <ArrowDownward sx={{ fontSize: 16, color: '#ef4444' }} />
            <Box>
              <Typography variant="caption" sx={{ color: '#dc2626' }}>
                下降
              </Typography>
              <Typography variant="h6" sx={{ fontWeight: 700, color: '#dc2626' }}>
                5
              </Typography>
            </Box>
          </Box>
        </Paper>
        <Paper sx={{ 
          flex: 1, 
          p: 1.5,
          backgroundColor: '#eff6ff',
          border: '1px solid #3b82f630',
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Remove sx={{ fontSize: 16, color: '#3b82f6' }} />
            <Box>
              <Typography variant="caption" sx={{ color: '#2563eb' }}>
                稳定
              </Typography>
              <Typography variant="h6" sx={{ fontWeight: 700, color: '#2563eb' }}>
                8
              </Typography>
            </Box>
          </Box>
        </Paper>
      </Box>

      {/* 关键词列表 */}
      <TableContainer 
        component={Paper} 
        sx={{ 
          maxHeight: 320,
          '&::-webkit-scrollbar': {
            width: 8,
          },
          '&::-webkit-scrollbar-track': {
            backgroundColor: '#f1f5f9',
          },
          '&::-webkit-scrollbar-thumb': {
            backgroundColor: '#cbd5e1',
            borderRadius: 4,
          },
        }}
      >
        <Table stickyHeader size="small">
          <TableHead>
            <TableRow>
              <TableCell sx={{ fontWeight: 600, backgroundColor: '#f8fafc' }}>关键词</TableCell>
              <TableCell align="center" sx={{ fontWeight: 600, backgroundColor: '#f8fafc' }}>排名</TableCell>
              <TableCell align="center" sx={{ fontWeight: 600, backgroundColor: '#f8fafc' }}>变化</TableCell>
              <TableCell align="center" sx={{ fontWeight: 600, backgroundColor: '#f8fafc' }}>搜索量</TableCell>
              <TableCell align="center" sx={{ fontWeight: 600, backgroundColor: '#f8fafc' }}>难度</TableCell>
              <TableCell align="center" sx={{ fontWeight: 600, backgroundColor: '#f8fafc' }}>平台</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {keywords.map((row, index) => (
              <TableRow
                key={row.id}
                sx={{
                  '&:hover': {
                    backgroundColor: '#f8fafc',
                  },
                  animation: animatedRows[row.id] ? `slideIn ${0.3 + index * 0.1}s ease-out` : 'none',
                  '@keyframes slideIn': {
                    from: { opacity: 0, transform: 'translateX(-20px)' },
                    to: { opacity: 1, transform: 'translateX(0)' },
                  },
                }}
              >
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Typography variant="body2" sx={{ fontWeight: 600 }}>
                      {row.keyword}
                    </Typography>
                    <IconButton size="small" sx={{ p: 0.5 }}>
                      <Visibility sx={{ fontSize: 14 }} />
                    </IconButton>
                  </Box>
                </TableCell>
                <TableCell align="center">
                  <Chip
                    label={row.position}
                    size="small"
                    sx={{
                      backgroundColor: `${getPositionColor(row.position)}20`,
                      color: getPositionColor(row.position),
                      fontWeight: 600,
                      minWidth: 32,
                    }}
                  />
                </TableCell>
                <TableCell align="center">
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 0.5 }}>
                    {getTrendIcon(row.trend, row.change)}
                    <Typography
                      variant="caption"
                      sx={{
                        fontWeight: 600,
                        color: row.change > 0 ? '#10b981' : row.change < 0 ? '#ef4444' : '#6b7280',
                      }}
                    >
                      {row.change > 0 ? `+${row.change}` : row.change}
                    </Typography>
                  </Box>
                </TableCell>
                <TableCell align="center">
                  <Typography variant="caption" sx={{ fontWeight: 500 }}>
                    {row.volume.toLocaleString()}
                  </Typography>
                </TableCell>
                <TableCell align="center">
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <LinearProgress
                      variant="determinate"
                      value={row.difficulty}
                      sx={{
                        width: 40,
                        height: 4,
                        borderRadius: 2,
                        backgroundColor: '#e5e7eb',
                        '& .MuiLinearProgress-bar': {
                          backgroundColor: getDifficultyColor(row.difficulty),
                          borderRadius: 2,
                        },
                      }}
                    />
                    <Typography variant="caption" sx={{ fontWeight: 500 }}>
                      {row.difficulty}
                    </Typography>
                  </Box>
                </TableCell>
                <TableCell align="center">
                  <Box sx={{ display: 'flex', gap: 0.5, justifyContent: 'center' }}>
                    <Chip
                      label={`百度 ${row.platforms.baidu}`}
                      size="small"
                      sx={{
                        height: 20,
                        fontSize: '0.7rem',
                        backgroundColor: '#fee2e2',
                        color: '#dc2626',
                      }}
                    />
                    <Chip
                      label={`谷歌 ${row.platforms.google}`}
                      size="small"
                      sx={{
                        height: 20,
                        fontSize: '0.7rem',
                        backgroundColor: '#dbeafe',
                        color: '#2563eb',
                      }}
                    />
                  </Box>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* 底部操作 */}
      <Box sx={{ mt: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="caption" sx={{ color: '#64748b' }}>
          显示前5个关键词，共追踪 256 个关键词
        </Typography>
        <Button
          size="small"
          variant="text"
          sx={{ textTransform: 'none' }}
        >
          查看全部
        </Button>
      </Box>
    </Box>
  );
}

export default KeywordRanking;