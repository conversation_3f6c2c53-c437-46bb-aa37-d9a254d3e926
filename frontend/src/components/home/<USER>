import React, { useState } from 'react';
import {
  Box,
  Typography,
  Button,
  Avatar,
  Chip,
  Paper,
  Checkbox,
  FormControlLabel,
  LinearProgress,
  Stack,
  IconButton,
  Alert,
} from '@mui/material';
import {
  Publish,
  Facebook,
  Twitter,
  LinkedIn,
  Instagram,
  YouTube,
  Language,
  CheckCircle,
  Schedule,
  Send,
  Edit,
} from '@mui/icons-material';

// 自定义平台图标
const WeChatIcon = () => (
  <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
    <path d="M8.691 2.188C3.891 2.188 0 5.476 0 9.53c0 2.212 1.17 4.203 3.002 5.55a.59.59 0 0 1 .213.665l-.39 1.48c-.019.07-.048.141-.048.213 0 .163.13.295.29.295a.326.326 0 0 0 .167-.054l1.903-1.114a.864.864 0 0 1 .717-.098 10.16 10.16 0 0 0 2.837.403c.276 0 .543-.027.811-.05-.857-2.578.157-4.972 1.932-6.446 1.703-1.415 3.882-1.98 5.853-1.838-.576-3.583-4.196-6.348-8.596-6.348zM5.785 5.991c.642 0 1.162.529 1.162 1.18a1.17 1.17 0 0 1-1.162 1.178A1.17 1.17 0 0 1 4.623 7.17c0-.651.52-1.18 1.162-1.18zm5.813 0c.642 0 1.162.529 1.162 1.18a1.17 1.17 0 0 1-1.162 1.178 1.17 1.17 0 0 1-1.162-1.178c0-.651.52-1.18 1.162-1.18zm5.34 2.867c-1.797-.052-3.746.512-5.28 1.786-1.72 1.428-2.687 3.72-1.78 6.22.942 2.453 3.666 4.229 6.884 4.229.826 0 1.622-.12 2.361-.336a.722.722 0 0 1 .598.082l1.584.926a.272.272 0 0 0 .14.047c.134 0 .24-.111.24-.247 0-.06-.023-.12-.038-.177l-.327-1.233a.582.582 0 0 1-.023-.156.49.49 0 0 1 .201-.398C23.024 18.48 24 16.82 24 14.98c0-3.21-2.931-5.837-6.656-6.088V8.89c-.135-.01-.27-.027-.407-.03zm-2.53 3.274c.535 0 .969.44.969.982a.976.976 0 0 1-.969.983.976.976 0 0 1-.969-.983c0-.542.434-.982.97-.982zm4.844 0c.535 0 .969.44.969.982a.976.976 0 0 1-.969.983.976.976 0 0 1-.969-.983c0-.542.434-.982.969-.982z"/>
  </svg>
);

const WeiboIcon = () => (
  <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
    <path d="M10.098 20.323c-3.977.391-7.414-1.406-7.672-4.02-.259-2.609 2.759-5.047 6.74-5.441 3.98-.394 7.413 1.404 7.671 4.018.259 2.6-2.759 5.049-6.737 5.443h-.002zM8.05 13.602c-2.957.467-5.133 2.459-4.863 4.453.27 1.994 2.902 3.185 5.858 2.719 2.957-.469 5.133-2.461 4.862-4.455-.27-1.992-2.902-3.185-5.857-2.717zm1.431 5.266c-.915.086-1.732-.273-1.826-.803-.094-.529.581-1.047 1.496-1.135.914-.084 1.73.273 1.826.805.094.531-.583 1.045-1.496 1.133zm1.078-2.105c-.303.168-.67.078-.819-.203-.151-.279-.029-.609.271-.781.303-.166.668-.074.821.203.151.281.029.609-.273.781zm6.827-6.914c.23-2.362-1.945-4.375-4.859-4.498-2.915-.123-5.131 1.674-4.949 4.023.061.785.654 1.404 1.414 1.336.76-.066 1.306-.732 1.243-1.518-.124-1.547 1.252-2.859 3.07-2.932 1.818-.074 3.249 1.137 3.194 2.699-.027.779.608 1.438 1.39 1.402.783-.033 1.471-.783 1.496-1.512h.001z"/>
  </svg>
);

const XiaohongshuIcon = () => (
  <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
    <rect x="4" y="4" width="16" height="16" rx="2" fill="currentColor"/>
    <text x="12" y="15" fontSize="10" fill="white" textAnchor="middle" fontWeight="bold">小红书</text>
  </svg>
);

function OneClickPublish() {
  const [selectedPlatforms, setSelectedPlatforms] = useState([]);
  const [isPublishing, setIsPublishing] = useState(false);
  const [publishStatus, setPublishStatus] = useState({});
  const [autoFormat, setAutoFormat] = useState(true);
  const [scheduledPublish, setScheduledPublish] = useState(false);

  const platforms = [
    { id: 'wechat', name: '微信公众号', icon: <WeChatIcon />, color: '#07C160' },
    { id: 'weibo', name: '微博', icon: <WeiboIcon />, color: '#E6162D' },
    { id: 'xiaohongshu', name: '小红书', icon: <XiaohongshuIcon />, color: '#FF2442' },
    { id: 'facebook', name: 'Facebook', icon: <Facebook />, color: '#1877F2' },
    { id: 'twitter', name: 'Twitter', icon: <Twitter />, color: '#1DA1F2' },
    { id: 'linkedin', name: 'LinkedIn', icon: <LinkedIn />, color: '#0077B5' },
    { id: 'instagram', name: 'Instagram', icon: <Instagram />, color: '#E4405F' },
    { id: 'youtube', name: 'YouTube', icon: <YouTube />, color: '#FF0000' },
    { id: 'website', name: '企业官网', icon: <Language />, color: '#8B5CF6' },
  ];

  const handlePlatformToggle = (platformId) => {
    if (selectedPlatforms.includes(platformId)) {
      setSelectedPlatforms(selectedPlatforms.filter(id => id !== platformId));
    } else {
      setSelectedPlatforms([...selectedPlatforms, platformId]);
    }
  };

  const handlePublish = async () => {
    setIsPublishing(true);
    setPublishStatus({});

    // 模拟逐个平台发布
    for (let i = 0; i < selectedPlatforms.length; i++) {
      const platformId = selectedPlatforms[i];
      await new Promise(resolve => setTimeout(resolve, 1000));
      setPublishStatus(prev => ({
        ...prev,
        [platformId]: 'success'
      }));
    }

    setIsPublishing(false);
  };

  return (
    <Box sx={{ width: '100%', height: '100%' }}>
      {/* 头部 */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <Avatar
          sx={{
            width: 48,
            height: 48,
            background: 'linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%)',
            mr: 2,
          }}
        >
          <Publish />
        </Avatar>
        <Box sx={{ flex: 1 }}>
          <Typography variant="h6" sx={{ fontWeight: 700, color: '#1e293b' }}>
            一键发布通道
          </Typography>
          <Typography variant="body2" sx={{ color: '#8B5CF6' }}>
            ● 多平台同步发布
          </Typography>
        </Box>
        <Chip
          label="BETA"
          sx={{
            background: 'linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%)',
            color: 'white',
            fontWeight: 700,
          }}
        />
      </Box>

      {/* 文章预览 */}
      <Paper
        elevation={0}
        sx={{
          p: 2,
          mb: 3,
          backgroundColor: '#F8F9FF',
          border: '1px solid #8B5CF630',
          borderRadius: 2,
        }}
      >
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
          <Box>
            <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 0.5 }}>
              AI搜索优化完全指南
            </Typography>
            <Typography variant="caption" sx={{ color: '#64748b' }}>
              掌握AI时代的SEO策略，让您的内容被ChatGPT、Claude优先推荐...
            </Typography>
          </Box>
          <IconButton size="small">
            <Edit sx={{ fontSize: 16 }} />
          </IconButton>
        </Box>
        <Box sx={{ display: 'flex', gap: 1, mt: 1 }}>
          <Chip label="SEO" size="small" sx={{ height: 20, fontSize: '0.7rem' }} />
          <Chip label="AI营销" size="small" sx={{ height: 20, fontSize: '0.7rem' }} />
          <Chip label="内容优化" size="small" sx={{ height: 20, fontSize: '0.7rem' }} />
        </Box>
      </Paper>

      {/* 平台选择 */}
      <Typography variant="subtitle2" sx={{ mb: 2, fontWeight: 600 }}>
        选择发布平台
      </Typography>
      <Box sx={{ 
        display: 'grid', 
        gridTemplateColumns: 'repeat(3, 1fr)', 
        gap: 1.5,
        mb: 3,
      }}>
        {platforms.map((platform) => (
          <Paper
            key={platform.id}
            onClick={() => handlePlatformToggle(platform.id)}
            sx={{
              p: 1.5,
              cursor: 'pointer',
              border: selectedPlatforms.includes(platform.id) 
                ? `2px solid ${platform.color}` 
                : '2px solid transparent',
              backgroundColor: selectedPlatforms.includes(platform.id)
                ? `${platform.color}10`
                : '#fff',
              transition: 'all 0.3s ease',
              position: 'relative',
              '&:hover': {
                transform: 'translateY(-2px)',
                boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
              },
            }}
          >
            {publishStatus[platform.id] === 'success' && (
              <CheckCircle 
                sx={{ 
                  position: 'absolute',
                  top: 4,
                  right: 4,
                  fontSize: 16,
                  color: '#10b981',
                }}
              />
            )}
            <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 0.5 }}>
              <Box sx={{ color: platform.color }}>
                {platform.icon}
              </Box>
              <Typography variant="caption" sx={{ 
                fontSize: '0.7rem',
                fontWeight: selectedPlatforms.includes(platform.id) ? 600 : 400,
              }}>
                {platform.name}
              </Typography>
            </Box>
          </Paper>
        ))}
      </Box>

      {/* 发布选项 */}
      <Stack spacing={1} sx={{ mb: 3 }}>
        <FormControlLabel
          control={
            <Checkbox
              checked={autoFormat}
              onChange={(e) => setAutoFormat(e.target.checked)}
              size="small"
              sx={{ '& .MuiSvgIcon-root': { fontSize: 18 } }}
            />
          }
          label={
            <Typography variant="body2" sx={{ fontSize: '0.875rem' }}>
              自动适配各平台格式
            </Typography>
          }
        />
        <FormControlLabel
          control={
            <Checkbox
              checked={scheduledPublish}
              onChange={(e) => setScheduledPublish(e.target.checked)}
              size="small"
              sx={{ '& .MuiSvgIcon-root': { fontSize: 18 } }}
            />
          }
          label={
            <Typography variant="body2" sx={{ fontSize: '0.875rem' }}>
              定时发布 (设置时间)
            </Typography>
          }
        />
      </Stack>

      {/* 发布进度 */}
      {isPublishing && (
        <Box sx={{ mb: 2 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
            <Typography variant="caption" sx={{ color: '#64748b' }}>
              正在发布...
            </Typography>
            <Typography variant="caption" sx={{ color: '#64748b' }}>
              {Object.keys(publishStatus).length}/{selectedPlatforms.length}
            </Typography>
          </Box>
          <LinearProgress 
            variant="determinate"
            value={(Object.keys(publishStatus).length / selectedPlatforms.length) * 100}
            sx={{
              height: 6,
              borderRadius: 3,
              backgroundColor: '#f1f5f9',
              '& .MuiLinearProgress-bar': {
                background: 'linear-gradient(90deg, #8B5CF6 0%, #7C3AED 100%)',
                borderRadius: 3,
              },
            }}
          />
        </Box>
      )}

      {/* 成功提示 */}
      {!isPublishing && Object.keys(publishStatus).length > 0 && (
        <Alert 
          severity="success" 
          sx={{ mb: 2 }}
          icon={<CheckCircle />}
        >
          成功发布到 {Object.keys(publishStatus).length} 个平台
        </Alert>
      )}

      {/* 发布按钮 */}
      <Button
        fullWidth
        variant="contained"
        startIcon={isPublishing ? <Schedule /> : <Send />}
        onClick={handlePublish}
        disabled={selectedPlatforms.length === 0 || isPublishing}
        sx={{
          background: 'linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%)',
          py: 1.5,
          borderRadius: 2,
          fontWeight: 600,
        }}
      >
        {isPublishing ? '正在发布中...' : `发布到 ${selectedPlatforms.length} 个平台`}
      </Button>

      {/* 底部提示 */}
      <Typography variant="caption" sx={{ 
        display: 'block',
        textAlign: 'center',
        color: '#64748b',
        mt: 2,
      }}>
        支持自动格式转换，确保内容在各平台最佳展示
      </Typography>
    </Box>
  );
}

export default OneClickPublish;