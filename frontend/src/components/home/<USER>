﻿import React from 'react';
import {
  Box,
  Container,
  Typography,
  Paper,
  Button,
} from '@mui/material';
import { useNavigate } from 'react-router-dom';

const HelpDocumentCenter = () => {
  const navigate = useNavigate();

  return (
    <Box sx={{ py: 8, bgcolor: '#f8fafc' }}>
      <Container maxWidth='lg'>
        <Paper sx={{ p: 4, textAlign: 'center' }}>
          <Typography variant='h4' sx={{ mb: 2, color: '#1e293b' }}>
            帮助文档中心
          </Typography>
          <Typography variant='body1' sx={{ color: '#64748b', mb: 3 }}>
            这是我们的帮助文档中心，调用后端API获取实时数据。
          </Typography>
          <Button
            variant='contained'
            onClick={() => navigate('/help')}
            sx={{ textTransform: 'none' }}
          >
            访问帮助文档
          </Button>
        </Paper>
      </Container>
    </Box>
  );
};

export default HelpDocumentCenter;
