import React, { Suspense } from 'react';
import { Routes, Route } from 'react-router-dom';
import { Box, CircularProgress, Typography } from '@mui/material';
import ProtectedRoute from '../auth/ProtectedRoute';
import Layout from '../layout/Layout';
import { routes } from '../../routes/routes';

// Loading component
function LoadingFallback() {
  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '50vh',
        gap: 2,
      }}
    >
      <CircularProgress size={40} />
      <Typography variant="body2" color="text.secondary">
        正在加载页面...
      </Typography>
    </Box>
  );
}

function AppRouter() {
  return (
    <Suspense fallback={<LoadingFallback />}>
      <Routes>
        {routes.map((route, index) => {
          const { path, component: Component, element, protected: isProtected, roles, public: isPublic, standalone } = route;

          // Handle special case for Navigate element
          if (element) {
            return (
              <Route
                key={index}
                path={path}
                element={element}
              />
            );
          }

          // Standalone routes (without layout)
          if (standalone) {
            return (
              <Route
                key={index}
                path={path}
                element={
                  isProtected ? (
                    <ProtectedRoute roles={roles}>
                      <Component />
                    </ProtectedRoute>
                  ) : <Component />
                }
              />
            );
          }

          // Routes with layout
          const routeElement = (() => {
            // Public routes (no authentication required)
            if (isPublic || (!isProtected && !roles)) {
              return <Component />;
            }

            // Protected routes (authentication required)
            return (
              <ProtectedRoute roles={roles}>
                <Component />
              </ProtectedRoute>
            );
          })();

          return (
            <Route
              key={index}
              path={path}
              element={
                <Layout>
                  {routeElement}
                </Layout>
              }
            />
          );
        })}
      </Routes>
    </Suspense>
  );
}

export default AppRouter;
