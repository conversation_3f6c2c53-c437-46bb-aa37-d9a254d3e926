import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  IconButton,
  Typography,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Menu,
  MenuItem,
  Chip,
  Tooltip,
  CircularProgress,
  Alert,
  Divider,
  Paper,
  InputAdornment,
  Fade
} from '@mui/material';
import {
  Add,
  Chat,
  Delete,
  Edit,
  MoreVert,
  Search,
  History,
  SmartToy,
  Schedule,
  Message
} from '@mui/icons-material';
import ApiService from '../../services/api';

const ConversationManager = ({ 
  onConversationSelect, 
  currentConversationId,
  onNewConversation 
}) => {
  const [conversations, setConversations] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredConversations, setFilteredConversations] = useState([]);
  
  // 对话创建对话框
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [newConversationTitle, setNewConversationTitle] = useState('');
  const [creating, setCreating] = useState(false);
  
  // 菜单管理
  const [menuAnchor, setMenuAnchor] = useState(null);
  const [selectedConversation, setSelectedConversation] = useState(null);
  
  // 错误处理
  const [error, setError] = useState(null);

  // 加载对话列表
  const loadConversations = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await ApiService.get('/api/v1/conversations', {
        params: { page: 1, size: 50 }
      });
      
      if (response.data?.items) {
        setConversations(response.data.items);
        setFilteredConversations(response.data.items);
      }
    } catch (error) {
      console.error('加载对话列表失败:', error);
      setError('加载对话列表失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  }, []);

  // 搜索对话
  const handleSearch = useCallback((query) => {
    setSearchQuery(query);
    
    if (!query.trim()) {
      setFilteredConversations(conversations);
      return;
    }
    
    const filtered = conversations.filter(conv =>
      conv.title.toLowerCase().includes(query.toLowerCase())
    );
    setFilteredConversations(filtered);
  }, [conversations]);

  // 创建新对话
  const handleCreateConversation = async () => {
    if (!newConversationTitle.trim()) {
      return;
    }
    
    setCreating(true);
    
    try {
      const response = await ApiService.post('/api/v1/conversations', {
        title: newConversationTitle.trim(),
        template_parameters: {},
        knowledge_bases: []
      });
      
      const newConversation = response.data;
      setConversations(prev => [newConversation, ...prev]);
      setFilteredConversations(prev => [newConversation, ...prev]);
      
      // 选中新创建的对话
      onConversationSelect(newConversation.id);
      if (onNewConversation) {
        onNewConversation(newConversation);
      }
      
      // 关闭对话框
      setCreateDialogOpen(false);
      setNewConversationTitle('');
      
    } catch (error) {
      console.error('创建对话失败:', error);
      setError('创建对话失败，请稍后重试');
    } finally {
      setCreating(false);
    }
  };

  // 删除对话
  const handleDeleteConversation = async (conversationId) => {
    try {
      await ApiService.delete(`/api/v1/conversations/${conversationId}`);
      
      setConversations(prev => prev.filter(conv => conv.id !== conversationId));
      setFilteredConversations(prev => prev.filter(conv => conv.id !== conversationId));
      
      // 如果删除的是当前选中的对话，清除选中状态
      if (currentConversationId === conversationId) {
        onConversationSelect(null);
      }
      
    } catch (error) {
      console.error('删除对话失败:', error);
      setError('删除对话失败，请稍后重试');
    }
    
    setMenuAnchor(null);
    setSelectedConversation(null);
  };

  // 格式化时间
  const formatTime = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now - date;
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMs / 3600000);
    const diffDays = Math.floor(diffMs / 86400000);
    
    if (diffMins < 1) return '刚刚';
    if (diffMins < 60) return `${diffMins}分钟前`;
    if (diffHours < 24) return `${diffHours}小时前`;
    if (diffDays < 7) return `${diffDays}天前`;
    
    return date.toLocaleDateString();
  };

  // 处理菜单操作
  const handleMenuClick = (event, conversation) => {
    event.stopPropagation();
    setMenuAnchor(event.currentTarget);
    setSelectedConversation(conversation);
  };

  const handleMenuClose = () => {
    setMenuAnchor(null);
    setSelectedConversation(null);
  };

  // 组件挂载时加载数据
  useEffect(() => {
    loadConversations();
  }, [loadConversations]);

  return (
    <Paper 
      elevation={1} 
      sx={{ 
        width: 320, 
        height: '100%', 
        display: 'flex', 
        flexDirection: 'column',
        borderRadius: 2
      }}
    >
      {/* 头部 */}
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6" sx={{ fontWeight: 600 }}>
            对话历史
          </Typography>
          <Tooltip title="新建对话">
            <IconButton 
              onClick={() => setCreateDialogOpen(true)}
              size="small"
              sx={{ 
                bgcolor: 'primary.main', 
                color: 'white',
                '&:hover': { bgcolor: 'primary.dark' }
              }}
            >
              <Add />
            </IconButton>
          </Tooltip>
        </Box>
        
        {/* 搜索框 */}
        <TextField
          fullWidth
          size="small"
          placeholder="搜索对话..."
          value={searchQuery}
          onChange={(e) => handleSearch(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Search fontSize="small" />
              </InputAdornment>
            ),
          }}
          sx={{ mb: 1 }}
        />
        
        {/* 统计信息 */}
        <Typography variant="caption" color="text.secondary">
          共 {filteredConversations.length} 个对话
        </Typography>
      </Box>

      {/* 对话列表 */}
      <Box sx={{ flex: 1, overflow: 'auto' }}>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress size={24} />
          </Box>
        ) : error ? (
          <Box sx={{ p: 2 }}>
            <Alert severity="error" action={
              <Button size="small" onClick={loadConversations}>
                重试
              </Button>
            }>
              {error}
            </Alert>
          </Box>
        ) : filteredConversations.length === 0 ? (
          <Box sx={{ p: 3, textAlign: 'center' }}>
            <SmartToy sx={{ fontSize: 48, color: 'text.secondary', mb: 1 }} />
            <Typography variant="body2" color="text.secondary">
              {searchQuery ? '未找到匹配的对话' : '还没有对话记录'}
            </Typography>
            {!searchQuery && (
              <Button 
                startIcon={<Add />}
                onClick={() => setCreateDialogOpen(true)}
                sx={{ mt: 1 }}
              >
                创建第一个对话
              </Button>
            )}
          </Box>
        ) : (
          <List sx={{ p: 0 }}>
            {filteredConversations.map((conversation, index) => (
              <React.Fragment key={conversation.id}>
                <ListItem
                  button
                  selected={currentConversationId === conversation.id}
                  onClick={() => onConversationSelect(conversation.id)}
                  sx={{
                    py: 1.5,
                    px: 2,
                    '&.Mui-selected': {
                      bgcolor: 'primary.light',
                      '&:hover': {
                        bgcolor: 'primary.light',
                      }
                    },
                    '&:hover': {
                      bgcolor: 'action.hover'
                    }
                  }}
                >
                  <ListItemIcon sx={{ minWidth: 40 }}>
                    <Chat color={currentConversationId === conversation.id ? 'primary' : 'action'} />
                  </ListItemIcon>
                  
                  <ListItemText
                    primary={
                      <Typography 
                        variant="body2" 
                        sx={{ 
                          fontWeight: currentConversationId === conversation.id ? 600 : 400,
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap'
                        }}
                      >
                        {conversation.title}
                      </Typography>
                    }
                    secondary={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 0.5 }}>
                        <Chip 
                          icon={<Message />}
                          label={`${conversation.message_count || 0}条`}
                          size="small"
                          variant="outlined"
                          sx={{ height: 20, fontSize: '0.7rem' }}
                        />
                        <Typography variant="caption" color="text.secondary">
                          {formatTime(conversation.updated_at)}
                        </Typography>
                      </Box>
                    }
                  />
                  
                  <IconButton
                    size="small"
                    onClick={(e) => handleMenuClick(e, conversation)}
                    sx={{ opacity: 0.7, '&:hover': { opacity: 1 } }}
                  >
                    <MoreVert fontSize="small" />
                  </IconButton>
                </ListItem>
                
                {index < filteredConversations.length - 1 && (
                  <Divider variant="inset" component="li" />
                )}
              </React.Fragment>
            ))}
          </List>
        )}
      </Box>

      {/* 创建对话对话框 */}
      <Dialog 
        open={createDialogOpen} 
        onClose={() => setCreateDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>创建新对话</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            fullWidth
            label="对话标题"
            value={newConversationTitle}
            onChange={(e) => setNewConversationTitle(e.target.value)}
            placeholder="请输入对话标题..."
            sx={{ mt: 1 }}
            onKeyPress={(e) => {
              if (e.key === 'Enter' && !creating) {
                handleCreateConversation();
              }
            }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCreateDialogOpen(false)}>
            取消
          </Button>
          <Button 
            onClick={handleCreateConversation}
            disabled={!newConversationTitle.trim() || creating}
            variant="contained"
          >
            {creating ? <CircularProgress size={20} /> : '创建'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* 操作菜单 */}
      <Menu
        anchorEl={menuAnchor}
        open={Boolean(menuAnchor)}
        onClose={handleMenuClose}
        TransitionComponent={Fade}
      >
        <MenuItem onClick={() => {
          // TODO: 实现重命名功能
          handleMenuClose();
        }}>
          <Edit fontSize="small" sx={{ mr: 1 }} />
          重命名
        </MenuItem>
        <MenuItem 
          onClick={() => {
            if (selectedConversation) {
              handleDeleteConversation(selectedConversation.id);
            }
          }}
          sx={{ color: 'error.main' }}
        >
          <Delete fontSize="small" sx={{ mr: 1 }} />
          删除
        </MenuItem>
      </Menu>
    </Paper>
  );
};

export default ConversationManager;
