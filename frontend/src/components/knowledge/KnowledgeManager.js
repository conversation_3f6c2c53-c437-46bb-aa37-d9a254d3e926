import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Card,
  CardContent,
  CardActions,
  Typography,
  Button,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Grid,
  Chip,
  LinearProgress,
  Alert,
  Menu,
  MenuItem,
  Tooltip,
  CircularProgress,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  FormControlLabel,
  Checkbox,
  InputAdornment
} from '@mui/material';
import {
  Add,
  LibraryBooks,
  Description,
  Delete,
  Edit,
  MoreVert,
  Upload,
  Search,
  CheckCircle,
  Error,
  Schedule,
  Storage,
  CloudUpload,
  InsertDriveFile,
  Folder
} from '@mui/icons-material';
import ApiService from '../../services/api';

const KnowledgeManager = ({ 
  selectedKnowledgeBases = [], 
  onSelectionChange,
  mode = 'selection' // 'selection' | 'management'
}) => {
  const [knowledgeBases, setKnowledgeBases] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  
  // 创建知识库对话框
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [newKbName, setNewKbName] = useState('');
  const [newKbDescription, setNewKbDescription] = useState('');
  const [creating, setCreating] = useState(false);
  
  // 上传文档对话框
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);
  const [selectedKb, setSelectedKb] = useState(null);
  const [documentTitle, setDocumentTitle] = useState('');
  const [documentContent, setDocumentContent] = useState('');
  const [uploading, setUploading] = useState(false);
  
  // 菜单管理
  const [menuAnchor, setMenuAnchor] = useState(null);
  const [menuKb, setMenuKb] = useState(null);

  // 加载知识库列表
  const loadKnowledgeBases = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await ApiService.get('/api/v1/knowledge-bases', {
        params: { page: 1, size: 50 }
      });
      
      if (response.data?.items) {
        setKnowledgeBases(response.data.items);
      }
    } catch (error) {
      console.error('加载知识库列表失败:', error);
      setError('加载知识库列表失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  }, []);

  // 创建知识库
  const handleCreateKnowledgeBase = async () => {
    if (!newKbName.trim()) return;
    
    setCreating(true);
    
    try {
      const response = await ApiService.post('/api/v1/knowledge-bases', {
        name: newKbName.trim(),
        description: newKbDescription.trim()
      });
      
      const newKb = response.data;
      setKnowledgeBases(prev => [newKb, ...prev]);
      
      setCreateDialogOpen(false);
      setNewKbName('');
      setNewKbDescription('');
      
    } catch (error) {
      console.error('创建知识库失败:', error);
      setError('创建知识库失败，请稍后重试');
    } finally {
      setCreating(false);
    }
  };

  // 上传文档
  const handleUploadDocument = async () => {
    if (!documentTitle.trim() || !documentContent.trim()) return;
    
    setUploading(true);
    
    try {
      await ApiService.post(`/api/v1/knowledge-bases/${selectedKb.id}/documents`, {
        title: documentTitle.trim(),
        content: documentContent.trim(),
        file_type: 'text'
      });
      
      // 刷新知识库列表以更新文档数量
      loadKnowledgeBases();
      
      setUploadDialogOpen(false);
      setDocumentTitle('');
      setDocumentContent('');
      setSelectedKb(null);
      
    } catch (error) {
      console.error('上传文档失败:', error);
      setError('上传文档失败，请稍后重试');
    } finally {
      setUploading(false);
    }
  };

  // 删除知识库
  const handleDeleteKnowledgeBase = async (kbId) => {
    try {
      await ApiService.delete(`/api/v1/knowledge-bases/${kbId}`);
      setKnowledgeBases(prev => prev.filter(kb => kb.id !== kbId));
      
      // 如果删除的知识库在选中列表中，移除它
      if (selectedKnowledgeBases.includes(kbId)) {
        const newSelection = selectedKnowledgeBases.filter(id => id !== kbId);
        onSelectionChange?.(newSelection);
      }
      
    } catch (error) {
      console.error('删除知识库失败:', error);
      setError('删除知识库失败，请稍后重试');
    }
    
    setMenuAnchor(null);
    setMenuKb(null);
  };

  // 处理知识库选择
  const handleKnowledgeBaseToggle = (kbId) => {
    if (mode !== 'selection') return;
    
    const newSelection = selectedKnowledgeBases.includes(kbId)
      ? selectedKnowledgeBases.filter(id => id !== kbId)
      : [...selectedKnowledgeBases, kbId];
    
    onSelectionChange?.(newSelection);
  };

  // 获取状态颜色
  const getStatusColor = (documentCount, vectorCount) => {
    if (documentCount === 0) return 'default';
    if (vectorCount === 0) return 'warning';
    return 'success';
  };

  // 获取状态文本
  const getStatusText = (documentCount, vectorCount) => {
    if (documentCount === 0) return '空知识库';
    if (vectorCount === 0) return '处理中';
    return '就绪';
  };

  // 处理菜单操作
  const handleMenuClick = (event, kb) => {
    event.stopPropagation();
    setMenuAnchor(event.currentTarget);
    setMenuKb(kb);
  };

  const handleMenuClose = () => {
    setMenuAnchor(null);
    setMenuKb(null);
  };

  // 组件挂载时加载数据
  useEffect(() => {
    loadKnowledgeBases();
  }, [loadKnowledgeBases]);

  if (loading && knowledgeBases.length === 0) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      {/* 头部操作栏 */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">
          {mode === 'selection' ? '选择知识库' : '知识库管理'}
        </Typography>
        <Button
          startIcon={<Add />}
          variant="contained"
          onClick={() => setCreateDialogOpen(true)}
          size="small"
        >
          新建知识库
        </Button>
      </Box>

      {/* 错误提示 */}
      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* 知识库列表 */}
      {knowledgeBases.length === 0 ? (
        <Paper sx={{ p: 4, textAlign: 'center' }}>
          <LibraryBooks sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
          <Typography variant="h6" color="text.secondary" gutterBottom>
            还没有知识库
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            创建知识库来存储和管理您的专业知识
          </Typography>
          <Button
            startIcon={<Add />}
            variant="contained"
            onClick={() => setCreateDialogOpen(true)}
          >
            创建第一个知识库
          </Button>
        </Paper>
      ) : (
        <Grid container spacing={2}>
          {knowledgeBases.map((kb) => (
            <Grid item xs={12} sm={6} md={4} key={kb.id}>
              <Card 
                sx={{ 
                  height: '100%',
                  cursor: mode === 'selection' ? 'pointer' : 'default',
                  border: mode === 'selection' && selectedKnowledgeBases.includes(kb.id) 
                    ? 2 : 1,
                  borderColor: mode === 'selection' && selectedKnowledgeBases.includes(kb.id)
                    ? 'primary.main' : 'divider',
                  '&:hover': {
                    boxShadow: 3
                  }
                }}
                onClick={() => handleKnowledgeBaseToggle(kb.id)}
              >
                <CardContent sx={{ pb: 1 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      {mode === 'selection' && (
                        <Checkbox
                          checked={selectedKnowledgeBases.includes(kb.id)}
                          size="small"
                          onClick={(e) => e.stopPropagation()}
                          onChange={() => handleKnowledgeBaseToggle(kb.id)}
                        />
                      )}
                      <Folder color="primary" />
                    </Box>
                    <IconButton
                      size="small"
                      onClick={(e) => handleMenuClick(e, kb)}
                      sx={{ opacity: 0.7, '&:hover': { opacity: 1 } }}
                    >
                      <MoreVert fontSize="small" />
                    </IconButton>
                  </Box>
                  
                  <Typography variant="h6" sx={{ mb: 1, fontSize: '1rem' }}>
                    {kb.name}
                  </Typography>
                  
                  {kb.description && (
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      {kb.description}
                    </Typography>
                  )}
                  
                  <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
                    <Chip
                      icon={<Description />}
                      label={`${kb.document_count || 0}个文档`}
                      size="small"
                      variant="outlined"
                    />
                    <Chip
                      icon={<Storage />}
                      label={`${kb.vector_count || 0}个向量`}
                      size="small"
                      variant="outlined"
                    />
                  </Box>
                  
                  <Chip
                    label={getStatusText(kb.document_count, kb.vector_count)}
                    size="small"
                    color={getStatusColor(kb.document_count, kb.vector_count)}
                    icon={
                      getStatusColor(kb.document_count, kb.vector_count) === 'success' 
                        ? <CheckCircle /> 
                        : getStatusColor(kb.document_count, kb.vector_count) === 'warning'
                        ? <Schedule />
                        : <Error />
                    }
                  />
                </CardContent>
                
                <CardActions sx={{ pt: 0 }}>
                  <Button
                    size="small"
                    startIcon={<Upload />}
                    onClick={(e) => {
                      e.stopPropagation();
                      setSelectedKb(kb);
                      setUploadDialogOpen(true);
                    }}
                  >
                    上传文档
                  </Button>
                </CardActions>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}

      {/* 创建知识库对话框 */}
      <Dialog 
        open={createDialogOpen} 
        onClose={() => setCreateDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>创建知识库</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            fullWidth
            label="知识库名称"
            value={newKbName}
            onChange={(e) => setNewKbName(e.target.value)}
            sx={{ mt: 1, mb: 2 }}
          />
          <TextField
            fullWidth
            label="描述（可选）"
            value={newKbDescription}
            onChange={(e) => setNewKbDescription(e.target.value)}
            multiline
            rows={3}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCreateDialogOpen(false)}>
            取消
          </Button>
          <Button 
            onClick={handleCreateKnowledgeBase}
            disabled={!newKbName.trim() || creating}
            variant="contained"
          >
            {creating ? <CircularProgress size={20} /> : '创建'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* 上传文档对话框 */}
      <Dialog 
        open={uploadDialogOpen} 
        onClose={() => setUploadDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          上传文档到 "{selectedKb?.name}"
        </DialogTitle>
        <DialogContent>
          <TextField
            fullWidth
            label="文档标题"
            value={documentTitle}
            onChange={(e) => setDocumentTitle(e.target.value)}
            sx={{ mt: 1, mb: 2 }}
          />
          <TextField
            fullWidth
            label="文档内容"
            value={documentContent}
            onChange={(e) => setDocumentContent(e.target.value)}
            multiline
            rows={10}
            placeholder="请输入文档内容..."
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setUploadDialogOpen(false)}>
            取消
          </Button>
          <Button 
            onClick={handleUploadDocument}
            disabled={!documentTitle.trim() || !documentContent.trim() || uploading}
            variant="contained"
          >
            {uploading ? <CircularProgress size={20} /> : '上传'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* 操作菜单 */}
      <Menu
        anchorEl={menuAnchor}
        open={Boolean(menuAnchor)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => {
          // TODO: 实现编辑功能
          handleMenuClose();
        }}>
          <Edit fontSize="small" sx={{ mr: 1 }} />
          编辑
        </MenuItem>
        <MenuItem 
          onClick={() => {
            if (menuKb) {
              handleDeleteKnowledgeBase(menuKb.id);
            }
          }}
          sx={{ color: 'error.main' }}
        >
          <Delete fontSize="small" sx={{ mr: 1 }} />
          删除
        </MenuItem>
      </Menu>
    </Box>
  );
};

export default KnowledgeManager;
