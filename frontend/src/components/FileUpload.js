import React, { useState } from 'react';
import {
  Box,
  Button,
  Typography,
  LinearProgress,
  IconButton,
  Paper,
  Alert,
} from '@mui/material';
import {
  CloudUpload,
  InsertDriveFile,
  Delete,
  CheckCircle,
  Error as ErrorIcon,
} from '@mui/icons-material';
import uploadService from '../services/uploadService';

function FileUpload({
  accept = 'image/*',
  maxSize = 10 * 1024 * 1024, // 10MB
  businessType = 'document',
  onUploadSuccess,
  onUploadError,
  helperText = '支持 JPG、PNG、PDF 格式，最大 10MB',
  label = '上传文件',
  required = false,
  disabled = false,
  value = null, // 传入的已有URL
}) {
  const [file, setFile] = useState(null);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadedUrl, setUploadedUrl] = useState(value);
  const [error, setError] = useState('');
  const [preview, setPreview] = useState(null);

  // 处理文件选择
  const handleFileSelect = async (event) => {
    const selectedFile = event.target.files[0];
    if (!selectedFile) return;

    // 重置错误状态
    setError('');

    // 验证文件
    const validation = uploadService.validateFile(
      selectedFile,
      accept.split(',').map(type => type.trim()),
      maxSize
    );

    if (!validation.valid) {
      setError(validation.error);
      return;
    }

    setFile(selectedFile);

    // 如果是图片，创建预览
    if (selectedFile.type.startsWith('image/')) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setPreview(reader.result);
      };
      reader.readAsDataURL(selectedFile);
    } else {
      setPreview(null);
    }

    // 自动开始上传
    await handleUpload(selectedFile);
  };

  // 处理文件上传
  const handleUpload = async (fileToUpload) => {
    if (!fileToUpload) return;

    setUploading(true);
    setUploadProgress(0);
    setError('');

    try {
      // 模拟上传进度
      const progressInterval = setInterval(() => {
        setUploadProgress((prev) => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 200);

      const result = await uploadService.uploadFile(
        fileToUpload,
        businessType,
        null,
        true, // 设置为公开，方便审核人员查看
        null
      );

      clearInterval(progressInterval);
      setUploadProgress(100);

      // 获取上传后的URL
      const fileUrl = result.access_url || result.storage_url;
      setUploadedUrl(fileUrl);

      if (onUploadSuccess) {
        onUploadSuccess(fileUrl, fileToUpload);
      }

      setTimeout(() => {
        setUploadProgress(0);
        setUploading(false);
      }, 500);
    } catch (err) {
      console.error('上传失败:', err);
      setError(err.message || '文件上传失败');
      setUploading(false);
      setUploadProgress(0);

      if (onUploadError) {
        onUploadError(err);
      }
    }
  };

  // 删除文件
  const handleRemove = () => {
    setFile(null);
    setUploadedUrl(null);
    setPreview(null);
    setError('');
    setUploadProgress(0);
    
    if (onUploadSuccess) {
      onUploadSuccess(null, null);
    }
  };

  // 格式化文件大小
  const formatFileSize = (bytes) => {
    if (!bytes) return '';
    return uploadService.formatFileSize(bytes);
  };

  return (
    <Box>
      {/* 标签 */}
      {label && (
        <Typography variant="body2" sx={{ mb: 1, fontWeight: 500 }}>
          {label}
          {required && <span style={{ color: '#ef4444' }}> *</span>}
        </Typography>
      )}

      {/* 上传区域 */}
      {!uploadedUrl && !file && (
        <Paper
          variant="outlined"
          sx={{
            p: 3,
            textAlign: 'center',
            backgroundColor: disabled ? '#f5f5f5' : '#fafafa',
            border: '2px dashed',
            borderColor: error ? '#ef4444' : '#e0e0e0',
            cursor: disabled ? 'not-allowed' : 'pointer',
            transition: 'all 0.3s',
            '&:hover': {
              borderColor: disabled ? '#e0e0e0' : '#1976d2',
              backgroundColor: disabled ? '#f5f5f5' : '#f0f7ff',
            },
          }}
        >
          <input
            type="file"
            accept={accept}
            onChange={handleFileSelect}
            style={{ display: 'none' }}
            id="file-upload-input"
            disabled={disabled || uploading}
          />
          <label htmlFor="file-upload-input" style={{ cursor: disabled ? 'not-allowed' : 'pointer' }}>
            <CloudUpload sx={{ fontSize: 48, color: '#9e9e9e', mb: 2 }} />
            <Typography variant="body1" sx={{ mb: 1 }}>
              点击或拖拽文件到此处上传
            </Typography>
            <Typography variant="caption" color="textSecondary">
              {helperText}
            </Typography>
          </label>
        </Paper>
      )}

      {/* 文件预览/已上传状态 */}
      {(file || uploadedUrl) && (
        <Paper
          variant="outlined"
          sx={{
            p: 2,
            display: 'flex',
            alignItems: 'center',
            gap: 2,
            backgroundColor: uploadedUrl ? '#f0fdf4' : '#fafafa',
            borderColor: uploadedUrl ? '#10b981' : '#e0e0e0',
          }}
        >
          {/* 预览图或文件图标 */}
          {preview ? (
            <Box
              component="img"
              src={preview}
              sx={{
                width: 60,
                height: 60,
                objectFit: 'cover',
                borderRadius: 1,
                border: '1px solid #e0e0e0',
              }}
            />
          ) : (
            <Box
              sx={{
                width: 60,
                height: 60,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: '#f5f5f5',
                borderRadius: 1,
              }}
            >
              <InsertDriveFile sx={{ fontSize: 32, color: '#757575' }} />
            </Box>
          )}

          {/* 文件信息 */}
          <Box sx={{ flex: 1 }}>
            <Typography variant="body2" sx={{ fontWeight: 500 }}>
              {file?.name || '营业执照'}
            </Typography>
            {file && (
              <Typography variant="caption" color="textSecondary">
                {formatFileSize(file.size)}
              </Typography>
            )}
            {uploadedUrl && (
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5, mt: 0.5 }}>
                <CheckCircle sx={{ fontSize: 16, color: '#10b981' }} />
                <Typography variant="caption" sx={{ color: '#10b981' }}>
                  上传成功
                </Typography>
              </Box>
            )}
          </Box>

          {/* 删除按钮 */}
          {!uploading && (
            <IconButton
              size="small"
              onClick={handleRemove}
              disabled={disabled}
              sx={{ color: '#ef4444' }}
            >
              <Delete />
            </IconButton>
          )}
        </Paper>
      )}

      {/* 上传进度条 */}
      {uploading && (
        <Box sx={{ mt: 2 }}>
          <LinearProgress
            variant="determinate"
            value={uploadProgress}
            sx={{
              height: 6,
              borderRadius: 3,
              backgroundColor: '#e0e0e0',
              '& .MuiLinearProgress-bar': {
                borderRadius: 3,
                backgroundColor: '#1976d2',
              },
            }}
          />
          <Typography variant="caption" color="textSecondary" sx={{ mt: 0.5 }}>
            上传中... {uploadProgress}%
          </Typography>
        </Box>
      )}

      {/* 错误提示 */}
      {error && (
        <Alert severity="error" sx={{ mt: 2 }} onClose={() => setError('')}>
          {error}
        </Alert>
      )}

      {/* 重新上传按钮（当有已上传文件时） */}
      {uploadedUrl && !uploading && (
        <Box sx={{ mt: 2 }}>
          <input
            type="file"
            accept={accept}
            onChange={handleFileSelect}
            style={{ display: 'none' }}
            id="file-reupload-input"
            disabled={disabled}
          />
          <label htmlFor="file-reupload-input">
            <Button
              variant="outlined"
              component="span"
              startIcon={<CloudUpload />}
              disabled={disabled}
              size="small"
            >
              重新上传
            </Button>
          </label>
        </Box>
      )}
    </Box>
  );
}

export default FileUpload;