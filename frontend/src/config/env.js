/**
 * 环境配置管理
 * 统一管理所有环境变量，提供类型安全和默认值
 */

// 应用基本配置
export const APP_CONFIG = {
  name: process.env.REACT_APP_APP_NAME || 'AI搜索优化平台',
  version: process.env.REACT_APP_APP_VERSION || '1.0.0',
  description: process.env.REACT_APP_APP_DESCRIPTION || '专业的AI搜索引擎优化平台',
  env: process.env.REACT_APP_ENV || 'development',
  debug: process.env.REACT_APP_DEBUG === 'true',
};

// API 配置
export const API_CONFIG = {
  baseURL: process.env.REACT_APP_API_BASE_URL || 'http://localhost:8000',
  version: process.env.REACT_APP_API_VERSION || 'v1',
  timeout: parseInt(process.env.REACT_APP_API_TIMEOUT) || 30000,
  wsURL: process.env.REACT_APP_WS_URL || 'ws://localhost:8000/ws',
};

// 获取完整的API URL
export const getApiUrl = (endpoint = '') => {
  const baseUrl = API_CONFIG.baseURL.replace(/\/$/, '');
  const version = API_CONFIG.version;
  const cleanEndpoint = endpoint.replace(/^\//, '');

  if (cleanEndpoint) {
    return `${baseUrl}/api/${version}/${cleanEndpoint}`;
  }
  return `${baseUrl}/api/${version}`;
};

// 文件上传配置
export const FILE_CONFIG = {
  maxSize: parseInt(process.env.REACT_APP_MAX_FILE_SIZE) || 10485760, // 10MB
  allowedTypes: process.env.REACT_APP_ALLOWED_FILE_TYPES?.split(',') || ['.pdf', '.doc', '.docx', '.txt', '.md'],
};

// 分页配置
export const PAGINATION_CONFIG = {
  defaultPageSize: parseInt(process.env.REACT_APP_DEFAULT_PAGE_SIZE) || 20,
  maxPageSize: parseInt(process.env.REACT_APP_MAX_PAGE_SIZE) || 100,
};

// 缓存配置
export const CACHE_CONFIG = {
  duration: parseInt(process.env.REACT_APP_CACHE_DURATION) || 300000, // 5分钟
};

// 第三方服务配置
export const THIRD_PARTY_CONFIG = {
  googleAnalytics: {
    trackingId: process.env.REACT_APP_GA_TRACKING_ID,
    enabled: process.env.REACT_APP_ENABLE_ANALYTICS === 'true',
  },
  sentry: {
    dsn: process.env.REACT_APP_SENTRY_DSN,
    enabled: process.env.REACT_APP_ENABLE_ERROR_REPORTING === 'true',
  },
  map: {
    apiKey: process.env.REACT_APP_MAP_API_KEY,
  },
  socialLogin: {
    google: {
      clientId: process.env.REACT_APP_GOOGLE_CLIENT_ID,
    },
    github: {
      clientId: process.env.REACT_APP_GITHUB_CLIENT_ID,
    },
    enabled: process.env.REACT_APP_ENABLE_SOCIAL_LOGIN === 'true',
  },
};

// 功能开关
export const FEATURE_FLAGS = {
  analytics: process.env.REACT_APP_ENABLE_ANALYTICS === 'true',
  errorReporting: process.env.REACT_APP_ENABLE_ERROR_REPORTING === 'true',
  socialLogin: process.env.REACT_APP_ENABLE_SOCIAL_LOGIN === 'true',
  darkMode: process.env.REACT_APP_ENABLE_DARK_MODE !== 'false', // 默认启用
  devTools: process.env.REACT_APP_SHOW_DEV_TOOLS === 'true',
  reduxDevTools: process.env.REACT_APP_ENABLE_REDUX_DEVTOOLS === 'true',
};

// 安全配置
export const SECURITY_CONFIG = {
  enableHttps: process.env.REACT_APP_ENABLE_HTTPS === 'true',
  csrfTokenName: process.env.REACT_APP_CSRF_TOKEN_NAME || 'csrftoken',
};

// CDN 配置
export const CDN_CONFIG = {
  url: process.env.REACT_APP_CDN_URL,
  assetsVersion: process.env.REACT_APP_ASSETS_VERSION || '1.0.0',
};

// 环境检查工具
export const isProduction = () => APP_CONFIG.env === 'production';
export const isDevelopment = () => APP_CONFIG.env === 'development';
export const isStaging = () => APP_CONFIG.env === 'staging';

// 调试工具 - 仅在开发环境输出
export const debugLog = (...args) => {
  if (APP_CONFIG.debug && process.env.NODE_ENV === 'development') {
    console.log('[DEBUG]', ...args);
  }
};

// 配置验证
export const validateConfig = () => {
  const errors = [];
  
  if (!API_CONFIG.baseURL) {
    errors.push('API_CONFIG.baseURL is required');
  }
  
  if (FEATURE_FLAGS.analytics && !THIRD_PARTY_CONFIG.googleAnalytics.trackingId) {
    errors.push('Google Analytics tracking ID is required when analytics is enabled');
  }
  
  if (FEATURE_FLAGS.errorReporting && !THIRD_PARTY_CONFIG.sentry.dsn) {
    errors.push('Sentry DSN is required when error reporting is enabled');
  }
  
  if (errors.length > 0 && process.env.NODE_ENV === 'development') {
    console.warn('Configuration validation errors:', errors);
  }
  
  return errors.length === 0;
};

// 导出所有配置
export default {
  APP_CONFIG,
  API_CONFIG,
  FILE_CONFIG,
  PAGINATION_CONFIG,
  CACHE_CONFIG,
  THIRD_PARTY_CONFIG,
  FEATURE_FLAGS,
  SECURITY_CONFIG,
  CDN_CONFIG,
  getApiUrl,
  isProduction,
  isDevelopment,
  isStaging,
  debugLog,
  validateConfig,
};
