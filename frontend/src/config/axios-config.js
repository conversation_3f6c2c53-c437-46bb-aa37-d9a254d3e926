import axios from 'axios';
import { ApiConfig } from './api-config';

// Create axios instance with default config
const axiosInstance = axios.create({
  baseURL: ApiConfig.baseURL,
  timeout: ApiConfig.timeout,
  headers: ApiConfig.defaultHeaders,
});

// Request interceptor
axiosInstance.interceptors.request.use(
  (config) => {
    // Add auth token to headers
    let token = localStorage.getItem(`${ApiConfig.storage.prefix}${ApiConfig.storage.tokenKey}`);

    // 如果没有找到，尝试使用其他可能的键名
    if (!token) {
      token = localStorage.getItem('token');
    }
    if (!token) {
      token = localStorage.getItem('ai_seo_auth_token'); // 实际使用的键名
    }



    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // Add API version to URL only if baseURL doesn't already include it
    if (!config.url.includes('/api/') && !config.baseURL?.includes('/api/')) {
      config.url = `/api/${ApiConfig.version}${config.url}`;
    }



    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
axiosInstance.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;
    
    // Handle 401 errors (unauthorized)
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;
      
      // Try to refresh token
      try {
        let refreshToken = localStorage.getItem(`${ApiConfig.storage.prefix}refresh_token`);
        if (!refreshToken) {
          refreshToken = localStorage.getItem('ai_seo_refresh_token'); // 实际使用的键名
        }
        if (refreshToken) {
          const response = await axios.post(
            `${ApiConfig.baseURL}/api/${ApiConfig.version}${ApiConfig.endpoints.auth.refresh}`,
            { refresh_token: refreshToken }
          );
          
          // Save new token
          const { access_token } = response.data;
          localStorage.setItem(`${ApiConfig.storage.prefix}${ApiConfig.storage.tokenKey}`, access_token);
          
          // Retry original request with new token
          originalRequest.headers.Authorization = `Bearer ${access_token}`;
          return axiosInstance(originalRequest);
        }
      } catch (refreshError) {
        // Refresh failed, redirect to login
        localStorage.removeItem(`${ApiConfig.storage.prefix}${ApiConfig.storage.tokenKey}`);
        localStorage.removeItem(`${ApiConfig.storage.prefix}refresh_token`);
        localStorage.removeItem(`${ApiConfig.storage.prefix}${ApiConfig.storage.userKey}`);
        window.location.href = '/#/auth/login';
        return Promise.reject(refreshError);
      }
    }
    
    // Handle other errors
    if (error.response) {
      // Server responded with error status
      const errorMessage = error.response.data?.detail || 
                          error.response.data?.message || 
                          ApiConfig.errorCodes[error.response.status] || 
                          '请求失败';
      
      // Create a more user-friendly error
      const customError = new Error(errorMessage);
      customError.status = error.response.status;
      customError.data = error.response.data;
      return Promise.reject(customError);
    } else if (error.request) {
      // Request was made but no response received
      const customError = new Error('网络连接失败，请检查网络');
      customError.status = 0;
      return Promise.reject(customError);
    } else {
      // Something else happened
      return Promise.reject(error);
    }
  }
);

export default axiosInstance;