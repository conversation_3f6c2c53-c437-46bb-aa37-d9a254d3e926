// API Configuration
export const ApiConfig = {
  // Base URL for API requests
  baseURL: process.env.NODE_ENV === 'production'
    ? 'https://geo.web.webideploy.com'
    : 'http://localhost:8000',  // 开发环境直接使用8000端口
  
  // API version
  version: 'v1',
  
  // Request timeout (in milliseconds)
  timeout: 30000,
  
  // Default headers
  defaultHeaders: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
  
  // API endpoints
  endpoints: {
    // Authentication endpoints
    auth: {
      login: '/auth/login',
      logout: '/auth/logout',
      register: '/auth/register',
      refresh: '/auth/refresh',
      verify: '/auth/verify',
      resetPassword: '/auth/reset-password',
      changePassword: '/auth/change-password',
      sendVerificationCode: '/auth/send-verification-code',
      verifyCode: '/auth/verify-code',
    },
    
    // User management endpoints
    users: {
      profile: '/users/profile',
      update: '/users/profile',
      list: '/users',
      create: '/users',
      delete: '/users',
      roles: '/users/roles',
    },
    
    // Enterprise management endpoints
    enterprises: {
      list: '/enterprises',
      create: '/enterprises',
      update: '/enterprises',
      delete: '/enterprises',
      details: '/enterprises',
      members: '/enterprises/members',
    },

    // Company management endpoints
    companies: {
      list: '/companies',
      create: '/companies',
      update: '/companies',
      delete: '/companies',
      details: '/companies',
      pending: '/companies/pending',
      verify: '/companies/verify',
    },

    // Agent management endpoints
    agents: {
      list: '/agents',
      create: '/agents',
      update: '/agents',
      delete: '/agents',
      details: '/agents',
      pending: '/agents/pending',
      verify: '/agents/verify',
      referralLinks: '/agents/referral-links',
      updateReferralLink: '/agents/referral-links',
      deleteReferralLink: '/agents/referral-links',
      toggleReferralLinkStatus: '/agents/referral-links',
    },
    
    // Channel management endpoints
    channels: {
      list: '/channels',
      create: '/channels',
      update: '/channels',
      delete: '/channels',
      details: '/channels',
      stats: '/channels/stats',
      content: '/channels/content',
      pending: '/channels/pending',
      verify: '/channels/verify',
      // 渠道商专用接口
      profile: '/channels/profile',
      me: '/channels/me',
      withdraw: '/channels/withdraw',
      requirements: '/channels/requirements',
      tasks: '/channels/tasks',
    },

    // Channel categories endpoints (渠道分类管理)
    channelCategories: {
      list: '/channel-categories/list',
      create: '/channel-categories',
      update: '/channel-categories',
      delete: '/channel-categories',
    },

    // Channel category mappings endpoints (用户渠道服务管理)
    channelCategoryMappings: {
      list: '/channel-category-mappings',
      create: '/channel-category-mappings',
      update: '/channel-category-mappings',
      delete: '/channel-category-mappings',
    },

    // Channel services endpoints (系统服务管理)
    channelServices: {
      list: '/channel-services',
      create: '/channel-services',
      update: '/channel-services',
      delete: '/channel-services',
      details: '/channel-services',
      pending: '/channel-services/pending',
      approval: '/channel-services',
    },


    
    // Content management endpoints
    content: {
      list: '/content',
      create: '/content',
      update: '/content',
      delete: '/content',
      details: '/content',
      publish: '/content/publish',
      unpublish: '/content/unpublish',
    },
    
    // AI service endpoints
    ai: {
      generate: '/ai/content/generate',
      optimize: '/ai/content/optimize',
      analyze: '/ai/keywords/analyze',
      suggestions: '/ai/suggestions',
      templates: '/ai/templates',
    },
    
    // Order and billing endpoints
    orders: {
      list: '/orders',
      create: '/orders',
      details: '/orders',
      payment: '/orders/payment',
      invoice: '/orders/invoice',
    },

    // Subscription management endpoints
    subscriptions: {
      list: '/subscriptions',
      current: '/subscriptions/current',
      create: '/subscriptions',
      details: '/subscriptions',
      cancel: '/subscriptions/cancel',
      renew: '/subscriptions/renew',
      upgrade: '/subscriptions/upgrade',
      downgrade: '/subscriptions/downgrade',
      statistics: '/subscriptions/statistics',
      plans: '/subscriptions/plans',
      plansManagement: '/subscriptions/plans/management',
    },
    
    // Analytics endpoints
    analytics: {
      dashboard: '/analytics/dashboard',
      traffic: '/analytics/traffic',
      performance: '/analytics/performance',
      revenue: '/analytics/revenue',
    },
    
    // System management endpoints
    system: {
      settings: '/system/settings',
      logs: '/system/logs',
      health: '/system/health',
      backup: '/system/backup',
    },
    
    // Documentation management endpoints
    documentation: {
      categories: {
        list: '/admin/help/categories',
        create: '/admin/help/categories',
        update: '/admin/help/categories',
        delete: '/admin/help/categories',
        sort: '/admin/help/categories/sort',
      },
      documents: {
        list: '/admin/help/documents',
        create: '/admin/help/documents',
        update: '/admin/help/documents',
        delete: '/admin/help/documents',
        get: '/admin/help/documents',
        search: '/admin/help/documents/search',
        popular: '/admin/help/documents/popular',
        feedback: '/admin/help/documents',
      },
    },
    
    // Announcement management endpoints
    announcements: {
      list: '/announcements',
      create: '/announcements',
      update: '/announcements',
      delete: '/announcements',
      get: '/announcements',
      stats: '/announcements/stats',
      publish: '/announcements/publish',
      markRead: '/announcements/mark-read',
      dismiss: '/announcements/dismiss',
      popup: '/announcements/popup',
    },
  },
  
  // Error codes and messages
  errorCodes: {
    400: '请求参数错误',
    401: '未授权访问',
    403: '权限不足',
    404: '资源不存在',
    422: '数据验证失败',
    429: '请求过于频繁',
    500: '服务器内部错误',
    502: '网关错误',
    503: '服务不可用',
    504: '网关超时',
  },
  
  // Request retry configuration
  retry: {
    attempts: 3,
    delay: 1000,
    backoff: 2,
  },
  
  // File upload configuration
  upload: {
    maxSize: 10 * 1024 * 1024, // 10MB
    allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'],
    endpoint: '/upload',
  },
  
  // Pagination defaults
  pagination: {
    defaultPage: 1,
    defaultPageSize: 20,
    maxPageSize: 100,
  },
  
  // Cache configuration
  cache: {
    enabled: true,
    ttl: 5 * 60 * 1000, // 5 minutes
  },

  // Storage configuration
  storage: {
    prefix: 'geo_app_',
    tokenKey: 'token',
    userKey: 'user',
    refreshTokenKey: 'refresh_token',
  },
};

// Helper function to build full API URL
export function buildApiUrl(endpoint, params = {}) {
  let url = `${ApiConfig.baseURL}/api/${ApiConfig.version}${endpoint}`;
  
  // Add query parameters
  const queryParams = new URLSearchParams();
  Object.keys(params).forEach(key => {
    if (params[key] !== null && params[key] !== undefined) {
      queryParams.append(key, params[key]);
    }
  });
  
  if (queryParams.toString()) {
    url += `?${queryParams.toString()}`;
  }
  
  return url;
}

// Helper function to get endpoint by path
export function getEndpoint(path) {
  const pathParts = path.split('.');
  let endpoint = ApiConfig.endpoints;
  
  for (const part of pathParts) {
    if (endpoint[part]) {
      endpoint = endpoint[part];
    } else {
      throw new Error(`Endpoint not found: ${path}`);
    }
  }
  
  return endpoint;
}

// Export API endpoints as a flat object for easier access
export const API_ENDPOINTS = {
  // Announcements
  ANNOUNCEMENTS: '/api/v1/announcements',
  ANNOUNCEMENTS_STATS: '/api/v1/announcements/stats',
  ANNOUNCEMENTS_PUBLISH: '/api/v1/announcements/publish',
  ANNOUNCEMENTS_MARK_READ: '/api/v1/announcements/mark-read',
  ANNOUNCEMENTS_DISMISS: '/api/v1/announcements/dismiss',
  ANNOUNCEMENTS_POPUP: '/api/v1/announcements/popup',
};

// Export base URL for direct use
export const API_BASE_URL = ApiConfig.baseURL;

export default ApiConfig;
