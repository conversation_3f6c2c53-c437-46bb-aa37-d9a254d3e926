// Application Configuration
export const AppConfig = {
  // Application information
  app: {
    name: 'AI搜索引擎优化平台',
    version: '1.0.0',
    description: 'AI-powered SEO optimization platform',
    author: 'AI SEO Team',
  },
  
  // Environment configuration
  env: {
    development: process.env.NODE_ENV === 'development',
    production: process.env.NODE_ENV === 'production',
    testing: process.env.NODE_ENV === 'test',
  },
  
  // Feature flags
  features: {
    enableAnalytics: true,
    enableNotifications: true,
    enableDarkMode: true,
    enableOfflineMode: false,
    enableBetaFeatures: false,
  },
  
  // UI configuration
  ui: {
    theme: 'light', // 'light' | 'dark' | 'auto'
    language: 'zh-CN',
    dateFormat: 'YYYY-MM-DD',
    timeFormat: 'HH:mm:ss',
    currency: 'CNY',
    timezone: 'Asia/Shanghai',
  },
  
  // Layout configuration
  layout: {
    sidebarWidth: 280,
    headerHeight: 64,
    footerHeight: 48,
    contentPadding: 24,
  },
  
  // Routing configuration
  routing: {
    mode: 'hash', // 'hash' | 'history'
    baseUrl: '/',
    defaultRoute: '/dashboard',
    loginRoute: '/auth/login',
    errorRoute: '/error',
  },
  
  // User roles and permissions
  roles: {
    super_admin: {
      name: '超级管理员',
      permissions: ['*'], // All permissions
    },
    admin: {
      name: '管理员',
      permissions: [
        'admin.*',
        'user.manage',
        'content.manage',
        'finance.manage',
      ],
    },
    regular_user: {
      name: '普通用户',
      permissions: [
        'dashboard.view',
        'profile.manage',
        'content.view',
        'role.apply',
      ],
    },
  },
  
  // Navigation menus by role
  navigation: {
    super_admin: [
      { path: '/super-admin/dashboard', title: '超级管理员控制台', icon: 'Dashboard' },
      { path: '/super-admin/users', title: '用户管理', icon: 'People' },
      { path: '/super-admin/roles', title: '角色管理', icon: 'Security' },
      { path: '/super-admin/role-permissions', title: '角色权限管理', icon: 'Assignment' },
      { path: '/super-admin/permissions', title: '权限管理', icon: 'VpnKey' },
      { path: '/super-admin/commission', title: '佣金设置', icon: 'MonetizationOn' },
      { path: '/super-admin/system-config', title: '系统配置', icon: 'Settings' },
    ],
    admin: [
      { path: '/admin/dashboard', title: '管理员控制台', icon: 'Dashboard' },
      { 
        title: '用户管理', 
        icon: 'People',
        children: [
          { path: '/admin/users', title: '用户列表', icon: 'Person' },
          { path: '/admin/roles', title: '角色审批', icon: 'Assignment' },
        ]
      },
      {
        title: '订阅管理',
        icon: 'WorkspacePremium',
        children: [
          { path: '/admin/products', title: '套餐管理', icon: 'ShoppingCart' },
          { path: '/admin/subscriptions', title: '订阅列表', icon: 'CardMembership' },
        ]
      },
      {
        title: '渠道管理',
        icon: 'Store',
        children: [
          { path: '/admin/channel-categories', title: '渠道分类管理', icon: 'Category' },
          { path: '/admin/services', title: '渠道实体管理', icon: 'Settings' },
          { path: '/admin/service-approval', title: '服务审批', icon: 'Assignment' },
        ]
      },
      {
        title: '稿件中心',
        icon: 'Article',
        children: [
          { path: '/admin/content', title: '接单中心', icon: 'Assignment' },
          { path: '/admin/tasks', title: '稿件任务', icon: 'Task' },
          { path: '/admin/content-service', title: '内容服务管理', icon: 'Work' },
        ]
      },
      { 
        title: '交易管理', 
        icon: 'AccountBalance',
        children: [
          { path: '/admin/orders', title: '订单中心', icon: 'ShoppingCart' },
          { path: '/admin/payments', title: '支付中心', icon: 'Payment' },
        ]
      },
      { 
        title: '平台运营', 
        icon: 'Business',
        children: [
          { path: '/admin/analytics', title: '数据分析中心', icon: 'Analytics' },
          { path: '/admin/status-monitor', title: '状态监控中心', icon: 'Analytics' },
        ]
      },
      { 
        title: '平台设置', 
        icon: 'Description',
        children: [
          { path: '/admin/platform-integration', title: '平台集成管理', icon: 'Settings' },
          { path: '/admin/announcements', title: '公告管理', icon: 'Announcement' },
          { path: '/admin/documentation', title: '帮助文档管理', icon: 'Help' },
        ]
      },
    ],
    regular_user: [
      { path: '/user/dashboard', title: '用户控制台', icon: 'Dashboard' },
      { path: '/user/profile', title: '个人资料', icon: 'Person' },
      { path: '/user/subscriptions', title: '我的订阅', icon: 'Payment' },
      { path: '/user/role-application', title: '角色申请', icon: 'Work' },
      { path: '/user/application-records', title: '申请记录', icon: 'Assignment' },
    ],
  },
  
  // Storage configuration
  storage: {
    prefix: 'ai_seo_',
    tokenKey: 'auth_token',
    userKey: 'user_data',
    settingsKey: 'user_settings',
    cacheKey: 'api_cache',
  },
  
  // Notification configuration
  notifications: {
    position: 'top-right',
    duration: 5000,
    maxVisible: 5,
  },
  
  // Performance configuration
  performance: {
    enableLazyLoading: true,
    enableCodeSplitting: true,
    enableServiceWorker: false,
    cacheStrategy: 'cache-first',
  },
  
  // Security configuration
  security: {
    enableCSRF: true,
    enableXSS: true,
    tokenRefreshThreshold: 5 * 60 * 1000, // 5 minutes
  },
  
  // Get navigation menu for user role
  getNavigationForRole(role) {
    return this.navigation[role] || this.navigation.regular_user;
  },
  
  // Check if user has permission
  hasPermission(userRole, permission) {
    const role = this.roles[userRole];
    if (!role) return false;
    
    // Admin has all permissions
    if (role.permissions.includes('*')) return true;
    
    // Check specific permission
    return role.permissions.includes(permission);
  },
};

export default AppConfig;
