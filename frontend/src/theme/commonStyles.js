// Common styles and utilities for consistent UI

export const cardStyles = {
  // Enhanced card with gradient background and hover effects
  enhanced: (theme) => ({
    borderRadius: 3,
    border: '1px solid',
    borderColor: 'divider',
    background: theme.palette.mode === 'dark'
      ? 'linear-gradient(145deg, rgba(30, 41, 59, 0.8), rgba(51, 65, 85, 0.4))'
      : 'linear-gradient(145deg, #ffffff, #f8fafc)',
    transition: 'all 0.3s ease',
    position: 'relative',
    overflow: 'hidden',
    '&:hover': {
      transform: 'translateY(-4px)',
      boxShadow: '0 12px 24px rgba(0, 0, 0, 0.1)',
      borderColor: 'primary.main',
    },
  }),

  // Card with top accent border
  withAccent: (color = 'primary') => ({
    borderRadius: 3,
    border: '1px solid',
    borderColor: 'divider',
    transition: 'all 0.3s ease',
    position: 'relative',
    overflow: 'hidden',
    '&::before': {
      content: '""',
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      height: '4px',
      background: `linear-gradient(90deg, var(--mui-palette-${color}-main), var(--mui-palette-${color}-dark))`,
    },
    '&:hover': {
      transform: 'translateY(-2px)',
      boxShadow: '0 8px 16px rgba(0, 0, 0, 0.1)',
      borderColor: `${color}.main`,
    },
  }),

  // Glassmorphism card
  glass: (theme) => ({
    borderRadius: 4,
    border: '1px solid',
    borderColor: 'divider',
    backgroundColor: theme.palette.mode === 'dark'
      ? 'rgba(30, 41, 59, 0.8)'
      : 'rgba(255, 255, 255, 0.9)',
    backdropFilter: 'blur(10px)',
    boxShadow: theme.palette.mode === 'dark'
      ? '0 25px 50px rgba(0, 0, 0, 0.5)'
      : '0 25px 50px rgba(0, 0, 0, 0.1)',
    transition: 'all 0.3s ease',
    '&:hover': {
      transform: 'translateY(-2px)',
      boxShadow: theme.palette.mode === 'dark'
        ? '0 30px 60px rgba(0, 0, 0, 0.6)'
        : '0 30px 60px rgba(0, 0, 0, 0.15)',
    },
  }),
};

export const buttonStyles = {
  // Primary gradient button
  gradient: {
    background: 'linear-gradient(45deg, #2563eb, #0891b2)',
    color: 'white',
    fontWeight: 600,
    borderRadius: 3,
    px: 4,
    py: 1.5,
    boxShadow: '0 4px 16px rgba(37, 99, 235, 0.3)',
    transition: 'all 0.3s ease',
    '&:hover': {
      background: 'linear-gradient(45deg, #1d4ed8, #0e7490)',
      transform: 'translateY(-2px)',
      boxShadow: '0 8px 24px rgba(37, 99, 235, 0.4)',
    },
  },

  // Outlined button with hover effects
  outlined: {
    borderWidth: 2,
    borderRadius: 3,
    fontWeight: 600,
    px: 4,
    py: 1.5,
    transition: 'all 0.3s ease',
    '&:hover': {
      transform: 'translateY(-1px)',
      boxShadow: '0 4px 16px rgba(37, 99, 235, 0.2)',
    },
  },
};

export const avatarStyles = {
  // Large avatar with shadow
  large: {
    width: 100,
    height: 100,
    fontSize: '2.5rem',
    fontWeight: 600,
    boxShadow: '0 8px 32px rgba(37, 99, 235, 0.3)',
    border: '4px solid',
    borderColor: 'background.paper',
  },

  // Medium avatar with shadow
  medium: {
    width: 56,
    height: 56,
    fontSize: '1.5rem',
    fontWeight: 600,
    boxShadow: '0 4px 16px rgba(37, 99, 235, 0.2)',
  },

  // Small avatar
  small: {
    width: 40,
    height: 40,
    fontSize: '1rem',
    fontWeight: 500,
  },
};

export const gradientText = {
  primary: {
    background: 'linear-gradient(45deg, #2563eb, #0891b2)',
    backgroundClip: 'text',
    WebkitBackgroundClip: 'text',
    WebkitTextFillColor: 'transparent',
    fontWeight: 800,
  },
  
  secondary: {
    background: 'linear-gradient(45deg, #0891b2, #10b981)',
    backgroundClip: 'text',
    WebkitBackgroundClip: 'text',
    WebkitTextFillColor: 'transparent',
    fontWeight: 800,
  },
};

export const animations = {
  // Fade in animation
  fadeIn: {
    '@keyframes fadeIn': {
      from: { opacity: 0, transform: 'translateY(20px)' },
      to: { opacity: 1, transform: 'translateY(0)' },
    },
    animation: 'fadeIn 0.6s ease-out',
  },

  // Slide in from left
  slideInLeft: {
    '@keyframes slideInLeft': {
      from: { opacity: 0, transform: 'translateX(-30px)' },
      to: { opacity: 1, transform: 'translateX(0)' },
    },
    animation: 'slideInLeft 0.6s ease-out',
  },

  // Pulse animation
  pulse: {
    '@keyframes pulse': {
      '0%': { transform: 'scale(1)' },
      '50%': { transform: 'scale(1.05)' },
      '100%': { transform: 'scale(1)' },
    },
    animation: 'pulse 2s infinite',
  },
};

export const backgroundPatterns = {
  // Grid pattern
  grid: (opacity = 0.5) => ({
    backgroundImage: `url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><defs><pattern id='grid' width='20' height='20' patternUnits='userSpaceOnUse'><path d='M 20 0 L 0 0 0 20' fill='none' stroke='rgba(37,99,235,0.1)' stroke-width='0.5'/></pattern></defs><rect width='100' height='100' fill='url(%23grid)'/></svg>")`,
    opacity,
  }),

  // Dots pattern
  dots: (opacity = 0.3) => ({
    backgroundImage: `url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><defs><pattern id='dots' width='20' height='20' patternUnits='userSpaceOnUse'><circle cx='10' cy='10' r='1' fill='rgba(37,99,235,0.2)'/></pattern></defs><rect width='100' height='100' fill='url(%23dots)'/></svg>")`,
    opacity,
  }),
};

export default {
  cardStyles,
  buttonStyles,
  avatarStyles,
  gradientText,
  animations,
  backgroundPatterns,
};
