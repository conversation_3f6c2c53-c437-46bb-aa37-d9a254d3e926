<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
    <title>Loading...</title>
    <script type="text/javascript">
      // 单页应用的404处理脚本
      // 将路径保存到sessionStorage，然后重定向到首页
      var pathSegmentsToKeep = 0;

      var l = window.location;
      var redirectPath = l.pathname.split('/').slice(0, 1 + pathSegmentsToKeep).join('/') + '/?/' +
            l.pathname.slice(1).split('/').slice(pathSegmentsToKeep).join('/').replace(/&/g, '~and~') +
            (l.search ? '&' + l.search.slice(1).replace(/&/g, '~and~') : '') +
            l.hash;

      // 保存原始路径
      sessionStorage.setItem('redirect', redirectPath);
      sessionStorage.setItem('originalPath', l.pathname + l.search + l.hash);
      
      // 重定向到首页
      l.replace(l.protocol + '//' + l.hostname + (l.port ? ':' + l.port : '') + '/');
    </script>
  </head>
  <body>
  </body>
</html>