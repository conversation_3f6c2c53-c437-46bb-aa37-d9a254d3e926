/**
 * Playwright 全局设置
 * 在所有测试开始前执行的设置操作
 */

const { chromium } = require('@playwright/test');

async function globalSetup(config) {
  console.log('🚀 开始全局测试设置...');
  
  // 启动浏览器进行初始化
  const browser = await chromium.launch();
  const context = await browser.newContext();
  const page = await context.newPage();
  
  try {
    // 等待前端服务启动
    console.log('⏳ 等待前端服务启动...');
    await waitForService('http://localhost:3000', 60000);
    
    // 等待后端服务启动
    console.log('⏳ 等待后端服务启动...');
    await waitForService('http://localhost:8000/docs', 60000);
    
    // 执行数据库初始化（如果需要）
    await initializeTestData(page);
    
    console.log('✅ 全局测试设置完成');
    
  } catch (error) {
    console.error('❌ 全局设置失败:', error);
    throw error;
  } finally {
    await browser.close();
  }
}

/**
 * 等待服务启动
 */
async function waitForService(url, timeout = 30000) {
  const startTime = Date.now();
  
  while (Date.now() - startTime < timeout) {
    try {
      const response = await fetch(url);
      if (response.ok || response.status === 404) {
        console.log(`✅ 服务 ${url} 已启动`);
        return;
      }
    } catch (error) {
      // 服务还未启动，继续等待
    }
    
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  throw new Error(`服务 ${url} 启动超时`);
}

/**
 * 初始化测试数据
 */
async function initializeTestData(page) {
  console.log('📝 初始化测试数据...');
  
  try {
    // 访问前端页面确保服务正常
    await page.goto('http://localhost:3000');
    await page.waitForLoadState('networkidle');
    
    // 这里可以添加更多的测试数据初始化逻辑
    // 例如：创建测试用户、初始化测试项目等
    
    console.log('✅ 测试数据初始化完成');
  } catch (error) {
    console.warn('⚠️ 测试数据初始化失败:', error.message);
    // 不抛出错误，允许测试继续进行
  }
}

module.exports = globalSetup;
