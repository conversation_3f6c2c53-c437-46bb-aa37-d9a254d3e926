const { test, expect } = require('@playwright/test');

// 测试配置
const BASE_URL = process.env.REACT_APP_URL || 'http://localhost:3000';
const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

// 测试用户信息
const TEST_USER = {
  email: '<EMAIL>',
  password: '123456'
};

test.describe('知识库管理功能测试', () => {
  
  test.beforeEach(async ({ page }) => {
    // 访问前端页面
    await page.goto('/');
    
    // 等待页面加载
    await page.waitForLoadState('networkidle');
    
    // 检查是否需要登录
    const loginButton = page.locator('button:has-text("登录")');
    if (await loginButton.isVisible()) {
      console.log('需要登录，开始登录流程...');
      await performLogin(page);
    }
  });

  test('应该能够访问知识库管理页面', async ({ page }) => {
    // 导航到企业控制中心
    await page.click('text=企业控制中心');
    await page.waitForLoadState('networkidle');
    
    // 点击知识库管理
    await page.click('text=知识库管理');
    await page.waitForLoadState('networkidle');
    
    // 验证页面标题
    await expect(page.locator('h5:has-text("知识库管理")')).toBeVisible();
    
    // 验证新建知识库按钮
    await expect(page.locator('button:has-text("新建知识库")')).toBeVisible();
    
    console.log('✅ 知识库管理页面访问成功');
  });

  test('应该能够创建新的知识库', async ({ page }) => {
    // 导航到知识库管理页面
    await navigateToKnowledgeManagement(page);
    
    // 点击新建知识库按钮
    await page.click('button:has-text("新建知识库")');
    
    // 等待对话框出现
    await expect(page.locator('text=新建知识库')).toBeVisible();
    
    // 填写知识库信息
    const knowledgeBaseName = `测试知识库_${Date.now()}`;
    await page.fill('input[name="name"]', knowledgeBaseName);
    await page.fill('textarea[name="description"]', '这是一个用于Playwright测试的知识库');
    
    // 点击创建按钮
    await page.click('button:has-text("创建")');
    
    // 等待创建成功的提示
    await expect(page.locator('text=知识库创建成功')).toBeVisible({ timeout: 10000 });
    
    console.log(`✅ 知识库创建成功: ${knowledgeBaseName}`);
  });

  test('应该能够查看知识库列表和统计信息', async ({ page }) => {
    // 导航到知识库管理页面
    await navigateToKnowledgeManagement(page);
    
    // 等待页面加载完成
    await page.waitForTimeout(2000);
    
    // 验证统计信息显示
    await expect(page.locator('text=知识库总数')).toBeVisible();
    await expect(page.locator('text=文档总数')).toBeVisible();
    await expect(page.locator('text=向量数量')).toBeVisible();
    
    // 验证搜索框
    await expect(page.locator('input[placeholder*="搜索"]')).toBeVisible();
    
    console.log('✅ 知识库列表和统计信息显示正常');
  });

  test('应该能够选择知识库并查看详情', async ({ page }) => {
    // 导航到知识库管理页面
    await navigateToKnowledgeManagement(page);
    
    // 等待知识库列表加载
    await page.waitForTimeout(3000);
    
    // 查找知识库列表项
    const knowledgeBaseItems = page.locator('[role="button"]').filter({ hasText: /测试|知识库|文档/ });
    const count = await knowledgeBaseItems.count();
    
    if (count > 0) {
      // 点击第一个知识库
      await knowledgeBaseItems.first().click();
      
      // 验证右侧显示知识库详情
      await expect(page.locator('text=文件列表')).toBeVisible();
      
      // 验证上传文件按钮可用
      await expect(page.locator('button:has-text("上传文件")')).toBeVisible();
      
      console.log('✅ 知识库详情显示正常');
    } else {
      console.log('⚠️ 没有找到知识库，跳过详情测试');
    }
  });

  test('应该能够打开文件上传对话框', async ({ page }) => {
    // 导航到知识库管理页面
    await navigateToKnowledgeManagement(page);
    
    // 等待知识库列表加载
    await page.waitForTimeout(3000);
    
    // 选择第一个知识库
    const knowledgeBaseItems = page.locator('[role="button"]').filter({ hasText: /测试|知识库|文档/ });
    const count = await knowledgeBaseItems.count();
    
    if (count > 0) {
      await knowledgeBaseItems.first().click();
      
      // 点击上传文件按钮
      await page.click('button:has-text("上传文件")');
      
      // 验证上传对话框出现
      await expect(page.locator('text=上传文件')).toBeVisible();
      
      // 验证文件上传界面
      await expect(page.locator('text=点击或拖拽文件到这里')).toBeVisible();
      
      // 关闭对话框
      await page.click('button:has-text("取消")');
      
      console.log('✅ 文件上传对话框功能正常');
    } else {
      console.log('⚠️ 没有找到知识库，跳过上传测试');
    }
  });

  test('应该能够搜索知识库', async ({ page }) => {
    // 导航到知识库管理页面
    await navigateToKnowledgeManagement(page);
    
    // 等待知识库列表加载
    await page.waitForTimeout(3000);
    
    // 在搜索框中输入搜索词
    const searchInput = page.locator('input[placeholder*="搜索"]');
    await searchInput.fill('测试');
    
    // 等待搜索结果
    await page.waitForTimeout(1000);
    
    console.log('✅ 知识库搜索功能正常');
  });

  test('应该能够测试API接口连通性', async ({ page }) => {
    // 导航到知识库管理页面
    await navigateToKnowledgeManagement(page);
    
    // 监听网络请求
    const apiRequests = [];
    page.on('request', request => {
      if (request.url().includes('/api/v1/knowledge-bases')) {
        apiRequests.push({
          url: request.url(),
          method: request.method(),
          headers: request.headers()
        });
      }
    });
    
    // 等待API请求
    await page.waitForTimeout(5000);
    
    // 验证API请求
    if (apiRequests.length > 0) {
      console.log('✅ API接口连通性测试通过');
      console.log(`发现 ${apiRequests.length} 个API请求:`);
      apiRequests.forEach((req, index) => {
        console.log(`  ${index + 1}. ${req.method} ${req.url}`);
      });
    } else {
      console.log('⚠️ 未检测到API请求，可能存在连接问题');
    }
  });

  test('应该能够处理API错误情况', async ({ page }) => {
    // 导航到知识库管理页面
    await navigateToKnowledgeManagement(page);
    
    // 监听控制台错误
    const consoleErrors = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text());
      }
    });
    
    // 等待页面完全加载
    await page.waitForTimeout(5000);
    
    // 检查是否有严重错误
    const criticalErrors = consoleErrors.filter(error => 
      error.includes('Failed to fetch') || 
      error.includes('Network Error') ||
      error.includes('500') ||
      error.includes('401')
    );
    
    if (criticalErrors.length === 0) {
      console.log('✅ 没有发现严重的API错误');
    } else {
      console.log('⚠️ 发现API错误:');
      criticalErrors.forEach(error => console.log(`  - ${error}`));
    }
  });
});

// 辅助函数：执行登录
async function performLogin(page) {
  try {
    // 查找登录表单
    const emailInput = page.locator('input[type="email"], input[name="email"]');
    const passwordInput = page.locator('input[type="password"], input[name="password"]');
    
    if (await emailInput.isVisible() && await passwordInput.isVisible()) {
      // 填写登录信息
      await emailInput.fill(TEST_USER.email);
      await passwordInput.fill(TEST_USER.password);
      
      // 点击登录按钮
      await page.click('button:has-text("登录")');
      
      // 等待登录成功
      await page.waitForLoadState('networkidle');
      
      console.log('✅ 登录成功');
    } else {
      console.log('⚠️ 未找到登录表单，可能已经登录');
    }
  } catch (error) {
    console.error('❌ 登录失败:', error);
    throw error;
  }
}

// 辅助函数：导航到知识库管理页面
async function navigateToKnowledgeManagement(page) {
  try {
    // 查找企业控制中心链接
    const enterpriseLink = page.locator('text=企业控制中心, a[href*="enterprise"]');
    
    if (await enterpriseLink.isVisible()) {
      await enterpriseLink.click();
    } else {
      // 尝试直接访问
      await page.goto('/enterprise');
    }
    
    await page.waitForLoadState('networkidle');
    
    // 点击知识库管理
    const knowledgeLink = page.locator('text=知识库管理, a[href*="knowledge"]');
    
    if (await knowledgeLink.isVisible()) {
      await knowledgeLink.click();
    } else {
      // 尝试直接访问
      await page.goto('/enterprise/knowledge-management');
    }
    
    await page.waitForLoadState('networkidle');
    
    console.log('✅ 已导航到知识库管理页面');
  } catch (error) {
    console.error('❌ 导航失败:', error);
    throw error;
  }
}
