/**
 * AI模板功能测试
 * 验证删除服务类型、分类、公开模板字段后的功能是否正常
 */

const { test, expect } = require('@playwright/test');

// 测试配置
const BASE_URL = 'http://localhost:3000';
const API_BASE_URL = 'http://localhost:8000';
const TEST_USER = {
  username: '<EMAIL>',
  password: '123456'
};

test.describe('AI模板功能测试', () => {

  test.beforeEach(async ({ page }) => {
    // 登录用户
    await loginUser(page);
  });

  test('应该能够创建AI模板（不包含已删除字段）', async ({ page }) => {
    // 导航到模板管理页面
    await page.goto(`${BASE_URL}/#/control-center/enterprise`);
    await page.waitForLoadState('networkidle');

    // 查找并点击模板管理相关的菜单或按钮
    // 这里需要根据实际的UI结构来定位元素
    const templateButton = page.locator('text=模板').or(page.locator('text=写作模板')).or(page.locator('[data-testid="template-menu"]'));
    if (await templateButton.count() > 0) {
      await templateButton.first().click();
    }

    // 等待模板页面加载
    await page.waitForTimeout(2000);

    // 点击创建模板按钮
    const createButton = page.locator('text=创建模板').or(page.locator('text=新建模板')).or(page.locator('[data-testid="create-template"]'));
    if (await createButton.count() > 0) {
      await createButton.first().click();
    }

    // 等待创建对话框打开
    await page.waitForTimeout(1000);

    // 填写模板信息（只包含保留的字段）
    const templateName = `测试模板_${Date.now()}`;
    
    // 填写模板名称
    const nameInput = page.locator('input[name="template_name"]').or(page.locator('label:has-text("模板名称") + * input'));
    if (await nameInput.count() > 0) {
      await nameInput.fill(templateName);
    }

    // 填写模板描述
    const descInput = page.locator('textarea[name="template_description"]').or(page.locator('label:has-text("模板描述") + * textarea'));
    if (await descInput.count() > 0) {
      await descInput.fill('这是一个测试模板，用于验证删除字段后的功能');
    }

    // 填写提示词模板
    const promptInput = page.locator('textarea[name="prompt_template"]').or(page.locator('label:has-text("模板内容") + * textarea'));
    if (await promptInput.count() > 0) {
      await promptInput.fill('请根据以下要求生成内容：{requirements}');
    }

    // 验证已删除的字段不再显示
    await expect(page.locator('text=服务类型')).toHaveCount(0);
    await expect(page.locator('text=分类')).toHaveCount(0);
    await expect(page.locator('text=公开模板')).toHaveCount(0);

    // 提交表单
    const submitButton = page.locator('button[type="submit"]').or(page.locator('text=创建模板')).or(page.locator('text=保存'));
    if (await submitButton.count() > 0) {
      await submitButton.last().click();
    }

    // 等待创建完成
    await page.waitForTimeout(2000);

    // 验证创建成功（可能显示成功消息或返回列表页面）
    const successMessage = page.locator('text=创建成功').or(page.locator('text=保存成功'));
    if (await successMessage.count() > 0) {
      await expect(successMessage.first()).toBeVisible();
    }

    console.log('✅ AI模板创建测试完成');
  });

  test('应该能够查看AI模板列表（不显示已删除字段）', async ({ page }) => {
    // 导航到模板管理页面
    await page.goto(`${BASE_URL}/#/control-center/enterprise`);
    await page.waitForLoadState('networkidle');

    // 查找并点击模板管理相关的菜单或按钮
    const templateButton = page.locator('text=模板').or(page.locator('text=写作模板'));
    if (await templateButton.count() > 0) {
      await templateButton.first().click();
    }

    // 等待模板列表加载
    await page.waitForTimeout(2000);

    // 验证页面不显示已删除的字段相关的筛选器
    await expect(page.locator('text=服务类型筛选')).toHaveCount(0);
    await expect(page.locator('text=分类筛选')).toHaveCount(0);
    await expect(page.locator('text=公开状态筛选')).toHaveCount(0);

    // 验证模板卡片不显示已删除的字段
    const templateCards = page.locator('[data-testid="template-card"]').or(page.locator('.template-card'));
    if (await templateCards.count() > 0) {
      const firstCard = templateCards.first();
      
      // 验证不显示服务类型标签
      await expect(firstCard.locator('text=内容生成')).toHaveCount(0);
      await expect(firstCard.locator('text=内容优化')).toHaveCount(0);
      
      // 验证不显示分类标签
      await expect(firstCard.locator('text=营销文案')).toHaveCount(0);
      await expect(firstCard.locator('text=内容创作')).toHaveCount(0);
      
      // 验证不显示公开/私有标签
      await expect(firstCard.locator('text=私有')).toHaveCount(0);
    }

    console.log('✅ AI模板列表测试完成');
  });

  test('应该能够编辑AI模板（不包含已删除字段）', async ({ page }) => {
    // 导航到模板管理页面
    await page.goto(`${BASE_URL}/#/control-center/enterprise`);
    await page.waitForLoadState('networkidle');

    // 查找并点击模板管理相关的菜单或按钮
    const templateButton = page.locator('text=模板').or(page.locator('text=写作模板'));
    if (await templateButton.count() > 0) {
      await templateButton.first().click();
    }

    // 等待模板列表加载
    await page.waitForTimeout(2000);

    // 查找编辑按钮并点击
    const editButton = page.locator('text=编辑').or(page.locator('[data-testid="edit-template"]')).or(page.locator('button:has-text("编辑")'));
    if (await editButton.count() > 0) {
      await editButton.first().click();
    }

    // 等待编辑对话框打开
    await page.waitForTimeout(1000);

    // 验证编辑表单不包含已删除的字段
    await expect(page.locator('label:has-text("服务类型")')).toHaveCount(0);
    await expect(page.locator('label:has-text("分类")')).toHaveCount(0);
    await expect(page.locator('text=公开模板')).toHaveCount(0);

    // 修改模板名称
    const nameInput = page.locator('input[name="template_name"]').or(page.locator('label:has-text("模板名称") + * input'));
    if (await nameInput.count() > 0) {
      await nameInput.fill(`编辑后的模板_${Date.now()}`);
    }

    // 保存修改
    const saveButton = page.locator('button[type="submit"]').or(page.locator('text=保存')).or(page.locator('text=更新'));
    if (await saveButton.count() > 0) {
      await saveButton.last().click();
    }

    // 等待保存完成
    await page.waitForTimeout(2000);

    console.log('✅ AI模板编辑测试完成');
  });

  test('应该能够测试API接口（不包含已删除字段）', async ({ page }) => {
    // 设置API拦截器来验证请求格式
    let apiRequest = null;
    
    page.route('**/api/v1/ai/templates**', async (route) => {
      apiRequest = {
        url: route.request().url(),
        method: route.request().method(),
        postData: route.request().postData()
      };
      
      // 继续请求
      await route.continue();
    });

    // 导航到模板管理页面并尝试创建模板
    await page.goto(`${BASE_URL}/#/control-center/enterprise`);
    await page.waitForLoadState('networkidle');

    // 模拟创建模板的API调用
    const response = await page.evaluate(async () => {
      try {
        const token = localStorage.getItem('ai_seo_auth_token') || localStorage.getItem('auth_token');
        const response = await fetch('http://localhost:8000/api/v1/ai/templates', {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });
        
        return {
          status: response.status,
          ok: response.ok,
          data: response.ok ? await response.json() : null
        };
      } catch (error) {
        return {
          status: 0,
          ok: false,
          error: error.message
        };
      }
    });

    // 验证API响应
    if (response.ok) {
      console.log('✅ API接口调用成功');
      console.log('API响应状态:', response.status);
      
      // 验证响应数据不包含已删除的字段
      if (response.data && response.data.data && response.data.data.items) {
        const templates = response.data.data.items;
        templates.forEach((template, index) => {
          // 验证模板对象不包含已删除的字段
          expect(template).not.toHaveProperty('service_type');
          expect(template).not.toHaveProperty('category');
          expect(template).not.toHaveProperty('is_public');
          
          console.log(`✅ 模板 ${index + 1} 字段验证通过`);
        });
      }
    } else {
      console.log('⚠️ API接口调用失败:', response.status, response.error);
    }

    console.log('✅ API接口测试完成');
  });

});

// 登录函数
async function loginUser(page) {
  console.log('开始登录流程...');

  // 导航到登录页面
  await page.goto(`${BASE_URL}/#/login`);

  // 等待登录表单加载
  await page.waitForSelector('input[type="email"], input[name="email"], input[name="username"]', { timeout: 10000 });

  // 填写登录信息
  const emailInput = page.locator('input[type="email"], input[name="email"], input[name="username"]').first();
  await emailInput.fill(TEST_USER.username);

  const passwordInput = page.locator('input[type="password"], input[name="password"]').first();
  await passwordInput.fill(TEST_USER.password);

  // 点击登录按钮
  const loginButton = page.locator('button[type="submit"], button:has-text("登录"), button:has-text("Login")').first();
  await loginButton.click();

  // 等待登录完成
  await page.waitForTimeout(3000);

  // 验证登录成功（检查是否跳转到主页面或显示用户信息）
  try {
    await page.waitForSelector('text=控制中心', { timeout: 5000 });
    console.log('✅ 登录成功');
  } catch (error) {
    console.log('⚠️ 登录可能失败，继续执行测试...');
  }
}
