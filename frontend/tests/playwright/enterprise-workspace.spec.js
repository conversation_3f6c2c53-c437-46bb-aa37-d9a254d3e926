/**
 * 企业控制中心工作空间卡片测试
 * 测试工作空间卡片的查看功能和项目详细信息接口对接
 */

const { test, expect } = require('@playwright/test');

// 测试配置
const BASE_URL = 'http://localhost:3000';
const API_BASE_URL = 'http://localhost:8000';

// 测试用户登录信息
const TEST_USER = {
  username: '<EMAIL>',
  password: '123456'
};

// 模拟项目数据
const MOCK_PROJECT = {
  id: 'test-project-123',
  project_name: '测试监控项目',
  project_description: '这是一个用于测试的监控项目',
  target_website: 'https://example.com',
  target_brand: '测试品牌',
  keywords: ['SEO', '搜索优化', 'AI'],
  search_engines: ['doubao', 'chatgpt', 'claude'],
  monitoring_frequency: 'daily',
  status: 'active',
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z'
};

test.describe('企业控制中心工作空间测试', () => {

  test.beforeEach(async ({ page }) => {
    // 首先登录
    await loginUser(page);

    // 设置API拦截器
    await setupApiInterceptors(page);

    // 导航到企业控制中心
    await page.goto(`${BASE_URL}/#/control-center/enterprise`);

    // 等待页面加载
    await page.waitForLoadState('networkidle');
  });

  test('应该显示工作空间管理器', async ({ page }) => {
    // 点击工作空间管理菜单项
    await page.click('[data-testid="workspace-manager-menu"]');
    
    // 等待工作空间管理器加载
    await page.waitForSelector('[data-testid="workspace-manager"]', { timeout: 10000 });
    
    // 验证页面标题
    await expect(page.locator('h4')).toContainText('工作空间管理器');
  });

  test('应该显示项目卡片列表', async ({ page }) => {
    // 导航到工作空间管理器
    await navigateToWorkspaceManager(page);
    
    // 等待项目卡片加载
    await page.waitForSelector('[data-testid="project-card"]', { timeout: 10000 });
    
    // 验证项目卡片存在
    const projectCards = await page.locator('[data-testid="project-card"]').count();
    expect(projectCards).toBeGreaterThan(0);
  });

  test('应该能够点击查看按钮并调用详情接口', async ({ page }) => {
    // 设置API响应拦截
    let apiCalled = false;
    let projectId = null;

    await page.route(`${API_BASE_URL}/api/v1/monitoring/projects/*`, async route => {
      const url = route.request().url();
      const matches = url.match(/\/monitoring\/projects\/([^\/]+)$/);
      if (matches && route.request().method() === 'GET') {
        apiCalled = true;
        projectId = matches[1];
        console.log('API调用 - 项目ID:', projectId);

        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            success: true,
            data: {
              ...MOCK_PROJECT,
              id: projectId
            },
            message: '获取项目详情成功'
          })
        });
      } else {
        await route.continue();
      }
    });

    // 导航到工作空间管理器
    await navigateToWorkspaceManager(page);

    // 等待项目卡片加载
    await page.waitForSelector('[data-testid="project-card"]', { timeout: 10000 });

    // 点击第一个项目卡片的查看按钮
    const viewButton = page.locator('[data-testid="view-project-button"]').first();
    await viewButton.click();

    // 等待API调用完成
    await page.waitForTimeout(2000);

    // 验证API被调用
    expect(apiCalled).toBe(true);
    expect(projectId).toBeTruthy();
    console.log('测试完成 - 项目ID:', projectId);
  });

  test('应该正确处理API响应并显示项目详情', async ({ page }) => {
    // 设置详情API响应
    await page.route(`${API_BASE_URL}/api/v1/monitoring/projects/${MOCK_PROJECT.id}`, async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          data: MOCK_PROJECT,
          message: '获取项目详情成功'
        })
      });
    });

    // 导航到工作空间管理器
    await navigateToWorkspaceManager(page);
    
    // 点击查看按钮
    await page.click('[data-testid="view-project-button"]');
    
    // 验证成功消息显示
    await expect(page.locator('.MuiSnackbar-root')).toContainText('查看项目: 测试监控项目');
  });

  test('应该处理API错误响应', async ({ page }) => {
    // 设置错误API响应
    await page.route(`${API_BASE_URL}/api/v1/monitoring/projects/*`, async route => {
      await route.fulfill({
        status: 404,
        contentType: 'application/json',
        body: JSON.stringify({
          success: false,
          message: '项目不存在'
        })
      });
    });

    // 导航到工作空间管理器
    await navigateToWorkspaceManager(page);
    
    // 点击查看按钮
    await page.click('[data-testid="view-project-button"]');
    
    // 验证错误处理
    await expect(page.locator('.MuiSnackbar-root')).toContainText('项目不存在');
  });

  test('应该验证项目卡片的基本信息显示', async ({ page }) => {
    // 导航到工作空间管理器
    await navigateToWorkspaceManager(page);
    
    // 等待项目卡片加载
    await page.waitForSelector('[data-testid="project-card"]', { timeout: 10000 });
    
    const firstCard = page.locator('[data-testid="project-card"]').first();
    
    // 验证项目名称显示
    await expect(firstCard.locator('[data-testid="project-name"]')).toBeVisible();
    
    // 验证项目描述显示
    await expect(firstCard.locator('[data-testid="project-description"]')).toBeVisible();
    
    // 验证项目状态显示
    await expect(firstCard.locator('[data-testid="project-status"]')).toBeVisible();
    
    // 验证操作按钮显示
    await expect(firstCard.locator('[data-testid="view-project-button"]')).toBeVisible();
    await expect(firstCard.locator('[data-testid="edit-project-button"]')).toBeVisible();
  });

  test('应该支持项目搜索功能', async ({ page }) => {
    // 导航到工作空间管理器
    await navigateToWorkspaceManager(page);

    // 等待搜索框加载
    await page.waitForSelector('[data-testid="project-search"]', { timeout: 10000 });

    // 输入搜索关键词
    await page.fill('[data-testid="project-search"]', '测试');

    // 等待搜索结果
    await page.waitForTimeout(500);

    // 验证搜索结果
    const visibleCards = await page.locator('[data-testid="project-card"]:visible').count();
    expect(visibleCards).toBeGreaterThanOrEqual(0);
  });

  test('真实API测试 - 调用后端监控项目详情接口', async ({ page }) => {
    // 不设置API拦截器，直接测试真实后端接口
    console.log('开始真实API测试...');

    // 监听网络请求
    const requests = [];
    page.on('request', request => {
      if (request.url().includes('/api/v1/monitoring/projects/')) {
        requests.push({
          url: request.url(),
          method: request.method(),
          headers: request.headers()
        });
        console.log('捕获到API请求:', request.url());
      }
    });

    // 监听网络响应
    page.on('response', response => {
      if (response.url().includes('/api/v1/monitoring/projects/')) {
        console.log('API响应状态:', response.status());
        console.log('API响应URL:', response.url());
      }
    });

    // 导航到工作空间管理器
    await navigateToWorkspaceManager(page);

    // 等待项目卡片加载
    await page.waitForSelector('[data-testid="project-card"]', { timeout: 15000 });

    // 获取第一个项目卡片
    const firstCard = page.locator('[data-testid="project-card"]').first();
    await expect(firstCard).toBeVisible();

    // 点击查看按钮
    const viewButton = firstCard.locator('[data-testid="view-project-button"]');
    await expect(viewButton).toBeVisible();
    await viewButton.click();

    // 等待API调用和响应
    await page.waitForTimeout(3000);

    // 验证API请求被发送
    expect(requests.length).toBeGreaterThan(0);

    // 验证请求格式
    const apiRequest = requests[0];
    expect(apiRequest.url).toMatch(/\/api\/v1\/monitoring\/projects\/[^\/]+$/);
    expect(apiRequest.method).toBe('GET');

    console.log('真实API测试完成');
    console.log('API请求详情:', apiRequest);
  });

});

// 登录函数
async function loginUser(page) {
  console.log('开始登录流程...');

  // 导航到登录页面
  await page.goto(`${BASE_URL}/#/login`);

  // 等待登录表单加载
  await page.waitForSelector('input[type="email"], input[name="email"], input[name="username"]', { timeout: 10000 });

  // 填写登录信息
  const emailInput = page.locator('input[type="email"], input[name="email"], input[name="username"]').first();
  await emailInput.fill(TEST_USER.username);

  const passwordInput = page.locator('input[type="password"], input[name="password"]').first();
  await passwordInput.fill(TEST_USER.password);

  // 点击登录按钮
  const loginButton = page.locator('button[type="submit"], button:has-text("登录"), button:has-text("Login")').first();
  await loginButton.click();

  // 等待登录完成，检查是否跳转到主页或控制中心
  await page.waitForURL(/\/#\/(dashboard|control-center)/, { timeout: 15000 });

  console.log('登录成功');
}

// 辅助函数
async function setupApiInterceptors(page) {
  // 拦截项目列表API
  await page.route(`${API_BASE_URL}/api/v1/monitoring/projects`, async route => {
    await route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify({
        success: true,
        data: {
          items: [MOCK_PROJECT],
          pagination: {
            page: 1,
            size: 20,
            total: 1,
            pages: 1
          }
        },
        message: '获取项目列表成功'
      })
    });
  });
}

async function navigateToWorkspaceManager(page) {
  // 点击工作空间管理菜单项
  await page.click('[data-testid="workspace-manager-menu"]');
  
  // 等待工作空间管理器加载
  await page.waitForSelector('[data-testid="workspace-manager"]', { timeout: 10000 });
}
