#!/usr/bin/env node

/**
 * Playwright 测试运行脚本
 * 用于运行企业控制中心工作空间测试
 */

const { spawn } = require('child_process');
const path = require('path');

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logHeader(message) {
  log('\n' + '='.repeat(60), 'cyan');
  log(`  ${message}`, 'bright');
  log('='.repeat(60), 'cyan');
}

function logStep(step, message) {
  log(`\n[${step}] ${message}`, 'blue');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

async function runCommand(command, args = [], options = {}) {
  return new Promise((resolve, reject) => {
    const child = spawn(command, args, {
      stdio: 'inherit',
      shell: true,
      ...options
    });

    child.on('close', (code) => {
      if (code === 0) {
        resolve(code);
      } else {
        reject(new Error(`Command failed with exit code ${code}`));
      }
    });

    child.on('error', (error) => {
      reject(error);
    });
  });
}

async function checkPrerequisites() {
  logStep('1', '检查前置条件...');
  
  try {
    // 检查 Node.js 版本
    await runCommand('node', ['--version']);
    logSuccess('Node.js 已安装');
    
    // 检查 npm 版本
    await runCommand('npm', ['--version']);
    logSuccess('npm 已安装');
    
    // 检查 Playwright 是否安装
    try {
      await runCommand('npx', ['playwright', '--version']);
      logSuccess('Playwright 已安装');
    } catch (error) {
      logWarning('Playwright 未安装，正在安装...');
      await runCommand('npm', ['install', '@playwright/test']);
      await runCommand('npx', ['playwright', 'install']);
      logSuccess('Playwright 安装完成');
    }
    
  } catch (error) {
    logError(`前置条件检查失败: ${error.message}`);
    throw error;
  }
}

async function installDependencies() {
  logStep('2', '安装依赖...');
  
  try {
    await runCommand('npm', ['install']);
    logSuccess('依赖安装完成');
  } catch (error) {
    logError(`依赖安装失败: ${error.message}`);
    throw error;
  }
}

async function runTests() {
  logStep('3', '运行 Playwright 测试...');

  log('\n测试配置信息:', 'cyan');
  log('- 测试账户: <EMAIL>', 'blue');
  log('- 测试密码: 123456', 'blue');
  log('- 前端地址: http://localhost:3000', 'blue');
  log('- 后端地址: http://localhost:8000', 'blue');
  log('- 测试接口: GET /api/v1/monitoring/projects/{project_id}', 'blue');

  try {
    // 运行特定的企业工作空间测试
    await runCommand('npx', [
      'playwright',
      'test',
      'tests/playwright/enterprise-workspace.spec.js',
      '--reporter=list',
      '--headed' // 显示浏览器界面以便观察测试过程
    ]);
    logSuccess('测试运行完成');

    log('\n测试内容验证:', 'cyan');
    log('✅ 用户登录功能 (<EMAIL>)', 'green');
    log('✅ 企业控制中心页面加载', 'green');
    log('✅ 工作空间管理器显示', 'green');
    log('✅ 项目卡片列表显示', 'green');
    log('✅ 查看按钮点击功能', 'green');
    log('✅ API接口调用测试', 'green');
    log('✅ 项目详情对话框显示', 'green');
    log('✅ 前后端联通验证', 'green');

  } catch (error) {
    logError(`测试运行失败: ${error.message}`);
    log('\n可能的问题排查:', 'yellow');
    log('1. 检查前端服务是否启动: npm start', 'yellow');
    log('2. 检查后端服务是否启动: python main.py', 'yellow');
    log('3. 检查测试账户是否存在: <EMAIL>', 'yellow');
    log('4. 检查API接口是否实现: /api/v1/monitoring/projects/{id}', 'yellow');
    throw error;
  }
}

async function generateReport() {
  logStep('4', '生成测试报告...');
  
  try {
    await runCommand('npx', ['playwright', 'show-report']);
    logSuccess('测试报告已生成');
  } catch (error) {
    logWarning(`报告生成失败: ${error.message}`);
  }
}

async function main() {
  try {
    logHeader('企业控制中心工作空间 Playwright 测试');
    
    await checkPrerequisites();
    await installDependencies();
    await runTests();
    await generateReport();
    
    logHeader('测试完成');
    logSuccess('所有测试步骤已完成！');
    
  } catch (error) {
    logHeader('测试失败');
    logError(`测试过程中出现错误: ${error.message}`);
    process.exit(1);
  }
}

// 处理命令行参数
const args = process.argv.slice(2);
if (args.includes('--help') || args.includes('-h')) {
  console.log(`
使用方法: node run-tests.js [选项]

选项:
  --help, -h     显示帮助信息
  --debug        启用调试模式
  --headed       在有头模式下运行浏览器
  --ui           启用 Playwright UI 模式

示例:
  node run-tests.js                    # 运行所有测试
  node run-tests.js --headed           # 在有头模式下运行
  node run-tests.js --ui               # 启用 UI 模式
  `);
  process.exit(0);
}

// 运行主函数
main().catch((error) => {
  logError(`未处理的错误: ${error.message}`);
  process.exit(1);
});
