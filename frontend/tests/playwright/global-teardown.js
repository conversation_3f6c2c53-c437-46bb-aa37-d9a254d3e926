/**
 * Playwright 全局拆卸
 * 在所有测试结束后执行的清理操作
 */

async function globalTeardown(config) {
  console.log('🧹 开始全局测试清理...');
  
  try {
    // 清理测试数据
    await cleanupTestData();
    
    // 清理临时文件
    await cleanupTempFiles();
    
    console.log('✅ 全局测试清理完成');
    
  } catch (error) {
    console.error('❌ 全局清理失败:', error);
    // 不抛出错误，避免影响测试结果
  }
}

/**
 * 清理测试数据
 */
async function cleanupTestData() {
  console.log('🗑️ 清理测试数据...');
  
  try {
    // 这里可以添加清理测试数据的逻辑
    // 例如：删除测试用户、清理测试项目等
    
    console.log('✅ 测试数据清理完成');
  } catch (error) {
    console.warn('⚠️ 测试数据清理失败:', error.message);
  }
}

/**
 * 清理临时文件
 */
async function cleanupTempFiles() {
  console.log('📁 清理临时文件...');
  
  try {
    const fs = require('fs').promises;
    const path = require('path');
    
    // 清理测试截图和视频（如果需要）
    const tempDirs = [
      'test-results',
      'playwright-report'
    ];
    
    for (const dir of tempDirs) {
      try {
        const dirPath = path.join(process.cwd(), dir);
        await fs.access(dirPath);
        // 目录存在，可以选择清理或保留
        console.log(`📂 保留目录: ${dir}`);
      } catch {
        // 目录不存在，跳过
      }
    }
    
    console.log('✅ 临时文件清理完成');
  } catch (error) {
    console.warn('⚠️ 临时文件清理失败:', error.message);
  }
}

module.exports = globalTeardown;
