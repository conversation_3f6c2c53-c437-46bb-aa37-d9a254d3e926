const { test, expect } = require('@playwright/test');

/**
 * AI写作流程端到端测试
 * 测试流程：
 * 1. 创建知识库（虚拟公司资料）
 * 2. 上传文档到知识库
 * 3. 创建写作模板（包含参数）
 * 4. 使用模板进行AI写作
 * 5. 验证参数拼接和知识库检索功能
 */

test.describe('AI写作流程测试', () => {
  let page;
  
  test.beforeEach(async ({ browser }) => {
    page = await browser.newPage();
    
    // 设置视口大小
    await page.setViewportSize({ width: 1920, height: 1080 });
    
    // 导航到首页
    await page.goto('http://localhost:3000');
    
    // 等待页面加载
    await page.waitForLoadState('networkidle');
    
    // 进入企业控制中心
    await page.click('text=控制台');
    await page.waitForTimeout(1000);
    
    await page.click('text=企业用户中心');
    await page.waitForTimeout(1000);
    
    await page.click('text=进入中心');
    await page.waitForTimeout(2000);
  });

  test('完整AI写作流程测试', async () => {
    console.log('🚀 开始AI写作流程测试...');

    // ========== 第一步：创建知识库 ==========
    console.log('📚 步骤1: 创建知识库');
    
    // 进入内容管理 -> 知识库管理
    await page.click('text=内容管理');
    await page.waitForTimeout(1000);
    
    await page.click('text=知识库管理');
    await page.waitForTimeout(2000);
    
    // 创建新知识库
    await page.click('text=创建知识库');
    await page.waitForTimeout(1000);
    
    // 填写知识库信息（虚拟公司）
    const companyName = '星辰科技有限公司';
    const companyDescription = '一家专注于人工智能和大数据分析的创新科技公司，成立于2020年，主要业务包括AI算法开发、数据分析服务、智能系统集成等。公司拥有50名员工，年营收5000万元。';
    
    await page.fill('input[placeholder*="知识库名称"]', `${companyName}企业资料库`);
    await page.fill('textarea[placeholder*="知识库描述"]', companyDescription);
    
    await page.click('text=创建');
    await page.waitForTimeout(3000);
    
    console.log('✅ 知识库创建成功');

    // ========== 第二步：上传文档到知识库 ==========
    console.log('📄 步骤2: 上传公司资料文档');
    
    // 选择刚创建的知识库
    await page.click(`text=${companyName}企业资料库`);
    await page.waitForTimeout(2000);
    
    // 创建测试文档内容
    const companyDocument = `
${companyName}企业简介

公司概况：
${companyName}成立于2020年，是一家专注于人工智能和大数据分析的高新技术企业。公司总部位于北京中关村科技园区，注册资本1000万元。

主营业务：
1. AI算法开发与优化
2. 大数据分析与挖掘
3. 智能系统集成服务
4. 机器学习模型训练
5. 自然语言处理技术

技术优势：
- 拥有自主研发的深度学习框架
- 在计算机视觉领域有多项专利
- 与清华大学、北京大学建立产学研合作
- 获得国家高新技术企业认证

团队规模：
- 总员工数：50人
- 研发人员：35人
- 销售团队：10人
- 管理团队：5人

财务状况：
- 2023年营收：5000万元
- 年增长率：150%
- 主要客户：金融、医疗、教育行业

发展规划：
计划在未来3年内扩展到200人规模，在上海、深圳设立分公司，目标成为国内领先的AI解决方案提供商。
`;

    // 点击上传文档按钮
    await page.click('text=上传文档');
    await page.waitForTimeout(1000);
    
    // 选择文本输入方式
    await page.click('text=文本输入');
    await page.waitForTimeout(1000);
    
    // 填写文档信息
    await page.fill('input[placeholder*="文档标题"]', `${companyName}企业简介`);
    await page.fill('textarea[placeholder*="文档内容"]', companyDocument);
    
    await page.click('text=确认上传');
    await page.waitForTimeout(5000); // 等待文档处理
    
    console.log('✅ 企业资料文档上传成功');

    // ========== 第三步：创建写作模板 ==========
    console.log('📝 步骤3: 创建写作模板');
    
    // 进入写作模板管理
    await page.click('text=内容管理');
    await page.waitForTimeout(1000);
    
    await page.click('text=写作模板');
    await page.waitForTimeout(2000);
    
    // 创建新模板
    await page.click('text=创建模板');
    await page.waitForTimeout(1000);
    
    // 填写模板信息
    const templateName = '企业宣传文案模板';
    const templatePrompt = `你是一名专业的企业文案策划师，请根据以下要求创建企业宣传文案：

写作要求：
- 文案类型：{{content_type}}
- 目标受众：{{target_audience}}
- 文案风格：{{writing_style}}
- 字数要求：{{word_count}}字左右
- 重点突出：{{key_points}}

请基于提供的企业资料，创作一篇专业、吸引人的{{content_type}}，确保内容真实可信，突出企业优势和特色。

文案结构要求：
1. 引人注目的标题
2. 企业核心优势介绍
3. 具体业务和服务描述
4. 成功案例或数据支撑
5. 号召性结尾

请确保文案具有说服力和感染力，能够有效传达企业价值。`;

    await page.fill('input[placeholder*="模板名称"]', templateName);
    await page.fill('textarea[placeholder*="模板描述"]', '用于生成企业宣传文案的专业模板，支持多种文案类型和风格定制');
    await page.fill('textarea[placeholder*="提示词模板"]', templatePrompt);
    
    // 添加模板参数
    await page.click('text=添加参数');
    await page.waitForTimeout(500);
    
    // 参数1: content_type
    await page.fill('input[placeholder*="参数名称"]', 'content_type');
    await page.fill('input[placeholder*="参数描述"]', '文案类型');
    await page.fill('input[placeholder*="默认值"]', '企业介绍');
    
    await page.click('text=添加参数');
    await page.waitForTimeout(500);
    
    // 参数2: target_audience
    const parameterInputs = await page.locator('input[placeholder*="参数名称"]').all();
    await parameterInputs[1].fill('target_audience');
    
    const descriptionInputs = await page.locator('input[placeholder*="参数描述"]').all();
    await descriptionInputs[1].fill('目标受众');
    
    const defaultInputs = await page.locator('input[placeholder*="默认值"]').all();
    await defaultInputs[1].fill('潜在客户');
    
    await page.click('text=添加参数');
    await page.waitForTimeout(500);
    
    // 参数3: writing_style
    await parameterInputs[2].fill('writing_style');
    await descriptionInputs[2].fill('写作风格');
    await defaultInputs[2].fill('专业正式');
    
    await page.click('text=添加参数');
    await page.waitForTimeout(500);
    
    // 参数4: word_count
    await parameterInputs[3].fill('word_count');
    await descriptionInputs[3].fill('字数要求');
    await defaultInputs[3].fill('800');
    
    await page.click('text=添加参数');
    await page.waitForTimeout(500);
    
    // 参数5: key_points
    await parameterInputs[4].fill('key_points');
    await descriptionInputs[4].fill('重点内容');
    await defaultInputs[4].fill('技术优势和创新能力');
    
    // 保存模板
    await page.click('text=创建模板');
    await page.waitForTimeout(3000);
    
    console.log('✅ 写作模板创建成功');

    // ========== 第四步：使用模板进行AI写作 ==========
    console.log('🤖 步骤4: 使用模板进行AI写作');
    
    // 进入AI内容创作
    await page.click('text=AI工具');
    await page.waitForTimeout(1000);
    
    await page.click('text=AI内容创作');
    await page.waitForTimeout(2000);
    
    // 选择写作模板
    await page.click('text=选择模板');
    await page.waitForTimeout(1000);
    
    await page.click(`text=${templateName}`);
    await page.waitForTimeout(1000);
    
    // 配置模板参数
    await page.fill('input[placeholder*="content_type"]', '企业品牌宣传文案');
    await page.fill('input[placeholder*="target_audience"]', '投资人和合作伙伴');
    await page.fill('input[placeholder*="writing_style"]', '专业且具有感染力');
    await page.fill('input[placeholder*="word_count"]', '1000');
    await page.fill('input[placeholder*="key_points"]', 'AI技术实力、市场前景、团队优势');
    
    // 选择知识库
    await page.click('text=选择知识库');
    await page.waitForTimeout(1000);
    
    await page.click(`text=${companyName}企业资料库`);
    await page.waitForTimeout(1000);
    
    // 输入写作需求
    const writingRequest = `请为${companyName}创作一篇专业的企业品牌宣传文案，重点突出公司的AI技术实力和市场前景，面向投资人和潜在合作伙伴。`;
    
    await page.fill('textarea[placeholder*="请描述您想要生成的内容"]', writingRequest);
    
    // 开始生成
    await page.click('text=生成');
    await page.waitForTimeout(1000);
    
    console.log('⏳ 等待AI生成内容...');
    
    // 等待生成完成（最多等待60秒）
    await page.waitForSelector('text=生成完成', { timeout: 60000 });
    
    console.log('✅ AI内容生成完成');

    // ========== 第五步：验证结果 ==========
    console.log('🔍 步骤5: 验证生成结果');
    
    // 检查生成的内容是否包含参数信息
    const generatedContent = await page.textContent('.generated-content, .message-content, .ai-response');
    
    // 验证参数是否正确拼接
    expect(generatedContent).toContain('企业品牌宣传文案');
    expect(generatedContent).toContain('投资人');
    expect(generatedContent).toContain('AI技术');
    
    // 验证知识库信息是否被检索和使用
    expect(generatedContent).toContain(companyName);
    expect(generatedContent).toContain('人工智能');
    expect(generatedContent).toContain('大数据');
    
    console.log('✅ 参数拼接验证通过');
    console.log('✅ 知识库检索验证通过');
    
    // 截图保存结果
    await page.screenshot({ 
      path: 'ai-writing-flow-result.png', 
      fullPage: true 
    });
    
    console.log('🎉 AI写作流程测试完成！');
  });

  test.afterEach(async () => {
    if (page) {
      await page.close();
    }
  });
});
