/**
 * 知识库端到端测试
 * 测试知识库的增删改查、文档上传、向量搜索等功能
 * 验证与Viking向量数据库的连通性
 */

const { test, expect } = require('@playwright/test');

// 测试配置
const BASE_URL = 'http://localhost:3000';
const API_BASE_URL = 'http://localhost:8000';

// 测试用户配置
const TEST_USER = {
  email: '<EMAIL>',
  password: 'test123456',
  fullName: '知识库测试用户'
};

// 测试数据
const TEST_KNOWLEDGE_BASE = {
  name: '测试知识库_' + Date.now(),
  description: '这是一个用于测试的知识库，包含各种类型的文档内容'
};

const TEST_DOCUMENTS = [
  {
    title: '人工智能基础知识',
    content: '人工智能（Artificial Intelligence，AI）是计算机科学的一个分支，它企图了解智能的实质，并生产出一种新的能以人类智能相似的方式做出反应的智能机器。人工智能包括机器学习、深度学习、自然语言处理等多个领域。',
    file_type: 'text'
  },
  {
    title: '机器学习算法介绍',
    content: '机器学习是人工智能的一个重要分支，主要研究如何让计算机系统通过经验自动改进性能。常见的机器学习算法包括线性回归、决策树、随机森林、支持向量机、神经网络等。',
    file_type: 'text'
  },
  {
    title: '深度学习与神经网络',
    content: '深度学习是机器学习的一个子集，它模仿人脑的神经网络结构来处理数据。深度学习在图像识别、语音识别、自然语言处理等领域取得了突破性进展。卷积神经网络（CNN）和循环神经网络（RNN）是两种重要的深度学习架构。',
    file_type: 'text'
  }
];

class KnowledgeBaseTestHelper {
  constructor(page) {
    this.page = page;
    this.authToken = null;
    this.userId = null;
    this.knowledgeBaseId = null;
    this.documentIds = [];
  }

  /**
   * 注册测试用户
   */
  async registerTestUser() {
    console.log('🔧 开始注册测试用户...');
    
    try {
      // 发送验证码
      const sendCodeResponse = await this.page.request.post(`${API_BASE_URL}/api/v1/auth/send-verification-code`, {
        data: {
          email: TEST_USER.email,
          code_type: 'register'
        }
      });

      // 注册用户（使用开发环境固定验证码）
      const registerResponse = await this.page.request.post(`${API_BASE_URL}/api/v1/auth/register`, {
        data: {
          email: TEST_USER.email,
          password: TEST_USER.password,
          full_name: TEST_USER.fullName,
          verification_code: '123456'
        }
      });

      if (registerResponse.ok()) {
        console.log('✅ 测试用户注册成功');
        return true;
      } else {
        const error = await registerResponse.text();
        console.log(`⚠️ 用户可能已存在: ${error}`);
        return true; // 用户已存在也算成功
      }
    } catch (error) {
      console.error('❌ 注册测试用户失败:', error);
      return false;
    }
  }

  /**
   * 登录测试用户
   */
  async loginTestUser() {
    console.log('🔐 开始登录测试用户...');
    
    try {
      const response = await this.page.request.post(`${API_BASE_URL}/api/v1/auth/login`, {
        data: {
          email: TEST_USER.email,
          password: TEST_USER.password
        }
      });

      if (response.ok()) {
        const result = await response.json();
        this.authToken = result.data.access_token;
        this.userId = result.data.user.id;
        console.log('✅ 用户登录成功');
        return true;
      } else {
        console.error('❌ 用户登录失败:', await response.text());
        return false;
      }
    } catch (error) {
      console.error('❌ 登录过程异常:', error);
      return false;
    }
  }

  /**
   * 获取认证头
   */
  getAuthHeaders() {
    return {
      'Authorization': `Bearer ${this.authToken}`,
      'Content-Type': 'application/json'
    };
  }

  /**
   * 创建知识库
   */
  async createKnowledgeBase() {
    console.log('📚 开始创建知识库...');
    
    try {
      const response = await this.page.request.post(`${API_BASE_URL}/api/v1/knowledge-bases`, {
        headers: this.getAuthHeaders(),
        data: TEST_KNOWLEDGE_BASE
      });

      if (response.ok()) {
        const result = await response.json();
        this.knowledgeBaseId = result.id;
        console.log(`✅ 知识库创建成功: ${result.name} (ID: ${this.knowledgeBaseId})`);
        return result;
      } else {
        console.error('❌ 知识库创建失败:', await response.text());
        return null;
      }
    } catch (error) {
      console.error('❌ 创建知识库异常:', error);
      return null;
    }
  }

  /**
   * 上传文档到知识库
   */
  async uploadDocuments() {
    console.log('📄 开始上传文档...');
    
    const uploadedDocs = [];
    
    for (const doc of TEST_DOCUMENTS) {
      try {
        const response = await this.page.request.post(
          `${API_BASE_URL}/api/v1/knowledge-bases/${this.knowledgeBaseId}/documents`,
          {
            headers: this.getAuthHeaders(),
            data: doc
          }
        );

        if (response.ok()) {
          const result = await response.json();
          this.documentIds.push(result.id);
          uploadedDocs.push(result);
          console.log(`✅ 文档上传成功: ${result.title}`);
        } else {
          console.error(`❌ 文档上传失败: ${doc.title}`, await response.text());
        }
      } catch (error) {
        console.error(`❌ 上传文档异常: ${doc.title}`, error);
      }
    }
    
    return uploadedDocs;
  }

  /**
   * 等待文档处理完成
   */
  async waitForDocumentProcessing() {
    console.log('⏳ 等待文档处理完成...');
    
    const maxWaitTime = 60000; // 最大等待60秒
    const checkInterval = 3000; // 每3秒检查一次
    const startTime = Date.now();
    
    while (Date.now() - startTime < maxWaitTime) {
      try {
        let allProcessed = true;
        
        for (const docId of this.documentIds) {
          const response = await this.page.request.get(
            `${API_BASE_URL}/api/v1/knowledge-bases/${this.knowledgeBaseId}/documents/${docId}`,
            {
              headers: this.getAuthHeaders()
            }
          );
          
          if (response.ok()) {
            const doc = await response.json();
            if (doc.embedding_status !== 'completed') {
              allProcessed = false;
              break;
            }
          }
        }
        
        if (allProcessed) {
          console.log('✅ 所有文档处理完成');
          return true;
        }
        
        await this.page.waitForTimeout(checkInterval);
      } catch (error) {
        console.error('❌ 检查文档状态异常:', error);
      }
    }
    
    console.log('⚠️ 文档处理超时，继续测试...');
    return false;
  }

  /**
   * 搜索知识库内容
   */
  async searchKnowledgeBase(query) {
    console.log(`🔍 搜索知识库内容: "${query}"`);
    
    try {
      const response = await this.page.request.post(
        `${API_BASE_URL}/api/v1/knowledge-bases/${this.knowledgeBaseId}/search`,
        {
          headers: this.getAuthHeaders(),
          data: {
            query: query,
            limit: 5
          }
        }
      );

      if (response.ok()) {
        const result = await response.json();
        console.log(`✅ 搜索成功，找到 ${result.results.length} 个结果`);
        return result;
      } else {
        console.error('❌ 搜索失败:', await response.text());
        return null;
      }
    } catch (error) {
      console.error('❌ 搜索异常:', error);
      return null;
    }
  }

  /**
   * 检查Viking数据库连通性
   */
  async checkVikingConnection() {
    console.log('🔗 检查Viking数据库连通性...');
    
    try {
      const response = await this.page.request.get(`${API_BASE_URL}/api/v1/vector/health`);
      
      if (response.ok()) {
        const result = await response.json();
        console.log(`✅ Viking连接状态: ${result.status}`);
        console.log(`   消息: ${result.message}`);
        console.log(`   使用模拟: ${result.use_mock}`);
        return result;
      } else {
        console.error('❌ Viking健康检查失败:', await response.text());
        return null;
      }
    } catch (error) {
      console.error('❌ Viking连接检查异常:', error);
      return null;
    }
  }

  /**
   * 清理测试数据
   */
  async cleanup() {
    console.log('🧹 开始清理测试数据...');
    
    if (this.knowledgeBaseId) {
      try {
        const response = await this.page.request.delete(
          `${API_BASE_URL}/api/v1/knowledge-bases/${this.knowledgeBaseId}`,
          {
            headers: this.getAuthHeaders()
          }
        );
        
        if (response.ok()) {
          console.log('✅ 知识库删除成功');
        } else {
          console.error('❌ 知识库删除失败:', await response.text());
        }
      } catch (error) {
        console.error('❌ 删除知识库异常:', error);
      }
    }
  }
}

// 测试套件
test.describe('知识库功能端到端测试', () => {
  let helper;

  test.beforeEach(async ({ page }) => {
    helper = new KnowledgeBaseTestHelper(page);
  });

  test.afterEach(async () => {
    if (helper) {
      await helper.cleanup();
    }
  });

  test('完整的知识库业务流程测试', async ({ page }) => {
    // 1. 用户注册和登录
    await helper.registerTestUser();
    const loginSuccess = await helper.loginTestUser();
    expect(loginSuccess).toBe(true);

    // 2. 检查Viking数据库连通性
    const vikingHealth = await helper.checkVikingConnection();
    expect(vikingHealth).not.toBeNull();
    expect(vikingHealth.status).toBe('healthy');

    // 3. 创建知识库
    const knowledgeBase = await helper.createKnowledgeBase();
    expect(knowledgeBase).not.toBeNull();
    expect(knowledgeBase.name).toBe(TEST_KNOWLEDGE_BASE.name);

    // 4. 上传文档
    const uploadedDocs = await helper.uploadDocuments();
    expect(uploadedDocs.length).toBe(TEST_DOCUMENTS.length);

    // 5. 等待文档处理
    await helper.waitForDocumentProcessing();

    // 6. 测试搜索功能
    const searchQueries = [
      '人工智能',
      '机器学习算法',
      '深度学习',
      '神经网络'
    ];

    for (const query of searchQueries) {
      const searchResult = await helper.searchKnowledgeBase(query);
      expect(searchResult).not.toBeNull();
      expect(searchResult.results.length).toBeGreaterThan(0);
    }

    console.log('🎉 所有测试通过！知识库功能正常，Viking数据库连通性良好！');
  });
});
