/**
 * 简化的AI模板功能测试
 * 验证删除字段后的基本功能
 */

const { test, expect } = require('@playwright/test');

// 测试配置
const BASE_URL = 'http://localhost:3000';
const TEST_USER = {
  username: '<EMAIL>',
  password: '123456'
};

test.describe('AI模板功能简单测试', () => {

  test('验证前端页面能正常加载', async ({ page }) => {
    console.log('开始测试前端页面加载...');

    // 导航到登录页面
    await page.goto(`${BASE_URL}/#/login`);
    
    // 等待页面加载
    await page.waitForTimeout(3000);
    
    // 检查登录页面是否正常显示
    const loginForm = page.locator('form').or(page.locator('input[type="email"]')).or(page.locator('input[type="password"]'));
    
    if (await loginForm.count() > 0) {
      console.log('✅ 登录页面加载成功');
      
      // 尝试登录
      try {
        const emailInput = page.locator('input[type="email"], input[name="email"], input[name="username"]').first();
        if (await emailInput.count() > 0) {
          await emailInput.fill(TEST_USER.username);
        }

        const passwordInput = page.locator('input[type="password"], input[name="password"]').first();
        if (await passwordInput.count() > 0) {
          await passwordInput.fill(TEST_USER.password);
        }

        const loginButton = page.locator('button[type="submit"], button:has-text("登录"), button:has-text("Login")').first();
        if (await loginButton.count() > 0) {
          await loginButton.click();
          await page.waitForTimeout(3000);
          console.log('✅ 登录操作完成');
        }
      } catch (error) {
        console.log('⚠️ 登录过程中出现错误，但继续测试:', error.message);
      }
    } else {
      console.log('⚠️ 登录表单未找到，可能已经登录或页面结构不同');
    }

    // 尝试导航到企业控制中心
    try {
      await page.goto(`${BASE_URL}/#/control-center/enterprise`);
      await page.waitForTimeout(3000);
      console.log('✅ 企业控制中心页面访问成功');
    } catch (error) {
      console.log('⚠️ 企业控制中心页面访问失败:', error.message);
    }

    // 检查页面是否包含模板相关内容
    const templateElements = page.locator('text=模板').or(page.locator('text=写作')).or(page.locator('text=AI'));
    if (await templateElements.count() > 0) {
      console.log('✅ 页面包含模板相关内容');
    } else {
      console.log('⚠️ 页面未找到模板相关内容');
    }

    console.log('✅ 前端页面加载测试完成');
  });

  test('验证删除的字段不再显示', async ({ page }) => {
    console.log('开始测试删除字段验证...');

    // 导航到主页
    await page.goto(`${BASE_URL}`);
    await page.waitForTimeout(2000);

    // 检查页面中不应该包含已删除字段的相关文本
    const serviceTypeTexts = [
      '服务类型',
      '内容生成',
      '内容优化', 
      '关键词分析',
      'SEO分析',
      '趋势分析'
    ];

    const categoryTexts = [
      '营销文案',
      '内容创作',
      '商务文档',
      '技术文档',
      '社交媒体'
    ];

    const publicTexts = [
      '公开模板',
      '私有模板',
      '公开状态'
    ];

    let foundDeletedFields = [];

    // 检查服务类型相关文本
    for (const text of serviceTypeTexts) {
      const elements = page.locator(`text=${text}`);
      const count = await elements.count();
      if (count > 0) {
        foundDeletedFields.push(`服务类型相关: ${text}`);
      }
    }

    // 检查分类相关文本
    for (const text of categoryTexts) {
      const elements = page.locator(`text=${text}`);
      const count = await elements.count();
      if (count > 0) {
        foundDeletedFields.push(`分类相关: ${text}`);
      }
    }

    // 检查公开状态相关文本
    for (const text of publicTexts) {
      const elements = page.locator(`text=${text}`);
      const count = await elements.count();
      if (count > 0) {
        foundDeletedFields.push(`公开状态相关: ${text}`);
      }
    }

    if (foundDeletedFields.length > 0) {
      console.log('⚠️ 发现可能未完全删除的字段:', foundDeletedFields);
    } else {
      console.log('✅ 未发现已删除字段的相关文本');
    }

    console.log('✅ 删除字段验证测试完成');
  });

  test('验证页面基本功能正常', async ({ page }) => {
    console.log('开始测试页面基本功能...');

    // 导航到主页
    await page.goto(`${BASE_URL}`);
    await page.waitForTimeout(2000);

    // 检查页面是否正常渲染（没有明显的错误）
    const bodyElement = page.locator('body');
    const bodyText = await bodyElement.textContent();
    
    if (bodyText && bodyText.length > 100) {
      console.log('✅ 页面内容正常渲染');
    } else {
      console.log('⚠️ 页面内容可能异常');
    }

    // 检查是否有JavaScript错误
    const errors = [];
    page.on('pageerror', error => {
      errors.push(error.message);
    });

    await page.waitForTimeout(3000);

    if (errors.length === 0) {
      console.log('✅ 未发现JavaScript错误');
    } else {
      console.log('⚠️ 发现JavaScript错误:', errors);
    }

    console.log('✅ 页面基本功能测试完成');
  });

});
