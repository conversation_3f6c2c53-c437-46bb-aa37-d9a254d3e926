/**
 * 测试配置文件
 * 包含测试环境的配置信息和工具函数
 */

// 环境配置
const TEST_CONFIG = {
  // 服务地址
  FRONTEND_URL: process.env.FRONTEND_URL || 'http://localhost:3000',
  BACKEND_URL: process.env.BACKEND_URL || 'http://localhost:8000',
  
  // 测试用户配置
  TEST_USERS: {
    KNOWLEDGE_TEST: {
      email: '<EMAIL>',
      password: 'test123456',
      fullName: '知识库测试用户'
    },
    ADMIN_TEST: {
      email: '<EMAIL>',
      password: 'admin123456',
      fullName: '管理员测试用户'
    }
  },
  
  // 测试超时配置
  TIMEOUTS: {
    DEFAULT: 30000,
    LONG_OPERATION: 60000,
    DOCUMENT_PROCESSING: 120000,
    VECTOR_SEARCH: 10000
  },
  
  // 测试数据配置
  TEST_DATA: {
    KNOWLEDGE_BASE: {
      name: '测试知识库_' + Date.now(),
      description: '这是一个用于自动化测试的知识库'
    },
    
    DOCUMENTS: [
      {
        title: '人工智能基础',
        content: '人工智能（AI）是计算机科学的一个分支，旨在创建能够执行通常需要人类智能的任务的系统。',
        file_type: 'text'
      },
      {
        title: '机器学习概述',
        content: '机器学习是人工智能的一个子集，它使计算机能够在没有明确编程的情况下学习和改进。',
        file_type: 'text'
      },
      {
        title: '深度学习技术',
        content: '深度学习是机器学习的一个分支，它使用多层神经网络来模拟人脑的工作方式。',
        file_type: 'text'
      }
    ],
    
    SEARCH_QUERIES: [
      '人工智能',
      '机器学习',
      '深度学习',
      '神经网络',
      '算法'
    ]
  }
};

/**
 * 测试工具类
 */
class TestUtils {
  /**
   * 等待指定时间
   */
  static async sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 生成随机字符串
   */
  static generateRandomString(length = 8) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  /**
   * 生成测试邮箱
   */
  static generateTestEmail(prefix = 'test') {
    return `${prefix}_${this.generateRandomString()}@example.com`;
  }

  /**
   * 格式化时间戳
   */
  static formatTimestamp(timestamp) {
    return new Date(timestamp).toLocaleString('zh-CN');
  }

  /**
   * 验证响应状态
   */
  static validateResponse(response, expectedStatus = 200) {
    if (response.status !== expectedStatus) {
      throw new Error(`Expected status ${expectedStatus}, got ${response.status}`);
    }
    return true;
  }

  /**
   * 等待条件满足
   */
  static async waitForCondition(conditionFn, timeout = 30000, interval = 1000) {
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeout) {
      if (await conditionFn()) {
        return true;
      }
      await this.sleep(interval);
    }
    
    throw new Error(`Condition not met within ${timeout}ms`);
  }

  /**
   * 重试执行函数
   */
  static async retry(fn, maxRetries = 3, delay = 1000) {
    let lastError;
    
    for (let i = 0; i < maxRetries; i++) {
      try {
        return await fn();
      } catch (error) {
        lastError = error;
        if (i < maxRetries - 1) {
          console.log(`Retry ${i + 1}/${maxRetries} after error:`, error.message);
          await this.sleep(delay);
        }
      }
    }
    
    throw lastError;
  }

  /**
   * 验证向量搜索结果
   */
  static validateSearchResults(results, query) {
    if (!Array.isArray(results)) {
      throw new Error('Search results should be an array');
    }
    
    if (results.length === 0) {
      console.warn(`No results found for query: ${query}`);
      return false;
    }
    
    // 验证结果结构
    for (const result of results) {
      if (!result.content || !result.similarity_score) {
        throw new Error('Invalid search result structure');
      }
      
      if (typeof result.similarity_score !== 'number') {
        throw new Error('Similarity score should be a number');
      }
    }
    
    return true;
  }

  /**
   * 生成测试报告
   */
  static generateTestReport(testResults) {
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        total: testResults.length,
        passed: testResults.filter(r => r.status === 'passed').length,
        failed: testResults.filter(r => r.status === 'failed').length,
        skipped: testResults.filter(r => r.status === 'skipped').length
      },
      details: testResults
    };
    
    return report;
  }
}

/**
 * API 客户端类
 */
class APIClient {
  constructor(baseURL, authToken = null) {
    this.baseURL = baseURL;
    this.authToken = authToken;
  }

  /**
   * 设置认证令牌
   */
  setAuthToken(token) {
    this.authToken = token;
  }

  /**
   * 获取请求头
   */
  getHeaders() {
    const headers = {
      'Content-Type': 'application/json'
    };
    
    if (this.authToken) {
      headers['Authorization'] = `Bearer ${this.authToken}`;
    }
    
    return headers;
  }

  /**
   * 发送GET请求
   */
  async get(endpoint) {
    const response = await fetch(`${this.baseURL}${endpoint}`, {
      method: 'GET',
      headers: this.getHeaders()
    });
    
    return {
      status: response.status,
      ok: response.ok,
      data: response.ok ? await response.json() : null,
      error: response.ok ? null : await response.text()
    };
  }

  /**
   * 发送POST请求
   */
  async post(endpoint, data) {
    const response = await fetch(`${this.baseURL}${endpoint}`, {
      method: 'POST',
      headers: this.getHeaders(),
      body: JSON.stringify(data)
    });
    
    return {
      status: response.status,
      ok: response.ok,
      data: response.ok ? await response.json() : null,
      error: response.ok ? null : await response.text()
    };
  }

  /**
   * 发送DELETE请求
   */
  async delete(endpoint) {
    const response = await fetch(`${this.baseURL}${endpoint}`, {
      method: 'DELETE',
      headers: this.getHeaders()
    });
    
    return {
      status: response.status,
      ok: response.ok,
      data: response.ok ? await response.json() : null,
      error: response.ok ? null : await response.text()
    };
  }
}

module.exports = {
  TEST_CONFIG,
  TestUtils,
  APIClient
};
